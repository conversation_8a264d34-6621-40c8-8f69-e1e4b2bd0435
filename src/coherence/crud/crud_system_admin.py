import hashlib
import uuid
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>
from uuid import UUI<PERSON>

from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from ..models.system_admin import SystemAdmin, SystemAdminAPIKey
from ..schemas.system_admin import (
    SystemAdminAPIKeyCreate,
    SystemAdminAPIKeyUpdate,
    SystemAdminCreate,
    SystemAdminUpdate,
)

# --- SystemAdmin CRUD ---

async def create_system_admin(db: AsyncSession, *, obj_in: SystemAdminCreate) -> SystemAdmin:
    """
    Create a new system admin.
    """
    db_obj = SystemAdmin(**obj_in.model_dump())
    db.add(db_obj)
    await db.commit()
    await db.refresh(db_obj)
    return db_obj

async def get_system_admin(db: AsyncSession, *, id: UUID) -> Optional[SystemAdmin]:
    """
    Retrieve a system admin by ID.
    """
    result = await db.execute(select(SystemAdmin).where(SystemAdmin.id == id))
    return result.scalars().first()

async def get_system_admin_by_clerk_id(db: AsyncSession, *, clerk_user_id: str) -> Optional[SystemAdmin]:
    """
    Retrieve a system admin by Clerk User ID.
    """
    result = await db.execute(select(SystemAdmin).where(SystemAdmin.clerk_user_id == clerk_user_id))
    return result.scalars().first()

async def get_multi_system_admin(db: AsyncSession, *, skip: int = 0, limit: int = 100) -> List[SystemAdmin]:
    """
    Retrieve multiple system admins with pagination.
    """
    result = await db.execute(select(SystemAdmin).offset(skip).limit(limit))
    return result.scalars().all()

async def update_system_admin(db: AsyncSession, *, db_obj: SystemAdmin, obj_in: SystemAdminUpdate) -> SystemAdmin:
    """
    Update a system admin.
    """
    update_data = obj_in.model_dump(exclude_unset=True)
    if update_data:
        await db.execute(
            update(SystemAdmin)
            .where(SystemAdmin.id == db_obj.id)
            .values(**update_data)
        )
        await db.commit()
        await db.refresh(db_obj)
    return db_obj

async def remove_system_admin(db: AsyncSession, *, id: UUID) -> Optional[SystemAdmin]:
    """
    Delete a system admin by ID.
    """
    db_obj = await get_system_admin(db, id=id)
    if db_obj:
        await db.execute(delete(SystemAdmin).where(SystemAdmin.id == id))
        await db.commit()
    return db_obj

# --- SystemAdminAPIKey CRUD ---

def _hash_api_key(api_key: str) -> str:
    """Simple SHA256 hashing for the API key."""
    return hashlib.sha256(api_key.encode()).hexdigest()

async def create_system_admin_api_key(db: AsyncSession, *, obj_in: SystemAdminAPIKeyCreate, system_admin_id: UUID) -> Tuple[SystemAdminAPIKey, str]:
    """
    Create a new API key for a system admin.

    Generates a new key, hashes it, stores the hash, and returns the DB object
    along with the *unhashed* key.

    Args:
        db: Database session
        obj_in: API key creation data
        system_admin_id: UUID of the system admin this key belongs to

    Returns:
        Tuple containing the created API key object and the raw (unhashed) API key string
    """
    raw_api_key = uuid.uuid4().hex
    key_hash = _hash_api_key(raw_api_key)

    # Create object dict from input model
    obj_data = obj_in.model_dump()

    # Add the system_admin_id and key_hash
    db_obj = SystemAdminAPIKey(
        **obj_data,
        system_admin_id=system_admin_id,
        key_hash=key_hash,
    )
    db.add(db_obj)
    await db.commit()
    await db.refresh(db_obj)
    return db_obj, raw_api_key

async def get_system_admin_api_key(db: AsyncSession, *, id: UUID) -> Optional[SystemAdminAPIKey]:
    """
    Retrieve an API key by its ID. Does not return the raw key.
    """
    result = await db.execute(select(SystemAdminAPIKey).where(SystemAdminAPIKey.id == id))
    return result.scalars().first()

async def get_system_admin_api_key_by_hash(db: AsyncSession, *, key_hash: str) -> Optional[SystemAdminAPIKey]:
    """
    Retrieve an API key by its hash.
    """
    result = await db.execute(select(SystemAdminAPIKey).where(SystemAdminAPIKey.key_hash == key_hash))
    return result.scalars().first()

async def get_multi_system_admin_api_key_for_admin(db: AsyncSession, *, system_admin_id: UUID, skip: int = 0, limit: int = 100) -> List[SystemAdminAPIKey]:
    """
    Retrieve multiple API keys for a specific system admin.
    """
    result = await db.execute(
        select(SystemAdminAPIKey)
        .where(SystemAdminAPIKey.system_admin_id == system_admin_id)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

async def update_system_admin_api_key(db: AsyncSession, *, db_obj: SystemAdminAPIKey, obj_in: SystemAdminAPIKeyUpdate) -> SystemAdminAPIKey:
    """
    Update an API key (e.g., name, permissions, revoked status, expires_at).

    Args:
        db: Database session
        db_obj: Existing API key object to update
        obj_in: New data to update the API key with (can be Pydantic model or dict)

    Returns:
        Updated API key object
    """
    # Handle both Pydantic model and dict inputs
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.model_dump(exclude_unset=True)

    # Handle potential update of last_used_at if provided, though typically done elsewhere
    if 'last_used_at' in update_data:
        # Usually updated by auth logic, but allow explicit update if needed
        pass

    # Skip system_admin_id if it's in the update data - shouldn't change ownership
    if 'system_admin_id' in update_data:
        del update_data['system_admin_id']

    if update_data:
        await db.execute(
            update(SystemAdminAPIKey)
            .where(SystemAdminAPIKey.id == db_obj.id)
            .values(**update_data)
        )
        await db.commit()
        await db.refresh(db_obj)
    return db_obj

async def remove_system_admin_api_key(db: AsyncSession, *, id: UUID) -> Optional[SystemAdminAPIKey]:
    """
    Delete an API key by ID.
    """
    db_obj = await get_system_admin_api_key(db, id=id)
    if db_obj:
        await db.execute(delete(SystemAdminAPIKey).where(SystemAdminAPIKey.id == id))
        await db.commit()
    return db_obj
# Helper alias for consistency
get_system_admin_by_id = get_system_admin