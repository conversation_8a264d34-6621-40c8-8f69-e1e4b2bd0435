"""
CRUD operations for Tenant.
"""
from typing import Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.models.tenant import Tenant
from src.coherence.schemas.tenant import TenantCreate


async def get_by_clerk_org_id(db: AsyncSession, *, clerk_org_id: str) -> Optional[Tenant]:
    """
    Get a tenant by its Clerk organization ID.
    """
    result = await db.execute(
        select(Tenant).filter(Tenant.clerk_org_id == clerk_org_id)
    )
    return result.scalars().first()


async def create(db: AsyncSession, *, obj_in: TenantCreate) -> Tenant:
    """
    Create a new tenant.
    """
    # Ensure all fields from TenantCreate that are in Tenant model are passed
    db_obj = Tenant(
        name=obj_in.name,
        clerk_org_id=obj_in.clerk_org_id,
        industry_pack=obj_in.industry_pack,
        compliance_tier=obj_in.compliance_tier,
        settings=obj_in.settings
        # admin_email from TenantCreate is not part of the Tenant model itself.
        # It's typically used for initial user setup associated with the tenant.
    )
    db.add(db_obj)
    await db.commit()
    await db.refresh(db_obj)
    return db_obj