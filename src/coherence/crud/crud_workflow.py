from typing import Any, Dict, List, Optional, Type, Union
from uuid import UUID

from sqlalchemy import delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from src.coherence.models.workflow import Workflow, WorkflowStep
from src.coherence.schemas.admin_workflow import (
    AdminWorkflowCreate,
    AdminWorkflowStepCreate,
    AdminWorkflowUpdate,
)


class CRUDWorkflow:
    def __init__(self, model: Type[Workflow]):
        self.model = model

    async def get_multi_by_tenant(
        self, db: AsyncSession, *, tenant_id: UUID, skip: int = 0, limit: int = 100
    ) -> List[Workflow]:
        stmt = (
            select(self.model)
            .where(self.model.tenant_id == tenant_id)
            .order_by(self.model.created_at.desc()) # Or by name, etc.
            .offset(skip)
            .limit(limit)
            .options(selectinload(self.model.steps)) # Eager load steps
        )
        result = await db.execute(stmt)
        return result.scalars().all()

    async def get_by_id_and_tenant(
        self, db: AsyncSession, *, id: UUID, tenant_id: UUID
    ) -> Optional[Workflow]:
        stmt = (
            select(self.model)
            .where(self.model.id == id, self.model.tenant_id == tenant_id)
            .options(selectinload(self.model.steps)) # Eager load steps
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def create_with_tenant(
        self, db: AsyncSession, *, obj_in: AdminWorkflowCreate, tenant_id: UUID
    ) -> Workflow:
        db_workflow = self.model(
            tenant_id=tenant_id,
            name=obj_in.name,
            description=obj_in.description,
            is_enabled=obj_in.is_enabled
        )
        db.add(db_workflow)
        await db.flush() # Flush to get db_workflow.id for steps

        for step_in in obj_in.steps:
            db_step = WorkflowStep(
                workflow_id=db_workflow.id,
                name=step_in.name,
                step_type=step_in.step_type,
                config=step_in.config,
                order=step_in.order
            )
            db.add(db_step)
        
        await db.commit()
        await db.refresh(db_workflow, ["steps"]) # Refresh to load steps relationship
        return db_workflow

    async def update(
        self, db: AsyncSession, *, db_obj: Workflow, obj_in: Union[AdminWorkflowUpdate, Dict[str, Any]]
    ) -> Workflow:
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)

        # Update direct workflow attributes
        for field, value in update_data.items():
            if field != "steps": # Handle steps separately
                setattr(db_obj, field, value)

        # Handle steps update (more complex: involves deleting old steps, updating existing, adding new)
        if "steps" in update_data and update_data["steps"] is not None:
            # Simple approach: delete all existing steps and recreate. 
            # More sophisticated: diff and apply changes.
            
            # Delete existing steps for this workflow
            await db.execute(delete(WorkflowStep).where(WorkflowStep.workflow_id == db_obj.id))
            await db.flush()

            # Add new steps
            new_steps_data = update_data["steps"]
            for step_data_dict in new_steps_data:
                # Ensure step_data_dict is a dict if it comes from AdminWorkflowStepCreate model
                if isinstance(step_data_dict, AdminWorkflowStepCreate):
                    step_data = step_data_dict.model_dump()
                elif isinstance(step_data_dict, dict):
                    step_data = step_data_dict
                else:
                    continue # Or raise error for unexpected type

                db_step = WorkflowStep(
                    workflow_id=db_obj.id,
                    name=step_data["name"],
                    step_type=step_data["step_type"],
                    config=step_data.get("config", {}),
                    order=step_data["order"]
                )
                db.add(db_step)
            await db.flush()

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj, ["steps"]) # Refresh to load potentially updated steps
        return db_obj

    async def remove_by_id_and_tenant(
        self, db: AsyncSession, *, id: UUID, tenant_id: UUID
    ) -> Optional[Workflow]:
        # First, retrieve the object to ensure it belongs to the tenant and to return it
        db_obj = await self.get_by_id_and_tenant(db, id=id, tenant_id=tenant_id)
        if db_obj:
            # Steps will be cascade-deleted due to relationship settings in the model
            await db.delete(db_obj)
            await db.commit()
        return db_obj

crud_workflow = CRUDWorkflow(Workflow) 