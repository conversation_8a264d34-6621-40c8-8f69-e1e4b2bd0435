from datetime import datetime
from typing import List, Optional, Type
from uuid import UUID

from sqlalchemy import desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.coherence.models.template import Template, TemplateScope, TemplateVersion
from src.coherence.schemas.admin_template import (
    AdminTemplateCreate,
    AdminTemplateUpdate,
)


class CRUDAdminTemplate:
    def __init__(self, model: Type[Template], version_model: Type[TemplateVersion]):
        self.model = model
        self.version_model = version_model

    async def get_multi_by_tenant(
        self, db: AsyncSession, *, tenant_id: UUID, skip: int = 0, limit: int = 100, include_global: bool = True
    ) -> List[Template]:
        """
        Get multiple templates by tenant ID with optional global templates.
        
        Args:
            db: The database session
            tenant_id: The tenant ID to filter by
            skip: Number of records to skip for pagination
            limit: Maximum number of records to return
            include_global: Whether to include global templates (default: True)
        
        Returns:
            List of Template objects
        """
        if include_global:
            # Include tenant-specific templates and global templates
            from sqlalchemy import or_
            stmt = (
                select(self.model)
                .where(
                    or_(
                        # Tenant-specific templates
                        (self.model.tenant_id == tenant_id) & (self.model.scope == TemplateScope.TENANT),
                        # Global templates
                        (self.model.scope == TemplateScope.GLOBAL)
                    )
                )
                .order_by(self.model.scope, self.model.key)  # Order by scope first, then key
                .offset(skip)
                .limit(limit)
                # Do not load versions eagerly - it causes MissingGreenlet errors
                # .options(joinedload(self.model.versions))
            )
        else:
            # Only include tenant-specific templates
            stmt = (
                select(self.model)
                .where(self.model.tenant_id == tenant_id, self.model.scope == TemplateScope.TENANT)
                .order_by(self.model.key)
                .offset(skip)
                .limit(limit)
                # Do not load versions eagerly - it causes MissingGreenlet errors
                # .options(joinedload(self.model.versions))
            )
        
        result = await db.execute(stmt)
        templates = result.scalars().all()  # No need for unique() without joinedload
        return templates

    async def get_by_id_and_tenant(
        self, db: AsyncSession, *, id: UUID, tenant_id: UUID, include_versions: bool = False, include_global: bool = True
    ) -> Optional[Template]:
        """
        Get a template by ID and tenant ID with optional access to global templates.
        
        Args:
            db: The database session
            id: The template ID to retrieve
            tenant_id: The tenant ID for permission checking
            include_versions: Whether to include all version history
            include_global: Whether to allow access to global templates
            
        Returns:
            Template object if found, None otherwise
        """
        if include_global:
            # Allow access to both tenant-specific and global templates
            from sqlalchemy import or_
            stmt = select(self.model).where(
                self.model.id == id,
                or_(
                    # Tenant-specific templates for this tenant
                    (self.model.tenant_id == tenant_id) & (self.model.scope == TemplateScope.TENANT),
                    # Global templates (accessible to all tenants)
                    (self.model.scope == TemplateScope.GLOBAL)
                )
            )
        else:
            # Only allow access to tenant-specific templates
            stmt = select(self.model).where(
                self.model.id == id, 
                self.model.tenant_id == tenant_id,
                self.model.scope == TemplateScope.TENANT
            )
        
        # Do not try to load versions eagerly - it causes MissingGreenlet errors
        # We'll load versions separately if needed
            
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def create_with_tenant(
        self, db: AsyncSession, *, obj_in: AdminTemplateCreate, tenant_id: UUID, user_id: Optional[UUID]
    ) -> Template:
        db_template = self.model(
            tenant_id=tenant_id,
            key=obj_in.key,
            category=obj_in.category.value,
            language=obj_in.language,
            description=obj_in.description,
            body=obj_in.body, # Current version body on Template model
            actions=obj_in.actions,
            parameters=obj_in.parameters,
            version=1, # Initial version
            scope=TemplateScope.TENANT, # Admin creates tenant-scoped templates
            created_by=user_id
        )
        db.add(db_template)
        await db.flush() # Get ID for template_version

        db_template_version = self.version_model(
            template_id=db_template.id,
            version=1,
            body=obj_in.body,
            actions=obj_in.actions,
            parameters=obj_in.parameters,
            editor_id=user_id,
            change_reason=obj_in.change_reason,
            edited_at=db_template.created_at # Match template creation time
        )
        db.add(db_template_version)
        
        await db.commit()
        await db.refresh(db_template, ["versions"]) # Ensure versions are loaded
        return db_template

    async def update_template(
        self, db: AsyncSession, *, template_id: UUID, obj_in: AdminTemplateUpdate, tenant_id: UUID, user_id: Optional[UUID], system_admin: bool = False
    ) -> Optional[Template]:
        db_template = await self.get_by_id_and_tenant(db, id=template_id, tenant_id=tenant_id, include_versions=True)
        if not db_template:
            return None
            
        # Check if the template is protected and the user is not a system admin
        if db_template.protected and not system_admin:
            # Log the attempt to modify a protected template
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Attempt to update protected template {db_template.id} by non-system admin user {user_id}")
            return None

        update_data = obj_in.model_dump(exclude_unset=True)
        needs_new_version = False

        if "body" in update_data and update_data["body"] != db_template.body:
            needs_new_version = True
        if "actions" in update_data and update_data["actions"] != db_template.actions:
            needs_new_version = True
        if "parameters" in update_data and update_data["parameters"] != db_template.parameters:
            needs_new_version = True

        # Direct updates to Template model for fields not triggering new version
        if "key" in update_data: db_template.key = update_data["key"]
        if "description" in update_data: db_template.description = update_data["description"]
        if "category" in update_data: db_template.category = update_data["category"].value
        if "language" in update_data: db_template.language = update_data["language"]
        
        # Only system admins can change the protected status
        if "protected" in update_data and system_admin: 
            db_template.protected = update_data["protected"]
            # Log the protection status change
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"System admin {user_id} changed protection status of template {db_template.id} to {update_data['protected']}")

        if needs_new_version:
            db_template.version += 1
            if "body" in update_data: db_template.body = update_data["body"]
            if "actions" in update_data: db_template.actions = update_data["actions"]
            if "parameters" in update_data: db_template.parameters = update_data["parameters"]

            new_version = self.version_model(
                template_id=db_template.id,
                version=db_template.version,
                body=db_template.body,
                actions=db_template.actions,
                parameters=db_template.parameters,
                editor_id=user_id,
                change_reason=update_data.get("change_reason"),
                edited_at=datetime.utcnow() # Ensure this is timezone-aware if DB is
            )
            db.add(new_version)
        
        # db_template.updated_at is handled by onupdate=func.now() in model
        db.add(db_template)
        await db.commit()
        await db.refresh(db_template, ["versions"])
        return db_template

    async def remove_by_id_and_tenant(
        self, db: AsyncSession, *, id: UUID, tenant_id: UUID, system_admin: bool = False
    ) -> Optional[Template]:
        db_obj = await self.get_by_id_and_tenant(db, id=id, tenant_id=tenant_id)
        if not db_obj:
            return None
            
        # Check if the template is protected and the user is not a system admin
        if db_obj.protected and not system_admin:
            # Log the attempt to delete a protected template
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Attempt to delete protected template {id} blocked for tenant {tenant_id}")
            return None
            
        # Versions will be cascade-deleted due to relationship settings in the model
        await db.delete(db_obj)
        await db.commit()
        return db_obj

    async def get_versions_by_template_and_tenant(
        self, db: AsyncSession, *, template_id: UUID, tenant_id: UUID
    ) -> List[TemplateVersion]:
        """
        Get all versions of a template, ensuring tenant access control.
        
        For global templates, any tenant can access. For tenant templates,
        only the owning tenant can access.
        
        Args:
            db: The database session
            template_id: The template ID
            tenant_id: The tenant ID for permission checking
            
        Returns:
            List of TemplateVersion objects sorted by version (descending)
        """
        import logging
        logger = logging.getLogger(__name__)
        
        try:
            # Check if this is a tenant-specific template or a global template
            from sqlalchemy import or_
            template_check = await db.execute(
                select(self.model.id, self.model.scope).where(
                    self.model.id == template_id,
                    or_(
                        # Allow access if it's a tenant-specific template belonging to this tenant
                        (self.model.tenant_id == tenant_id) & (self.model.scope == TemplateScope.TENANT),
                        # Allow access if it's a global template (accessible to all tenants)
                        (self.model.scope == TemplateScope.GLOBAL)
                    )
                )
            )
            
            template_info = template_check.first()
            if not template_info:
                logger.warning(f"Template {template_id} not found or not accessible by tenant {tenant_id}")
                return [] # Return empty list rather than raising an exception - API layer will handle presentation
            
            # If we got here, the template exists and the tenant has access
            stmt = (
                select(self.version_model)
                .where(self.version_model.template_id == template_id)
                .order_by(desc(self.version_model.version))
            )
            
            result = await db.execute(stmt)
            versions = result.unique().scalars().all()
            
            logger.info(f"Found {len(versions)} versions for template {template_id}")
            return versions
            
        except Exception as e:
            logger.error(f"Error retrieving versions for template {template_id}: {str(e)}")
            # Re-raise to let API layer handle error response
            raise

crud_admin_template = CRUDAdminTemplate(Template, TemplateVersion) 