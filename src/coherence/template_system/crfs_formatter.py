"""Coherence Response Format Standard (CRFS) formatter.

This module implements the CRFS v2.0 formatter for structured API response formatting.
It provides a flexible, extensible system for formatting any API response type.
"""

import json
import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from jinja2 import Environment
from jinja2.exceptions import TemplateError

logger = logging.getLogger(__name__)


class CRFSFormatter:
    """Formats API responses according to the Coherence Response Format Standard."""

    def __init__(self, format_config: Dict[str, Any], template_context: Optional[Dict[str, Any]] = None):
        """Initialize the CRFS formatter.

        Args:
            format_config: CRFS configuration dictionary
            template_context: Optional template context for unified templates
        """
        self.config = format_config
        self.template_context = template_context or {}
        self.version = format_config.get("crfs_version", "2.0")
        self.kind = format_config.get("kind", "coherence-response")

        # Validate configuration
        if self.kind != "coherence-response":
            raise ValueError(f"Invalid format kind: {self.kind}")

        # Initialize Jinja2 environment
        self.env = Environment(autoescape=False)
        self._register_filters()

        # Regex for safely converting dot notation to .get() calls
        # Avoid splitting on method calls like get()
        self._safe_property_regex = re.compile(r"(\w+)\.(?!get\(|GET\(|Get\()(\w+)")
        self._special_property_regex = re.compile(r"(\w+)\.(@[\w\-]+)")

    def format(self, context: Dict[str, Any]) -> str:
        """Format a response according to the CRFS configuration.

        Args:
            context: Template context containing API response and metadata

        Returns:
            Formatted response string
        """
        try:
            logger.info(f"CRFS formatting with config: {self.config}")
            logger.info(f"Context keys: {list(context.keys())}")

            # Recursively unwrap any standard API response wrappers
            context = self._unwrap_result(context)

            # Extract base data using data_path
            base_data_path = self.config.get("data_path", ".")
            data = self._extract_data(context, base_data_path)

            # Get format configuration
            format_config = self.config.get("format", {"type": "raw"})
            format_type = format_config.get("type", "raw")

            # Format based on type
            if format_type == "raw":
                return self._format_raw(data)
            elif format_type == "template":
                return self._format_template(format_config, context)
            elif format_type == "structured":
                return self._format_structured(format_config, data, context)
            else:
                logger.warning(
                    f"Unknown format type: {format_type}, falling back to raw"
                )
                return self._format_raw(data)

        except Exception as e:
            logger.exception("Error formatting response")
            return self._handle_error(e, context)

    def _unwrap_result(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Unwrap standard API response wrappers in the context."""

        result = context.get("result")
        metadata: Dict[str, Any] = context.get("response_metadata", {})

        while isinstance(result, dict) and all(
            k in result for k in ["success", "status_code", "result"]
        ):
            metadata = {
                "success": result.get("success"),
                "status_code": result.get("status_code"),
                "headers": result.get("headers"),
                "content_type": result.get("content_type"),
                "cached": result.get("cached"),
                "raw_response": result.get("raw_response"),
            }
            result = result.get("result")

        if result is not None:
            context = dict(context)
            context["result"] = result
            if metadata:
                context["response_metadata"] = metadata

        return context

    def _register_filters(self):
        """Register custom Jinja2 filters for template rendering."""
        # JSON formatting
        self.env.filters["json_pretty"] = lambda x: json.dumps(x, indent=2)
        self.env.filters["json"] = lambda x: json.dumps(x)

        # String formatting
        self.env.filters["upper"] = lambda x: str(x).upper()
        self.env.filters["lower"] = lambda x: str(x).lower()
        self.env.filters["title"] = lambda x: str(x).title()
        self.env.filters["replace"] = lambda x, old, new: str(x).replace(old, new)

        # Number formatting
        self.env.filters["number"] = self._format_number
        self.env.filters["currency"] = self._format_currency
        self.env.filters["percentage"] = lambda x: f"{float(x):.1%}"

        # Date formatting
        self.env.filters["date"] = self._format_date
        self.env.filters["datetime"] = self._format_datetime

        # Math operations
        self.env.filters["multiply"] = lambda x, y: float(x) * float(y)
        self.env.filters["divide"] = lambda x, y: (
            float(x) / float(y) if float(y) != 0 else 0
        )
        self.env.filters["add"] = lambda x, y: float(x) + float(y)
        self.env.filters["subtract"] = lambda x, y: float(x) - float(y)

    def _format_raw(self, data: Any) -> str:
        """Format data as raw JSON output.

        Args:
            data: Data to format

        Returns:
            JSON string representation
        """
        if isinstance(data, str):
            return data
        return json.dumps(data, indent=2)

    def _format_template(
        self, format_config: Dict[str, Any], context: Dict[str, Any]
    ) -> str:
        """Format using a simple template string.

        Args:
            format_config: Format configuration containing template
            context: Template context

        Returns:
            Rendered template string
        """
        template_str = format_config.get("template", "No template provided")
        return self._render_template(template_str, context)

    def _format_structured(
        self, format_config: Dict[str, Any], data: Any, context: Dict[str, Any]
    ) -> str:
        """Format using structured sections.

        Args:
            format_config: Format configuration containing structure
            data: Extracted data
            context: Template context

        Returns:
            Formatted output with sections
        """
        structure = format_config.get("structure", {})
        output_parts = []

        # Add header if present
        if header := structure.get("header"):
            rendered_header = self._render_template(header, context)
            output_parts.append(f"# {rendered_header}\n")

        # Process summary if present
        if summary_config := self.config.get("summary"):
            if summary_output := self._format_summary(summary_config, context):
                output_parts.append(summary_output)

        # Process sections
        sections = structure.get("sections", [])
        logger.info(f"Processing {len(sections)} sections")

        # Check what's in the context result
        if "result" in context:
            result = context["result"]
            logger.info(
                f"Result type: {type(result)}, keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}"
            )
            if isinstance(result, dict) and "@graph" in result:
                logger.info(
                    f"@graph found, type: {type(result['@graph'])}, length: {len(result['@graph']) if hasattr(result['@graph'], '__len__') else 'N/A'}"
                )
            if isinstance(result, dict) and "@context" in result:
                logger.info(f"@context found, type: {type(result['@context'])}")

        for i, section in enumerate(sections):
            try:
                logger.info(
                    f"Processing section {i}: type={section.get('type')}, data_path={section.get('data_path')}"
                )
                if formatted_section := self._format_section(section, data, context):
                    output_parts.append(formatted_section)
                    logger.info(f"Section {i} produced output")
                else:
                    logger.info(f"Section {i} produced no output")
            except Exception as e:
                logger.error(
                    f"Error formatting section {section.get('id', 'unknown')}: {e}"
                )
                continue

        logger.info(f"Total output parts: {len(output_parts)}")
        return "\n\n".join(output_parts)

    def _format_summary(
        self, summary_config: Dict[str, Any], context: Dict[str, Any]
    ) -> Optional[str]:
        """Format summary fields.

        Args:
            summary_config: Summary configuration
            context: Template context

        Returns:
            Formatted summary or None
        """
        fields = summary_config.get("fields", [])
        if not fields:
            return None

        output_parts = ["## Summary"]

        for field in fields:
            try:
                label = field.get("label", "")
                value_template = field.get("value", "")
                format_type = field.get("format", "text")

                # Render value
                value = self._render_template(value_template, context)

                # Apply formatting
                if format_type == "number":
                    value = self._format_number(value)
                elif format_type == "currency":
                    value = self._format_currency(value)
                elif format_type == "datetime":
                    value = self._format_datetime(value)
                elif format_type == "percentage":
                    value = f"{float(value):.1%}"

                output_parts.append(f"- **{label}**: {value}")

            except Exception as e:
                logger.error(f"Error formatting summary field {field}: {e}")
                continue

        return "\n".join(output_parts)

    def _format_section(
        self, section: Dict[str, Any], data: Any, context: Dict[str, Any]
    ) -> Optional[str]:
        """Format a single section.

        Args:
            section: Section configuration
            data: Section data
            context: Template context

        Returns:
            Formatted section or None
        """
        # Check visibility conditions
        if conditionals := section.get("conditionals", {}):
            visibility = self._check_visibility(conditionals, context)
            logger.debug(
                f"Section visibility check: {visibility} for conditionals: {conditionals}"
            )
            if not visibility:
                return None

        # Get section-specific data
        section_data_path = section.get("data_path")
        if section_data_path:
            logger.debug(f"Extracting data from path: {section_data_path}")
            section_data = self._extract_data(
                {"result": context.get("result", {}), "data": data}, section_data_path
            )
            logger.debug(
                f"Extracted data type: {type(section_data)}, length: {len(section_data) if section_data and hasattr(section_data, '__len__') else 'N/A'}"
            )
        else:
            section_data = data

        # Format based on section type
        section_type = section.get("type", "text")
        section_id = section.get("id", "unknown")

        logger.debug(
            f"Formatting section {section_id} of type {section_type} with data: {str(section_data)[:100]}..."
        )

        try:
            if section_type == "text":
                return self._format_text_section(section, context)
            elif section_type == "conditional":
                return self._format_conditional_section(section, context)
            elif section_type == "list":
                return self._format_list_section(section, section_data, context)
            elif section_type == "object":
                return self._format_object_section(section, section_data, context)
            elif section_type == "json":
                return self._format_json_section(section, section_data)
            else:
                logger.warning(
                    f"Unknown section type: {section_type} for section {section_id}"
                )
                return None

        except Exception as e:
            logger.error(f"Error formatting section {section_id}: {e}")
            return None

    def _format_text_section(
        self, section: Dict[str, Any], context: Dict[str, Any]
    ) -> str:
        """Format a text section.

        Args:
            section: Section configuration
            context: Template context

        Returns:
            Formatted text
        """
        content = section.get("content", "")
        style = section.get("style", "normal")
        title = section.get("title")

        output_parts = []

        # Add title if present
        if title:
            output_parts.append(f"### {title}")

        # Render content
        rendered_content = self._render_template(content, context)

        # Apply style
        if style == "heading":
            rendered_content = f"# {rendered_content}"
        elif style == "success":
            rendered_content = f"✓ {rendered_content}"
        elif style == "error":
            rendered_content = f"✗ {rendered_content}"
        elif style == "info":
            rendered_content = f"ℹ {rendered_content}"
        elif style == "warning":
            rendered_content = f"⚠ {rendered_content}"

        output_parts.append(rendered_content)
        return "\n".join(output_parts)

    def _format_conditional_section(
        self, section: Dict[str, Any], context: Dict[str, Any]
    ) -> Optional[str]:
        """Format a conditional section.

        Args:
            section: Section configuration
            context: Template context

        Returns:
            Formatted output based on conditions
        """
        conditions = section.get("conditions", {})
        title = section.get("title")

        output_parts = []

        # Add title if present
        if title:
            output_parts.append(f"### {title}")

        # Evaluate conditions
        for _condition_name, condition_config in conditions.items():
            check = condition_config.get("check", "True")

            if self._evaluate_condition(check, context):
                content = condition_config.get("content", "")
                style = condition_config.get("style", "normal")

                # Render content
                rendered_content = self._render_template(content, context)

                # Apply style
                if style == "success":
                    rendered_content = f"✓ {rendered_content}"
                elif style == "error":
                    rendered_content = f"✗ {rendered_content}"
                elif style == "info":
                    rendered_content = f"ℹ {rendered_content}"
                elif style == "warning":
                    rendered_content = f"⚠ {rendered_content}"

                output_parts.append(rendered_content)
                break  # Only show first matching condition

        return "\n".join(output_parts) if output_parts else None

    def _format_list_section(
        self, section: Dict[str, Any], data: Any, context: Dict[str, Any]
    ) -> Optional[str]:
        """Format a list section.

        Args:
            section: Section configuration
            data: List data
            context: Template context

        Returns:
            Formatted list
        """
        title = section.get("title")
        item_format = section.get("item_format", {})

        # Handle both list and dict data
        items = []
        if isinstance(data, dict):
            # Convert dict to list of key-value pairs
            items = [{"key": k, "value": v} for k, v in data.items()]
        elif isinstance(data, list):
            items = data
        else:
            return None

        if not items:
            return None

        output_parts = []

        # Add title if present
        if title:
            output_parts.append(f"### {title}")

        # Format each item
        for i, item in enumerate(items):
            item_context = {**context, "item": item, "index": i}

            # Support both key-value dicts and regular items
            if isinstance(item, dict) and "key" in item and "value" in item:
                item_context.update(item)

            # Get item template
            template_str = item_format.get("template", "{{ item }}")
            formatted_item = self._render_template(template_str, item_context)

            # Apply formatting style
            style = item_format.get("style", "bullet")
            if style == "numbered":
                output_parts.append(f"{i + 1}. {formatted_item}")
            else:  # bullet
                output_parts.append(f"- {formatted_item}")

        return "\n".join(output_parts)

    def _format_object_section(
        self, section: Dict[str, Any], data: Any, context: Dict[str, Any]
    ) -> Optional[str]:
        """Format an object section.

        Args:
            section: Section configuration
            data: Object data
            context: Template context

        Returns:
            Formatted object
        """
        if not isinstance(data, dict):
            return None

        title = section.get("title")
        fields = section.get("fields", [])
        show_all = section.get("show_all", True)

        output_parts = []

        # Add title if present
        if title:
            output_parts.append(f"### {title}")

        # If fields are specified, show only those
        if fields:
            for field_config in fields:
                key = field_config.get("key")
                label = field_config.get("label", key)

                if key in data:
                    value = data[key]
                    output_parts.append(f"- **{label}**: {value}")
        # Otherwise show all fields
        elif show_all:
            for key, value in data.items():
                output_parts.append(f"- **{key}**: {value}")

        return "\n".join(output_parts) if output_parts else None

    def _format_json_section(self, section: Dict[str, Any], data: Any) -> str:
        """Format a JSON section.

        Args:
            section: Section configuration
            data: Data to format as JSON

        Returns:
            Formatted JSON
        """
        title = section.get("title")
        indent = section.get("indent", 2)

        output_parts = []

        # Add title if present
        if title:
            output_parts.append(f"### {title}")

        # Format as JSON
        output_parts.append("```json")
        output_parts.append(json.dumps(data, indent=indent))
        output_parts.append("```")

        return "\n".join(output_parts)

    def _extract_data(self, context: Dict[str, Any], path: str) -> Any:
        """Extract data using JSON-Path-lite notation or eval for method calls.

        Args:
            context: Data context
            path: Path string (e.g., "result.data.items[0].name" or "result.get('@graph')")

        Returns:
            Extracted data or None
        """
        if path == ".":
            return context

        try:
            # If the path contains method calls (like .get()), evaluate it
            if ".get(" in path or path.startswith("get("):
                # Create a safe environment for evaluation
                safe_env = {
                    "result": context.get("result", {}),
                    "results": context.get("results", {}),
                    "data": context.get("data", {}),
                    "parameters": context.get("parameters", {}),
                }

                logger.debug(f"Evaluating path: {path}")

                # Evaluate the path expression
                result = eval(path, {"__builtins__": {}}, safe_env)
                logger.debug(
                    f"Evaluated result type: {type(result)}, value: {str(result)[:100]}..."
                )
                return result

            # Otherwise, use the standard path parsing
            current = context
            parts = self._parse_path(path)

            for part in parts:
                if isinstance(part, int):
                    # Array index
                    if isinstance(current, list) and 0 <= part < len(current):
                        current = current[part]
                    else:
                        return None
                else:
                    # Object key
                    if isinstance(current, dict):
                        current = current.get(part)
                    else:
                        return None

                if current is None:
                    return None

            return current

        except Exception as e:
            logger.error(f"Error extracting data from path {path}: {e}")
            logger.error(
                f"Context type: {type(context)}, keys: {list(context.keys()) if isinstance(context, dict) else 'Not a dict'}"
            )
            return None

    def _parse_path(self, path: str) -> List[Union[str, int]]:
        """Parse a JSON-Path-lite string into path components.

        Args:
            path: Path string

        Returns:
            List of path components
        """
        parts = []
        # Split on dots, but handle array notation
        tokens = path.split(".")

        for token in tokens:
            # Check for array notation
            if "[" in token and "]" in token:
                # Extract key and index
                match = re.match(r"([^[]+)\[(\d+)\]", token)
                if match:
                    key = match.group(1)
                    index = int(match.group(2))
                    if key:  # If there's a key before the bracket
                        parts.append(key)
                    parts.append(index)
                else:
                    parts.append(token)
            else:
                parts.append(token)

        return parts

    def _render_template(self, template_str: str, context: Dict[str, Any]) -> str:
        """Render a Jinja2 template string.

        Args:
            template_str: Template string
            context: Template context

        Returns:
            Rendered string
        """
        try:
            # Pre-process template string to handle special characters in property names
            import re

            processed_template = template_str

            # Convert object.@property to object["@property"] for special character properties
            processed_template = re.sub(
                r"(\b\w+)\.(@[\w\-]+)", r'\1["\2"]', processed_template
            )

            # Convert object["@property"].field to object["@property"]["field"]
            processed_template = re.sub(
                r'\["(@[\w\-]+)"\]\.(\w+)', r'["\1"]["\2"]', processed_template
            )

            # Convert simple dot notation to safe get() access
            processed_template = self._special_property_regex.sub(
                lambda m: f'{m.group(1)}["{m.group(2)}"]', processed_template
            )

            processed_template = self._safe_property_regex.sub(
                lambda m: f'{m.group(1)}.get("{m.group(2)}")', processed_template
            )

            # Make template access safer with get() method
            # Convert {{ object.field }} to {{ object.get("field") }}
            # Skip if already a method call, filter, or within quotes
            processed_template = re.sub(
                r'\{\{\s*(\w+)\.(\w+)(?!\s*\()(?!\s*\|)(?![^"]*"\s*\}\})\s*\}\}',
                r'{{ \1.get("\2") }}',
                processed_template,
            )

            # Convert nested access {{ object.field.subfield }} safely
            processed_template = re.sub(
                r"\{\{\s*(\w+)\.(\w+)\.(\w+)(?!\s*\()(?!\s*\|)\s*\}\}",
                r'{{ \1.get("\2", {}).get("\3") }}',
                processed_template,
            )

            # Convert complex chains like object.get("field").subfield to safe access
            processed_template = re.sub(
                r'\.get\("([^"]+)"\)\.(\w+)(?!\s*\()',
                r'.get("\1", {}).get("\2")',
                processed_template,
            )

            template = self.env.from_string(processed_template)
            return template.render(**context)
        except TemplateError as e:
            logger.error(f"Template error: {e}")
            logger.debug(f"Original template: {template_str}")
            logger.debug(
                f"Processed template: {processed_template if 'processed_template' in locals() else 'N/A'}"
            )
            return template_str  # Return unrendered template on error

    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """Evaluate a condition expression.

        Args:
            condition: Condition string
            context: Context for evaluation

        Returns:
            True if condition passes, False otherwise
        """
        try:
            # Transform dotted property access to safe get() calls
            safe_condition = self._special_property_regex.sub(
                lambda m: f'{m.group(1)}.get("{m.group(2)}")',
                condition,
            )
            safe_condition = self._safe_property_regex.sub(
                lambda m: f'{m.group(1)}.get("{m.group(2)}")',
                safe_condition,
            )

            safe_condition = re.sub(
                r'\.get\("([^"]+)"\)\.(\w+)',
                r'.get("\1", {}).get("\2")',
                safe_condition,
            )

            # Create a safe evaluation environment
            safe_env = {
                "result": context.get("result", {}),
                "results": context.get("results", {}),
                "parameters": context.get("parameters", {}),
                "data": context.get("data", {}),
                "error": context.get("error", None),
                "true": True,
                "false": False,
                "True": True,
                "False": False,
                "none": None,
                "None": None,
                "len": len,
                "str": str,
                "int": int,
                "float": float,
                "bool": bool,
            }

            # Evaluate the condition
            return bool(eval(safe_condition, {"__builtins__": {}}, safe_env))

        except Exception as e:
            logger.error(f"Error evaluating condition '{condition}': {e}")
            logger.error(f"Context keys: {list(context.keys())}")
            if "result" in context and isinstance(context["result"], dict):
                logger.error(f"Result keys: {list(context['result'].keys())}")
            logger.error(f"Safe env keys: {list(safe_env.keys())}")
            # Return False to hide elements with problematic conditions
            return False

    def _check_visibility(
        self, conditionals: Dict[str, Any], context: Dict[str, Any]
    ) -> bool:
        """Check if a section should be visible based on conditionals.

        Args:
            conditionals: Conditional configuration
            context: Template context

        Returns:
            True if visible, False otherwise
        """
        if show_if := conditionals.get("show_if"):
            result = self._evaluate_condition(show_if, context)
            logger.info(f"Condition '{show_if}' evaluated to: {result}")
            return result

        if hide_if := conditionals.get("hide_if"):
            result = not self._evaluate_condition(hide_if, context)
            logger.info(f"Hide condition '{hide_if}' evaluated to: {result}")
            return result

        return True  # Default to visible

    def _handle_error(self, error: Exception, context: Dict[str, Any]) -> str:
        """Handle formatting errors.

        Args:
            error: Exception that occurred
            context: Template context

        Returns:
            Error message
        """
        if error_template := self.config.get("error_template"):
            error_context = {
                **context,
                "error": {"message": str(error), "type": type(error).__name__},
            }
            try:
                return self._render_template(error_template, error_context)
            except Exception:
                pass  # Fall through to default error

        return f"Error formatting response: {str(error)}"

    def _format_number(self, value: Any) -> str:
        """Format a number value.

        Args:
            value: Number to format

        Returns:
            Formatted number string
        """
        try:
            num = float(value)
            if num.is_integer():
                return f"{int(num):,}"
            else:
                return f"{num:,.2f}"
        except (ValueError, TypeError):
            return str(value)

    def _format_currency(self, value: Any, currency: str = "USD") -> str:
        """Format a currency value.

        Args:
            value: Amount to format
            currency: Currency code

        Returns:
            Formatted currency string
        """
        try:
            amount = float(value)
            if currency == "USD":
                return f"${amount:,.2f}"
            else:
                return f"{amount:,.2f} {currency}"
        except (ValueError, TypeError):
            return str(value)

    def _format_date(self, value: Any, format_str: str = "%Y-%m-%d") -> str:
        """Format a date value.

        Args:
            value: Date to format
            format_str: strftime format string

        Returns:
            Formatted date string
        """
        try:
            if isinstance(value, str):
                # Try to parse ISO format
                dt = datetime.fromisoformat(value.replace("Z", "+00:00"))
            elif isinstance(value, datetime):
                dt = value
            else:
                return str(value)

            return dt.strftime(format_str)
        except Exception:
            return str(value)

    def _format_datetime(
        self, value: Any, format_str: str = "%Y-%m-%d %H:%M:%S"
    ) -> str:
        """Format a datetime value.

        Args:
            value: DateTime to format
            format_str: strftime format string

        Returns:
            Formatted datetime string
        """
        return self._format_date(value, format_str)

    @classmethod
    def from_unified_template(cls, template_data: Dict[str, Any]) -> 'CRFSFormatter':
        """
        Create a CRFS formatter from unified template data.
        
        Args:
            template_data: Unified template data
            
        Returns:
            Configured CRFS formatter instance
        """
        # Extract CRFS configuration from unified template
        response_config = template_data.get("response", {})
        crfs_config = response_config.get("crfs", {})
        
        # Ensure proper CRFS structure
        if "kind" not in crfs_config:
            crfs_config["kind"] = "coherence-response"
        if "crfs_version" not in crfs_config:
            crfs_config["crfs_version"] = "2.0"
        
        # Add error mapping from template
        error_mapping = response_config.get("error_mapping", {})
        if error_mapping:
            crfs_config["error_mapping"] = error_mapping
        
        # Add template context for enhanced rendering
        template_context = {
            "template_key": template_data.get("meta", {}).get("key"),
            "endpoint_id": template_data.get("meta", {}).get("endpoint_id"),
            "action_config": template_data.get("action_config", {}),
            "docs": template_data.get("docs_config", {})
        }
        
        return cls(crfs_config, template_context)
    
    def format_with_error_mapping(self, context: Dict[str, Any], error_code: Optional[str] = None) -> str:
        """
        Format response with unified template error mapping support.
        
        Args:
            context: Template context containing API response and metadata
            error_code: Optional error code for error mapping
            
        Returns:
            Formatted response string
        """
        try:
            # Check for error conditions
            if error_code and "error_mapping" in self.config:
                error_mapping = self.config["error_mapping"]
                if error_code in error_mapping:
                    error_message = error_mapping[error_code]
                    # Render error message as template if needed
                    if isinstance(error_message, str) and "{{" in error_message:
                        return self._render_template(error_message, context)
                    return error_message
            
            # Regular CRFS formatting
            return self.format(context)
            
        except Exception as e:
            logger.error(f"Error in unified template CRFS formatting: {e}")
            return self._handle_error(e, context)
    
    def format_with_auto_selection(self, context: Dict[str, Any], accept_header: Optional[str] = None) -> str:
        """
        Format response with automatic format selection based on Accept header.
        
        Args:
            context: Template context containing API response and metadata
            accept_header: Optional Accept header for format selection
            
        Returns:
            Formatted response string
        """
        # Check if auto-selection is enabled
        if self.config.get("auto_select", False):
            format_name = self._auto_select_format(accept_header)
            # Update config with selected format
            if format_name and "formats" in self.config:
                if format_name in self.config["formats"]:
                    self.config["format"] = self.config["formats"][format_name]
                    logger.info(f"Auto-selected format: {format_name}")
        
        return self.format(context)
    
    def _auto_select_format(self, accept_header: Optional[str] = None) -> Optional[str]:
        """
        Auto-select format based on Accept header.
        
        Args:
            accept_header: HTTP Accept header value
            
        Returns:
            Selected format name or None
        """
        if not accept_header or "formats" not in self.config:
            return None
        
        formats = self.config.get("formats", {})
        
        # Priority order for content types
        content_type_mapping = {
            "application/json": ["application/json", "json"],
            "text/plain": ["text/plain", "text"],
            "text/html": ["text/html", "html"],
            "application/xml": ["application/xml", "xml"],
            "text/markdown": ["text/markdown", "markdown"],
        }
        
        # Parse Accept header (simplified)
        accept_types = [t.strip().split(";")[0] for t in accept_header.split(",")]
        
        # Find first matching format
        for accept_type in accept_types:
            if accept_type in content_type_mapping:
                for format_name in content_type_mapping[accept_type]:
                    if format_name in formats:
                        return format_name
        
        # Check for wildcard
        if "*/*" in accept_types:
            return self.config.get("default_format", "structured")
        
        return None
    
    def format_mixed_response(self, context: Dict[str, Any], sections: List[Dict[str, Any]]) -> str:
        """
        Format response with mixed sections that can have different format types.
        
        Args:
            context: Template context containing API response and metadata
            sections: List of section configurations with format types
            
        Returns:
            Formatted response string with mixed formats
        """
        output_parts = []
        
        for section in sections:
            section_format = section.get("format", {})
            section_data = self._extract_data(context, section.get("data_path", "."))
            
            # Create temporary formatter for this section
            section_formatter = CRFSFormatter(section_format, self.template_context)
            formatted_section = section_formatter.format({"result": section_data})
            
            # Add section wrapper if specified
            if section_title := section.get("title"):
                output_parts.append(f"## {section_title}")
            
            output_parts.append(formatted_section)
        
        return "\n\n".join(output_parts)
    
    def format_streaming_response(self, context_stream) -> str:
        """
        Format streaming response data.
        
        Args:
            context_stream: Generator/iterator of context chunks
            
        Returns:
            Formatted streaming response
        """
        output_parts = []
        
        for chunk in context_stream:
            try:
                formatted_chunk = self.format(chunk)
                output_parts.append(formatted_chunk)
            except Exception as e:
                logger.error(f"Error formatting stream chunk: {e}")
                continue
        
        return "\n".join(output_parts)
    
    def get_response_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about the CRFS configuration and formatting options.
        
        Returns:
            Metadata dictionary
        """
        return {
            "crfs_version": self.version,
            "kind": self.kind,
            "available_formats": list(self.config.get("formats", {}).keys()),
            "auto_select_enabled": self.config.get("auto_select", False),
            "default_format": self.config.get("default_format", "structured"),
            "supports_streaming": True,
            "supports_mixed_formats": True
        }
    
    def generate_parameter_help(self, parameters_schema: Dict[str, Any]) -> str:
        """
        Generate help text for parameters using CRFS formatting.
        
        Args:
            parameters_schema: Parameter schema from unified template
            
        Returns:
            Formatted parameter help text
        """
        if not parameters_schema:
            return "No parameters required."
        
        help_sections = []
        
        for param_name, param_config in parameters_schema.items():
            param_type = param_config.get("type", "string")
            required = param_config.get("required", False)
            description = param_config.get("description", "")
            
            # Format parameter info
            param_info = f"**{param_name}** ({param_type})"
            if required:
                param_info += " *required*"
            
            if description:
                param_info += f": {description}"
            
            # Add constraints
            constraints = []
            if param_config.get("minimum") is not None:
                constraints.append(f"min: {param_config['minimum']}")
            if param_config.get("maximum") is not None:
                constraints.append(f"max: {param_config['maximum']}")
            if param_config.get("minLength") is not None:
                constraints.append(f"min length: {param_config['minLength']}")
            if param_config.get("maxLength") is not None:
                constraints.append(f"max length: {param_config['maxLength']}")
            if param_config.get("enum"):
                constraints.append(f"options: {', '.join(map(str, param_config['enum']))}")
            
            if constraints:
                param_info += f" ({', '.join(constraints)})"
            
            help_sections.append(param_info)
        
        return "\n".join(help_sections)
