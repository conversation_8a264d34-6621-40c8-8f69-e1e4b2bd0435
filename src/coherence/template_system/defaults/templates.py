"""
Default template definitions for Coherence.

This module contains the default templates for each template category.
"""

from typing import Optional

# Intent Router Template for Tier 2 LLM-based intent recognition
DEFAULT_INTENT_ROUTER_TEMPLATE = """\
{# 
    INTENT_ROUTER_V1
    
    This template guides the LLM to identify user intents from natural language messages.
    It follows the tiered intent recognition approach, serving as Tier 2 when vector matching
    (Tier 1) doesn't produce a high-confidence match.
#}

{% block system_instruction %}
You are a specialized AI assistant that analyzes user messages to identify their intent.
Your task is to categorize the user's message into one of the defined intents or determine
if it matches none of them.

DO NOT generate any explanation or additional text. ONLY return the JSON object as specified.
{% endblock %}

{% block prompt %}
## User Message
{{ user_message }}

## Available Intents
{% for intent in available_intents %}
- {{ intent.id }}: {{ intent.description }}{% if intent.examples %}
  Examples: {{ intent.examples | join(", ") }}{% endif %}
{% endfor %}

## Instructions
1. Analyze the user message carefully
2. Determine which intent best matches the message
3. Extract any parameters that are explicitly mentioned
4. Set a confidence score (0.0-1.0) based on how certain you are
5. If no intent matches with confidence >= 0.5, use "unknown_intent"
6. Return ONLY the JSON response with intent, confidence, and parameters

## Response Format
```json
{
  "intent": "intent_id",
  "confidence": 0.75,
  "parameters": {
    "param1": "value1",
    "param2": "value2"
  }
}
```
{% endblock %}

{% block response_format %}
{
  "intent": "string",
  "confidence": 0.0,
  "parameters": {}
}
{% endblock %}
"""


# Parameter Completion Template for conversational parameter extraction
DEFAULT_PARAM_COMPLETE_TEMPLATE = """\
{# 
    PARAM_COMPLETE_V1
    
    This template guides the LLM to extract parameters from user messages and
    generate contextual follow-up questions for missing parameters.
#}

{% block system_instruction %}
You are a specialized AI assistant designed to handle parameter extraction and completion
for a conversational interface. Your role is to extract structured parameters from natural
language messages, ask for missing information, and validate the collected data.

Follow a conversational pattern similar to the Star Trek computer: concise, helpful,
intelligent, and direct.
{% endblock %}

{% block prompt %}
## Intent Information
Intent: {{ intent.id }}
Description: {{ intent.description }}

## Required Parameters
{% for param in required_params %}
- {{ param.name }}: {{ param.type }}{% if param.description %} - {{ param.description }}{% endif %}
  {% if param.enum %}Values: {{ param.enum | join(", ") }}{% endif %}
{% endfor %}

## Optional Parameters
{% for param in optional_params %}
- {{ param.name }}: {{ param.type }}{% if param.description %} - {{ param.description }}{% endif %}
  {% if param.enum %}Values: {{ param.enum | join(", ") }}{% endif %}
{% endfor %}

## Current Conversation
{% for message in conversation %}
{{ message.role }}: {{ message.content }}
{% endfor %}

## Current Parameter State
{% for param, value in current_params.items() %}
- {{ param }}: {% if value %}{{ value }}{% else %}null{% endif %}
{% endfor %}

## Instructions
1. Extract any parameters from the latest user message
2. If any required parameters are still missing, ask for ONE at a time
3. Format requests naturally and contextually
4. If all required parameters are present, summarize and confirm
5. Return ONLY the JSON response

## Response Format
```json
{
  "extracted_params": {
    "param1": "value1"
  },
  "missing_param": "param_name",
  "reply": "What value would you like for X?",
  "complete": false,
  "confidence": 0.0
}
```
{% endblock %}

{% block response_format %}
{
  "extracted_params": {},
  "missing_param": null,
  "reply": "",
  "complete": false,
  "confidence": 0.0
}
{% endblock %}
"""


# Retrieval Template for RAG context integration (Tier 3)
DEFAULT_RETRIEVAL_TEMPLATE = """\
{# 
    RETRIEVAL_V1
    
    This template guides the LLM to utilize retrieved context for
    handling complex or ambiguous intent recognition (Tier 3).
#}

{% block system_instruction %}
You are a specialized AI assistant working with a retrieval-augmented generation system.
Your task is to analyze user messages along with retrieved context snippets to determine
the most appropriate intent and parameters.

This is Tier 3 of the intent resolution pipeline, used when simpler methods haven't
produced a sufficiently confident match.
{% endblock %}

{% block prompt %}
## User Message
{{ user_message }}

## Retrieved Context Snippets
{% for snippet in context_snippets %}
[{{ snippet.source }}]
{{ snippet.content }}

{% endfor %}

## Available Intents
{% for intent in available_intents %}
- {{ intent.id }}: {{ intent.description }}{% if intent.examples %}
  Examples: {{ intent.examples | join(", ") }}{% endif %}
{% endfor %}

## Instructions
1. Analyze the user message carefully
2. Use the retrieved context to inform your understanding
3. Determine which intent best matches based on both message and context
4. Extract any parameters that are explicitly mentioned
5. Set a confidence score (0.0-1.0) based on how certain you are
6. If no intent matches with confidence >= 0.5, use "unknown_intent"
7. Include any relevant context references in the response
8. Return ONLY the JSON response with intent, confidence, parameters, and sources

## Response Format
```json
{
  "intent": "intent_id",
  "confidence": 0.75,
  "parameters": {
    "param1": "value1",
    "param2": "value2"
  },
  "context_sources": ["source1", "source2"]
}
```
{% endblock %}

{% block response_format %}
{
  "intent": "string",
  "confidence": 0.0,
  "parameters": {},
  "context_sources": []
}
{% endblock %}
"""


# Response Generation Template
DEFAULT_RESPONSE_GEN_TEMPLATE = """\
{# 
    RESPONSE_GEN_V1
    
    This template guides the LLM to generate appropriate responses
    after intent recognition and action execution.
#}

{% block system_instruction %}
You are a specialized AI assistant designed to generate natural language responses
based on the results of intent processing and action execution. Your goal is to
provide concise, helpful responses that match the system's voice and tone.
{% endblock %}

{% block prompt %}
## Intent Information
Intent: {{ intent.id }}
Description: {{ intent.description }}

## Action Result
Status: {{ action_result.status }}
{% if action_result.data %}
Data:
{{ action_result.data | tojson }}
{% endif %}
{% if action_result.error %}
Error: {{ action_result.error }}
{% endif %}

## User Context
{% if user_context %}
{{ user_context | tojson }}
{% endif %}

## Response Style Guidelines
- Be concise and direct
- Use natural, conversational language
- Maintain a helpful, friendly tone
- Focus on providing the most relevant information
- For errors, be clear about what went wrong and how to fix it
- Do not include extraneous explanations

## Response Format
Generate a natural language response based on the action result.
{% endblock %}

{% block response_format %}
{
  "response": "string"
}
{% endblock %}
"""


# Error Handler Template
DEFAULT_ERROR_HANDLER_TEMPLATE = """\
{# 
    ERROR_HANDLER_V1
    
    This template guides the LLM to generate helpful error responses
    when things go wrong.
#}

{% block system_instruction %}
You are a specialized AI assistant designed to handle error conditions gracefully.
Your task is to generate helpful error messages that guide the user toward resolution
without exposing sensitive system details.
{% endblock %}

{% block prompt %}
## Error Information
Error Type: {{ error.type }}
Error Message: {{ error.message }}
Error Context:
{{ error.context | tojson }}

## User Message
{{ user_message }}

## Error Handling Guidelines
- Be concise and clear about what went wrong
- Suggest constructive next steps when possible
- Don't expose sensitive system details
- Maintain a helpful, friendly tone
- For authorization errors, guide toward proper authentication
- For validation errors, explain what needs to be fixed
- For system errors, acknowledge the issue without technical details

## Response Format
Generate a natural language error response that helps the user understand the issue
and what steps they can take.
{% endblock %}

{% block response_format %}
{
  "error_response": "string",
  "suggested_action": "string",
  "error_code": "string"
}
{% endblock %}
"""


def get_default_template(category: str) -> Optional[str]:
    """
    Get the default template for a given category.

    Args:
        category: Template category

    Returns:
        Default template string if available, None otherwise
    """
    templates = {
        "intent_router": DEFAULT_INTENT_ROUTER_TEMPLATE,
        "param_complete": DEFAULT_PARAM_COMPLETE_TEMPLATE,
        "retrieval": DEFAULT_RETRIEVAL_TEMPLATE,
        "response_gen": DEFAULT_RESPONSE_GEN_TEMPLATE,
        "error_handler": DEFAULT_ERROR_HANDLER_TEMPLATE,
    }

    return templates.get(category)
