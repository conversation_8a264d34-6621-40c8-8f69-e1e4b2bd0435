"""
UI Field Generation System for Unified Templates.

Converts parameter schemas to UI field definitions compatible with shadcn/ui components.
"""

from typing import Any, Dict, List, Optional
from enum import Enum


class UIFieldType(str, Enum):
    """Supported UI field types for form generation."""
    
    TEXT = "text"
    EMAIL = "email"
    PASSWORD = "password"
    NUMBER = "number"
    DATE = "date"
    DATETIME_LOCAL = "datetime-local"
    TIME = "time"
    TEL = "tel"
    URL = "url"
    TEXTAREA = "textarea"
    SELECT = "select"
    MULTISELECT = "multiselect"
    CHECKBOX = "checkbox"
    RADIO = "radio"
    SWITCH = "switch"
    SLIDER = "slider"
    FILE = "file"
    COLOR = "color"


class UIFieldGenerator:
    """
    Generates UI field definitions from parameter schemas.
    
    Converts OpenAPI-style parameter schemas into UI field configurations
    that can be consumed by frontend components.
    """
    
    @staticmethod
    def generate_ui_fields(parameters_schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate UI field definitions from parameter schema.
        
        Args:
            parameters_schema: OpenAPI-style parameter schema
            
        Returns:
            Dictionary of UI field definitions
        """
        if not parameters_schema:
            return {}
            
        ui_fields = {}
        
        for param_name, param_config in parameters_schema.items():
            ui_field = UIFieldGenerator._convert_parameter_to_ui_field(
                param_name, param_config
            )
            ui_fields[param_name] = ui_field
            
        return ui_fields
    
    @staticmethod
    def _convert_parameter_to_ui_field(param_name: str, param_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert a single parameter to UI field definition.
        
        Args:
            param_name: Parameter name
            param_config: Parameter configuration
            
        Returns:
            UI field definition
        """
        param_type = param_config.get("type", "string")
        param_format = param_config.get("format")
        param_enum = param_config.get("enum")
        
        # Base UI field structure
        ui_field = {
            "type": UIFieldGenerator._determine_ui_field_type(param_type, param_format, param_enum),
            "label": UIFieldGenerator._generate_label(param_name),
            "required": param_config.get("required", False),
            "description": param_config.get("description"),
            "placeholder": UIFieldGenerator._generate_placeholder(param_name, param_type),
        }
        
        # Add type-specific configurations
        UIFieldGenerator._add_type_specific_config(ui_field, param_config)
        
        # Add validation rules
        UIFieldGenerator._add_validation_rules(ui_field, param_config)
        
        # Clean up None values
        return {k: v for k, v in ui_field.items() if v is not None}
    
    @staticmethod
    def _determine_ui_field_type(param_type: str, param_format: Optional[str], param_enum: Optional[List]) -> str:
        """Determine the appropriate UI field type."""
        if param_enum:
            return UIFieldType.SELECT if len(param_enum) <= 10 else UIFieldType.MULTISELECT
        
        if param_type == "boolean":
            return UIFieldType.SWITCH
        
        if param_type == "integer" or param_type == "number":
            return UIFieldType.NUMBER
        
        if param_type == "string":
            if param_format == "date":
                return UIFieldType.DATE
            elif param_format == "date-time":
                return UIFieldType.DATETIME_LOCAL
            elif param_format == "time":
                return UIFieldType.TIME
            elif param_format == "email":
                return UIFieldType.EMAIL
            elif param_format == "password":
                return UIFieldType.PASSWORD
            elif param_format == "uri" or param_format == "url":
                return UIFieldType.URL
            elif param_format == "color":
                return UIFieldType.COLOR
            else:
                return UIFieldType.TEXT
        
        if param_type == "array":
            return UIFieldType.MULTISELECT
        
        return UIFieldType.TEXT
    
    @staticmethod
    def _generate_label(param_name: str) -> str:
        """Generate a human-readable label from parameter name."""
        # Convert snake_case to Title Case
        return param_name.replace("_", " ").title()
    
    @staticmethod
    def _generate_placeholder(param_name: str, param_type: str) -> str:
        """Generate placeholder text for the field."""
        if param_type == "string":
            return f"Enter {param_name.replace('_', ' ')}"
        elif param_type in ["integer", "number"]:
            return f"Enter {param_name.replace('_', ' ')} value"
        elif param_type == "boolean":
            return None  # Switches don't need placeholders
        else:
            return f"Select {param_name.replace('_', ' ')}"
    
    @staticmethod
    def _add_type_specific_config(ui_field: Dict[str, Any], param_config: Dict[str, Any]) -> None:
        """Add type-specific configuration to UI field."""
        field_type = ui_field["type"]
        
        if field_type == UIFieldType.SELECT or field_type == UIFieldType.MULTISELECT:
            # Add options for select fields
            enum_values = param_config.get("enum", [])
            ui_field["options"] = [
                {"value": str(val), "label": str(val).title()}
                for val in enum_values
            ]
        
        elif field_type == UIFieldType.NUMBER:
            # Add number constraints
            if "minimum" in param_config:
                ui_field["min"] = param_config["minimum"]
            if "maximum" in param_config:
                ui_field["max"] = param_config["maximum"]
            if "multipleOf" in param_config:
                ui_field["step"] = param_config["multipleOf"]
            if param_config.get("type") == "integer":
                ui_field["step"] = ui_field.get("step", 1)
        
        elif field_type == UIFieldType.TEXT or field_type == UIFieldType.TEXTAREA:
            # Add string constraints
            if "minLength" in param_config:
                ui_field["minLength"] = param_config["minLength"]
            if "maxLength" in param_config:
                ui_field["maxLength"] = param_config["maxLength"]
            if "pattern" in param_config:
                ui_field["pattern"] = param_config["pattern"]
            
            # Use textarea for long text fields
            max_length = param_config.get("maxLength", 0)
            if max_length > 255 or param_config.get("format") == "textarea":
                ui_field["type"] = UIFieldType.TEXTAREA
                ui_field["rows"] = min(max(max_length // 80, 3), 10)
        
        elif field_type == UIFieldType.DATE or field_type == UIFieldType.DATETIME_LOCAL:
            # Add date constraints
            if "minimum" in param_config:
                ui_field["min"] = param_config["minimum"]
            if "maximum" in param_config:
                ui_field["max"] = param_config["maximum"]
        
        # Add default value if present
        if "default" in param_config:
            ui_field["defaultValue"] = param_config["default"]
    
    @staticmethod
    def _add_validation_rules(ui_field: Dict[str, Any], param_config: Dict[str, Any]) -> None:
        """Add validation rules to UI field."""
        validation = {}
        
        # Required validation
        if param_config.get("required", False):
            validation["required"] = "This field is required"
        
        # Type-specific validation
        param_type = param_config.get("type", "string")
        
        if param_type == "string":
            if "minLength" in param_config:
                validation["minLength"] = {
                    "value": param_config["minLength"],
                    "message": f"Must be at least {param_config['minLength']} characters"
                }
            if "maxLength" in param_config:
                validation["maxLength"] = {
                    "value": param_config["maxLength"],
                    "message": f"Must be no more than {param_config['maxLength']} characters"
                }
            if "pattern" in param_config:
                validation["pattern"] = {
                    "value": param_config["pattern"],
                    "message": "Invalid format"
                }
        
        elif param_type in ["integer", "number"]:
            if "minimum" in param_config:
                validation["min"] = {
                    "value": param_config["minimum"],
                    "message": f"Must be at least {param_config['minimum']}"
                }
            if "maximum" in param_config:
                validation["max"] = {
                    "value": param_config["maximum"],
                    "message": f"Must be no more than {param_config['maximum']}"
                }
        
        if validation:
            ui_field["validation"] = validation


class UIFormGenerator:
    """
    Generates complete form configurations from unified template schemas.
    """
    
    @staticmethod
    def generate_form_config(template_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a complete form configuration from unified template.
        
        Args:
            template_data: Unified template data
            
        Returns:
            Complete form configuration for frontend consumption
        """
        parameters = template_data.get("parameters", {})
        schema = parameters.get("schema", {})
        existing_ui_fields = parameters.get("ui_fields", {})
        
        # Generate UI fields from schema
        generated_ui_fields = UIFieldGenerator.generate_ui_fields(schema)
        
        # Merge with existing UI field overrides
        final_ui_fields = {**generated_ui_fields, **existing_ui_fields}
        
        # Create form configuration
        form_config = {
            "title": template_data.get("meta", {}).get("key", "Form").replace("_", " ").title(),
            "description": template_data.get("docs_config", {}).get("description"),
            "fields": final_ui_fields,
            "submitLabel": "Submit",
            "validation": {
                "mode": "onBlur",
                "reValidateMode": "onChange"
            }
        }
        
        # Add form-level configuration from template
        if "prompts" in template_data:
            extraction_prompts = template_data["prompts"].get("extraction", {})
            for field_name, field_config in final_ui_fields.items():
                if field_name in extraction_prompts:
                    field_config["helpText"] = extraction_prompts[field_name]
        
        return form_config