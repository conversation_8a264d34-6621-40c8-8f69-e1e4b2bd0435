"""
Template service for Coherence.

This module provides the service layer for template management,
including retrieval, caching, and resolution of the template hierarchy.
"""

import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, delete, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.core.redis_client import RedisClient, get_redis_client
from src.coherence.models.template import Template, TemplateDependency, TemplateVersion
from src.coherence.template_system.engine.renderer import TemplateRenderer

logger = logging.getLogger(__name__)


class TemplateService:
    """
    Service for managing templates in Coherence.

    Handles template retrieval, caching, and resolution across the
    template hierarchy (global → pack → tenant).

    Attributes:
        renderer: Template renderer for rendering templates
        template_cache: In-memory cache of templates
    """

    def __init__(self):
        """Initialize the template service."""
        self.renderer = TemplateRenderer()
        self.template_cache: Dict[str, Dict[str, Any]] = {}
        self.redis_client: Optional[RedisClient] = None
        self.TEMPLATE_UPDATE_CHANNEL = "template_updates"

    async def get_template(
        self,
        db: AsyncSession,
        key: str,
        category: str,
        tenant_id: Optional[uuid.UUID] = None,
        pack_id: Optional[uuid.UUID] = None,
        language: str = "en",
    ) -> Optional[Template]:
        """
        Get a template, following the hierarchy: tenant → pack → global.

        Args:
            db: Database session
            key: Template key
            category: Template category (intent_router, param_complete, etc.)
            tenant_id: Tenant ID for tenant-specific templates
            pack_id: Pack ID for industry pack templates
            language: Template language code

        Returns:
            Template object if found, None otherwise
        """
        # Check cache first
        cache_key = (
            f"{key}:{category}:{tenant_id or 'None'}:{pack_id or 'None'}:{language}"
        )
        if cache_key in self.template_cache:
            return self.template_cache[cache_key]

        # Try to find tenant-specific template
        template = None
        if tenant_id:
            template = await self._get_template_by_scope(
                db, key, category, "tenant", tenant_id, language
            )

        # Fall back to pack template
        if not template and pack_id:
            template = await self._get_template_by_scope(
                db, key, category, "pack", pack_id, language
            )

        # Fall back to global template
        if not template:
            template = await self._get_template_by_scope(
                db, key, category, "global", None, language
            )

        # Cache the result
        if template:
            self.template_cache[cache_key] = template

        return template

    async def _get_template_by_scope(
        self,
        db: AsyncSession,
        key: str,
        category: str,
        scope: str,
        scope_id: Optional[uuid.UUID],
        language: str,
    ) -> Optional[Template]:
        """
        Get a template by scope.

        Args:
            db: Database session
            key: Template key
            category: Template category
            scope: Template scope (global, pack, tenant)
            scope_id: Scope ID (None for global)
            language: Template language code

        Returns:
            Template object if found, None otherwise
        """
        query = (
            select(Template)
            .where(
                and_(
                    Template.key == key,
                    Template.category == category,
                    Template.scope == scope,
                    Template.language == language,
                )
            )
            .order_by(Template.version.desc())
        )

        if scope == "global":
            # Global templates have no scope_id
            query = query.where(Template.scope_id.is_(None))
        else:
            # Pack and tenant templates have a scope_id
            query = query.where(Template.scope_id == scope_id)

        result = await db.execute(query)
        return result.scalars().first()

    async def list_templates(
        self,
        db: AsyncSession,
        tenant_id: Optional[uuid.UUID] = None,
        category: Optional[str] = None,
        scope: Optional[str] = None,
        language: str = "en",
    ) -> List[Template]:
        """
        List templates matching the given criteria.

        Args:
            db: Database session
            tenant_id: Filter by tenant ID
            category: Filter by category
            scope: Filter by scope
            language: Filter by language

        Returns:
            List of templates matching the criteria
        """
        query = select(Template).where(Template.language == language)

        if tenant_id:
            query = query.where(
                (Template.tenant_id == tenant_id) | (Template.scope == "global")
            )

        if category:
            query = query.where(Template.category == category)

        if scope:
            query = query.where(Template.scope == scope)

        result = await db.execute(query)
        return result.scalars().all()

    async def create_template(
        self,
        db: AsyncSession,
        key: str,
        category: str,
        body: str,
        scope: str = "tenant",
        scope_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        language: str = "en",
        description: Optional[str] = None,
        actions: Optional[Dict[str, Any]] = None,
        parameters: Optional[Dict[str, Any]] = None,
        response_format: Optional[Dict[str, Any]] = None,
        created_by: Optional[uuid.UUID] = None,
    ) -> Template:
        """
        Create a new template.

        Args:
            db: Database session
            key: Template key
            category: Template category
            body: Template body
            scope: Template scope (global, pack, tenant)
            scope_id: Scope ID (None for global)
            tenant_id: Tenant ID for tenant-specific templates
            language: Template language code
            description: Template description
            actions: JSON actions for intent_router templates
            parameters: JSON parameters schema
            created_by: User ID who created the template

        Returns:
            Newly created template

        Raises:
            ValueError: If template is invalid
        """
        # Validate the template body
        if not self._validate_template_syntax(body):
            raise ValueError("Invalid template syntax")

        # Check for existing template
        existing_query = (
            select(func.count())
            .select_from(Template)
            .where(
                and_(
                    Template.key == key,
                    Template.category == category,
                    Template.scope == scope,
                    Template.scope_id == scope_id,
                    Template.language == language,
                )
            )
        )

        result = await db.execute(existing_query)
        count = result.scalar()

        # If template exists, increment version
        version = 1
        if count > 0:
            version_query = (
                select(func.max(Template.version))
                .select_from(Template)
                .where(
                    and_(
                        Template.key == key,
                        Template.category == category,
                        Template.scope == scope,
                        Template.scope_id == scope_id,
                        Template.language == language,
                    )
                )
            )

            result = await db.execute(version_query)
            max_version = result.scalar() or 0
            version = max_version + 1

        # Create new template
        template = Template(
            id=uuid.uuid4(),
            key=key,
            category=category,
            body=body,
            scope=scope,
            scope_id=scope_id,
            tenant_id=tenant_id,
            language=language,
            version=version,
            description=description,
            actions=actions,
            parameters=parameters,
            response_format=response_format,
            created_by=created_by,
        )

        db.add(template)

        # Create version history
        template_version = TemplateVersion(
            template_id=template.id,
            version=version,
            body=body,
            actions=actions,
            parameters=parameters,
            response_format=template.response_format,  # Include response_format
            editor_id=created_by,
            change_reason="Initial creation"
            if version == 1
            else f"Update to version {version}",
        )

        db.add(template_version)

        # Store template dependencies
        dependencies = self._extract_dependencies(body)
        for dep_type, dep_name in dependencies:
            # Find the dependency template
            dep_template = await self._find_dependency_template(
                db, dep_name, tenant_id=tenant_id
            )

            if dep_template:
                dependency = TemplateDependency(
                    id=uuid.uuid4(),
                    template_id=template.id,
                    depends_on_template_id=dep_template.id,
                    dependency_type=dep_type,
                )

                db.add(dependency)

        await db.commit()
        await db.refresh(template)

        # Invalidate cache
        self._invalidate_cache(key, category, tenant_id, scope_id, language)

        # Process vectorization and other post-creation actions
        await self._after_template_change(db, template, is_new=True)

        return template

    async def update_template(
        self,
        db: AsyncSession,
        template_id: uuid.UUID,
        body: str,
        actions: Optional[Dict[str, Any]] = None,
        parameters: Optional[Dict[str, Any]] = None,
        response_format: Optional[Dict[str, Any]] = None,
        editor_id: Optional[uuid.UUID] = None,
        change_reason: Optional[str] = None,
    ) -> Template:
        """
        Update an existing template.

        Args:
            db: Database session
            template_id: Template ID
            body: New template body
            actions: Updated JSON actions
            parameters: Updated JSON parameters
            editor_id: User ID who updated the template
            change_reason: Reason for the update

        Returns:
            Updated template

        Raises:
            ValueError: If template is invalid or not found
        """
        # Validate the template body
        if not self._validate_template_syntax(body):
            raise ValueError("Invalid template syntax")

        # Find the template
        query = select(Template).where(Template.id == template_id)
        result = await db.execute(query)
        template = result.scalars().first()

        if not template:
            raise ValueError(f"Template with ID {template_id} not found")

        # Log template data before update
        logger.info(f"Template before update: id={template_id}, version={template.version}")
        logger.info(f"Template actions before update: {template.actions}")
        logger.info(f"Template parameters before update: {template.parameters}")
        logger.info(f"Incoming actions: {actions}")
        logger.info(f"Incoming parameters: {parameters}")

        # Update template with the new values
        # Most importantly - ensure body is updated first
        # This is required because of a trigger on the database that checks for content changes
        template.body = body
        template.updated_at = datetime.now()
        template.version += 1

        # Update optional fields if provided
        if actions is not None:
            template.actions = actions

        if parameters is not None:
            template.parameters = parameters
            
        if response_format is not None:
            template.response_format = response_format

        # Create version history with null checks
        try:
            template_version = TemplateVersion(
                template_id=template.id,
                version=template.version,
                body=body,
                actions=actions,  # Use the passed actions parameter instead of template.actions
                parameters=parameters,  # Use the passed parameters parameter instead of template.parameters
                response_format=template.response_format,  # Include response_format
                editor_id=editor_id,
                change_reason=change_reason or f"Update to version {template.version}",
            )
            logger.info(f"Successfully created template version: {template_version}")
        except Exception as e:
            logger.error(f"Error creating template version: {str(e)}", exc_info=True)
            raise

        db.add(template_version)

        # Update dependencies
        # First, delete existing dependencies
        delete_stmt = delete(TemplateDependency).where(TemplateDependency.template_id == template.id)
        await db.execute(delete_stmt)

        # Then, add new dependencies
        dependencies = self._extract_dependencies(body)
        for dep_type, dep_name in dependencies:
            # Find the dependency template
            dep_template = await self._find_dependency_template(
                db, dep_name, tenant_id=template.tenant_id
            )

            if dep_template:
                dependency = TemplateDependency(
                    id=uuid.uuid4(),
                    template_id=template.id,
                    depends_on_template_id=dep_template.id,
                    dependency_type=dep_type,
                )

                db.add(dependency)

        await db.commit()
        await db.refresh(template)

        # Invalidate cache
        self._invalidate_cache(
            template.key,
            template.category,
            template.tenant_id,
            template.scope_id,
            template.language,
        )

        # Broadcast template update via Redis pub/sub
        await self._broadcast_template_update(template)

        # Process vectorization and other post-update actions
        await self._after_template_change(db, template, is_new=False)

        return template

    async def delete_template(
        self,
        db: AsyncSession,
        template_id: uuid.UUID,
    ) -> bool:
        """
        Delete a template.

        Args:
            db: Database session
            template_id: Template ID

        Returns:
            True if template was deleted, False otherwise
        """
        # Find the template first to get cache invalidation info
        query = select(Template).where(Template.id == template_id)
        result = await db.execute(query)
        template = result.scalars().first()

        if not template:
            return False

        # Delete template dependencies
        await db.execute(
            select(TemplateDependency)
            .where(TemplateDependency.template_id == template_id)
            .delete()
        )

        # Delete template versions
        await db.execute(
            select(TemplateVersion)
            .where(TemplateVersion.template_id == template_id)
            .delete()
        )

        # Delete template
        await db.delete(template)
        await db.commit()

        # Invalidate cache
        self._invalidate_cache(
            template.key,
            template.category,
            template.tenant_id,
            template.scope_id,
            template.language,
        )

        # Broadcast template deletion via Redis pub/sub
        await self._broadcast_template_update(template, is_delete=True)

        return True
        
    async def _after_template_change(
        self,
        db: AsyncSession,
        template: Template,
        is_new: bool = False,
    ) -> None:
        """
        Handle post-template change operations including vectorization.
        
        Args:
            db: Database session
            template: The template that was created or updated
            is_new: Whether this is a new template or an update
        """
        try:
            # Publish template.updated or template.created event
            event_type = "template.created" if is_new else "template.updated"
            
            await self._publish_template_update(
                event_type=event_type,
                template_id=str(template.id),
                key=template.key,
                category=template.category,
                tenant_id=str(template.tenant_id) if template.tenant_id else None,
                scope_id=str(template.scope_id) if template.scope_id else None,
                language=template.language,
            )
            
            # Call vectorization service to index the template
            try:
                # Build vector index name based on scope
                if template.scope == "tenant" and template.tenant_id:
                    index_name = f"template_idx_{template.tenant_id}"
                elif template.scope == "pack" and template.scope_id:
                    index_name = f"template_idx_pack_{template.scope_id}"
                else:
                    index_name = "template_idx_global"
                    
                # Import the vector indexer if it exists
                try:
                    from src.coherence.services.vector_indexer import VectorIndexer
                    vector_indexer = VectorIndexer()
                    
                    # Vectorize and index template
                    await vector_indexer.upsert_template(
                        db=db,
                        template_id=str(template.id),  # Convert UUID to string
                        template_key=template.key, 
                        template_category=template.category,
                        template_body=template.body,
                        template_description=template.description or "",
                        index_name=index_name
                    )
                    logger.info(f"Indexed template {template.key} in {index_name}")
                except ImportError:
                    # Vector indexer not available, log but continue
                    logger.warning("VectorIndexer not available, skipping template indexing")
            except Exception as e:
                # Log vectorization error but don't fail the template operation
                logger.error(f"Error vectorizing template: {str(e)}")
                
        except Exception as e:
            # Log but don't fail template operation
            logger.error(f"Error in post-template processing: {str(e)}")

    async def render_template(
        self,
        db: AsyncSession,
        key: str,
        category: str,
        context: Dict[str, Any],
        tenant_id: Optional[uuid.UUID] = None,
        pack_id: Optional[uuid.UUID] = None,
        language: str = "en",
    ) -> str:
        """
        Render a template with the given context.

        Args:
            db: Database session
            key: Template key
            category: Template category
            context: Context variables for template rendering
            tenant_id: Tenant ID for tenant-specific templates
            pack_id: Pack ID for industry pack templates
            language: Template language code

        Returns:
            Rendered template

        Raises:
            ValueError: If template is not found
        """
        # Get template
        template = await self.get_template(
            db, key, category, tenant_id, pack_id, language
        )

        if not template:
            raise ValueError(
                f"Template not found: key={key}, category={category}, "
                f"tenant_id={tenant_id}, pack_id={pack_id}, language={language}"
            )

        # Check if we have a response format configuration
        if template.response_format:
            # Check if response_format is multi-format (dictionary with content types as keys)
            actual_content_type = context.get("content_type")
            selected_format = None
            
            # Check if this is a multi-format configuration
            if isinstance(template.response_format, dict) and not template.response_format.get("kind"):
                # Multi-format configuration - select based on actual content type
                if actual_content_type:
                    # Try exact match first
                    if actual_content_type in template.response_format:
                        selected_format = template.response_format[actual_content_type]
                    else:
                        # Try to match by prefix (e.g., "application/json" matches "application/json;charset=utf-8")
                        for content_type, format_config in template.response_format.items():
                            if content_type == "preferred":  # Skip the preferred key
                                continue
                            if actual_content_type.startswith(content_type):
                                selected_format = format_config
                                break
                
                # If no match found by content type, use the preferred format
                if not selected_format and "preferred" in template.response_format:
                    preferred_type = template.response_format["preferred"]
                    if preferred_type in template.response_format:
                        selected_format = template.response_format[preferred_type]
                        logger.info(f"Using preferred format for template {template.key}: {preferred_type}")
            else:
                # Single format configuration - use as is
                selected_format = template.response_format
            
            if selected_format:
                logger.info(f"Using response format for template {template.key}: {selected_format.get('kind')}")
                # Check if it's CRFS format
                if selected_format.get("kind") == "coherence-response":
                    logger.info(f"Using CRFS formatter for template {template.key} with content type {actual_content_type}")
                    from src.coherence.template_system.crfs_formatter import CRFSFormatter
                    formatter = CRFSFormatter(selected_format)
                    return formatter.format(context)
                # Fall back to legacy response formatter
                elif selected_format.get("type") != "raw":
                    from src.coherence.template_system.response_formatter import ResponseFormatter
                    formatter = ResponseFormatter(selected_format)
                    return formatter.format_response(context)

        # Standard template rendering
        # Get template dependencies
        templates_dict = {}
        templates_dict[f"{template.key}:{template.category}"] = template.body

        # Add dependencies to the renderer
        dependency_query = (
            select(TemplateDependency, Template)
            .join(Template, TemplateDependency.depends_on_template_id == Template.id)
            .where(TemplateDependency.template_id == template.id)
        )

        result = await db.execute(dependency_query)
        for _dependency, dep_template in result:
            templates_dict[
                f"{dep_template.key}:{dep_template.category}"
            ] = dep_template.body

        # Set up renderer with templates
        renderer = TemplateRenderer(templates_dict)

        # Render template
        return renderer.render(f"{template.key}:{template.category}", context)

    def _validate_template_syntax(self, body: str) -> bool:
        """
        Validate template syntax.

        Args:
            body: Template body

        Returns:
            True if template syntax is valid, False otherwise
        """
        try:
            # Create a temporary renderer to validate the template
            renderer = TemplateRenderer({"temp": body})
            renderer.validate("temp")
            return True
        except Exception as e:
            logger.error(f"Template validation error: {str(e)}")
            return False

    def _extract_dependencies(self, body: str) -> List[Tuple[str, str]]:
        """
        Extract template dependencies from the template body.

        Args:
            body: Template body

        Returns:
            List of (dependency_type, dependency_name) tuples
        """
        dependencies = []

        try:
            # Extract extends and includes
            for line in body.split("\n"):
                line = line.strip()
                if line.startswith("{%") and "extends" in line:
                    # Extract template name from {% extends "template_name" %}
                    template_name = line.split("extends")[1].strip().strip("\"'%}")
                    dependencies.append(("extends", template_name))
                elif line.startswith("{%") and "include" in line:
                    # Extract template name from {% include "template_name" %}
                    template_name = line.split("include")[1].strip().strip("\"'%}")
                    dependencies.append(("includes", template_name))

            return dependencies
        except Exception as e:
            logger.error(f"Error extracting dependencies: {str(e)}")
            return []

    async def _find_dependency_template(
        self,
        db: AsyncSession,
        name: str,
        tenant_id: Optional[uuid.UUID] = None,
    ) -> Optional[Template]:
        """
        Find a dependency template by name.

        Args:
            db: Database session
            name: Template name (key:category format)
            tenant_id: Tenant ID for tenant-specific templates

        Returns:
            Template object if found, None otherwise
        """
        try:
            # Parse name into key and category (format: key:category)
            key, category = name.split(":")

            # Try to find in tenant scope first
            if tenant_id:
                query = (
                    select(Template)
                    .where(
                        and_(
                            Template.key == key,
                            Template.category == category,
                            Template.tenant_id == tenant_id,
                        )
                    )
                    .order_by(Template.version.desc())
                )

                result = await db.execute(query)
                template = result.scalars().first()

                if template:
                    return template

            # Fall back to global template
            query = (
                select(Template)
                .where(
                    and_(
                        Template.key == key,
                        Template.category == category,
                        Template.scope == "global",
                    )
                )
                .order_by(Template.version.desc())
            )

            result = await db.execute(query)
            return result.scalars().first()
        except Exception as e:
            logger.error(f"Error finding dependency template: {str(e)}")
            return None

    def _invalidate_cache(
        self,
        key: str,
        category: str,
        tenant_id: Optional[uuid.UUID],
        scope_id: Optional[uuid.UUID],
        language: str,
    ):
        """
        Invalidate template cache.

        Args:
            key: Template key
            category: Template category
            tenant_id: Tenant ID
            scope_id: Scope ID
            language: Language code
        """
        cache_key = (
            f"{key}:{category}:{tenant_id or 'None'}:{scope_id or 'None'}:{language}"
        )

        if cache_key in self.template_cache:
            del self.template_cache[cache_key]

    def clear_cache(self):
        """Clear the entire template cache."""
        self.template_cache.clear()

    async def _broadcast_template_update(
        self, template: Template, is_delete: bool = False
    ) -> None:
        """
        Broadcast template update via Redis pub/sub.

        Args:
            template: Template that was updated
            is_delete: Whether the template was deleted
        """
        try:
            # Lazy-initialize Redis client if needed
            if self.redis_client is None:
                self.redis_client = await get_redis_client()

            # Create message payload
            message = {
                "event_type": "template_deleted" if is_delete else "template_updated",
                "template_id": str(template.id),
                "key": template.key,
                "category": template.category,
                "tenant_id": str(template.tenant_id) if template.tenant_id else None,
                "scope": template.scope,
                "scope_id": str(template.scope_id) if template.scope_id else None,
                "version": template.version,
                "timestamp": datetime.now().isoformat(),
            }

            # Publish to Redis channel
            await self.redis_client.publish(self.TEMPLATE_UPDATE_CHANNEL, message)
            logger.info(
                f"Template update broadcast: {message['event_type']}",
                template_id=message["template_id"],
                key=message["key"],
                category=message["category"],
            )
        except Exception as e:
            # Don't fail the template operation if broadcasting fails
            logger.error(f"Error broadcasting template update: {str(e)}")

    async def _publish_template_update(
        self, 
        event_type: str,
        template_id: str,
        key: str, 
        category: str,
        tenant_id: Optional[str] = None,
        scope_id: Optional[str] = None,
        language: str = "en"
    ) -> None:
        """
        Publish template update event.
        
        Args:
            event_type: Type of event (e.g., "template.created", "template.updated")
            template_id: Template ID
            key: Template key
            category: Template category
            tenant_id: Tenant ID (if applicable)
            scope_id: Scope ID (if applicable)
            language: Language code
        """
        try:
            # Lazy-initialize Redis client if needed
            if self.redis_client is None:
                self.redis_client = await get_redis_client()

            # Create event payload
            event_payload = {
                "event_type": event_type,
                "template_id": template_id,
                "key": key,
                "category": category,
                "tenant_id": tenant_id,
                "scope_id": scope_id,
                "language": language,
                "timestamp": datetime.now().isoformat(),
            }

            # Publish event to Redis
            channel_name = f"template_events:{tenant_id}" if tenant_id else "template_events:global"
            await self.redis_client.publish(channel_name, event_payload)
            
            logger.info(
                f"Published template event: {event_type} - template_id={template_id}, key={key}, category={category}, channel={channel_name}"
            )
        except Exception as e:
            # Don't fail the template operation if publishing fails
            logger.error(f"Error publishing template update: {str(e)}")

    async def start_template_update_listener(
        self, cache_clear_callback: Optional[callable] = None
    ) -> None:
        """
        Start a listener for template updates from other application instances.

        Args:
            cache_clear_callback: Optional callback to execute when template cache needs to be cleared
        """
        try:
            # Lazy-initialize Redis client if needed
            if self.redis_client is None:
                self.redis_client = await get_redis_client()

            # Subscribe to channel
            pubsub = await self.redis_client.subscribe(self.TEMPLATE_UPDATE_CHANNEL)

            # Define message handler
            async def handle_template_update(
                channel: str, message: Dict[str, Any]
            ) -> None:
                if channel == self.TEMPLATE_UPDATE_CHANNEL:
                    try:
                        # Clear local cache for the updated template
                        if "key" in message and "category" in message:
                            logger.info(
                                f"Received template update: {message.get('event_type')}",
                                template_id=message.get("template_id"),
                                key=message.get("key"),
                                category=message.get("category"),
                            )

                            # Extract template info
                            key = message.get("key")
                            category = message.get("category")
                            tenant_id = message.get("tenant_id")
                            scope_id = message.get("scope_id")
                            language = message.get("language", "en")

                            # Clear cache for this template
                            if tenant_id:
                                try:
                                    tenant_uuid = uuid.UUID(tenant_id)
                                    self._invalidate_cache(
                                        key, category, tenant_uuid, None, language
                                    )
                                except ValueError:
                                    pass

                            if scope_id:
                                try:
                                    scope_uuid = uuid.UUID(scope_id)
                                    self._invalidate_cache(
                                        key, category, None, scope_uuid, language
                                    )
                                except ValueError:
                                    pass

                            # Also clear global template
                            self._invalidate_cache(key, category, None, None, language)

                            # Call the callback if provided
                            if cache_clear_callback:
                                await cache_clear_callback(message)
                    except Exception as e:
                        logger.error(
                            f"Error processing template update message: {str(e)}"
                        )

            # Start listening for messages
            import asyncio

            stop_event = asyncio.Event()
            listen_task = asyncio.create_task(
                self.redis_client.listen_for_messages(
                    pubsub, handle_template_update, stop_event
                )
            )

            logger.info("Template update listener started")
            return listen_task, stop_event

        except Exception as e:
            logger.error(f"Error starting template update listener: {str(e)}")
            return None, None

    # New methods for unified template support
    
    # NOTE: Commented out duplicate create_template method that was overriding the original
    # The original create_template at line 180 already handles all needed functionality
    # including scope_id which is required by the openapi endpoints
    #
    # async def create_template(
    #     self,
    #     db: AsyncSession,
    #     key: str,
    #     category: str,
    #     scope: str,
    #     tenant_id: Optional[uuid.UUID] = None,
    #     body: str = "",
    #     description: Optional[str] = None,
    #     protected: bool = False,
    #     language: str = "en",
    #     # Unified template fields
    #     endpoint_id: Optional[str] = None,
    #     intent_config: Optional[Dict[str, Any]] = None,
    #     action_config: Optional[Dict[str, Any]] = None,
    #     ui_fields: Optional[Dict[str, Any]] = None,
    #     prompts: Optional[Dict[str, Any]] = None,
    #     docs_config: Optional[Dict[str, Any]] = None,
    #     parameters: Optional[Dict[str, Any]] = None,
    #     response_format: Optional[Dict[str, Any]] = None,
    #     created_by: Optional[uuid.UUID] = None
    # ) -> Template:
    #     """
    #     Create a new template.
    #     
    #     Args:
    #         db: Database session
    #         key: Template key
    #         category: Template category
    #         scope: Template scope
    #         tenant_id: Tenant ID (for tenant-scoped templates)
    #         body: Template body (for legacy templates)
    #         description: Template description
    #         protected: Whether template is protected
    #         language: Language code
    #         endpoint_id: Endpoint identifier (unified templates)
    #         intent_config: Intent configuration (unified templates)
    #         action_config: Action configuration (unified templates)
    #         ui_fields: UI field definitions (unified templates)
    #         prompts: Prompt configurations (unified templates)
    #         docs_config: Documentation configuration (unified templates)
    #         parameters: Parameter schema (unified templates)
    #         response_format: Response format configuration (unified templates)
    #         created_by: User who created the template
    #         
    #     Returns:
    #         Created template
    #     """
    #     template = Template(
    #         id=uuid.uuid4(),
    #         key=key,
    #         category=category,
    #         scope=scope,
    #         tenant_id=tenant_id,
    #         body=body,
    #         description=description,
    #         protected=protected,
    #         language=language,
    #         # Unified template fields
    #         endpoint_id=endpoint_id,
    #         intent_config=intent_config,
    #         action_config=action_config,
    #         ui_fields=ui_fields,
    #         prompts=prompts,
    #         docs_config=docs_config,
    #         parameters=parameters,
    #         response_format=response_format,
    #         created_by=created_by
    #     )
    #     
    #     db.add(template)
    #     await db.commit()
    #     await db.refresh(template)
    #     
    #     # Broadcast template creation
    #     await self._broadcast_template_update(template)
    #     
    #     logger.info(f"Created template: {key} ({category})")
    #     return template
    
    # NOTE: Commented out duplicate list_templates method
    # The original list_templates at line 143 already exists
    # async def list_templates(
    #     self,
    #     db: AsyncSession,
    #     filters: Optional[List] = None,
    #     limit: Optional[int] = None,
    #     offset: Optional[int] = None
    # ) -> List[Template]:
    #     """
    #     List templates with optional filtering.
    #     
    #     Args:
    #         db: Database session
    #         filters: List of SQLAlchemy filter conditions
    #         limit: Maximum number of results
    #         offset: Number of results to skip
    #         
    #     Returns:
    #         List of templates
    #     """
    #     query = select(Template)
    #     
    #     if filters:
    #         for filter_condition in filters:
    #             query = query.where(filter_condition)
    #     
    #     query = query.order_by(Template.updated_at.desc())
    #     
    #     if offset:
    #         query = query.offset(offset)
    #     if limit:
    #         query = query.limit(limit)
    #     
    #     result = await db.execute(query)
    #     return result.scalars().all()
    
    async def get_template_by_id(
        self,
        db: AsyncSession,
        template_id: uuid.UUID
    ) -> Optional[Template]:
        """
        Get a template by its ID.
        
        Args:
            db: Database session
            template_id: Template ID
            
        Returns:
            Template if found, None otherwise
        """
        query = select(Template).where(Template.id == template_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    # NOTE: Commented out duplicate delete_template method
    # The original delete_template at line 454 already exists
    # async def delete_template(
    #     self,
    #     db: AsyncSession,
    #     template_id: uuid.UUID
    # ) -> bool:
    #     """
    #     Delete a template by ID.
    #     
    #     Args:
    #         db: Database session
    #         template_id: Template ID
    #         
    #     Returns:
    #         True if deleted, False if not found
    #     """
    #     # Get template for broadcasting
    #     template = await self.get_template_by_id(db, template_id)
    #     if not template:
    #         return False
    #     
    #     # Delete template
    #     delete_stmt = delete(Template).where(Template.id == template_id)
    #     result = await db.execute(delete_stmt)
    #     await db.commit()
    #     
    #     if result.rowcount > 0:
    #         # Broadcast template deletion
    #         await self._broadcast_template_update(template, is_delete=True)
    #         
    #         # Invalidate cache
    #         self._invalidate_cache(
    #             template.key,
    #             template.category,
    #             template.tenant_id,
    #             template.scope_id,
    #             template.language
    #         )
    #         
    #         logger.info(f"Deleted template: {template.key} ({template.category})")
    #         return True
    #     
    #     return False
