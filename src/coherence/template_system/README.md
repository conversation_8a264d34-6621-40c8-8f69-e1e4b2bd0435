# Coherence Template System

This directory contains the implementation of the Coherence template system, a key component that enables customization and flexibility in various parts of the middleware.

## Directory Structure

- `engine/`: Core template rendering components
  - `renderer.py`: Template rendering engine using Jinja2
- `services/`: Service layer for template management
  - `template_service.py`: CRUD operations and rendering service
- `defaults/`: Default template definitions
  - `templates.py`: Default templates for each category

## Template System Architecture

The template system follows a layered architecture:

1. **Template Models**: Database models defined in `models/template.py`
2. **Template Services**: CRUD and rendering services in `services/`
3. **Template Engine**: Jinja2-based rendering in `engine/`
4. **Template Defaults**: Global default templates in `defaults/`
5. **API Endpoints**: REST API for template management in `api/v1/endpoints/templates.py`

## Key Concepts

### Template Categories

Templates are categorized by their purpose:

1. `intent_router`: Used for LLM-based intent recognition
2. `param_complete`: Used for parameter extraction
3. `retrieval`: Used for RAG context integration
4. `response_gen`: Used for response generation
5. `error_handler`: Used for error handling

### Template Scope Hierarchy

Templates follow a hierarchical structure:

1. **Global templates**: System-wide baseline templates
2. **Pack templates**: Industry-specific templates
3. **Tenant templates**: Customer-specific customizations

### Template Inheritance

Templates support Jinja2 inheritance mechanisms:

- `{% extends "base_template" %}`: Inherit from a base template
- `{% include "partial_template" %}`: Include another template
- `{{ super() }}`: Access parent template content

## Implementation Details

### Template Engine

The template engine (`TemplateRenderer`) is a thin wrapper around Jinja2 that adds:

- Security sandboxing
- Custom filters
- Template validation
- Dependency tracking

### Template Service

The template service (`TemplateService`) provides:

- CRUD operations for templates
- Template hierarchy resolution (tenant → pack → global)
- Caching for performance optimization
- Versioning and dependency management

### Database Models

Templates are stored in several tables:

- `templates`: Core template content and metadata
- `template_versions`: Version history for templates
- `template_dependencies`: Relationships between templates
- `template_tests`: Test cases for templates

## Integration with Intent Pipeline

The template system integrates with the intent pipeline through:

1. Tier 2 (LLM) intent resolution using intent_router templates
2. Parameter completion using param_complete templates
3. Error handling using error_handler templates

See `intent_pipeline/template_integration.py` for details.

## Usage Examples

### Creating a Template

```python
await template_service.create_template(
    db=db,
    key="CUSTOM_INTENT_ROUTER",
    category="intent_router",
    body="{% block system_instruction %}...",
    scope="tenant",
    scope_id=tenant_id,
    tenant_id=tenant_id,
)
```

### Rendering a Template

```python
rendered = await template_service.render_template(
    db=db,
    key="INTENT_ROUTER_V1",
    category="intent_router",
    context={
        "user_message": "Book a flight to New York",
        "available_intents": [...],
    },
    tenant_id=tenant_id,
)
```

## Template Format

Templates follow a block-based structure:

```jinja
{# Template name and description #}

{% block system_instruction %}
You are a specialized AI assistant that...
{% endblock %}

{% block prompt %}
## User Message
{{ user_message }}

## Available Options
...
{% endblock %}

{% block response_format %}
{
  "key": "value"
}
{% endblock %}
```