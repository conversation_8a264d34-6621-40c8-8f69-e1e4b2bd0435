"""
Unified Template Generator for Coherence Template System.

Generates complete unified templates from OpenAPI specifications and other sources.
Consolidates action, intent, error handling, documentation, and UI generation into single templates.
"""

import json
import logging
from typing import Any, Dict, List, Optional, Set, Union
from urllib.parse import urlparse

from src.coherence.models.template import TemplateCategory, TemplateScope
from src.coherence.template_system.ui_generator import UIFieldGenerator
from src.coherence.core.llm.factory import LLMFactory

logger = logging.getLogger(__name__)


class UnifiedTemplateGenerator:
    """
    Generates unified templates from various sources (OpenAPI, manual, etc.).
    
    Creates complete templates containing all components:
    - Intent configuration
    - Action configuration  
    - Parameter schemas and UI fields
    - Response formatting (CRFS)
    - Error handling
    - Documentation
    """
    
    def __init__(self, llm_factory: Optional[LLMFactory] = None):
        """Initialize the unified template generator.
        
        Args:
            llm_factory: LLM factory for enhanced generation
        """
        self.llm_factory = llm_factory
    
    def generate_from_openapi_endpoint(
        self,
        operation: Dict[str, Any],
        path: str,
        method: str,
        base_url: str = "",
        spec_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a unified template from an OpenAPI operation.
        
        Args:
            operation: OpenAPI operation object
            path: API path
            method: HTTP method
            base_url: Base URL for the API
            spec_info: OpenAPI spec info section
            
        Returns:
            Complete unified template data
        """
        endpoint_id = f"{method.upper()}_{path}"
        operation_id = operation.get("operationId", endpoint_id.replace("/", "_").replace("{", "").replace("}", ""))
        
        # Generate base template structure
        template_data = {
            "meta": {
                "key": operation_id.lower(),
                "endpoint_id": endpoint_id,
                "version": 1
            },
            "intent": self._generate_intent_config(operation, path, method),
            "action": self._generate_action_config(operation, path, method, base_url),
            "parameters": self._generate_parameters_config(operation),
            "prompts": self._generate_prompts_config(operation, path, method),
            "response": self._generate_response_config(operation),
            "docs_config": self._generate_docs_config(operation, spec_info)
        }
        
        # Add test data if available
        test_data = self._generate_test_data(operation, path, method)
        if test_data:
            template_data["test_data"] = test_data
        
        return template_data
    
    def _generate_intent_config(self, operation: Dict[str, Any], path: str, method: str) -> Dict[str, Any]:
        """Generate intent configuration for the operation."""
        summary = operation.get("summary", "")
        description = operation.get("description", "")
        operation_id = operation.get("operationId", "")
        
        # Generate natural language patterns
        patterns = []
        
        # Base patterns from operation details
        if summary:
            patterns.append(summary.lower())
        
        # Method-specific patterns
        if method.upper() == "GET":
            patterns.extend([
                f"get {operation_id.replace('_', ' ')}",
                f"fetch {operation_id.replace('_', ' ')}",
                f"retrieve {operation_id.replace('_', ' ')}"
            ])
        elif method.upper() == "POST":
            patterns.extend([
                f"create {operation_id.replace('_', ' ')}",
                f"add {operation_id.replace('_', ' ')}",
                f"submit {operation_id.replace('_', ' ')}"
            ])
        elif method.upper() == "PUT":
            patterns.extend([
                f"update {operation_id.replace('_', ' ')}",
                f"modify {operation_id.replace('_', ' ')}",
                f"edit {operation_id.replace('_', ' ')}"
            ])
        elif method.upper() == "DELETE":
            patterns.extend([
                f"delete {operation_id.replace('_', ' ')}",
                f"remove {operation_id.replace('_', ' ')}"
            ])
        
        # Path-based patterns
        path_words = [word for word in path.split("/") if word and not word.startswith("{")]
        if path_words:
            resource = " ".join(path_words)
            patterns.append(f"{method.lower()} {resource}")
        
        # Enhanced patterns using LLM if available
        if self.llm_factory and (summary or description):
            enhanced_patterns = self._generate_enhanced_patterns(summary, description, method, path)
            patterns.extend(enhanced_patterns)
        
        # Remove duplicates and clean up
        patterns = list(set([p.strip() for p in patterns if p.strip()]))
        
        return {
            "patterns": patterns[:10],  # Limit to top 10 patterns
            "confidence_threshold": 0.85,
            "fallback_templates": ["general_api_fallback"]
        }
    
    def _generate_action_config(
        self, 
        operation: Dict[str, Any], 
        path: str, 
        method: str, 
        base_url: str
    ) -> Dict[str, Any]:
        """Generate action configuration for the operation."""
        
        # Extract authentication requirements
        auth_config = {}
        credential_ref = None
        security = operation.get("security", [])
        if security:
            # Simple auth detection - can be enhanced
            for scheme in security:
                for scheme_name in scheme.keys():
                    if "api" in scheme_name.lower() or "key" in scheme_name.lower():
                        auth_config = {"type": "api_key", "header": "X-API-Key"}
                        credential_ref = f"{urlparse(base_url).netloc.replace('.', '_')}_api_key"
                    elif "bearer" in scheme_name.lower() or "jwt" in scheme_name.lower():
                        auth_config = {"type": "bearer", "header": "Authorization"}
                        credential_ref = f"{urlparse(base_url).netloc.replace('.', '_')}_bearer_token"
                    break
                if auth_config:
                    break
        
        # Extract timeout and retry configuration
        retries_config = {
            "max_attempts": 3,
            "backoff": "exponential",
            "timeout": 30
        }
        
        # Check for specific timeout in operation extensions
        if "x-timeout" in operation:
            retries_config["timeout"] = operation["x-timeout"]
        
        # Extract API version from path or base URL
        api_version = "v1"  # Default
        path_parts = path.split("/")
        for part in path_parts:
            if part.startswith("v") and part[1:].isdigit():
                api_version = part
                break
        
        # Build integration section
        integration = {
            "base_url": base_url,
            "api_version": api_version
        }
        if credential_ref:
            integration["credential_ref"] = credential_ref
        
        # Check for health check endpoint in extensions
        if "x-health-check" in operation:
            integration["health_check"] = {
                "endpoint": operation["x-health-check"],
                "interval": 300
            }
        
        return {
            "method": method.upper(),
            "path": path,
            "integration": integration,
            "auth": auth_config,
            "retries": retries_config,
            "headers": {"Content-Type": "application/json"}
        }
    
    def _generate_parameters_config(self, operation: Dict[str, Any]) -> Dict[str, Any]:
        """Generate parameters configuration including schema and UI fields."""
        parameters = operation.get("parameters", [])
        request_body = operation.get("requestBody", {})
        
        # Build parameter schema
        schema = {}
        
        # Process path, query, and header parameters
        for param in parameters:
            param_name = param.get("name")
            param_schema = param.get("schema", {})
            param_in = param.get("in")
            
            if param_in in ["path", "query"]:  # Skip header params for now
                schema[param_name] = {
                    "type": param_schema.get("type", "string"),
                    "required": param.get("required", False),
                    "description": param.get("description", ""),
                    **param_schema  # Include all schema properties
                }
        
        # Process request body parameters
        if request_body:
            content = request_body.get("content", {})
            json_content = content.get("application/json", {})
            body_schema = json_content.get("schema", {})
            
            if body_schema.get("type") == "object":
                properties = body_schema.get("properties", {})
                required = body_schema.get("required", [])
                
                for prop_name, prop_schema in properties.items():
                    schema[prop_name] = {
                        "required": prop_name in required,
                        "description": prop_schema.get("description", ""),
                        **prop_schema
                    }
        
        # Generate UI fields from schema
        ui_fields = UIFieldGenerator.generate_ui_fields(schema)
        
        # Generate validation rules from schema
        validation_rules = self._generate_validation_rules(schema)
        
        # Generate transformation rules
        transformations = self._generate_transformation_rules(schema)
        
        config = {
            "schema": schema,
            "ui_fields": ui_fields
        }
        
        if validation_rules:
            config["validation_rules"] = validation_rules
            
        if transformations:
            config["transformations"] = transformations
            
        return config
    
    def _generate_prompts_config(self, operation: Dict[str, Any], path: str, method: str) -> Dict[str, Any]:
        """Generate prompt configurations for the operation."""
        operation_id = operation.get("operationId", f"{method}_{path}".replace("/", "_"))
        summary = operation.get("summary", "")
        description = operation.get("description", "")
        
        # System prompt
        system_prompt = f"""You are an AI assistant that helps users interact with the {operation_id} API endpoint.

Operation: {summary or operation_id}
{f"Description: {description}" if description else ""}

You should:
1. Help users understand what this endpoint does
2. Collect any required parameters in a conversational way
3. Execute the API call when all parameters are collected
4. Present the results in a clear, user-friendly format

Be helpful, concise, and professional in your responses."""

        # Generate extraction prompts for each parameter
        parameters = operation.get("parameters", [])
        extraction_prompts = {}
        
        for param in parameters:
            param_name = param.get("name")
            param_description = param.get("description", "")
            param_required = param.get("required", False)
            
            if param.get("in") in ["path", "query"]:
                prompt = f"What value would you like for {param_name.replace('_', ' ')}?"
                if param_description:
                    prompt += f" ({param_description})"
                if param_required:
                    prompt += " (required)"
                
                extraction_prompts[param_name] = prompt
        
        # Completion prompt
        completion_prompt = f"I'll {method.lower()} the {operation_id.replace('_', ' ')} with your parameters..."
        
        return {
            "system": system_prompt,
            "extraction": extraction_prompts,
            "completion": completion_prompt
        }
    
    def _generate_response_config(self, operation: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response configuration including CRFS formatting."""
        responses = operation.get("responses", {})
        
        # Build CRFS configuration
        crfs_config = {
            "type": "structured",
            "auto_select": True,
            "default_format": "structured",
            "sections": []
        }
        
        # Analyze success responses to determine formatting
        success_response = responses.get("200") or responses.get("201") or responses.get("default")
        
        if success_response:
            content = success_response.get("content", {})
            json_content = content.get("application/json", {})
            schema = json_content.get("schema", {})
            
            # Generate sections based on response schema
            if schema.get("type") == "object":
                properties = schema.get("properties", {})
                
                # Add a title section
                crfs_config["sections"].append({
                    "type": "text",
                    "style": "heading", 
                    "content": "{{ operation_result.title or 'API Response' }}"
                })
                
                # Add data sections based on properties
                for prop_name, prop_schema in properties.items():
                    if prop_schema.get("type") == "array":
                        crfs_config["sections"].append({
                            "type": "list",
                            "title": prop_name.replace("_", " ").title(),
                            "data_path": f"result.{prop_name}",
                            "style": "bullet"
                        })
                    elif prop_schema.get("type") == "object":
                        crfs_config["sections"].append({
                            "type": "object",
                            "title": prop_name.replace("_", " ").title(),
                            "data_path": f"result.{prop_name}"
                        })
                    else:
                        crfs_config["sections"].append({
                            "type": "text",
                            "content": f"**{prop_name.replace('_', ' ').title()}**: {{{{ result.{prop_name} }}}}"
                        })
            
            elif schema.get("type") == "array":
                crfs_config["sections"] = [{
                    "type": "list",
                    "title": "Results",
                    "data_path": "result",
                    "style": "bullet"
                }]
        
        # Error mapping
        error_mapping = {}
        for status_code, response in responses.items():
            if status_code.startswith("4") or status_code.startswith("5"):
                description = response.get("description", f"Error {status_code}")
                error_mapping[status_code] = f"❌ {description}"
        
        return {
            "crfs": crfs_config,
            "error_mapping": error_mapping
        }
    
    def _generate_docs_config(self, operation: Dict[str, Any], spec_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate documentation configuration."""
        summary = operation.get("summary", "")
        description = operation.get("description", "")
        
        # Generate examples
        examples = []
        if summary:
            examples.append(summary)
        
        # Add method-specific examples
        operation_id = operation.get("operationId", "")
        if operation_id:
            examples.append(f"Can you {operation_id.replace('_', ' ')}?")
        
        return {
            "description": description or summary or "API endpoint",
            "examples": examples[:3],  # Limit to top 3 examples
            "usage_notes": operation.get("x-usage-notes", "")
        }
    
    def _generate_enhanced_patterns(self, summary: str, description: str, method: str, path: str) -> List[str]:
        """Generate enhanced intent patterns using LLM."""
        try:
            if not self.llm_factory:
                return []
            
            llm = self.llm_factory.create_llm()
            
            prompt = f"""Generate 5 natural language patterns that users might use to request this API operation:

API: {method.upper()} {path}
Summary: {summary}
Description: {description}

Generate realistic user phrases that would indicate they want to use this API. Return only the phrases, one per line.

Examples:
- "get user profile"
- "fetch my account details" 
- "show user information"
- "retrieve account data"
- "display profile info"
"""
            
            response = llm.chat([{"role": "user", "content": prompt}])
            patterns = [line.strip("- ").strip() for line in response.split("\n") if line.strip()]
            return patterns[:5]  # Limit to 5 enhanced patterns
            
        except Exception as e:
            logger.warning(f"Failed to generate enhanced patterns: {e}")
            return []
    
    def generate_from_manual_spec(
        self,
        key: str,
        description: str,
        method: str,
        path: str,
        parameters: Optional[Dict[str, Any]] = None,
        response_schema: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a unified template from manual specification.
        
        Args:
            key: Template key
            description: Description of the operation
            method: HTTP method
            path: API path
            parameters: Parameter schema
            response_schema: Response schema
            
        Returns:
            Complete unified template data
        """
        endpoint_id = f"{method.upper()}_{path}"
        
        # Build minimal operation object
        operation = {
            "operationId": key,
            "summary": description,
            "description": description,
            "parameters": [],
            "responses": {"200": {"description": "Success"}}
        }
        
        # Add parameters if provided
        if parameters:
            for param_name, param_config in parameters.items():
                operation["parameters"].append({
                    "name": param_name,
                    "in": "query",
                    "required": param_config.get("required", False),
                    "schema": param_config
                })
        
        # Add response schema if provided  
        if response_schema:
            operation["responses"]["200"]["content"] = {
                "application/json": {"schema": response_schema}
            }
        
        return self.generate_from_openapi_endpoint(operation, path, method)
    
    def _generate_validation_rules(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Generate validation rules from parameter schema."""
        validation_rules = {}
        
        for param_name, param_schema in schema.items():
            rules = {}
            
            # Type validation
            param_type = param_schema.get("type")
            if param_type:
                rules["type"] = param_type
            
            # String validations
            if param_type == "string":
                if "minLength" in param_schema:
                    rules["min_length"] = param_schema["minLength"]
                if "maxLength" in param_schema:
                    rules["max_length"] = param_schema["maxLength"]
                if "pattern" in param_schema:
                    rules["pattern"] = param_schema["pattern"]
                if "enum" in param_schema:
                    rules["enum"] = param_schema["enum"]
                if "format" in param_schema:
                    rules["format"] = param_schema["format"]
                    
            # Number validations
            elif param_type in ["number", "integer"]:
                if "minimum" in param_schema:
                    rules["min"] = param_schema["minimum"]
                if "maximum" in param_schema:
                    rules["max"] = param_schema["maximum"]
                if "exclusiveMinimum" in param_schema:
                    rules["exclusive_min"] = param_schema["exclusiveMinimum"]
                if "exclusiveMaximum" in param_schema:
                    rules["exclusive_max"] = param_schema["exclusiveMaximum"]
                if "multipleOf" in param_schema:
                    rules["multiple_of"] = param_schema["multipleOf"]
                    
            # Array validations
            elif param_type == "array":
                if "minItems" in param_schema:
                    rules["min_items"] = param_schema["minItems"]
                if "maxItems" in param_schema:
                    rules["max_items"] = param_schema["maxItems"]
                if "uniqueItems" in param_schema:
                    rules["unique_items"] = param_schema["uniqueItems"]
                    
            # Add required flag
            if param_schema.get("required"):
                rules["required"] = True
                
            if rules:
                validation_rules[param_name] = rules
                
        return validation_rules
    
    def _generate_transformation_rules(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Generate transformation rules based on parameter types and formats."""
        transformations = {}
        
        for param_name, param_schema in schema.items():
            transforms = []
            param_type = param_schema.get("type")
            param_format = param_schema.get("format")
            
            # String transformations
            if param_type == "string":
                # Always trim strings
                transforms.append("trim")
                
                # Format-specific transformations
                if param_format == "email":
                    transforms.append("lowercase")
                elif param_format == "uri" or param_format == "url":
                    transforms.append("lowercase")
                elif param_name.lower() in ["username", "email", "id", "key"]:
                    transforms.append("lowercase")
                    
                # Check for specific patterns
                if param_name.lower() in ["phone", "phone_number", "tel", "telephone"]:
                    transforms.append("remove_non_numeric")
                    
            # Array transformations
            elif param_type == "array":
                if param_schema.get("uniqueItems"):
                    transforms.append("deduplicate")
                    
            if transforms:
                transformations[param_name] = transforms
                
        return transformations
    
    def validate_unified_template(self, template_data: Dict[str, Any]) -> List[str]:
        """
        Validate a unified template structure.
        
        Args:
            template_data: Template data to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Required top-level sections
        required_sections = ["meta", "intent", "action", "parameters", "prompts", "response", "docs_config"]
        
        for section in required_sections:
            if section not in template_data:
                errors.append(f"Missing required section: {section}")
        
        # Validate meta section
        meta = template_data.get("meta", {})
        if not meta.get("key"):
            errors.append("meta.key is required")
        if not meta.get("endpoint_id"):
            errors.append("meta.endpoint_id is required")
        
        # Validate intent section
        intent = template_data.get("intent", {})
        if not intent.get("patterns"):
            errors.append("intent.patterns is required and must not be empty")
        
        # Validate action section
        action = template_data.get("action", {})
        if not action.get("method"):
            errors.append("action.method is required")
        if not action.get("path"):
            errors.append("action.path is required")
        
        # Validate parameters section
        parameters = template_data.get("parameters", {})
        if "schema" not in parameters:
            errors.append("parameters.schema is required")
        if "ui_fields" not in parameters:
            errors.append("parameters.ui_fields is required")
        
        return errors
    
    def _generate_test_data(self, operation: Dict[str, Any], path: str, method: str) -> Optional[Dict[str, Any]]:
        """Generate test data for offline development and testing."""
        # Get response examples if available
        responses = operation.get("responses", {})
        success_response = responses.get("200") or responses.get("201")
        
        if not success_response:
            return None
            
        # Check for examples in response
        content = success_response.get("content", {})
        json_content = content.get("application/json", {})
        examples = json_content.get("examples", {})
        
        mock_responses = {}
        
        # Use provided examples if available
        if examples:
            for example_name, example_data in examples.items():
                if "value" in example_data:
                    mock_responses[example_name] = example_data["value"]
        
        # Generate sample parameters
        parameters = operation.get("parameters", [])
        sample_params = {}
        
        for param in parameters:
            param_name = param.get("name")
            param_schema = param.get("schema", {})
            param_type = param_schema.get("type", "string")
            
            # Generate sample values based on type
            if param_type == "string":
                if param_schema.get("enum"):
                    sample_params[param_name] = param_schema["enum"][0]
                elif param_schema.get("format") == "email":
                    sample_params[param_name] = "<EMAIL>"
                elif param_schema.get("format") == "date":
                    sample_params[param_name] = "2024-01-01"
                elif param_name.lower() in ["id", "user_id", "account_id"]:
                    sample_params[param_name] = "12345"
                else:
                    sample_params[param_name] = f"sample_{param_name}"
            elif param_type == "integer":
                sample_params[param_name] = 123
            elif param_type == "number":
                sample_params[param_name] = 123.45
            elif param_type == "boolean":
                sample_params[param_name] = True
            elif param_type == "array":
                sample_params[param_name] = ["item1", "item2"]
                
        # Generate a default success response if no examples
        if not mock_responses:
            schema = json_content.get("schema", {})
            if schema.get("type") == "object":
                mock_response = {}
                properties = schema.get("properties", {})
                
                for prop_name, prop_schema in properties.items():
                    prop_type = prop_schema.get("type")
                    if prop_type == "string":
                        mock_response[prop_name] = f"Sample {prop_name}"
                    elif prop_type == "integer":
                        mock_response[prop_name] = 42
                    elif prop_type == "number":
                        mock_response[prop_name] = 123.45
                    elif prop_type == "boolean":
                        mock_response[prop_name] = True
                    elif prop_type == "array":
                        mock_response[prop_name] = ["item1", "item2"]
                    elif prop_type == "object":
                        mock_response[prop_name] = {"key": "value"}
                        
                mock_responses["success"] = mock_response
            elif schema.get("type") == "array":
                mock_responses["success"] = [
                    {"id": 1, "name": "Item 1"},
                    {"id": 2, "name": "Item 2"}
                ]
        
        test_data = {}
        
        if mock_responses:
            test_data["mock_responses"] = mock_responses
            
        if sample_params:
            test_data["sample_parameters"] = sample_params
            
        return test_data if test_data else None