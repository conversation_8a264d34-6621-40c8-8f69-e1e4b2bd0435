"""Response formatter for template-based responses.

This module provides structured formatting for API responses based on
template configuration. It supports various formatting types including
conditional sections, lists, objects, and raw output.
"""

import json
from typing import Any, Dict, List, Optional
from jinja2 import Environment, Template


class ResponseFormatter:
    """Formats API responses according to template-defined structures."""
    
    def __init__(self, response_format: Optional[Dict[str, Any]] = None):
        """Initialize the response formatter.
        
        Args:
            response_format: Response format configuration from template
        """
        self.format_config = response_format or {"type": "raw"}
        self.env = Environment(autoescape=False)
        
        # Register custom filters
        self.env.filters["json_pretty"] = lambda x: json.dumps(x, indent=2)
        self.env.filters["upper"] = lambda x: str(x).upper()
        self.env.filters["title"] = lambda x: str(x).title()
        self.env.filters["replace"] = lambda x, old, new: str(x).replace(old, new)
    
    def format_response(self, context: Dict[str, Any]) -> str:
        """Format a response according to the template configuration.
        
        Args:
            context: Template context containing results and parameters
            
        Returns:
            Formatted response string
        """
        format_type = self.format_config.get("type", "raw")
        
        if format_type == "raw":
            return self._format_raw(context)
        elif format_type == "structured":
            return self._format_structured(context)
        else:
            # Fallback to raw formatting
            return self._format_raw(context)
    
    def _format_raw(self, context: Dict[str, Any]) -> str:
        """Format response as raw output.
        
        Args:
            context: Template context
            
        Returns:
            Raw formatted string
        """
        result = context.get("result", {})
        results = context.get("results", {})
        
        # If we have a single result, use it
        if result:
            if isinstance(result, dict):
                return json.dumps(result, indent=2)
            return str(result)
        
        # If we have multiple results, format them
        if results:
            parts = []
            for key, value in results.items():
                if isinstance(value, dict):
                    parts.append(f"{key}:\n{json.dumps(value, indent=2)}")
                else:
                    parts.append(f"{key}: {value}")
            return "\n\n".join(parts)
        
        return "No results available"
    
    def _format_structured(self, context: Dict[str, Any]) -> str:
        """Format response using structured sections.
        
        Args:
            context: Template context
            
        Returns:
            Structured formatted string
        """
        # Get structure config
        structure = self.format_config.get("structure", {})
        
        # Build output parts
        output_parts = []
        
        # Add header if present
        header = structure.get("header")
        if header:
            output_parts.append(f"# {header}\n")
        
        # Process sections
        sections = structure.get("sections", [])
        for section in sections:
            formatted_section = self._format_section(section, context)
            if formatted_section:
                output_parts.append(formatted_section)
        
        return "\n\n".join(output_parts)
    
    def _format_section(self, section: Dict[str, Any], context: Dict[str, Any]) -> Optional[str]:
        """Format a single section.
        
        Args:
            section: Section configuration
            context: Template context
            
        Returns:
            Formatted section or None if section should be hidden
        """
        section_type = section.get("type", "text")
        
        # Check conditionals
        conditionals = section.get("conditionals", {})
        if "show_if" in conditionals:
            condition = conditionals["show_if"]
            if not self._evaluate_condition(condition, context):
                return None
        
        if section_type == "text":
            return self._format_text_section(section, context)
        elif section_type == "conditional":
            return self._format_conditional_section(section, context)
        elif section_type == "list":
            return self._format_list_section(section, context)
        elif section_type == "object":
            return self._format_object_section(section, context)
        elif section_type == "json":
            return self._format_json_section(section, context)
        elif section_type == "summary":
            return self._format_summary_section(section, context)
        else:
            return None
    
    def _format_text_section(self, section: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Format a text section.
        
        Args:
            section: Section configuration
            context: Template context
            
        Returns:
            Formatted text
        """
        content = section.get("content", "")
        style = section.get("style", "normal")
        
        # Render content with Jinja2
        template = self.env.from_string(content)
        rendered = template.render(**context)
        
        # Apply styling
        if style == "heading":
            return f"# {rendered}"
        elif style == "success":
            return f"✓ {rendered}"
        elif style == "error":
            return f"✗ {rendered}"
        elif style == "info":
            return f"ℹ {rendered}"
        else:
            return rendered
    
    def _format_conditional_section(self, section: Dict[str, Any], context: Dict[str, Any]) -> Optional[str]:
        """Format a conditional section.
        
        Args:
            section: Section configuration
            context: Template context
            
        Returns:
            Formatted content or None
        """
        conditions = section.get("conditions", {})
        
        for condition_name, condition_config in conditions.items():
            check = condition_config.get("check", "")
            if self._evaluate_condition(check, context):
                content = condition_config.get("content", "")
                style = condition_config.get("style", "normal")
                
                # Render content
                template = self.env.from_string(content)
                rendered = template.render(**context)
                
                # Apply styling
                if style == "success":
                    return f"✓ {rendered}"
                elif style == "error":
                    return f"✗ {rendered}"
                elif style == "info":
                    return f"ℹ {rendered}"
                else:
                    return rendered
        
        return None
    
    def _format_list_section(self, section: Dict[str, Any], context: Dict[str, Any]) -> Optional[str]:
        """Format a list section.
        
        Args:
            section: Section configuration
            context: Template context
            
        Returns:
            Formatted list or None
        """
        source = section.get("source", "")
        item_format = section.get("item_format", {})
        
        # Get list data
        # Split source path (e.g., "result.items" or "results.default.data") 
        source_parts = source.split('.')
        list_data = context
        for part in source_parts:
            list_data = list_data.get(part, [])
        
        if not isinstance(list_data, list):
            return None
        
        formatted_items = []
        for i, item in enumerate(list_data):
            if item_format.get("type") == "object":
                formatted_item = self._format_object_item(item, item_format)
            else:
                formatted_item = str(item)
            
            formatted_items.append(f"{i + 1}. {formatted_item}")
        
        return "\n".join(formatted_items)
    
    def _format_object_section(self, section: Dict[str, Any], context: Dict[str, Any]) -> Optional[str]:
        """Format an object section.
        
        Args:
            section: Section configuration
            context: Template context
            
        Returns:
            Formatted object or None
        """
        source = section.get("source", "")
        show_keys = section.get("show_keys", True)
        key_order = section.get("key_order", [])
        key_formatting = section.get("key_formatting", {})
        
        # Get object data
        # Split source path (e.g., "result" or "results.default") 
        source_parts = source.split('.')
        obj_data = context
        for part in source_parts:
            obj_data = obj_data.get(part, {})
        
        if not isinstance(obj_data, dict):
            return None
        
        formatted_pairs = []
        
        # Use key order if specified
        keys = key_order if key_order else sorted(obj_data.keys())
        
        for key in keys:
            if key in obj_data:
                formatted_key = key_formatting.get(key, key.replace("_", " ").title())
                value = obj_data[key]
                
                if isinstance(value, dict):
                    value_str = json.dumps(value, indent=2)
                elif isinstance(value, list):
                    value_str = json.dumps(value, indent=2)
                else:
                    value_str = str(value)
                
                if show_keys:
                    formatted_pairs.append(f"{formatted_key}: {value_str}")
                else:
                    formatted_pairs.append(value_str)
        
        return "\n".join(formatted_pairs)
    
    def _format_json_section(self, section: Dict[str, Any], context: Dict[str, Any]) -> Optional[str]:
        """Format a JSON section.
        
        Args:
            section: Section configuration
            context: Template context
            
        Returns:
            Formatted JSON or None
        """
        source = section.get("source", "")
        indent = section.get("indent", 2)
        
        # Get data
        # Split source path
        source_parts = source.split('.')
        data = context
        for part in source_parts:
            data = data.get(part, {})
        
        return json.dumps(data, indent=indent)
    
    def _format_object_item(self, item: Any, format_config: Dict[str, Any]) -> str:
        """Format a single object item.
        
        Args:
            item: Item to format
            format_config: Formatting configuration
            
        Returns:
            Formatted item string
        """
        if not isinstance(item, dict):
            return str(item)
        
        show_keys = format_config.get("show_keys", True)
        style = format_config.get("style", "normal")
        
        if style == "compact":
            parts = []
            for k, v in item.items():
                if show_keys:
                    parts.append(f"{k}: {v}")
                else:
                    parts.append(str(v))
            return " | ".join(parts)
        else:
            return json.dumps(item, indent=2)
    
    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """Evaluate a condition expression.
        
        Args:
            condition: Condition expression
            context: Template context
            
        Returns:
            True if condition passes, False otherwise
        """
        try:
            # Create a safe evaluation environment
            safe_env = {
                "result": context.get("result", {}),
                "results": context.get("results", {}),
                "parameters": context.get("parameters", {}),
                "true": True,
                "false": False,
                "none": None,
            }
            
            # Evaluate the condition
            return bool(eval(condition, {"__builtins__": {}}, safe_env))
        except Exception:
            return False
    
    def _format_summary_section(self, section: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Format a summary section with fields and values.
        
        Args:
            section: Section configuration
            context: Template context
            
        Returns:
            Formatted summary section
        """
        title = section.get("title", "")
        fields = section.get("fields", [])
        
        output_parts = []
        
        # Add title if present
        if title:
            output_parts.append(f"## {title}")
        
        # Format each field
        for field in fields:
            label = field.get("label", "")
            value_template = field.get("value", "")
            
            # Render value with context
            template = self.env.from_string(value_template)
            value = template.render(**context)
            
            output_parts.append(f"- {label}: {value}")
        
        return "\n".join(output_parts)