"""
Template system for Coherence.

This module provides the template management system for Coherence,
including template rendering, inheritance, versioning, and caching.
"""

from src.coherence.template_system.engine.renderer import Template<PERSON>enderer
from src.coherence.template_system.services.template_service import TemplateService
from src.coherence.template_system.ui_generator import UIFieldGenerator, UIFormGenerator
from src.coherence.template_system.unified_generator import UnifiedTemplateGenerator
from src.coherence.template_system.crfs_formatter import CRFSFormatter

__all__ = [
    "TemplateRenderer", 
    "TemplateService", 
    "UIFieldGenerator", 
    "UIFormGenerator",
    "UnifiedTemplateGenerator",
    "CRFSFormatter"
]
