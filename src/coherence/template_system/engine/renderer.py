"""
Template rendering engine for Coherence.

This module provides the core template rendering engine using Jinja2,
with support for template inheritance, includes, and custom filters.
"""

import json
import logging
from typing import Any, Dict, List, Optional

import jinja2
from jinja2 import DictLoader, Environment, select_autoescape

logger = logging.getLogger(__name__)


class TemplateRenderer:
    """
    Template renderer for Coherence templates.

    Uses Jinja2 for template rendering with support for inheritance
    and inclusion across the template hierarchy (global → pack → tenant).

    Attributes:
        templates: Dictionary of template bodies keyed by template ID
        environment: Jinja2 environment for rendering templates
    """

    def __init__(
        self, templates: Optional[Dict[str, str]] = None, sandbox: bool = True
    ):
        """
        Initialize the template renderer.

        Args:
            templates: Dictionary of template bodies keyed by template ID
            sandbox: Whether to enable sandbox mode for secure template execution
        """
        self.templates = templates or {}

        # Create Jinja2 environment
        self.environment = Environment(
            loader=DictLoader(self.templates),
            autoescape=select_autoescape(["html", "xml"]),
            undefined=jinja2.StrictUndefined,  # Fail on undefined variables
            trim_blocks=True,
            lstrip_blocks=True,
        )

        # Register custom filters
        self._register_filters()

        # Set up sandbox if enabled
        if sandbox:
            self._setup_sandbox()

    def _register_filters(self) -> None:
        """Register custom filters for template rendering."""
        # JSON formatting filter
        self.environment.filters["tojson"] = lambda obj: json.dumps(
            obj, ensure_ascii=False
        )
        
        # Pretty JSON formatting filter
        self.environment.filters["json_pretty"] = lambda obj: json.dumps(
            obj, indent=2, ensure_ascii=False
        )

        # Safe string filter to prevent XSS
        self.environment.filters["safe_string"] = self._safe_string

        # Truncate filter
        self.environment.filters["truncate"] = lambda s, length=100: (
            s[:length] + "..." if len(s) > length else s
        )

    def _safe_string(self, value: str) -> str:
        """
        Filter to prevent XSS in templates.

        Args:
            value: String to sanitize

        Returns:
            Sanitized string
        """
        if not isinstance(value, str):
            return str(value)

        # Simple sanitization for demonstration
        # In production, use a more robust solution like bleach
        # Order matters! Do & replacement first to avoid double-escaping
        value = value.replace("&", "&amp;")
        value = value.replace("<", "&lt;")
        value = value.replace(">", "&gt;")
        value = value.replace('"', "&quot;")
        value = value.replace("'", "&#x27;")

        return value

    def _setup_sandbox(self) -> None:
        """
        Set up sandbox environment for secure template execution.

        Restricts access to potentially dangerous attributes and methods.
        """
        # In a production system, consider using a more robust sandbox
        # like jinja2-sandbox or a custom implementation

        # Restrict access to dangerous attributes
        def sandbox_getattr(obj: Any, attr: str) -> Any:
            if attr.startswith("_") or attr in ["pop", "update", "clear"]:
                raise AttributeError(f"Access to {attr} is not allowed in templates")
            return getattr(obj, attr)

        # Override getattr behavior in environment
        # Use setattr to safely set the attribute without mypy complaining
        self.environment.getattr = sandbox_getattr

    def add_template(self, template_id: str, body: str) -> None:
        """
        Add a template to the renderer.

        Args:
            template_id: Unique identifier for the template
            body: Template body
        """
        self.templates[template_id] = body
        self.environment.loader = DictLoader(self.templates)

    def add_templates(self, templates: Dict[str, str]) -> None:
        """
        Add multiple templates to the renderer.

        Args:
            templates: Dictionary of template bodies keyed by template ID
        """
        self.templates.update(templates)
        self.environment.loader = DictLoader(self.templates)

    def remove_template(self, template_id: str) -> None:
        """
        Remove a template from the renderer.

        Args:
            template_id: Unique identifier for the template
        """
        if template_id in self.templates:
            del self.templates[template_id]
            self.environment.loader = DictLoader(self.templates)

    def render(self, template_id: str, context: Dict[str, Any]) -> str:
        """
        Render a template with the given context.

        Args:
            template_id: Unique identifier for the template
            context: Context variables for template rendering

        Returns:
            Rendered template

        Raises:
            jinja2.exceptions.TemplateNotFound: If template not found
            jinja2.exceptions.TemplateSyntaxError: If template has syntax errors
            jinja2.exceptions.UndefinedError: If template uses undefined variables
        """
        try:
            template = self.environment.get_template(template_id)
            result = template.render(**context)
            # Don't strip whitespace - tests expect exact output
            return result
        except (
            jinja2.exceptions.TemplateNotFound,
            jinja2.exceptions.TemplateSyntaxError,
            jinja2.exceptions.UndefinedError,
        ) as e:
            logger.error(f"Template rendering error for {template_id}: {str(e)}")
            raise
        except Exception as e:
            logger.exception(
                f"Unexpected error rendering template {template_id}: {str(e)}"
            )
            raise ValueError(f"Template rendering failed: {str(e)}") from e

    def validate(self, template_id: str) -> bool:
        """
        Validate a template for syntax errors.

        Args:
            template_id: Unique identifier for the template

        Returns:
            True if template is valid, False otherwise
        """
        try:
            self.environment.get_template(template_id)
            return True
        except (
            jinja2.exceptions.TemplateNotFound,
            jinja2.exceptions.TemplateSyntaxError,
        ) as e:
            logger.error(f"Template validation error for {template_id}: {str(e)}")
            return False

    def get_dependencies(self, template_id: str) -> List[str]:
        """
        Get the dependencies of a template.

        Identifies templates referenced in 'extends' and 'include' statements.

        Args:
            template_id: Unique identifier for the template

        Returns:
            List of template IDs that this template depends on
        """
        dependencies = []

        try:
            source = self.templates.get(template_id, "")

            # Simple parsing for extends and includes
            # In a production system, use proper AST parsing
            for line in source.split("\n"):
                line = line.strip()
                if line.startswith("{%") and "extends" in line:
                    # Extract template name from {% extends "template_name" %}
                    template_name = line.split("extends")[1].strip().strip("\"'%} ")
                    dependencies.append(template_name)
                elif line.startswith("{%") and "include" in line:
                    # Extract template name from {% include "template_name" %}
                    template_name = line.split("include")[1].strip().strip("\"'%} ")
                    dependencies.append(template_name)

            return dependencies
        except Exception as e:
            logger.error(f"Error getting dependencies for {template_id}: {str(e)}")
            return []
