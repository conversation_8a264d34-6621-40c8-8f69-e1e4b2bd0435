"""
Middleware for automatic audit logging of admin routes.

This middleware logs all requests to admin routes to provide a
complete audit trail of administrative actions.
"""

import re
import uuid
from typing import Optional

import structlog
from fastapi import Fast<PERSON><PERSON>, Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import AS<PERSON><PERSON>pp

from src.coherence.db.deps import get_admin_db_session
from src.coherence.services.audit_service import get_audit_service

logger = structlog.get_logger(__name__)

# Regex pattern to identify admin routes
ADMIN_ROUTE_PATTERN = r"^/v1/admin"


class AuditLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging admin route access.

    Automatically logs all requests to admin routes to provide a
    comprehensive audit trail for security and compliance.
    """

    def __init__(
        self,
        app: ASGIApp,
        exclude_paths: Optional[list[str]] = None,
    ):
        """Initialize middleware with the app and configuration.

        Args:
            app: The FastAPI application
            exclude_paths: List of paths to exclude from audit logging
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or []

    async def dispatch(self, request: Request, call_next):
        """Process the request and log admin routes.

        Args:
            request: The incoming request
            call_next: Function to call the next middleware/handler

        Returns:
            The response from the route handler
        """
        # Skip non-admin routes
        path = request.url.path

        if not re.match(ADMIN_ROUTE_PATTERN, path):
            # Not an admin route, just pass through
            return await call_next(request)

        # Skip excluded paths
        if any(exclude in path for exclude in self.exclude_paths):
            return await call_next(request)

        # Extract tenant_id from headers
        tenant_id = request.headers.get("X-Tenant-ID")
        user_id = request.headers.get("X-User-ID")

        # Handle case where tenant_id is not set
        if not tenant_id:
            logger.warning(
                "Admin route accessed without tenant_id",
                path=path,
                method=request.method,
            )
            return await call_next(request)

        try:
            # Convert to UUID objects with better error handling
            tenant_uuid = None
            user_uuid = None

            if tenant_id:
                try:
                    tenant_uuid = uuid.UUID(tenant_id)
                except ValueError as e:
                    logger.warning(
                        "Invalid tenant_id format in request header",
                        tenant_id=tenant_id,
                        path=path,
                        method=request.method,
                        error=str(e),
                    )

            if user_id:
                try:
                    user_uuid = uuid.UUID(user_id)
                except ValueError as e:
                    logger.warning(
                        "Invalid user_id format in request header",
                        user_id=user_id,
                        path=path,
                        method=request.method,
                        error=str(e),
                    )

            # Skip audit logging if tenant_uuid is not valid
            if not tenant_uuid:
                logger.warning(
                    "Skipping audit logging due to invalid tenant_id",
                    path=path,
                    method=request.method,
                )
                return await call_next(request)

            # Get database session - using admin context to ensure this works
            # even if tenant context would normally block access
            async with get_admin_db_session() as db:
                # Create audit service
                audit_service = await get_audit_service(db)

                # Log the request
                await audit_service.log_admin_request(
                    request=request,
                    tenant_id=tenant_uuid,
                    user_id=user_uuid,
                )

                # Commit the audit log
                await db.commit()

        except Exception as e:
            # Log the error but don't block the request
            logger.error(
                "Failed to log admin action",
                error=str(e),
                path=path,
                method=request.method,
            )

        # Continue processing the request
        return await call_next(request)


def add_audit_logging(app: FastAPI, exclude_paths: Optional[list[str]] = None):
    """Add audit logging middleware to the application.

    Args:
        app: FastAPI application
        exclude_paths: Paths to exclude from audit logging
    """
    app.add_middleware(
        AuditLoggingMiddleware,
        exclude_paths=exclude_paths or ["/admin/health", "/admin/metrics"],
    )
