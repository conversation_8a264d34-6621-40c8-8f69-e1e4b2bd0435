"""
Rate limiting middleware for API endpoints.

This middleware protects high-value routes from abuse by
implementing rate limits based on client IP and tenant ID.
"""

import re
import time
from typing import Callable, Dict, Optional, Tuple

import structlog
from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.status import HTTP_429_TOO_MANY_REQUESTS

logger = structlog.get_logger(__name__)

# Patterns for routes that need strict rate limiting
ADMIN_ROUTE_PATTERN = r"^/v1/admin"
STATUS_ROUTE_PATTERN = r"^/v1/status"


class SimpleRateLimiter:
    """
    Simple in-memory rate limiter implementation.

    Tracks request counts within time windows for different clients.
    Real-world implementations would use Redis for distributed rate limiting.
    """

    def __init__(self):
        """Initialize the rate limiter."""
        # {client_id: [(timestamp, count), ...]}
        self.request_counts: Dict[str, list] = {}
        # Clean up old entries periodically
        self.cleanup_interval = 60  # seconds
        self.last_cleanup = time.time()

    def _cleanup_old_records(self, window_seconds: int):
        """Remove entries outside the current time window.

        Args:
            window_seconds: Time window in seconds to keep entries for
        """
        now = time.time()

        # Only clean up if enough time has passed
        if now - self.last_cleanup < self.cleanup_interval:
            return

        cutoff = now - window_seconds

        for client_id in list(self.request_counts.keys()):
            # Filter to keep only records within the window
            self.request_counts[client_id] = [
                (timestamp, count)
                for timestamp, count in self.request_counts.get(client_id, [])
                if timestamp > cutoff
            ]

            # Remove empty client entries
            if not self.request_counts[client_id]:
                del self.request_counts[client_id]

        self.last_cleanup = now

    def get_request_count(self, client_id: str, window_seconds: int) -> int:
        """Get the number of requests made by a client in the time window.

        Args:
            client_id: Unique identifier for the client
            window_seconds: Time window in seconds

        Returns:
            Total request count within the window
        """
        self._cleanup_old_records(window_seconds)

        now = time.time()
        cutoff = now - window_seconds

        # Sum all request counts within the window
        count = sum(
            count
            for timestamp, count in self.request_counts.get(client_id, [])
            if timestamp > cutoff
        )

        return count

    def add_request(self, client_id: str) -> None:
        """Record a new request for the client.

        Args:
            client_id: Unique identifier for the client
        """
        now = time.time()

        if client_id not in self.request_counts:
            self.request_counts[client_id] = []

        # Add a new entry with timestamp and count=1
        self.request_counts[client_id].append((now, 1))


# Global rate limiter instance - note this would be Redis-backed in production
rate_limiter = SimpleRateLimiter()


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for rate limiting API requests.

    Prevents abuse by limiting the number of requests a client can
    make within a time window. Different limits apply to different routes.
    """

    def __init__(
        self,
        app,
        admin_rate_limit: int = 30,  # requests per minute
        admin_window_seconds: int = 60,
        status_rate_limit: int = 60,  # requests per minute
        status_window_seconds: int = 60,
        exclude_paths: Optional[list[str]] = None,
    ):
        """Initialize with rate limit configuration.

        Args:
            app: The FastAPI application
            admin_rate_limit: Maximum requests per minute for admin routes
            admin_window_seconds: Time window for admin route limits
            status_rate_limit: Maximum requests per minute for status routes
            status_window_seconds: Time window for status route limits
            exclude_paths: List of paths to exclude from rate limiting
        """
        super().__init__(app)
        self.admin_rate_limit = admin_rate_limit
        self.admin_window_seconds = admin_window_seconds
        self.status_rate_limit = status_rate_limit
        self.status_window_seconds = status_window_seconds
        self.exclude_paths = exclude_paths or []

    def _get_client_key(self, request: Request) -> str:
        """Generate a unique key for the client for rate limiting.

        Args:
            request: The incoming request

        Returns:
            A unique key combining client IP and tenant ID
        """
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"

        # Get tenant ID if available
        tenant_id = request.headers.get("X-Tenant-ID", "unknown")

        # Combine for a unique key
        return f"{client_ip}:{tenant_id}"

    def _should_rate_limit(self, request: Request) -> Tuple[bool, int, int]:
        """Check if the request should be rate limited.

        Args:
            request: The incoming request

        Returns:
            Tuple of (should_limit, allowed_requests, window_seconds)
        """
        path = request.url.path

        # Skip excluded paths
        if any(exclude in path for exclude in self.exclude_paths):
            return False, 0, 0

        # Check if this is an admin route
        if re.match(ADMIN_ROUTE_PATTERN, path):
            return True, self.admin_rate_limit, self.admin_window_seconds

        # Check if this is a status route
        if re.match(STATUS_ROUTE_PATTERN, path):
            return True, self.status_rate_limit, self.status_window_seconds

        # No rate limit for other routes
        return False, 0, 0

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process the request and apply rate limiting if needed.

        Args:
            request: The incoming request
            call_next: Function to call the next middleware/handler

        Returns:
            The response from the route handler or 429 if rate limited
        """
        # Check if this route should be rate limited
        should_limit, allowed_requests, window_seconds = self._should_rate_limit(
            request
        )

        if not should_limit:
            # Skip rate limiting for this route
            return await call_next(request)

        # Get client key for rate limiting
        client_key = self._get_client_key(request)

        # Check current request count
        current_count = rate_limiter.get_request_count(client_key, window_seconds)

        # Add current request to the count
        rate_limiter.add_request(client_key)

        # If the count exceeds the limit, return 429
        if current_count >= allowed_requests:
            # Log the rate limit exceeded
            logger.warning(
                "Rate limit exceeded",
                client_key=client_key,
                path=request.url.path,
                method=request.method,
                current_count=current_count,
                allowed_requests=allowed_requests,
            )

            # Return 429 Too Many Requests
            return Response(
                content=f"Rate limit exceeded. Try again in {window_seconds} seconds.",
                status_code=HTTP_429_TOO_MANY_REQUESTS,
                headers={
                    "Retry-After": str(window_seconds),
                    "X-RateLimit-Limit": str(allowed_requests),
                    "X-RateLimit-Remaining": "0",
                    "X-RateLimit-Reset": str(int(time.time() + window_seconds)),
                },
            )

        # Add rate limit headers to successful responses
        response = await call_next(request)

        # Add rate limit headers to the response
        remaining = max(0, allowed_requests - current_count - 1)
        response.headers["X-RateLimit-Limit"] = str(allowed_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time() + window_seconds))

        return response


def add_rate_limiting(
    app: FastAPI,
    admin_rate_limit: int = 30,
    status_rate_limit: int = 60,
    exclude_paths: Optional[list[str]] = None,
):
    """Add rate limiting middleware to the application.

    Args:
        app: FastAPI application
        admin_rate_limit: Maximum requests per minute for admin routes
        status_rate_limit: Maximum requests per minute for status routes
        exclude_paths: Paths to exclude from rate limiting
    """
    app.add_middleware(
        RateLimitingMiddleware,
        admin_rate_limit=admin_rate_limit,
        status_rate_limit=status_rate_limit,
        exclude_paths=exclude_paths
        or ["/health", "/metrics", "/admin/health", "/admin/metrics"],
    )
