"""
Tenant Context Middleware for Row-Level Security (RLS).

This middleware sets the PostgreSQL session parameters for row-level security,
ensuring that database queries are properly filtered by tenant/organization.
"""

import logging
import uuid
from contextvars import ContextVar
from typing import Callable, Optional

from fastapi import (  # HTTPException might not be needed here if auth deps handle it
    Request,
    Response,
)
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.types import ASGIApp

# JWT decoding is now primarily handled by auth dependencies, but kept for reference or specific cases
# from jose import jwt, JWTError 
# from src.coherence.core.config import settings 

logger = logging.getLogger(__name__)

# Context variables for SQLAlchemy event listener and potentially other parts of the app
# These are set by the middleware based on request.state, which should be populated by auth dependencies.
current_rls_tenant_id_var: ContextVar[Optional[str]] = ContextVar("current_rls_tenant_id_var", default=None)
current_rls_clerk_org_id_var: ContextVar[Optional[str]] = ContextVar("current_rls_clerk_org_id_var", default=None)
current_rls_is_system_admin_var: ContextVar[bool] = ContextVar("current_rls_is_system_admin_var", default=False)
# Context variables required by db.session.py and new context system (Phase 3, Task 3.5.1)
current_clerk_org_id_var: ContextVar[str | None] = ContextVar("current_clerk_org_id", default=None)
current_tenant_id_var: ContextVar[int | None] = ContextVar("current_tenant_id", default=None)
is_system_admin_var: ContextVar[bool] = ContextVar("is_system_admin", default=False)


class TenantContextMiddleware(BaseHTTPMiddleware):
    """
    Middleware that ensures RLS-relevant values from request.state (populated by auth dependencies)
    are available for the SQLAlchemyTenantContext to set as PostgreSQL session variables.
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Processes the request. It primarily ensures that context variables are updated
        based on request.state, which should have been populated by earlier
        authentication dependencies for protected routes.
        """
        
        # Values from request.state (should be set by auth dependencies like get_clerk_auth_details)
        # Initialize to ensure they exist, even if auth dep doesn't run (e.g. public routes)
        rls_tenant_id = getattr(request.state, "rls_tenant_id", None)
        rls_clerk_org_id = getattr(request.state, "rls_clerk_org_id", None)
        rls_is_system_admin = getattr(request.state, "is_system_admin", False) # is_system_admin from auth.py

        # The middleware previously had its own JWT decoding logic.
        # This is now simplified as auth dependencies are expected to handle primary auth
        # and populate request.state. If specific public routes need JWT processing
        # without full auth deps, that logic could be selectively re-added or handled differently.
        # For now, we rely on what auth dependencies (or lack thereof for public routes) provide.

        logger.debug(
            f"TenantContextMiddleware: Pre-setting context vars from request.state: "
            f"rls_tenant_id='{rls_tenant_id}', "
            f"rls_clerk_org_id='{rls_clerk_org_id}', "
            f"rls_is_system_admin={rls_is_system_admin}"
        )

        # Set context variables that the SQLAlchemy hook will read.
        # These vars are request-scoped due to ContextVar.
        token_rls_tenant_id = current_rls_tenant_id_var.set(rls_tenant_id)
        token_rls_clerk_org_id = current_rls_clerk_org_id_var.set(rls_clerk_org_id)
        token_rls_is_system_admin = current_rls_is_system_admin_var.set(rls_is_system_admin)
        
        try:
            response = await call_next(request)
        finally:
            # Reset context variables after the request is processed
            current_rls_tenant_id_var.reset(token_rls_tenant_id)
            current_rls_clerk_org_id_var.reset(token_rls_clerk_org_id)
            current_rls_is_system_admin_var.reset(token_rls_is_system_admin)
            logger.debug("TenantContextMiddleware: Context vars reset.")

        return response


class SQLAlchemyTenantContext:
    """
    SQLAlchemy event listeners to set PostgreSQL session parameters for RLS.
    Reads from context variables populated by TenantContextMiddleware.
    """

    @staticmethod
    def _execute_set_variable(cursor, variable_name: str, value: Optional[str]):
        """Helper to set a session variable, handling NULL correctly."""
        if value is not None:
            # Ensure value is properly escaped if it's not a simple type;
            # however, for UUIDs and known safe strings, direct interpolation is common.
            # For security with arbitrary strings, parameter binding is preferred,
            # but SET LOCAL doesn't directly support parameters in the same way as DML.
            # Assuming value is a safe string (like UUID or 'true'/'false').
            cursor.execute(f"SET LOCAL {variable_name} = '{value}';")
            logger.debug(f"SQLAlchemyTenantContext: Set PG var: {variable_name} = '{value}'")
        else:
            cursor.execute(f"RESET {variable_name};")
            logger.debug(f"SQLAlchemyTenantContext: Set PG var: {variable_name} = NULL")

    @staticmethod
    def set_rls_session_variables(dbapi_connection, connection_record):
        """
        Set the RLS context in PostgreSQL using values from context variables.
        This function is called when a connection is checked out from the pool.
        """
        # Get values from context variables
        rls_tenant_id = current_rls_tenant_id_var.get()
        rls_clerk_org_id = current_rls_clerk_org_id_var.get()
        rls_is_system_admin = current_rls_is_system_admin_var.get()

        cursor = dbapi_connection.cursor()
        try:
            # Set app.current_tenant_id (for transitional RLS policies)
            if rls_tenant_id:
                try:
                    uuid.UUID(str(rls_tenant_id)) # Validate format
                    SQLAlchemyTenantContext._execute_set_variable(cursor, "app.current_tenant_id", str(rls_tenant_id))
                except ValueError:
                    logger.warning(f"Invalid UUID for rls_tenant_id: '{rls_tenant_id}', setting to NULL.")
                    SQLAlchemyTenantContext._execute_set_variable(cursor, "app.current_tenant_id", None)
            else:
                SQLAlchemyTenantContext._execute_set_variable(cursor, "app.current_tenant_id", None)

            # Set app.current_clerk_org_id (for new RLS policies)
            SQLAlchemyTenantContext._execute_set_variable(cursor, "app.current_clerk_org_id", rls_clerk_org_id)
            
            # Set app.is_system_admin
            admin_value_str = "true" if rls_is_system_admin else "false"
            SQLAlchemyTenantContext._execute_set_variable(cursor, "app.is_system_admin", admin_value_str)

            # Set app.rls.lookup_clerk_org_id for the public /by-clerk-org endpoint
            # This needs to be set based on query parameters for that specific endpoint,
            # not globally from the user's session clerk_org_id.
            # The endpoint itself handles this with `SET LOCAL`.
            # So, we don't set 'app.rls.lookup_clerk_org_id' here globally.

            logger.debug(
                f"SQLAlchemyTenantContext: Set session variables: "
                f"app.current_tenant_id='{rls_tenant_id}', "
                f"app.current_clerk_org_id='{rls_clerk_org_id}', "
                f"app.is_system_admin='{admin_value_str}'"
            )
        except Exception as e:
            logger.error(f"Error setting RLS session variables: {e}", exc_info=True)
            if hasattr(dbapi_connection, "rollback"):
                dbapi_connection.rollback()
            raise
        finally:
            cursor.close()

    # The following methods are examples of how to register the hook.
    # Actual registration happens in db/session.py or similar.

    @classmethod
    def get_checkout_listener(cls) -> Callable:
        """
        Returns the event listener function to be registered with SQLAlchemy's 'checkout' event.
        """
        return cls.set_rls_session_variables

    # Example usage (actual registration is in db/session.py):
    # from sqlalchemy import event
    # from sqlalchemy.pool import Pool
    # event.listen(Pool, "checkout", SQLAlchemyTenantContext.get_checkout_listener())
    
    # Methods for explicitly setting context outside of request cycle (e.g., background tasks)
    # These would directly call set_rls_session_variables on a given connection.
    # For background tasks, contextvars need to be propagated or set explicitly.

    @classmethod
    def set_context_for_connection(cls, dbapi_connection, connection_record,
                                   tenant_id: Optional[str] = None, # tenant_id for transition
                                   clerk_org_id: Optional[str] = None,
                                   is_system_admin: bool = False,
                                   is_admin: bool = False): # For backward compatibility
        """
        Utility to manually set RLS context on a DBAPI connection.

        Args:
            dbapi_connection: The database connection
            connection_record: Connection metadata
            tenant_id: Optional UUID string of tenant ID (for transition period)
            clerk_org_id: Optional string of clerk organization ID
            is_system_admin: Boolean flag indicating if context is system admin
            is_admin: Boolean flag for backward compatibility (old RLS policies)
        """
        cursor = dbapi_connection.cursor()
        try:
            if tenant_id:
                try:
                    uuid.UUID(str(tenant_id))
                    cls._execute_set_variable(cursor, "app.current_tenant_id", str(tenant_id))
                except ValueError:
                    cls._execute_set_variable(cursor, "app.current_tenant_id", None)
            else:
                cls._execute_set_variable(cursor, "app.current_tenant_id", None)

            cls._execute_set_variable(cursor, "app.current_clerk_org_id", clerk_org_id)

            # Set system admin flag (newer approach)
            cls._execute_set_variable(cursor, "app.is_system_admin", "true" if is_system_admin else "false")

            # Set is_admin flag (for backward compatibility)
            admin_value = is_admin or is_system_admin  # Either flag makes this true
            cls._execute_set_variable(cursor, "app.is_admin", "true" if admin_value else "false")
        finally:
            cursor.close()
