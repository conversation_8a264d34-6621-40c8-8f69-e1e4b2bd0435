"""
Error Handling Middleware

This middleware adds error handling capabilities to the application,
ensuring consistent error responses and proper logging.
"""

import logging
import time
import uuid
from typing import Awaitable, Callable, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.types import <PERSON><PERSON><PERSON>pp

from src.coherence.core.errors.base import CoherenceError, InternalError
from src.coherence.core.metrics import increment_error_counter

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for consistent error handling and request logging.

    This middleware adds a request ID to each request, logs request details,
    and ensures that all errors are properly caught and formatted.
    """

    def __init__(
        self,
        app: ASGIApp,
        exclude_paths: Optional[list[str]] = None,
        log_level: int = logging.INFO,
    ):
        """
        Initialize the middleware.

        Args:
            app: The ASGI application
            exclude_paths: List of paths to exclude from logging/tracking
            log_level: Logging level for request/response logs
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or []
        self.log_level = log_level

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Process the request and handle any errors.

        Args:
            request: The incoming request
            call_next: The next middleware or endpoint handler

        Returns:
            The response from the next middleware or endpoint
        """
        # Skip excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)

        # Generate a unique request ID if not already set
        if not hasattr(request.state, "request_id"):
            request.state.request_id = str(uuid.uuid4())

        # Record start time for request duration calculation
        start_time = time.time()

        # Log request details
        logger.log(
            self.log_level,
            f"Request started: {request.method} {request.url.path}",
            extra={
                "request_id": request.state.request_id,
                "method": request.method,
                "path": request.url.path,
                "client_host": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", "unknown"),
            },
        )

        try:
            # Process the request
            response = await call_next(request)

            # Calculate request duration
            duration = time.time() - start_time

            # Add the request ID to the response headers
            response.headers["X-Request-ID"] = request.state.request_id

            # Log response details
            logger.log(
                self.log_level,
                f"Request completed: {request.method} {request.url.path} {response.status_code}",
                extra={
                    "request_id": request.state.request_id,
                    "method": request.method,
                    "path": request.url.path,
                    "status_code": response.status_code,
                    "duration": duration,
                },
            )

            return response

        except CoherenceError as e:
            # Calculate request duration
            duration = time.time() - start_time

            # Log the error
            logger.error(
                f"Request failed with {type(e).__name__}: {e.message}",
                extra={
                    "request_id": request.state.request_id,
                    "method": request.method,
                    "path": request.url.path,
                    "error_code": e.error_code,
                    "duration": duration,
                },
                exc_info=e.original_exception or e,
            )

            # Increment the error counter metric
            increment_error_counter(
                error_type=type(e).__name__,
                endpoint=request.url.path,
                status_code=e.status_code,
            )

            # Re-raise the exception to be handled by the exception handlers
            raise

        except Exception as e:
            # Calculate request duration
            duration = time.time() - start_time

            # Log the unexpected error
            logger.error(
                f"Request failed with unhandled exception: {type(e).__name__}: {str(e)}",
                extra={
                    "request_id": request.state.request_id,
                    "method": request.method,
                    "path": request.url.path,
                    "duration": duration,
                },
                exc_info=e,
            )

            # Increment the error counter metric
            increment_error_counter(
                error_type=type(e).__name__,
                endpoint=request.url.path,
                status_code=500,
            )

            # Wrap unhandled exceptions in an InternalError to ensure consistent handling
            internal_error = InternalError(
                message="An unexpected error occurred",
                original_exception=e,
            )

            # Raise the internal error to be handled by the exception handlers
            raise internal_error from e


def add_request_id_context(
    get_response_fn: Callable[[Request], Awaitable[Response]]
) -> Callable[[Request], Awaitable[Response]]:
    """
    Middleware function to add request ID to the request context.

    This is a simpler middleware that just adds a request ID to the request
    context without the full error handling of ErrorHandlingMiddleware.

    Args:
        get_response_fn: The original get_response function

    Returns:
        A middleware function
    """

    async def middleware(request: Request) -> Response:
        """
        Add a request ID to the request context.

        Args:
            request: The incoming request

        Returns:
            The response from the next middleware or endpoint
        """
        # Generate a unique request ID if not already set
        if not hasattr(request.state, "request_id"):
            request.state.request_id = str(uuid.uuid4())

        # Process the request
        response = await get_response_fn(request)

        # Add the request ID to the response headers
        response.headers["X-Request-ID"] = request.state.request_id

        return response

    return middleware
