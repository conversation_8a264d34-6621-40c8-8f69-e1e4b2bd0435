from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

# --- Workflow Step Schemas ---

class AdminWorkflowStepBase(BaseModel):
    name: str = Field(..., description="Name of the workflow step.")
    step_type: str = Field(..., description="Type of the workflow step (e.g., 'action', 'condition', 'integration').")
    config: Dict[str, Any] = Field({}, description="Configuration for the workflow step.")
    order: int = Field(..., ge=0, description="Execution order of the step.")

class AdminWorkflowStepCreate(AdminWorkflowStepBase):
    pass

class AdminWorkflowStep(AdminWorkflowStepBase):
    id: UUID = Field(..., description="Unique identifier for the workflow step.")

    model_config = ConfigDict(from_attributes=True)


# --- Workflow Schemas ---

class AdminWorkflowBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="Name of the workflow.")
    description: Optional[str] = Field(None, max_length=1000, description="Optional description for the workflow.")
    steps: List[AdminWorkflowStepCreate] = Field([], description="List of steps in the workflow.")
    is_enabled: bool = Field(True, description="Whether the workflow is currently enabled.")

class AdminWorkflowCreate(AdminWorkflowBase):
    pass

class AdminWorkflowUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Name of the workflow.")
    description: Optional[str] = Field(None, max_length=1000, description="Optional description for the workflow.")
    steps: Optional[List[AdminWorkflowStepCreate]] = Field(None, description="List of steps in the workflow.")
    is_enabled: Optional[bool] = Field(None, description="Whether the workflow is currently enabled.")

class AdminWorkflowInDBBase(AdminWorkflowBase):
    id: UUID = Field(..., description="Unique identifier for the workflow.")
    tenant_id: UUID = Field(..., description="Identifier of the tenant owning this workflow.")
    created_at: datetime = Field(..., description="Timestamp of when the workflow was created.")
    updated_at: datetime = Field(..., description="Timestamp of when the workflow was last updated.")
    
    steps: List[AdminWorkflowStep] = Field([], description="List of steps in the workflow with their IDs.")


    model_config = ConfigDict(from_attributes=True)

# Schema for API responses
class AdminWorkflow(AdminWorkflowInDBBase):
    pass 