from .admin_template import (
    AdminTemplate,  # Renamed from AdminTemplateInDB as per actual class name
    AdminTemplateCreate,
    AdminTemplateUpdate,
)
from .admin_workflow import (
    AdminWorkflow,  # Renamed from AdminWorkflowInDB as per actual class name
    AdminWorkflowCreate,
    AdminWorkflowUpdate,
)
from .mixins import TenantMixin  # Corrected to import existing TenantMixin
from .openapi import (  # Updated to reflect actual classes in openapi.py
    ActionCode,
    ActionParameter,
    ActionVersions,
    AuthMethod,
    GenerateActionsRequest,
    GenerateActionsResponse,
    GeneratedAction,
    GetActionRequest,
    ImportOpenAPIRequest,
    ImportOpenAPIResponse,
    OpenAPIEndpoint,
    ValidateActionRequest,
    ValidateActionResponse,
)

# APIIntegration, # Assuming these are defined elsewhere or will be removed if not
# APIIntegrationCreate,
# APIIntegrationUpdate,
# APIIntegrationInDB,
# APIEndpoint as APIEndpointSchema, # Renaming to avoid conflict if another APIEndpoint exists
# APIEndpointCreate,
# APIEndpointUpdate,
# APIEndpointInDB,
# APIAuthConfig,
# APIAuthConfigCreate,
# APIAuthConfigUpdate,
# APIAuthConfigInDB,
# APIRateLimit,
# APIRateLimitCreate,
# APIRateLimitUpdate,
# APIRateLimitInDB,
# OAuthState,
# OAuthStateCreate,
# OAuthStateUpdate,
# OAuthStateInDB,
# Note: The commented out lines for APIIntegration, APIEndpoint, etc. are kept
# in case they are defined in another part of openapi.py not shown or are needed from elsewhere.
# If they are truly unused and not defined, they should be removed.
# For now, focusing on fixing the immediate OpenAPISpec import error.
from .request import ResolveRequest
from .response import (  # Removed ErrorResponse, HealthCheckResponse
    FallbackResponse,
    ResolveResponse,
)
from .system_admin import (
    SystemAdmin,
    SystemAdminAPIKey,
    SystemAdminAPIKeyCreate,
    SystemAdminAPIKeyCreateResponse,
    SystemAdminAPIKeyUpdate,
    SystemAdminCreate,
    SystemAdminUpdate,
)
from .tenant import (
    APIKeyCreate,
    APIKeyCreateResponse,
    APIKeyRead,  # Adding other relevant schemas from tenant.py
    APIKeyUpdate,
    OrganizationAPIKeyCreateRequest,
    OrganizationAPIKeyCreateResponse,
    OrganizationAPIKeyRead,
    OrganizationAPIKeyUpdateRequest,
    TenantCreate,
    TenantRead,  # Renamed from Tenant
    # TenantInDB, # Removed, TenantRead serves this purpose
    TenantSettingsBase,
    TenantSettingsCreate,
    TenantSettingsRead,  # Replaced TenantSettings with specific schemas
    TenantSettingsUpdate,
    TenantUpdate,
    TenantWithAPIKeys,
)

__all__ = [
    "AdminTemplate",
    "AdminTemplateCreate",
    "AdminTemplateUpdate",
    "AdminTemplate",
    "AdminWorkflow",
    "AdminWorkflowCreate",
    "AdminWorkflowUpdate",
    "AdminWorkflow",
    "TenantMixin", # Added TenantMixin to __all__
    "ImportOpenAPIRequest",
    "OpenAPIEndpoint",
    "AuthMethod",
    "ImportOpenAPIResponse",
    "GenerateActionsRequest",
    "ActionParameter",
    "GeneratedAction",
    "GenerateActionsResponse",
    "ActionCode",
    "ActionVersions",
    "ValidateActionRequest",
    "ValidateActionResponse",
    "GetActionRequest",
    # "APIIntegration",
    # "APIIntegrationCreate",
    # "APIIntegrationUpdate",
    # "APIIntegrationInDB",
    # "APIEndpointSchema", # Corresponds to renamed import
    # "APIEndpointCreate",
    # "APIEndpointUpdate",
    # "APIEndpointInDB",
    # "APIAuthConfig",
    # "APIAuthConfigCreate",
    # "APIAuthConfigUpdate",
    # "APIAuthConfigInDB",
    # "APIRateLimit",
    # "APIRateLimitCreate",
    # "APIRateLimitUpdate",
    # "APIRateLimitInDB",
    # "OAuthState",
    # "OAuthStateCreate",
    # "OAuthStateUpdate",
    # "OAuthStateInDB",
    "ResolveRequest",
    "ResolveResponse",
    "FallbackResponse", # Added FallbackResponse
    "TenantRead",
    "TenantCreate",
    "TenantUpdate",
    # "TenantInDB", # Removed
    "TenantSettingsBase",
    "TenantSettingsCreate",
    "TenantSettingsUpdate",
    "TenantSettingsRead",
    "APIKeyRead",
    "APIKeyCreate",
    "APIKeyUpdate",
    "APIKeyCreateResponse",
    "OrganizationAPIKeyRead",
    "OrganizationAPIKeyCreateRequest",
    "OrganizationAPIKeyCreateResponse",
    "OrganizationAPIKeyUpdateRequest",
    "TenantWithAPIKeys",
    "SystemAdmin",
    "SystemAdminCreate",
    "SystemAdminUpdate",
    "SystemAdminAPIKey",
    "SystemAdminAPIKeyCreate",
    "SystemAdminAPIKeyUpdate",
    "SystemAdminAPIKeyCreateResponse",
]