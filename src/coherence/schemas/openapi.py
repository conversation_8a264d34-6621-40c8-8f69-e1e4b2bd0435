"""
Schema definitions for OpenAPI integration API endpoints.
"""
import uuid
from typing import Any, Dict, List, Optional

from pydantic import UUID4, BaseModel, Field, validator

from .mixins import TenantMixin


class ImportOpenAPIRequest(BaseModel):
    """Request model for importing an API specification (OpenAPI, FAPI, or BAPI)."""

    name: str = Field(
        ..., description="Name for this API integration", min_length=1, max_length=100
    )
    spec: Dict = Field(..., description="API specification as a JSON object")
    url: Optional[str] = Field(None, description="URL to fetch the API specification from")
    content: Optional[str] = Field(None, description="API specification as a JSON string")
    
    @validator("content", always=True)
    def validate_inputs(cls, content, values):
        """Validate that at least one input method is provided."""
        if not values.get("spec") and not content and not values.get("url"):
            raise ValueError("You must provide either spec, content, or url")
        return content


class OpenAPIEndpoint(BaseModel):
    """Model for an API endpoint extracted from an OpenAPI spec."""

    id: str = Field(..., description="Unique ID of the endpoint")
    path: str = Field(..., description="API endpoint path")
    method: str = Field(..., description="HTTP method")
    operation_id: Optional[str] = Field(
        None, description="Operation ID from OpenAPI spec"
    )


class AuthMethod(BaseModel):
    """Model for an authentication method extracted from an OpenAPI spec."""

    type: str = Field(..., description="Authentication type")
    description: Optional[str] = Field(
        None, description="Description of the auth method"
    )
    # Additional fields depending on auth type
    name: Optional[str] = Field(None, description="Name for API key auth")
    in_location: Optional[str] = Field(
        None, alias="in", description="Location for API key (header, query, etc.)"
    )
    flows: Optional[Dict] = Field(None, description="OAuth2 flows")


class ImportOpenAPIResponse(TenantMixin, BaseModel):
    """Response model for importing an API specification."""

    integration_id: str = Field(..., description="Unique ID of the created integration")
    # tenant_id is inherited from TenantMixin
    name: str = Field(..., description="Name of the integration")
    version: str = Field(..., description="API version from spec")
    base_url: Optional[str] = Field(None, description="Base URL for the API")
    endpoints: List[OpenAPIEndpoint] = Field(..., description="Extracted API endpoints")
    auth_methods: List[AuthMethod] = Field([], description="Authentication methods")
    spec_format: str = Field("openapi", description="Format of the imported API specification")


class GenerateActionsRequest(TenantMixin, BaseModel):
    """Request model for generating actions from an OpenAPI integration."""

    integration_id: UUID4 = Field(..., description="ID of the API integration")
    # tenant_id is inherited from TenantMixin

class ActionParameter(BaseModel):
    """Model for a parameter in an API action."""

    name: str = Field(..., description="Parameter name")
    in_location: str = Field(
        ..., alias="in", description="Parameter location (path, query, body)"
    )
    required: bool = Field(False, description="Whether the parameter is required")
    description: Optional[str] = Field(None, description="Parameter description")
    schema_data: Dict = Field(
        {}, alias="parameter_schema", description="JSON Schema for the parameter"
    )
    media_type: Optional[str] = Field(
        None, description="Media type for body parameters"
    )


class GeneratedAction(BaseModel):
    """Model for a generated action from an API endpoint."""

    endpoint_id: str = Field(..., description="ID of the API endpoint")
    action_name: str = Field(..., description="Generated action name")
    class_name: str = Field(..., description="Generated class name")
    parameters: List[ActionParameter] = Field(
        [], description="Parameters for the action"
    )
    description: str = Field("", description="Generated action description")


class GenerateActionsResponse(TenantMixin, BaseModel):
    """Response model for generating actions from an OpenAPI integration."""

    actions: List[GeneratedAction] = Field(..., description="Generated actions")
    # tenant_id is inherited from TenantMixin

class ActionCode(TenantMixin, BaseModel):
    """Model for a stored action code."""

    id: str = Field(..., description="Unique ID of the generated action")
    endpoint_id: str = Field(..., description="ID of the API endpoint")
    class_name: str = Field(..., description="Generated class name")
    version: str = Field(..., description="Version hash of the action")
    validation_status: str = Field(..., description="Validation status of the action")
    validation_message: Optional[str] = Field(
        None, description="Validation error message if any"
    )
    generated_at: str = Field(
        ..., description="Timestamp when the action was generated"
    )
    last_validated_at: Optional[str] = Field(
        None, description="Timestamp when the action was last validated"
    )
    code: Optional[str] = Field(None, description="Generated Python code")


class ActionVersions(TenantMixin, BaseModel):
    """Model for listing available versions of an action."""

    endpoint_id: str = Field(..., description="ID of the API endpoint")
    # tenant_id is inherited from TenantMixin
    active_version: Optional[str] = Field(None, description="Currently active version")
    versions: List[ActionCode] = Field(..., description="Available versions")


class ValidateActionRequest(TenantMixin, BaseModel):
    """Request model for validating an action."""

    action_id: UUID4 = Field(..., description="ID of the generated action to validate")
    # tenant_id is inherited from TenantMixin

class ValidateActionResponse(TenantMixin, BaseModel):
    """Response model for action validation."""

    id: str = Field(..., description="ID of the generated action")
    endpoint_id: str = Field(..., description="ID of the API endpoint")
    class_name: str = Field(..., description="Generated class name")
    validation_status: str = Field(
        ..., description="Validation status after validation"
    )
    validation_message: Optional[str] = Field(
        None, description="Validation error message if any"
    )
    is_valid: bool = Field(..., description="Whether the action is valid")


class GetActionRequest(TenantMixin, BaseModel):
    """Request model for getting an action."""

    endpoint_id: UUID4 = Field(..., description="ID of the API endpoint")
    # tenant_id is inherited from TenantMixin
    version: Optional[str] = Field(
        None,
        description="Version hash of the action (optional, get latest if not provided)",
    )
    include_code: bool = Field(
        True, description="Whether to include the code in the response"
    )
    include_outdated: bool = Field(
        False, description="Whether to include outdated versions"
    )


class ActionTestRequest(BaseModel):
    """Request model for testing an API action."""

    action_config: Dict[str, Any] = Field(
        ..., description="Configuration for the action to execute"
    )
    parameters: Dict[str, Any] = Field(
        ..., description="Parameters to pass to the action"
    )
    integration_id: Optional[str] = Field(
        None, description="ID of the integration (if applicable)"
    )
    tenant_id: Optional[uuid.UUID] = Field(
        None, description="The tenant ID (optional, will use authenticated tenant's ID if not provided)"
    )


class ActionTestResponse(BaseModel):
    """Response model for API action test results."""

    success: bool = Field(..., description="Whether the request succeeded")
    status_code: Optional[int] = Field(None, description="HTTP status code")
    result: Optional[Dict[str, Any]] = Field(None, description="Processed API response")
    raw_response: Optional[Any] = Field(None, description="Raw API response")
    headers: Optional[Dict[str, str]] = Field(None, description="Response headers")
    error: Optional[Dict[str, Any]] = Field(None, description="Error information if failed")
    cached: bool = Field(False, description="Whether the response was from cache")


class UpdateIntegrationRequest(BaseModel):
    """Request model for updating an API integration."""
    
    name: Optional[str] = Field(None, description="New name for the integration")
    description: Optional[str] = Field(None, description="New description for the integration")
    base_url: Optional[str] = Field(None, description="New base URL for the integration")
