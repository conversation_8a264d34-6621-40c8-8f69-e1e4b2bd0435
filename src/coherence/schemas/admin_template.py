from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


# Re-define enums for Pydantic validation, mirroring SQLAlchemy models
class AdminTemplateScope(str, Enum):
    TENANT = "tenant"
    # Admin might only manage tenant scope, or view global/pack
    # For now, focusing on tenant as per admin site context
    GLOBAL = "global"
    PACK = "pack"

class AdminTemplateCategory(str, Enum):
    INTENT_ROUTER = "intent_router"
    PARAM_COMPLETE = "param_complete"
    RETRIEVAL = "retrieval"
    RESPONSE_GEN = "response_gen"
    ERROR_HANDLER = "error_handler"
    ACTION = "action"
    DOCUMENTATION = "documentation"
    UNIFIED = "unified"

# --- Template Version Schema (Read-Only for Admin API) ---
class AdminTemplateVersionBase(BaseModel):
    version: int = Field(..., description="Version number of the template.")
    body: str = Field(..., description="The main content/body of the template version.")
    actions: Optional[Dict[str, Any]] = Field(None, description="Actions for intent_router templates (version specific).")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Parameter schema for the template (version specific).")
    response_format: Optional[Dict[str, Any]] = Field(None, description="Response formatting configuration (version specific).")
    
    # Unified template version fields
    endpoint_id: Optional[str] = Field(None, description="Endpoint identifier for this version")
    intent_config: Optional[Dict[str, Any]] = Field(None, description="Intent patterns and configuration for this version")
    action_config: Optional[Dict[str, Any]] = Field(None, description="API action configuration for this version")
    ui_fields: Optional[Dict[str, Any]] = Field(None, description="UI field definitions for this version")
    prompts: Optional[Dict[str, Any]] = Field(None, description="Prompts configuration for this version")
    docs_config: Optional[Dict[str, Any]] = Field(None, description="Documentation configuration for this version")
    test_data: Optional[Dict[str, Any]] = Field(None, description="Test data for this version")
    
    editor_id: Optional[UUID] = Field(None, description="ID of the user who edited this version.")
    edited_at: datetime = Field(..., description="Timestamp of when this version was edited/created.")
    change_reason: Optional[str] = Field(None, description="Reason for this template version change.")

    model_config = ConfigDict(from_attributes=True)

class AdminTemplateVersion(AdminTemplateVersionBase):
    template_id: UUID = Field(..., description="ID of the parent template.")

# --- Admin Template Schemas ---
class AdminTemplateBase(BaseModel):
    key: str = Field(..., min_length=1, max_length=255, description="Unique key for the template within its scope and category.")
    description: Optional[str] = Field(None, max_length=1000, description="Optional description for the template.")
    category: AdminTemplateCategory = Field(..., description="Category of the template.")
    language: str = Field(default="en", min_length=2, max_length=10, description="Language code for the template (e.g., en, es).")
    body: str = Field(..., description="The main content/body of the template.")
    actions: Optional[Dict[str, Any]] = Field(None, description="Actions for intent_router templates.")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Parameter schema for the template.")
    response_format: Optional[Dict[str, Any]] = Field(None, description="Response formatting configuration for the template.")
    protected: bool = Field(default=False, description="When true, template cannot be edited or deleted by regular admins")
    
    # Unified template fields
    endpoint_id: Optional[str] = Field(None, description="Endpoint identifier for unified templates (e.g., 'GET_/api/weather')")
    intent_config: Optional[Dict[str, Any]] = Field(None, description="Intent patterns and configuration for unified templates")
    action_config: Optional[Dict[str, Any]] = Field(None, description="API action configuration (method, path, auth, retries)")
    ui_fields: Optional[Dict[str, Any]] = Field(None, description="UI field definitions for parameter forms")
    prompts: Optional[Dict[str, Any]] = Field(None, description="System, extraction, and completion prompts")
    docs_config: Optional[Dict[str, Any]] = Field(None, description="Documentation configuration and examples")
    test_data: Optional[Dict[str, Any]] = Field(None, description="Test data and mock responses for development")
    # scope will likely be TENANT for admin created templates, tenant_id will be from context

class AdminTemplateCreate(AdminTemplateBase):
    # When creating, a new version 1 is implicitly made.
    # The initial `body`, `actions`, `parameters` go into both the Template and TemplateVersion v1.
    change_reason: Optional[str] = Field(None, description="Reason for creating this initial version.")

class AdminTemplateUpdate(BaseModel):
    key: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    category: Optional[AdminTemplateCategory] = None
    language: Optional[str] = Field(None, min_length=2, max_length=10)
    body: Optional[str] = None # Updating body creates a new version
    actions: Optional[Dict[str, Any]] = None # Updating actions creates a new version
    parameters: Optional[Dict[str, Any]] = None # Updating parameters creates a new version
    response_format: Optional[Dict[str, Any]] = None # Updating response format creates a new version
    protected: Optional[bool] = None # If set, updates the protection status
    
    # Unified template fields for updates
    endpoint_id: Optional[str] = None
    intent_config: Optional[Dict[str, Any]] = None # Updating intent config creates a new version
    action_config: Optional[Dict[str, Any]] = None # Updating action config creates a new version
    ui_fields: Optional[Dict[str, Any]] = None # Updating UI fields creates a new version
    prompts: Optional[Dict[str, Any]] = None # Updating prompts creates a new version
    docs_config: Optional[Dict[str, Any]] = None # Updating docs config creates a new version
    test_data: Optional[Dict[str, Any]] = None # Updating test data creates a new version
    
    change_reason: Optional[str] = Field(None, description="Reason for this update (creates new version if body/actions/params change).")

class AdminTemplateInDBBase(AdminTemplateBase):
    id: UUID = Field(..., description="Unique identifier for the template.")
    tenant_id: UUID = Field(..., description="Identifier of the tenant owning this template (for TENANT scope). Synthetic value for global templates.")
    scope: AdminTemplateScope = Field(..., description="Scope of the template, e.g., tenant, global, pack.")
    version: int = Field(..., description="Current active version number of the template.")
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID] = None
    protected: bool = Field(False, description="If true, template cannot be edited or deleted by regular admins")

    latest_version_details: Optional[AdminTemplateVersionBase] = Field(None, description="Details of the current active version.")
    # versions: List[AdminTemplateVersion] = Field([], description="Full history of template versions.") # Potentially too much for list/get by default

    model_config = ConfigDict(from_attributes=True)
    
    # Custom validator to handle global templates without tenant_id
    @classmethod
    def model_validate(cls, obj, *, context=None, update=None, strict=None, from_attributes=None):
        """Custom validator to handle global templates without tenant_id."""
        # If it's a global template without tenant_id, use a default UUID
        if getattr(obj, 'scope', None) == AdminTemplateScope.GLOBAL and getattr(obj, 'tenant_id', None) is None:
            if update is None:
                update = {}
            update['tenant_id'] = UUID('00000000-0000-0000-0000-000000000000')
        return super().model_validate(obj, context=context, update=update, strict=strict, from_attributes=from_attributes)

class AdminTemplate(AdminTemplateInDBBase):
    pass

class AdminTemplateWithVersions(AdminTemplateInDBBase):
    versions: List[AdminTemplateVersion] = Field([], description="Full history of template versions.")

# --- Documentation Template Schemas ---
class ParameterDocumentation(BaseModel):
    """Documentation for a single parameter"""
    name: str = Field(..., description="Parameter name")
    description: str = Field(..., description="Detailed description of the parameter")
    type: str = Field(..., description="Data type of the parameter (e.g., string, number, boolean)")
    format: Optional[str] = Field(None, description="Format specification (e.g., date-time, email)")
    required: bool = Field(False, description="Whether the parameter is required")
    default: Optional[Any] = Field(None, description="Default value if not provided")
    examples: List[Any] = Field(default_factory=list, description="Example values")
    constraints: Optional[Dict[str, Any]] = Field(None, description="Validation constraints (min, max, pattern, etc.)")
    
class ResponseDocumentation(BaseModel):
    """Documentation for an API response"""
    status_code: int = Field(..., description="HTTP status code")
    description: str = Field(..., description="Description of when this response is returned")
    content_type: str = Field(default="application/json", description="Response content type")
    schema: Optional[Dict[str, Any]] = Field(None, description="Response schema")
    examples: Optional[Dict[str, Any]] = Field(None, description="Example responses")
    
class ExampleDocumentation(BaseModel):
    """Example usage of an API endpoint"""
    title: str = Field(..., description="Title of the example")
    description: Optional[str] = Field(None, description="Detailed description of the example")
    request: str = Field(..., description="Natural language request example")
    parameters: Dict[str, Any] = Field(..., description="Extracted parameters from the request")
    response: Dict[str, Any] = Field(..., description="Expected API response")
    notes: Optional[str] = Field(None, description="Additional notes or explanations")
    
class RateLimitDocumentation(BaseModel):
    """Rate limiting information"""
    limit: int = Field(..., description="Number of requests allowed")
    window: str = Field(..., description="Time window (e.g., '1m', '1h', '1d')")
    scope: str = Field(default="user", description="Scope of rate limit (user, tenant, global)")
    description: Optional[str] = Field(None, description="Additional rate limit details")
    
class AuthenticationDocumentation(BaseModel):
    """Authentication requirements"""
    required: bool = Field(..., description="Whether authentication is required")
    methods: List[str] = Field(..., description="Supported authentication methods")
    scopes: Optional[List[str]] = Field(None, description="Required permission scopes")
    description: Optional[str] = Field(None, description="Additional authentication details")

class DocumentationContent(BaseModel):
    """Complete documentation content for an API endpoint"""
    endpoint: str = Field(..., description="API endpoint path")
    method: str = Field(..., description="HTTP method")
    summary: str = Field(..., description="Short summary of what the endpoint does")
    description: str = Field(..., description="Detailed description of the endpoint")
    parameters: List[ParameterDocumentation] = Field(default_factory=list, description="Parameter documentation")
    responses: List[ResponseDocumentation] = Field(default_factory=list, description="Response documentation")
    examples: List[ExampleDocumentation] = Field(default_factory=list, description="Usage examples")
    rate_limits: Optional[RateLimitDocumentation] = Field(None, description="Rate limiting information")
    authentication: Optional[AuthenticationDocumentation] = Field(None, description="Authentication requirements")
    deprecation: Optional[Dict[str, Any]] = Field(None, description="Deprecation information if applicable")
    related_endpoints: Optional[List[str]] = Field(None, description="Related API endpoints")
    tags: List[str] = Field(default_factory=list, description="Tags for categorization")
    
class DocumentationTemplate(AdminTemplateBase):
    """Template specifically for API documentation"""
    category: AdminTemplateCategory = Field(default=AdminTemplateCategory.DOCUMENTATION, description="Always 'documentation' for doc templates")
    
    # Override content field to use structured documentation
    body: str = Field(None, description="Deprecated - use documentation_content instead")
    documentation_content: DocumentationContent = Field(..., description="Structured documentation content")
    
    # Links to related templates
    related_action_template: Optional[str] = Field(None, description="Key of the related action template")
    related_intent_template: Optional[str] = Field(None, description="Key of the related intent template")
    
    # Metadata for better search and discovery
    searchable_content: Optional[str] = Field(None, description="Concatenated text for better vector search")
    
    class Config:
        json_schema_extra = {
            "example": {
                "key": "weather_current_get_documentation",
                "description": "Documentation for getting current weather",
                "documentation_content": {
                    "endpoint": "/weather/current",
                    "method": "GET",
                    "summary": "Get current weather conditions",
                    "description": "Returns the current weather conditions for a specified location",
                    "parameters": [
                        {
                            "name": "location",
                            "description": "City name, coordinates, or zip code",
                            "type": "string",
                            "required": True,
                            "examples": ["New York", "40.7128,-74.0060", "10001"]
                        }
                    ],
                    "responses": [
                        {
                            "status_code": 200,
                            "description": "Successful response with weather data",
                            "schema": {
                                "temperature": "number",
                                "conditions": "string",
                                "humidity": "number"
                            }
                        }
                    ]
                }
            }
        }


# --- Template Test Schemas ---
class TemplateTestRequest(BaseModel):
    """Request schema for testing a template."""
    parameters: Optional[Dict[str, Any]] = Field(
        None, 
        description="Parameters to test with. If not provided, will use sample_parameters from test_data"
    )
    scenario: Optional[str] = Field(
        "success",
        description="Test scenario to use (e.g., 'success', 'error', 'not_found'). Must match a key in mock_responses"
    )
    user_preference: Optional[str] = Field(
        "structured",
        description="Response format preference for CRFS formatting (structured, markdown, plain)"
    )

    model_config = ConfigDict(from_attributes=True)


class TemplateTestResponse(BaseModel):
    """Response schema for template test results."""
    template_id: UUID = Field(..., description="ID of the tested template")
    template_key: str = Field(..., description="Key of the tested template")
    scenario: str = Field(..., description="Test scenario that was used")
    parameters_provided: Dict[str, Any] = Field(..., description="Parameters that were provided for testing")
    parameters_transformed: Dict[str, Any] = Field(..., description="Parameters after transformation rules were applied")
    validation_errors: List[str] = Field(default_factory=list, description="Parameter validation errors if any")
    mock_response: Dict[str, Any] = Field(..., description="Mock response returned for the scenario")
    formatted_response: Optional[Dict[str, Any]] = Field(None, description="CRFS-formatted response if available")
    test_data_available: bool = Field(..., description="Whether the template has test_data configured")
    scenarios_available: List[str] = Field(default_factory=list, description="List of available test scenarios")

    model_config = ConfigDict(from_attributes=True)