from datetime import datetime
from typing import Any, Optional  # Using Any for JSONB as requested

from pydantic import UUID4, BaseModel, Field


# Schemas for SystemAdmin
class SystemAdminBase(BaseModel):
    clerk_user_id: str = Field(..., description="Clerk User ID associated with the system admin")


class SystemAdminCreate(SystemAdminBase):
    created_by: str = Field(..., description="Identifier of the user/system creating this admin")


class SystemAdminUpdate(BaseModel):
    # Currently no updatable fields defined for SystemAdmin itself.
    # If fields become updatable, add them here as Optional.
    pass


class SystemAdminInDBBase(SystemAdminBase):
    id: UUID4 = Field(..., description="Unique identifier for the system admin")
    created_at: datetime = Field(..., description="Timestamp when the admin was created")
    created_by: str = Field(..., description="Identifier of the user/system that created this admin")

    model_config = {"from_attributes": True}


class SystemAdmin(SystemAdminInDBBase):
    # Main response model for SystemAdmin
    pass


# Schemas for SystemAdminAPIKey
class SystemAdminAPIKeyBase(BaseModel):
    name: str = Field(..., description="A user-friendly name for the API key")
    permissions: Optional[Any] = Field(None, description="JSONB field for storing specific permissions associated with the key")
    expires_at: Optional[datetime] = Field(None, description="Optional expiration timestamp for the API key")


class SystemAdminAPIKeyCreate(SystemAdminAPIKeyBase):
    created_by: str = Field(..., description="Identifier of the user/system creating this API key")


class SystemAdminAPIKeyUpdate(BaseModel):
    name: Optional[str] = Field(None, description="Updated name for the API key")
    permissions: Optional[Any] = Field(None, description="Updated permissions for the API key")
    expires_at: Optional[datetime] = Field(None, description="Updated expiration timestamp for the API key")
    revoked: Optional[bool] = Field(None, description="Flag to revoke or un-revoke the API key")


class SystemAdminAPIKeyInDBBase(SystemAdminAPIKeyBase):
    id: UUID4 = Field(..., description="Unique identifier for the API key")
    key_hash: str = Field(..., description="Hashed value of the API key (the raw key is not stored)")
    system_admin_id: UUID4 = Field(..., description="ID of the system admin this key belongs to")
    created_at: datetime = Field(..., description="Timestamp when the API key was created")
    created_by: str = Field(..., description="Identifier of the user/system that created this API key")
    last_used_at: Optional[datetime] = Field(None, description="Timestamp when the API key was last used")
    revoked: bool = Field(..., description="Indicates if the API key has been revoked")

    model_config = {"from_attributes": True}


class SystemAdminAPIKey(SystemAdminAPIKeyInDBBase):
    # Main response model for SystemAdminAPIKey (doesn't include the raw key)
    pass


class SystemAdminAPIKeyCreateResponse(SystemAdminAPIKey):
    # Response model used *only* upon creation, includes the raw API key
    api_key: str = Field(..., description="The actual generated API key (only shown once upon creation)")