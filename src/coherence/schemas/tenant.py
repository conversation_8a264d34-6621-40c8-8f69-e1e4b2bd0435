"""
Pydantic schemas for tenant management in the Coherence API.

These schemas define the data structures for creating, updating,
and retrieving tenant information and API keys.
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, ConfigDict, EmailStr, Field

from .mixins import TenantMixin


class TenantBase(BaseModel):
    """Base schema with common tenant fields."""

    name: str = Field(..., description="Name of the tenant organization")
    clerk_org_id: Optional[str] = Field(
        None, description="Clerk organization ID associated with this tenant"
    )
    industry_pack: Optional[str] = Field(
        None, description="Industry-specific template pack"
    )
    compliance_tier: Optional[str] = Field(
        None, description="Compliance tier for this tenant"
    )
    settings: Optional[Dict[str, str]] = Field(
        None, description="Additional tenant settings as a JSON object"
    )


class TenantCreate(TenantBase):
    """Schema for creating a new tenant."""

    admin_email: EmailStr = Field(..., description="Email of the tenant admin user")


class TenantUpdate(BaseModel):
    """Schema for updating an existing tenant."""

    name: Optional[str] = Field(None, description="Name of the tenant organization")
    industry_pack: Optional[str] = Field(
        None, description="Industry-specific template pack"
    )
    compliance_tier: Optional[str] = Field(
        None, description="Compliance tier for this tenant"
    )
    settings: Optional[Dict[str, str]] = Field(
        None, description="Additional tenant settings as a JSON object"
    )


class TenantRead(TenantBase):
    """Schema for reading tenant information."""

    id: uuid.UUID = Field(..., description="Unique tenant identifier")
    created_at: datetime = Field(..., description="When the tenant was created")
    updated_at: datetime = Field(..., description="When the tenant was last updated")

    model_config = ConfigDict(from_attributes=True)


class APIKeyBase(BaseModel):
    """Base schema with common API key fields."""

    label: Optional[str] = Field(None, description="Optional label for the API key")
    expires_at: Optional[datetime] = Field(
        None, description="When this API key expires"
    )


class APIKeyCreate(TenantMixin, APIKeyBase):
    """Schema for creating a new API key."""

    # tenant_id is inherited from TenantMixin

class APIKeyUpdate(BaseModel):
    """Schema for updating an API key."""

    label: Optional[str] = Field(None, description="Optional label for the API key")
    revoked: Optional[bool] = Field(None, description="Whether this API key is revoked")
    expires_at: Optional[datetime] = Field(
        None, description="When this API key expires"
    )


class APIKeyRead(TenantMixin, APIKeyBase):
    """Schema for reading API key information (without the key itself)."""

    id: uuid.UUID = Field(..., description="Unique API key identifier")
    # tenant_id is inherited from TenantMixin
    created_at: datetime = Field(..., description="When the API key was created")
    last_used_at: Optional[datetime] = Field(
        None, description="When the API key was last used"
    )
    revoked: bool = Field(..., description="Whether this API key is revoked")

    model_config = ConfigDict(from_attributes=True)


class APIKeyCreateResponse(APIKeyRead):
    """Response schema when creating a new API key, including the key value."""

    key: str = Field(..., description="The API key value (only shown once)")


# NEW Schemas for OrganizationAPIKey

class OrganizationAPIKeyBase(BaseModel):
    """Base schema for Organization API Keys."""
    name: str = Field(..., description="A user-defined name for the API key.")
    permissions: Optional[List[str]] = Field(None, description="List of permissions associated with the key.")
    expires_at: Optional[datetime] = Field(None, description="Optional expiration date for the API key.")

class OrganizationAPIKeyCreateRequest(BaseModel):
    """Schema for creating a new Organization API Key. clerk_org_id is typically a path param."""
    name: str = Field(..., min_length=3, max_length=100, description="A user-defined name for the API key.")
    permissions: Optional[List[str]] = Field(None, description="List of permissions associated with the key. E.g., ['workflows:read', 'templates:write']")
    expires_at: Optional[datetime] = Field(None, description="Optional expiration date for the API key.")

class OrganizationAPIKeyRead(OrganizationAPIKeyBase):
    """Schema for reading an Organization API Key (excluding the sensitive key value)."""
    id: uuid.UUID = Field(..., description="Unique API key identifier.")
    clerk_org_id: str = Field(..., description="Clerk organization ID this key belongs to.")
    key_prefix: str = Field(..., description="The first few characters of the API key for identification.")
    created_by: str = Field(..., description="Clerk User ID of the creator.")
    created_at: datetime = Field(..., description="Timestamp of when the key was created.")
    last_used_at: Optional[datetime] = Field(None, description="Timestamp of when the key was last used.")
    revoked: bool = Field(..., description="Whether this API key is currently revoked.")

    model_config = ConfigDict(from_attributes=True)

class OrganizationAPIKeyCreateResponse(OrganizationAPIKeyRead):
    """
    Response schema when creating a new Organization API Key.
    Includes the full API key value, which is shown only once.
    """
    key: str = Field(..., description="The full API key value. Store this securely, it will not be shown again.")

class OrganizationAPIKeyUpdateRequest(BaseModel):
    """Schema for updating an Organization API Key."""
    name: Optional[str] = Field(None, min_length=3, max_length=100, description="New name for the API key.")
    permissions: Optional[List[str]] = Field(None, description="Updated list of permissions.")
    revoked: Optional[bool] = Field(None, description="Set to true to revoke the key, false to unrevoke (if allowed).")
    expires_at: Optional[datetime] = Field(None, description="New expiration date for the API key. Can be null to remove expiration.")
class TenantSettingsBase(BaseModel):
    """Base schema for tenant settings."""

    tier1_threshold: Optional[str] = Field(
        None, description="Confidence threshold for tier 1 intent resolution"
    )
    tier2_threshold: Optional[str] = Field(
        None, description="Confidence threshold for tier 2 intent resolution"
    )
    llm_model: Optional[str] = Field(
        None, description="LLM model to use for this tenant"
    )
    embedding_model: Optional[str] = Field(
        None, description="Embedding model to use for this tenant"
    )
    max_requests_per_min: Optional[str] = Field(
        None, description="Rate limit for API requests"
    )
    max_tokens_per_month: Optional[str] = Field(
        None, description="Token usage limit per month"
    )
    settings: Optional[Dict[str, str]] = Field(
        None, description="Additional settings as a JSON object"
    )


class TenantSettingsCreate(TenantMixin, TenantSettingsBase):
    """Schema for creating tenant settings."""

    # tenant_id is inherited from TenantMixin

class TenantSettingsUpdate(TenantSettingsBase):
    """Schema for updating tenant settings."""

    pass


class TenantSettingsRead(TenantMixin, TenantSettingsBase):
    """Schema for reading tenant settings."""

    # tenant_id is inherited from TenantMixin
    created_at: datetime = Field(..., description="When the settings were created")
    updated_at: datetime = Field(..., description="When the settings were last updated")

    model_config = ConfigDict(from_attributes=True)


class TenantWithAPIKeys(TenantRead):
    """Schema for a tenant with its API keys."""

    api_keys: List[APIKeyRead] = Field(..., description="API keys for this tenant")
