"""Enhanced OpenAPI schema models with full constraint support."""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field


class EnhancedParameterSchema(BaseModel):
    """Enhanced parameter schema with full OpenAPI support.
    
    This model captures all OpenAPI parameter schema information that can be
    used for better form generation, validation, and user experience.
    """
    name: str
    type: str
    in_location: str
    required: bool = False
    description: Optional[str] = None
    
    # Enhanced schema fields from OpenAPI specification
    default: Optional[Any] = None
    example: Optional[Any] = None
    enum: Optional[List[Any]] = None
    format: Optional[str] = None
    pattern: Optional[str] = None
    
    # Validation constraints
    minimum: Optional[Union[int, float]] = None
    maximum: Optional[Union[int, float]] = None
    exclusive_minimum: Optional[Union[int, float]] = None
    exclusive_maximum: Optional[Union[int, float]] = None
    multiple_of: Optional[Union[int, float]] = None
    
    # String constraints
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    
    # Array constraints
    min_items: Optional[int] = None
    max_items: Optional[int] = None
    unique_items: Optional[bool] = None
    
    # Object constraints
    min_properties: Optional[int] = None
    max_properties: Optional[int] = None
    
    # Additional metadata
    deprecated: Optional[bool] = None
    read_only: Optional[bool] = None
    write_only: Optional[bool] = None
    nullable: Optional[bool] = None
    
    # Media type for request body parameters
    media_type: Optional[str] = None
    
    # Raw schema for complex types
    raw_schema: Optional[Dict[str, Any]] = None

    class Config:
        validate_by_name = True


class EnhancedAPIEndpointRead(BaseModel):
    """Enhanced API endpoint read schema with rich parameter data."""
    id: str
    integration_id: str
    path: str
    method: str
    operation_id: Optional[str] = None
    summary: Optional[str] = None
    description: Optional[str] = None
    tags: List[str] = []
    deprecated: bool = False
    enabled: bool = True
    
    # Enhanced parameter information
    enhanced_parameters: List[EnhancedParameterSchema] = []
    
    # Response schema information
    response_schemas: Dict[str, Any] = {}
    
    # Operation-level security requirements
    operation_security: List[Dict[str, Any]] = []
    
    # Raw OpenAPI snippet for backwards compatibility
    openapi_snippet: Optional[Dict[str, Any]] = None


class ParameterConstraints(BaseModel):
    """Model for parameter validation constraints."""
    
    # Numeric constraints
    minimum: Optional[Union[int, float]] = None
    maximum: Optional[Union[int, float]] = None
    exclusive_minimum: Optional[Union[int, float]] = None
    exclusive_maximum: Optional[Union[int, float]] = None
    multiple_of: Optional[Union[int, float]] = None
    
    # String constraints
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    pattern: Optional[str] = None
    
    # Array constraints
    min_items: Optional[int] = None
    max_items: Optional[int] = None
    unique_items: Optional[bool] = None
    
    # Enum constraint
    enum: Optional[List[Any]] = None
    
    def has_constraints(self) -> bool:
        """Check if any constraints are defined."""
        return any([
            self.minimum is not None,
            self.maximum is not None,
            self.exclusive_minimum is not None,
            self.exclusive_maximum is not None,
            self.multiple_of is not None,
            self.min_length is not None,
            self.max_length is not None,
            self.pattern is not None,
            self.min_items is not None,
            self.max_items is not None,
            self.unique_items is not None,
            self.enum is not None,
        ])


class SchemaValidationResult(BaseModel):
    """Result of schema validation."""
    valid: bool
    errors: List[str] = []
    warnings: List[str] = []
    
    def add_error(self, message: str) -> None:
        """Add validation error."""
        self.valid = False
        self.errors.append(message)
        
    def add_warning(self, message: str) -> None:
        """Add validation warning."""
        self.warnings.append(message)