"""
Request schemas for the Coherence API.
"""

from typing import Any, Dict, Optional
from uuid import uuid4

from pydantic import UUID4, BaseModel, Field

from .mixins import TenantMixin


class ResolveRequest(TenantMixin, BaseModel):
    """
    Request schema for resolving a natural language message to an intent.

    Used with the /resolve endpoint to initiate a new conversation or
    continue an existing one with a new intent.
    """

    conversation_id: UUID4 = Field(
        default_factory=uuid4,
        description="Unique identifier for the conversation session",
    )
    user_id: str = Field(
        ...,
        description="Identifier for the user sending the message",
    )
    role: str = Field(
        ...,
        description="Role of the user (e.g., 'admin', 'user')",
    )
    message: str = Field(
        ...,
        description="Natural language message from the user",
    )
    context: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional context for the conversation",
    )
    template_id: Optional[str] = Field(
        None,
        description="Template ID to use for intent resolution (if not using the default)",
    )


class ContinueRequest(TenantMixin, BaseModel):
    """
    Request schema for continuing a conversation with additional information.

    Used with the /continue endpoint to respond to a previous 'ask' response
    where the system needs more information to complete an intent.
    """

    conversation_id: str = Field(
        ...,
        description="Identifier for the existing conversation to continue",
    )
    user_id: str = Field(
        ...,
        description="Identifier for the user sending the message",
    )
    message: str = Field(
        ...,
        description="User's response to a previous question",
    )
    field: Optional[str] = Field(
        None,
        description="The field this response is providing information for",
    )
    role: Optional[str] = Field(
        "user",
        description="Role of the user in the conversation",
    )
