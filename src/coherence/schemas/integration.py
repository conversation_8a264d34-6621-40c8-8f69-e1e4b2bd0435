"""Pydantic schemas for API Integrations."""

import uuid
from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel

from src.coherence.models.integration import AuthType, IntegrationStatus


# Base schemas for reading individual components
class APIRateLimitRead(BaseModel):
    id: uuid.UUID
    requests_per_min: int
    burst_size: int
    cooldown_sec: int

    class Config:
        orm_mode = True


class APIEndpointRead(BaseModel):
    id: uuid.UUID
    path: str
    method: str
    operation_id: Optional[str] = None
    action_class_name: Optional[str] = None
    intent_id: Optional[str] = None
    enabled: bool
    # Add the missing fields from the model
    summary: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    deprecated: Optional[bool] = False
    openapi_snippet: Optional[Dict[str, object]] = None
    rate_limits: List[APIRateLimitRead] = []

    class Config:
        orm_mode = True


class APIAuthConfigRead(BaseModel):
    auth_type: AuthType
    # credentials are intentionally omitted for security
    scopes: Optional[List[str]] = None
    expires_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class APIIntegrationRead(BaseModel):
    id: uuid.UUID
    tenant_id: uuid.UUID
    name: str
    version: Optional[str] = None
    openapi_spec: Dict[str, object] # May need further refinement depending on usage
    base_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    status: IntegrationStatus

    class Config:
        orm_mode = True


# Extended schema for the GET /admin/integrations/{id} endpoint
class APIIntegrationExtendedRead(APIIntegrationRead):
    auth_config: Optional[APIAuthConfigRead] = None
    endpoints: List[APIEndpointRead] = []
    # Add api_type field which is used in the frontend but not in the model
    api_type: Optional[str] = "rest"  # Default to "rest" for backward compatibility
    description: Optional[str] = None  # Add description field for frontend display

    class Config:
        orm_mode = True