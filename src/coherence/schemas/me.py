from typing import Dict, Optional, Set

from pydantic import BaseModel, Field

from src.coherence.schemas.tenant import TenantRead


class OrgDetails(BaseModel):
    id: Optional[str] = Field(None, description="Organization ID from Clerk")
    name: Optional[str] = Field(None, description="Organization name from Clerk JWT")
    slug: Optional[str] = Field(None, description="Organization slug from Clerk JWT")
    role: Optional[str] = Field(None, description="User's role in the organization from Clerk JWT")
    public_metadata: Optional[Dict] = Field({}, description="Organization public metadata from Clerk JWT")


class UserSession(BaseModel):
    org: OrgDetails = Field(..., description="Organization details from Clerk JWT")
    tenant: Optional[TenantRead] = Field(None, description="Corresponding Coherence Tenant record")
    permissions: Set[str] = Field(..., description="Set of Coherence permissions for the user")