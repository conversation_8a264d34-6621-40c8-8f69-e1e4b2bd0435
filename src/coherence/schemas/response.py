"""
Response schemas for the Coherence API.
"""

from typing import Any, Dict, Generic, List, Literal, Optional, TypeVar, Union

from pydantic import UUID4, BaseModel, Field

from .mixins import TenantMixin

# Generic type for success responses
T = TypeVar('T')


class BaseResponse(TenantMixin, BaseModel):
    """Base class for all response types."""

    kind: str = Field(..., description="Response type identifier")
    conversation_id: Optional[UUID4] = Field(None, description="Conversation identifier for multi-turn interactions")


class SuccessResponse(BaseModel, Generic[T]):
    """Generic success response wrapper."""
    
    success: bool = Field(True, description="Indicates successful response")
    data: T = Field(..., description="Response data")
    message: Optional[str] = Field(None, description="Optional success message")


class ReplyResponse(BaseResponse):
    """
    Response when an intent has been successfully processed.

    This indicates that the system understood the intent, collected
    all necessary parameters, and executed the action successfully.
    """

    kind: Literal["reply"] = "reply"
    text: str = Field(..., description="Human-readable response to display to the user")
    intent: Optional[str] = Field(
        None, description="The resolved intent (if available)"
    )
    data: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional structured data related to the intent execution",
    )


class AskResponse(BaseResponse):
    """
    Response when more information is needed to complete an intent.

    This occurs when the system has identified an intent but needs
    additional parameters to execute it successfully.
    """

    kind: Literal["ask"] = "ask"
    field: str = Field(..., description="The name of the field that needs to be filled")
    question: str = Field(..., description="Human-readable question to ask the user")
    options: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="Optional list of possible values for the field",
    )


class ClarificationResponse(BaseResponse):
    """
    Response when the intent is ambiguous and needs clarification.

    This occurs when multiple possible intents match the user's message,
    and the system needs the user to clarify which one they meant.
    """

    kind: Literal["intent_clarification"] = "intent_clarification"
    options: List[Dict[str, str]] = Field(
        default_factory=list,
        description="List of possible intents with their descriptions",
    )
    question: str = Field(
        ...,
        description="Human-readable question to ask the user",
    )


class AsyncResponse(BaseResponse):
    """
    Response when an intent will be processed asynchronously.

    This occurs for long-running operations where the result will be
    available later through a status endpoint.
    """

    kind: Literal["async"] = "async"
    workflow_id: UUID4 = Field(..., description="Identifier for the async workflow")
    status_url: str = Field(..., description="URL to check workflow status")
    estimated_time: Optional[int] = Field(
        None,
        description="Estimated time to completion in seconds (if available)",
    )


class FallbackResponse(BaseResponse):
    """
    Response when the system couldn't understand the user's message.

    This occurs when no intent could be matched with sufficient confidence.
    """

    kind: Literal["fallback"] = "fallback"
    text: Optional[str] = Field(
        "I'm not sure I understand. Could you rephrase that?",
        description="Human-readable fallback message",
    )
    reason: Optional[str] = Field(
        None,
        description="Technical reason for the fallback (for debugging)",
    )


# Union type for all possible responses
ResolveResponse = Union[
    ReplyResponse,
    AskResponse,
    ClarificationResponse,
    AsyncResponse,
    FallbackResponse,
]
