"""
Parameter extraction component for the intent pipeline.

This module handles:
- Extracting parameters from natural language messages
- Validating parameter values against defined schemas
- Generating conversational prompts for missing parameters
"""

import json
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import structlog

from src.coherence.core.llm.base import <PERSON><PERSON>rovider
from src.coherence.intent_pipeline.schemas.intent import (
    IntentDefinition,
    ParameterDefinition,
    ParameterType,
)

logger = structlog.get_logger(__name__)


class ParameterExtractor:
    """
    Service to extract parameters from natural language.

    Supports pattern-based extraction for common types and
    LLM-based extraction for complex parameters.

    Attributes:
        llm_provider: LLM provider for complex parameter extraction
        max_completion_rounds: Maximum number of rounds to collect parameters
    """

    def __init__(
        self,
        llm_provider: LLMProvider,
        max_completion_rounds: int = 3,
    ):
        """Initialize the parameter extractor.

        Args:
            llm_provider: LLM provider for complex parameter extraction
            max_completion_rounds: Maximum parameter collection rounds
        """
        self.llm_provider = llm_provider
        self.max_completion_rounds = max_completion_rounds

        # Common regex patterns for parameter extraction
        self._patterns = {
            ParameterType.NUMBER: r"(\d+(\.\d+)?)",
            ParameterType.DATE: r"(\d{4}-\d{2}-\d{2}|\d{1,2}/\d{1,2}/\d{2,4})",
            ParameterType.TIME: r"(\d{1,2}:\d{2}(:\d{2})?(\s*[AP]M)?)",
            ParameterType.EMAIL: r"([a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+)",
            ParameterType.PHONE: r"(\+?\d{1,3}[-.\s]?\(?\d{1,3}\)?[-.\s]?\d{1,4}[-.\s]?\d{1,4})",
            ParameterType.URL: r"(https?://[^\s]+)",
            ParameterType.CURRENCY: r"([\$\£\€]\s*\d+(\.\d{2})?|\d+(\.\d{2})?\s*[\$\£\€])",
        }

    async def extract_parameters(
        self,
        message: str,
        intent_definition: IntentDefinition,
        existing_params: Optional[Dict[str, Any]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
    ) -> Tuple[Dict[str, Any], List[str]]:
        """Extract parameters from a user message using a conversational approach.

        Uses LLM-first extraction for natural parameter collection,
        falling back to patterns only when needed.

        Args:
            message: User's natural language message
            intent_definition: Definition of the intent
            existing_params: Parameters already collected in previous turns
            conversation_history: Previous conversation for context

        Returns:
            Tuple of (filled_parameters, missing_parameters)
        """
        filled_params = existing_params.copy() if existing_params else {}

        # First, try LLM-based extraction for all parameters
        # This allows better handling of natural language variations
        all_params = intent_definition.parameters
        
        # Only extract parameters we haven't already filled
        params_to_extract = {
            name: param
            for name, param in all_params.items()
            if name not in filled_params
        }
        
        if params_to_extract:
            llm_params = await self._extract_with_llm_conversational(
                message=message,
                params=params_to_extract,
                intent_description=intent_definition.description,
                conversation_history=conversation_history,
                existing_params=filled_params
            )

            # Validate and store LLM-extracted parameters
            for param_name, param_value in llm_params.items():
                if param_name in intent_definition.parameters:
                    param_def = intent_definition.parameters[param_name]
                    try:
                        validated_value = self._validate_parameter(param_value, param_def)
                        filled_params[param_name] = validated_value
                        logger.info(
                            "Extracted parameter with conversational LLM",
                            param_name=param_name,
                            param_type=param_def.type,
                            value=validated_value
                        )
                    except ValueError as e:
                        logger.warning(
                            "LLM parameter validation failed",
                            param_name=param_name,
                            error=str(e),
                        )

        # For any remaining REQUIRED parameters that LLM missed,
        # try pattern-based extraction as fallback
        remaining_params = {
            name: param
            for name, param in intent_definition.parameters.items()
            if name not in filled_params and param.required
        }

        for param_name, param_def in remaining_params.items():
            param_value = self._extract_with_pattern(message, param_def)
            
            if param_value is not None:
                try:
                    validated_value = self._validate_parameter(param_value, param_def)
                    filled_params[param_name] = validated_value
                    logger.info(
                        "Extracted parameter with pattern fallback",
                        param_name=param_name,
                        param_type=param_def.type,
                    )
                except ValueError as e:
                    logger.warning(
                        "Pattern extraction validation failed",
                        param_name=param_name,
                        error=str(e),
                    )

        # Determine which required parameters are still missing
        missing_params = [
            name
            for name, param in intent_definition.parameters.items()
            if param.required and name not in filled_params
        ]

        return filled_params, missing_params

    def _extract_with_pattern(
        self,
        message: str,
        param_def: ParameterDefinition,
    ) -> Optional[Any]:
        """Extract a parameter using regex patterns.

        Args:
            message: User message
            param_def: Parameter definition

        Returns:
            Extracted value or None if not found
        """
        # Use custom regex if provided
        if param_def.validation_regex:
            pattern = param_def.validation_regex
        # Use built-in pattern for the parameter type
        elif param_def.type in self._patterns:
            pattern = self._patterns[param_def.type]
        # No pattern available for this type
        else:
            return None

        # Try to extract with the pattern
        match = re.search(pattern, message)
        if match:
            return match.group(1)

        return None

    async def _extract_with_llm(
        self,
        message: str,
        params: Dict[str, ParameterDefinition],
        intent_description: str,
    ) -> Dict[str, Any]:
        """Extract parameters using the LLM.

        Args:
            message: User message
            params: Dictionary of parameter definitions
            intent_description: Description of the intent

        Returns:
            Dictionary of extracted parameters
        """
        if not params:
            return {}

        # Build parameter schema for LLM
        param_schema = "{\n"
        for name, param in params.items():
            param_schema += f'  "{name}": {{\n'
            param_schema += f'    "type": "{param.type}",\n'
            param_schema += f'    "description": "{param.description}"\n'
            param_schema += "  },\n"
        param_schema = param_schema.rstrip(",\n") + "\n}"

        # Build system prompt
        system_prompt = """You are a parameter extraction system. 
Extract the parameters from the user's message based on the given schema.
If a parameter is not present, omit it from the response.
Return ONLY valid JSON with extracted parameters."""

        # Build user prompt
        user_prompt = f"""Intent: {intent_description}

Parameter Schema:
{param_schema}

User Message:
{message}

Extract the parameters from the user message and return ONLY a JSON object with the parameter names and values.
"""

        # Call the LLM
        response = await self.llm_provider.generate(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            max_tokens=512,
            temperature=0.0,
        )

        # Parse the JSON response
        try:
            content = response.content.strip()
            # Try to find JSON in the response if it contains extra text
            json_start = content.find("{")
            json_end = content.rfind("}")
            if json_start >= 0 and json_end > json_start:
                content = content[json_start : json_end + 1]

            extracted_params = json.loads(content)
            return extracted_params
        except json.JSONDecodeError:
            logger.error(
                "Failed to parse LLM parameter extraction response",
                response=response.content,
            )
            return {}
    
    async def _extract_with_llm_conversational(
        self,
        message: str,
        params: Dict[str, ParameterDefinition],
        intent_description: str,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        existing_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Extract parameters using a conversational LLM approach.
        
        This method handles natural language variations, ordinal references,
        and contextual parameter extraction.
        
        Args:
            message: Current user message
            params: Parameters to extract
            intent_description: Description of the intent
            conversation_history: Previous conversation context
            existing_params: Already extracted parameters
            
        Returns:
            Dictionary of extracted parameters
        """
        if not params:
            return {}
            
        # Build parameter details for the prompt
        param_details = []
        for name, param in params.items():
            detail = {
                "name": name,
                "type": str(param.type),
                "description": param.description,
                "required": param.required,
                "options": param.options if param.options else None,
                "examples": []
            }
            
            # Add example values if available
            if param.default_value is not None:
                detail["examples"].append(param.default_value)
            
            param_details.append(detail)
        
        # Build conversation context
        context_messages = []
        if conversation_history:
            for msg in conversation_history[-5:]:  # Include last 5 messages for context
                context_messages.append(f"{msg.get('role', 'user')}: {msg.get('content', '')}")
        
        # Build system prompt for conversational extraction
        system_prompt = """You are an advanced conversational parameter extraction system.
Your task is to extract parameters from natural language understanding the context and handling variations.

Key capabilities:
1. Understand ordinal references (e.g., "the second one", "option #3")
2. Handle natural language variations (e.g., "tomorrow" for dates, "5 bucks" for currency)
3. Consider conversation context for ambiguous references
4. Extract multiple parameters from a single message when possible
5. Understand implicit references based on context

Return ONLY a JSON object with extracted parameters and their confidence levels."""

        # Build the user prompt
        user_prompt = f"""Intent: {intent_description}

Current conversation context:
{chr(10).join(context_messages) if context_messages else "No previous context"}

Already collected parameters:
{json.dumps(existing_params, indent=2) if existing_params else "None"}

Parameters to extract:
{json.dumps(param_details, indent=2)}

Current message: "{message}"

Extract any parameters present in the message. For each parameter found, include:
{{
    "parameter_name": {{
        "value": extracted_value,
        "confidence": 0.0-1.0,
        "source": "explicit|inferred|contextual"
    }}
}}

Consider:
- Ordinal references (first, second, third, #1, #2, option 3)
- Natural language dates/times (tomorrow, next week, 3pm)
- Contextual references (it, that one, the same)
- Multiple parameters in one message
"""

        # Call the LLM
        response = await self.llm_provider.generate(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            max_tokens=1024,
            temperature=0.1,  # Low temperature for more consistent extraction
        )
        
        # Parse the response
        try:
            content = response.content.strip()
            # Extract JSON from response
            json_start = content.find("{")
            json_end = content.rfind("}")
            if json_start >= 0 and json_end > json_start:
                content = content[json_start : json_end + 1]
            
            extracted_data = json.loads(content)
            
            # Convert to simpler format, filtering by confidence
            extracted_params = {}
            confidence_threshold = 0.6
            
            for param_name, param_data in extracted_data.items():
                if isinstance(param_data, dict) and "value" in param_data:
                    confidence = param_data.get("confidence", 1.0)
                    if confidence >= confidence_threshold:
                        extracted_params[param_name] = param_data["value"]
                        logger.info(
                            "Extracted parameter with conversational context",
                            param_name=param_name,
                            confidence=confidence,
                            source=param_data.get("source", "unknown")
                        )
                else:
                    # Handle simple value format
                    extracted_params[param_name] = param_data
            
            return extracted_params
            
        except json.JSONDecodeError as e:
            logger.error(
                "Failed to parse conversational LLM response",
                response=response.content,
                error=str(e)
            )
            # Fallback to basic extraction
            return await self._extract_with_llm(message, params, intent_description)

    def _validate_parameter(
        self,
        value: Any,
        param_def: ParameterDefinition,
    ) -> Any:
        """Validate and convert a parameter value.

        Args:
            value: Raw parameter value
            param_def: Parameter definition

        Returns:
            Validated and converted parameter value

        Raises:
            ValueError: If validation fails
        """
        if value is None:
            if param_def.required:
                raise ValueError(f"Required parameter {param_def.name} is missing")
            return param_def.default_value

        # Convert string value to the appropriate type
        try:
            if param_def.type == ParameterType.STRING:
                return str(value)
            elif param_def.type == ParameterType.NUMBER:
                return float(value)
            elif param_def.type == ParameterType.BOOLEAN:
                if isinstance(value, bool):
                    return value
                if isinstance(value, str):
                    value = value.lower()
                    if value in ("true", "yes", "y", "1"):
                        return True
                    elif value in ("false", "no", "n", "0"):
                        return False
                raise ValueError(f"Invalid boolean value: {value}")
            elif param_def.type == ParameterType.DATE:
                # Handle common date formats
                if isinstance(value, str):
                    # Try ISO format first (YYYY-MM-DD)
                    try:
                        return datetime.strptime(value, "%Y-%m-%d").date()
                    except ValueError:
                        pass

                    # Try MM/DD/YYYY
                    try:
                        return datetime.strptime(value, "%m/%d/%Y").date()
                    except ValueError:
                        pass

                    # Try DD/MM/YYYY
                    try:
                        return datetime.strptime(value, "%d/%m/%Y").date()
                    except ValueError:
                        pass

                raise ValueError(f"Invalid date format: {value}")

            # Add more type conversions as needed

            # Default: return as is for custom types
            return value

        except (ValueError, TypeError) as e:
            raise ValueError(
                f"Parameter validation error for {param_def.name}: {str(e)}"
            ) from e

    async def generate_parameter_prompt(
        self,
        param_name: str,
        param_def: ParameterDefinition,
        intent_description: str,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        collected_params: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Generate a conversational prompt to collect a missing parameter.

        Creates natural, context-aware questions to collect parameters
        in a conversational way.

        Args:
            param_name: Name of the parameter to collect
            param_def: Parameter definition
            intent_description: Description of the current intent
            conversation_history: Previous conversation messages
            collected_params: Already collected parameters for context

        Returns:
            Human-readable question to ask the user
        """
        # Use predefined prompt if available
        if param_def.prompt:
            return param_def.prompt

        # Contextual prompts based on parameter type and situation
        if conversation_history and len(conversation_history) > 0:
            # Continuing conversation - more contextual
            if param_def.type == ParameterType.DATE:
                prompt_template = f"When would you like to {intent_description.lower()}?"
            elif param_def.type == ParameterType.TIME:
                prompt_template = f"What time works best?"
            elif param_def.type == ParameterType.NUMBER:
                prompt_template = f"How many {param_def.description.lower()} do you need?"
            elif param_def.type == ParameterType.BOOLEAN:
                prompt_template = f"Would you like to {param_def.description.lower()}?"
            else:
                prompt_template = f"What {param_def.description.lower()} would you prefer?"
                
            # Add context from collected params
            if collected_params:
                context_parts = []
                for key, value in collected_params.items():
                    if key != param_name:  # Don't reference the param we're asking about
                        context_parts.append(f"{key}: {value}")
                
                if context_parts:
                    prompt_template = f"Great! I have {', '.join(context_parts)}. Now, {prompt_template}"
        else:
            # Initial question - more descriptive
            if param_def.type == ParameterType.DATE:
                prompt_template = f"To {intent_description.lower()}, what date would you like?"
            elif param_def.type == ParameterType.TIME:
                prompt_template = f"To {intent_description.lower()}, what time would work?"
            elif param_def.type == ParameterType.NUMBER:
                prompt_template = f"To {intent_description.lower()}, how many {param_def.description.lower()} would you like?"
            else:
                prompt_template = f"To {intent_description.lower()}, I'll need the {param_def.description.lower()}."

        # Add options in a natural way
        if param_def.options:
            if len(param_def.options) <= 3:
                options_text = " or ".join([opt["label"] for opt in param_def.options][:-1])
                options_text += f" or {param_def.options[-1]['label']}"
                prompt_template += f" You can choose {options_text}."
            else:
                prompt_template += f" Here are your options: {', '.join([opt['label'] for opt in param_def.options])}."
        
        # Add examples for complex types
        if param_def.type in [ParameterType.DATE, ParameterType.TIME, ParameterType.EMAIL, ParameterType.PHONE]:
            example_text = ""
            if param_def.type == ParameterType.DATE:
                example_text = "(like 'tomorrow', 'next Monday', or '2024-03-15')"
            elif param_def.type == ParameterType.TIME:
                example_text = "(like '3pm', '15:00', or 'in an hour')"
            elif param_def.type == ParameterType.EMAIL:
                example_text = "(like '<EMAIL>')"
            elif param_def.type == ParameterType.PHONE:
                example_text = "(like '******-0123' or '555-0123')"
                
            if example_text:
                prompt_template += f" {example_text}"

        return prompt_template


async def get_parameter_extractor(
    llm_provider: LLMProvider,
    max_completion_rounds: int = 3,
) -> ParameterExtractor:
    """Factory function to create a ParameterExtractor.

    Args:
        llm_provider: LLM provider for complex parameter extraction
        max_completion_rounds: Maximum rounds for parameter collection

    Returns:
        An initialized ParameterExtractor
    """
    return ParameterExtractor(
        llm_provider=llm_provider,
        max_completion_rounds=max_completion_rounds,
    )
