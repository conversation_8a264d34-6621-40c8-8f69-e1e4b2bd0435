"""
Base action class for intent action implementations.

This module defines the base class that all action implementations must inherit from.
It ensures a consistent interface for action execution across the system.
"""

import abc
from typing import Any, Dict, Optional


class BaseAction(abc.ABC):
    """Base class for all intent actions.
    
    All action implementations should inherit from this class to ensure
    a consistent interface for action execution.
    """
    
    @abc.abstractmethod
    async def execute(self, parameters: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute the action with the provided parameters.
        
        Args:
            parameters: Parameters extracted from the intent
            context: Additional context information (user, tenant, etc.)
            
        Returns:
            Result dictionary with action outcome
        """
        pass