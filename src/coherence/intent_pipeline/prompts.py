"""
LL<PERSON> prompts for the simplified intent resolution approach.
"""

LLM_INTENT_RESOLUTION_SYSTEM_PROMPT = """You are an intelligent API assistant that helps users interact with various APIs through natural language. Your task is to understand what the user wants and select the most appropriate template from the available options."""

LLM_INTENT_RESOLUTION_USER_PROMPT = """User message: {message}

Previous conversation:
{conversation_history}

Available templates (sorted by relevance score):
{template_descriptions}

Based on the user's message and conversation context, analyze their intent and select the most appropriate template. Consider:

1. **Question words** (what, how, explain, describe, tell me about) usually indicate documentation requests
2. **Action words** (get, fetch, create, update, delete, book, schedule) usually indicate API operations
3. **Context** from the conversation history
4. **Relevance scores** from the vector search (higher is better)

Important guidelines:
- If the user is clearly asking about HOW something works or WHAT parameters are needed, prefer documentation templates
- If the user wants to actually DO something or GET data, prefer action templates
- When unclear, you can ask for clarification
- Consider that GET operations can be ambiguous - context matters

Respond with valid JSON in this exact format:
{{
    "selected_template": "template_key_here",
    "template_type": "action" or "documentation",
    "confidence": 0.0-1.0,
    "clarification_needed": true/false,
    "clarification_question": "optional question if clarification needed",
    "parameters_to_collect": ["param1", "param2"] or [],
    "reasoning": "brief explanation of your choice",
    "alternate_suggestions": ["template_key1", "template_key2"] or []
}}

Examples:
- "tell me about the icd10 api parameters" → documentation template
- "get the icd10 code for diabetes" → action template
- "what does the weather api do" → documentation template
- "check the weather in New York" → action template"""

def format_template_descriptions(templates):
    """Format template results for LLM consumption."""
    descriptions = []
    for i, template in enumerate(templates):
        desc = f"{i+1}. {template['template_key']} ({template['template_category']})"
        desc += f"\n   Score: {template['score']:.3f}"
        desc += f"\n   Description: {template['description']}"
        
        if template.get('example_usage'):
            desc += f"\n   Example: {template['example_usage']}"
            
        if template.get('parameters'):
            params = list(template['parameters'].keys())
            if params:
                desc += f"\n   Parameters: {', '.join(params)}"
                
        descriptions.append(desc)
    
    return "\n\n".join(descriptions)

def format_conversation_history(message_history, max_messages=5):
    """Format conversation history for LLM context."""
    if not message_history:
        return "No previous messages"
    
    # Take the last N messages
    recent_messages = message_history[-max_messages:]
    
    formatted = []
    for msg in recent_messages:
        role = msg.get('role', 'user')
        content = msg.get('content', '')
        formatted.append(f"{role.capitalize()}: {content}")
    
    return "\n".join(formatted)