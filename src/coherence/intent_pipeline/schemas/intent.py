"""
Intent schemas for the Coherence intent pipeline.

This module contains the core data models for intent definition,
resolution, and parameter handling.
"""

from enum import Enum
from typing import Any, Dict, List, Optional, Set

from pydantic import BaseModel, Field

from ...schemas.mixins import TenantMixin


class ParameterType(str, Enum):
    """Types of parameters that can be extracted from user messages."""

    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "boolean"
    DATE = "date"
    TIME = "time"
    DATETIME = "datetime"
    DURATION = "duration"
    CURRENCY = "currency"
    EMAIL = "email"
    PHONE = "phone"
    URL = "url"
    CUSTOM = "custom"


class ParameterDefinition(TenantMixin, BaseModel):
    """
    Definition of a parameter expected by an intent.

    Specifies validation rules, type information, and prompts for
    parameter collection.
    """

    name: str = Field(..., description="Parameter identifier")
    type: ParameterType = Field(ParameterType.STRING, description="Parameter data type")
    required: bool = Field(True, description="Whether this parameter is required")
    description: str = Field(
        ..., description="Human-readable description of the parameter"
    )
    prompt: Optional[str] = Field(
        None, description="Question to ask the user when collecting this parameter"
    )
    validation_regex: Optional[str] = Field(
        None, description="Regex pattern for validation"
    )
    validation_error: Optional[str] = Field(
        None, description="Error message for failed validation"
    )
    default_value: Optional[Any] = Field(
        None, description="Default value if not provided"
    )
    unit: Optional[str] = Field(
        None, description="Unit of measurement (for numeric values)"
    )
    options: Optional[List[Dict[str, Any]]] = Field(
        None, description="Possible values for the parameter"
    )
    custom_extraction_template: Optional[str] = Field(
        None, description="Template for custom extraction logic"
    )


class IntentDefinition(TenantMixin, BaseModel):
    """
    Definition of an intent that can be matched to user messages.

    Contains metadata about the intent, examples for vector training,
    and parameter specifications.
    """

    name: str = Field(..., description="Unique intent identifier")
    description: str = Field(
        ..., description="Human-readable description of the intent"
    )
    examples: List[str] = Field(
        default_factory=list,
        description="Example phrases to train vector matching",
    )
    parameters: Dict[str, ParameterDefinition] = Field(
        default_factory=dict,
        description="Parameters expected by this intent",
    )
    required_fields: Set[str] = Field(
        default_factory=set,
        description="Set of field names that are required",
    )
    conversation_template_id: Optional[str] = Field(
        None,
        description="Template ID for generating conversation responses",
    )
    action_class: Optional[str] = Field(
        None,
        description="Action class to execute when this intent is resolved",
    )
    is_fallback: bool = Field(
        False,
        description="Whether this is a fallback intent",
    )
    parent_intent: Optional[str] = Field(
        None,
        description="Parent intent for hierarchical intents",
    )
    group: Optional[str] = Field(
        None,
        description="Functional group this intent belongs to",
    )


class IntentResolution(TenantMixin, BaseModel):
    """
    Result of resolving a user message to an intent.

    Contains the detected intent, confidence score, and any
    parameters that have been extracted or are still missing.
    """

    intent: Optional[str] = Field(None, description="Resolved intent identifier")
    confidence: float = Field(..., description="Confidence score (0.0-1.0)")
    filled: Dict[str, Any] = Field(
        default_factory=dict,
        description="Parameter values that have been successfully extracted",
    )
    missing: List[str] = Field(
        default_factory=list,
        description="Parameter names that are still missing",
    )
    alternatives: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="Alternative intents that could also match",
    )
    partial_match: bool = Field(
        False,
        description="Whether this is a partial match that needs clarification",
    )


class ConversationContext(TenantMixin, BaseModel):
    """
    Context for a multi-turn conversation.

    Tracks the state of an ongoing conversation, including intent,
    parameter collection status, and conversation history.
    """

    conversation_id: str = Field(
        ..., description="Unique identifier for the conversation"
    )
    tenant_id: str = Field(..., description="Tenant ID for multi-tenant isolation")
    user_id: str = Field(..., description="User ID for personalization")
    user_role: str = Field(
        ..., description="User role for context-appropriate responses"
    )
    current_intent: Optional[str] = Field(None, description="Currently active intent")
    parameters: Dict[str, Any] = Field(
        default_factory=dict,
        description="Parameters collected so far",
    )
    missing_parameters: List[str] = Field(
        default_factory=list,
        description="Parameters still needed for the current intent",
    )
    intent_confirmation_state: Optional[str] = Field(
        None,
        description="Intent confirmation state (confirmed, clarifying, etc.)",
    )
    last_question: Optional[str] = Field(
        None,
        description="Last question asked to the user",
    )
    message_history: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="History of the conversation messages",
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata for the conversation",
    )
