"""
Intent resolver component that maps natural language to intents.

This module contains:
- The Vector Intent Matcher (Tier 1) for fast intent recognition
- The LLM Intent Router (Tier 2) for fallback intent recognition 
- The Intent Resolution pipeline combining both approaches
"""

import json
import time
from typing import Dict, List, Optional, Tuple

import structlog
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.core.llm.base import LLMProvider
from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.metrics import INTENT_CONFIDENCE, INTENT_RESOLUTION_LATENCY
from src.coherence.core.qdrant_client import QdrantClient
from src.coherence.intent_pipeline.schemas.intent import (
    IntentDefinition,
    IntentResolution,
)
from enum import Enum

logger = structlog.get_logger(__name__)


class TemplateClassification(str, Enum):
    """Classification of user intent - action or documentation request."""
    ACTION = "action"
    DOCUMENTATION = "documentation"
    UNCLEAR = "unclear"


class IntentResolverError(Exception):
    """Exception raised when the intent resolver encounters an error."""

    pass


class IntentResolver:
    """Service for resolving user intents from natural language messages.

    This class implements the tiered intent recognition approach:
    1. First attempts vector matching (Tier 1) for fast responses
    2. Falls back to LLM-based routing (Tier 2) if vector matching confidence is low
    3. Optionally uses RAG augmentation (Tier 3) for complex or novel intents

    Attributes:
        qdrant_client: The Qdrant client for vector search
        llm_provider: The LLM provider for Tier 2 and 3 processing
        embedding_provider: Provider for generating embeddings
        tier1_threshold: Confidence threshold for Tier 1 success
        tier2_threshold: Confidence threshold for Tier 2 success
    """

    def __init__(
        self,
        qdrant_client: QdrantClient,
        llm_provider: LLMProvider,
        tier1_threshold: float = 0.6,  # Lower threshold for better semantic matching
        tier2_threshold: float = 0.75,
    ):
        """Initialize the IntentResolver with dependencies.

        Args:
            qdrant_client: The Qdrant client for vector search
            llm_provider: The LLM provider for Tier 2 and 3 processing
            tier1_threshold: Confidence threshold for Tier 1 success (default: 0.85)
            tier2_threshold: Confidence threshold for Tier 2 success (default: 0.75)
        """
        self.qdrant_client = qdrant_client
        self.llm_provider = llm_provider
        self.tier1_threshold = tier1_threshold
        self.tier2_threshold = tier2_threshold
        # Keywords that suggest documentation request
        self.doc_keywords = ["how", "what", "explain", "help", "docs", "documentation", 
                           "parameters", "format", "example", "usage", "guide"]

    async def _generate_embedding(self, message: str) -> List[float]:
        """Generate embedding for a message using the embedding provider."""
        # This would use a dedicated embedding service in a real implementation
        # For now, we'll use the LLM provider's embedding capability
        from src.coherence.core.config import settings
        
        logger.info(
            "Generating embedding for intent resolution",
            message_length=len(message),
            embedding_model=settings.EMBEDDING_MODEL,
            embedding_dimension=settings.EMBEDDING_DIMENSION
        )
        
        try:
            embedding = await self.llm_provider.generate_embedding(
                text=message,
                dimensions=settings.EMBEDDING_DIMENSION
            )
            
            # Log the actual dimension received
            logger.info(
                "Embedding generated successfully",
                embedding_dimension_actual=len(embedding),
                embedding_dimension_expected=settings.EMBEDDING_DIMENSION
            )
            
            return embedding
        except Exception as e:
            logger.error(
                "Error generating embedding",
                error=str(e),
                error_type=type(e).__name__,
                embedding_model=settings.EMBEDDING_MODEL,
                embedding_dimension=settings.EMBEDDING_DIMENSION
            )
            raise

    async def _resolve_tier1(
        self,
        tenant_id: str,
        user_role: str,
        message: str,
        embedding: List[float],
    ) -> Tuple[Optional[IntentResolution], float]:
        """Resolve intent using vector matching (Tier 1).

        Args:
            tenant_id: ID of the tenant
            user_role: Role of the user (e.g., "admin", "user")
            message: The natural language message
            embedding: Pre-generated embedding vector

        Returns:
            Tuple of (IntentResolution or None, confidence score)
            If confidence is below threshold, returns (None, confidence)
        """
        start_time = time.time()
        collection_name = f"intent_idx_{tenant_id}_{user_role}"

        try:
            # Search vectors in the tenant-specific collection
            results = await self.qdrant_client.search(
                collection_name=collection_name,
                query_vector=embedding,
                limit=5,
            )

            if not results:
                # Try fallback to default collection if tenant-specific is empty
                collection_name = f"intent_idx_default_{user_role}"
                results = await self.qdrant_client.search(
                    collection_name=collection_name,
                    query_vector=embedding,
                    limit=5,
                )

            if not results:
                # No results found in either collection
                latency_ms = int((time.time() - start_time) * 1000)
                logger.info(
                    "No vector matches found for message",
                    tier="vector",
                    tenant_id=tenant_id,
                    user_role=user_role,
                    latency_ms=latency_ms,
                )
                INTENT_RESOLUTION_LATENCY.labels(
                    tier="vector", result="no_match"
                ).observe(latency_ms / 1000)
                return None, 0.0

            # Get the top result
            top_result = results[0]
            # Access score directly from dictionary
            confidence = float(top_result.get("score", 0.0))

            # Record latency and confidence metrics
            latency_ms = int((time.time() - start_time) * 1000)
            INTENT_RESOLUTION_LATENCY.labels(
                tier="vector",
                result="success"
                if confidence >= self.tier1_threshold
                else "low_confidence",
            ).observe(latency_ms / 1000)
            INTENT_CONFIDENCE.labels(tier="vector").observe(confidence)

            # Check if confidence meets threshold
            if confidence < self.tier1_threshold:
                logger.info(
                    "Vector match below threshold",
                    tier="vector",
                    tenant_id=tenant_id,
                    confidence=confidence,
                    threshold=self.tier1_threshold,
                    latency_ms=latency_ms,
                )
                return None, confidence

            # Extract intent and parameters directly from the result dictionary
            # Note: payload is already merged into the result dictionary
            intent_name = top_result.get("intent")

            # Create the intent resolution result
            # Note: We don't want to pass the parameter definitions as filled parameters
            # The filled parameters should be actual extracted values, not definitions
            resolution = IntentResolution(
                intent=intent_name,
                confidence=confidence,
                filled={},  # Start with empty filled params - these will be extracted by the parameter extractor
                missing=[],  # Will be determined by parameter completion service
                alternatives=None,  # No alternatives in Tier 1
                partial_match=False,  # Not a partial match
            )

            logger.info(
                "Intent resolved via vector matching",
                tier="vector",
                tenant_id=tenant_id,
                user_role=user_role,
                intent=intent_name,
                confidence=confidence,
                latency_ms=latency_ms,
            )

            return resolution, confidence

        except Exception as e:
            latency_ms = int((time.time() - start_time) * 1000)
            logger.error(
                "Error in Tier 1 intent resolution",
                tier="vector",
                tenant_id=tenant_id,
                error=str(e),
                error_type=type(e).__name__,
                latency_ms=latency_ms,
            )
            INTENT_RESOLUTION_LATENCY.labels(tier="vector", result="error").observe(
                latency_ms / 1000
            )
            return None, 0.0

    async def _resolve_tier2(
        self,
        user_id: str,
        user_role: str,
        message: str,
        intents: List[IntentDefinition],
        tier1_confidence: float,
    ) -> Tuple[Optional[IntentResolution], float]:
        """Resolve intent using local LLM (Tier 2).

        Args:
            user_id: ID of the user
            user_role: Role of the user
            message: The natural language message
            intents: List of available intent definitions
            tier1_confidence: Confidence from Tier 1 attempt

        Returns:
            Tuple of (IntentResolution or None, confidence score)
        """
        start_time = time.time()

        try:
            # Build the intent catalog in YAML format
            intent_catalog = "intents:\n"
            for intent in intents:
                intent_catalog += f"- name: {intent.name}\n"
                intent_catalog += f"  description: {intent.description}\n"
                if intent.required_fields:
                    fields_str = ", ".join(intent.required_fields)
                    intent_catalog += f"  required_fields: [{fields_str}]\n"

            # Build the user prompt
            user_prompt = f"""# YAML below shows INTENT CATALOG available to the user
{intent_catalog}
---
message: |
  {message}"""

            # Build the system prompt
            system_prompt = """You are a routing engine that maps a user's chat message to an intent schema.

Always respond with valid JSON in the following format:
{
  "intent": "intent_name or null",
  "confidence": 0.0-1.0,
  "filled": {"param1": "value1"},
  "missing": ["param2"],
  "alternatives": null,
  "partial_match": false
}

If no intent matches, return {"intent": null, "confidence": 0.0, "filled": {}, "missing": [], "alternatives": null, "partial_match": false}"""

            # Call the LLM API
            response = await self.llm_provider.generate(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=256,
                temperature=0.0,
                user=user_id,
            )

            # Extract the response content
            content = response.content.strip()
            
            # Clean up the response if it contains markdown code fences
            if content.startswith("```"):
                # Remove opening fence
                content = content.split("\n", 1)[1] if "\n" in content else content[3:]
                # Remove closing fence
                if content.endswith("```"):
                    content = content[:-3].strip()

            # Parse the JSON response
            try:
                result = json.loads(content)

                # Create the IntentResolution
                resolution = IntentResolution(
                    intent=result.get("intent"),
                    confidence=result.get("confidence", 0.0),
                    filled=result.get("filled", {}),
                    missing=result.get("missing", []),
                    alternatives=result.get("alternatives"),
                    partial_match=result.get("partial_match", False),
                )

                # Record latency and confidence metrics
                latency_ms = int((time.time() - start_time) * 1000)
                confidence = resolution.confidence

                INTENT_RESOLUTION_LATENCY.labels(
                    tier="llm",
                    result="success"
                    if confidence >= self.tier2_threshold
                    else "low_confidence",
                ).observe(latency_ms / 1000)
                INTENT_CONFIDENCE.labels(tier="llm").observe(confidence)

                # Log the resolution
                logger.info(
                    "Intent resolution via LLM completed",
                    tier="llm",
                    user_id=user_id,
                    user_role=user_role,
                    intent=resolution.intent,
                    confidence=confidence,
                    tier1_confidence=tier1_confidence,
                    latency_ms=latency_ms,
                )

                return resolution, confidence

            except json.JSONDecodeError as e:
                raise IntentResolverError(
                    f"Failed to parse JSON response: {e}. Response: {content}"
                ) from e

        except Exception as e:
            # Log the error and re-raise
            latency_ms = int((time.time() - start_time) * 1000)
            logger.error(
                "Intent resolution failed in Tier 2",
                tier="llm",
                user_id=user_id,
                user_role=user_role,
                error=str(e),
                error_type=type(e).__name__,
                latency_ms=latency_ms,
            )
            INTENT_RESOLUTION_LATENCY.labels(tier="llm", result="error").observe(
                latency_ms / 1000
            )
            return None, 0.0

    async def resolve(
        self,
        *,
        tenant_id: str,
        user_id: str,
        user_role: str,
        message: str,
        intents: List[IntentDefinition],
    ) -> IntentResolution:
        """Resolve a user message to an intent using the tiered approach.

        Args:
            tenant_id: The ID of the tenant
            user_id: The ID of the user sending the message
            user_role: The role of the user (e.g., "admin", "user")
            message: The natural language message from the user
            intents: List of available intent definitions to match against

        Returns:
            An IntentResolution object containing the matched intent and parameters

        Raises:
            IntentResolverError: If there is an error in the resolution process
        """
        time.time()
        log = logger.bind(
            tenant_id=tenant_id,
            user_id=user_id,
            user_role=user_role,
            message_length=len(message),
        )

        try:
            # Generate embedding for the message
            embedding = await self._generate_embedding(message)

            # Tier 1: Vector matching for fast intent recognition
            tier1_result, tier1_confidence = await self._resolve_tier1(
                tenant_id=tenant_id,
                user_role=user_role,
                message=message,
                embedding=embedding,
            )

            # If Tier 1 successful with high confidence, return the result
            if tier1_result and tier1_confidence >= self.tier1_threshold:
                log.info(
                    "Intent resolved in Tier 1",
                    tier="vector",
                    intent=tier1_result.intent,
                    confidence=tier1_confidence,
                )
                return tier1_result

            # Tier 2: Local LLM for more complex intent recognition
            tier2_result, tier2_confidence = await self._resolve_tier2(
                user_id=user_id,
                user_role=user_role,
                message=message,
                intents=intents,
                tier1_confidence=tier1_confidence,
            )

            # If Tier 2 successful with high confidence, return the result
            if tier2_result and tier2_confidence >= self.tier2_threshold:
                log.info(
                    "Intent resolved in Tier 2",
                    tier="llm",
                    intent=tier2_result.intent,
                    confidence=tier2_confidence,
                    tier1_confidence=tier1_confidence,
                )
                return tier2_result

            # If we have a Tier 1 result but low confidence, and Tier 2 failed completely,
            # return the Tier 1 result anyway as a fallback
            if tier1_result and not tier2_result:
                log.info(
                    "Using low-confidence Tier 1 result as fallback",
                    tier="vector",
                    intent=tier1_result.intent,
                    confidence=tier1_confidence,
                )
                return tier1_result

            # If we have a Tier 2 result but low confidence, return it as a fallback
            if tier2_result:
                log.info(
                    "Using low-confidence Tier 2 result as fallback",
                    tier="llm",
                    intent=tier2_result.intent,
                    confidence=tier2_confidence,
                    tier1_confidence=tier1_confidence,
                )
                return tier2_result

            # If both tiers failed, return a null intent resolution
            log.warning(
                "Intent resolution failed in all tiers",
                tier1_confidence=tier1_confidence,
                tier2_confidence=tier2_confidence,
            )

            return IntentResolution(
                intent=None,
                confidence=0.0,
                filled={},
                missing=[],
                alternatives=None,
                partial_match=False,
            )

        except Exception as e:
            # Log the error and re-raise
            log.exception(
                "Unhandled exception in intent resolution",
                error=str(e),
                error_type=type(e).__name__,
            )
            raise IntentResolverError(f"Intent resolution failed: {e}") from e
    
    async def _classify_intent(
        self,
        message: str,
        search_results: List[Dict]
    ) -> TemplateClassification:
        """Classify user intent as action or documentation based on message and search results.
        
        Args:
            message: User's message
            search_results: Vector search results containing both intent and doc templates
            
        Returns:
            Classification of the intent
        """
        message_lower = message.lower()
        
        # Check for documentation keywords in message
        has_doc_keywords = any(keyword in message_lower for keyword in self.doc_keywords)
        
        # Separate results by template type
        intent_results = []
        doc_results = []
        
        for result in search_results:
            template_category = result.get("payload", {}).get("template_category", "")
            if template_category == "documentation":
                doc_results.append(result)
            elif template_category in ["intent_router", "action"]:
                intent_results.append(result)
        
        # If we have high confidence documentation results and doc keywords
        if doc_results and has_doc_keywords:
            top_doc_score = doc_results[0].get("score", 0)
            if top_doc_score > 0.7:
                return TemplateClassification.DOCUMENTATION
        
        # If we have high confidence action results without doc keywords
        if intent_results and not has_doc_keywords:
            top_intent_score = intent_results[0].get("score", 0)
            if top_intent_score > 0.7:
                return TemplateClassification.ACTION
        
        # If scores are close, use keywords as tiebreaker
        if doc_results and intent_results:
            doc_score = doc_results[0].get("score", 0)
            intent_score = intent_results[0].get("score", 0)
            
            if abs(doc_score - intent_score) < 0.1:  # Scores are close
                return TemplateClassification.DOCUMENTATION if has_doc_keywords else TemplateClassification.ACTION
        
        # Default to action if unclear
        return TemplateClassification.ACTION if intent_results else TemplateClassification.UNCLEAR
    
    async def resolve_with_classification(
        self,
        *,
        tenant_id: str,
        user_id: str,  
        user_role: str,
        message: str,
        intents: List[IntentDefinition],
    ) -> Tuple[IntentResolution, TemplateClassification]:
        """Resolve intent with classification of template type.
        
        This enhanced method returns both the intent resolution and classification
        of whether the user wants to execute an action or get documentation.
        
        Args:
            tenant_id: The ID of the tenant
            user_id: The ID of the user
            user_role: The role of the user
            message: The natural language message
            intents: Available intent definitions
            
        Returns:
            Tuple of (IntentResolution, TemplateClassification)
        """
        # Search across all template types
        embedding = await self._generate_embedding(message)
        
        # Search in template collection (includes both intent and doc templates)
        collection_name = f"template_idx_{tenant_id}"
        
        try:
            results = await self.qdrant_client.search(
                collection_name=collection_name,
                query_vector=embedding,
                limit=10,  # Get more results to see both types
            )
            
            # Classify the intent
            classification = await self._classify_intent(message, results)
            
            # Filter results based on classification
            if classification == TemplateClassification.DOCUMENTATION:
                # Return documentation template
                doc_results = [r for r in results if r.get("payload", {}).get("template_category") == "documentation"]
                
                if doc_results:
                    top_doc = doc_results[0]
                    return IntentResolution(
                        intent="show_documentation",
                        confidence=top_doc.get("score", 0),
                        filled={"template_key": top_doc.get("payload", {}).get("template_key")},
                        missing=[],
                        metadata={"template_type": "documentation"}
                    ), classification
            
            # Otherwise, use standard intent resolution
            resolution = await self.resolve(
                tenant_id=tenant_id,
                user_id=user_id,
                user_role=user_role,
                message=message,
                intents=intents
            )
            
            return resolution, classification
            
        except Exception as e:
            logger.error(f"Error in enhanced resolution: {e}")
            # Fallback to standard resolution
            resolution = await self.resolve(
                tenant_id=tenant_id,
                user_id=user_id, 
                user_role=user_role,
                message=message,
                intents=intents
            )
            return resolution, TemplateClassification.UNCLEAR
    
    async def simple_vector_search(
        self,
        tenant_id: str,
        message: str,
        limit: int = 10
    ) -> List[Dict]:
        """
        Simple vector search that returns all relevant templates without classification.
        
        This method searches across all template types (action, documentation, etc.)
        and returns the raw results for LLM processing.
        
        Args:
            tenant_id: Tenant ID for isolation
            message: User's message
            limit: Number of results to return
            
        Returns:
            List of templates with scores and metadata
        """
        # Generate embedding for the message
        embedding = await self._generate_embedding(message)
        
        # Search across all template types in the template collection
        collection_name = f"template_idx_{tenant_id}"
        
        try:
            # Primary search in tenant-specific template collection
            results = await self.qdrant_client.search(
                collection_name=collection_name,
                query_vector=embedding,
                limit=limit,
            )
            
            # If no results, try the intent collection as fallback
            if not results:
                collection_name = f"intent_idx_{tenant_id}_user"
                results = await self.qdrant_client.search(
                    collection_name=collection_name,
                    query_vector=embedding,
                    limit=limit,
                )
            
            # Format results for LLM consumption
            formatted_results = []
            for result in results:
                payload = result.get("payload", {})
                formatted_results.append({
                    "score": result.get("score", 0),
                    "template_key": payload.get("template_key") or payload.get("key"),
                    "template_category": payload.get("template_category", "unknown"),
                    "description": payload.get("description", ""),
                    "intent": payload.get("intent", ""),
                    "parameters": payload.get("parameters", {}),
                    "example_usage": payload.get("example_usage", ""),
                    "response_format": payload.get("response_format", None),
                    "actions": payload.get("actions", []),
                })
            
            logger.info(
                "Simple vector search completed",
                tenant_id=tenant_id,
                message_length=len(message),
                results_count=len(formatted_results),
                top_score=formatted_results[0]["score"] if formatted_results else 0,
            )
            
            return formatted_results
            
        except Exception as e:
            logger.error(
                "Error in simple vector search",
                tenant_id=tenant_id,
                error=str(e),
                error_type=type(e).__name__,
            )
            return []


async def get_intent_resolver(
    db: AsyncSession,
    qdrant_client: QdrantClient,
    llm_factory: LLMFactory,
) -> IntentResolver:
    """Factory function to create an IntentResolver instance.

    Args:
        db: Database session
        qdrant_client: Qdrant client
        llm_factory: Factory for creating LLM providers

    Returns:
        An instance of IntentResolver with its dependencies
    """
    # Use OpenAI as the LLM provider for Tier 2
    from src.coherence.core.config import settings

    llm_provider = llm_factory.create_provider(
        name=settings.LLM_PROVIDER,
        model=settings.LLM_MODEL,
    )

    # Create the intent resolver
    return IntentResolver(
        qdrant_client=qdrant_client,
        llm_provider=llm_provider,
    )
