"""
Integration between the template system and intent pipeline.

This module provides functionality to use templates for intent resolution
in the Tier 2 (LLM-based) intent recognition process.
"""

import json
import logging
from typing import List, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.core.llm.base import <PERSON><PERSON>rovider
from src.coherence.intent_pipeline.schemas.intent import (
    IntentDefinition,
    IntentResolution,
)
from src.coherence.template_system.services.template_service import TemplateService

logger = logging.getLogger(__name__)


class TemplateBasedIntentResolver:
    """
    Intent resolver that uses templates for LLM-based intent resolution (Tier 2).

    This class integrates the template system with the intent resolution
    pipeline, using templates for generating LLM prompts.

    Attributes:
        template_service: Service for managing and rendering templates
        llm_provider: Provider for LLM-based processing
    """

    def __init__(
        self,
        template_service: TemplateService,
        llm_provider: LLMProvider,
    ):
        """
        Initialize the template-based intent resolver.

        Args:
            template_service: Service for managing and rendering templates
            llm_provider: Provider for LLM-based processing
        """
        self.template_service = template_service
        self.llm_provider = llm_provider

    async def resolve_intent_with_template(
        self,
        db: AsyncSession,
        user_id: str,
        user_role: str,
        message: str,
        intents: List[IntentDefinition],
        tenant_id: Optional[str] = None,
        template_key: str = "INTENT_ROUTER_V1",
    ) -> Tuple[Optional[IntentResolution], float]:
        """
        Resolve intent using a template and LLM.

        Args:
            db: Database session
            user_id: ID of the user
            user_role: Role of the user
            message: The natural language message
            intents: List of available intent definitions
            tenant_id: ID of the tenant (optional)
            template_key: Key of the template to use

        Returns:
            Tuple of (IntentResolution or None, confidence score)
        """
        try:
            # Prepare context for template rendering
            context = {
                "user_message": message,
                "available_intents": [
                    {
                        "id": intent.name,
                        "description": intent.description,
                        "examples": intent.examples
                        if hasattr(intent, "examples")
                        else [],
                    }
                    for intent in intents
                ],
            }

            # Render the template
            rendered_template = await self.template_service.render_template(
                db=db,
                key=template_key,
                category="intent_router",
                context=context,
                tenant_id=tenant_id,
            )

            # Parse the template into system instructions and user prompt
            system_instruction, user_prompt = self._parse_template(rendered_template)

            # Call the LLM API
            response = await self.llm_provider.generate(
                messages=[
                    {"role": "system", "content": system_instruction},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=256,
                temperature=0.0,
                user=user_id,
            )

            # Extract the response content
            content = response.content.strip()

            # Parse the JSON response
            try:
                result = json.loads(content)

                # Create the IntentResolution
                resolution = IntentResolution(
                    intent=result.get("intent"),
                    confidence=result.get("confidence", 0.0),
                    filled=result.get("parameters", {}),
                    missing=[],  # Will be determined by parameter completion service
                )

                return resolution, resolution.confidence

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}. Response: {content}")
                return None, 0.0

        except Exception as e:
            # Log the error
            logger.error(
                "Error in template-based intent resolution",
                error=str(e),
                error_type=type(e).__name__,
            )
            return None, 0.0

    def _parse_template(self, rendered_template: str) -> Tuple[str, str]:
        """
        Parse a rendered template into system instructions and user prompt.

        Args:
            rendered_template: The rendered template

        Returns:
            Tuple of (system_instruction, user_prompt)
        """
        # Simple parsing based on template structure
        # In a production system, this would be more robust
        lines = rendered_template.split("\n")
        in_system_block = False
        in_prompt_block = False

        system_instruction = []
        user_prompt = []

        for line in lines:
            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith("{#"):
                continue

            # Check for block markers
            if "{% block system_instruction %}" in line:
                in_system_block = True
                continue

            if "{% endblock %}" in line and in_system_block:
                in_system_block = False
                continue

            if "{% block prompt %}" in line:
                in_prompt_block = True
                continue

            if "{% endblock %}" in line and in_prompt_block:
                in_prompt_block = False
                continue

            # Add lines to appropriate block
            if in_system_block:
                system_instruction.append(line)
            elif in_prompt_block:
                user_prompt.append(line)

        return "\n".join(system_instruction), "\n".join(user_prompt)


async def create_template_intent_resolver(
    db: AsyncSession,
    llm_provider: LLMProvider,
) -> TemplateBasedIntentResolver:
    """
    Factory function to create a template-based intent resolver.

    Args:
        db: Database session
        llm_provider: LLM provider for text generation

    Returns:
        TemplateBasedIntentResolver instance
    """
    template_service = TemplateService()

    return TemplateBasedIntentResolver(
        template_service=template_service,
        llm_provider=llm_provider,
    )
