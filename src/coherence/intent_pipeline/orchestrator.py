"""
Orchestrator for the intent resolution and API action pipeline.

This module coordinates the entire intent resolution and API execution flow:
1. Intent recognition via the tiered approach (vector → LLM → RAG)
2. Parameter extraction and validation
3. Dynamic API action execution based on template configurations
4. Response transformation and rendering using templates
5. Error handling and fallback management
6. Conversation context management for multi-turn interactions

The system supports both synchronous and asynchronous execution modes:
- Synchronous: Immediate execution and response
- Asynchronous: Creating workflow records and dispatching background tasks

Key components:
- ChatOrchestrator: Central coordinator for the entire pipeline
- DynamicActionExecutor: Executes API calls based on template configuration
- TemplateService: Manages templates for rendering user responses
- IntentResolver: Determines user intent using the tiered approach
- ParameterExtractor: Extracts and validates parameters from user messages
"""

import json
import time
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog
from fastapi import Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.core.config import settings
from src.coherence.core.llm.base import LL<PERSON>rovider
from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.qdrant_client import get_qdrant_client
from src.coherence.core.redis_client import RedisClient
from src.coherence.db.deps import get_db
from src.coherence.intent_pipeline.parameter_extraction import (
    ParameterExtractor,
    get_parameter_extractor,
)
from src.coherence.intent_pipeline.resolver import IntentResolver, get_intent_resolver, TemplateClassification
from src.coherence.intent_pipeline.schemas.intent import (
    ConversationContext,
    IntentDefinition,
    IntentResolution,
    ParameterDefinition,
    ParameterType,
)
from src.coherence.models.template import Template
from src.coherence.models.executed_action import ExecutedAction, ActionType, ParameterCollectionState
from src.coherence.openapi_adapter.credential_manager import get_credential_manager
from src.coherence.openapi_adapter.dynamic_executor import get_dynamic_executor
from src.coherence.template_system.services.template_service import TemplateService

logger = structlog.get_logger(__name__)


class ChatOrchestrator:
    """
    Orchestrator for the intent pipeline, API action execution, and conversation management.

    Responsible for:
    - Managing multi-turn conversations and maintaining context
    - Coordinating between intent resolution and parameter collection
    - Storing conversation state between requests
    - Executing API actions defined in templates using DynamicActionExecutor
    - Applying fallback strategies when API calls fail
    - Rendering response templates with results from API calls
    - Tracking execution metrics and storing action history
    - Handling both synchronous and asynchronous execution flows

    Architecture:
    The ChatOrchestrator implements a layered approach to processing user requests:
    1. Intent Recognition: User messages are matched to intents using the IntentResolver
    2. Parameter Extraction: Required parameters are extracted or requested from the user
    3. Template Retrieval: Templates containing API configurations are loaded
    4. Action Execution: API calls are executed based on template configuration
    5. Response Generation: Templates are rendered with API results to create responses

    Error Handling:
    - Circuit breaker protection for API endpoints
    - Fallback templates for handling API errors
    - Configurable retry mechanisms for transient failures
    - Comprehensive error logging and reporting

    Attributes:
        db: Database session for persistence operations
        redis_client: Redis client for state management and caching
        intent_resolver: Resolver for intent recognition
        parameter_extractor: Extractor for parameters from user messages
        llm_provider: LLM provider for response generation and completion
        template_service: Service for template management and rendering
        action_executor: Dynamic executor for API actions based on templates
    """

    def __init__(
        self,
        db: AsyncSession,
        redis_client: RedisClient,
        intent_resolver: IntentResolver,
        parameter_extractor: ParameterExtractor,
        llm_provider: LLMProvider,
        template_service: TemplateService = None,
        action_executor = None,
    ):
        """Initialize the ChatOrchestrator with dependencies.

        Args:
            db: Database session
            redis_client: Redis client for state management
            intent_resolver: Resolver for intent recognition
            parameter_extractor: Extractor for parameters
            llm_provider: LLM provider for response generation
            template_service: Template service for rendering templates
            action_executor: Dynamic action executor for API calls
        """
        self.db = db
        self.redis_client = redis_client
        self.intent_resolver = intent_resolver
        self.parameter_extractor = parameter_extractor
        self.llm_provider = llm_provider
        self.template_service = template_service or TemplateService()
        self.action_executor = action_executor

    async def handle_message(
        self,
        tenant_id: str,
        user_id: str,
        user_role: str,
        message: str,
        message_history: Optional[List[Dict[str, Any]]] = None,
        context: Optional[Dict[str, Any]] = None,
        request: Optional[Request] = None,
    ) -> Dict[str, Any]:
        """Handle a new user message and resolve the intent.

        This is the main entry point for the intent pipeline, handling:
        1. Intent recognition
        2. Parameter extraction
        3. Action execution
        4. Response generation

        Args:
            tenant_id: Tenant ID for multi-tenant isolation
            user_id: User ID for personalization
            user_role: User role (e.g., "admin", "user")
            message: Natural language message from the user
            message_history: Previous messages in the conversation
            context: Additional context for the conversation
            request: FastAPI request object

        Returns:
            Response dictionary with type and payload
        """
        start_time = time.time()

        # Create a trace ID for this request
        trace_id = str(uuid.uuid4())
        log = logger.bind(
            trace_id=trace_id,
            tenant_id=tenant_id,
            user_id=user_id,
            user_role=user_role,
        )

        # Initialize conversation context
        conversation_id = str(uuid.uuid4())
        if context and context.get("conversation_id"):
            conversation_id = context.get("conversation_id") or conversation_id

        conversation_context = ConversationContext(
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            user_id=user_id,
            user_role=user_role,
            current_intent=None,  # No active intent yet
            intent_confirmation_state=None,  # No confirmation state yet
            last_question=None,  # No question has been asked yet
            message_history=message_history or [],
            metadata=context or {},
        )

        # Add the current message to the history
        conversation_context.message_history.append(
            {
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat(),
            }
        )

        try:
            # Get available intents for this tenant and role
            available_intents = await self._get_available_intents(
                tenant_id=tenant_id,
                user_role=user_role,
            )

            log.info(
                "Processing message",
                conversation_id=conversation_id,
                available_intents=len(available_intents),
                message_length=len(message),
            )

            # Resolve the intent using the tiered approach with classification
            if hasattr(self.intent_resolver, 'resolve_with_classification'):
                intent_resolution, template_classification = await self.intent_resolver.resolve_with_classification(
                    tenant_id=tenant_id,
                    user_id=user_id,
                    user_role=user_role,
                    message=message,
                    intents=available_intents,
                )
            else:
                # Fallback to original method if classification method doesn't exist
                intent_resolution = await self.intent_resolver.resolve(
                    tenant_id=tenant_id,
                    user_id=user_id,
                    user_role=user_role,
                    message=message,
                    intents=available_intents,
                )
                template_classification = TemplateClassification.ACTION  # Default to action

            log.info(
                "Intent resolution completed",
                conversation_id=conversation_id,
                intent=intent_resolution.intent,
                confidence=intent_resolution.confidence,
                classification=template_classification.value,
                alternatives=intent_resolution.alternatives is not None,
            )

            # Handle the resolved intent
            if intent_resolution.intent:
                return await self._handle_resolved_intent(
                    intent_resolution=intent_resolution,
                    conversation_context=conversation_context,
                    available_intents=available_intents,
                    template_classification=template_classification,
                    log=log,
                )
            else:
                # No intent matched with sufficient confidence
                log.warning(
                    "No intent matched with sufficient confidence",
                    conversation_id=conversation_id,
                )

                return {
                    "type": "reply",
                    "outcome": "I'm not sure what you're asking. Could you please rephrase that?",
                }

        except Exception as e:
            log.exception(
                "Error in chat orchestrator",
                error=str(e),
                error_type=type(e).__name__,
            )

            return {
                "type": "reply",
                "outcome": "I'm sorry, I encountered an error processing your request.",
            }
        finally:
            # Log the total processing time
            end_time = time.time()
            processing_time = (end_time - start_time) * 1000  # ms
            log.info(
                "Request processing completed",
                processing_time_ms=int(processing_time),
            )

    async def handle_message_with_llm(
        self,
        tenant_id: str,
        user_id: str,
        user_role: str,
        message: str,
        message_history: Optional[List[Dict[str, Any]]] = None,
        context: Optional[Dict[str, Any]] = None,
        request: Optional[Request] = None,
    ) -> Dict[str, Any]:
        """Handle a user message using the simplified LLM-based resolution.
        
        This alternative method uses direct vector search + LLM decision making
        instead of the complex tiered classification system.
        
        Args:
            tenant_id: Tenant ID for multi-tenant isolation
            user_id: User ID for personalization
            user_role: User role (e.g., "admin", "user")
            message: Natural language message from the user
            message_history: Previous messages in the conversation
            context: Additional context for the conversation
            request: FastAPI request object
            
        Returns:
            Response dictionary with type and payload
        """
        start_time = time.time()
        
        # Import prompts at runtime to avoid circular imports
        from src.coherence.intent_pipeline.prompts import (
            LLM_INTENT_RESOLUTION_SYSTEM_PROMPT,
            LLM_INTENT_RESOLUTION_USER_PROMPT,
            format_template_descriptions,
            format_conversation_history
        )
        
        # Create a trace ID for this request
        trace_id = str(uuid.uuid4())
        log = logger.bind(
            trace_id=trace_id,
            tenant_id=tenant_id,
            user_id=user_id,
            user_role=user_role,
        )
        
        # Initialize conversation context
        conversation_id = str(uuid.uuid4())
        if context and context.get("conversation_id"):
            conversation_id = context.get("conversation_id") or conversation_id
            
        conversation_context = ConversationContext(
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            user_id=user_id,
            user_role=user_role,
            current_intent=None,
            intent_confirmation_state=None,
            last_question=None,
            message_history=message_history or [],
            metadata=context or {},
        )
        
        # Add the current message to the history
        conversation_context.message_history.append({
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat(),
        })
        
        try:
            # 1. Perform simple vector search
            search_results = await self.intent_resolver.simple_vector_search(
                tenant_id=tenant_id,
                message=message,
                limit=10
            )
            
            if not search_results:
                log.warning("No templates found in vector search")
                return {
                    "type": "reply",
                    "outcome": "I couldn't find any matching operations for your request. Please try rephrasing."
                }
            
            # 2. Format templates for LLM
            template_descriptions = format_template_descriptions(search_results)
            conversation_history = format_conversation_history(conversation_context.message_history[:-1])  # Exclude current message
            
            # 3. Ask LLM to resolve intent
            llm_prompt = LLM_INTENT_RESOLUTION_USER_PROMPT.format(
                message=message,
                conversation_history=conversation_history,
                template_descriptions=template_descriptions
            )
            
            # 4. Get LLM response
            response = await self.llm_provider.generate(
                messages=[
                    {"role": "system", "content": LLM_INTENT_RESOLUTION_SYSTEM_PROMPT},
                    {"role": "user", "content": llm_prompt}
                ],
                max_tokens=512,
                temperature=0.0,
                user=user_id,
            )
            
            # 5. Parse LLM response
            try:
                # Log the raw LLM response for debugging
                log.info(f"Raw LLM response: {response.content}")
                
                # Clean up the response if it contains markdown code fences
                content = response.content.strip()
                if content.startswith("```"):
                    # Remove opening fence
                    content = content.split("\n", 1)[1] if "\n" in content else content[3:]
                    # Remove closing fence
                    if content.endswith("```"):
                        content = content[:-3].strip()
                    # Remove json language identifier if present
                    if content.startswith("json"):
                        content = content[4:].strip()
                
                llm_decision = json.loads(content)
            except json.JSONDecodeError as e:
                log.error(f"Failed to parse LLM response: {e}")
                log.error(f"Raw response was: {response.content}")
                log.error(f"Cleaned content was: {content}")
                return {
                    "type": "error",
                    "outcome": "I had trouble understanding the response. Please try again."
                }
            
            log.info(
                "LLM decision made",
                selected_template=llm_decision.get("selected_template"),
                confidence=llm_decision.get("confidence"),
                clarification_needed=llm_decision.get("clarification_needed"),
                reasoning=llm_decision.get("reasoning")
            )
            
            # 6. Handle based on LLM decision
            if llm_decision.get("clarification_needed"):
                # Save conversation context for continuation
                conversation_context.current_intent = "pending_clarification"
                conversation_context.parameters = {
                    "clarification_context": {
                        "original_message": message,
                        "search_results": [r["template_key"] for r in search_results[:5]],
                        "llm_reasoning": llm_decision.get("reasoning", "")
                    }
                }
                
                log.info(f"Saving conversation context with ID: {conversation_context.conversation_id}")
                await self._save_conversation_context(conversation_context)
                
                return {
                    "type": "intent_clarification",
                    "question": llm_decision["clarification_question"],
                    "alternatives": llm_decision.get("alternate_suggestions", [])
                }
            
            # 7. Find the selected template
            selected_template = next(
                (t for t in search_results if t["template_key"] == llm_decision["selected_template"]),
                None
            )
            
            if not selected_template:
                log.error(f"Selected template not found: {llm_decision['selected_template']}")
                return {
                    "type": "reply",
                    "outcome": "I couldn't find the selected operation. Please try again."
                }
            
            # 8. Handle documentation vs action
            if llm_decision["template_type"] == "documentation":
                # For documentation, we'll need to implement a proper document retrieval
                return {
                    "type": "reply",
                    "outcome": f"Documentation for {selected_template['template_key']}:\n\n{selected_template.get('description', 'No description available')}"
                }
            else:
                # For actions, check if we need parameters
                required_params = selected_template.get("parameters", {})
                if required_params and llm_decision.get("parameters_to_collect"):
                    # Need to collect parameters
                    first_param = llm_decision["parameters_to_collect"][0]
                    param_def = required_params.get(first_param, {})
                    
                    question = f"Please provide the {first_param}"
                    if param_def.get("description"):
                        question += f" ({param_def['description']})"
                    
                    return {
                        "type": "ask",
                        "missing_field": first_param,
                        "question": question
                    }
                else:
                    # Execute action
                    return await self._execute_action(
                        intent_name=selected_template["intent"],
                        parameters={},
                        conversation_context=conversation_context
                    )
                    
        except Exception as e:
            log.exception("Error in LLM-based intent resolution", error=str(e))
            return {
                "type": "error",
                "outcome": "I encountered an error processing your request. Please try again."
            }
        finally:
            # Log the total processing time
            end_time = time.time()
            processing_time = (end_time - start_time) * 1000  # ms
            log.info(
                "LLM request processing completed",
                processing_time_ms=int(processing_time),
            )
    
    async def continue_conversation(
        self,
        tenant_id: str,
        conversation_id: str,
        user_id: str,
        message: str,
        request: Optional[Request] = None,
    ) -> Dict[str, Any]:
        """Continue an existing conversation with new information.

        Used when the previous call required more information from the user
        and returned an "ask" response.

        Args:
            tenant_id: Tenant ID for multi-tenant isolation
            conversation_id: ID of the existing conversation
            user_id: User ID for personalization
            message: New message from the user
            request: FastAPI request object

        Returns:
            Response dictionary with type and payload
        """
        log = logger.bind(
            tenant_id=tenant_id,
            user_id=user_id,
            conversation_id=conversation_id,
        )

        try:
            # Retrieve the conversation context from Redis
            context_key = f"conversation:{tenant_id}:{conversation_id}"
            conversation_json = await self.redis_client.get(context_key)

            if not conversation_json:
                log.warning("Conversation context not found")
                return {
                    "type": "reply",
                    "outcome": "I'm sorry, I couldn't find our previous conversation. Could we start over?",
                }

            # Parse the conversation context
            conversation_data = json.loads(conversation_json)
            conversation_context = ConversationContext(**conversation_data)

            # Add the new message to the history
            conversation_context.message_history.append(
                {
                    "role": "user",
                    "content": message,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            # Get available intents for this tenant and role
            available_intents = await self._get_available_intents(
                tenant_id=tenant_id,
                user_role=conversation_context.user_role,
            )

            # Find the current intent definition
            current_intent_def = next(
                (
                    i
                    for i in available_intents
                    if i.name == conversation_context.current_intent
                ),
                None,
            )

            if not current_intent_def:
                log.warning(
                    "Current intent not found in available intents",
                    current_intent=conversation_context.current_intent,
                )
                return {
                    "type": "reply",
                    "outcome": "I'm sorry, I'm having trouble with this conversation. Could we start over?",
                }

            # Try to extract the requested parameter from the message
            if conversation_context.missing_parameters:
                requested_param = conversation_context.missing_parameters[0]
                param_def = current_intent_def.parameters.get(requested_param)

                if param_def:
                    # Extract just this parameter
                    (
                        filled_params,
                        missing_params,
                    ) = await self.parameter_extractor.extract_parameters(
                        message=message,
                        intent_definition=current_intent_def,
                        existing_params=conversation_context.parameters,
                    )

                    # Update the conversation context
                    conversation_context.parameters = filled_params
                    conversation_context.missing_parameters = missing_params

                    # Save the updated context
                    await self._save_conversation_context(conversation_context)

                    log.info(
                        "Parameter extraction completed",
                        filled_params=list(filled_params.keys()),
                        missing_params=missing_params,
                    )

                    # Check if we still need more parameters
                    if missing_params:
                        # Need more parameters
                        next_param = missing_params[0]
                        next_param_def = current_intent_def.parameters.get(next_param)

                        if next_param_def:
                            # Generate a question for the next parameter
                            question = await self.parameter_extractor.generate_parameter_prompt(
                                param_name=next_param,
                                param_def=next_param_def,
                                intent_description=current_intent_def.description,
                                conversation_history=conversation_context.message_history,
                            )

                            conversation_context.last_question = question
                            await self._save_conversation_context(conversation_context)

                            return {
                                "type": "ask",
                                "missing_field": next_param,
                                "question": question,
                            }

                    # All parameters collected, execute the action
                    return await self._execute_action(
                        intent_name=current_intent_def.name,
                        parameters=filled_params,
                        conversation_context=conversation_context,
                    )

            # If we get here, treat the message as a new intent
            return await self.handle_message(
                tenant_id=tenant_id,
                user_id=user_id,
                user_role=conversation_context.user_role,
                message=message,
                message_history=conversation_context.message_history,
                context={"conversation_id": conversation_id},
                request=request,
            )

        except Exception as e:
            log.exception(
                "Error continuing conversation",
                error=str(e),
                error_type=type(e).__name__,
            )

            return {
                "type": "reply",
                "outcome": "I'm sorry, I encountered an error continuing our conversation.",
            }

    async def _handle_resolved_intent(
        self,
        intent_resolution: IntentResolution,
        conversation_context: ConversationContext,
        available_intents: List[IntentDefinition],
        template_classification: TemplateClassification,
        log: structlog.BoundLogger,
    ) -> Dict[str, Any]:
        """Handle a successfully resolved intent.

        Manages parameter collection and action execution for
        a resolved intent.

        Args:
            intent_resolution: Resolved intent information
            conversation_context: Current conversation context
            available_intents: List of available intent definitions
            log: Logger with request context

        Returns:
            Response dictionary with type and payload
        """
        # Get the intent definition
        intent_def = next(
            (i for i in available_intents if i.name == intent_resolution.intent),
            None,
        )

        if not intent_def:
            log.warning(
                "Resolved intent not found in available intents",
                intent=intent_resolution.intent,
            )
            return {
                "type": "reply",
                "outcome": "I understand what you're asking for, but I don't know how to handle it yet.",
            }

        # Update the conversation context
        conversation_context.current_intent = intent_resolution.intent
        conversation_context.parameters = intent_resolution.filled

        # Extract and validate parameters
        (
            filled_params,
            missing_params,
        ) = await self.parameter_extractor.extract_parameters(
            message=conversation_context.message_history[-1]["content"],
            intent_definition=intent_def,
            existing_params=intent_resolution.filled,
        )

        # Update the conversation context with extracted parameters
        conversation_context.parameters = filled_params
        conversation_context.missing_parameters = missing_params

        # Save the conversation context
        await self._save_conversation_context(conversation_context)

        log.info(
            "Parameter extraction completed",
            intent=intent_resolution.intent,
            filled_params=list(filled_params.keys()),
            missing_params=missing_params,
        )

        # Check if we need more parameters
        if missing_params:
            # Need more parameters
            next_param = missing_params[0]
            next_param_def = intent_def.parameters.get(next_param)

            if next_param_def:
                # Generate a question for the next parameter
                question = await self.parameter_extractor.generate_parameter_prompt(
                    param_name=next_param,
                    param_def=next_param_def,
                    intent_description=intent_def.description,
                    conversation_history=conversation_context.message_history,
                )

                conversation_context.last_question = question
                await self._save_conversation_context(conversation_context)

                return {
                    "type": "ask",
                    "missing_field": next_param,
                    "question": question,
                }

        # Check if this is a documentation request
        if template_classification == TemplateClassification.DOCUMENTATION or intent_def.name == "show_documentation":
            # Handle documentation response
            return await self._handle_documentation_response(
                intent_name=intent_def.name,
                parameters=filled_params,
                conversation_context=conversation_context,
            )

        # All parameters collected, execute the action
        return await self._execute_action(
            intent_name=intent_def.name,
            parameters=filled_params,
            conversation_context=conversation_context,
        )

    async def _execute_action(
        self,
        intent_name: str,
        parameters: Dict[str, Any],
        conversation_context: ConversationContext,
    ) -> Dict[str, Any]:
        """Execute the action for a resolved intent using the template-driven API system.

        This method is the core of the template-driven API action system. It performs the following steps:
        1. Retrieves the template associated with the intent
        2. Validates parameters against template-defined validation rules
        3. Applies parameter transformations (trim, lowercase, etc.)
        4. Determines if the action should be executed synchronously or asynchronously
        5. For asynchronous actions, creates a workflow record and returns a status URL
        6. For synchronous actions:
           a. Executes each API action defined in the template using DynamicActionExecutor
           b. Handles errors and applies fallback strategies
           c. Renders the response template with the API results
           d. Records the action execution for auditing and monitoring

        The method implements comprehensive error handling with multiple fallback strategies:
        - API-specific fallback templates defined in the template
        - Generic error handlers for different error types
        - Default message templates for common error scenarios

        It also provides detailed metrics and logging for monitoring:
        - Total execution time for the entire action
        - Per-API execution time
        - Template rendering time
        - Error counts and types

        Args:
            intent_name: Name of the intent to execute
            parameters: Extracted and validated parameters for the action
            conversation_context: Current conversation state and history

        Returns:
            Response dictionary with:
            - For synchronous actions: {"type": "action", "outcome": rendered_response}
            - For asynchronous actions: {"type": "async", "workflow_id": id, "status_url": url}
            - For validation errors: {"type": "validation_error", "errors": [...]}
            - For errors: {"type": "error"|"fallback", "outcome": error_message, "error": {details}}
        """
        try:
            # Convert tenant_id string to UUID
            tenant_id = uuid.UUID(conversation_context.tenant_id)
            
            # Lazy initialization of action executor if not provided
            if self.action_executor is None:
                credential_manager = await get_credential_manager(self.db)
                self.action_executor = await get_dynamic_executor(credential_manager)
            
            # Get the template for the intent (used for API actions)
            template = await self._get_intent_template(intent_name, tenant_id)
            
            # Phase 3 Enhancement: Validate parameters against template rules
            if template and hasattr(template, 'parameters') and template.parameters:
                validation_rules = template.parameters.get('validation_rules', {})
                if validation_rules:
                    validation_errors = self._validate_parameters(parameters, validation_rules)
                    if validation_errors:
                        logger.warning(
                            "Parameter validation failed",
                            intent_name=intent_name,
                            errors=validation_errors,
                            tenant_id=str(tenant_id)
                        )
                        return {
                            "type": "validation_error",
                            "errors": validation_errors,
                            "outcome": self._format_validation_errors(validation_errors)
                        }
            
            # Phase 3 Enhancement: Apply parameter transformations
            if template and hasattr(template, 'parameters') and template.parameters:
                transformations = template.parameters.get('transformations', {})
                if transformations:
                    parameters = self._apply_transformations(parameters, transformations)
                    logger.info(
                        "Applied parameter transformations",
                        intent_name=intent_name,
                        transformations=list(transformations.keys()),
                        tenant_id=str(tenant_id)
                    )
            
            # Record execution metrics
            exec_start_time = time.time()
            logger.info(
                f"Starting action execution for intent {intent_name}",
                tenant_id=str(tenant_id),
                has_template=template is not None,
                has_actions=template is not None and bool(template.actions)
            )
            
            # Determine if this is a synchronous or asynchronous action
            # For demonstration, we'll treat certain intents as async
            is_async = any(
                async_keyword in intent_name.lower()
                for async_keyword in ["schedule", "batch", "report", "export"]
            )

            if is_async:
                # This is an asynchronous action, so create a workflow
                workflow_id = uuid.uuid4()

                # Store workflow in the database
                from src.coherence.models.generated_action import GeneratedAction
                from src.coherence.models.workflow_status import WorkflowStatus

                # Create workflow record
                workflow = WorkflowStatus(
                    id=workflow_id,
                    tenant_id=tenant_id,
                    status="running",
                    progress=0.0,
                    current_step="Initializing task",
                    result=None,
                )
                
                # Create a record of the generated action
                generated_action = GeneratedAction(
                    id=uuid.uuid4(),
                    workflow_id=workflow_id,
                    tenant_id=tenant_id,
                    intent_name=intent_name,
                    parameters=parameters,
                    template_key=template.key if template else None,
                    template_id=template.id if template else None,
                    template_version=template.version if template else None,
                    created_at=datetime.now(),
                    status="pending"
                )

                self.db.add(workflow)
                self.db.add(generated_action)
                await self.db.commit()

                # Here, in a real implementation, we would start a background task
                # to process the workflow. For now, we'll just return the workflow ID.

                # Add the assistant's response to the conversation history
                response = f"I've started processing your {intent_name} request. You can check the status at any time."
                conversation_context.message_history.append(
                    {
                        "role": "assistant",
                        "content": response,
                        "timestamp": datetime.now().isoformat(),
                    }
                )

                # Reset the current intent now that it's complete
                conversation_context.current_intent = None
                conversation_context.missing_parameters = []

                # Save the updated context
                await self._save_conversation_context(conversation_context)

                return {
                    "type": "async",
                    "workflow_id": str(workflow_id),
                    "status_url": f"/v1/status/{workflow_id}",
                }
            else:
                # This is a synchronous action
                
                # Check if we have a template with API actions
                if template and template.actions:
                    # Execute each action in the template
                    results = {}
                    
                    # Handle both single action and multiple actions formats
                    actions_list = []
                    if isinstance(template.actions, dict) and 'api_action' in template.actions:
                        # Single action format
                        actions_list = [template.actions['api_action']]
                    elif isinstance(template.actions, list):
                        # Multiple actions format
                        actions_list = template.actions
                    else:
                        # Unknown format, treat as single action
                        actions_list = [template.actions]
                    
                    for action_index, action_config in enumerate(actions_list):
                        action_start_time = time.time()
                        api_key = action_config.get("api_key", "default")
                        
                        try:
                            logger.info(
                                f"Executing action {action_index+1}/{len(template.actions)} for intent {intent_name}",
                                api_key=api_key,
                                method=action_config.get("method", "GET"),
                                endpoint=action_config.get("endpoint", ""),
                                tenant_id=str(tenant_id)
                            )
                            
                            # Phase 3 Enhancement: Resolve base URL from integration config
                            base_url = None
                            if hasattr(template, 'action_config') and template.action_config:
                                integration_config = template.action_config.get('integration', {})
                                if integration_config:
                                    base_url = integration_config.get('base_url')
                                    api_version = integration_config.get('api_version')
                                    if base_url and api_version and api_version not in base_url:
                                        # Append API version to base URL if not already present
                                        base_url = base_url.rstrip('/') + '/' + api_version
                                    logger.info(
                                        "Using integration config for base URL",
                                        base_url=base_url,
                                        api_version=api_version,
                                        tenant_id=str(tenant_id)
                                    )
                            
                            # Execute the action with the dynamic executor
                            # Check if we have multi-format response configuration
                            response_format = template.response_format
                            preferred_content_type = None
                            
                            # Determine if response_format is multi-format (dictionary) or single format
                            if isinstance(response_format, dict) and "preferred" in response_format:
                                # Multi-format configuration
                                preferred_content_type = response_format.get("preferred")
                            
                            # Pass base_url to executor if available
                            execute_params = {
                                "action_config": action_config,
                                "parameters": parameters,
                                "tenant_id": tenant_id,
                                "response_format": response_format,
                                "preferred_content_type": preferred_content_type
                            }
                            if base_url:
                                execute_params["base_url"] = base_url
                            
                            action_result = await self.action_executor.execute(**execute_params)
                            
                            # Store the result using the action's API key as the key
                            results[api_key] = action_result
                            
                            action_exec_time = (time.time() - action_start_time) * 1000  # ms
                            logger.info(
                                "Action execution completed",
                                api_key=api_key,
                                success=action_result.get("success", False),
                                status_code=action_result.get("status_code"),
                                execution_time_ms=int(action_exec_time),
                                tenant_id=str(tenant_id)
                            )
                            
                        except Exception as e:
                            action_exec_time = (time.time() - action_start_time) * 1000  # ms
                            logger.exception(
                                f"Error executing action for intent {intent_name}",
                                api_key=api_key,
                                error=str(e),
                                error_type=type(e).__name__,
                                execution_time_ms=int(action_exec_time),
                                tenant_id=str(tenant_id)
                            )
                            
                            # Add error info to results
                            results[api_key] = {
                                "success": False,
                                "error": {
                                    "type": "execution_error",
                                    "message": str(e)
                                }
                            }
                            
                            # Check if we should use a fallback template
                            fallback_template_key = action_config.get("error_handling", {}).get("fallback_template")
                            if fallback_template_key:
                                try:
                                    # Try to get and render the fallback template
                                    fallback_template = await self.template_service.get_template(
                                        db=self.db,
                                        key=fallback_template_key,
                                        category="error_handler",
                                        tenant_id=tenant_id
                                    )
                                    
                                    if fallback_template:
                                        # Get the fallback context
                                        error_context = {
                                            "error": {
                                                "type": type(e).__name__,
                                                "message": str(e),
                                                "api_key": api_key
                                            },
                                            "parameters": parameters,
                                            "intent": {
                                                "name": intent_name
                                            }
                                        }
                                        
                                        # Render the fallback template
                                        fallback_response = await self.template_service.render_template(
                                            db=self.db,
                                            key=fallback_template_key,
                                            category="error_handler",
                                            context=error_context,
                                            tenant_id=tenant_id
                                        )
                                        
                                        # Store in results for template access
                                        results[f"{api_key}_fallback"] = {
                                            "fallback_template": fallback_template_key,
                                            "error_type": type(e).__name__,
                                            "error_message": str(e),
                                            "response": fallback_response
                                        }
                                except Exception as fallback_error:
                                    logger.error(
                                        f"Error fetching fallback template {fallback_template_key}",
                                        error=str(fallback_error),
                                        tenant_id=str(tenant_id)
                                    )
                    
                    # Create the rendering context with all results
                    render_start_time = time.time()
                    # If there's only one result keyed by api_key, make it available as 'result' too
                    result = None
                    # Extract content-type for template rendering
                    content_type = None
                    if results and len(results) == 1:
                        result = list(results.values())[0]
                        content_type = result.get("content_type")
                    
                    # Phase 3 Enhancement: Add CRFS auto-selection support
                    # Extract headers from conversation context for format selection
                    headers = {}
                    if hasattr(conversation_context, 'request_headers'):
                        headers = conversation_context.request_headers or {}
                    
                    context = {
                        "parameters": parameters,
                        "results": results,
                        "result": result,  # Make single result available directly
                        "content_type": content_type,  # Add actual content type for CRFS selection
                        "headers": headers,  # For CRFS auto-selection based on Accept header
                        "intent": {
                            "name": intent_name,
                            "parameters": parameters
                        },
                        "conversation": {
                            "id": conversation_context.conversation_id,
                            "user_id": conversation_context.user_id,
                            "history": conversation_context.message_history
                        },
                        # Phase 3: Add CRFS configuration if available
                        "crfs_config": template.response_format if hasattr(template, 'response_format') else None
                    }
                    
                    # Will create a record of the action execution after successful rendering
                    
                    try:
                        # Render the template to generate the response
                        response = await self.template_service.render_template(
                            db=self.db,
                            key=template.key,
                            category=template.category,
                            context=context,
                            tenant_id=tenant_id
                        )
                        
                        # For now, just log the successful action execution
                        # TODO: Create a proper action execution tracking model
                        logger.info(
                            "Action execution completed successfully",
                            intent_name=intent_name,
                            template_key=template.key,
                            response_length=len(response) if isinstance(response, str) else None,
                            tenant_id=str(tenant_id)
                        )
                        
                        render_time = (time.time() - render_start_time) * 1000  # ms
                        logger.info(
                            "Template rendering completed",
                            template_key=template.key,
                            render_time_ms=int(render_time),
                            tenant_id=str(tenant_id)
                        )
                        
                    except Exception as e:
                        render_time = (time.time() - render_start_time) * 1000  # ms
                        logger.exception(
                            f"Error rendering template for intent {intent_name}",
                            template_key=template.key,
                            error=str(e),
                            render_time_ms=int(render_time),
                            tenant_id=str(tenant_id)
                        )
                        
                        # Fallback to default response if template rendering fails
                        response = f"I processed your request for {intent_name}, but encountered an issue with the response format."
                        
                        # Try to use a generic error template
                        try:
                            generic_error_template = await self.template_service.get_template(
                                db=self.db,
                                key="generic_error",
                                category="error_handler",
                                tenant_id=tenant_id
                            )
                            
                            if generic_error_template:
                                error_context = {
                                    "intent": {
                                        "name": intent_name,
                                        "parameters": parameters
                                    },
                                    "error": {
                                        "type": "template_rendering",
                                        "message": str(e)
                                    }
                                }
                                
                                response = await self.template_service.render_template(
                                    db=self.db,
                                    key="generic_error",
                                    category="error_handler",
                                    context=error_context,
                                    tenant_id=tenant_id
                                )
                        except Exception:
                            # Ignore errors in the generic error template
                            pass
                        
                else:
                    # No template or no actions, generate a simple response
                    # This is a temporary placeholder for development
                    response = f"I would execute the {intent_name} action with parameters: {json.dumps(parameters, default=str)}"

                # Add the assistant's response to the conversation history
                conversation_context.message_history.append(
                    {
                        "role": "assistant",
                        "content": response,
                        "timestamp": datetime.now().isoformat(),
                    }
                )

                # Reset the current intent now that it's complete
                conversation_context.current_intent = None
                conversation_context.missing_parameters = []

                # Save the updated context
                await self._save_conversation_context(conversation_context)
                
                # Log total execution time
                total_exec_time = (time.time() - exec_start_time) * 1000  # ms
                logger.info(
                    f"Action execution completed for intent {intent_name}",
                    total_execution_time_ms=int(total_exec_time),
                    tenant_id=str(tenant_id)
                )

                return {
                    "type": "action",
                    "outcome": response,
                }
        except Exception as e:
            # Log the error
            logger.exception(
                f"Error in execute_action for intent {intent_name}",
                error=str(e),
                error_type=type(e).__name__,
                tenant_id=conversation_context.tenant_id
            )
            
            # Determine error type for better fallback handling
            error_type = "system_error"
            if isinstance(e, ValueError):
                error_type = "parameter_validation"
            elif isinstance(e, PermissionError):
                error_type = "permission_denied"
            elif isinstance(e, TimeoutError):
                error_type = "service_unavailable"
            elif "circuit" in str(e).lower():
                error_type = "service_unavailable"
            elif "api" in str(e).lower() or "http" in str(e).lower():
                error_type = "api_error"
            
            # Use our centralized fallback handler
            tenant_id = None
            try:
                tenant_id = uuid.UUID(conversation_context.tenant_id)
            except (ValueError, AttributeError):
                pass
                
            fallback_response = await self._handle_fallback(
                intent_name=intent_name,
                parameters=parameters,
                error_type=error_type,
                error_message=str(e),
                tenant_id=tenant_id
            )
            
            # If this was a user-facing error, add it to the conversation history
            if fallback_response["type"] in ["fallback", "error"]:
                conversation_context.message_history.append({
                    "role": "assistant",
                    "content": fallback_response["outcome"],
                    "timestamp": datetime.now().isoformat(),
                })
                
                # Reset the current intent since we had an error
                conversation_context.current_intent = None
                conversation_context.missing_parameters = []
                
                # Save the updated context
                await self._save_conversation_context(conversation_context)
            
            # Log the failed action for now
            # TODO: Create a proper action execution tracking model
            if tenant_id:
                logger.error(
                    "Action execution failed",
                    intent_name=intent_name,
                    error_type=error_type,
                    error_message=str(e),
                    tenant_id=str(tenant_id)
                )
            
            return fallback_response

    async def _get_available_intents(
        self,
        tenant_id: str,
        user_role: str,
    ) -> List[IntentDefinition]:
        """Get available intents for a tenant and role.

        Fetches intents from:
        1. api_endpoints that have intent-related fields populated
        2. templates with category 'intent_router'

        Args:
            tenant_id: Tenant ID
            user_role: User role

        Returns:
            List of available intent definitions
        """
        from src.coherence.models.integration import APIEndpoint, APIIntegration, IntegrationStatus
        from src.coherence.models.template import Template, TemplateCategory
        from sqlalchemy import and_, or_
        from sqlalchemy.future import select
        
        intents = []
        
        try:
            # First, get intents from api_endpoints that have intent fields populated
            # Join with api_integrations to filter by tenant
            query = select(APIEndpoint).join(
                APIIntegration, APIEndpoint.integration_id == APIIntegration.id
            ).where(
                and_(
                    APIEndpoint.intent_name != None,
                    APIIntegration.tenant_id == uuid.UUID(tenant_id),
                    APIIntegration.status == IntegrationStatus.ACTIVE
                )
            )
            
            result = await self.db.execute(query)
            endpoints = result.scalars().all()
            
            # Convert endpoints to intent definitions
            for endpoint in endpoints:
                if endpoint.intent_name and endpoint.intent_description:
                    # Parse intent parameters from JSON or use empty dict
                    parameters = {}
                    if endpoint.intent_parameters:
                        try:
                            parameters = json.loads(endpoint.intent_parameters) if isinstance(endpoint.intent_parameters, str) else endpoint.intent_parameters
                        except:
                            parameters = {}
                    
                    # Parse intent examples from JSON or use empty list
                    examples = []
                    if endpoint.intent_examples:
                        try:
                            examples = json.loads(endpoint.intent_examples) if isinstance(endpoint.intent_examples, str) else endpoint.intent_examples
                        except:
                            examples = []
                    
                    # Parse required fields from JSON or use empty list
                    required_fields = []
                    if endpoint.intent_required_fields:
                        try:
                            required_fields = json.loads(endpoint.intent_required_fields) if isinstance(endpoint.intent_required_fields, str) else endpoint.intent_required_fields
                        except:
                            required_fields = []
                    
                    # Convert parameter definitions to proper format
                    param_defs = {}
                    for param_name, param_info in parameters.items():
                        if isinstance(param_info, dict):
                            # Map type strings to valid ParameterType enum values
                            param_type = param_info.get("type", "string")
                            if param_type == "str":
                                param_type = "string"
                            elif param_type == "int":
                                param_type = "number"
                            elif param_type not in ["string", "number", "boolean", "date", "time", "datetime", "duration", "currency", "email", "phone", "url", "custom"]:
                                param_type = "string"  # Default to string for unknown types
                            
                            param_defs[param_name] = ParameterDefinition(
                                name=param_name,
                                type=ParameterType(param_type),
                                required=param_info.get("required", False),
                                description=param_info.get("description", ""),
                                prompt=param_info.get("prompt", f"Please provide {param_name}"),
                                validation_regex=param_info.get("validation_regex"),
                                default_value=param_info.get("default_value"),
                                options=param_info.get("options"),
                                validation_error=param_info.get("validation_error"),
                                unit=param_info.get("unit"),
                                custom_extraction_template=param_info.get("custom_extraction_template")
                            )
                    
                    intents.append(
                        IntentDefinition(
                            name=endpoint.intent_name,
                            description=endpoint.intent_description,
                            examples=examples,
                            parameters=param_defs,
                            required_fields=set(required_fields) if required_fields else set(),
                        )
                    )
            
            # Second, get intents from templates with category 'intent_router'
            template_query = select(Template).where(
                and_(
                    Template.tenant_id == uuid.UUID(tenant_id),
                    Template.category == TemplateCategory.INTENT_ROUTER.value,
                    # Only get active templates (not soft deleted)
                )
            )
            
            template_result = await self.db.execute(template_query)
            templates = template_result.scalars().all()
            
            # Convert templates to intent definitions
            for template in templates:
                # Extract intent name from template key
                # For template key like "intent_api_get_alerts", use "api_get_alerts" as intent name
                # to match what's stored in the vector database
                intent_name = template.key
                if intent_name.startswith("intent_"):
                    intent_name = intent_name[7:]  # Remove "intent_" prefix
                
                # Use template description or generate one
                intent_description = template.description or f"Intent for {intent_name}"
                
                # Extract parameters from template
                param_defs = {}
                if template.parameters:
                    for param_name, param_info in template.parameters.items():
                        if isinstance(param_info, dict):
                            # Map type strings to valid ParameterType enum values
                            param_type = param_info.get("type", "string")
                            if param_type == "str":
                                param_type = "string"
                            elif param_type == "int":
                                param_type = "number"
                            elif param_type not in ["string", "number", "boolean", "date", "time", "datetime", "duration", "currency", "email", "phone", "url", "custom"]:
                                param_type = "string"  # Default to string for unknown types
                            
                            param_defs[param_name] = ParameterDefinition(
                                name=param_name,
                                type=ParameterType(param_type),
                                required=param_info.get("required", False),
                                description=param_info.get("description", ""),
                                prompt=param_info.get("prompt", f"Please provide {param_name}"),
                                validation_regex=param_info.get("validation_regex"),
                                default_value=param_info.get("default_value"),
                                options=param_info.get("options"),
                                validation_error=param_info.get("validation_error"),
                                unit=param_info.get("unit"),
                                custom_extraction_template=param_info.get("custom_extraction_template")
                            )
                
                # Extract examples from template body if possible
                examples = []
                if template.body:
                    # Simple extraction of examples from template body
                    lines = template.body.split("\n")
                    for line in lines:
                        if line.strip().startswith("- ") and "→" not in line and "{% " not in line:
                            example = line.strip()[2:].strip()
                            if example:
                                examples.append(example)
                
                # Get required fields from parameters
                required_fields = [
                    name for name, param in param_defs.items()
                    if param.required
                ]
                
                intents.append(
                    IntentDefinition(
                        name=intent_name,
                        description=intent_description,
                        examples=examples,
                        parameters=param_defs,
                        required_fields=set(required_fields),
                    )
                )
            
            logger.info(
                f"Loaded {len(intents)} intents for tenant {tenant_id} and role {user_role} "
                f"({len(endpoints)} from endpoints, {len(templates)} from templates)",
                intent_names=[intent.name for intent in intents]
            )
            
        except Exception as e:
            logger.error(
                f"Error loading intents from api_endpoints: {str(e)}",
                error_type=type(e).__name__,
                tenant_id=tenant_id
            )
        
        return intents

    async def _handle_fallback(self, intent_name: str, parameters: Dict[str, Any], error_type: str = "unknown", error_message: Optional[str] = None, tenant_id: Optional[uuid.UUID] = None) -> Dict[str, Any]:
        """Handle fallback strategies when intent resolution or action execution fails.

        This method implements a sophisticated fallback strategy system with multiple layers:
        1. Specific fallback templates for error types (fallback_{error_type})
        2. Generic fallback template for any error (generic_fallback)
        3. Hardcoded fallback messages tailored to specific error categories
        
        The method attempts to find and render appropriate templates first, falling back
        to predefined messages if no template is available or if rendering fails.
        
        Supported error types:
        - permission_denied: User lacks permissions for the action
        - service_unavailable: External service is unavailable (timeouts, circuit breaker)
        - parameter_validation: Parameter validation failed
        - intent_confusion: Ambiguous intent detection
        - api_error: API endpoint returned an error
        - system_error: Internal system error (default)
        
        The fallback response includes error details useful for debugging while
        providing a user-friendly message suitable for end-users.

        Args:
            intent_name: Name of the intent that failed
            parameters: Current parameters that were being used
            error_type: Categorized type of error that occurred
            error_message: Detailed technical error message
            tenant_id: The tenant ID for template lookup

        Returns:
            Fallback response dictionary with format:
            {
                "type": "fallback",
                "outcome": "User-friendly message",
                "error": {
                    "type": "error_type",
                    "message": "Detailed error message"
                }
            }
        """
        try:
            # Try to find a fallback template for this type of error
            error_template_key = f"fallback_{error_type}"
            error_template = None
            
            if tenant_id:
                try:
                    error_template = await self.template_service.get_template(
                        db=self.db,
                        key=error_template_key,
                        category="error_handler",
                        tenant_id=tenant_id
                    )
                except Exception:
                    pass
                
            # If no specific error template, try the generic fallback
            if not error_template:
                try:
                    error_template = await self.template_service.get_template(
                        db=self.db,
                        key="generic_fallback",
                        category="error_handler",
                        tenant_id=tenant_id
                    )
                except Exception:
                    pass
            
            # If we found a template, render it
            if error_template:
                error_context = {
                    "error": {
                        "type": error_type,
                        "message": error_message or "Unknown error"
                    },
                    "intent": {
                        "name": intent_name,
                        "parameters": parameters
                    }
                }
                
                fallback_response = await self.template_service.render_template(
                    db=self.db,
                    key=error_template.key,
                    category=error_template.category,
                    context=error_context,
                    tenant_id=tenant_id
                )
                
                return {
                    "type": "fallback",
                    "outcome": fallback_response,
                    "error": {
                        "type": error_type,
                        "message": error_message
                    }
                }
                
        except Exception as e:
            logger.exception(
                "Error in fallback handler",
                error=str(e),
                error_type=type(e).__name__
            )
        
        # Default fallback message
        if error_type == "permission_denied":
            message = "I'm sorry, but you don't have permission to perform this action."
        elif error_type == "service_unavailable":
            message = "I'm sorry, but this service is currently unavailable. Please try again later."
        elif error_type == "parameter_validation":
            message = "I couldn't process your request because some information is incorrect or missing."
        elif error_type == "intent_confusion":
            message = "I'm not quite sure what you're asking for. Could you rephrase your request?"
        elif error_type == "api_error":
            message = "I encountered an issue while processing your request with an external service."
        else:
            message = "I'm sorry, but I couldn't process your request. Could you try again?"
            
        return {
            "type": "fallback",
            "outcome": message,
            "error": {
                "type": error_type,
                "message": error_message
            }
        }
    
    async def _get_intent_template(
        self,
        intent_name: str,
        tenant_id: uuid.UUID
    ) -> Optional[Template]:
        """Get the template for an intent.
        
        This method tries to find the template in this order:
        1. Response generation template with the intent name as key
        2. Intent router template with the intent name as key
        3. Any template with the intent name as key
        
        Args:
            intent_name: Name of the intent
            tenant_id: Tenant ID
            
        Returns:
            Template object if found, None otherwise
        """
        template = None
        
        # Try different categories, in order of preference
        categories = ["response_gen", "intent_router"]
        
        for category in categories:
            try:
                template = await self.template_service.get_template(
                    db=self.db,
                    key=intent_name,
                    category=category,
                    tenant_id=tenant_id
                )
                
                if template:
                    return template
            except Exception as e:
                log = logger.bind(
                    intent=intent_name,
                    category=category,
                    tenant_id=str(tenant_id)
                )
                log.debug(f"Template not found: {str(e)}")
        
        # If no template was found in the preferred categories,
        # try a more general query (any category)
        try:
            # Query for the template with any category
            query = (
                "SELECT * FROM templates WHERE key = :key AND tenant_id = :tenant_id"
                " ORDER BY version DESC LIMIT 1"
            )
            result = await self.db.execute(
                query, {"key": intent_name, "tenant_id": tenant_id}
            )
            template_row = result.fetchone()
            
            if template_row:
                # Convert row to Template object
                template = Template(**dict(template_row))
                return template
                
        except Exception as e:
            logger.warning(
                f"Failed to get any template for intent {intent_name}",
                error=str(e),
                tenant_id=str(tenant_id)
            )
            
        return None
        
    async def _handle_documentation_response(
        self,
        intent_name: str,
        parameters: Dict[str, Any],
        conversation_context: ConversationContext,
    ) -> Dict[str, Any]:
        """Handle documentation requests by retrieving and formatting API documentation.
        
        This method retrieves documentation templates and renders them to provide
        human-readable API documentation to users.
        
        Args:
            intent_name: Name of the documentation intent
            parameters: Extracted parameters (e.g., endpoint, method)
            conversation_context: Current conversation state
            
        Returns:
            Response dictionary with formatted documentation
        """
        try:
            tenant_id = uuid.UUID(conversation_context.tenant_id)
            user_id = uuid.UUID(conversation_context.user_id)
            
            # Create ExecutedAction record for tracking
            executed_action = ExecutedAction(
                tenant_id=tenant_id,
                user_id=user_id,
                conversation_id=conversation_context.conversation_id,
                intent_name=intent_name,
                action_type=ActionType.DOCUMENTATION,
                parameter_state=ParameterCollectionState.COMPLETED,
                parameters_collected=parameters,
                is_documentation_request=True,
                documentation_endpoint=parameters.get("endpoint"),
                documentation_method=parameters.get("method"),
            )
            self.db.add(executed_action)
            await self.db.commit()
            
            # Log documentation request
            logger.info(
                f"Handling documentation request for intent {intent_name}",
                tenant_id=str(tenant_id),
                parameters=parameters,
                executed_action_id=str(executed_action.id)
            )
            
            # Retrieve the documentation template
            documentation_template = await self._get_documentation_template(
                intent_name=intent_name,
                endpoint=parameters.get("endpoint"),
                method=parameters.get("method"),
                tenant_id=tenant_id
            )
            
            if not documentation_template:
                logger.warning(
                    f"Documentation template not found for intent {intent_name}",
                    tenant_id=str(tenant_id),
                    parameters=parameters
                )
                
                return {
                    "type": "reply",
                    "outcome": f"I couldn't find documentation for {parameters.get('endpoint', 'this endpoint')}. Please check if the endpoint is correct."
                }
            
            # Render the documentation template
            context = {
                "parameters": parameters,
                "documentation": documentation_template,
                "intent": {
                    "name": intent_name,
                    "parameters": parameters
                }
            }
            
            response = await self.template_service.render_template(
                db=self.db,
                key=documentation_template.key,
                category=documentation_template.category,
                context=context,
                tenant_id=tenant_id
            )
            
            # Add the response to conversation history
            conversation_context.message_history.append({
                "role": "assistant",
                "content": response,
                "timestamp": datetime.now().isoformat(),
                "type": "documentation"
            })
            
            # Reset intent since documentation is complete
            conversation_context.current_intent = None
            conversation_context.missing_parameters = []
            
            # Save the updated context
            await self._save_conversation_context(conversation_context)
            
            # Update the executed action with response
            executed_action.final_response = response
            executed_action.completed_at = datetime.now()
            await self.db.commit()
            
            logger.info(
                f"Documentation response generated for intent {intent_name}",
                tenant_id=str(tenant_id),
                executed_action_id=str(executed_action.id)
            )
            
            return {
                "type": "reply",
                "outcome": response,
            }
            
        except Exception as e:
            logger.exception(
                f"Error handling documentation response for intent {intent_name}",
                error=str(e),
                error_type=type(e).__name__,
                tenant_id=conversation_context.tenant_id
            )
            
            # Update the executed action with error
            if 'executed_action' in locals():
                executed_action.error_message = str(e)
                executed_action.parameter_state = ParameterCollectionState.FAILED
                executed_action.completed_at = datetime.now()
                await self.db.commit()
            
            return {
                "type": "error",
                "outcome": "I encountered an error while retrieving the documentation. Please try again later."
            }
    
    async def _get_documentation_template(
        self,
        intent_name: str,
        endpoint: Optional[str],
        method: Optional[str], 
        tenant_id: uuid.UUID
    ) -> Optional[Template]:
        """Retrieve the documentation template for a given endpoint.
        
        Args:
            intent_name: Name of the documentation intent
            endpoint: API endpoint path
            method: HTTP method
            tenant_id: Tenant ID
            
        Returns:
            Documentation template if found, None otherwise
        """
        try:
            # For documentation intents, the template key usually follows a pattern
            # like "doc_<endpoint>_<method>" or similar
            if endpoint and method:
                template_key = f"doc_{endpoint.replace('/', '_')}_{method.lower()}"
            else:
                template_key = intent_name
            
            # Try to get the template with the key
            template = await self.template_service.get_template(
                db=self.db,
                key=template_key,
                category="documentation",
                tenant_id=tenant_id
            )
            
            if not template:
                # Fallback to getting by intent name
                template = await self.template_service.get_template(
                    db=self.db,
                    key=intent_name,
                    category="documentation", 
                    tenant_id=tenant_id
                )
                
            return template
            
        except Exception as e:
            logger.exception(
                f"Error retrieving documentation template for intent {intent_name}",
                error=str(e),
                error_type=type(e).__name__,
                tenant_id=str(tenant_id)
            )
            
            return None

    def _validate_parameters(self, parameters: Dict[str, Any], validation_rules: Dict[str, Any]) -> List[str]:
        """Validate parameters against template-defined validation rules.
        
        Args:
            parameters: The parameters to validate
            validation_rules: Dictionary of validation rules per parameter
            
        Returns:
            List of validation error messages
        """
        errors = []
        
        for param_name, rules in validation_rules.items():
            if param_name not in parameters and rules.get("required"):
                errors.append(f"Required parameter '{param_name}' is missing")
                continue
                
            if param_name not in parameters:
                continue
                
            value = parameters[param_name]
            param_type = rules.get("type")
            
            # Type validation
            if param_type:
                if param_type == "string" and not isinstance(value, str):
                    errors.append(f"Parameter '{param_name}' must be a string")
                elif param_type == "integer" and not isinstance(value, int):
                    errors.append(f"Parameter '{param_name}' must be an integer")
                elif param_type == "number" and not isinstance(value, (int, float)):
                    errors.append(f"Parameter '{param_name}' must be a number")
                elif param_type == "boolean" and not isinstance(value, bool):
                    errors.append(f"Parameter '{param_name}' must be a boolean")
                elif param_type == "array" and not isinstance(value, list):
                    errors.append(f"Parameter '{param_name}' must be an array")
            
            # String validations
            if isinstance(value, str):
                if "min_length" in rules and len(value) < rules["min_length"]:
                    errors.append(f"Parameter '{param_name}' must be at least {rules['min_length']} characters long")
                if "max_length" in rules and len(value) > rules["max_length"]:
                    errors.append(f"Parameter '{param_name}' must be at most {rules['max_length']} characters long")
                if "pattern" in rules:
                    import re
                    if not re.match(rules["pattern"], value):
                        errors.append(f"Parameter '{param_name}' does not match required pattern: {rules['pattern']}")
                if "enum" in rules and value not in rules["enum"]:
                    errors.append(f"Parameter '{param_name}' must be one of: {', '.join(rules['enum'])}")
                if "format" in rules:
                    # Basic format validation
                    if rules["format"] == "email" and "@" not in value:
                        errors.append(f"Parameter '{param_name}' must be a valid email address")
                    elif rules["format"] == "url" and not value.startswith(("http://", "https://")):
                        errors.append(f"Parameter '{param_name}' must be a valid URL")
            
            # Number validations
            if isinstance(value, (int, float)):
                if "min" in rules and value < rules["min"]:
                    errors.append(f"Parameter '{param_name}' must be >= {rules['min']}")
                if "max" in rules and value > rules["max"]:
                    errors.append(f"Parameter '{param_name}' must be <= {rules['max']}")
                if "exclusive_min" in rules and value <= rules["exclusive_min"]:
                    errors.append(f"Parameter '{param_name}' must be > {rules['exclusive_min']}")
                if "exclusive_max" in rules and value >= rules["exclusive_max"]:
                    errors.append(f"Parameter '{param_name}' must be < {rules['exclusive_max']}")
                if "multiple_of" in rules and value % rules["multiple_of"] != 0:
                    errors.append(f"Parameter '{param_name}' must be a multiple of {rules['multiple_of']}")
            
            # Array validations
            if isinstance(value, list):
                if "min_items" in rules and len(value) < rules["min_items"]:
                    errors.append(f"Parameter '{param_name}' must have at least {rules['min_items']} items")
                if "max_items" in rules and len(value) > rules["max_items"]:
                    errors.append(f"Parameter '{param_name}' must have at most {rules['max_items']} items")
                if "unique_items" in rules and rules["unique_items"] and len(value) != len(set(value)):
                    errors.append(f"Parameter '{param_name}' must have unique items")
        
        return errors
    
    def _apply_transformations(self, parameters: Dict[str, Any], transformations: Dict[str, List[str]]) -> Dict[str, Any]:
        """Apply transformation rules to parameters.
        
        Args:
            parameters: The parameters to transform
            transformations: Dictionary of transformation rules per parameter
            
        Returns:
            Transformed parameters
        """
        transformed = parameters.copy()
        
        for param_name, transforms in transformations.items():
            if param_name not in transformed or not transforms:
                continue
                
            value = transformed[param_name]
            
            for transform in transforms:
                if transform == "trim" and isinstance(value, str):
                    value = value.strip()
                elif transform == "lowercase" and isinstance(value, str):
                    value = value.lower()
                elif transform == "uppercase" and isinstance(value, str):
                    value = value.upper()
                elif transform == "remove_non_numeric" and isinstance(value, str):
                    # Remove all non-numeric characters
                    value = ''.join(filter(str.isdigit, value))
                elif transform == "deduplicate" and isinstance(value, list):
                    # Remove duplicates while preserving order
                    seen = set()
                    new_list = []
                    for item in value:
                        if item not in seen:
                            seen.add(item)
                            new_list.append(item)
                    value = new_list
            
            transformed[param_name] = value
        
        return transformed
    
    def _format_validation_errors(self, errors: List[str]) -> str:
        """Format validation errors into a user-friendly message.
        
        Args:
            errors: List of validation error messages
            
        Returns:
            Formatted error message
        """
        if not errors:
            return ""
        
        if len(errors) == 1:
            return f"I found an issue with your request: {errors[0]}"
        
        error_list = "\n".join(f"• {error}" for error in errors)
        return f"I found some issues with your request:\n{error_list}\n\nPlease correct these and try again."
    
    async def _save_conversation_context(self, context: ConversationContext) -> None:
        """Save the conversation context to Redis.

        Args:
            context: Conversation context to save
        """
        # Generate the Redis key
        key = f"conversation:{context.tenant_id}:{context.conversation_id}"
        logger.info(f"Saving conversation with key: {key}")
        logger.info(f"Tenant ID: {context.tenant_id}, type: {type(context.tenant_id)}")
        logger.info(f"Conversation ID: {context.conversation_id}, type: {type(context.conversation_id)}")

        # Serialize the context to JSON
        context_json = context.model_dump_json()

        # Save to Redis with a TTL (1 hour)
        await self.redis_client.set(key, context_json, expire=3600)
        logger.info(f"Conversation saved successfully to Redis with key: {key}")


async def get_chat_orchestrator(
    db: AsyncSession = Depends(get_db),  # noqa: B008
) -> ChatOrchestrator:
    """Factory function to create a ChatOrchestrator.

    Args:
        db: Database session

    Returns:
        An initialized ChatOrchestrator
    """
    # Import dependencies locally to avoid circular imports
    from src.coherence.core.redis_client import get_redis_client

    # Get actual dependencies
    redis_client = await get_redis_client()
    qdrant_client = await get_qdrant_client()
    llm_factory = LLMFactory()

    # Fail fast if using mock provider in production
    if settings.ENV == "production" and settings.LLM_PROVIDER.lower() == "mock":
        raise RuntimeError("MockLLMProvider must not be used in production environment")

    # Get the intent resolver
    intent_resolver = await get_intent_resolver(
        db=db,
        qdrant_client=qdrant_client,
        llm_factory=llm_factory,
    )

    # Get the parameter extractor
    parameter_extractor = await get_parameter_extractor(
        llm_provider=llm_factory.create_provider(
            name=settings.LLM_PROVIDER,
            model=settings.PARAM_COMPLETION_MODEL,
        ),
        max_completion_rounds=settings.MAX_COMPLETION_ROUNDS,
    )

    # Create the LLM provider
    llm_provider = llm_factory.create_provider(
        name=settings.LLM_PROVIDER,
        model=settings.LLM_MODEL,
    )

    # Additional verification that we're not using a mock provider in production
    from src.coherence.core.llm.providers.mock_provider import MockLLMProvider

    if settings.ENV == "production" and isinstance(llm_provider, MockLLMProvider):
        raise RuntimeError("MockLLMProvider must not be used in production environment")

    # Initialize the template service
    template_service = TemplateService()
    
    # Initialize the credential manager and action executor
    credential_manager = await get_credential_manager(db)
    action_executor = await get_dynamic_executor(credential_manager)

    # Create and return the orchestrator
    return ChatOrchestrator(
        db=db,
        redis_client=redis_client,
        intent_resolver=intent_resolver,
        parameter_extractor=parameter_extractor,
        llm_provider=llm_provider,
        template_service=template_service,
        action_executor=action_executor,
    )
