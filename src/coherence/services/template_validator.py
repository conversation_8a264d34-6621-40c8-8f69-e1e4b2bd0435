"""
Template Validation Service for unified templates.

This module provides comprehensive validation for unified templates including:
- Completeness validation (all required sections present)
- Credential reference validation
- CRFS configuration validation
- Parameter mapping validation
- Execution readiness checks
"""

import logging
import re
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass

from src.coherence.models.template import TemplateCategory
from src.coherence.openapi_adapter.credential_manager import CredentialManager

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of template validation."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    metadata: Optional[Dict[str, object]] = None


class UnifiedTemplateValidator:
    """
    Validator for unified templates.
    
    This class provides comprehensive validation for unified templates
    to ensure they contain all necessary information for execution.
    """
    
    # Required sections for a complete unified template
    REQUIRED_SECTIONS = ['meta', 'intent', 'action', 'response']
    
    # Valid authentication types
    VALID_AUTH_TYPES = ['api_key', 'bearer', 'oauth2', 'basic', 'custom']
    
    # Valid HTTP methods
    VALID_HTTP_METHODS = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS']
    
    # Valid parameter types
    VALID_PARAM_TYPES = ['string', 'integer', 'number', 'boolean', 'array', 'object']
    
    # Valid string formats
    VALID_STRING_FORMATS = ['email', 'uri', 'url', 'date', 'date-time', 'time', 
                           'uuid', 'hostname', 'ipv4', 'ipv6', 'regex']
    
    # Valid transformations
    VALID_TRANSFORMATIONS = ['trim', 'lowercase', 'uppercase', 'remove_non_numeric',
                            'deduplicate', 'sort', 'reverse']
    
    def __init__(self, credential_manager: Optional[CredentialManager] = None):
        """Initialize the validator.
        
        Args:
            credential_manager: Optional credential manager for validating credential references
        """
        self.credential_manager = credential_manager
    
    def validate_completeness(self, template: Dict) -> ValidationResult:
        """
        Validate template contains all required execution details.
        
        Args:
            template: The template dictionary to validate
            
        Returns:
            ValidationResult with errors and warnings
        """
        errors = []
        warnings = []
        metadata = {}
        
        # Check required sections
        missing_sections = []
        for section in self.REQUIRED_SECTIONS:
            if section not in template:
                missing_sections.append(section)
        
        if missing_sections:
            errors.append(f"Missing required sections: {', '.join(missing_sections)}")
            
        # Validate each section if present
        if 'meta' in template:
            meta_errors = self._validate_meta_section(template['meta'])
            errors.extend(meta_errors)
            
        if 'intent' in template:
            intent_errors, intent_warnings = self._validate_intent_section(template['intent'])
            errors.extend(intent_errors)
            warnings.extend(intent_warnings)
            
        if 'action' in template:
            action_errors, action_warnings = self._validate_action_section(template['action'])
            errors.extend(action_errors)
            warnings.extend(action_warnings)
            
        if 'response' in template:
            response_errors = self._validate_response_section(template['response'])
            errors.extend(response_errors)
        
        # Validate credential references if credential manager is available
        if 'credentials' in template and self.credential_manager:
            cred_errors = self._validate_credential_references(template['credentials'])
            errors.extend(cred_errors)
        
        # Validate parameter mappings
        if 'action' in template and 'intent' in template:
            param_errors = self._validate_parameter_mappings(template)
            errors.extend(param_errors)
        
        # Check for test data
        if 'test_data' not in template:
            warnings.append("No test data provided - consider adding mock responses for development")
        
        # Collect metadata
        metadata['has_test_data'] = 'test_data' in template
        metadata['auth_type'] = template.get('action', {}).get('authentication', {}).get('type')
        metadata['parameter_count'] = len(template.get('action', {}).get('parameter_mapping', {}))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            metadata=metadata
        )
    
    def validate_execution_readiness(self, template: Dict) -> bool:
        """
        Check if template can execute without external dependencies.
        
        Args:
            template: The template to check
            
        Returns:
            True if template is ready for execution
        """
        # Check for base URL
        if not self._has_base_url(template):
            return False
        
        # Check for required auth configuration
        if not self._has_valid_auth(template):
            return False
        
        # Check for parameter schemas
        if not self._has_parameter_schemas(template):
            return False
        
        # Check for response format
        if not self._has_response_format(template):
            return False
        
        return True
    
    def _validate_meta_section(self, meta: Dict) -> List[str]:
        """Validate the meta section."""
        errors = []
        
        # Required fields
        required_fields = ['key', 'endpoint_id']
        for field in required_fields:
            if field not in meta or not meta[field]:
                errors.append(f"Meta section missing required field: {field}")
        
        # Validate key format (should be snake_case)
        if 'key' in meta:
            if not re.match(r'^[a-z][a-z0-9_]*$', meta['key']):
                errors.append(f"Invalid key format: {meta['key']}. Should be snake_case")
        
        # Validate endpoint_id format
        if 'endpoint_id' in meta:
            if not re.match(r'^[A-Z]+_/', meta['endpoint_id']):
                errors.append(f"Invalid endpoint_id format: {meta['endpoint_id']}. Should be METHOD_/path")
        
        return errors
    
    def _validate_intent_section(self, intent: Dict) -> Tuple[List[str], List[str]]:
        """Validate the intent section."""
        errors = []
        warnings = []
        
        # Check for patterns
        if 'patterns' not in intent or not intent['patterns']:
            errors.append("Intent section must have at least one pattern")
        else:
            # Validate pattern format
            for pattern in intent['patterns']:
                if not isinstance(pattern, str):
                    errors.append(f"Invalid pattern type: {type(pattern)}. Must be string")
                elif '{' in pattern and '}' in pattern:
                    # Extract parameter names from pattern
                    param_names = re.findall(r'\{(\w+)\}', pattern)
                    if not param_names:
                        warnings.append(f"Pattern has braces but no parameters: {pattern}")
        
        # Check confidence threshold
        if 'confidence_threshold' in intent:
            threshold = intent['confidence_threshold']
            if not isinstance(threshold, (int, float)) or threshold < 0 or threshold > 1:
                errors.append(f"Invalid confidence_threshold: {threshold}. Must be between 0 and 1")
        
        return errors, warnings
    
    def _validate_action_section(self, action: Dict) -> Tuple[List[str], List[str]]:
        """Validate the action section."""
        errors = []
        warnings = []
        
        # Check HTTP method
        if 'method' not in action:
            errors.append("Action section missing required field: method")
        elif action['method'] not in self.VALID_HTTP_METHODS:
            errors.append(f"Invalid HTTP method: {action['method']}")
        
        # Check path
        if 'path' not in action:
            errors.append("Action section missing required field: path")
        elif not action['path'].startswith('/'):
            errors.append(f"Path must start with /: {action['path']}")
        
        # Validate integration section
        if 'integration' in action:
            integration = action['integration']
            if 'base_url' not in integration:
                warnings.append("Integration section missing base_url")
            else:
                # Check for environment variable syntax
                base_url = integration['base_url']
                if '${' in base_url:
                    # Validate environment variable format
                    env_pattern = r'\$\{([A-Z_][A-Z0-9_]*)(:[^}]+)?\}'
                    if not re.match(env_pattern, base_url):
                        errors.append(f"Invalid environment variable format: {base_url}")
        
        # Validate authentication
        if 'authentication' in action:
            auth = action['authentication']
            if 'type' not in auth:
                errors.append("Authentication section missing type")
            elif auth['type'] not in self.VALID_AUTH_TYPES:
                errors.append(f"Invalid authentication type: {auth['type']}")
            
            # Validate auth-specific fields
            if auth.get('type') == 'api_key':
                if 'header' not in auth and 'query' not in auth:
                    errors.append("API key authentication must specify header or query parameter")
        
        # Validate parameter mapping
        if 'parameter_mapping' in action:
            param_errors = self._validate_parameter_mapping(action['parameter_mapping'])
            errors.extend(param_errors)
        
        # Validate validation rules
        if 'validation_rules' in action:
            rule_errors = self._validate_validation_rules(action['validation_rules'])
            errors.extend(rule_errors)
        
        # Validate transformations
        if 'transformations' in action:
            trans_errors = self._validate_transformations(action['transformations'])
            errors.extend(trans_errors)
        
        return errors, warnings
    
    def _validate_response_section(self, response: Dict) -> List[str]:
        """Validate the response section."""
        errors = []
        
        # Check for CRFS configuration
        if 'crfs' not in response:
            errors.append("Response section missing CRFS configuration")
        else:
            crfs = response['crfs']
            
            # Validate CRFS type
            if 'type' in crfs and crfs['type'] not in ['structured', 'template', 'raw']:
                errors.append(f"Invalid CRFS type: {crfs['type']}")
            
            # Validate formats if present
            if 'formats' in crfs:
                if not isinstance(crfs['formats'], dict):
                    errors.append("CRFS formats must be a dictionary")
                else:
                    # Check that default format exists if specified
                    if 'default_format' in crfs:
                        if crfs['default_format'] not in crfs['formats']:
                            errors.append(f"Default format '{crfs['default_format']}' not found in formats")
            
            # Validate auto_select
            if 'auto_select' in crfs and not isinstance(crfs['auto_select'], bool):
                errors.append("CRFS auto_select must be boolean")
        
        # Validate error mapping
        if 'error_mapping' in response:
            if not isinstance(response['error_mapping'], dict):
                errors.append("Error mapping must be a dictionary")
            else:
                # Check that keys are valid HTTP status codes
                for code, message in response['error_mapping'].items():
                    try:
                        status_code = int(code)
                        if status_code < 100 or status_code > 599:
                            errors.append(f"Invalid HTTP status code in error_mapping: {code}")
                    except ValueError:
                        errors.append(f"Invalid status code format in error_mapping: {code}")
        
        return errors
    
    def _validate_credential_references(self, credentials: Dict) -> List[str]:
        """Validate credential references."""
        errors = []
        
        for cred_name, cred_config in credentials.items():
            if not isinstance(cred_config, dict):
                errors.append(f"Invalid credential configuration for {cred_name}")
                continue
            
            # Check required fields
            if 'type' not in cred_config:
                errors.append(f"Credential {cred_name} missing type")
            
            # TODO: Check if credential exists in credential manager
            # This would require async method or caching credential names
            
        return errors
    
    def _validate_parameter_mapping(self, mapping: Dict) -> List[str]:
        """Validate parameter mapping syntax."""
        errors = []
        
        # Jinja2 pattern for parameter references
        param_pattern = r'\{\{[^}]+\}\}'
        
        for param_name, param_value in mapping.items():
            if not isinstance(param_value, str):
                errors.append(f"Parameter mapping value must be string: {param_name}")
                continue
            
            # Check for valid Jinja2 syntax
            if '{{' in param_value:
                # Basic syntax check
                if param_value.count('{{') != param_value.count('}}'):
                    errors.append(f"Unbalanced braces in parameter mapping: {param_name}")
                
                # Check for common patterns
                if not re.search(r'\{\{\s*parameters\.\w+', param_value):
                    errors.append(f"Parameter mapping should reference parameters.X: {param_name}")
        
        return errors
    
    def _validate_validation_rules(self, rules: Dict) -> List[str]:
        """Validate validation rules structure."""
        errors = []
        
        for param_name, rule in rules.items():
            if not isinstance(rule, dict):
                errors.append(f"Validation rule must be dictionary: {param_name}")
                continue
            
            # Check type if present
            param_type = rule.get('type', 'string')  # Default to string if not specified
            if 'type' in rule:
                if rule['type'] not in self.VALID_PARAM_TYPES:
                    errors.append(f"Invalid parameter type for {param_name}: {rule['type']}")
                    # Don't validate type-specific rules if type is invalid
                    continue
            
            # Validate string-specific rules only if type is string (or not specified)
            if param_type == 'string':
                if 'format' in rule and rule['format'] not in self.VALID_STRING_FORMATS:
                    errors.append(f"Invalid string format for {param_name}: {rule['format']}")
                
                if 'pattern' in rule:
                    try:
                        re.compile(rule['pattern'])
                    except re.error:
                        errors.append(f"Invalid regex pattern for {param_name}: {rule['pattern']}")
            
            # Validate numeric rules
            if rule.get('type') in ['integer', 'number']:
                numeric_fields = ['min', 'max', 'exclusive_min', 'exclusive_max', 'multiple_of']
                for field in numeric_fields:
                    if field in rule and not isinstance(rule[field], (int, float)):
                        errors.append(f"Invalid {field} value for {param_name}: must be numeric")
        
        return errors
    
    def _validate_transformations(self, transformations: Dict) -> List[str]:
        """Validate transformation configuration."""
        errors = []
        
        for param_name, transforms in transformations.items():
            if not isinstance(transforms, list):
                errors.append(f"Transformations must be list: {param_name}")
                continue
            
            for transform in transforms:
                if transform not in self.VALID_TRANSFORMATIONS:
                    errors.append(f"Invalid transformation for {param_name}: {transform}")
        
        return errors
    
    def _validate_parameter_mappings(self, template: Dict) -> List[str]:
        """Validate that parameter mappings align with intent patterns."""
        errors = []
        
        # Extract parameters from intent patterns
        intent_params = set()
        for pattern in template.get('intent', {}).get('patterns', []):
            params = re.findall(r'\{(\w+)\}', pattern)
            intent_params.update(params)
        
        # Extract parameters from action mapping
        action_params = set()
        for mapping_value in template.get('action', {}).get('parameter_mapping', {}).values():
            if isinstance(mapping_value, str):
                # Extract parameter references
                # Match parameters.name including those with default filters
                params = re.findall(r'parameters\.(\w+)', mapping_value)
                action_params.update(params)
        
        # Also check validation rules for defined parameters
        validation_rules = template.get('action', {}).get('validation_rules', {})
        defined_params = set(validation_rules.keys())
        
        # Parameters with validation rules are considered defined even if not in intent
        all_defined_params = intent_params | defined_params
        
        # Check for mismatches
        missing_in_mapping = intent_params - action_params
        if missing_in_mapping:
            # This is a warning, not an error, as some parameters might be optional
            pass
        
        # Only flag as error if parameter is used but not defined anywhere
        extra_in_mapping = action_params - all_defined_params
        if extra_in_mapping:
            errors.append(f"Parameters in action not defined in intent: {extra_in_mapping}")
        
        return errors
    
    def _has_base_url(self, template: Dict) -> bool:
        """Check if template has a base URL configured."""
        integration = template.get('action', {}).get('integration', {})
        return bool(integration.get('base_url'))
    
    def _has_valid_auth(self, template: Dict) -> bool:
        """Check if template has valid authentication configuration."""
        auth = template.get('action', {}).get('authentication', {})
        if not auth:
            # Some APIs don't require auth
            return True
        
        # Check for auth type
        if 'type' not in auth:
            return False
        
        # Check auth-specific requirements
        auth_type = auth['type']
        if auth_type == 'api_key':
            return 'header' in auth or 'query' in auth
        elif auth_type == 'bearer':
            return 'value' in auth or 'token' in auth
        elif auth_type == 'oauth2':
            return 'client_id' in auth or 'flow' in auth
        
        return True
    
    def _has_parameter_schemas(self, template: Dict) -> bool:
        """Check if template has parameter schemas defined."""
        # For unified templates, validation rules serve as schemas
        return bool(template.get('action', {}).get('validation_rules'))
    
    def _has_response_format(self, template: Dict) -> bool:
        """Check if template has response format configured."""
        return bool(template.get('response', {}).get('crfs'))
    
    def validate_test_mode_compatibility(self, template: Dict) -> ValidationResult:
        """
        Validate that template is compatible with test mode.
        
        Args:
            template: The template to validate
            
        Returns:
            ValidationResult indicating test mode compatibility
        """
        errors = []
        warnings = []
        
        if 'test_data' not in template:
            errors.append("Template has no test_data section for test mode")
        else:
            test_data = template['test_data']
            
            # Check for mock responses
            if 'mock_responses' not in test_data:
                errors.append("test_data missing mock_responses")
            else:
                # Check for at least a success response
                if 'success' not in test_data['mock_responses']:
                    warnings.append("No success mock response defined")
            
            # Check for sample parameters
            if 'sample_parameters' not in test_data:
                warnings.append("No sample parameters provided for testing")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )