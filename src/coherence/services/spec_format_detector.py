"""
Spec format detection service for identifying and validating API specification formats.

The service supports detection of different API specification formats:
- OpenAPI: Standard OpenAPI/Swagger specifications
- FAPI: Financial-grade API specifications
- BAPI: Business API specifications
"""

import json
import logging
from enum import Enum
from typing import Dict, Union

# Set up module logger
logger = logging.getLogger(__name__)


class SpecFormat(str, Enum):
    """Enum for API specification format values."""
    
    OPENAPI = "openapi"  # OpenAPI specification
    FAPI = "fapi"        # Financial-grade API specification
    BAPI = "bapi"        # Business API specification
    UNKNOWN = "unknown"  # Unknown specification format


class SpecFormatDetector:
    """Detects and validates the format of API specifications."""
    
    @staticmethod
    def detect_format(spec: Union[Dict, str]) -> SpecFormat:
        """
        Detect the format of an API specification.
        
        Args:
            spec: The API specification as a dict or JSON string
            
        Returns:
            SpecFormat enum indicating the detected format
            
        Raises:
            ValueError: If the specification cannot be parsed or is invalid
        """
        # Convert string to dict if needed
        if isinstance(spec, str):
            try:
                spec = json.loads(spec)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse API specification JSON: {e}")
                raise ValueError(f"Invalid API specification JSON: {e}") from e
        
        if not isinstance(spec, dict):
            logger.error(f"API specification must be a dictionary, got {type(spec)}")
            raise ValueError(f"API specification must be a dictionary, got {type(spec)}")
            
        # Detect OpenAPI format
        if "openapi" in spec or "swagger" in spec:
            logger.debug("Detected OpenAPI specification")
            return SpecFormat.OPENAPI
            
        # Detect FAPI format
        if any(k in spec for k in ["financial_api", "fapi_version"]) or SpecFormatDetector._has_fapi_indicators(spec):
            logger.debug("Detected FAPI specification")
            return SpecFormat.FAPI
            
        # Detect BAPI format
        if any(k in spec for k in ["business_api", "bapi_version"]) or SpecFormatDetector._has_bapi_indicators(spec):
            logger.debug("Detected BAPI specification")
            return SpecFormat.BAPI
            
        # If no specific format detected, try deeper inspection
        if SpecFormatDetector._looks_like_openapi(spec):
            logger.debug("Specification appears to be OpenAPI based on structure")
            return SpecFormat.OPENAPI
            
        logger.warning("Could not determine API specification format")
        return SpecFormat.UNKNOWN
    
    @staticmethod
    def _has_fapi_indicators(spec: Dict) -> bool:
        """
        Check if the specification has FAPI-specific indicators.
        
        Args:
            spec: The API specification dictionary
            
        Returns:
            bool: True if FAPI indicators are present, False otherwise
        """
        # Check for security profiles (FAPI specific)
        if "security_profiles" in spec:
            return True
            
        # Check for FAPI-specific security requirements
        if "security" in spec:
            security = spec["security"]
            if isinstance(security, list) and any(
                "mtls" in scheme or "financial_api" in scheme for scheme in security if isinstance(scheme, dict)
            ):
                return True
                
        # Check for FAPI endpoints structure
        if "endpoints" in spec and isinstance(spec["endpoints"], list):
            for endpoint in spec["endpoints"]:
                if isinstance(endpoint, dict) and all(k in endpoint for k in ["path", "method"]):
                    # FAPI typically has a different structure than OpenAPI paths
                    return True
                    
        return False
    
    @staticmethod
    def _has_bapi_indicators(spec: Dict) -> bool:
        """
        Check if the specification has BAPI-specific indicators.
        
        Args:
            spec: The API specification dictionary
            
        Returns:
            bool: True if BAPI indicators are present, False otherwise
        """
        # Check for services (BAPI specific)
        if "services" in spec and isinstance(spec["services"], list):
            return True
            
        # Check for BAPI-specific authentication
        if "authentication" in spec and isinstance(spec["authentication"], dict):
            return True
            
        # Check for business models
        if "models" in spec and isinstance(spec["models"], dict):
            return True
            
        return False
    
    @staticmethod
    def _looks_like_openapi(spec: Dict) -> bool:
        """
        Check if the specification structure resembles OpenAPI.
        
        Args:
            spec: The API specification dictionary
            
        Returns:
            bool: True if it looks like OpenAPI, False otherwise
        """
        # Check for common OpenAPI structure elements
        if "paths" in spec and isinstance(spec["paths"], dict):
            return True
            
        if "components" in spec and isinstance(spec["components"], dict):
            return True
            
        # Just having an info object with a title is not enough to identify as OpenAPI
        # as many API specs have this basic structure
        return False