"""
Template vector indexing service for semantic search capabilities.

This module provides functionality to index templates in a vector database (Qdrant)
for semantic searching and similarity matching.
"""

import time
from typing import Any, Dict, List, Optional

import structlog
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.core.config import settings
from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.metrics import (
    EMBEDDING_METRICS,
    VECTOR_SEARCH_LATENCY,
    VECTOR_UPSERT_LATENCY,
)
from src.coherence.core.qdrant_client import QdrantClient
from src.coherence.db.deps import get_db

logger = structlog.get_logger(__name__)


class VectorIndexer:
    """Service for indexing and searching templates in a vector database."""

    def __init__(
        self,
        qdrant_client: Optional[QdrantClient] = None,
        llm_factory: Optional[LLMFactory] = None,
    ):
        """
        Initialize the vector indexer service.

        Args:
            qdrant_client: Optional QdrantClient instance. If not provided, a new one is created.
            llm_factory: Optional LLMFactory instance. If not provided, a new one is created.
        """
        self.qdrant_client = qdrant_client or QdrantClient()
        self.llm_factory = llm_factory or LLMFactory()
        # Use standardized embedding dimensions from config
        self.embedding_dimensions = settings.EMBEDDING_DIMENSION

    async def upsert_template(
        self,
        db: AsyncSession,
        template_id: str,
        template_key: str,
        template_category: str,
        template_body: str,
        template_description: Optional[str] = None,
        index_name: str = "template_idx_global",
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Index a template in the vector database.

        Args:
            db: Database session
            template_id: Unique identifier for the template
            template_key: Template key identifier
            template_category: Template category
            template_body: Template body content
            template_description: Optional template description
            index_name: Name of the vector index to use
            metadata: Additional metadata to store with the vector

        Returns:
            bool: True if indexing was successful, False otherwise
        """
        from src.coherence.core.errors.base import EmbeddingDimensionMismatchError
        
        start_time = time.time()
        logger.info(
            "Starting template vectorization",
            template_id=template_id,
            template_key=template_key,
            template_category=template_category,
            index_name=index_name,
            body_length=len(template_body),
            embedding_model=settings.EMBEDDING_MODEL,
            embedding_dimension=self.embedding_dimensions
        )
        
        try:
            # Ensure the collection exists and has the correct dimensions
            try:
                collection_exists = await self._ensure_template_collection_exists(index_name)
                logger.info(
                    "Template collection check completed",
                    collection_name=index_name,
                    collection_exists=collection_exists
                )
            except EmbeddingDimensionMismatchError as edme:
                # Already a properly formatted EmbeddingDimensionMismatchError, just re-raise
                logger.error(
                    "Cannot proceed with template vectorization due to dimension mismatch",
                    template_id=template_id,
                    index_name=index_name,
                    expected_dimension=edme.expected_dimension,
                    actual_dimension=edme.actual_dimension,
                    error=str(edme),
                    total_time_ms=int((time.time() - start_time) * 1000)
                )
                # Just re-raise the error as it's already properly formatted
                raise

            # Generate embedding for template
            # Combine key, category, description, and body for comprehensive embedding
            embedding_text = f"Template: {template_key}\nCategory: {template_category}\n"
            if template_description:
                embedding_text += f"Description: {template_description}\n"
            embedding_text += f"Body: {template_body}"
            
            logger.info(
                "Prepared text for embedding generation",
                text_length=len(embedding_text),
                template_id=template_id
            )
            
            # Generate embedding
            embedding_start = time.time()
            embedding = await self._generate_embedding(embedding_text)
            embedding_time = time.time() - embedding_start
            
            logger.info(
                "Embedding generation completed",
                template_id=template_id,
                embedding_length=len(embedding),
                embedding_time_ms=int(embedding_time * 1000)
            )
            
            # Create metadata
            if metadata is None:
                metadata = {}
            
            point_metadata = {
                "template_id": template_id,
                "template_key": template_key,
                "template_category": template_category,
                **metadata
            }
            
            # Upsert the template vector
            upsert_start = time.time()
            with VECTOR_UPSERT_LATENCY.labels(collection=index_name).time():
                success = await self.qdrant_client.upsert_template_vectors(
                    collection_name=index_name,
                    points=[{
                        "id": template_id,
                        "vector": embedding,
                        "metadata": point_metadata
                    }]
                )
            upsert_time = time.time() - upsert_start
            
            total_time = time.time() - start_time
            logger.info(
                "Template vectorization completed",
                template_id=template_id,
                index_name=index_name,
                embedding_dimension=len(embedding),
                upsert_success=success,
                upsert_time_ms=int(upsert_time * 1000),
                total_time_ms=int(total_time * 1000)
            )
            
            return True
        except EmbeddingDimensionMismatchError:
            # Let EmbeddingDimensionMismatchError propagate to higher-level handlers
            # This allows for more specific handling at the API endpoint level
            raise
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(
                "Failed to vectorize template",
                template_id=template_id,
                index_name=index_name,
                error=str(e),
                error_type=type(e).__name__,
                total_time_ms=int(total_time * 1000),
                exc_info=True
            )
            return False

    async def upsert_documentation_template(
        self,
        db: AsyncSession,
        template_id: str,
        template_key: str,
        documentation_content: Dict[str, Any],
        index_name: str = "template_idx_global",
        tenant_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Index a documentation template with question-oriented vectors for better search.
        
        Args:
            db: Database session
            template_id: Unique identifier for the template
            template_key: Template key identifier
            documentation_content: Parsed documentation content from JSON
            index_name: Name of the vector index to use
            tenant_id: Optional tenant ID
            metadata: Additional metadata to store with the vector
            
        Returns:
            bool: True if indexing was successful, False otherwise
        """
        try:
            # Extract relevant information from documentation content
            endpoint = documentation_content.get("endpoint", "")
            method = documentation_content.get("method", "")
            summary = documentation_content.get("summary", "")
            description = documentation_content.get("description", "")
            parameters = documentation_content.get("parameters", [])
            tags = documentation_content.get("tags", [])
            
            # Build parameter descriptions
            param_descriptions = []
            for param in parameters:
                param_name = param.get("name", "")
                param_desc = param.get("description", "")
                param_type = param.get("type", "")
                param_required = param.get("required", False)
                
                param_text = f"{param_name} ({param_type})"
                if param_required:
                    param_text += " [required]"
                if param_desc:
                    param_text += f": {param_desc}"
                param_descriptions.append(param_text)
            
            # Create question-oriented embedding texts for documentation
            embedding_texts = [
                f"How to use {endpoint} {method} API",
                f"Documentation for {endpoint} {method}",
                f"What parameters does {endpoint} {method} need",
                f"How to call {endpoint} {method}",
                f"Help with {endpoint} {method}",
                f"{summary}",
                f"{description}",
            ]
            
            # Add intent examples if available
            intent_examples = documentation_content.get("intent_examples", [])
            if intent_examples:
                embedding_texts.extend(intent_examples)
            else:
                # Generate default intent examples if none provided
                embedding_texts.extend([
                    f"How do I use the {endpoint} {method} API?",
                    f"What parameters does {endpoint} {method} need?",
                    f"Show me documentation for {endpoint} {method}",
                    f"Explain how to use {endpoint} {method}",
                    f"What does the {endpoint} {method} API do?",
                    f"Help with {endpoint} {method}",
                    f"Documentation for {endpoint} {method}",
                    f"How to call {endpoint} {method}",
                    f"Examples for {endpoint} {method}"
                ])
            
            if param_descriptions:
                embedding_texts.append(f"Parameters: {', '.join(param_descriptions)}")
            
            if tags:
                embedding_texts.append(f"Tags: {', '.join(tags)}")
            
            # Generate embeddings for all question variations
            embeddings = []
            for text in embedding_texts:
                if text.strip():  # Only process non-empty texts
                    embedding = await self._generate_embedding(text)
                    embeddings.append({
                        "id": f"{template_id}_{len(embeddings)}",
                        "vector": embedding,
                        "metadata": {
                            "template_id": template_id,
                            "template_key": template_key,
                            "template_category": "documentation",
                            "tenant_id": tenant_id,
                            "endpoint": endpoint,
                            "method": method,
                            "text": text,
                            "searchable_text": text,
                            **(metadata or {})
                        }
                    })
            
            # Upsert all embeddings for this documentation template
            success = await self.qdrant_client.upsert_template_vectors(
                collection_name=index_name,
                points=embeddings
            )
            
            logger.info(
                "Documentation template vectorization completed",
                template_id=template_id,
                template_key=template_key,
                num_embeddings=len(embeddings),
                index_name=index_name
            )
            
            return success
            
        except Exception as e:
            logger.error(
                "Failed to vectorize documentation template",
                template_id=template_id,
                template_key=template_key,
                error=str(e),
                exc_info=True
            )
            return False

    async def delete_template(
        self,
        template_id: str,
        index_name: str = "template_idx_global",
    ) -> bool:
        """
        Delete a template from the vector database.

        Args:
            template_id: Unique identifier for the template
            index_name: Name of the vector index

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        try:
            await self.qdrant_client.delete_template_vectors(
                collection_name=index_name,
                points_selector={"ids": [template_id]}
            )
            logger.info(f"Template {template_id} deleted from {index_name}")
            return True
        except Exception as e:
            logger.error(
                f"Failed to delete template {template_id} from {index_name}: {str(e)}",
                exc_info=True
            )
            return False

    async def search_templates(
        self,
        query: str,
        tenant_id: Optional[str] = None,
        pack_id: Optional[str] = None,
        category: Optional[str] = None,
        limit: int = 5,
        score_threshold: float = 0.7,
    ) -> List[Dict[str, Any]]:
        """
        Search for templates semantically similar to a query.

        Args:
            query: The search query text
            tenant_id: Optional tenant ID to filter results
            pack_id: Optional pack ID to filter results
            category: Optional category to filter results
            limit: Maximum number of results to return
            score_threshold: Minimum similarity score threshold

        Returns:
            List of matching templates with their similarity scores
        """
        start_time = time.time()
        logger.info(
            "Starting template search",
            query_length=len(query),
            tenant_id=tenant_id,
            pack_id=pack_id,
            category=category,
            limit=limit,
            score_threshold=score_threshold,
            embedding_model=settings.EMBEDDING_MODEL,
            embedding_dimension=self.embedding_dimensions
        )
        
        try:
            # Generate embedding for query
            embedding_start = time.time()
            logger.info("Generating embedding for search query")
            query_vector = await self._generate_embedding(query)
            embedding_time = time.time() - embedding_start
            
            logger.info(
                "Query embedding generated",
                embedding_length=len(query_vector),
                embedding_time_ms=int(embedding_time * 1000)
            )
            
            # Determine which collections to search based on scope
            collections_to_search = []
            
            # Add tenant-specific collection if applicable
            if tenant_id:
                collections_to_search.append(f"template_idx_{tenant_id}")
            
            # Add pack-specific collection if applicable
            if pack_id:
                collections_to_search.append(f"template_idx_pack_{pack_id}")
            
            # Always include global collection
            collections_to_search.append("template_idx_global")
            
            logger.info(
                "Collections selected for search",
                collections=collections_to_search
            )
            
            all_results = []
            collection_search_stats = []
            
            # Prepare the filter
            filter_dict = {}
            if category:
                filter_dict["template_category"] = {"$eq": category}
                logger.info("Using category filter", category=category)
            
            # Search each collection
            for collection_name in collections_to_search:
                collection_start = time.time()
                try:
                    # Check if collection exists before searching
                    exists = await self._check_collection_exists(collection_name)
                    
                    if exists:
                        # Verify the collection dimensions before searching
                        try:
                            await self.qdrant_client._verify_dimensions(collection_name, self.embedding_dimensions)
                            
                            logger.info(
                                "Searching collection",
                                collection_name=collection_name
                            )
                            
                            with VECTOR_SEARCH_LATENCY.labels(collection=collection_name).time():
                                results = await self.qdrant_client.search_templates(
                                    collection_name=collection_name,
                                    query_vector=query_vector,
                                    filter=filter_dict if filter_dict else None,
                                    limit=limit,
                                    score_threshold=score_threshold
                                )
                            
                            collection_search_time = time.time() - collection_start
                            collection_search_stats.append({
                                "collection": collection_name,
                                "exists": True,
                                "results_count": len(results),
                                "search_time_ms": int(collection_search_time * 1000),
                                "error": None
                            })
                            
                            logger.info(
                                "Collection search completed",
                                collection_name=collection_name,
                                results_count=len(results),
                                search_time_ms=int(collection_search_time * 1000)
                            )
                            
                            all_results.extend(results)
                        except ValueError as dimension_error:
                            # Handle dimension mismatch - log but continue with other collections
                            collection_search_time = time.time() - collection_start
                            logger.warning(
                                "Skipping collection due to dimension mismatch",
                                collection_name=collection_name,
                                error=str(dimension_error),
                                error_type="ValueError",
                                search_time_ms=int(collection_search_time * 1000)
                            )
                            collection_search_stats.append({
                                "collection": collection_name,
                                "exists": True,
                                "results_count": 0,
                                "dimension_mismatch": True,
                                "search_time_ms": int(collection_search_time * 1000),
                                "error": str(dimension_error)
                            })
                    else:
                        logger.info(
                            "Collection does not exist, skipping",
                            collection_name=collection_name
                        )
                        collection_search_stats.append({
                            "collection": collection_name,
                            "exists": False,
                            "results_count": 0,
                            "search_time_ms": int((time.time() - collection_start) * 1000),
                            "error": None
                        })
                        
                except Exception as coll_e:
                    collection_search_time = time.time() - collection_start
                    logger.warning(
                        "Could not search collection",
                        collection_name=collection_name,
                        error=str(coll_e),
                        error_type=type(coll_e).__name__,
                        search_time_ms=int(collection_search_time * 1000)
                    )
                    collection_search_stats.append({
                        "collection": collection_name,
                        "exists": True,  # We assume it exists but failed to search
                        "results_count": 0,
                        "search_time_ms": int(collection_search_time * 1000),
                        "error": str(coll_e)
                    })
                    continue
            
            # Sort by score and take top results
            sorted_results = sorted(
                all_results, 
                key=lambda x: x.get("score", 0),
                reverse=True
            )
            
            final_results = sorted_results[:limit]
            total_time = time.time() - start_time
            
            # Check if we had any dimension mismatch issues
            dimension_mismatches = [s for s in collection_search_stats if s.get("dimension_mismatch", False)]
            if dimension_mismatches:
                logger.warning(
                    "Some collections were skipped due to dimension mismatches",
                    dimension_mismatch_count=len(dimension_mismatches),
                    mismatched_collections=[s["collection"] for s in dimension_mismatches]
                )
            
            logger.info(
                "Template search completed",
                total_results_before_limit=len(sorted_results),
                final_results_count=len(final_results),
                collections_searched=len(collections_to_search),
                collections_succeeded=len(collections_to_search) - len(dimension_mismatches),
                total_time_ms=int(total_time * 1000),
                collection_stats=collection_search_stats
            )
            
            return final_results
        
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(
                "Template search failed",
                query_length=len(query),
                error=str(e),
                error_type=type(e).__name__,
                total_time_ms=int(total_time * 1000),
                tenant_id=tenant_id,
                pack_id=pack_id,
                category=category,
                exc_info=True
            )
            return []

    async def _ensure_template_collection_exists(self, index_name: str) -> bool:
        """
        Ensure that a template collection exists in the vector database.

        Args:
            index_name: Name of the collection to check/create

        Returns:
            bool: True if collection exists or was created, False otherwise
        """
        start_time = time.time()
        logger.info(
            "Ensuring template collection exists",
            collection_name=index_name
        )
        
        try:
            # Check if collection exists
            exists = await self._check_collection_exists(index_name)
            
            if exists:
                # Validate that the existing collection has the correct dimension
                try:
                    # Use the _verify_dimensions method from QdrantClient to validate dimensions
                    actual_dimension = await self.qdrant_client._verify_dimensions(index_name, self.embedding_dimensions)
                    logger.info(
                        "Template collection already exists with correct dimensions",
                        collection_name=index_name,
                        dimensions=self.embedding_dimensions,
                        time_ms=int((time.time() - start_time) * 1000)
                    )
                    return True
                except ValueError as ve:
                    # Dimension mismatch error
                    # Extract actual dimension from the ValueError args if available
                    actual_dimension = ve.args[1] if len(ve.args) > 1 else "unknown"
                    expected_dimension = ve.args[2] if len(ve.args) > 2 else self.embedding_dimensions
                    
                    logger.error(
                        "Existing template collection has incorrect dimensions",
                        collection_name=index_name,
                        expected_dimensions=expected_dimension,
                        actual_dimensions=actual_dimension,
                        error=str(ve),
                        time_ms=int((time.time() - start_time) * 1000)
                    )
                    # We don't automatically fix this as it could lead to data loss
                    # Instead, we propagate the error with more complete information
                    from src.coherence.core.errors.base import (
                        EmbeddingDimensionMismatchError,
                    )
                    raise EmbeddingDimensionMismatchError(
                        expected_dimension=expected_dimension,
                        actual_dimension=actual_dimension,
                        model=settings.EMBEDDING_MODEL,
                        message=f"Collection dimension mismatch for {index_name}: Expected {expected_dimension}-d vectors, found {actual_dimension}-d vectors"
                    ) from ve
            
            # Collection doesn't exist, create it
            create_start = time.time()
            logger.info(
                "Creating template collection",
                collection_name=index_name,
                vector_size=self.embedding_dimensions
            )
            
            # Create the collection
            creation_result = await self.qdrant_client.create_template_collection(
                collection_name=index_name,
                vector_size=self.embedding_dimensions
            )
            
            creation_time = time.time() - create_start
            total_time = time.time() - start_time
            
            logger.info(
                "Template collection created",
                collection_name=index_name,
                creation_success=creation_result,
                creation_time_ms=int(creation_time * 1000),
                total_time_ms=int(total_time * 1000)
            )
            
            return True
        except EmbeddingDimensionMismatchError:
            # Specifically re-raise EmbeddingDimensionMismatchError
            raise
        except ValueError as ve:
            # Propagate other ValueErrors from dimension validation
            total_time = time.time() - start_time
            logger.error(
                "Dimension validation failed for template collection",
                collection_name=index_name,
                error=str(ve),
                error_type="ValueError",
                total_time_ms=int(total_time * 1000)
            )
            raise
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(
                "Failed to ensure template collection",
                collection_name=index_name,
                error=str(e),
                error_type=type(e).__name__,
                total_time_ms=int(total_time * 1000),
                exc_info=True
            )
            return False

    async def _check_collection_exists(self, collection_name: str) -> bool:
        """
        Check if a collection exists in Qdrant.

        Args:
            collection_name: Name of the collection to check

        Returns:
            bool: True if collection exists, False otherwise
        """
        start_time = time.time()
        logger.debug(
            "Checking if collection exists",
            collection_name=collection_name
        )
        
        try:
            collections = await self.qdrant_client.list_collections()
            exists = collection_name in collections
            
            logger.debug(
                "Collection existence check completed",
                collection_name=collection_name,
                exists=exists,
                all_collections_count=len(collections),
                time_ms=int((time.time() - start_time) * 1000)
            )
            
            return exists
        except Exception as e:
            logger.error(
                "Failed to check if collection exists",
                collection_name=collection_name,
                error=str(e),
                error_type=type(e).__name__,
                time_ms=int((time.time() - start_time) * 1000)
            )
            return False

    async def _generate_embedding(self, text: str) -> List[float]:
        """
        Generate an embedding for the given text.

        Args:
            text: The text to generate an embedding for

        Returns:
            List[float]: The embedding vector
            
        Raises:
            EmbeddingDimensionMismatchError: If the generated embedding dimensions 
                don't match the expected dimensions
        """
        from src.coherence.core.errors.base import EmbeddingDimensionMismatchError
        
        logger.info(
            "Generating embedding for vectorization",
            text_length=len(text),
            embedding_model=settings.EMBEDDING_MODEL,
            embedding_dimension=self.embedding_dimensions,
            vector_indexer="VectorIndexer"
        )
        
        try:
            with EMBEDDING_METRICS.labels(provider="openai", model=settings.EMBEDDING_MODEL).time():
                # Get LLM provider and log - now with fail_fast=True
                llm_provider = self.llm_factory.get_default_provider(fail_fast=True)
                logger.info(
                    "LLM provider initialized",
                    provider_type=type(llm_provider).__name__,
                    embedding_model=settings.EMBEDDING_MODEL
                )
                
                # Explicitly pass the dimensions to ensure consistency
                start_time = time.time()
                embedding = await llm_provider.generate_embedding(
                    text=text,
                    dimensions=self.embedding_dimensions
                )
                elapsed_time = time.time() - start_time
                
                # Log successful embedding
                actual_dimension = len(embedding)
                logger.info(
                    "Embedding generated for vectorization",
                    elapsed_time_ms=int(elapsed_time * 1000),
                    embedding_dimension_actual=actual_dimension,
                    embedding_dimension_expected=self.embedding_dimensions,
                    dimensions_match=(actual_dimension == self.embedding_dimensions)
                )
                
                # Fail fast on dimension mismatch instead of returning zero vectors
                if actual_dimension != self.embedding_dimensions:
                    logger.error(
                        "Embedding dimension mismatch",
                        expected_dimension=self.embedding_dimensions,
                        actual_dimension=actual_dimension,
                        embedding_model=settings.EMBEDDING_MODEL,
                        vector_indexer="VectorIndexer"
                    )
                    raise EmbeddingDimensionMismatchError(
                        expected_dimension=self.embedding_dimensions,
                        actual_dimension=actual_dimension,
                        model=settings.EMBEDDING_MODEL,
                        message=f"Expected {self.embedding_dimensions}-d vectors, got {actual_dimension}-d from model {settings.EMBEDDING_MODEL}"
                    )
                
                return embedding
        except EmbeddingDimensionMismatchError:
            # Re-raise the EmbeddingDimensionMismatchError to propagate it
            raise
        except Exception as e:
            logger.error(
                "Failed to generate embedding",
                error=str(e),
                error_type=type(e).__name__,
                embedding_model=settings.EMBEDDING_MODEL,
                embedding_dimension=self.embedding_dimensions,
                exc_info=True
            )
            # Don't return a zero vector as fallback anymore, fail loudly
            raise


# Factory function for FastAPI dependency injection
async def get_vector_indexer(
    db: AsyncSession = Depends(get_db),
) -> VectorIndexer:
    """
    Factory function to create a VectorIndexer instance.

    Args:
        db: Database session from FastAPI dependency

    Returns:
        VectorIndexer: A configured vector indexer instance
    """
    return VectorIndexer()
