"""Service for extracting rich schema information from OpenAPI specs."""

import logging
from typing import Any, Dict, List, Optional, Union

from ..schemas.openapi_enhanced import (
    EnhancedParameterSchema,
    ParameterConstraints,
    SchemaValidationResult
)

logger = logging.getLogger(__name__)


class OpenAPISchemaExtractor:
    """Service for extracting enhanced schema information from OpenAPI specs.
    
    This service provides methods to extract rich parameter schemas with full
    OpenAPI constraint support for better form generation and validation.
    """
    
    @staticmethod
    def extract_enhanced_parameters(
        api_spec: Dict,
        path: str,
        method: str
    ) -> List[EnhancedParameterSchema]:
        """Extract enhanced parameter schemas from OpenAPI operation.
        
        Args:
            api_spec: Full OpenAPI specification
            path: API endpoint path
            method: HTTP method (lowercase)
            
        Returns:
            List of enhanced parameter schemas
        """
        parameters = []
        
        # Get operation from spec
        paths = api_spec.get("paths", {})
        path_item = paths.get(path, {})
        operation = path_item.get(method.lower())
        if not operation:
            return parameters
        
        # Collect parameters from both path level and operation level
        all_parameters = []
        
        # Add path-level parameters (shared by all operations on this path)
        path_params = path_item.get("parameters", [])
        if isinstance(path_params, list):
            all_parameters.extend(path_params)
        
        # Add operation-level parameters (specific to this method)
        operation_params = operation.get("parameters", [])
        if isinstance(operation_params, list):
            all_parameters.extend(operation_params)
        
        # Process all collected parameters
        logger.debug(f"Processing {len(all_parameters)} parameters for {method.upper()} {path}")
        
        # Use a set to track unique parameter names to avoid duplicates
        seen_params = set()
        
        for param in all_parameters:
            # Handle $ref in parameters
            if isinstance(param, dict) and "$ref" in param:
                param = OpenAPISchemaExtractor._resolve_schema_reference(api_spec, param)
            
            if not isinstance(param, dict) or "name" not in param or "in" not in param:
                logger.debug(f"Skipping invalid parameter: {param}")
                continue
                
            param_in = param.get("in")
            param_name = param.get("name")
            
            if param_in not in ["path", "query", "header"]:
                logger.debug(f"Skipping parameter {param_name} with location: {param_in}")
                continue
            
            # Create unique key for this parameter (name + location)
            param_key = f"{param_name}:{param_in}"
            if param_key in seen_params:
                logger.debug(f"Skipping duplicate parameter: {param_name} in {param_in}")
                continue
            
            enhanced_param = OpenAPISchemaExtractor._extract_parameter_schema(
                api_spec, param, param_in
            )
            if enhanced_param:
                parameters.append(enhanced_param)
                seen_params.add(param_key)
                logger.debug(f"Added parameter: {enhanced_param.name} ({enhanced_param.type}) in {enhanced_param.in_location}")
        
        # Process request body parameters (OpenAPI 3.0+)
        request_body = operation.get("requestBody")
        
        # Handle $ref in request body
        if isinstance(request_body, dict) and "$ref" in request_body:
            request_body = OpenAPISchemaExtractor._resolve_schema_reference(api_spec, request_body)
            
        if isinstance(request_body, dict):
            body_params = OpenAPISchemaExtractor._extract_request_body_parameters(
                api_spec, request_body
            )
            parameters.extend(body_params)
            logger.debug(f"Added {len(body_params)} body parameters")
        
        logger.info(f"Total parameters extracted for {method.upper()} {path}: {len(parameters)}")
        return parameters
    
    @staticmethod
    def _extract_parameter_schema(
        api_spec: Dict,
        param: Dict,
        param_in: str
    ) -> Optional[EnhancedParameterSchema]:
        """Extract enhanced schema for a single parameter.
        
        Args:
            api_spec: Full OpenAPI specification
            param: Parameter object from OpenAPI spec
            param_in: Parameter location (path, query, header)
            
        Returns:
            Enhanced parameter schema or None if invalid
        """
        try:
            # Get schema, handling both OpenAPI 3.0 and 2.0 formats
            schema = param.get("schema", {})
            if not schema and "type" in param:
                # OpenAPI 2.0 compatibility
                schema = {
                    "type": param.get("type"),
                    "format": param.get("format"),
                    "enum": param.get("enum"),
                    "default": param.get("default"),
                    "minimum": param.get("minimum"),
                    "maximum": param.get("maximum"),
                    "minLength": param.get("minLength"),
                    "maxLength": param.get("maxLength"),
                    "pattern": param.get("pattern"),
                }
            
            # Resolve schema references
            resolved_schema = OpenAPISchemaExtractor._resolve_schema_reference(
                api_spec, schema
            )
            
            # Extract constraints
            constraints = OpenAPISchemaExtractor._extract_parameter_constraints(
                resolved_schema
            )
            
            # Determine parameter type
            param_type = OpenAPISchemaExtractor._extract_type_from_schema(
                resolved_schema
            )
            
            return EnhancedParameterSchema(
                name=param.get("name"),
                type=param_type,
                in_location=param_in,
                required=param.get("required", param_in == "path"),
                description=param.get("description", ""),
                default=resolved_schema.get("default"),
                example=resolved_schema.get("example") or param.get("example"),
                enum=resolved_schema.get("enum"),
                format=resolved_schema.get("format"),
                pattern=resolved_schema.get("pattern"),
                minimum=constraints.minimum,
                maximum=constraints.maximum,
                exclusive_minimum=constraints.exclusive_minimum,
                exclusive_maximum=constraints.exclusive_maximum,
                multiple_of=resolved_schema.get("multipleOf"),
                min_length=constraints.min_length,
                max_length=constraints.max_length,
                min_items=constraints.min_items,
                max_items=constraints.max_items,
                unique_items=resolved_schema.get("uniqueItems"),
                deprecated=param.get("deprecated", False),
                nullable=resolved_schema.get("nullable", False),
                raw_schema=resolved_schema
            )
            
        except Exception as e:
            logger.warning(f"Failed to extract parameter schema for {param.get('name')}: {e}")
            return None
    
    @staticmethod
    def _extract_request_body_parameters(
        api_spec: Dict,
        request_body: Dict
    ) -> List[EnhancedParameterSchema]:
        """Extract parameters from request body schema.
        
        Args:
            api_spec: Full OpenAPI specification
            request_body: Request body object from OpenAPI spec
            
        Returns:
            List of enhanced parameter schemas
        """
        parameters = []
        
        content = request_body.get("content", {})
        logger.debug(f"Processing request body with {len(content)} media types: {list(content.keys())}")
        
        for media_type, media_obj in content.items():
            if not isinstance(media_obj, dict):
                continue
                
            schema = media_obj.get("schema")
            if not schema:
                continue
                
            # Resolve schema reference
            resolved_schema = OpenAPISchemaExtractor._resolve_schema_reference(
                api_spec, schema
            )
            
            # If the schema is an object with properties, flatten them
            if resolved_schema.get("type") == "object" and "properties" in resolved_schema:
                required_props = resolved_schema.get("required", [])
                props = resolved_schema["properties"]
                logger.debug(f"Processing {len(props)} properties from request body schema")
                
                for prop_name, prop_schema in props.items():
                    prop_resolved = OpenAPISchemaExtractor._resolve_schema_reference(
                        api_spec, prop_schema
                    )
                    
                    constraints = OpenAPISchemaExtractor._extract_parameter_constraints(
                        prop_resolved
                    )
                    
                    param_type = OpenAPISchemaExtractor._extract_type_from_schema(
                        prop_resolved
                    )
                    
                    logger.debug(f"Adding body parameter: {prop_name} ({param_type})")
                    parameters.append(EnhancedParameterSchema(
                        name=prop_name,
                        type=param_type,
                        in_location="body",
                        required=prop_name in required_props,
                        description=prop_resolved.get("description", f"Request body property: {prop_name}"),
                        default=prop_resolved.get("default"),
                        example=prop_resolved.get("example"),
                        enum=prop_resolved.get("enum"),
                        format=prop_resolved.get("format"),
                        pattern=prop_resolved.get("pattern"),
                        minimum=constraints.minimum,
                        maximum=constraints.maximum,
                        exclusive_minimum=constraints.exclusive_minimum,
                        exclusive_maximum=constraints.exclusive_maximum,
                        multiple_of=prop_resolved.get("multipleOf"),
                        min_length=constraints.min_length,
                        max_length=constraints.max_length,
                        min_items=constraints.min_items,
                        max_items=constraints.max_items,
                        unique_items=prop_resolved.get("uniqueItems"),
                        nullable=prop_resolved.get("nullable", False),
                        media_type=media_type,
                        raw_schema=prop_resolved
                    ))
            else:
                # For non-object schemas, add as a single body parameter
                constraints = OpenAPISchemaExtractor._extract_parameter_constraints(
                    resolved_schema
                )
                
                param_type = OpenAPISchemaExtractor._extract_type_from_schema(
                    resolved_schema
                )
                
                parameters.append(EnhancedParameterSchema(
                    name="body",
                    type=param_type,
                    in_location="body",
                    required=request_body.get("required", False),
                    description=request_body.get("description", "Request body"),
                    default=resolved_schema.get("default"),
                    example=resolved_schema.get("example"),
                    enum=resolved_schema.get("enum"),
                    format=resolved_schema.get("format"),
                    pattern=resolved_schema.get("pattern"),
                    minimum=constraints.minimum,
                    maximum=constraints.maximum,
                    exclusive_minimum=constraints.exclusive_minimum,
                    exclusive_maximum=constraints.exclusive_maximum,
                    multiple_of=resolved_schema.get("multipleOf"),
                    min_length=constraints.min_length,
                    max_length=constraints.max_length,
                    min_items=constraints.min_items,
                    max_items=constraints.max_items,
                    unique_items=resolved_schema.get("uniqueItems"),
                    nullable=resolved_schema.get("nullable", False),
                    media_type=media_type,
                    raw_schema=resolved_schema
                ))
            
            # Only process the first content type for now to avoid duplicates
            # In the future, we might want to handle different media types differently
            if parameters:
                break
        
        return parameters
    
    @staticmethod
    def _extract_parameter_constraints(schema: Dict) -> ParameterConstraints:
        """Extract validation constraints from parameter schema.
        
        Args:
            schema: OpenAPI schema object
            
        Returns:
            Parameter constraints object
        """
        return ParameterConstraints(
            minimum=schema.get("minimum"),
            maximum=schema.get("maximum"),
            exclusive_minimum=schema.get("exclusiveMinimum"),
            exclusive_maximum=schema.get("exclusiveMaximum"),
            multiple_of=schema.get("multipleOf"),
            min_length=schema.get("minLength"),
            max_length=schema.get("maxLength"),
            pattern=schema.get("pattern"),
            min_items=schema.get("minItems"),
            max_items=schema.get("maxItems"),
            unique_items=schema.get("uniqueItems"),
            enum=schema.get("enum")
        )
    
    @staticmethod
    def _resolve_schema_reference(api_spec: Dict, schema: Dict) -> Dict:
        """Resolve $ref references in OpenAPI schemas.
        
        Args:
            api_spec: The full OpenAPI specification
            schema: Schema object that may contain $ref
            
        Returns:
            Resolved schema object
        """
        if not isinstance(schema, dict):
            return schema
            
        # Handle $ref references
        if "$ref" in schema:
            ref_path = schema["$ref"]
            
            # Only handle internal references for now
            if ref_path.startswith("#/"):
                # Parse the reference path
                path_parts = ref_path[2:].split("/")  # Remove #/ prefix
                
                # Navigate through the spec
                current = api_spec
                for part in path_parts:
                    if isinstance(current, dict) and part in current:
                        current = current[part]
                    else:
                        # Reference not found, return original schema
                        logger.warning(f"Schema reference not found: {ref_path}")
                        return schema
                
                # Recursively resolve in case the referenced schema has more refs
                return OpenAPISchemaExtractor._resolve_schema_reference(api_spec, current)
        
        # Handle allOf by merging schemas
        if "allOf" in schema and isinstance(schema["allOf"], list):
            merged = {}
            for sub_schema in schema["allOf"]:
                resolved_sub = OpenAPISchemaExtractor._resolve_schema_reference(
                    api_spec, sub_schema
                )
                if isinstance(resolved_sub, dict):
                    # Merge properties
                    if "properties" in resolved_sub:
                        if "properties" not in merged:
                            merged["properties"] = {}
                        merged["properties"].update(resolved_sub["properties"])
                    
                    # Merge required arrays
                    if "required" in resolved_sub:
                        if "required" not in merged:
                            merged["required"] = []
                        merged["required"].extend(resolved_sub["required"])
                    
                    # Update other fields
                    for key, value in resolved_sub.items():
                        if key not in ["properties", "required"]:
                            merged[key] = value
            
            return merged
            
        # Handle anyOf/oneOf by taking the first option for UI simplicity
        if "anyOf" in schema and isinstance(schema["anyOf"], list) and len(schema["anyOf"]) > 0:
            return OpenAPISchemaExtractor._resolve_schema_reference(
                api_spec, schema["anyOf"][0]
            )
            
        if "oneOf" in schema and isinstance(schema["oneOf"], list) and len(schema["oneOf"]) > 0:
            return OpenAPISchemaExtractor._resolve_schema_reference(
                api_spec, schema["oneOf"][0]
            )
        
        return schema
    
    @staticmethod
    def _extract_type_from_schema(schema: Dict) -> str:
        """Extract a simple type string from an OpenAPI schema.
        
        Args:
            schema: OpenAPI schema object
            
        Returns:
            Simple type string for UI consumption
        """
        if not isinstance(schema, dict):
            return "string"
            
        schema_type = schema.get("type", "string")
        
        # Handle specific formats
        if schema_type == "string":
            format_type = schema.get("format")
            if format_type in ["date", "date-time", "email", "uri", "uuid", "password", "byte", "binary"]:
                return format_type
        elif schema_type == "number":
            format_type = schema.get("format")
            if format_type == "float":
                return "float"
            elif format_type == "double":
                return "double"
        elif schema_type == "integer":
            format_type = schema.get("format")
            if format_type in ["int32", "int64"]:
                return format_type
                
        # Handle enums
        if "enum" in schema:
            return "enum"
            
        return schema_type
    
    @staticmethod
    def validate_parameter_value(
        value: Any,
        parameter: EnhancedParameterSchema
    ) -> SchemaValidationResult:
        """Validate a parameter value against its schema.
        
        Args:
            value: The value to validate
            parameter: The parameter schema
            
        Returns:
            Validation result
        """
        result = SchemaValidationResult(valid=True)
        
        # Check required
        if parameter.required and (value is None or value == ""):
            result.add_error(f"Parameter '{parameter.name}' is required")
            return result
        
        # Skip validation for empty optional values
        if not parameter.required and (value is None or value == ""):
            return result
        
        # Type-specific validation
        if parameter.type in ["number", "integer", "float", "double", "int32", "int64"]:
            try:
                num_value = float(value) if parameter.type in ["number", "float", "double"] else int(value)
                
                # Numeric constraints
                if parameter.minimum is not None and num_value < parameter.minimum:
                    result.add_error(f"Value {num_value} is less than minimum {parameter.minimum}")
                
                if parameter.maximum is not None and num_value > parameter.maximum:
                    result.add_error(f"Value {num_value} is greater than maximum {parameter.maximum}")
                
                if parameter.exclusive_minimum is not None and num_value <= parameter.exclusive_minimum:
                    result.add_error(f"Value {num_value} must be greater than {parameter.exclusive_minimum}")
                
                if parameter.exclusive_maximum is not None and num_value >= parameter.exclusive_maximum:
                    result.add_error(f"Value {num_value} must be less than {parameter.exclusive_maximum}")
                
                if parameter.multiple_of is not None and num_value % parameter.multiple_of != 0:
                    result.add_error(f"Value {num_value} is not a multiple of {parameter.multiple_of}")
                    
            except (ValueError, TypeError):
                result.add_error(f"Value '{value}' is not a valid {parameter.type}")
        
        elif parameter.type == "string":
            str_value = str(value)
            
            # String length constraints
            if parameter.min_length is not None and len(str_value) < parameter.min_length:
                result.add_error(f"String length {len(str_value)} is less than minimum {parameter.min_length}")
            
            if parameter.max_length is not None and len(str_value) > parameter.max_length:
                result.add_error(f"String length {len(str_value)} is greater than maximum {parameter.max_length}")
            
            # Pattern validation
            if parameter.pattern:
                import re
                try:
                    if not re.match(parameter.pattern, str_value):
                        result.add_error(f"Value '{str_value}' does not match pattern '{parameter.pattern}'")
                except re.error:
                    result.add_warning(f"Invalid regex pattern: '{parameter.pattern}'")
        
        # Enum validation
        if parameter.enum and value not in parameter.enum:
            result.add_error(f"Value '{value}' is not in allowed values: {parameter.enum}")
        
        return result