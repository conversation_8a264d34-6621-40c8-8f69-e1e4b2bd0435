"""
Audit logging service for tracking administrative actions.

This module provides functionality to log actions taken within the system,
particularly for sensitive administrative operations.
"""

import json
import uuid
from typing import Any, Dict, Optional, Union

import structlog
from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.models.audit_log import AuditLog

logger = structlog.get_logger(__name__)


class AuditService:
    """
    Service for recording audit log entries.

    Creates structured audit logs for system actions, with focus on
    administrative operations and security-sensitive events.
    """

    def __init__(self, db: AsyncSession):
        """Initialize with database session for persistence.

        Args:
            db: Database session for storing audit logs
        """
        self.db = db

    async def log_admin_action(
        self,
        action: str,
        tenant_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> AuditLog:
        """Log an administrative action.

        Args:
            action: Description of the action being performed
            tenant_id: ID of the tenant context
            user_id: ID of the user performing the action (if available)
            details: Additional details about the action

        Returns:
            Created audit log entry
        """
        try:
            # Create new audit log entry
            audit_log = AuditLog(
                tenant_id=tenant_id,
                action=action,
                user_id=user_id,
                details=details,
            )

            # Add to database
            self.db.add(audit_log)
            await self.db.flush()

            # Log to console as well (useful for debugging and monitoring)
            log_ctx = {
                "audit_log_id": str(audit_log.id),
                "tenant_id": str(tenant_id),
                "action": action,
            }

            if user_id:
                log_ctx["user_id"] = str(user_id)

            if details:
                # Avoid logging sensitive information
                safe_details = self._sanitize_details(details)
                log_ctx["details"] = safe_details

            logger.info("Admin action logged", **log_ctx)

            return audit_log

        except Exception as e:
            # Capture the full exception details for better debugging
            import traceback

            error_trace = traceback.format_exc()

            logger.error(
                "Failed to log admin action",
                error=str(e),
                error_trace=error_trace,
                action=action,
                tenant_id=str(tenant_id) if tenant_id else None,
            )
            # Intentionally not raising - we don't want audit logging failures
            # to break the actual admin operation
            return None

    async def log_admin_request(
        self,
        request: Request,
        tenant_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
    ) -> Optional[AuditLog]:
        """Log an administrative API request.

        Args:
            request: FastAPI request object
            tenant_id: ID of the tenant context
            user_id: ID of the user making the request (if available)

        Returns:
            Created audit log entry or None if logging failed
        """
        try:
            # Extract request information
            path = request.url.path
            method = request.method

            # Create action description
            action = f"API_{method}_{path}"

            # Extract request parameters for the audit details
            # For GET requests, include query parameters
            # For other methods, include JSON body if available
            details = {}

            if method == "GET":
                # Add query parameters
                details["query_params"] = dict(request.query_params)
            else:
                # For POST, PUT, DELETE, try to get the body
                try:
                    # We can't read the body directly as it might have been consumed already
                    # So we check if the "body" attribute exists (sometimes FastAPI attaches it)
                    if hasattr(request, "body"):
                        body = request.body
                        if body:
                            # Try to parse as JSON
                            try:
                                body_json = json.loads(body)
                                # Sanitize the body to avoid logging sensitive information
                                details["body"] = self._sanitize_body(body_json)
                            except json.JSONDecodeError:
                                # Not JSON, just log that a body was present
                                details["body"] = "<non-JSON body>"
                except Exception:
                    # If anything fails, just skip body logging
                    pass

            # Add client info
            details["client"] = {
                "host": request.client.host if request.client else "unknown",
                "headers": {
                    k: v
                    for k, v in request.headers.items()
                    if k.lower() not in {"authorization", "x-api-key", "cookie"}
                },
            }

            # Log the request
            return await self.log_admin_action(
                action=action,
                tenant_id=tenant_id,
                user_id=user_id,
                details=details,
            )

        except Exception as e:
            # Capture the full exception details for better debugging
            import traceback

            error_trace = traceback.format_exc()

            logger.error(
                "Failed to log admin request",
                error=str(e),
                error_trace=error_trace,
                path=request.url.path,
                method=request.method,
                tenant_id=str(tenant_id) if tenant_id else None,
                user_id=str(user_id) if user_id else None,
            )
            return None

    def _sanitize_details(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """Remove sensitive information from details.

        Args:
            details: Dictionary with details to sanitize

        Returns:
            Sanitized details dictionary
        """
        if not details:
            return {}

        # Create a copy to avoid modifying the original
        sanitized = details.copy()

        # List of sensitive keys to mask
        sensitive_keys = [
            "password",
            "secret",
            "token",
            "key",
            "credential",
            "api_key",
            "auth",
            "jwt",
            "authorization",
            "access_token",
            "refresh_token",
        ]

        # Recursively sanitize nested dictionaries
        def sanitize_dict(d: Dict[str, Any]) -> Dict[str, Any]:
            for k, v in list(d.items()):
                # Check if this is a sensitive key
                if any(sensitive in k.lower() for sensitive in sensitive_keys):
                    if v:  # Only mask if there's actually a value
                        d[k] = "********"
                elif isinstance(v, dict):
                    # Recursively sanitize nested dict
                    d[k] = sanitize_dict(v)
                elif isinstance(v, list):
                    # Handle lists of dictionaries
                    if v and isinstance(v[0], dict):
                        d[k] = [
                            sanitize_dict(item) if isinstance(item, dict) else item
                            for item in v
                        ]
            return d

        return sanitize_dict(sanitized)

    def _sanitize_body(
        self, body: Union[Dict[str, Any], list]
    ) -> Union[Dict[str, Any], list]:
        """Sanitize request body to remove sensitive information.

        Args:
            body: Request body to sanitize

        Returns:
            Sanitized body
        """
        if isinstance(body, dict):
            return self._sanitize_details(body)
        elif isinstance(body, list) and body and isinstance(body[0], dict):
            return [
                self._sanitize_details(item) if isinstance(item, dict) else item
                for item in body
            ]
        return body


async def get_audit_service(db: AsyncSession) -> AuditService:
    """Factory function for creating an AuditService.

    Args:
        db: Database session

    Returns:
        Initialized AuditService
    """
    return AuditService(db)
