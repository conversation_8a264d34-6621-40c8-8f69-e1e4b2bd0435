"""
Code validation service for generated Python code.

This module provides functionality to validate the
syntax and structure of generated Python code.
"""

import ast
from typing import Optional, Tuple

import structlog

logger = structlog.get_logger(__name__)


class CodeValidationError(Exception):
    """Exception raised when code validation fails."""

    pass


class CodeValidator:
    """
    Validator for Python code syntax and structure.

    Validates that generated code is syntactically correct
    and has the expected structure and imports.
    """

    def __init__(self):
        """Initialize the code validator."""
        pass

    def validate_python_syntax(self, code: str) -> Tuple[bool, Optional[str]]:
        """
        Validate that the Python code has correct syntax.

        Args:
            code: Python code to validate

        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Parse the code into an AST
            ast.parse(code)
            return True, None
        except SyntaxError as e:
            error_message = (
                f"Syntax error at line {e.lineno}, column {e.offset}: {e.msg}"
            )
            logger.warning("Code validation failed", error=error_message)
            return False, error_message
        except Exception as e:
            error_message = f"Failed to parse code: {str(e)}"
            logger.warning("Code validation failed", error=error_message)
            return False, error_message

    def validate_action_class(
        self, code: str, expected_class_name: str
    ) -> Tuple[bool, Optional[str]]:
        """
        Validate that the code defines an action class with expected structure.

        Args:
            code: Python code to validate
            expected_class_name: Expected name of the action class

        Returns:
            Tuple of (is_valid, error_message)
        """
        # First validate syntax
        syntax_valid, syntax_error = self.validate_python_syntax(code)
        if not syntax_valid:
            return False, f"Invalid Python syntax: {syntax_error}"

        try:
            # Parse into AST
            tree = ast.parse(code)

            # Find class definition
            class_def = None
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef) and node.name == expected_class_name:
                    class_def = node
                    break

            if not class_def:
                return False, f"Class '{expected_class_name}' not found in code"

            # Check for required methods
            has_init = False
            has_execute = False

            for node in class_def.body:
                if isinstance(node, ast.FunctionDef):
                    if node.name == "__init__":
                        has_init = True
                    elif node.name == "execute":
                        has_execute = True

            if not has_init:
                return (
                    False,
                    f"Class '{expected_class_name}' is missing __init__ method",
                )

            if not has_execute:
                return False, f"Class '{expected_class_name}' is missing execute method"

            # Check for required imports
            required_imports = ["aiohttp", "typing"]
            found_imports = set()

            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for name in node.names:
                        found_imports.add(name.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        for name in node.names:
                            found_imports.add(f"{node.module}.{name.name}")

            missing_imports = [
                imp
                for imp in required_imports
                if not any(imp in found_imp for found_imp in found_imports)
            ]

            if missing_imports:
                return False, f"Missing required imports: {', '.join(missing_imports)}"

            return True, None

        except Exception as e:
            error_message = f"Failed to validate action class: {str(e)}"
            logger.warning("Action class validation failed", error=error_message)
            return False, error_message

    def check_importability(
        self, code: str, module_name: str = "generated_action"
    ) -> Tuple[bool, Optional[str]]:
        """
        Advanced check to validate that code can be imported as a module.

        This is more thorough than syntax validation, as it catches issues
        with imports and other runtime problems.

        Args:
            code: Python code to validate
            module_name: Name to use for the temporary module

        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # First validate syntax
            syntax_valid, syntax_error = self.validate_python_syntax(code)
            if not syntax_valid:
                return False, f"Invalid Python syntax: {syntax_error}"

            # Compile the code to check for more issues
            compile(code, "<string>", "exec")

            # More thorough checks could be done here, such as:
            # - Creating a temporary file
            # - Attempting to import it using importlib
            # - Checking for specific class attributes

            # For now, we'll just return the syntax check result
            return True, None

        except Exception as e:
            error_message = f"Code cannot be imported: {str(e)}"
            logger.warning("Import validation failed", error=error_message)
            return False, error_message
