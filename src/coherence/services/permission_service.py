from enum import Enum
from typing import Optional, Set


class CoherencePermission(Enum):
    # Workflow Permissions
    WORKFLOW_CREATE = "workflow:create"
    WORKFLOW_READ = "workflow:read"
    WORKFLOW_UPDATE = "workflow:update"
    WORKFLOW_DELETE = "workflow:delete"
    WORKFLOW_EXECUTE = "workflow:execute"

    # Template Permissions
    TEMPLATE_CREATE = "template:create"
    TEMPLATE_READ = "template:read"
    TEMPLATE_UPDATE = "template:update"
    TEMPLATE_DELETE = "template:delete"
    TEMPLATE_READ_VERSIONS = "template:read_versions"

    # Integration Permissions
    INTEGRATION_CREATE = "integration:create"
    INTEGRATION_READ = "integration:read"
    INTEGRATION_UPDATE = "integration:update"
    INTEGRATION_DELETE = "integration:delete"
    INTEGRATION_MANAGE_CREDENTIALS = "integration:manage_credentials"

    # Tenant/Organization Permissions (Note: TENANT_* permissions are for sysadmin context on tenant table)
    ORGANIZATION_VIEW_OWN_DASHBOARD = "organization:view_own_dashboard"
    TENANT_LIST_ALL = "tenant:list_all"
    TENANT_CREATE = "tenant:create"
    TENANT_EDIT = "tenant:edit"
    TENANT_DELETE = "tenant:delete"

    # API Key Permissions
    APIKEY_CREATE = "apikey:create"
    APIKEY_READ = "apikey:read"
    APIKEY_DELETE = "apikey:delete"

    # System Settings Permissions
    SYSTEM_SETTINGS_VIEW = "system_settings:view"
    SYSTEM_SETTINGS_EDIT = "system_settings:edit"

    # System Health Permissions
    SYSTEM_HEALTH_VIEW = "system_health:view"

    # Audit Log Permissions
    AUDITLOG_VIEW = "auditlog:view"
    
    # Chat Permissions
    CHAT_ACCESS = "chat:access"

    @classmethod
    def all_permissions(cls) -> Set[str]:
        return {permission.value for permission in cls}

class PermissionService:
    def __init__(self):
        # Mapping from Clerk role (simplified) to Coherence permissions
        self._role_permissions_map = {
            "admin": {
                CoherencePermission.WORKFLOW_CREATE.value,
                CoherencePermission.WORKFLOW_READ.value,
                CoherencePermission.WORKFLOW_UPDATE.value,
                CoherencePermission.WORKFLOW_DELETE.value,
                CoherencePermission.TEMPLATE_CREATE.value,
                CoherencePermission.TEMPLATE_READ.value,
                CoherencePermission.TEMPLATE_UPDATE.value,
                CoherencePermission.TEMPLATE_DELETE.value,
                CoherencePermission.TEMPLATE_READ_VERSIONS.value,
                CoherencePermission.INTEGRATION_CREATE.value,
                CoherencePermission.INTEGRATION_READ.value,
                CoherencePermission.INTEGRATION_UPDATE.value,
                CoherencePermission.INTEGRATION_DELETE.value,
                CoherencePermission.INTEGRATION_MANAGE_CREDENTIALS.value,
                CoherencePermission.APIKEY_CREATE.value,
                CoherencePermission.APIKEY_READ.value,
                CoherencePermission.APIKEY_DELETE.value,
                CoherencePermission.ORGANIZATION_VIEW_OWN_DASHBOARD.value,
                CoherencePermission.AUDITLOG_VIEW.value,
                CoherencePermission.CHAT_ACCESS.value,
            },
            "member": {
                CoherencePermission.WORKFLOW_READ.value,
                CoherencePermission.WORKFLOW_EXECUTE.value,
                CoherencePermission.TEMPLATE_READ.value,
                CoherencePermission.ORGANIZATION_VIEW_OWN_DASHBOARD.value,
                CoherencePermission.CHAT_ACCESS.value,
            },
        }

        self._system_admin_permissions = {
            CoherencePermission.TENANT_LIST_ALL.value,
            CoherencePermission.TENANT_CREATE.value,
            CoherencePermission.TENANT_EDIT.value,
            CoherencePermission.TENANT_DELETE.value,
            CoherencePermission.ORGANIZATION_VIEW_OWN_DASHBOARD.value,
            CoherencePermission.SYSTEM_SETTINGS_VIEW.value,
            CoherencePermission.SYSTEM_SETTINGS_EDIT.value,
            CoherencePermission.SYSTEM_HEALTH_VIEW.value,
            CoherencePermission.WORKFLOW_READ.value,
            CoherencePermission.WORKFLOW_CREATE.value,
            CoherencePermission.WORKFLOW_UPDATE.value,
            CoherencePermission.WORKFLOW_DELETE.value,
        }
        # System admins also get all regular permissions
        self._system_admin_permissions.update(CoherencePermission.all_permissions())


    def get_coherence_permissions(
        self, clerk_org_role: Optional[str], is_system_admin: bool
    ) -> Set[str]:
        """
        Calculates the set of Coherence-specific permissions for a user
        based on their Clerk organization role and system admin status.

        Args:
            clerk_org_role: The user's role in the Clerk organization (e.g., "admin", "member").
                            Clerk might provide this as "org:admin", "org:member".
                            This service expects simplified role names.
            is_system_admin: Boolean indicating if the user has system administrator privileges.

        Returns:
            A set of Coherence permission strings.
        """
        permissions: Set[str] = set()

        if is_system_admin:
            # System admins get all defined system permissions and all other permissions
            permissions.update(self._system_admin_permissions)
            # Ensure all permissions are granted, covering any not explicitly in system_admin_permissions
            permissions.update(CoherencePermission.all_permissions())
            # Add the wildcard permission "system:*" for convenience
            permissions.add("system:*")


        # Add permissions based on Clerk organization role
        # These are additive to system admin permissions if the user is both
        simplified_role = None
        if clerk_org_role:
            if "admin" in clerk_org_role.lower(): # Handles "admin", "org:admin", etc.
                simplified_role = "admin"
            elif "member" in clerk_org_role.lower(): # Handles "member", "org:member", etc.
                simplified_role = "member"

        if simplified_role and simplified_role in self._role_permissions_map:
            permissions.update(self._role_permissions_map[simplified_role])
        
        # Fallback: if a system admin has no specific org role, they still have their system perms.
        # If a regular user has an unrecognized org role, they get no org-level perms from this mapping.

        return permissions

# Example Usage (for testing or direct invocation if needed):
# if __name__ == "__main__":
#     service = PermissionService()
#
#     # Test case 1: Org Admin, not System Admin
#     org_admin_perms = service.get_coherence_permissions(clerk_org_role="admin", is_system_admin=False)
#     print(f"Org Admin Permissions: {org_admin_perms}")
#
#     # Test case 2: Org Member, not System Admin
#     org_member_perms = service.get_coherence_permissions(clerk_org_role="org:member", is_system_admin=False)
#     print(f"Org Member Permissions: {org_member_perms}")
#
#     # Test case 3: System Admin, no specific org role (might happen if they are outside an org context)
#     sys_admin_no_role_perms = service.get_coherence_permissions(clerk_org_role=None, is_system_admin=True)
#     print(f"System Admin (no org role) Permissions: {sys_admin_no_role_perms}")
#
#     # Test case 4: System Admin and also an Org Admin in a specific org
#     sys_admin_and_org_admin_perms = service.get_coherence_permissions(clerk_org_role="admin", is_system_admin=True)
#     print(f"System Admin and Org Admin Permissions: {sys_admin_and_org_admin_perms}")
#     # Expected: Should include all permissions
#
#     # Test case 5: Unknown role, not System Admin
#     unknown_role_perms = service.get_coherence_permissions(clerk_org_role="viewer", is_system_admin=False)
#     print(f"Unknown Role Permissions: {unknown_role_perms}") # Expected: empty set or minimal if any default
#
#     # Test case 6: No role, not System Admin
#     no_role_perms = service.get_coherence_permissions(clerk_org_role=None, is_system_admin=False)
#     print(f"No Role Permissions: {no_role_perms}") # Expected: empty set
#
#     print(f"All defined permissions: {CoherencePermission.all_permissions()}")