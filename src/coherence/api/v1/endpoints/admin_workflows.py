from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

# Corrected imports based on file review
from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,  # Assuming ClerkAuthDetails is the model returned by get_clerk_auth_details
    RequirePermission,
    get_clerk_auth_details,
)
from src.coherence.crud.crud_workflow import crud_workflow
from src.coherence.db.deps import get_db as get_db_session
from src.coherence.schemas.admin_workflow import (
    AdminWorkflow,
    AdminWorkflowCreate,
    AdminWorkflowUpdate,
)
from src.coherence.services.permission_service import CoherencePermission


# If ClerkAuthDetails from auth.py doesn't include tenant_id directly,
# but get_clerk_auth_details populates request.state.tenant_id,
# the endpoints would need access to request.state or ClerkAuthDetails needs to be adapted.
# For now, we assume ClerkAuthDetails might be structured to include it or be adapted like this:
class EndpointClerkAuthDetails(ClerkAuthDetails): # Temporary adaptation if needed
    tenant_id: Optional[UUID] = None
    # This is illustrative. Ideally, the model from auth.py is sufficient
    # or request.state is used directly in endpoints if tenant_id isn't on the auth model.
    # Based on auth.py, get_clerk_auth_details sets request.state.tenant_id
    # and returns ClerkAuthDetails which has org_id. We should use org_id as the tenant_id
    # if that's the mapping, or retrieve from request.state.
    # For this edit, we will assume current_user.org_id is the clerk_org_id that maps to tenant_id
    # and that crud functions expect the actual tenant_id UUID after lookup.
    # The get_clerk_auth_details actually returns a ClerkAuthDetails that doesn't have tenant_id.
    # The tenant_id for RLS is derived and put into request.state.tenant_id.
    # The crud layer expects tenant_id (the UUID). 
    # The endpoint will need the request object to get request.state.tenant_id.


router = APIRouter()

@router.get(
    "/",
    response_model=List[AdminWorkflow],
    dependencies=[Depends(RequirePermission(CoherencePermission.WORKFLOW_READ))],
    summary="List Workflows for Tenant",
    description="Retrieve a list of workflows associated with the authenticated user's tenant."
)
async def list_workflows(
    request: Request, # Added request
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details), # Use ClerkAuthDetails from auth.py
    skip: int = 0,
    limit: int = 100,
):
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        # If user is system admin and tenant_id is not set, it might be an issue or intended behavior
        # For non-system admins, tenant_id should generally be present if org_id was found.
        if not current_user.is_system_admin:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant ID not found for user.")
        # Potentially allow system admins to proceed without tenant_id for some listing actions (e.g. list ALL workflows)
        # However, crud_workflow.get_multi_by_tenant requires a tenant_id. This needs clarification for sys admins.
        # For now, strictly require tenant_id for this endpoint.
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant context not established.")

    workflows = await crud_workflow.get_multi_by_tenant(db, tenant_id=tenant_id, skip=skip, limit=limit)
    return workflows

@router.post(
    "/",
    response_model=AdminWorkflow,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(RequirePermission(CoherencePermission.WORKFLOW_CREATE))],
    summary="Create New Workflow",
    description="Create a new workflow for the authenticated user's tenant."
)
async def create_workflow(
    *,
    request: Request, # Added request
    db: AsyncSession = Depends(get_db_session),
    workflow_in: AdminWorkflowCreate,
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details),
):
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        if not current_user.is_system_admin: # Allow sys admin to create if tenant_id is part of obj_in or other logic
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant ID not found for user.")
        # This specific CRUD expects tenant_id. If a sys admin can create for any tenant, 
        # this endpoint/CRUD needs adjustment or tenant_id must be provided in workflow_in for sys admins.
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant context not established for creation.")

    workflow = await crud_workflow.create_with_tenant(db, obj_in=workflow_in, tenant_id=tenant_id)
    return workflow

@router.get(
    "/{workflow_id}",
    response_model=AdminWorkflow,
    dependencies=[Depends(RequirePermission(CoherencePermission.WORKFLOW_READ))],
    summary="Get Workflow by ID",
    description="Retrieve a specific workflow by its ID, ensuring it belongs to the user's tenant."
)
async def get_workflow(
    *,
    request: Request, # Added request
    db: AsyncSession = Depends(get_db_session),
    workflow_id: UUID,
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details),
):
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        if not current_user.is_system_admin:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant ID not found for user.")
        # Sys admin might be able to get any workflow by ID, but CRUD needs tenant_id for verification.
        # This implies sys admin might still need a tenant context or CRUD needs sys admin bypass.
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant context not established.")

    workflow = await crud_workflow.get_by_id_and_tenant(db, id=workflow_id, tenant_id=tenant_id)
    if not workflow:
        # If system admin and workflow not found with tenant_id, it might truly not exist OR belong to another tenant.
        # If sys admin should bypass tenant_id check for GET, CRUD needs adjustment.
        # For now, 404 is correct if not found under assumed tenant_id.
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Workflow not found.")
    return workflow

@router.put(
    "/{workflow_id}",
    response_model=AdminWorkflow,
    dependencies=[Depends(RequirePermission(CoherencePermission.WORKFLOW_UPDATE))],
    summary="Update Workflow",
    description="Update an existing workflow by its ID."
)
async def update_workflow(
    *,
    request: Request, # Added request
    db: AsyncSession = Depends(get_db_session),
    workflow_id: UUID,
    workflow_in: AdminWorkflowUpdate,
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details),
):
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        if not current_user.is_system_admin:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant ID not found for user.")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant context not established.")

    db_workflow = await crud_workflow.get_by_id_and_tenant(db, id=workflow_id, tenant_id=tenant_id)
    if not db_workflow:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Workflow not found.")
    
    updated_workflow = await crud_workflow.update(db, db_obj=db_workflow, obj_in=workflow_in)
    return updated_workflow

@router.delete(
    "/{workflow_id}",
    response_model=AdminWorkflow, 
    dependencies=[Depends(RequirePermission(CoherencePermission.WORKFLOW_DELETE))],
    summary="Delete Workflow",
    description="Delete a workflow by its ID."
)
async def delete_workflow(
    *,
    request: Request, # Added request
    db: AsyncSession = Depends(get_db_session),
    workflow_id: UUID,
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details),
):
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        if not current_user.is_system_admin:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant ID not found for user.")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant context not established.")

    deleted_workflow = await crud_workflow.remove_by_id_and_tenant(db, id=workflow_id, tenant_id=tenant_id)
    if not deleted_workflow:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Workflow not found.")
    return deleted_workflow 