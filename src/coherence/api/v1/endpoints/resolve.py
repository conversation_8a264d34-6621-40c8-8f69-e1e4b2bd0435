"""
Endpoints for resolving intents from natural language input.
"""

import json
import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import UUID4
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.coherence.api.v1.dependencies.auth import (
    get_clerk_auth_details,
)
from src.coherence.api.v1.utils.auth_utils import is_system_admin
from src.coherence.db.deps import get_db
from src.coherence.intent_pipeline.orchestrator import (
    ChatOrchestrator,
    get_chat_orchestrator,
)
from src.coherence.models.tenant import Tenant
from src.coherence.schemas.request import ContinueRequest, ResolveRequest
from src.coherence.schemas.response import ResolveResponse
from src.coherence.core.redis_client import get_redis_client

router = APIRouter()
logger = logging.getLogger(__name__)


async def get_tenant_with_admin_bypass(
    request: Request,
    clerk_auth = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
) -> Tenant:
    """
    Get tenant either from API key or from Clerk JWT for system admins.
    
    This function allows bypassing the API key requirement for system administrators.
    Regular users must still provide a valid API key.
    
    Args:
        request: The FastAPI request
        clerk_auth: Clerk authentication details dependency
        db: Database session
        
    Returns:
        Tenant: The tenant for the request
        
    Raises:
        HTTPException: If tenant can't be retrieved
    """
    # First, check if the user is a system admin - if so, we can bypass API key check
    if is_system_admin(request):
        logger.info(f"System admin bypass for API key: {clerk_auth.user_id}")
        
        # If system admin has a specific organization context, try to get that tenant
        if clerk_auth.org_id:
            logger.info(f"Getting tenant for system admin with org_id: {clerk_auth.org_id}")
            tenant_result = await db.execute(
                select(Tenant).where(Tenant.clerk_org_id == clerk_auth.org_id)
            )
            tenant = tenant_result.scalar_one_or_none()
            
            if tenant:
                logger.info(f"Found tenant {tenant.id} for system admin organization {clerk_auth.org_id}")
                request.state.tenant = tenant
                request.state.tenant_id = tenant.id
                request.state.rls_tenant_id = str(tenant.id)
                return tenant
        
        # If no specific org or tenant not found, try to get a default tenant
        logger.info("Getting default tenant for system admin with no specific organization")
        result = await db.execute(select(Tenant).limit(1))
        tenant = result.scalar_one_or_none()
        
        if tenant:
            logger.info(f"Using default tenant {tenant.id} for system admin")
            request.state.tenant = tenant
            request.state.tenant_id = tenant.id
            request.state.rls_tenant_id = str(tenant.id)
            return tenant
            
        # If no tenant is found, raise error
        logger.error("No tenants found for system admin")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No tenants available for API calls",
        )
    
    # If not a system admin, check for API key
    # Extract the API key from headers
    api_key = request.headers.get("X-API-Key")
    if not api_key:
        logger.warning("API key required: user is not a system admin and no API key provided")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required",
            headers={"WWW-Authenticate": "ApiKey"},
        )
    
    # Use the existing API key validation function but without logging the deprecation warning
    # This is a simplified version of get_tenant_from_api_key
    # Hash the API key for lookup
    import hashlib

    from sqlalchemy import func

    from src.coherence.models.tenant import OrganizationAPIKey
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    
    # Check for master system admin key
    from src.coherence.core.config import settings
    if settings.SYSTEM_ADMIN_API_KEY and api_key == settings.SYSTEM_ADMIN_API_KEY:
        logger.info("System Admin Master Key used")
        request.state.is_system_admin = True
        first_tenant_res = await db.execute(select(Tenant).limit(1))
        first_tenant = first_tenant_res.scalar_one_or_none()
        if not first_tenant:
            logger.error("System Admin Key used, but no tenants exist for context.")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
                detail="System Admin Key used, but no tenants exist for context."
            )
        request.state.tenant = first_tenant
        request.state.tenant_id = first_tenant.id
        request.state.rls_tenant_id = str(first_tenant.id)
        logger.info(f"System Admin Master Key context set, tenant {first_tenant.id}")
        return first_tenant
    
    # Look up the org API key
    org_api_key_result = await db.execute(
        select(OrganizationAPIKey)
        .where(OrganizationAPIKey.key_hash == key_hash)
        .where(OrganizationAPIKey.revoked is False)
    )
    org_api_key = org_api_key_result.scalar_one_or_none()
    
    if not org_api_key:
        logger.warning(f"Organization API Key not found or revoked. Prefix: {api_key[:8]}...")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="Invalid Organization API Key"
        )
    
    # Check if the key is expired
    from datetime import datetime
    if org_api_key.expires_at and org_api_key.expires_at < datetime.now(org_api_key.expires_at.tzinfo):
        logger.warning(f"Organization API Key expired. ID: {org_api_key.id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Organization API Key has expired"
        )
    
    # Get the tenant for this org API key
    tenant_result = await db.execute(
        select(Tenant).where(Tenant.clerk_org_id == org_api_key.clerk_org_id)
    )
    tenant = tenant_result.scalar_one_or_none()
    
    if not tenant:
        logger.error(f"Organization {org_api_key.clerk_org_id} not found for API key ID {org_api_key.id}.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail=f"Organization {org_api_key.clerk_org_id} not found for API key."
        )
    
    # Update the last used timestamp
    org_api_key.last_used_at = func.now()
    await db.commit()
    await db.refresh(org_api_key)
    
    # Set context in request state
    request.state.tenant = tenant
    request.state.tenant_id = tenant.id
    request.state.rls_tenant_id = str(tenant.id)
    request.state.clerk_org_id = tenant.clerk_org_id
    request.state.rls_clerk_org_id = tenant.clerk_org_id
    request.state.api_key_permissions = org_api_key.permissions
    request.state.is_system_admin = False  # Org API keys are not system admin keys
    
    logger.info(f"Org API Key auth successful: Key ID {org_api_key.id}, Org ID {tenant.clerk_org_id}")
    return tenant


@router.post(
    "/resolve",
    response_model=ResolveResponse,
    response_model_exclude_none=True,
    status_code=status.HTTP_200_OK,
    summary="Resolve intent from natural language",
)
async def resolve_intent(
    request: Request,
    resolve_request: ResolveRequest,
    tenant: Tenant = Depends(get_tenant_with_admin_bypass),
    orchestrator: ChatOrchestrator = Depends(get_chat_orchestrator),
    db=Depends(get_db),
) -> Dict[str, Any]:
    """
    Resolve a natural language message to an intent and take appropriate action.

    This endpoint:
    1. Identifies the user's intent from their message
    2. Extracts required parameters for the action
    3. Executes the action if all parameters are present
    4. Prompts for additional information if needed

    The conversation_id should be reused for multi-turn interactions.
    """
    try:
        # Log the incoming request
        logger.info(
            f"Intent resolution request: tenant={tenant.id}, "
            f"conversation={resolve_request.conversation_id}, "
            f"role={resolve_request.role}"
        )

        # Process the message through the orchestrator using the new simplified approach
        # Make sure to include the conversation_id in the context
        context = resolve_request.context.copy()
        context["conversation_id"] = str(resolve_request.conversation_id)
        
        result = await orchestrator.handle_message_with_llm(
            tenant_id=str(tenant.id),
            user_id=str(resolve_request.user_id),
            user_role=resolve_request.role,
            message=resolve_request.message,
            message_history=resolve_request.context.get("message_history", []),
            context=context,
            request=request,
        )

        # Transform the result based on the response type
        if result.get("type") == "ask":
            # Need more information from the user
            return {
                "kind": "ask",
                "field": result.get("missing_field"),
                "question": result.get("question"),
                "conversation_id": resolve_request.conversation_id,  # Include conversation ID
            }
        elif result.get("type") == "intent_clarification":
            # Need clarification about what the user wants
            return {
                "kind": "intent_clarification",
                "question": result.get("question"),
                "options": [{"id": str(i), "text": alt} for i, alt in enumerate(result.get("alternatives", []))],
                "conversation_id": resolve_request.conversation_id,  # Include conversation ID
            }
        elif result.get("type") == "action":
            # Action successfully executed
            return {
                "kind": "reply",
                "text": result.get("outcome"),
            }
        elif result.get("type") == "async":
            # Async action started
            return {
                "kind": "async",
                "workflow_id": result.get("workflow_id"),
                "status_url": f"/v1/status/{result.get('workflow_id')}",
            }
        else:
            # Fallback - couldn't resolve intent
            return {
                "kind": "fallback",
                "text": "I couldn't understand that request. Could you please rephrase it?",
            }

    except Exception as e:
        logger.exception(f"Error resolving intent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing request: {str(e)}",
        ) from e


@router.post(
    "/continue",
    response_model=ResolveResponse,
    response_model_exclude_none=True,
    status_code=status.HTTP_200_OK,
    summary="Continue a conversation with additional information",
)
async def continue_conversation(
    request: Request,
    continue_request: ContinueRequest,
    tenant: Tenant = Depends(get_tenant_with_admin_bypass),
    orchestrator: ChatOrchestrator = Depends(get_chat_orchestrator),
    db=Depends(get_db),
) -> Dict[str, Any]:
    """
    Continue a conversation by providing additional information.

    Used when the previous call to /resolve required more information
    from the user (returned an "ask" response).
    """
    try:
        # Log the continuation request
        logger.info(
            f"Conversation continuation: tenant={tenant.id}, "
            f"conversation={continue_request.conversation_id}"
        )

        # Check if we have a conversation context in Redis to determine approach
        context_key = f"conversation:{tenant.id}:{continue_request.conversation_id}"
        logger.info(f"Looking for conversation with key: {context_key}")
        logger.info(f"Tenant ID: {tenant.id}, type: {type(tenant.id)}")
        logger.info(f"Conversation ID: {continue_request.conversation_id}, type: {type(continue_request.conversation_id)}")
        
        redis_client = await get_redis_client()
        # Also check if the conversation was saved with a string tenant ID
        string_context_key = f"conversation:{str(tenant.id)}:{str(continue_request.conversation_id)}"
        logger.info(f"Also checking string key: {string_context_key}")
        
        conversation_json = await redis_client.get(context_key)
        if not conversation_json:
            # Try with string tenant ID
            conversation_json = await redis_client.get(string_context_key)
            if conversation_json:
                logger.info(f"Found conversation with string key")
        
        logger.info(f"Found conversation context: {conversation_json is not None}")
        
        if conversation_json:
            conversation_data = json.loads(conversation_json)
            # If the conversation has pending_clarification, use the LLM approach
            if conversation_data.get("current_intent") == "pending_clarification":
                # Use the new LLM approach with conversation context
                result = await orchestrator.handle_message_with_llm(
                    tenant_id=str(tenant.id),
                    user_id=str(continue_request.user_id),
                    user_role=continue_request.role or "user",
                    message=continue_request.message,
                    message_history=conversation_data.get("message_history", []),
                    context={
                        "conversation_id": str(continue_request.conversation_id),
                        "clarification_context": conversation_data.get("parameters", {}).get("clarification_context", {})
                    },
                    request=request,
                )
            else:
                # Use the standard continuation approach
                result = await orchestrator.continue_conversation(
                    tenant_id=str(tenant.id),
                    conversation_id=str(continue_request.conversation_id),
                    user_id=str(continue_request.user_id),
                    message=continue_request.message,
                    request=request,
                )
        else:
            # No conversation context found
            logger.warning(f"No conversation found for key: {context_key} or {string_context_key}")
            logger.warning(f"Tried with tenant ID {tenant.id} (type: {type(tenant.id)}) and conversation ID {continue_request.conversation_id} (type: {type(continue_request.conversation_id)})")
            result = {
                "type": "reply",
                "outcome": "I'm sorry, I couldn't find our previous conversation. Could we start over?"
            }

        # Transform the result based on the response type (same logic as resolve)
        if result.get("type") == "ask":
            return {
                "kind": "ask",
                "field": result.get("missing_field"),
                "question": result.get("question"),
            }
        elif result.get("type") == "intent_clarification":
            # Need clarification about what the user wants
            return {
                "kind": "intent_clarification",
                "question": result.get("question"),
                "options": [{"id": str(i), "text": alt} for i, alt in enumerate(result.get("alternatives", []))],
                "conversation_id": continue_request.conversation_id,
            }
        elif result.get("type") == "action":
            return {
                "kind": "reply",
                "text": result.get("outcome"),
            }
        elif result.get("type") == "async":
            return {
                "kind": "async",
                "workflow_id": result.get("workflow_id"),
                "status_url": f"/v1/status/{result.get('workflow_id')}",
            }
        else:
            return {
                "kind": "fallback",
                "text": "I couldn't process that information. Could you try again?",
            }

    except Exception as e:
        logger.exception(f"Error continuing conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing request: {str(e)}",
        ) from e


@router.get(
    "/status/{workflow_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Check status of an async workflow",
)
async def check_workflow_status(
    workflow_id: UUID4,
    tenant: Tenant = Depends(get_tenant_with_admin_bypass),
    db=Depends(get_db),
) -> Dict[str, Any]:
    """
    Check the status of an asynchronous workflow.

    Used to poll for the result of an async action after receiving
    an "async" response from /resolve or /continue.
    """
    from sqlalchemy import select

    from src.coherence.models.workflow_status import WorkflowStatus

    # Query the workflow status table
    stmt = select(WorkflowStatus).where(
        (WorkflowStatus.id == workflow_id) & (WorkflowStatus.tenant_id == tenant.id)
    )

    result = await db.execute(stmt)
    workflow = result.scalars().first()

    if not workflow:
        # Workflow not found - raise 404
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Workflow with ID {workflow_id} not found",
        )

    # Return workflow status information
    response = {
        "workflow_id": str(workflow.id),
        "status": workflow.status,
        "progress": workflow.progress,
        "current_step": workflow.current_step,
    }

    # Include result if available
    if workflow.result:
        response["result"] = workflow.result

    return response
