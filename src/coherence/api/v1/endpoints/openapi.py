"""
OpenAPI integration endpoints for the Coherence API.

These endpoints allow importing OpenAPI specifications, generating 
actions, and managing API integrations.
"""

import json
import logging
import re
import uuid
from datetime import datetime
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, status
from pydantic import BaseModel
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    get_clerk_auth_details,
)
from src.coherence.core.qdrant_client import QdrantClient
from src.coherence.db.deps import get_db
from src.coherence.models.generated_action import ValidationStatus
from src.coherence.models.integration import (
    APIEndpoint,
    APIIntegration,
    APIOriginalSpec,
    SpecFormat,
)
from src.coherence.models.template import Template
from src.coherence.models.tenant import Tenant
from src.coherence.openapi_adapter.action_generator import get_action_generator
from src.coherence.openapi_adapter.adapter import get_openapi_adapter, OpenAPIParseError
from src.coherence.openapi_adapter.intent_mapper import get_intent_mapper
from src.coherence.openapi_adapter.spec_adapters import SpecAdapterFactory
from src.coherence.schemas.openapi import (
    ActionCode,
    ActionVersions,
    GenerateActionsRequest,
    GenerateActionsResponse,
    GeneratedAction,
    ImportOpenAPIRequest,
    ImportOpenAPIResponse,
    UpdateIntegrationRequest,
    ValidateActionRequest,
    ValidateActionResponse,
)
from src.coherence.services.spec_format_detector import SpecFormatDetector
from src.coherence.services.schema_extractor import OpenAPISchemaExtractor
from src.coherence.template_system.services.template_service import TemplateService


# New request schemas for selective endpoint processing
class GenerateActionsForEndpointsRequest(BaseModel):
    integration_id: uuid.UUID
    endpoint_ids: List[uuid.UUID]  # Specific endpoints to process
    force_regenerate: bool = False

class GenerateIntentsForEndpointsRequest(BaseModel):
    integration_id: uuid.UUID
    endpoint_ids: List[uuid.UUID]  # Specific endpoints to process
    
class GenerateTemplatesForEndpointsRequest(BaseModel):
    integration_id: uuid.UUID
    endpoint_ids: List[uuid.UUID]  # Specific endpoints to process

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=List[Dict])
async def list_integrations(
    request: Request, # Added request parameter
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details), # Use Clerk auth
    db: AsyncSession = Depends(get_db),
):
    """List all OpenAPI integrations for the tenant."""
    try:
        # Get tenant_id from request state (set by get_clerk_auth_details)
        tenant_id = getattr(request.state, "tenant_id", None)
        is_system_admin = getattr(request.state, "is_system_admin", False)

        # Query integrations based on user type
        if tenant_id:
            # Regular user with tenant context - get only their integrations
            result = await db.execute(
                select(APIIntegration).where(APIIntegration.tenant_id == tenant_id)
            )
        elif is_system_admin:
            # System admin without tenant context - get all integrations
            logger.info("System admin accessing integrations without tenant context - showing all integrations")
            result = await db.execute(
                select(APIIntegration)
            )
        else:
            # Non-system admin without tenant context - return empty list
            logger.warning("Non-system admin user has no tenant context - returning empty integrations list")
            return []
            
        integrations = result.scalars().all()

        # Format the response to match frontend expectations
        integration_list = []
        for integration in integrations:
            # Check if integration has credentials configured
            auth_config_result = await db.execute(
                text("SELECT integration_id FROM api_auth_configs WHERE integration_id = :integration_id"),
                {"integration_id": integration.id}
            )
            has_credentials = auth_config_result.fetchone() is not None
            
            # Extract information from the integration with better error handling
            response_entry = {
                "id": str(integration.id),
                "tenant_id": str(integration.tenant_id),
                "name": integration.name,
                "api_type": "rest",  # Assuming all are REST APIs since it's OpenAPI
                "created_at": integration.created_at.isoformat(),
                "updated_at": integration.updated_at.isoformat(),
                "has_credentials": has_credentials,
                "description": integration.version or None,  # Default fallback
                "spec_format": integration.spec_format,  # Add spec format to response
            }

            # Additional fields with explicit try/except
            try:
                if integration.openapi_spec and isinstance(integration.openapi_spec, dict):
                    info = integration.openapi_spec.get("info", {})

                    # Get description and summary
                    if info:
                        if info.get("description"):
                            response_entry["description"] = info.get("description")
                        if info.get("title"):
                            response_entry["summary"] = info.get("title")

                    # Get tags
                    tags = []
                    spec_tags = integration.openapi_spec.get("tags", [])
                    if spec_tags:
                        for tag in spec_tags:
                            if isinstance(tag, dict) and "name" in tag:
                                tags.append(tag["name"])
                        if tags:
                            response_entry["tags"] = tags

                    # Log success
                    logger.info(f"Successfully extracted OpenAPI info for integration {integration.id}")
            except Exception as spec_err:
                logger.warning(f"Error extracting OpenAPI spec info for integration {integration.id}: {spec_err}")

            # Add to list
            integration_list.append(response_entry)

        # Sort by name for consistency
        integration_list.sort(key=lambda x: x["name"])
        return integration_list
        
    except Exception as e:
        # Log the exception for debugging purposes
        logger.error(f"Failed to list integrations for tenant {tenant_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve integrations.",
        ) from e


@router.get("/{integration_id}", response_model=Dict)
async def get_integration(
    request: Request, # Added request
    integration_id: uuid.UUID,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details), # Use Clerk auth
    db: AsyncSession = Depends(get_db),
):
    """Get details of a specific OpenAPI integration."""
    # Get tenant_id from request state and check if user is system admin
    tenant_id = getattr(request.state, "tenant_id", None)
    is_system_admin = getattr(request.state, "is_system_admin", False)

    # Query the integration
    integration = await db.get(APIIntegration, integration_id)
    
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Integration not found",
        )

    # Verify ownership and existence
    # System admins can view any integration, regular users must own it
    if not is_system_admin:
        if not tenant_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")
            
        if integration.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found or not owned by tenant",
            )
    
    # For system admins without a tenant context, log the access
    if is_system_admin and not tenant_id:
        logger.info(f"System admin accessing integration details for integration ID {integration_id} without tenant context")

    # Query endpoints for this integration
    result = await db.execute(
        text("SELECT id, path, method, operation_id, enabled FROM api_endpoints "
             "WHERE integration_id = :integration_id "
             "ORDER BY path, method"),
        {"integration_id": integration_id},
    )
    endpoints = result.fetchall()

    # Query auth config
    result = await db.execute(
        text("SELECT auth_type FROM api_auth_configs "
             "WHERE integration_id = :integration_id"),
        {"integration_id": integration_id},
    )
    auth_config = result.fetchone()

    # Check if original spec exists
    result = await db.execute(
        text("SELECT format, created_at FROM api_original_specs "
             "WHERE integration_id = :integration_id"),
        {"integration_id": integration_id},
    )
    original_spec = result.fetchone()
    
    # Format the response
    return {
        "id": str(integration.id),
        "name": integration.name,
        "version": integration.version,
        "base_url": integration.base_url,
        "status": integration.status,
        "created_at": integration.created_at.isoformat(),
        "updated_at": integration.updated_at.isoformat(),
        "spec_format": integration.spec_format,
        "original_format": original_spec.format if original_spec else None,
        "endpoints": [
            {
                "id": str(endpoint.id),
                "path": endpoint.path,
                "method": endpoint.method,
                "operation_id": endpoint.operation_id,
                "enabled": endpoint.enabled,
            }
            for endpoint in endpoints
        ],
        "auth_type": auth_config.auth_type if auth_config else None,
    }


async def fetch_openapi_spec(url: str) -> Dict:
    """Fetch an API specification from a URL.
    
    Args:
        url: The URL to fetch the specification from
        
    Returns:
        The parsed API specification as a dictionary
        
    Raises:
        HTTPException: If the URL cannot be fetched or the content is invalid
    """
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(url, follow_redirects=True)
            response.raise_for_status()
            content = response.text
            return json.loads(content)
    except (httpx.RequestError, json.JSONDecodeError) as e:
        logger.error(f"Failed to fetch API spec from URL {url}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to fetch API specification from URL: {str(e)}"
        ) from e

def parse_spec_content(content: str) -> Dict:
    """Parse API specification content from a string.
    
    Args:
        content: The API specification as a JSON string
        
    Returns:
        The parsed API specification as a dictionary
        
    Raises:
        HTTPException: If the content is invalid
    """
    try:
        return json.loads(content)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse API spec content: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to parse API specification content: {str(e)}"
        ) from e

@router.post(
    "/import",
    response_model=ImportOpenAPIResponse,
    status_code=status.HTTP_201_CREATED,
)
async def import_openapi_spec(
    request: Request,
    request_data: ImportOpenAPIRequest,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """Import an API specification (OpenAPI, FAPI, or BAPI) and create an integration."""
    try:
        # Get tenant_id from request state first
        tenant_id = getattr(request.state, "tenant_id", None)
        is_system_admin = getattr(request.state, "is_system_admin", False)
        
        # Handle system admin without tenant context
        if not tenant_id and is_system_admin:
            # Get first available tenant for system admin imports
            tenant_result = await db.execute(select(Tenant).limit(1))
            default_tenant = tenant_result.scalar_one_or_none()
            if default_tenant:
                tenant_id = default_tenant.id
                logger.info(f"System admin importing to default tenant: {tenant_id}")
            else:
                # If no tenants exist at all, the system admin cannot import.
                logger.error("System admin attempted import, but no tenants exist in the system.")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="No tenants available in the system for system admin import."
                )
        elif not tenant_id and not is_system_admin:
             logger.error("Non-admin user attempted import without tenant context.")
             raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found for import.")

        # Ensure tenant_id is now valid before proceeding
        if not tenant_id:
             logger.error(f"Import failed: tenant_id is still None after checks. is_system_admin={is_system_admin}")
             raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal error: Could not determine tenant for import.")

        # Get the API specification from the request (spec object, URL, or content string)
        api_spec = None
        
        if hasattr(request_data, "spec") and request_data.spec:
            api_spec = request_data.spec
            logger.info("Using spec from request body")
        elif request_data.url:
            logger.info(f"Fetching spec from URL: {request_data.url}")
            api_spec = await fetch_openapi_spec(request_data.url)
        elif request_data.content:
            logger.info("Parsing spec from content string")
            api_spec = parse_spec_content(request_data.content)
            
        if not api_spec:
            logger.error("No API specification provided in request")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No API specification provided. Please provide either 'spec' object, 'url', or 'content' field."
            )
            
        # Detect the specification format
        try:
            spec_format = SpecFormatDetector.detect_format(api_spec)
            logger.info(f"Detected API specification format: {spec_format}")
            
            # Get the appropriate adapter for this format
            adapter = SpecAdapterFactory.create_adapter(api_spec)
        except ValueError as e:
            logger.error(f"Unsupported API specification format: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported API specification format: {str(e)}"
            ) from e
        except Exception as e:
            logger.error(f"Error detecting API specification format: {type(e).__name__}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to detect API specification format: {str(e) or type(e).__name__}"
            ) from e
            
        # Convert the specification to OpenAPI format
        try:
            converted_spec = adapter.adapt(api_spec)
        except Exception as e:
            logger.error(f"Error converting API specification: {type(e).__name__}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to convert API specification: {str(e) or type(e).__name__}"
            ) from e
        
        # Process the converted specification using existing OpenAPIAdapter
        try:
            openapi_adapter = await get_openapi_adapter(db)
            integration = await openapi_adapter.import_spec(
                tenant_id=tenant_id,
                name=request_data.name,
                spec_data=converted_spec,
            )
        except OpenAPIParseError as e:
            logger.error(f"OpenAPI parsing error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to parse API specification: {str(e)}"
            ) from e
        except Exception as e:
            logger.error(f"Error importing API specification: {type(e).__name__}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to import API specification: {str(e) or type(e).__name__}"
            ) from e
        
        # Update the integration with the detected format
        await db.execute(
            text("UPDATE api_integrations SET spec_format = :spec_format WHERE id = :id"),
            {"spec_format": spec_format.value, "id": integration["integration_id"]}
        )
        
        # If the format is not OpenAPI, store the original specification
        if spec_format != SpecFormat.OPENAPI:
            original_spec = APIOriginalSpec(
                integration_id=uuid.UUID(integration["integration_id"]),
                format=spec_format.value,
                content=api_spec
            )
            db.add(original_spec)
        
        await db.commit()
        
        # Add format to the response
        integration["spec_format"] = spec_format.value
        
        return integration

    except HTTPException:
        # Re-raise HTTPExceptions as-is to preserve their status code and detail
        raise
    except ValueError as e:
        # Handle specification format errors specifically
        logger.error(f"Invalid API specification format: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid API specification format: {str(e)}",
        ) from e
    except OpenAPIParseError as e:
        # Handle OpenAPI parsing errors specifically
        logger.error(f"Failed to parse API specification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to parse API specification: {str(e)}",
        ) from e
    except Exception as e:
        # Log the full exception for debugging
        logger.error(f"Unexpected error importing API specification: {type(e).__name__}: {str(e)}", exc_info=True)
        
        # Provide a more detailed error message
        error_message = str(e) if str(e) else f"Unexpected error of type {type(e).__name__}"
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to import API specification: {error_message}",
        ) from e


@router.post(
    "/actions/generate",
    response_model=GenerateActionsResponse,
)
async def generate_actions(
    request_data: GenerateActionsRequest, # Renamed to avoid conflict
    request: Request, # Added request
    force_regenerate: bool = Query(
        False, description="Force regeneration even if valid versions exist"
    ),
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details), # Use Clerk auth
    db: AsyncSession = Depends(get_db),
):
    """
    Generate actions from an OpenAPI integration.

    This endpoint will:
    1. Identify all enabled endpoints for the integration
    2. Generate action classes for each endpoint
    3. Validate the generated code
    4. Store the actions in the database
    5. Return the list of generated actions
    """
    try:
        # Get tenant_id from request state
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
             raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        # Verify that the integration belongs to this tenant
        integration = await db.get(APIIntegration, request_data.integration_id)
        if not integration or integration.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found or not owned by tenant",
            )

        # Get the action generator
        action_generator = await get_action_generator(db)

        # Query for all endpoints associated with this integration
        result = await db.execute(
            text("SELECT id FROM api_endpoints WHERE integration_id = :integration_id AND enabled = true"),
            {"integration_id": request_data.integration_id},
        )
        endpoints = result.fetchall()

        # Generate actions for each endpoint
        actions = []
        for endpoint in endpoints:
            try:
                # Generate the action with validation and persistence
                action_result = await action_generator.generate_action_class(
                    endpoint_id=endpoint.id,
                    force_regenerate=force_regenerate,
                    validate=True,
                    save=True,
                )

                # Add to the list
                actions.append(
                    GeneratedAction(
                        endpoint_id=action_result["endpoint_id"],
                        action_name=action_result["class_name"].replace("Action", ""),
                        class_name=action_result["class_name"],
                        parameters=action_result["parameters"],
                        description=action_result.get("description", ""),
                    )
                )
            except Exception as endpoint_err:
                # Log the error but continue with other endpoints
                import logging

                logging.getLogger(__name__).warning(
                    f"Failed to generate action for endpoint {endpoint.id}: {str(endpoint_err)}"
                )

        # Commit all changes
        await db.commit()

        return {"actions": actions}

    except Exception as e:
        # Ensure rollback on error
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate actions: {str(e)}",
        ) from e


@router.post(
    "/intents/generate",
    response_model=Dict,
)
async def generate_intents(
    request_data: GenerateActionsRequest, # Renamed to avoid conflict
    request: Request, # Added request
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details), # Use Clerk auth
    db: AsyncSession = Depends(get_db),
):
    """Generate intents from an OpenAPI integration."""
    try:
        # Get tenant_id from request state
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
             raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        # Verify that the integration belongs to this tenant
        integration = await db.get(APIIntegration, request_data.integration_id)
        if not integration or integration.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found or not owned by tenant",
            )

        # Get the intent mapper
        intent_mapper = await get_intent_mapper(db)

        # Generate intents for the integration
        intents = await intent_mapper.generate_intents_for_integration(
            integration_id=request_data.integration_id,
            tenant_id=tenant_id,
        )

        # Format the response
        return {
            "integration_id": str(request_data.integration_id),
            "intent_count": len(intents),
            "intents": [intent.model_dump() for intent in intents],
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate intents: {str(e)}",
        ) from e


@router.put(
    "/integrations/{integration_id}",
    response_model=Dict,
)
async def update_integration(
    request: Request,
    integration_id: uuid.UUID,
    update_data: UpdateIntegrationRequest,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """Update an OpenAPI integration's details."""
    try:
        # Get tenant_id from request state
        tenant_id = getattr(request.state, "tenant_id", None)
        is_system_admin = getattr(request.state, "is_system_admin", False)

        # Verify ownership and existence
        # System admins can update any integration, regular users must own it
        integration = await db.get(APIIntegration, integration_id)
        
        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found",
            )
            
        if not is_system_admin:
            if not tenant_id:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")
                
            if integration.tenant_id != tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Integration not found or not owned by tenant",
                )

        # Update fields if provided
        if update_data.name is not None:
            integration.name = update_data.name
            
        if update_data.description is not None:
            # Description is stored as version field for now (based on existing code pattern)
            integration.version = update_data.description
            
        if update_data.base_url is not None:
            integration.base_url = update_data.base_url

        # Save changes
        await db.commit()
        
        # Return updated integration
        return {
            "id": str(integration.id),
            "name": integration.name,
            "description": integration.version,
            "base_url": integration.base_url,
            "updated_at": integration.updated_at.isoformat(),
        }
        
    except HTTPException:
        raise
    except Exception as e:
        # Ensure rollback on error
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update integration: {str(e)}",
        ) from e


@router.delete(
    "/integrations/{integration_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_integration(
    request: Request, # Added request
    integration_id: uuid.UUID,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details), # Use Clerk auth
    db: AsyncSession = Depends(get_db),
):
    """Delete an OpenAPI integration."""
    # Get tenant_id from request state
    tenant_id = getattr(request.state, "tenant_id", None)
    if not tenant_id:
         raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

    # Verify that the integration belongs to this tenant
    integration = await db.get(APIIntegration, integration_id)
    if not integration or integration.tenant_id != tenant_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Integration not found or not owned by tenant",
        )

    # Delete the integration (cascades to related objects)
    await db.delete(integration)
    await db.commit()

    return None  # 204 No Content


@router.get(
    "/actions/endpoint/{endpoint_id}",
    response_model=ActionVersions,
)
async def get_endpoint_actions(
    # Reordered parameters: request first, then non-defaults, then defaults
    request: Request,
    endpoint_id: uuid.UUID = Path(..., description="ID of the API endpoint"),
    include_code: bool = Query(
        False, description="Whether to include the code in the response"
    ),
    include_outdated: bool = Query(
        False, description="Whether to include outdated versions"
    ),
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """
    Get all action versions for an endpoint.

    This endpoint will:
    1. Find all action versions for the specified endpoint
    2. Return them ordered by generation time (newest first)
    3. Optionally include the code in the response
    4. Optionally include outdated versions
    """
    try:
        # Get tenant_id from request state
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
             raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        # First verify that the endpoint belongs to this tenant
        result = await db.execute(
            text("""
            SELECT e.id, e.integration_id
            FROM api_endpoints e
            JOIN api_integrations i ON e.integration_id = i.id
            WHERE e.id = :endpoint_id AND i.tenant_id = :tenant_id
            """),
            {"endpoint_id": endpoint_id, "tenant_id": tenant_id},
        )
        endpoint = result.fetchone()

        if not endpoint:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Endpoint not found",
            )

        # Get the action generator
        action_generator = await get_action_generator(db)

        # Get all actions for this endpoint
        actions = await action_generator.get_endpoint_actions(
            endpoint_id=endpoint_id,
            include_code=include_code,
            include_outdated=include_outdated,
        )

        # Find the active version (the most recent valid version)
        active_version = None
        for action in actions:
            if action.get("validation_status") == ValidationStatus.VALID.value:
                active_version = action.get("version")
                break

        # Return the result
        return {
            "endpoint_id": str(endpoint_id),
            "active_version": active_version,
            "versions": actions,
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get endpoint actions: {str(e)}",
        ) from e


@router.get(
    "/actions/endpoint/{endpoint_id}/version/{version}",
    response_model=ActionCode,
)
async def get_action_version(
    # Reordered parameters: request first, then non-defaults, then defaults
    request: Request,
    endpoint_id: uuid.UUID = Path(..., description="ID of the API endpoint"),
    version: str = Path(..., description="Version hash of the action"),
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """
    Get a specific version of an action.

    This endpoint will:
    1. Find the specified action version
    2. Return its details including the code
    """
    try:
        # Get tenant_id from request state
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
             raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        # First verify that the endpoint belongs to this tenant
        result = await db.execute(
            text("""
            SELECT e.id, e.integration_id
            FROM api_endpoints e
            JOIN api_integrations i ON e.integration_id = i.id
            WHERE e.id = :endpoint_id AND i.tenant_id = :tenant_id
            """),
            {"endpoint_id": endpoint_id, "tenant_id": tenant_id},
        )
        endpoint = result.fetchone()

        if not endpoint:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Endpoint not found",
            )

        # Get the action generator
        action_generator = await get_action_generator(db)

        # Get the specific action version
        action = await action_generator.get_action_by_version(
            endpoint_id=endpoint_id,
            version=version,
        )

        if not action:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Action version {version} not found",
            )

        # Return the action
        return action

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get action version: {str(e)}",
        ) from e


@router.post(
    "/actions/generate-selected",
    response_model=GenerateActionsResponse,
)
async def generate_actions_for_selected_endpoints(
    request_data: GenerateActionsForEndpointsRequest,
    request: Request,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """Generate actions for selected endpoints only."""
    try:
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        # Verify integration existence
        # Let RLS handle the ownership check instead of manually comparing tenant_id
        integration = await db.get(APIIntegration, request_data.integration_id)
        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found",
            )

        # Verify all endpoints belong to this integration
        endpoint_check = await db.execute(
            text("""
                SELECT COUNT(*) as count
                FROM api_endpoints 
                WHERE id = ANY(:endpoint_ids) AND integration_id = :integration_id
            """),
            {
                "endpoint_ids": [str(id) for id in request_data.endpoint_ids],
                "integration_id": request_data.integration_id
            },
        )
        
        count = endpoint_check.scalar()
        if count != len(request_data.endpoint_ids):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Some endpoints don't belong to this integration",
            )

        # Generate actions for selected endpoints
        action_generator = await get_action_generator(db)
        actions = []
        
        for endpoint_id in request_data.endpoint_ids:
            try:
                action_result = await action_generator.generate_action_class(
                    endpoint_id=endpoint_id,
                    force_regenerate=request_data.force_regenerate,
                    validate=True,
                    save=True,
                )
                
                actions.append(
                    GeneratedAction(
                        endpoint_id=action_result["endpoint_id"],
                        action_name=action_result["class_name"].replace("Action", ""),
                        class_name=action_result["class_name"],
                        parameters=action_result["parameters"],
                        description=action_result.get("description", ""),
                    )
                )
            except Exception as endpoint_err:
                logger.warning(f"Failed to generate action for endpoint {endpoint_id}: {str(endpoint_err)}")

        await db.commit()
        return {"actions": actions}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate actions: {str(e)}",
        ) from e

@router.post(
    "/intents/generate-selected",
    response_model=Dict[str, List[Dict[str, Any]]],
)
async def generate_intents_for_selected_endpoints(
    request_data: GenerateIntentsForEndpointsRequest,
    request: Request,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """Generate intents for selected endpoints only."""
    try:
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        # Verify integration existence
        # Let RLS handle the ownership check instead of manually comparing tenant_id
        integration = await db.get(APIIntegration, request_data.integration_id)
        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found",
            )

        # Verify all endpoints belong to this integration
        endpoint_check = await db.execute(
            text("""
                SELECT COUNT(*) as count
                FROM api_endpoints 
                WHERE id = ANY(:endpoint_ids) AND integration_id = :integration_id
            """),
            {
                "endpoint_ids": [str(id) for id in request_data.endpoint_ids],
                "integration_id": request_data.integration_id
            },
        )
        
        count = endpoint_check.scalar()
        if count != len(request_data.endpoint_ids):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Some endpoints don't belong to this integration",
            )

        # Generate intents for selected endpoints
        intent_mapper = await get_intent_mapper(db)
        created_intents = []
        
        for endpoint_id in request_data.endpoint_ids:
            try:
                # Pass tenant_id instead of use_llm which isn't supported
                intent_def = await intent_mapper.generate_intent(
                    endpoint_id=endpoint_id,
                    tenant_id=tenant_id
                )
                
                if intent_def:
                    # Update the endpoint with intent data
                    endpoint = await db.get(APIEndpoint, endpoint_id)
                    if endpoint:
                        endpoint.intent_name = intent_def.name
                        endpoint.intent_description = intent_def.description
                        endpoint.intent_examples = intent_def.examples
                        endpoint.intent_parameters = {k: v.model_dump() for k, v in intent_def.parameters.items()}
                        endpoint.intent_required_fields = list(intent_def.required_fields)
                        endpoint.intent_generated_at = datetime.utcnow()
                        endpoint.intent_generation_status = "generated"
                        db.add(endpoint)
                    
                    created_intents.append({
                        "name": intent_def.name,
                        "description": intent_def.description,
                        "endpoint_id": str(endpoint_id),
                        "examples": intent_def.examples,
                        "parameters": intent_def.parameters,
                        "required_fields": list(intent_def.required_fields)
                    })
            except Exception as e:
                # Log the error but continue with other endpoints
                logger.error(f"Error generating intent for endpoint {endpoint_id}: {str(e)}")
                # Add a placeholder entry to show the failed intent
                created_intents.append({
                    "id": None,
                    "name": f"Failed to generate intent for endpoint {endpoint_id}",
                    "description": f"Error: {str(e)}",
                    "endpoint_id": str(endpoint_id),
                    "examples": []
                })
        
        await db.commit()
        return {"intents": created_intents}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate intents: {str(e)}"
        ) from e
        
def extract_examples_from_template(body: str) -> List[str]:
    """Extract example phrases from an intent template body.
    
    Args:
        body: The template body text
        
    Returns:
        List of extracted example phrases
    """
    examples = []
    lines = body.split("\n")
    in_examples_section = False
    in_intent_patterns_section = False
    
    for line in lines:
        line = line.strip()
        
        # Check for older format with LLM-Accessible Examples
        if "## LLM-Accessible Examples" in line:
            in_examples_section = True
            continue
        
        # Check for new format with intent_patterns block
        if "{% block intent_patterns %}" in line:
            in_intent_patterns_section = True
            continue
            
        if "{% endblock %}" in line and in_intent_patterns_section:
            in_intent_patterns_section = False
            continue
        
        # Process examples in older format
        if in_examples_section:
            if line.startswith("##"):
                # End of examples section
                break
                
            if line.startswith("- "):
                # Extract the example text
                example = line[2:].strip()
                if example:
                    examples.append(example)
        
        # Process examples in newer format (intent_patterns block)
        if in_intent_patterns_section:
            if line.startswith("- "):
                # Extract the example text
                example = line[2:].strip()
                # Remove parameter placeholders like {param_name}
                example = re.sub(r'\{[^}]*\}', 'example_value', example)
                if example:
                    examples.append(example)
    
    return examples

async def index_intent_template(
    tenant_id: str,
    template: Template,
    llm_provider: Any,
    qdrant_client: QdrantClient
) -> bool:
    """Index a single intent template in Qdrant.
    
    Args:
        tenant_id: Tenant ID
        template: The template to index
        llm_provider: LLM provider for embeddings
        qdrant_client: Qdrant client for vector operations
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Skip non-intent templates
        if template.category != "intent_router":
            return False
            
        # For intent templates, always index those with intent_ prefix that are API-related
        # The Template model doesn't have a metadata field in standard format
        is_api_endpoint_template = (
            template.key.startswith("intent_endpoint_") or 
            template.key.startswith("intent_api_")
        )
        
        # Determine if the template should be vectorized based on its key
        if not is_api_endpoint_template:
            logger.warning(f"Template {template.key} is not an API endpoint intent template - skipping vectorization")
            return False
            
        # Derive the response template key from template properties
        response_template_key = None
        
        # First check if it's in the template actions
        if template.actions and isinstance(template.actions, dict) and template.actions.get("intent"):
            response_template_key = template.actions.get("intent")
        # Otherwise derive from template key
        else:
            # For auto-generated templates, use the template key without the intent_ prefix
            if template.key.startswith("intent_"):
                response_template_key = template.key[7:]  # Remove "intent_" prefix
            else:
                response_template_key = template.key
                
        logger.info(f"Using derived response_template_key '{response_template_key}' for template {template.key}")
            
        # Extract examples from template body
        examples = extract_examples_from_template(template.body)
        if not examples:
            logger.warning(f"No examples found in template {template.key}")
            return False
        
        logger.info(f"Found {len(examples)} examples in template {template.key}")
        for idx, example in enumerate(examples[:5]):  # Log first 5 examples
            logger.info(f"Example {idx+1}: {example}")
            
        # Ensure collection exists
        collection_name = f"intent_idx_{tenant_id}_user"
        if not await qdrant_client.collection_exists(collection_name):
            await qdrant_client.create_intent_collection(
                tenant_id=tenant_id,
                role="user",
                vector_size=384  # Standardized dimension
            )
            
        # Extract parameter information from template
        parameters = template.parameters or {}
        
        # Index each example
        successful_examples = 0
        for example in examples:
            try:
                # Generate embedding for the example
                embedding = await llm_provider.generate_embedding(example)
                
                # Add to Qdrant
                await qdrant_client.upsert_intent_vectors(
                    tenant_id=tenant_id,
                    role="user",
                    vectors=[{
                        "id": str(uuid.uuid4()),
                        "vector": embedding,
                        "metadata": {
                            "intent": response_template_key,
                            "text": example,
                            "parameters": parameters,
                            "template_id": str(template.id),
                            "template_key": template.key
                        }
                    }]
                )
                successful_examples += 1
            except Exception as example_err:
                logger.error(f"Error indexing example '{example}' from template {template.key}: {example_err}")
                
        logger.info(f"Successfully indexed {successful_examples}/{len(examples)} examples from template {template.key}")
        return successful_examples > 0
        
    except Exception as e:
        logger.error(f"Error indexing template {template.key}: {e}")
        return False

@router.post(
    "/templates/generate-selected",
    response_model=Dict[str, List[Dict[str, Any]]],
)
async def generate_templates_for_selected_endpoints(
    request_data: GenerateTemplatesForEndpointsRequest,
    request: Request,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """Generate templates for selected endpoints only."""
    try:
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        # Verify integration existence
        # Let RLS handle the ownership check instead of manually comparing tenant_id
        integration = await db.get(APIIntegration, request_data.integration_id)
        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found",
            )

        # Verify all endpoints belong to this integration
        endpoint_check = await db.execute(
            text("""
                SELECT COUNT(*) as count
                FROM api_endpoints 
                WHERE id = ANY(:endpoint_ids) AND integration_id = :integration_id
            """),
            {
                "endpoint_ids": [str(id) for id in request_data.endpoint_ids],
                "integration_id": request_data.integration_id
            },
        )
        
        count = endpoint_check.scalar()
        if count != len(request_data.endpoint_ids):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Some endpoints don't belong to this integration",
            )
        
        # Get action generator and template service
        action_generator = await get_action_generator(db)
        template_service = TemplateService()
        
        # Set up LLM provider and Qdrant client for indexing
        from src.coherence.core.llm.factory import LLMFactory
        from src.coherence.core.qdrant_client import get_qdrant_client
        
        # Initialize LLM provider for template indexing
        llm_provider = None
        try:
            from src.coherence.core.config import settings
            llm_factory = LLMFactory()
            llm_provider = llm_factory.create_provider(
                name=settings.LLM_PROVIDER,
                model=settings.LLM_MODEL
            )
        except Exception as e:
            logger.warning(f"Could not initialize LLM provider for indexing: {e}")
        qdrant_client = await get_qdrant_client()
        
        # Generate and create templates for selected endpoints
        created_templates = []
        intent_templates = []  # Store intent templates for indexing
        
        for endpoint_id in request_data.endpoint_ids:
            # Get endpoint details to include in template generation
            endpoint_result = await db.execute(
                select(APIEndpoint).where(APIEndpoint.id == endpoint_id)
            )
            endpoint = endpoint_result.scalar_one_or_none()
            if not endpoint:
                logger.warning(f"Endpoint {endpoint_id} not found")
                continue
                
            # Generate template definitions
            template_defs = await action_generator._generate_action_templates(
                endpoint_id=endpoint_id,
                tenant_id=tenant_id,
                api_key=integration.name.lower().replace(" ", "_"),
                base_url=integration.base_url
            )
            
            # Create templates via TemplateService
            for template_def in template_defs:
                template = await template_service.create_template(
                    db=db,
                    key=template_def["key"],
                    category=template_def["category"],
                    body=template_def["body"],
                    scope=template_def["scope"],
                    scope_id=template_def["scope_id"],
                    tenant_id=template_def["tenant_id"],
                    description=template_def.get("description"),
                    actions=template_def.get("actions"),
                    parameters=template_def.get("parameters"),
                    response_format=template_def.get("response_format")
                    # removed metadata parameter as it's not supported by create_template
                )
                
                created_templates.append({
                    "id": str(template.id),
                    "key": template.key,
                    "category": template.category,
                    "description": template.description,
                    "endpoint_id": str(endpoint_id),
                    "endpoint_path": endpoint.path,
                    "method": endpoint.method
                })
                
                # Save intent templates for indexing
                if template.category == "intent_router":
                    intent_templates.append(template)
        
        # Index intent templates in Qdrant if we have an LLM provider
        if llm_provider:
            for template in intent_templates:
                await index_intent_template(
                    tenant_id=str(tenant_id),
                    template=template,
                    llm_provider=llm_provider,
                    qdrant_client=qdrant_client
                )
        else:
            logger.warning("Skipping template indexing - no LLM provider available")
        
        return {"templates": created_templates}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate templates: {str(e)}"
        ) from e

@router.post(
    "/{integration_id}/templates/generate",
    response_model=Dict[str, List[Dict[str, Any]]],
)
async def generate_integration_templates(
    request: Request,
    integration_id: uuid.UUID,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """
    Generate template definitions for all enabled endpoints of an integration.
    
    This endpoint will:
    1. Fetch all enabled endpoints
    2. Generate template definitions for each
    3. Insert them via TemplateService
    4. Index intent templates in Qdrant for vector matching
    5. Return the created templates
    """
    try:
        # Get tenant_id from request state
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")
            
        # Verify integration exists
        # Let RLS handle the ownership check instead of manually comparing tenant_id
        integration = await db.get(APIIntegration, integration_id)
        
        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found"
            )
            
        # Fetch enabled endpoints
        result = await db.execute(
            select(APIEndpoint).where(
                APIEndpoint.integration_id == integration_id,
                APIEndpoint.enabled is True
            )
        )
        endpoints = result.scalars().all()
        
        # Get action generator
        action_generator = await get_action_generator(db)
        
        # Get template service
        template_service = TemplateService()
        
        # Set up LLM provider and Qdrant client for indexing
        from src.coherence.core.llm.factory import LLMFactory
        from src.coherence.core.qdrant_client import get_qdrant_client
        
        # Initialize LLM provider for template indexing
        llm_provider = None
        try:
            from src.coherence.core.config import settings
            llm_factory = LLMFactory()
            llm_provider = llm_factory.create_provider(
                name=settings.LLM_PROVIDER,
                model=settings.LLM_MODEL
            )
        except Exception as e:
            logger.warning(f"Could not initialize LLM provider for indexing: {e}")
        qdrant_client = await get_qdrant_client()
        
        # Generate and create templates
        created_templates = []
        intent_templates = []  # Store intent templates for indexing
        
        for endpoint in endpoints:
            # Generate template definitions
            template_defs = await action_generator._generate_action_templates(
                endpoint_id=endpoint.id,
                tenant_id=tenant_id,
                api_key=integration.name.lower().replace(" ", "_"),
                base_url=integration.base_url
            )
            
            # Create templates via TemplateService
            for template_def in template_defs:
                template = await template_service.create_template(
                    db=db,
                    key=template_def["key"],
                    category=template_def["category"],
                    body=template_def["body"],
                    scope=template_def["scope"],
                    scope_id=template_def["scope_id"],
                    tenant_id=template_def["tenant_id"],
                    description=template_def.get("description"),
                    actions=template_def.get("actions"),
                    parameters=template_def.get("parameters"),
                    response_format=template_def.get("response_format")
                    # removed metadata parameter as it's not supported by create_template
                )
                created_templates.append({
                    "id": str(template.id),
                    "key": template.key,
                    "category": template.category,
                    "description": template.description
                })
                
                # Save intent templates for indexing
                if template.category == "intent_router":
                    intent_templates.append(template)
        
        # Index intent templates in Qdrant if we have an LLM provider
        indexed_count = 0
        if llm_provider:
            for template in intent_templates:
                success = await index_intent_template(
                    tenant_id=str(tenant_id),
                    template=template,
                    llm_provider=llm_provider,
                    qdrant_client=qdrant_client
                )
                if success:
                    indexed_count += 1
            
            logger.info(f"Indexed {indexed_count} intent templates in Qdrant for tenant {tenant_id}")
        else:
            logger.warning("Skipping template indexing - no LLM provider available")
                
        # Return the created templates
        return {"templates": created_templates}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate templates: {str(e)}"
        ) from e
        
@router.post(
    "/{integration_id}/intents/generate",
    response_model=Dict[str, List[Dict[str, Any]]],
)
async def generate_integration_intents(
    request: Request,
    integration_id: uuid.UUID,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """
    Generate intents for all enabled endpoints of an integration.
    
    This endpoint will:
    1. Fetch all enabled endpoints
    2. Use LLM to generate intent definitions
    3. Persist them in the intents table
    4. Return the created intents
    """
    try:
        # Get tenant_id from request state
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")
            
        # Verify integration exists
        # Let RLS handle the ownership check instead of manually comparing tenant_id
        integration = await db.get(APIIntegration, integration_id)
        
        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found"
            )
            
        # Fetch enabled endpoints
        result = await db.execute(
            select(APIEndpoint).where(
                APIEndpoint.integration_id == integration_id,
                APIEndpoint.enabled is True
            )
        )
        endpoints = result.scalars().all()
        
        # Get intent mapper
        intent_mapper = await get_intent_mapper(db)
        
        # Generate and create intents
        created_intents = []
        for endpoint in endpoints:
            try:
                # Generate intent definition
                # Pass tenant_id instead of use_llm which isn't supported
                intent_def = await intent_mapper.generate_intent(
                    endpoint_id=endpoint.id,
                    tenant_id=tenant_id
                )
                
                if intent_def:
                    # Update the endpoint with intent data
                    endpoint = await db.get(APIEndpoint, endpoint.id)
                    if endpoint:
                        endpoint.intent_name = intent_def.name
                        endpoint.intent_description = intent_def.description
                        endpoint.intent_examples = intent_def.examples
                        endpoint.intent_parameters = {k: v.model_dump() for k, v in intent_def.parameters.items()}
                        endpoint.intent_required_fields = list(intent_def.required_fields)
                        endpoint.intent_generated_at = datetime.utcnow()
                        endpoint.intent_generation_status = "generated"
                        db.add(endpoint)
                    
                    created_intents.append({
                        "name": intent_def.name,
                        "description": intent_def.description,
                        "endpoint_id": str(endpoint.id),
                        "examples": intent_def.examples,
                        "parameters": intent_def.parameters,
                        "required_fields": list(intent_def.required_fields)
                    })
            except Exception as e:
                # Log the error but continue with other endpoints
                logger.error(f"Error generating intent for endpoint {endpoint.id}: {str(e)}")
                # Add a placeholder entry to show the failed intent
                created_intents.append({
                    "id": None,
                    "name": f"Failed to generate intent for endpoint {endpoint.id}",
                    "description": f"Error: {str(e)}",
                    "endpoint_id": str(endpoint.id),
                    "examples": []
                })
            
        await db.commit()
        # Return the created intents
        return {"intents": created_intents}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate intents: {str(e)}"
        ) from e
        
@router.post(
    "/actions/validate",
    response_model=ValidateActionResponse,
)
async def validate_action(
    request_data: ValidateActionRequest, # Renamed to avoid conflict
    request: Request, # Added request
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details), # Use Clerk auth
    db: AsyncSession = Depends(get_db),
):
    """
    Validate an action.

    This endpoint will:
    1. Find the specified action
    2. Validate its code
    3. Update its validation status
    4. Return the validation result
    """
    try:
        # Get tenant_id from request state
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
             raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        # First verify that the action belongs to this tenant
        result = await db.execute(
            text("""
            SELECT a.id, a.endpoint_id, e.integration_id
            FROM generated_actions a
            JOIN api_endpoints e ON a.endpoint_id = e.id
            JOIN api_integrations i ON e.integration_id = i.id
            WHERE a.id = :action_id AND i.tenant_id = :tenant_id
            """),
            {"action_id": request_data.action_id, "tenant_id": tenant_id},
        )
        action_data = result.fetchone()

        if not action_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Action not found",
            )

        # Get the action generator
        action_generator = await get_action_generator(db)

        # Validate the action
        validation_result = await action_generator.validate_action(
            action_id=request_data.action_id,
        )

        # Commit the changes
        await db.commit()

        # Return the validation result
        return validation_result

    except HTTPException:
        raise
    except Exception as e:
        # Ensure rollback on error
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to validate action: {str(e)}",
        ) from e


@router.get(
    "/endpoints/{endpoint_id}/enhanced-parameters",
    response_model=Dict[str, Any],
)
async def get_enhanced_endpoint_parameters(
    request: Request,
    endpoint_id: uuid.UUID,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """Get enhanced parameter schemas for an API endpoint.
    
    This endpoint returns rich parameter information including validation
    constraints, format hints, examples, and default values extracted
    from the OpenAPI specification.
    """
    try:
        # Get tenant_id from request state
        tenant_id = getattr(request.state, "tenant_id", None)
        is_system_admin = getattr(request.state, "is_system_admin", False)
        
        # Get endpoint with integration check
        result = await db.execute(
            text("""
                SELECT e.id, e.path, e.method, e.operation_id, e.summary, e.description,
                       e.tags, e.deprecated, e.enabled, e.openapi_snippet,
                       i.id as integration_id, i.tenant_id, i.openapi_spec
                FROM api_endpoints e
                JOIN api_integrations i ON e.integration_id = i.id
                WHERE e.id = :endpoint_id
            """),
            {"endpoint_id": endpoint_id},
        )
        endpoint_data = result.fetchone()
        
        if not endpoint_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Endpoint not found",
            )
        
        # Check tenant ownership (system admins can access any endpoint)
        if not is_system_admin:
            if not tenant_id or endpoint_data.tenant_id != tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Endpoint not found or not owned by tenant",
                )
        
        # Extract enhanced parameters using OpenAPISchemaExtractor
        enhanced_parameters = OpenAPISchemaExtractor.extract_enhanced_parameters(
            api_spec=endpoint_data.openapi_spec,
            path=endpoint_data.path,
            method=endpoint_data.method.lower()
        )
        
        # Get operation-level security requirements
        operation = endpoint_data.openapi_spec.get("paths", {}).get(
            endpoint_data.path, {}
        ).get(endpoint_data.method.lower(), {})
        
        operation_security = operation.get("security", [])
        
        # Extract response schemas
        responses = operation.get("responses", {})
        response_schemas = {}
        for status_code, response_spec in responses.items():
            if "content" in response_spec:
                response_schemas[status_code] = response_spec["content"]
        
        # Format the response
        return {
            "endpoint_id": str(endpoint_id),
            "path": endpoint_data.path,
            "method": endpoint_data.method,
            "operation_id": endpoint_data.operation_id,
            "summary": endpoint_data.summary,
            "description": endpoint_data.description,
            "tags": endpoint_data.tags or [],
            "deprecated": endpoint_data.deprecated,
            "enabled": endpoint_data.enabled,
            "enhanced_parameters": [param.model_dump() for param in enhanced_parameters],
            "response_schemas": response_schemas,
            "operation_security": operation_security,
            "openapi_snippet": endpoint_data.openapi_snippet,  # For backwards compatibility
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting enhanced parameters for endpoint {endpoint_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get enhanced parameters: {str(e)}",
        ) from e
