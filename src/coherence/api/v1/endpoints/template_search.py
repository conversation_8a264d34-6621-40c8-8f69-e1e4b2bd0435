"""
Template search API endpoints.

This module provides an API for semantic search of templates using
vector embeddings in Qdrant.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    RequirePermission,
    get_clerk_auth_details,
)
from src.coherence.db.deps import get_db
from src.coherence.services.permission_service import CoherencePermission
from src.coherence.services.vector_indexer import VectorIndexer, get_vector_indexer

router = APIRouter()
logger = logging.getLogger(__name__)


class TemplateSearchRequest(BaseModel):
    """Request for searching templates."""

    query: str = Field(..., description="Search query text")
    category: Optional[str] = Field(None, description="Optional category filter")
    limit: int = Field(5, description="Maximum number of results to return", ge=1, le=50)
    score_threshold: float = Field(
        0.7, description="Minimum similarity score (0-1)", ge=0, le=1
    )


class TemplateSearchResult(BaseModel):
    """Result of a template search."""

    id: str = Field(..., description="Result ID")
    template_id: str = Field(..., description="Template ID")
    template_key: str = Field(..., description="Template key")
    template_category: str = Field(..., description="Template category")
    score: float = Field(..., description="Similarity score (0-1)")


class TemplateSearchResponse(BaseModel):
    """Response for template search."""

    results: List[TemplateSearchResult] = Field(..., description="Search results")
    query: str = Field(..., description="Original query")
    total: int = Field(..., description="Total number of results")


@router.post("/search", response_model=TemplateSearchResponse)
async def search_templates(
    request: Request,
    search_request: TemplateSearchRequest,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    vector_indexer: VectorIndexer = Depends(get_vector_indexer),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.TEMPLATE_READ)),
):
    """
    Search for templates using semantic similarity.

    This endpoint uses vector embeddings to find templates that are semantically
    similar to the provided query. Results are ranked by similarity score.
    """
    tenant_id = getattr(request.state, "tenant_id", None)
    if not tenant_id:
        raise HTTPException(
            status_code=400,
            detail="Tenant context required to search templates",
        )

    getattr(request.state, "is_system_admin", False)

    try:
        # Define collections to search based on tenant context
        collections_to_search = []

        # Always include tenant-specific collection if available
        if tenant_id:
            collections_to_search.append(f"template_idx_{tenant_id}")

        # Add global collection
        collections_to_search.append("template_idx_global")

        # Initialize filter based on category
        filter_dict = {}
        if search_request.category:
            filter_dict["template_category"] = {"$eq": search_request.category}

        # Combine results from all collections
        all_results = []
        for collection_name in collections_to_search:
            try:
                results = await vector_indexer.search_templates(
                    query=search_request.query,
                    filter=filter_dict if filter_dict else None,
                    limit=search_request.limit,
                    score_threshold=search_request.score_threshold,
                    index_name=collection_name,
                )
                all_results.extend(results)
            except Exception as e:
                logger.warning(f"Error searching collection {collection_name}: {str(e)}")
                continue

        # Sort by score and take top results
        sorted_results = sorted(
            all_results, key=lambda x: x.get("score", 0), reverse=True
        )
        limited_results = sorted_results[: search_request.limit]

        # Format response
        search_results = [
            TemplateSearchResult(
                id=str(result.get("id", "")),
                template_id=str(result.get("template_id", "")),
                template_key=result.get("template_key", ""),
                template_category=result.get("template_category", ""),
                score=result.get("score", 0),
            )
            for result in limited_results
        ]

        return TemplateSearchResponse(
            results=search_results,
            query=search_request.query,
            total=len(search_results),
        )

    except Exception as e:
        logger.error(f"Template search failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Template search failed: {str(e)}",
        )