"""
System Admin API endpoints.

All operations require system admin privileges.
"""

import hashlib
import secrets
import uuid
from enum import Enum
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, Path, Request, status
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.coherence.api.v1.dependencies.auth import (
    check_is_system_admin,
    get_clerk_auth_details,
)
from src.coherence.core.errors.logging import log_error
from src.coherence.crud.crud_system_admin import (
    create_system_admin,
    create_system_admin_api_key,
    get_multi_system_admin,
    get_multi_system_admin_api_key_for_admin,
    get_system_admin,
    get_system_admin_api_key,
    remove_system_admin,
    remove_system_admin_api_key,
    update_system_admin_api_key,
)
from src.coherence.db.deps import get_db
from src.coherence.models.system_admin import SystemAdmin as SystemAdminModel
from src.coherence.models.tenant import <PERSON><PERSON><PERSON><PERSON><PERSON>, Tenant
from src.coherence.schemas.system_admin import (
    SystemAdmin,
    SystemAdminAPIKey,
    SystemAdminAPIKeyCreate,
    SystemAdminAPIKeyCreateResponse,
    SystemAdminAPIKeyUpdate,
    SystemAdminCreate,
)
from src.coherence.schemas.tenant import (
    OrganizationAPIKeyCreateRequest,
    OrganizationAPIKeyCreateResponse,
    OrganizationAPIKeyRead,
)
from src.coherence.services.audit_service import get_audit_service

logger = None  # Removed unused logger

# Define AuditEventType locally if not found elsewhere
class AuditEventType(str, Enum):
    SYSTEM_ADMIN_CREATED = "SYSTEM_ADMIN_CREATED"
    SYSTEM_ADMIN_DELETED = "SYSTEM_ADMIN_DELETED"
    SYSTEM_ADMIN_API_KEY_CREATED = "SYSTEM_ADMIN_API_KEY_CREATED"
    SYSTEM_ADMIN_API_KEY_UPDATED = "SYSTEM_ADMIN_API_KEY_UPDATED"
    SYSTEM_ADMIN_API_KEY_DELETED = "SYSTEM_ADMIN_API_KEY_DELETED"

router = APIRouter(prefix="/system", tags=["system-admin"])

# Helper function to get a system admin by Clerk user ID
async def get_system_admin_by_clerk_id(db: AsyncSession, clerk_user_id: str):
    result = await db.execute(
        select(SystemAdminModel).where(SystemAdminModel.clerk_user_id == clerk_user_id)
    )
    system_admin = result.scalar_one_or_none()
    
    if not system_admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="System administrator not found for this user",
        )
    
    return system_admin


# --- System Admin Management Routes ---

@router.post("/system-admins", response_model=SystemAdmin, status_code=status.HTTP_201_CREATED)
async def create_new_system_admin(
    admin_in: SystemAdminCreate,
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    Create a new system admin.

    Requires system admin privileges.
    """
    # Check the requester is a system admin
    await check_is_system_admin(request)
    
    # Set created_by to the authenticated user's ID
    admin_in.created_by = auth_details.get("user_id", "unknown")
    
    try:
        # Create the new system admin
        db_obj = await create_system_admin(db=db, obj_in=admin_in)
        
        # Audit log
        audit_service = await get_audit_service(db)

        # System admins aren't tied to a specific tenant, use a placeholder UUID
        system_tenant_id = uuid.UUID("00000000-0000-0000-0000-000000000000")

        await audit_service.log_admin_action(
            action=AuditEventType.SYSTEM_ADMIN_CREATED.value,
            tenant_id=system_tenant_id,  # Use placeholder UUID for system-level activities
            user_id=uuid.UUID(auth_details.get("user_id")) if auth_details.get("user_id") else None,
            details={
                "resource_id": str(db_obj.id),
                "clerk_user_id": admin_in.clerk_user_id,
            },
        )
        
        return db_obj
    except HTTPException:
        raise
    except Exception as e:
        log_error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create system admin: {str(e)}"
        ) from e


@router.get("/system-admins", response_model=List[SystemAdmin])
async def list_system_admins(
    request: Request,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    Retrieve a list of system admins.

    Requires system admin privileges.
    """
    # Check the requester is a system admin
    await check_is_system_admin(request)
    
    try:
        return await get_multi_system_admin(db=db, skip=skip, limit=limit)
    except HTTPException:
        raise
    except Exception as e:
        log_error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to list system admins: {str(e)}"
        ) from e


@router.get("/system-admins/{admin_id}", response_model=SystemAdmin)
async def get_single_system_admin(
    admin_id: uuid.UUID,
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    Get a specific system admin by ID.

    Requires system admin privileges.
    """
    # Check the requester is a system admin
    await check_is_system_admin(request)
    
    try:
        db_obj = await get_system_admin(db=db, id=admin_id)
        if not db_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"System admin with ID {admin_id} not found",
            )
        return db_obj
    except HTTPException:
        raise
    except Exception as e:
        log_error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get system admin: {str(e)}"
        ) from e


@router.delete("/system-admins/{admin_id}", response_model=SystemAdmin)
async def delete_system_admin(
    admin_id: uuid.UUID,
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    Delete a system admin.

    Requires system admin privileges. All associated API keys will also be deleted.
    """
    # Check the requester is a system admin
    await check_is_system_admin(request)
    
    try:
        # First check if the admin exists
        db_obj = await get_system_admin(db=db, id=admin_id)
        if not db_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"System admin with ID {admin_id} not found",
            )
        
        # Don't allow admins to delete themselves
        if db_obj.clerk_user_id == auth_details.get("user_id"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="System admins cannot delete themselves",
            )
        
        # Remove the admin (this will cascade delete API keys)
        removed_admin = await remove_system_admin(db=db, id=admin_id)
        
        # Audit log
        audit_service = await get_audit_service(db)

        # System admins aren't tied to a specific tenant, use a placeholder UUID
        system_tenant_id = uuid.UUID("00000000-0000-0000-0000-000000000000")

        await audit_service.log_admin_action(
            action=AuditEventType.SYSTEM_ADMIN_DELETED.value,
            tenant_id=system_tenant_id,  # Use placeholder UUID for system-level activities
            user_id=uuid.UUID(auth_details.get("user_id")) if auth_details.get("user_id") else None,
            details={
                "resource_id": str(admin_id),
                "clerk_user_id": db_obj.clerk_user_id,
            },
        )
        
        return removed_admin
    except HTTPException:
        raise
    except Exception as e:
        log_error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete system admin: {str(e)}"
        ) from e


# --- System Admin API Key Management Routes ---

@router.post("/api-keys", response_model=SystemAdminAPIKeyCreateResponse, status_code=status.HTTP_201_CREATED)
async def create_api_key(
    api_key_in: SystemAdminAPIKeyCreate,
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    Create a new API key for a system admin.

    Requires system admin privileges. The raw API key is returned only once.
    """
    # Check the requester is a system admin
    await check_is_system_admin(request)

    # Get system admin record for the current user
    system_admin = await get_system_admin_by_clerk_id(db, clerk_user_id=auth_details.get("user_id"))
    
    # Set created_by to the authenticated user's ID
    api_key_in.created_by = auth_details.get("user_id", "unknown")
    
    try:
        # Create the API key
        db_obj, raw_key = await create_system_admin_api_key(
            db=db,
            obj_in=api_key_in,
            system_admin_id=system_admin.id,
        )
        
        # Audit log
        audit_service = await get_audit_service(db)

        # System admins aren't tied to a specific tenant, use a placeholder UUID
        system_tenant_id = uuid.UUID("00000000-0000-0000-0000-000000000000")

        await audit_service.log_admin_action(
            action=AuditEventType.SYSTEM_ADMIN_API_KEY_CREATED.value,
            tenant_id=system_tenant_id,  # Use placeholder UUID for system-level activities
            user_id=uuid.UUID(auth_details.get("user_id")) if auth_details.get("user_id") else None,
            details={
                "resource_id": str(db_obj.id),
                "key_name": api_key_in.name,
                "system_admin_id": str(system_admin.id),
                "expires_at": db_obj.expires_at.isoformat() if db_obj.expires_at else None,
            },
        )
        
        # Return combined result with the raw key
        return SystemAdminAPIKeyCreateResponse(
            **db_obj.__dict__,
            api_key=raw_key,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create API key: {str(e)}"
        ) from e


@router.get("/api-keys", response_model=List[SystemAdminAPIKey])
async def list_api_keys(
    request: Request,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    List all system admin API keys.

    Requires system admin privileges.
    """
    # Check the requester is a system admin
    await check_is_system_admin(request)

    # Get system admin record for the current user
    system_admin = await get_system_admin_by_clerk_id(db, clerk_user_id=auth_details.get("user_id"))
    
    try:
        # Get all API keys
        return await get_multi_system_admin_api_key_for_admin(
            db=db,
            system_admin_id=system_admin.id,
            skip=skip,
            limit=limit,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to list API keys: {str(e)}"
        ) from e


@router.get("/api-keys/by-admin/{admin_id}", response_model=List[SystemAdminAPIKey])
async def list_api_keys_for_admin(
    admin_id: uuid.UUID,
    request: Request,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    List API keys for a specific system admin.

    Requires system admin privileges.
    """
    # Check the requester is a system admin
    await check_is_system_admin(request)
    
    try:
        # Verify the target admin exists
        target_admin = await get_system_admin(db=db, id=admin_id)
        if not target_admin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"System admin with ID {admin_id} not found",
            )
        
        # Get API keys for the specified admin
        return await get_multi_system_admin_api_key_for_admin(
            db=db,
            system_admin_id=admin_id,
            skip=skip,
            limit=limit,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to list API keys: {str(e)}"
        ) from e


@router.put("/api-keys/{key_id}", response_model=SystemAdminAPIKey)
async def update_api_key(
    key_id: uuid.UUID,
    api_key_in: SystemAdminAPIKeyUpdate,
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    Update an API key (name, permissions, expiration, or revoke status).

    Requires system admin privileges.
    """
    # Check the requester is a system admin
    await check_is_system_admin(request)
    
    try:
        # Get the API key
        db_obj = await get_system_admin_api_key(db=db, id=key_id)
        if not db_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"API key with ID {key_id} not found",
            )
        
        # Update the API key
        updated_key = await update_system_admin_api_key(
            db=db,
            db_obj=db_obj,
            obj_in=api_key_in,
        )
        
        # Audit log
        audit_service = await get_audit_service(db)

        # System admins aren't tied to a specific tenant, use a placeholder UUID
        system_tenant_id = uuid.UUID("00000000-0000-0000-0000-000000000000")

        await audit_service.log_admin_action(
            action=AuditEventType.SYSTEM_ADMIN_API_KEY_UPDATED.value,
            tenant_id=system_tenant_id,  # Use placeholder UUID for system-level activities
            user_id=uuid.UUID(auth_details.get("user_id")) if auth_details.get("user_id") else None,
            details={
                "resource_id": str(key_id),
                "key_name": updated_key.name,
                "revoked": updated_key.revoked,
                "expires_at": updated_key.expires_at.isoformat() if updated_key.expires_at else None,
            },
        )
        
        return updated_key
    except HTTPException:
        raise
    except Exception as e:
        log_error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update API key: {str(e)}"
        ) from e


@router.delete("/api-keys/{key_id}", response_model=SystemAdminAPIKey)
async def delete_api_key(
    key_id: uuid.UUID,
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    Delete an API key.

    Requires system admin privileges.
    """
    # Check the requester is a system admin
    await check_is_system_admin(request)
    
    try:
        # Get the API key
        db_obj = await get_system_admin_api_key(db=db, id=key_id)
        if not db_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"API key with ID {key_id} not found",
            )
        
        # Delete the API key
        removed_key = await remove_system_admin_api_key(db=db, id=key_id)
        
        # Audit log
        audit_service = await get_audit_service(db)

        # System admins aren't tied to a specific tenant, use a placeholder UUID
        system_tenant_id = uuid.UUID("00000000-0000-0000-0000-000000000000")

        await audit_service.log_admin_action(
            action=AuditEventType.SYSTEM_ADMIN_API_KEY_DELETED.value,
            tenant_id=system_tenant_id,  # Use placeholder UUID for system-level activities
            user_id=uuid.UUID(auth_details.get("user_id")) if auth_details.get("user_id") else None,
            details={
                "resource_id": str(key_id),
                "key_name": removed_key.name,
                "system_admin_id": str(removed_key.system_admin_id),
            },
        )
        
        return removed_key
    except HTTPException:
        raise
    except Exception as e:
        log_error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete API key: {str(e)}"
        ) from e


# Organization API Key Management (accessible by org admins)
@router.get(
    "/org-api-keys/{clerk_org_id}",
    response_model=List[OrganizationAPIKeyRead],
    summary="List API Keys for an Organization"
)
async def list_organization_api_keys(
    clerk_org_id: str = Path(..., description="The Clerk Organization ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    List API keys for a specific organization.

    This endpoint returns all API keys for the specified organization.
    Organization administrators can view their own organization's API keys.
    """
    # Verify the authenticated user has access to this organization
    if auth_details.get("clerk_org_id") != clerk_org_id and not auth_details.get("is_system_admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access API keys for this organization"
        )

    # Verify the tenant/organization exists by clerk_org_id
    tenant_result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id))
    tenant = tenant_result.scalar_one_or_none()
    if not tenant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found")

    # Get the API keys for this organization
    result = await db.execute(
        select(OrganizationAPIKey)
        .where(OrganizationAPIKey.clerk_org_id == clerk_org_id)
        .order_by(OrganizationAPIKey.created_at.desc())
    )
    api_keys = result.scalars().all()
    return api_keys

@router.post(
    "/org-api-keys/{clerk_org_id}",
    response_model=OrganizationAPIKeyCreateResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new API Key for an Organization"
)
async def create_organization_api_key(
    key_create_request: OrganizationAPIKeyCreateRequest,
    clerk_org_id: str = Path(..., description="The Clerk Organization ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> Any:
    """
    Create a new API key for the specified organization.

    This endpoint creates a new API key for the specified organization.
    Organization administrators can create API keys for their own organization.
    """
    # Verify the authenticated user has access to this organization
    if auth_details.get("clerk_org_id") != clerk_org_id and not auth_details.get("is_system_admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to create API keys for this organization"
        )

    # Verify the tenant/organization exists
    tenant_result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id))
    tenant = tenant_result.scalar_one_or_none()
    if not tenant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found")

    # Generate the API key
    api_key_value = f"coh_org_{secrets.token_urlsafe(32)}"
    key_hash = hashlib.sha256(api_key_value.encode()).hexdigest()
    key_prefix = api_key_value[:8]  # Store the first 8 characters as prefix

    # Create the new API key
    new_api_key = OrganizationAPIKey(
        clerk_org_id=clerk_org_id,
        name=key_create_request.name,
        permissions=key_create_request.permissions,
        expires_at=key_create_request.expires_at,
        key_hash=key_hash,
        key_prefix=key_prefix,
        created_by=auth_details.get("user_id"),
        revoked=False
    )

    db.add(new_api_key)
    try:
        await db.commit()
        await db.refresh(new_api_key)
    except IntegrityError:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="API key could not be created. Potential hash collision or other constraint violation."
        )
    
    return OrganizationAPIKeyCreateResponse(
        **new_api_key.__dict__,
        key=api_key_value
    )

@router.delete(
    "/org-api-keys/{clerk_org_id}/{api_key_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Revoke an API Key for an Organization"
)
async def revoke_organization_api_key(
    clerk_org_id: str = Path(..., description="The Clerk Organization ID"),
    api_key_id: uuid.UUID = Path(..., description="The ID of the API key to revoke"),
    db: AsyncSession = Depends(get_db),
    auth_details: dict = Depends(get_clerk_auth_details),
) -> None:
    """
    Revoke an API key for the specified organization.

    This endpoint revokes an API key for the specified organization.
    Organization administrators can revoke API keys for their own organization.
    """
    # Verify the authenticated user has access to this organization
    if auth_details.get("clerk_org_id") != clerk_org_id and not auth_details.get("is_system_admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to revoke API keys for this organization"
        )

    # Get the API key
    result = await db.execute(
        select(OrganizationAPIKey).where(
            OrganizationAPIKey.id == api_key_id,
            OrganizationAPIKey.clerk_org_id == clerk_org_id
        )
    )
    api_key = result.scalar_one_or_none()

    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API Key not found or does not belong to this organization"
        )

    if api_key.revoked:
        # Already revoked, so just return success
        return

    # Revoke the API key
    api_key.revoked = True
    try:
        await db.commit()
    except Exception:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke API key."
        )
    return