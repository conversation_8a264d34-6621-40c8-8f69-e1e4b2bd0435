"""
API endpoint for template validation.
"""

import logging
from typing import Dict
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    RequirePermission,
    get_clerk_auth_details,
)
from src.coherence.db.deps import get_db as get_db_session
from src.coherence.models.template import TemplateCategory
from src.coherence.schemas.admin_template import AdminTemplateCategory
from src.coherence.services.permission_service import CoherencePermission
from src.coherence.services.template_validator import (
    UnifiedTemplateValidator,
    ValidationResult
)
from src.coherence.openapi_adapter.credential_manager import CredentialManager

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post(
    "/validate",
    response_model=ValidationResult,
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_CREATE))]
)
async def validate_template(
    request: Request,
    template_data: Dict,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details)
) -> ValidationResult:
    """
    Validate a unified template structure.
    
    This endpoint validates:
    - Template completeness (all required sections)
    - Credential references
    - CRFS configuration
    - Parameter mappings
    - Execution readiness
    
    Args:
        template_data: The template dictionary to validate
        
    Returns:
        ValidationResult with errors, warnings, and metadata
    """
    tenant_id = getattr(request.state, 'tenant_id', None)
    clerk_org_id = getattr(request.state, 'clerk_org_id', None)
    
    logger.info(f"Validating template for tenant_id={tenant_id}, clerk_org_id={clerk_org_id}")
    
    # Initialize credential manager with db session
    credential_manager = CredentialManager(db)
    
    # Create validator
    validator = UnifiedTemplateValidator(credential_manager=credential_manager)
    
    # Perform validation
    result = validator.validate_completeness(template_data)
    
    # Log validation results
    if result.is_valid:
        logger.info(f"Template validation successful. Warnings: {len(result.warnings)}")
    else:
        logger.warning(f"Template validation failed. Errors: {len(result.errors)}, Warnings: {len(result.warnings)}")
        for error in result.errors:
            logger.warning(f"Validation error: {error}")
    
    return result


@router.post(
    "/validate/execution-readiness",
    response_model=Dict,
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_READ))]
)
async def check_execution_readiness(
    request: Request,
    template_data: Dict,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details)
) -> Dict:
    """
    Check if a template is ready for execution.
    
    This is a quick check to determine if a template has all the
    necessary information to execute without external dependencies.
    
    Args:
        template_data: The template dictionary to check
        
    Returns:
        Dict with 'ready' boolean and optional 'missing' list
    """
    # Initialize credential manager with db session
    credential_manager = CredentialManager(db)
    
    # Create validator
    validator = UnifiedTemplateValidator(credential_manager=credential_manager)
    
    # Check execution readiness
    is_ready = validator.validate_execution_readiness(template_data)
    
    response = {"ready": is_ready}
    
    if not is_ready:
        # Provide details on what's missing
        missing = []
        
        if not validator._has_base_url(template_data):
            missing.append("base_url")
        
        if not validator._has_valid_auth(template_data):
            missing.append("authentication")
        
        if not validator._has_parameter_schemas(template_data):
            missing.append("parameter_schemas")
        
        if not validator._has_response_format(template_data):
            missing.append("response_format")
        
        response["missing"] = missing
    
    logger.info(f"Execution readiness check: ready={is_ready}, missing={response.get('missing', [])}")
    
    return response


@router.post(
    "/validate/test-mode",
    response_model=ValidationResult,
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_READ))]
)
async def validate_test_mode_compatibility(
    request: Request,
    template_data: Dict,
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details)
) -> ValidationResult:
    """
    Validate that a template is compatible with test mode.
    
    Checks that the template has:
    - test_data section
    - mock_responses
    - sample_parameters (warning if missing)
    
    Args:
        template_data: The template dictionary to validate
        
    Returns:
        ValidationResult with test mode compatibility status
    """
    # Create validator (no credential manager needed for test mode validation)
    validator = UnifiedTemplateValidator()
    
    # Validate test mode compatibility
    result = validator.validate_test_mode_compatibility(template_data)
    
    logger.info(f"Test mode validation: valid={result.is_valid}, errors={len(result.errors)}, warnings={len(result.warnings)}")
    
    return result


@router.post(
    "/validate/unified",
    response_model=ValidationResult,
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_CREATE))]
)
async def validate_unified_template(
    request: Request,
    template_data: Dict,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details)
) -> ValidationResult:
    """
    Comprehensive validation specifically for unified templates.
    
    This performs all validation checks and ensures the template
    meets the requirements for the unified template architecture.
    
    Args:
        template_data: The template dictionary to validate
        
    Returns:
        ValidationResult with detailed validation information
    """
    # Check if this is actually a unified template
    meta = template_data.get('meta', {})
    category = meta.get('category')
    
    # If category is specified in meta, it should be 'unified'
    if category and category != 'unified':
        return ValidationResult(
            is_valid=False,
            errors=[f"Template category must be 'unified', got '{category}'"],
            warnings=[]
        )
    
    # Initialize credential manager with db session
    credential_manager = CredentialManager(db)
    
    # Create validator
    validator = UnifiedTemplateValidator(credential_manager=credential_manager)
    
    # Perform comprehensive validation
    result = validator.validate_completeness(template_data)
    
    # Add execution readiness to metadata
    if result.metadata is None:
        result.metadata = {}
    
    result.metadata['execution_ready'] = validator.validate_execution_readiness(template_data)
    
    # Check test mode compatibility
    test_result = validator.validate_test_mode_compatibility(template_data)
    result.metadata['test_mode_ready'] = test_result.is_valid
    
    # Log comprehensive results
    logger.info(
        f"Unified template validation complete: valid={result.is_valid}, "
        f"errors={len(result.errors)}, warnings={len(result.warnings)}, "
        f"execution_ready={result.metadata['execution_ready']}, "
        f"test_mode_ready={result.metadata['test_mode_ready']}"
    )
    
    return result