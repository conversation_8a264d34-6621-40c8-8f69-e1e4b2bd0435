"""User preferences endpoints for the Coherence API."""

import uuid
from datetime import datetime
from typing import Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, status
from pydantic import BaseModel
from sqlalchemy import DateTime, ForeignKey, String, func
from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>, UUID
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import Mapped, mapped_column

from src.coherence.api.v1.dependencies.auth import get_tenant_from_api_key
from src.coherence.db.base_class import Base
from src.coherence.db.deps import get_db
from src.coherence.models.tenant import Tenant


# Create a new model for user preferences
class UserPreference(Base):
    """User preferences model for storing user-specific settings."""

    __tablename__ = "user_preferences"

    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: Mapped[str] = mapped_column(String, nullable=False, unique=True)
    tenant_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="SET NULL"),
        nullable=True,
    )
    preferences: Mapped[Optional[Dict[str, str]]] = mapped_column(JSONB, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
    )


# Pydantic schemas for API
class UserPreferenceBase(BaseModel):
    """Base schema for user preferences."""
    tenant_id: Optional[uuid.UUID] = None
    preferences: Optional[Dict] = None


class UserPreferenceCreate(UserPreferenceBase):
    """Schema for creating user preferences."""
    user_id: str


class UserPreferenceUpdate(UserPreferenceBase):
    """Schema for updating user preferences."""
    pass


class UserPreferenceRead(UserPreferenceBase):
    """Schema for reading user preferences."""
    id: uuid.UUID
    user_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


router = APIRouter()


@router.get("/{user_id}", response_model=UserPreferenceRead)
async def get_user_preferences(
    user_id: str = Path(...),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_tenant_from_api_key),
) -> UserPreferenceRead:
    """
    Get preferences for a specific user.
    
    This endpoint is accessible via the admin API key.
    """
    # Query the database for user preferences
    result = await db.execute(
        select(UserPreference).where(UserPreference.user_id == user_id)
    )
    user_preference = result.scalar_one_or_none()

    if not user_preference:
        # If no preferences exist, return a 404
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User preferences not found",
        )

    return user_preference


@router.post("/{user_id}", response_model=UserPreferenceRead)
async def create_user_preferences(
    user_preference_in: UserPreferenceCreate,
    user_id: str = Path(...),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_tenant_from_api_key),
) -> UserPreferenceRead:
    """
    Create or update preferences for a specific user.
    
    If preferences already exist, they will be updated.
    This endpoint is accessible via the admin API key.
    """
    # Ensure path user_id matches the body user_id
    if user_id != user_preference_in.user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User ID in path must match user ID in request body",
        )

    # Check if preferences already exist for this user
    result = await db.execute(
        select(UserPreference).where(UserPreference.user_id == user_id)
    )
    existing_preference = result.scalar_one_or_none()

    if existing_preference:
        # Update existing preferences
        if user_preference_in.tenant_id is not None:
            existing_preference.tenant_id = user_preference_in.tenant_id
        
        if user_preference_in.preferences is not None:
            # Merge preferences if they already exist
            if existing_preference.preferences:
                merged_preferences = existing_preference.preferences.copy()
                merged_preferences.update(user_preference_in.preferences)
                existing_preference.preferences = merged_preferences
            else:
                existing_preference.preferences = user_preference_in.preferences
        
        user_preference = existing_preference
    else:
        # Create new preferences
        user_preference = UserPreference(
            user_id=user_id,
            tenant_id=user_preference_in.tenant_id,
            preferences=user_preference_in.preferences,
        )
        db.add(user_preference)

    try:
        await db.commit()
        await db.refresh(user_preference)
        return user_preference
    except IntegrityError as err:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="User preferences creation failed due to constraint violation",
        ) from err


@router.put("/{user_id}", response_model=UserPreferenceRead)
async def update_user_preferences(
    user_preference_in: UserPreferenceUpdate,
    user_id: str = Path(...),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_tenant_from_api_key),
) -> UserPreferenceRead:
    """
    Update preferences for a specific user.
    
    If preferences don't exist, they will be created.
    This endpoint is accessible via the admin API key.
    """
    # Check if preferences already exist for this user
    result = await db.execute(
        select(UserPreference).where(UserPreference.user_id == user_id)
    )
    user_preference = result.scalar_one_or_none()

    if not user_preference:
        # Create new preferences if they don't exist
        user_preference = UserPreference(
            user_id=user_id,
            tenant_id=user_preference_in.tenant_id,
            preferences=user_preference_in.preferences,
        )
        db.add(user_preference)
    else:
        # Update existing preferences
        update_data = user_preference_in.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            if field == "preferences" and value is not None and user_preference.preferences is not None:
                # Merge preferences dictionaries
                merged_preferences = user_preference.preferences.copy()
                merged_preferences.update(value)
                setattr(user_preference, field, merged_preferences)
            else:
                setattr(user_preference, field, value)

    try:
        await db.commit()
        await db.refresh(user_preference)
        return user_preference
    except IntegrityError as err:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="User preferences update failed due to constraint violation",
        ) from err


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user_preferences(
    user_id: str = Path(...),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_tenant_from_api_key),
) -> None:
    """
    Delete preferences for a specific user.
    
    This endpoint is accessible via the admin API key.
    """
    # Query the database for user preferences
    result = await db.execute(
        select(UserPreference).where(UserPreference.user_id == user_id)
    )
    user_preference = result.scalar_one_or_none()

    if not user_preference:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User preferences not found",
        )

    await db.delete(user_preference)
    await db.commit()
