"""Template management endpoints for the Coherence API."""

import logging
import uuid
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, status
from pydantic import BaseModel, ConfigDict, Field, field_validator
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    RequirePermission,
    get_clerk_auth_details,
)
from src.coherence.db.deps import get_db
from src.coherence.models.template import Template
from src.coherence.services.permission_service import CoherencePermission
from src.coherence.template_system.defaults.templates import get_default_template
from src.coherence.template_system.services.template_service import TemplateService

router = APIRouter()
template_service = TemplateService()


# Define Pydantic schemas for request/response
class TemplateCreate(BaseModel):
    """Schema for creating a new template."""

    key: str = Field(..., description="Unique template key")
    category: str = Field(..., description="Template category")
    body: str = Field(..., description="Template content")
    scope: str = Field("tenant", description="Template scope (global, pack, tenant)")
    scope_id: Optional[uuid.UUID] = Field(
        None, description="Scope ID (None for global)"
    )
    language: str = Field("en", description="Template language code")
    description: Optional[str] = Field(None, description="Template description")
    actions: Optional[Dict[str, Any]] = Field(
        None, description="Actions for intent_router templates"
    )
    parameters: Optional[Dict[str, Any]] = Field(None, description="Parameter schema")

    @field_validator("category")
    @classmethod
    def validate_category(cls, v: str) -> str:
        valid_categories = [
            "intent_router",
            "param_complete",
            "retrieval",
            "response_gen",
            "error_handler",
        ]
        if v not in valid_categories:
            raise ValueError(f"Category must be one of: {', '.join(valid_categories)}")
        return v

    @field_validator("scope")
    @classmethod
    def validate_scope(cls, v: str) -> str:
        valid_scopes = ["global", "pack", "tenant"]
        if v not in valid_scopes:
            raise ValueError(f"Scope must be one of: {', '.join(valid_scopes)}")
        return v


class TemplateUpdate(BaseModel):
    """Schema for updating an existing template."""

    body: str = Field(..., description="Template content")
    actions: Optional[Dict[str, Any]] = Field(
        None, description="Actions for intent_router templates"
    )
    parameters: Optional[Dict[str, Any]] = Field(None, description="Parameter schema")
    change_reason: Optional[str] = Field(None, description="Reason for the update")


class TemplateRollback(BaseModel):
    """Schema for rolling back to a previous template version."""

    version: int = Field(..., description="Version to roll back to")
    change_reason: Optional[str] = Field(None, description="Reason for the rollback")


class TemplateRead(BaseModel):
    """Schema for reading a template."""

    id: uuid.UUID
    key: str
    category: str
    scope: str
    scope_id: Optional[uuid.UUID]
    tenant_id: Optional[uuid.UUID]
    version: int
    language: str
    body: str
    description: Optional[str]
    actions: Optional[Dict[str, Any]]
    parameters: Optional[Dict[str, Any]]
    created_at: Any
    updated_at: Any

    model_config = ConfigDict(from_attributes=True)


class TemplateVersionRead(BaseModel):
    """Schema for reading a template version."""

    template_id: uuid.UUID
    version: int
    body: str
    actions: Optional[Dict[str, Any]]
    parameters: Optional[Dict[str, Any]]
    editor_id: Optional[uuid.UUID]
    edited_at: Any
    change_reason: Optional[str]

    model_config = ConfigDict(from_attributes=True)


class RenderTemplateRequest(BaseModel):
    """Schema for rendering a template."""

    key: str = Field(..., description="Template key")
    category: str = Field(..., description="Template category")
    context: Dict[str, Any] = Field(..., description="Template context variables")
    pack_id: Optional[uuid.UUID] = Field(
        None, description="Pack ID for industry pack templates"
    )
    language: str = Field("en", description="Template language code")


class RenderTemplateResponse(BaseModel):
    """Schema for template rendering response."""

    rendered: str = Field(..., description="Rendered template content")


@router.get("/", response_model=List[TemplateRead])
async def list_templates(
    request: Request,
    category: Optional[str] = Query(None, description="Filter by category"),
    scope: Optional[str] = Query(None, description="Filter by scope"),
    language: str = Query("en", description="Filter by language"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.TEMPLATE_READ)),
):
    """List all templates for the current organization."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    
    # System admins can see all templates
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    if is_system_admin:
        # For system admins, we might want to show all templates or require tenant selection
        # For now, require tenant context even for system admins
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="System admin must specify a tenant context to list templates"
            )
    
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant context required to list templates"
        )
    
    templates = await template_service.list_templates(
        db, tenant_id=tenant_id, category=category, scope=scope, language=language
    )
    return templates


@router.post("/", response_model=TemplateRead, status_code=201)
async def create_template(
    request: Request,
    template: TemplateCreate,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.TEMPLATE_CREATE)),
):
    """Create a new template."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant context required to create templates"
        )
    
    try:
        # For tenant scope, set the tenant_id
        if template.scope == "tenant":
            tenant_id_param = tenant_id
            scope_id_param = tenant_id
        else:
            tenant_id_param = None
            scope_id_param = template.scope_id

        created_template = await template_service.create_template(
            db=db,
            key=template.key,
            category=template.category,
            body=template.body,
            scope=template.scope,
            scope_id=scope_id_param,
            tenant_id=tenant_id_param,
            language=template.language,
            description=template.description,
            actions=template.actions,
            parameters=template.parameters,
        )
        return created_template
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.get("/{template_id}", response_model=TemplateRead)
async def get_template(
    request: Request,
    template_id: uuid.UUID = Path(..., description="Template ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.TEMPLATE_READ)),
):
    """Get a single template by ID."""
    logging.info(f"Entering get_template endpoint for ID: {template_id}")
    
    tenant_id = getattr(request.state, 'tenant_id', None)
    is_system_admin = getattr(request.state, "is_system_admin", False)
    
    logging.info(f"Request context - tenant_id: {tenant_id}, is_system_admin: {is_system_admin}")

    # Check if system admin and bypass tenant check if true
    if is_system_admin:
        logging.info(f"System admin access: Bypassing tenant check for template {template_id}")
        try:
            result = await db.get(Template, template_id)
            logging.info(f"Database query for template ID {template_id} completed for system admin. Found: {result is not None}")
            if not result:
                logging.warning(f"Template with ID {template_id} not found for system admin.")
                raise HTTPException(status_code=404, detail="Template not found")
            logging.info(f"Successfully fetched template with ID {template_id} for system admin")
            return result
        except Exception as e:
            logging.error(f"Error fetching template with ID {template_id} for system admin: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error fetching template: {e}") from e

    # Standard tenant check for non-system admin users
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant context required to access template"
        )
    
    try:
        # Find the template directly by ID
        result = await db.get(Template, template_id)
        logging.info(f"Database query for template ID {template_id} completed for tenant {tenant_id}. Found: {result is not None}")
    except Exception as e:
        logging.error(f"Error fetching template with ID {template_id} for tenant {tenant_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error fetching template: {e}") from e

    if not result:
        logging.warning(f"Template with ID {template_id} not found.")
        raise HTTPException(status_code=404, detail="Template not found")

    # Check if the template belongs to the current tenant or is global
    if result.tenant_id != tenant_id and result.scope != "global":
         logging.warning(f"Template with ID {template_id} found but does not belong to tenant {tenant_id} and is not global.")
         raise HTTPException(status_code=404, detail="Template not found or you do not have access")

    logging.info(f"Successfully fetched template with ID {template_id} for tenant {tenant_id}")
    return result


@router.put("/{template_id}", response_model=TemplateRead)
async def update_template(
    request: Request,
    template_update: TemplateUpdate,
    template_id: uuid.UUID = Path(..., description="Template ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.TEMPLATE_UPDATE)),
):
    """Update an existing template."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant context required to update templates"
        )
    
    try:
        updated_template = await template_service.update_template(
            db=db,
            template_id=template_id,
            body=template_update.body,
            actions=template_update.actions,
            parameters=template_update.parameters,
            change_reason=template_update.change_reason,
        )
        return updated_template
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.delete("/{template_id}", status_code=204)
async def delete_template(
    request: Request,
    template_id: uuid.UUID = Path(..., description="Template ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.TEMPLATE_DELETE)),
):
    """Delete a template."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant context required to delete templates"
        )
    
    success = await template_service.delete_template(db, template_id)
    if not success:
        raise HTTPException(status_code=404, detail="Template not found")
    return None


@router.post("/render", response_model=RenderTemplateResponse)
async def render_template(
    request: RenderTemplateRequest,
    request_obj: Request,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.TEMPLATE_READ)),
):
    """Render a template with the given context."""
    tenant_id = getattr(request_obj.state, 'tenant_id', None)
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant context required to render templates"
        )
    
    try:
        rendered = await template_service.render_template(
            db=db,
            key=request.key,
            category=request.category,
            context=request.context,
            tenant_id=tenant_id,
            pack_id=request.pack_id,
            language=request.language,
        )
        return RenderTemplateResponse(rendered=rendered)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.get("/versions/{template_id}", response_model=List[TemplateVersionRead])
async def get_template_versions(
    request: Request,
    template_id: uuid.UUID = Path(..., description="Template ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.TEMPLATE_READ_VERSIONS)),
):
    """Get all versions of a template."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant context required to access template versions"
        )
    
    try:
        # Verify template exists and belongs to this tenant
        from sqlalchemy import select

        from src.coherence.models.template import Template

        template_query = select(Template).where(
            (Template.id == template_id)
            & ((Template.tenant_id == tenant_id) | (Template.scope == "global"))
        )

        result = await db.execute(template_query)
        template = result.scalars().first()

        if not template:
            raise HTTPException(
                status_code=404,
                detail=f"Template with ID {template_id} not found or you don't have access",
            )

        # Get template versions
        from sqlalchemy import desc, select

        from src.coherence.models.template_version import TemplateVersion

        versions_query = (
            select(TemplateVersion)
            .where(TemplateVersion.template_id == template_id)
            .order_by(desc(TemplateVersion.version))
        )

        result = await db.execute(versions_query)
        versions = result.scalars().all()

        return versions
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error retrieving template versions: {str(e)}"
        ) from e


@router.post("/versions/{template_id}/rollback", response_model=TemplateRead)
async def rollback_template(
    request: Request,
    rollback: TemplateRollback,
    template_id: uuid.UUID = Path(..., description="Template ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.TEMPLATE_UPDATE)),
):
    """Roll back a template to a previous version."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant context required to rollback templates"
        )
    
    try:
        # First verify the version exists
        from sqlalchemy import select

        from src.coherence.models.template_version import TemplateVersion

        version_query = select(TemplateVersion).where(
            (TemplateVersion.template_id == template_id)
            & (TemplateVersion.version == rollback.version)
        )

        result = await db.execute(version_query)
        version = result.scalars().first()

        if not version:
            raise HTTPException(
                status_code=404,
                detail=f"Template version {rollback.version} not found for template {template_id}",
            )

        # Then update the template with the content from this version
        change_reason = (
            rollback.change_reason or f"Rollback to version {rollback.version}"
        )

        updated_template = await template_service.update_template(
            db=db,
            template_id=template_id,
            body=version.body,
            actions=version.actions,
            parameters=version.parameters,
            change_reason=change_reason,
        )

        return updated_template
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error rolling back template: {str(e)}"
        ) from e


@router.post("/default/{category}", response_model=TemplateRead, status_code=201)
async def create_default_template(
    request: Request,
    category: str = Path(..., description="Template category"),
    key: str = Query(..., description="Template key"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.TEMPLATE_CREATE)),
):
    """Create a template from default template for the given category."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant context required to create templates"
        )
    
    template_body = get_default_template(category)
    if not template_body:
        raise HTTPException(
            status_code=400,
            detail=f"No default template found for category: {category}",
        )

    try:
        created_template = await template_service.create_template(
            db=db,
            key=key,
            category=category,
            body=template_body,
            scope="tenant",
            scope_id=tenant_id,
            tenant_id=tenant_id,
        )
        return created_template
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
