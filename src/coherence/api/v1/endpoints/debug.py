"""
Debug endpoints for troubleshooting JWT and authentication.
These should be disabled in production.
"""

import logging
import os
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    get_clerk_auth_details,
)
from src.coherence.core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()

class JWTDebugResponse(BaseModel):
    user_id: str
    is_system_admin: bool
    jwt_claims: Dict[str, Any]
    org_id: Optional[str] = None
    org_name: Optional[str] = None
    org_role: Optional[str] = None
    org_slug: Optional[str] = None
    org_metadata: Optional[Dict[str, Any]] = None
    env_settings: Dict[str, Any]

@router.get(
    "/debug/jwt",
    response_model=JWTDebugResponse,
    summary="Debug JWT claims and auth details",
    description="Returns detailed information about the JWT token and authentication state for debugging.",
)
async def debug_jwt(
    request: Request,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
) -> JWTDebugResponse:
    """
    Debug endpoint for JWT and auth issues.
    Only available when DEBUG_ENDPOINTS is enabled.
    """
    # This will only be available when DEBUG_ENDPOINTS is enabled
    if not getattr(settings, "DEBUG_ENDPOINTS", False):
        logger.warning("Debug endpoints are disabled. Set DEBUG_ENDPOINTS=true to enable.")
        return JWTDebugResponse(
            user_id=clerk_auth.user_id,
            is_system_admin=clerk_auth.is_system_admin,
            jwt_claims={"message": "Debug endpoints are disabled"},
            env_settings={"DEBUG_ENDPOINTS": False}
        )
    
    # Extract raw JWT claims from the request state (added during authentication)
    raw_claims = getattr(request.state, "jwt_claims", {})
    
    # Log detailed JWT debug information
    logger.info(f"JWT Debug Request for user {clerk_auth.user_id}")
    logger.info(f"JWT Raw Claims: {raw_claims}")
    
    # Return comprehensive debug information
    return JWTDebugResponse(
        user_id=clerk_auth.user_id,
        is_system_admin=clerk_auth.is_system_admin,
        jwt_claims=raw_claims,
        org_id=clerk_auth.org_id,
        org_name=clerk_auth.org_name,
        org_role=clerk_auth.org_role,
        org_slug=clerk_auth.org_slug,
        org_metadata=clerk_auth.org_metadata,
        env_settings={
            "DEBUG_ENDPOINTS": getattr(settings, "DEBUG_ENDPOINTS", False),
            "ENV": settings.ENV,
            "SYSTEM_ADMIN_CLERK_USER_ID": os.environ.get("SYSTEM_ADMIN_CLERK_USER_ID"),
        }
    )