"""Tenant management endpoints for the Coherence API."""

import hashlib
import logging  # Added import
import secrets
import sys
import uuid
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, status
from sqlalchemy import text  # Added for SET LOCAL
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,  # New
    RequirePermission,
    check_is_system_admin,  # New
    get_clerk_auth_details,  # New
    get_tenant_from_api_key,  # Will be replaced
)
from src.coherence.db.deps import get_db

# APIKey model might not be needed here if we remove its creation during tenant creation
from src.coherence.models.tenant import (  # Restoring APIKey import
    APIKey,
    Tenant,
    TenantSettings,
)
from src.coherence.schemas.tenant import (
    APIKeyCreate,
    APIKeyCreateResponse,
    APIKeyRead,
    APIKeyUpdate,
    TenantCreate,
    TenantRead,
    TenantSettingsRead,
    TenantSettingsUpdate,
    TenantUpdate,
)
from src.coherence.services.permission_service import (
    CoherencePermission,  # Add this import
)

router = APIRouter(tags=["Organizations"]) # Added default tag
logger = logging.getLogger(__name__) # Added logger


# Public endpoint for Clerk organization ID lookup
@router.get("/tenants/by-clerk-org", response_model=Optional[TenantRead], tags=["Organizations Lookup"]) # Clarified tag
async def get_tenant_by_clerk_org(
    clerk_org_id: str = Query(..., description="Filter by Clerk organization ID"),
    db: AsyncSession = Depends(get_db),
) -> Optional[TenantRead]:
    """
    Get a tenant by Clerk organization ID.

    This is a public endpoint that doesn't require authentication,
    specifically for the Admin UI to associate Clerk orgs with tenants.
    """
    print(f"DEBUG_TENANTS: Entered get_tenant_by_clerk_org with clerk_org_id: {clerk_org_id}", file=sys.stderr)
    # Set session variable for RLS to allow lookup by clerk_org_id
    await db.execute(text(f"SET LOCAL app.rls.lookup_clerk_org_id = '{clerk_org_id}';"))
    print(f"DEBUG_TENANTS: Executing query for: {clerk_org_id}", file=sys.stderr)
    result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id))
    tenant = result.scalar_one_or_none()
    print(f"DEBUG_TENANTS: Tenant found by query: {tenant}", file=sys.stderr)
    return tenant

# Tenant management endpoints
@router.get("/tenants", response_model=List[TenantRead], tags=["System Admin - Organizations"]) # Clarified tag
async def list_tenants(
    request: Request, # Added request
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    db: AsyncSession = Depends(get_db),
    # Auth: Ensure this endpoint is only accessible by system admins.
    # This relies on a primary auth dependency (e.g., get_clerk_auth_details with a system admin JWT,
    # or get_org_api_key_principal with the master key) having set request.state.is_system_admin.
    _system_admin_check: None = Depends(check_is_system_admin),
) -> List[TenantRead]:
    """
    List all tenants (organizations).
    Only accessible to system administrators.
    """
    # The check_is_system_admin dependency handles authorization.
    # It will raise an HTTPException if request.state.is_system_admin is not True.
    if not getattr(request.state, "is_system_admin", False):
         # This check is a safeguard if check_is_system_admin isn't used or somehow bypassed
        logger.error("list_tenants accessed without system admin context after dependency check.") # Should not happen
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="System administrator privileges required.")

    logger.info("Listing all tenants (system admin access).")
    result = await db.execute(select(Tenant).offset(skip).limit(limit))
    return list(result.scalars().all())


@router.post("/tenants", response_model=TenantRead, status_code=status.HTTP_201_CREATED, tags=["System Admin - Organizations"]) # Clarified tag
async def create_tenant(
    request: Request, # Added request
    tenant_in: TenantCreate,
    db: AsyncSession = Depends(get_db),
    # Use new system admin check. Assumes a primary auth (master key or Clerk sys admin JWT)
    # has already populated request.state.is_system_admin via a router-level or other dependency.
    # For direct protection if no router-level dependency:
    _system_admin_check: None = Depends(check_is_system_admin),
    # If get_clerk_auth_details is used at router level:
    # auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details)
) -> TenantRead: # Changed response_model
    """
    Create a new tenant (organization).
    This endpoint is now only accessible to system administrators.
    It no longer creates an old-style API key. New Organization API Keys should be created separately.
    """
    # Ensure system admin context is set by a preceding dependency
    if not getattr(request.state, "is_system_admin", False):
         # This check is a safeguard if check_is_system_admin isn't used or fails silently
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="System administrator privileges required.")

    if not tenant_in.clerk_org_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="clerk_org_id is required to create a new tenant/organization.",
        )
    
    # Create new tenant
    db_tenant = Tenant(
        name=tenant_in.name,
        clerk_org_id=tenant_in.clerk_org_id,
        industry_pack=tenant_in.industry_pack,
        compliance_tier=tenant_in.compliance_tier,
        settings=tenant_in.settings if tenant_in.settings is not None else {"is_admin": False}, # Ensure settings default
    )
    db.add(db_tenant)
    
    # Create default tenant settings if not provided or ensure is_admin is set
    # The tenant.settings now defaults to {"is_admin": False} if tenant_in.settings is None.
    # If specific default settings are needed, they can be added here.
    # For example, if TenantSettings model is still primary for these:
    TenantSettings(
         tenant_id=db_tenant.id, # This requires db_tenant.id, so flush first
         tier1_threshold="0.85", # Example defaults
         tier2_threshold="0.7",
         llm_model="gpt-4",
         embedding_model="text-embedding-ada-002",
         # settings field in TenantSettings can store other misc settings
    )
    # db.add(db_settings) # This would require db_tenant.id to be available

    try:
        # Flush to get db_tenant.id if needed for TenantSettings FK
        await db.flush()
        # Now create TenantSettings if it's a separate linked table and needs db_tenant.id
        # If settings are directly on Tenant model, this part might change.
        # Assuming TenantSettings is still used:
        if not await db.get(TenantSettings, db_tenant.id): # Check if settings exist
            default_tenant_settings_values = {
                "tier1_threshold": "0.85", "tier2_threshold": "0.7",
                "llm_model": "gpt-4", "embedding_model": "text-embedding-ada-002",
                "max_requests_per_min": "60", "max_tokens_per_month": "1000000",
            }
            # If tenant_in.settings provided some, merge them, otherwise use defaults.
            # The Tenant model's settings field might also store some of this.
            # This logic needs to align with how Tenant.settings and TenantSettings table interact.
            # For now, creating a basic TenantSettings entry:
            db_tenant_settings_obj = TenantSettings(tenant_id=db_tenant.id, **default_tenant_settings_values)
            db.add(db_tenant_settings_obj)

        await db.commit()
        await db.refresh(db_tenant)
    except IntegrityError as err:
        await db.rollback()
        logger.error(f"Error creating tenant: {err}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Tenant with this name or Clerk organization ID already exists.",
        ) from err
    except Exception as e:
        await db.rollback()
        logger.error(f"Unexpected error creating tenant: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while creating the tenant.",
        ) from e

    return db_tenant # Return TenantRead


@router.get("/organizations/{clerk_org_id_param}", response_model=TenantRead, tags=["Organizations"])
async def get_organization_by_clerk_id(
    request: Request,
    clerk_org_id_param: str = Path(..., description="Clerk Organization ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
) -> TenantRead:
    """
    Get a specific organization (tenant) by its Clerk Organization ID.

    Accessible by:
    - System administrators.
    - Members of the specified organization.
    """
    is_system_admin = getattr(request.state, "is_system_admin", False)
    
    # If not system admin, check if the authenticated user's org matches the path org_id
    if not is_system_admin:
        # auth_details.org_id is the clerk_org_id from the JWT
        if auth_details.org_id != clerk_org_id_param:
            logger.warning(
                f"User {auth_details.user_id} in org {auth_details.org_id} "
                f"attempted to access organization {clerk_org_id_param} without permission."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this organization's information.",
            )
    
    logger.info(
        f"Accessing organization {clerk_org_id_param}. "
        f"User: {auth_details.user_id}, Auth Org: {auth_details.org_id}, SystemAdmin: {is_system_admin}"
    )
    result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id_param))
    db_tenant = result.scalar_one_or_none()

    if not db_tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found",
        )

    return db_tenant


@router.put("/organizations/{clerk_org_id_param}", response_model=TenantRead, tags=["Organizations"])
async def update_organization_by_clerk_id(
    request: Request,
    tenant_in: TenantUpdate,
    clerk_org_id_param: str = Path(..., description="Clerk Organization ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
) -> TenantRead:
    """
    Update an organization (tenant) by its Clerk Organization ID.

    Accessible by:
    - System administrators.
    - Admin members of the specified organization.
    """
    is_system_admin = getattr(request.state, "is_system_admin", False)
    
    if not is_system_admin:
        # auth_details.org_id is the clerk_org_id from the JWT
        if auth_details.org_id != clerk_org_id_param:
            logger.warning(
                f"User {auth_details.user_id} in org {auth_details.org_id} "
                f"attempted to update organization {clerk_org_id_param} (wrong org)."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this organization.",
            )
        
        # If it's the correct org, now check if user is org admin
        if auth_details.org_role not in ["admin", "owner"]:
            logger.warning(
                f"User {auth_details.user_id} in org {auth_details.org_id} (role: {auth_details.org_role}) "
                f"attempted to update organization {clerk_org_id_param} without org admin role."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Organization admin privileges required to update this organization.",
            )
    
    logger.info(
        f"Updating organization {clerk_org_id_param}. "
        f"User: {auth_details.user_id}, Auth Org: {auth_details.org_id}, Role: {auth_details.org_role}, SystemAdmin: {is_system_admin}"
    )
    result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id_param))
    db_tenant = result.scalar_one_or_none()

    if not db_tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found",
        )

    # Update tenant fields
    update_data = tenant_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_tenant, field, value)

    try:
        await db.commit()
        await db.refresh(db_tenant)
        return db_tenant
    except IntegrityError as err:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Tenant update failed due to constraint violation",
        ) from err


@router.delete("/organizations/{clerk_org_id_param}", status_code=status.HTTP_204_NO_CONTENT, tags=["System Admin - Organizations"])
async def delete_organization_by_clerk_id(
    request: Request,
    clerk_org_id_param: str = Path(..., description="Clerk Organization ID"),
    db: AsyncSession = Depends(get_db),
    _system_admin_check: None = Depends(check_is_system_admin),
) -> None:
    """
    Delete an organization (tenant) by its Clerk Organization ID.
    Only accessible to system administrators.
    """
    # System admin check is handled by the dependency.
    # Ensure request.state.is_system_admin is True if the dependency doesn't raise.
    if not getattr(request.state, "is_system_admin", False):
         # This is a safeguard. check_is_system_admin should raise if not admin.
        logger.error("delete_organization_by_clerk_id accessed without system admin context after dependency check.")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="System administrator privileges required.")

    logger.info(f"System admin deleting organization {clerk_org_id_param}.")
    result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id_param))
    db_tenant = result.scalar_one_or_none()

    if not db_tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found",
        )

    await db.delete(db_tenant)
    await db.commit()


@router.get("/admin/organizations/{organization_id}", response_model=TenantRead, tags=["Admin - Organizations"])
async def get_admin_organization_dashboard(
    request: Request,
    organization_id: str = Path(..., description="The Clerk Organization ID of the organization"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.ORGANIZATION_VIEW_OWN_DASHBOARD)),
) -> TenantRead:
    """
    Get a specific organization's dashboard details by its Clerk Organization ID.

    Accessible by:
    - System administrators (can view any organization).
    - Admin/Member of the specified organization (can only view their own organization's dashboard).

    The `RequirePermission(ORGANIZATION_VIEW_OWN_DASHBOARD)` dependency ensures the user has the general
    permission to view organization dashboards. The logic below further refines this for non-system admins
    to ensure they can only view their own.
    """
    is_system_admin = getattr(request.state, "is_system_admin", False)

    if not is_system_admin:
        if auth_details.org_id != organization_id:
            logger.warning(
                f"User {auth_details.user_id} in org {auth_details.org_id} "
                f"attempted to access organization dashboard for {organization_id} without permission."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this organization's dashboard.",
            )
    
    logger.info(
        f"Accessing organization dashboard for {organization_id}. "
        f"User: {auth_details.user_id}, Auth Org: {auth_details.org_id}, SystemAdmin: {is_system_admin}"
    )

    result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == organization_id))
    db_organization = result.scalar_one_or_none()

    if not db_organization:
        logger.warning(
            f"Organization dashboard not found or access denied for organization_id: {organization_id}. "
            f"Auth User: {auth_details.user_id}, Auth Org: {auth_details.org_id}"
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Organization with ID '{organization_id}' not found or access denied.",
        )

    logger.info(f"Successfully fetched organization dashboard for organization_id: {organization_id}, name: {db_organization.name}")
    return db_organization


# API Key management endpoints
@router.get("/tenants/{tenant_id}/api-keys", response_model=List[APIKeyRead], deprecated=True, tags=["Deprecated Tenant API Keys"])
async def list_tenant_api_keys(
    tenant_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_tenant_from_api_key), # Uses old auth
) -> List[APIKeyRead]:
    """
    (DEPRECATED) List all API keys for a specific tenant (using old APIKey model).
    Please use the new organization-based API key endpoints: /admin/organizations/{clerk_org_id}/api-keys

    Tenants can only access their own API keys.
    Admins can access any tenant's API keys.
    """
    logger.warning(f"Deprecated endpoint GET /{tenant_id}/api-keys called.")
    # Verify tenant has access or is admin
    tenant_settings = current_tenant.settings or {}
    is_admin = tenant_settings.get("is_admin", False)
    if str(current_tenant.id) != str(tenant_id) and not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this tenant's API keys",
        )

    result = await db.execute(select(APIKey).where(APIKey.tenant_id == tenant_id))
    return list(result.scalars().all())


@router.post(
    "/{tenant_id}/api-keys",
    response_model=APIKeyCreateResponse,
    status_code=status.HTTP_201_CREATED,
    deprecated=True,
    tags=["Deprecated Tenant API Keys"]
)
async def create_api_key(
    request: Request,
    api_key_in: APIKeyCreate,
    tenant_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_tenant_from_api_key), # Uses old auth
) -> APIKeyCreateResponse:
    """
    (DEPRECATED) Create a new API key for a tenant (using old APIKey model).
    Please use the new organization-based API key endpoints: /admin/organizations/{clerk_org_id}/api-keys

    Tenants can only create API keys for themselves.
    Admins can create API keys for any tenant.
    """
    logger.warning(f"Deprecated endpoint POST /{tenant_id}/api-keys called.")
    # Verify tenant_id in path matches tenant_id in body
    if str(tenant_id) != str(api_key_in.tenant_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant ID in path must match tenant ID in request body",
        )

    # Verify tenant has access or is admin or system admin
    is_system_admin = getattr(request.state, "is_system_admin", False)
    is_admin = False

    if not is_system_admin:
        # Only check tenant settings if not system admin
        tenant_settings = current_tenant.settings or {}
        is_admin = tenant_settings.get("is_admin", False)

    if str(current_tenant.id) != str(tenant_id) and not (is_admin or is_system_admin):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to create API keys for this tenant",
        )

    # Verify tenant exists
    result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
    if not result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found",
        )

    # Generate new API key
    api_key_value = f"coh_{secrets.token_urlsafe(32)}"
    api_key_hash = hashlib.sha256(api_key_value.encode()).hexdigest()

    db_api_key = APIKey(
        tenant_id=api_key_in.tenant_id,
        label=api_key_in.label,
        key_hash=api_key_hash,
        expires_at=api_key_in.expires_at,
        revoked=False,
    )
    db.add(db_api_key)

    try:
        await db.commit()
        await db.refresh(db_api_key)

        # Return the newly created API key with the key value
        # This is the only time the key value will be visible
        return APIKeyCreateResponse(
            **db_api_key.__dict__,
            key=api_key_value,
        )
    except IntegrityError as err:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="API key creation failed due to constraint violation",
        ) from err


@router.put("/tenants/{tenant_id}/api-keys/{api_key_id}", response_model=APIKeyRead, deprecated=True, tags=["Deprecated Tenant API Keys"])
async def update_api_key(
    api_key_in: APIKeyUpdate,
    tenant_id: uuid.UUID = Path(...),
    api_key_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_tenant_from_api_key), # Uses old auth
) -> APIKeyRead:
    """
    (DEPRECATED) Update an API key (using old APIKey model).
    Please use the new organization-based API key endpoints for any future updates if applicable,
    though direct updates to new keys might have different path/payload.

    Tenants can only update their own API keys.
    Admins can update any tenant's API keys.
    """
    logger.warning(f"Deprecated endpoint PUT /{tenant_id}/api-keys/{api_key_id} called.")
    # Verify tenant has access or is admin
    tenant_settings = current_tenant.settings or {}
    is_admin = tenant_settings.get("is_admin", False)
    if str(current_tenant.id) != str(tenant_id) and not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update API keys for this tenant",
        )

    # Get the API key
    result = await db.execute(
        select(APIKey).where(
            APIKey.id == api_key_id,
            APIKey.tenant_id == tenant_id,
        )
    )
    db_api_key = result.scalar_one_or_none()

    if not db_api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API key not found",
        )

    # Update API key fields
    update_data = api_key_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_api_key, field, value)

    try:
        await db.commit()
        await db.refresh(db_api_key)
        return db_api_key
    except IntegrityError as err:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="API key update failed due to constraint violation",
        ) from err


@router.delete(
    "/tenants/{tenant_id}/api-keys/{api_key_id}", status_code=status.HTTP_204_NO_CONTENT, deprecated=True, tags=["Deprecated Tenant API Keys"]
)
async def delete_api_key(
    tenant_id: uuid.UUID = Path(...),
    api_key_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_tenant_from_api_key), # Uses old auth
) -> None:
    """
    (DEPRECATED) Delete an API key (using old APIKey model).
    Please use the new organization-based API key endpoints: /admin/organizations/{clerk_org_id}/api-keys/{key_id}

    Tenants can only delete their own API keys.
    Admins can delete any tenant's API keys.
    """
    logger.warning(f"Deprecated endpoint DELETE /{tenant_id}/api-keys/{api_key_id} called.")
    # Verify tenant has access or is admin
    tenant_settings = current_tenant.settings or {}
    is_admin = tenant_settings.get("is_admin", False)
    if str(current_tenant.id) != str(tenant_id) and not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete API keys for this tenant",
        )

    # Get the API key
    result = await db.execute(
        select(APIKey).where(
            APIKey.id == api_key_id,
            APIKey.tenant_id == tenant_id,
        )
    )
    db_api_key = result.scalar_one_or_none()

    if not db_api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API key not found",
        )

    await db.delete(db_api_key)
    await db.commit()


# Tenant Settings endpoints
@router.get("/organizations/{clerk_org_id_param}/settings", response_model=TenantSettingsRead, tags=["Organizations"])
async def get_organization_settings(
    request: Request,
    clerk_org_id_param: str = Path(..., description="Clerk Organization ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
) -> TenantSettingsRead:
    """
    Get settings for a specific organization by its Clerk Organization ID.

    Accessible by:
    - System administrators.
    - Members of the specified organization.
    """
    is_system_admin = getattr(request.state, "is_system_admin", False)
    
    if not is_system_admin:
        # auth_details.org_id is the clerk_org_id from the JWT
        if auth_details.org_id != clerk_org_id_param:
            logger.warning(
                f"User {auth_details.user_id} in org {auth_details.org_id} "
                f"attempted to access settings for organization {clerk_org_id_param} without permission."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this organization's settings.",
            )
    
    # First, get the tenant (organization) by clerk_org_id_param to find its internal ID
    tenant_result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id_param))
    db_tenant = tenant_result.scalar_one_or_none()

    if not db_tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found, cannot retrieve settings.",
        )

    logger.info(
        f"Accessing settings for organization {clerk_org_id_param} (Tenant ID: {db_tenant.id}). "
        f"User: {auth_details.user_id}, Auth Org: {auth_details.org_id}, SystemAdmin: {is_system_admin}"
    )
    
    result = await db.execute(
        select(TenantSettings).where(TenantSettings.tenant_id == db_tenant.id)
    )
    db_settings = result.scalar_one_or_none()

    if not db_settings:
        # Consider creating default settings if not found, or if that's handled elsewhere.
        # For now, matching previous behavior of 404 if settings explicitly not found.
        logger.warning(f"TenantSettings entry not found for tenant_id {db_tenant.id} (Clerk Org: {clerk_org_id_param})")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization settings not found. Please ensure settings are initialized.",
        )

    return db_settings


@router.put("/organizations/{clerk_org_id_param}/settings", response_model=TenantSettingsRead, tags=["Organizations"])
async def update_organization_settings(
    request: Request,
    settings_in: TenantSettingsUpdate,
    clerk_org_id_param: str = Path(..., description="Clerk Organization ID"),
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
) -> TenantSettingsRead:
    """
    Update settings for a specific organization by its Clerk Organization ID.

    Accessible by:
    - System administrators.
    - Admin members of the specified organization.
    """
    is_system_admin = getattr(request.state, "is_system_admin", False)
    current_user_clerk_id = auth_details.user_id
    current_session_clerk_org_id = auth_details.org_id # Clerk Org ID from JWT
    current_org_role = auth_details.org_role

    if not is_system_admin:
        if current_session_clerk_org_id != clerk_org_id_param:
            logger.warning(
                f"User {current_user_clerk_id} in org {current_session_clerk_org_id} "
                f"attempted to update settings for organization {clerk_org_id_param} (wrong org)."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this organization's settings.",
            )
        # If it's the correct org, check if user is org admin
        if current_org_role not in ["admin", "owner"]:
            logger.warning(
                f"User {current_user_clerk_id} in org {current_session_clerk_org_id} (role: {current_org_role}) "
                f"attempted to update settings for organization {clerk_org_id_param} without org admin role."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Organization admin privileges required to update this organization's settings.",
            )
    
    # First, get the tenant (organization) by clerk_org_id_param to find its internal ID
    tenant_result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id_param))
    db_tenant = tenant_result.scalar_one_or_none()

    if not db_tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found, cannot update settings.",
        )
    
    internal_tenant_id = db_tenant.id # Use this for TenantSettings table

    logger.info(
        f"Updating settings for organization {clerk_org_id_param} (Tenant ID: {internal_tenant_id}). "
        f"User: {current_user_clerk_id}, Auth Org: {current_session_clerk_org_id}, Role: {current_org_role}, SystemAdmin: {is_system_admin}"
    )
    
    result = await db.execute(
        select(TenantSettings).where(TenantSettings.tenant_id == internal_tenant_id)
    )
    db_settings = result.scalar_one_or_none()

    if not db_settings:
        # If settings don't exist, create them for this tenant
        logger.info(f"No existing settings found for tenant {internal_tenant_id} (Org: {clerk_org_id_param}). Creating new settings entry.")
        db_settings = TenantSettings(tenant_id=internal_tenant_id)
        db.add(db_settings)
        # We might need to flush here if we immediately try to use db_settings.settings
        # await db.flush() # To get default values if model has them, or ensure it's in session

    # Update settings fields
    update_data = settings_in.model_dump(exclude_unset=True)

    # Special handling for the 'is_admin' flag if it exists in TenantSettings.settings payload
    # This flag is legacy. Only system admins should be able to modify it.
    if "settings" in update_data and isinstance(update_data["settings"], dict):
        payload_internal_settings = update_data["settings"]
        db_current_internal_settings = db_settings.settings or {} # Settings from DB

        if "is_admin" in payload_internal_settings and \
           payload_internal_settings.get("is_admin") != db_current_internal_settings.get("is_admin"):
            if not is_system_admin:
                logger.warning(
                    f"User {current_user_clerk_id} (Org: {current_session_clerk_org_id}) attempted to change 'is_admin' flag "
                    f"on TenantSettings for tenant {internal_tenant_id} (Clerk Org: {clerk_org_id_param}) without system admin privileges."
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Only system administrators can change the legacy 'is_admin' flag in settings.",
                )
            else:
                 logger.info(f"System admin {current_user_clerk_id} is changing 'is_admin' flag for tenant settings {db_settings.tenant_id}.")

    for field, value in update_data.items():
        if (
            field == "settings"
            and value is not None
            and db_settings.settings is not None
        ):
            # Merge settings dictionaries
            merged_settings = db_settings.settings.copy()
            merged_settings.update(value)
            setattr(db_settings, field, merged_settings)
        else:
            setattr(db_settings, field, value)

    try:
        await db.commit()
        await db.refresh(db_settings)
        return db_settings
    except IntegrityError as err:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Tenant settings update failed due to constraint violation",
        ) from err