from typing import Optional, Set

from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import UUID4, BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    get_clerk_auth_details,
)
from src.coherence.db.deps import get_db
from src.coherence.models.tenant import Tenant
from src.coherence.services.permission_service import PermissionService

router = APIRouter()

class SessionInfoResponse(BaseModel):
    user_id: str
    tenant_id: Optional[UUID4] = None
    tenant_name: Optional[str] = None
    org_id: Optional[str] = None  # NEW: Add org_id to response
    org_name: Optional[str] = None  # NEW: Add org_name to response
    org_role: Optional[str] = None  # NEW: Add org_role to response
    is_system_admin: bool
    permissions: Set[str]

@router.get(
    "",
    response_model=SessionInfoResponse,
    summary="Get Current User Session Information",
    description="Retrieves session details for the authenticated user, including their Clerk organization details and Coherence-specific permissions.",
)
async def get_session_info(
    request: Request,
    db: AsyncSession = Depends(get_db),
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    permission_service: PermissionService = Depends(PermissionService),
) -> SessionInfoResponse:
    """
    Provides session information for the currently authenticated user.

    This includes:
    - Clerk User ID
    - Clerk Organization details (ID, name, role)
    - Associated Coherence Tenant ID and Name (if configured)
    - System administrator status
    - A set of Coherence-specific permissions
    """
    import logging
    logger = logging.getLogger(__name__)
    
    logger.info(f"Processing session info request for user ID: {clerk_auth.user_id}")
    
    if not clerk_auth.user_id:
        # This should ideally be caught by get_clerk_auth_details, but as a safeguard:
        raise HTTPException(status_code=401, detail="User not authenticated")

    clerk_user_id_val = clerk_auth.user_id
    clerk_org_id_val = clerk_auth.org_id
    is_system_admin_val = clerk_auth.is_system_admin
    clerk_org_role_val = clerk_auth.org_role
    clerk_org_name_val = clerk_auth.org_name

    # Create a fallback organization for system admins if no org is available from Clerk
    if is_system_admin_val and not clerk_org_id_val:
        clerk_org_id_val = "org_fallback_for_system_admin"
        clerk_org_name_val = "System Admin Organization"
        clerk_org_role_val = "admin"

    # Log diagnostic information about organization details
    logger.info(f"User {clerk_user_id_val} organization details: org_id={clerk_org_id_val}, org_role={clerk_org_role_val}, org_name={clerk_org_name_val}")

    coherence_permissions = permission_service.get_coherence_permissions(
        clerk_org_role=clerk_org_role_val, is_system_admin=is_system_admin_val
    )

    tenant_id: Optional[UUID4] = None
    tenant_name: Optional[str] = None

    if clerk_org_id_val:
        # Attempt to find the Coherence tenant linked to the Clerk organization ID
        # Try both clerk_org_id and clerk_organization_id columns for compatibility
        result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id_val))
        tenant = result.scalar_one_or_none()
        
        # Fallback to checking the older column name if it exists
        if not tenant:
            try:
                result = await db.execute(select(Tenant).where(Tenant.clerk_organization_id == clerk_org_id_val))
                tenant = result.scalar_one_or_none()
            except AttributeError:
                # clerk_organization_id column doesn't exist
                pass
            
        if tenant:
            tenant_id = tenant.id
            tenant_name = tenant.name
            logger.info(f"Found matching tenant: id={tenant_id}, name={tenant_name}")
        elif not is_system_admin_val:
            # If not a system admin and the org mapping is missing, this could be an issue.
            logger.warning(f"No tenant found for org_id={clerk_org_id_val} and user is not system admin")

    response = SessionInfoResponse(
        user_id=clerk_user_id_val,
        tenant_id=tenant_id,
        tenant_name=tenant_name,
        org_id=clerk_org_id_val,
        org_name=clerk_org_name_val,
        org_role=clerk_org_role_val,
        is_system_admin=is_system_admin_val,
        permissions=coherence_permissions,
    )
    
    # Log the response being sent to the client
    logger.info(f"Session info response: {response.model_dump()}")
    
    return response