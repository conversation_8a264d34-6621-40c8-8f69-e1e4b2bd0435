"""Admin endpoints for the Coherence API."""

import uuid
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    RequirePermission,
    check_is_system_admin,
    get_clerk_auth_details,
)
from src.coherence.db.deps import get_db
from src.coherence.models.integration import APIEndpoint, APIIntegration
from src.coherence.models.tenant import Tenant
from src.coherence.schemas.integration import APIIntegrationExtendedRead
from src.coherence.schemas.tenant import TenantRead
from src.coherence.services.permission_service import CoherencePermission

router = APIRouter()


@router.get("/health")
async def admin_health():
    """Admin health check endpoint."""
    return {"status": "ok"}


@router.get("/dashboard", response_model=None)
async def admin_dashboard(
    request: Request,
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _org_admin_check: None = Depends(RequirePermission(CoherencePermission.ORGANIZATION_VIEW_OWN_DASHBOARD)),
):
    """
    Admin dashboard for organization administrators.

    Only accessible to organization administrators.
    """
    tenant = getattr(request.state, 'tenant', None)
    tenant_name = tenant.name if tenant else "Unknown"
    return {
        "msg": "Admin Dashboard", 
        "is_admin": True, 
        "tenant_name": tenant_name,
        "org_id": auth_details.org_id,
        "org_name": auth_details.org_name
    }


@router.get("/system", response_model=None)
async def system_admin_dashboard(
    request: Request,
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _system_admin_check: None = Depends(check_is_system_admin),
):
    """
    System admin dashboard.

    Only accessible to system administrators.
    """
    return {
        "msg": "System Admin Dashboard",
        "is_system_admin": True,
        "user_id": auth_details.user_id,
    }


@router.get("/tenants/stats", response_model=None)
async def tenant_stats(
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _org_admin_check: None = Depends(RequirePermission(CoherencePermission.ORGANIZATION_VIEW_OWN_DASHBOARD)),
):
    """
    Get statistics about tenants.

    Only accessible to organization administrators.
    """
    # System admins can see all tenant stats
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    if is_system_admin:
        # Get all tenant stats for system admin
        result = await db.execute(select(Tenant))
        tenants = list(result.scalars().all())
    else:
        # Get only current organization's tenant for org admins
        tenant = getattr(request.state, 'tenant', None)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        tenants = [tenant]

    # Calculate statistics
    stats = {
        "total_tenants": len(tenants),
        "tenants_by_industry": {},
        "tenants_by_compliance_tier": {},
    }

    # Count tenants by industry and compliance tier
    for tenant in tenants:
        # Count by industry
        industry = tenant.industry_pack or "unknown"
        if industry not in stats["tenants_by_industry"]:
            stats["tenants_by_industry"][industry] = 0
        stats["tenants_by_industry"][industry] += 1

        # Count by compliance tier
        compliance_tier = tenant.compliance_tier or "standard"
        if compliance_tier not in stats["tenants_by_compliance_tier"]:
            stats["tenants_by_compliance_tier"][compliance_tier] = 0
        stats["tenants_by_compliance_tier"][compliance_tier] += 1

    return stats


@router.get("/system/database", response_model=None)
async def database_status(
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _system_admin_check: None = Depends(check_is_system_admin),
):
    """
    Get database status information.

    Only accessible to system administrators.
    """
    # Execute a query as system admin (bypassing RLS)
    result = await db.execute(
        text("""
    SELECT
        t.id as tenant_id,
        t.name as tenant_name,
        COUNT(a.id) as api_key_count,
        COUNT(CASE WHEN a.revoked = false THEN a.id END) as active_api_key_count
    FROM
        tenants t
    LEFT JOIN
        api_keys a ON t.id = a.tenant_id
    GROUP BY
        t.id, t.name
    """)
    )

    # Fetch results
    rows = result.fetchall()
    tenants = [
        {
            "tenant_id": str(row[0]),
            "tenant_name": row[1],
            "api_key_count": row[2],
            "active_api_key_count": row[3],
        }
        for row in rows
    ]

    return {
        "database_status": "healthy",
        "tenant_count": len(tenants),
        "tenants": tenants,
    }


@router.get("/tenants", response_model=List[TenantRead])
async def list_tenants_admin(
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _system_admin_check: None = Depends(check_is_system_admin),
) -> List[TenantRead]:
    """
    List all tenants.

    Accessible to system administrators only.
    """
    result = await db.execute(select(Tenant))
    return list(result.scalars().all())


@router.get("/integrations", response_model=List[dict])
@router.get("/integrations/all", response_model=List[dict])  # Keep the old endpoint for backward compatibility
async def list_all_integrations(
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _system_admin_check: None = Depends(check_is_system_admin),
):
    """
    List all integrations across all tenants.
    
    This endpoint bypasses tenant isolation and returns all integrations
    in the system. It's intended for system administration purposes.
    """
    # Query all integrations across all tenants
    result = await db.execute(
        text("""
        SELECT
            i.id,
            i.name,
            i.version,
            i.base_url,
            i.status,
            i.created_at,
            i.tenant_id,
            t.name as tenant_name
        FROM
            api_integrations i
        JOIN
            tenants t ON i.tenant_id = t.id
        ORDER BY
            i.name
        """)
    )
    
    integrations = result.fetchall()
    
    # Format the response
    return [
        {
            "id": str(integration.id),
            "name": integration.name,
            "version": integration.version,
            "base_url": integration.base_url,
            "status": integration.status,
            "created_at": integration.created_at.isoformat(),
            "tenant_id": str(integration.tenant_id),
            "tenant_name": integration.tenant_name,
        }
        for integration in integrations
    ]


@router.get(
    "/integrations/{integration_id}",
    response_model=APIIntegrationExtendedRead,
    summary="Get a specific API Integration with full details",
)
async def get_integration_details(
    integration_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _system_admin_check: None = Depends(check_is_system_admin),
) -> APIIntegrationExtendedRead:
    """
    Retrieve a single API Integration by its ID, including its authentication
    configuration, endpoints, and rate limits for each endpoint.

    This endpoint is accessible only to system administrators.
    """
    stmt = (
        select(APIIntegration)
        .where(APIIntegration.id == integration_id)
        .options(
            selectinload(APIIntegration.auth_config),
            selectinload(APIIntegration.endpoints).selectinload(APIEndpoint.rate_limits),
        )
    )
    result = await db.execute(stmt)
    integration = result.scalar_one_or_none()

    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"APIIntegration with id {integration_id} not found",
        )
    
    # Extract description from OpenAPI spec if not already set
    if hasattr(integration, "openapi_spec") and integration.openapi_spec:
        openapi_spec = integration.openapi_spec
        if "info" in openapi_spec and "description" in openapi_spec["info"]:
            integration.description = openapi_spec["info"]["description"]
    
    # Set api_type based on available data or default to "rest"
    integration.api_type = "rest"  # Default
    
    # For each endpoint, ensure the fields are populated correctly if not set
    for endpoint in integration.endpoints:
        if endpoint.openapi_snippet:
            # If summary is not set but exists in the snippet, use it
            if not endpoint.summary and "summary" in endpoint.openapi_snippet:
                endpoint.summary = endpoint.openapi_snippet["summary"]
            
            # If description is not set but exists in the snippet, use it  
            if not endpoint.description and "description" in endpoint.openapi_snippet:
                endpoint.description = endpoint.openapi_snippet["description"]
            
            # If tags are not set but exist in the snippet, use them
            if not endpoint.tags and "tags" in endpoint.openapi_snippet:
                endpoint.tags = endpoint.openapi_snippet["tags"]
            
            # If deprecated is not set but exists in the snippet, use it
            if "deprecated" in endpoint.openapi_snippet:
                endpoint.deprecated = endpoint.openapi_snippet["deprecated"]
    
    return integration
