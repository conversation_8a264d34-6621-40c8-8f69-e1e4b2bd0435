"""
API endpoints for testing and managing API actions.

This module provides endpoints for testing API actions with actual parameters,
validating configuration and credentials, and viewing results.
"""

import logging
import uuid
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, Request

from src.coherence.api.v1.dependencies.auth import (
    RequirePermission,
    get_clerk_auth_details,
)
from src.coherence.db.deps import get_db
from src.coherence.openapi_adapter.credential_manager import get_credential_manager
from src.coherence.openapi_adapter.dynamic_executor import get_dynamic_executor
from src.coherence.schemas.openapi import ActionTestRequest, ActionTestResponse
from src.coherence.services.permission_service import CoherencePermission

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/test", response_model=ActionTestResponse)
async def test_action(
    action_request: ActionTestRequest,
    request: Request,
    db = Depends(get_db),
    auth=Depends(RequirePermission(CoherencePermission.INTEGRATION_MANAGE_CREDENTIALS)),
) -> Dict[str, Any]:
    """
    Execute an API action with the provided parameters for testing purposes.
    
    This endpoint allows administrators to test API actions with actual parameters
    before using them in templates or workflows. It executes the API request directly
    and returns detailed information about the response.
    
    Args:
        request: The action test request containing configuration and parameters
        executor: DynamicActionExecutor dependency for executing the action
        credential_manager: CredentialManager dependency for credentials access
        auth: Authentication dependency for authorization checks
        
    Returns:
        The result of the API action execution including success status,
        response data, and any error information
        
    Raises:
        HTTPException: If the request fails due to authorization issues
    """
    # Get tenant_id from request payload or fallback to request.state which is set by auth dependencies
    tenant_id = action_request.tenant_id or getattr(request.state, "tenant_id", None)
    
    logger.info(
        f"Testing action for integration {action_request.integration_id}",
        extra={"tenant_id": str(tenant_id) if tenant_id else "unknown", "source": "request" if action_request.tenant_id else "auth"},
    )
    
    try:
        # Make a copy of action_config to avoid modifying the original request data
        action_config_copy = action_request.action_config.copy()
        
        # Check if integration_id is present and valid in action_config
        if not action_config_copy.get("integration_id"):
            # If top-level integration_id is present and valid, use it
            if action_request.integration_id:
                action_config_copy["integration_id"] = str(action_request.integration_id)
                logger.info(
                    f"Using top-level integration_id {action_request.integration_id} for action_config",
                    extra={"tenant_id": str(tenant_id) if tenant_id else "unknown"},
                )
        
        # If integration_id is provided, verify access
        if action_request.integration_id:
            # Check if the integration belongs to the tenant
            # This check is implicitly handled by Row-Level Security,
            # but we include it for clarity and additional safety
            pass
            
        # Initialize the credential manager and executor
        credential_manager = await get_credential_manager(db)
        executor = await get_dynamic_executor(credential_manager=credential_manager)
            
        # Execute the action with the provided parameters
        from uuid import UUID
        # Use tenant_id from request.state or default to a UUID if not available
        tenant_uuid = None
        
        if tenant_id:
            try:
                tenant_uuid = UUID(str(tenant_id))
            except (ValueError, TypeError):
                logger.warning(
                    f"Invalid tenant_id format: {tenant_id}, using default UUID",
                    extra={"tenant_id": str(tenant_id) if tenant_id else "unknown"},
                )
        
        # If tenant_uuid is still None, use default UUID
        if tenant_uuid is None:
            tenant_uuid = UUID("00000000-0000-0000-0000-000000000000")
            
        result = await executor.execute(
            action_config=action_config_copy,
            parameters=action_request.parameters,
            tenant_id=tenant_uuid,
        )
        
        return result
    
    except Exception as e:
        logger.exception(
            f"Error executing action test: {str(e)}",
            extra={"tenant_id": str(tenant_id) if tenant_id else "unknown"},
        )
        
        return {
            "success": False,
            "error": {
                "type": "execution_error",
                "message": f"Failed to execute API request: {str(e)}",
                "details": {"exception": str(e)},
            },
        }


@router.get("/test-history", dependencies=[Depends(RequirePermission(CoherencePermission.INTEGRATION_READ))])
async def get_test_history(
    request: Request,
    integration_id: Optional[uuid.UUID] = None,
    limit: int = 10,
    db = Depends(get_db),
    auth=Depends(get_clerk_auth_details),
) -> Dict[str, Any]:
    """
    Retrieve recent test history for API actions.
    
    This endpoint allows administrators to view recent test executions,
    their parameters, and results. This can be useful for auditing and debugging.
    
    Args:
        integration_id: Optional filter for a specific integration
        limit: Maximum number of history items to return
        auth: Authentication dependency for authorization checks
        
    Returns:
        List of recent test executions with their parameters and results
    """
    # This endpoint would typically query a database for test history
    # For now, return a placeholder response
    return {
        "test_history": [],
        "message": "Test history feature not yet implemented",
    }