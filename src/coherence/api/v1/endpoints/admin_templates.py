import logging
from typing import List
from uuid import UUID

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    RequirePermission,
    get_clerk_auth_details,
)
from src.coherence.crud.crud_admin_template import (
    crud_admin_template,  # Use actual CRUD
)
from src.coherence.db.deps import get_db as get_db_session
from src.coherence.models.template import (
    Template as TemplateModel,  # For type hinting and accessing versions
)
from src.coherence.schemas.admin_template import (
    AdminTemplate,
    AdminTemplateCategory,  # Added for category validation
    AdminTemplateCreate,
    AdminTemplateScope,  # Added for scope validation
    AdminTemplateUpdate,
    AdminTemplateVersion,
    AdminTemplateWithVersions,
)
from src.coherence.services.permission_service import CoherencePermission
from src.coherence.services.vector_indexer import VectorIndexer
from src.coherence.services.template_validator import UnifiedTemplateValidator
from src.coherence.openapi_adapter.credential_manager import CredentialManager

router = APIRouter()

def _construct_admin_template_response(template_model: TemplateModel) -> AdminTemplate:
    """Helper to construct AdminTemplate response with latest_version_details."""
    logger = logging.getLogger(__name__)
    
    template_id = getattr(template_model, 'id', 'unknown')
    template_key = getattr(template_model, 'key', 'unknown')
    
    # Log starting validation with template details
    logger.info(f"Starting validation for template id={template_id}, key={template_key}")
    
    # Do not try to access versions at this point - it causes MissingGreenlet errors
    # Simply initialize it to None, we'll manually construct the version data later if needed
    latest_version_detail = None
    
    # Global templates don't have tenant_id, so we need to handle that
    # For global templates without tenant_id, use a default UUID
    tenant_id = template_model.tenant_id
    if tenant_id is None:
        logger.info(f"Global template {template_id} has no tenant_id, using default UUID")
        # Use zeros UUID as a default for global templates - this is just for API response compatibility
        tenant_id = UUID('00000000-0000-0000-0000-000000000000')
        
    # Check category value
    template_category = template_model.category
    logger.info(f"Validating category '{template_category}' for template {template_id}")
    
    # If category is not a valid enum value, use the default
    try:
        # Try to find a matching enum value, if not use the default
        valid_categories = [c.value for c in AdminTemplateCategory]
        logger.info(f"Valid categories: {valid_categories}")
        
        if template_category not in valid_categories:
            logger.warning(f"Invalid category '{template_category}' for template {template_id}, using default")
            admin_category = AdminTemplateCategory.INTENT_ROUTER
        else:
            # Convert from TemplateCategory to AdminTemplateCategory
            admin_category = AdminTemplateCategory(template_category)
            logger.info(f"Category '{template_category}' is valid for template {template_id}")
    except Exception as e:
        logger.error(f"Error validating category for template {template_id}: {str(e)}")
        admin_category = AdminTemplateCategory.INTENT_ROUTER
    
    # When the properties don't exist or can't be accessed, provide safe defaults
    try:
        actions = template_model.actions if template_model.actions is not None else {}
        logger.info(f"Template {template_id} actions type: {type(actions).__name__}, non-empty: {bool(actions)}")
    except Exception as e:
        logger.error(f"Error accessing actions for template {template_id}: {str(e)}")
        actions = {}
        
    try:
        parameters = template_model.parameters if template_model.parameters is not None else {}
        logger.info(f"Template {template_id} parameters type: {type(parameters).__name__}, non-empty: {bool(parameters)}")
    except Exception as e:
        logger.error(f"Error accessing parameters for template {template_id}: {str(e)}")
        parameters = {}
    
    # Log validation of scope
    template_scope = getattr(template_model, 'scope', 'unknown')
    logger.info(f"Validating scope '{template_scope}' for template {template_id}")
    
    # More explicit mapping with error handling
    try:
        # Convert the scope enum from TemplateScope to AdminTemplateScope
        admin_scope = AdminTemplateScope(template_model.scope)
        
        # Check for potential issues with body content
        body_length = len(template_model.body) if template_model.body else 0
        logger.info(f"Template {template_id} body length: {body_length} characters")
        if body_length > 100000:  # Warn about extremely large templates
            logger.warning(f"Template {template_id} has very large body: {body_length} characters")
        
        # Log creation of AdminTemplate object
        logger.info(f"Creating AdminTemplate object for template {template_id}")
        
        # Log unified template fields for debugging
        logger.info(f"Template {template_id} unified fields - intent_config: {bool(template_model.intent_config)}, action_config: {bool(template_model.action_config)}, endpoint_id: {template_model.endpoint_id}")
        
        # Debug: Log the actual values
        logger.info(f"Template {template_id} intent_config value: {template_model.intent_config}")
        logger.info(f"Template {template_id} action_config value: {template_model.action_config}")
        logger.info(f"Template {template_id} ui_fields value: {template_model.ui_fields}")
        logger.info(f"Template {template_id} prompts value: {template_model.prompts}")
        logger.info(f"Template {template_id} docs_config value: {template_model.docs_config}")
        logger.info(f"Template {template_id} test_data value: {template_model.test_data}")
        logger.info(f"Template {template_id} response_format value: {template_model.response_format}")
        
        result = AdminTemplate(
            id=template_model.id,
            tenant_id=tenant_id,
            key=template_model.key,
            description=template_model.description,
            category=admin_category, 
            language=template_model.language,
            body=template_model.body,
            actions=actions,
            parameters=parameters,
            scope=admin_scope, 
            version=template_model.version,
            created_at=template_model.created_at,
            updated_at=template_model.updated_at,
            created_by=template_model.created_by,
            protected=template_model.protected,
            latest_version_details=latest_version_detail,
            # Unified template fields
            endpoint_id=template_model.endpoint_id,
            intent_config=template_model.intent_config,
            action_config=template_model.action_config,
            ui_fields=template_model.ui_fields,
            prompts=template_model.prompts,
            docs_config=template_model.docs_config,
            test_data=template_model.test_data,
            response_format=template_model.response_format
        )
        logger.info(f"Successfully created AdminTemplate for template {template_id}")
        
        # Debug: Log what's in the result object
        logger.info(f"Result object intent_config: {result.intent_config}")
        logger.info(f"Result object action_config: {result.action_config}")
        logger.info(f"Result object has intent_config: {hasattr(result, 'intent_config')}")
        
        return result
    except Exception as e:
        logger.error(f"Error creating AdminTemplate for template {template_id}: {str(e)}")
        # Log the full exception with stack trace for better debugging
        import traceback
        error_tb = traceback.format_exc()
        logger.error(f"AdminTemplate creation error details:\n{error_tb}")
        raise

def _construct_admin_template_with_versions_response(template_model: TemplateModel) -> AdminTemplateWithVersions:
    """Helper to construct AdminTemplateWithVersions response."""
    logger = logging.getLogger(__name__)
    
    # Do not try to access versions at this point - it causes MissingGreenlet errors
    # Use empty list for versions and None for latest_version_detail
    pydantic_versions: List[AdminTemplateVersion] = []
    latest_version_detail = None
    
    # Global templates don't have tenant_id, so we need to handle that
    # For global templates without tenant_id, use a default UUID
    tenant_id = template_model.tenant_id
    if tenant_id is None:
        logger.info(f"Global template {template_model.id} has no tenant_id, using default UUID")
        # Use zeros UUID as a default for global templates - this is just for API response compatibility
        tenant_id = UUID('00000000-0000-0000-0000-000000000000')
        
    # Check category value
    template_category = template_model.category
    # If category is not a valid enum value, use the default
    try:
        # Try to find a matching enum value, if not use the default
        if template_category not in [c.value for c in AdminTemplateCategory]:
            logger.warning(f"Invalid category '{template_category}' for template {template_model.id}, using default")
            admin_category = AdminTemplateCategory.INTENT_ROUTER
        else:
            # Convert from TemplateCategory to AdminTemplateCategory
            admin_category = AdminTemplateCategory(template_category)
    except Exception as e:
        logger.error(f"Error validating category for template {template_model.id}: {str(e)}")
        admin_category = AdminTemplateCategory.INTENT_ROUTER
    
    # When the properties don't exist or can't be accessed, provide safe defaults
    try:
        actions = template_model.actions if template_model.actions is not None else {}
    except Exception as e:
        logger.error(f"Error accessing actions for template {template_model.id}: {str(e)}")
        actions = {}
        
    try:
        parameters = template_model.parameters if template_model.parameters is not None else {}
    except Exception as e:
        logger.error(f"Error accessing parameters for template {template_model.id}: {str(e)}")
        parameters = {}
    
    # More explicit mapping with error handling
    try:
        # Convert the scope enum from TemplateScope to AdminTemplateScope
        admin_scope = AdminTemplateScope(template_model.scope)
        
        return AdminTemplateWithVersions(
            id=template_model.id,
            tenant_id=tenant_id,
            key=template_model.key,
            description=template_model.description,
            category=admin_category,
            language=template_model.language,
            body=template_model.body,
            actions=actions,
            parameters=parameters,
            scope=admin_scope,
            version=template_model.version,
            created_at=template_model.created_at,
            updated_at=template_model.updated_at,
            created_by=template_model.created_by,
            protected=template_model.protected,
            latest_version_details=latest_version_detail,
            versions=pydantic_versions,
            # Unified template fields
            endpoint_id=template_model.endpoint_id,
            intent_config=template_model.intent_config,
            action_config=template_model.action_config,
            ui_fields=template_model.ui_fields,
            prompts=template_model.prompts,
            docs_config=template_model.docs_config,
            test_data=template_model.test_data,
            response_format=template_model.response_format
        )
    except Exception as e:
        logger.error(f"Error creating AdminTemplateWithVersions for template {template_model.id}: {str(e)}")
        raise


@router.get(
    "/", 
    response_model=List[AdminTemplate],
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_READ))]
)
async def list_templates(
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details),
    skip: int = 0,
    limit: int = 100,
    include_global: bool = True
) -> List[AdminTemplate]:
    """
    List templates for the current tenant.
    
    By default, includes global templates. Set include_global=false to show only tenant-specific templates.
    """
    logger = logging.getLogger(__name__)
    logger.info(f"list_templates called: skip={skip}, limit={limit}, include_global={include_global}")
    
    tenant_id = getattr(request.state, 'tenant_id', None)
    clerk_org_id = getattr(request.state, 'clerk_org_id', None)
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    logger.info(f"Request context: tenant_id={tenant_id}, clerk_org_id={clerk_org_id}, is_system_admin={is_system_admin}")
    
    # For Clerk organization-based auth, we use clerk_org_id directly
    # No need to require a tenant record
    if not clerk_org_id and not is_system_admin:
        # Non-system admins must have a Clerk organization context
        logger.warning("Template listing denied - no Clerk organization context and not system admin")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Organization context required to list templates."
        )
    
    # If we have a clerk_org_id but no tenant_id, that's OK - we'll use clerk_org_id for filtering
    # This supports the new Clerk organization-based architecture without requiring tenant records
    
    # Get templates with the option to include global templates
    # Prefer tenant_id if available, otherwise use clerk_org_id
    if tenant_id:
        logger.info(f"Fetching templates for tenant_id={tenant_id}, include_global={include_global}")
        db_templates = await crud_admin_template.get_multi_by_tenant(
            db, tenant_id=UUID(str(tenant_id)), skip=skip, limit=limit, include_global=include_global
        )
    elif clerk_org_id:
        # For clerk org without tenant mapping, we'll fetch global templates only for now
        # In the future, this could be extended to support org-specific templates
        logger.info(f"Fetching templates for clerk_org_id={clerk_org_id}, fetching global templates only")
        from sqlalchemy.future import select
        from src.coherence.models.template import Template as TemplateModel
        
        # Fetch only global templates (tenant_id is NULL)
        query = select(TemplateModel).where(TemplateModel.tenant_id.is_(None))
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        db_templates = result.scalars().all()
    elif is_system_admin:
        # System admin can see all templates
        logger.info("System admin fetching all templates")
        from sqlalchemy.future import select
        from src.coherence.models.template import Template as TemplateModel
        
        query = select(TemplateModel)
        if include_global:
            # Include all templates
            pass
        else:
            # Exclude global templates
            query = query.where(TemplateModel.tenant_id.isnot(None))
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        db_templates = result.scalars().all()
    else:
        logger.error("No tenant_id or clerk_org_id available, cannot fetch templates")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No organization context available",
        )
    
    logger.info(f"Found {len(db_templates)} total templates before validation")
    
    # Log the template keys and categories for debugging
    for idx, template in enumerate(db_templates):
        template_key = getattr(template, 'key', 'unknown')
        template_id = getattr(template, 'id', 'unknown')
        template_category = getattr(template, 'category', 'unknown')
        template_scope = getattr(template, 'scope', 'unknown')
        template_tenant_id = getattr(template, 'tenant_id', None)
        
        # Check if this might be an ICD-10 template
        is_icd10 = 'icd10' in template_key.lower() if template_key else False
        log_level = logging.INFO if not is_icd10 else logging.WARNING
        
        logger.log(log_level, f"Template {idx+1}/{len(db_templates)}: id={template_id}, key={template_key}, "  
                 f"category={template_category}, scope={template_scope}, tenant_id={template_tenant_id}")
        
        # Extra detailed logging for potential ICD-10 templates
        if is_icd10:
            logger.warning(f"Potential ICD-10 template found: {template_key}, id={template_id}")
            try:
                # Log additional details that might cause validation issues
                template_body_length = len(getattr(template, 'body', '')) if hasattr(template, 'body') else 'unknown'
                template_actions = bool(getattr(template, 'actions', None)) if hasattr(template, 'actions') else 'unknown'
                template_parameters = bool(getattr(template, 'parameters', None)) if hasattr(template, 'parameters') else 'unknown'
                logger.warning(f"ICD-10 template details: body_length={template_body_length}, "  
                             f"has_actions={template_actions}, has_parameters={template_parameters}")
            except Exception as e:
                logger.error(f"Error accessing ICD-10 template details: {str(e)}")
    
    # Apply validation to each template before returning
    validated_templates = []
    validation_failures = 0
    
    for template in db_templates:
        template_id = getattr(template, 'id', 'unknown')
        template_key = getattr(template, 'key', 'unknown')
        
        try:
            logger.info(f"Validating template id={template_id}, key={template_key}")
            validated_template = _construct_admin_template_response(template)
            validated_templates.append(validated_template)
            logger.info(f"✓ Successfully validated template id={template_id}, key={template_key}")
        except Exception as e:
            validation_failures += 1
            logger.error(f"❌ Error validating template id={template_id}, key={template_key}: {str(e)}")
            # Log the full stack trace for better debugging
            import traceback
            error_tb = traceback.format_exc()
            logger.error(f"Validation error details:\n{error_tb}")
            # Continue with other templates instead of failing the whole request
    
    logger.info(f"Template validation results: {len(validated_templates)} succeeded, {validation_failures} failed")
    if validation_failures > 0:
        logger.warning(f"Some templates ({validation_failures}) failed validation and will not be shown in the admin dashboard")
    
    return validated_templates

@router.post(
    "/", 
    response_model=AdminTemplate, 
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_CREATE))]
)
async def create_template(
    request: Request,
    template_in: AdminTemplateCreate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details)
) -> AdminTemplate:
    tenant_id = getattr(request.state, 'tenant_id', None)
    clerk_org_id = getattr(request.state, 'clerk_org_id', None)
    
    # For template creation, we currently require a tenant_id
    # Organizations without tenant mapping can only view global templates, not create new ones
    if not tenant_id:
        logger.warning(f"Template creation denied - no tenant mapping for clerk_org_id={clerk_org_id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Organization must be configured with tenant mapping to create templates. Please contact your administrator."
        )
    
    # Convert tenant_id to UUID
    tenant_uuid = UUID(str(tenant_id)) 
    
    user_id_for_audit = UUID(current_user.user_id) if current_user.user_id else None # Ensure user_id is UUID
    
    # Validate unified templates before creation
    if template_in.category == AdminTemplateCategory.UNIFIED:
        # Convert the template to unified format for validation
        unified_template = {
            "meta": {
                "key": template_in.key,
                "endpoint_id": template_in.parameters.get("endpoint_id") if template_in.parameters else None,
                "category": "unified"
            },
            "intent": template_in.parameters.get("intent_config") if template_in.parameters else {},
            "action": template_in.parameters.get("action_config") if template_in.parameters else {},
            "response": template_in.response_format if template_in.response_format else {}
        }
        
        # Initialize validator with credential manager
        credential_manager = CredentialManager(db)
        validator = UnifiedTemplateValidator(credential_manager=credential_manager)
        
        # Validate the template
        validation_result = validator.validate_completeness(unified_template)
        
        if not validation_result.is_valid:
            logger.warning(f"Unified template validation failed: {validation_result.errors}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Template validation failed",
                    "errors": validation_result.errors,
                    "warnings": validation_result.warnings
                }
            )
        
        # Log warnings if any
        if validation_result.warnings:
            logger.info(f"Template validation warnings: {validation_result.warnings}")
    
    db_template = await crud_admin_template.create_with_tenant(db, obj_in=template_in, tenant_id=tenant_uuid, user_id=user_id_for_audit)
    
    # Add vectorization in background task if it's an intent router template
    if template_in.category == AdminTemplateCategory.INTENT_ROUTER:
        logger = logging.getLogger(__name__)
        logger.info(f"Adding vectorization task for new template {db_template.id} with key {db_template.key}")
        
        # Initialize vector indexer
        vector_indexer = VectorIndexer()
        
        # Determine index name based on tenant
        index_name = f"intent_idx_{tenant_uuid}_user"
        
        # Add background task to vectorize the template
        background_tasks.add_task(
            vector_indexer.upsert_template,
            db=db,
            template_id=str(db_template.id),
            template_key=db_template.key,
            template_category=db_template.category,
            template_body=db_template.body,
            template_description=db_template.description,
            index_name=index_name
        )
    
    return _construct_admin_template_response(db_template)

@router.get(
    "/{template_id}", 
    response_model=AdminTemplate, # Default to not showing all versions
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_READ))]
)
async def get_template(
    request: Request,
    template_id: UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details),
    include_global: bool = True
) -> AdminTemplate:
    """
    Get a specific template by ID.
    
    By default, this will allow access to global templates. Set include_global=false to only access tenant-specific templates.
    """
    tenant_id = getattr(request.state, 'tenant_id', None)
    clerk_org_id = getattr(request.state, 'clerk_org_id', None)
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    if not clerk_org_id and not is_system_admin:
        # Non-system admins must have a Clerk organization context
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Organization context required to access templates."
        )

    # The CRUD method loads versions by default
    if tenant_id:
        db_template = await crud_admin_template.get_by_id_and_tenant(
            db, id=template_id, tenant_id=UUID(str(tenant_id)), include_global=include_global
        )
    elif clerk_org_id or is_system_admin:
        # For clerk org without tenant mapping or system admin, fetch template directly
        from sqlalchemy.future import select
        from src.coherence.models.template import Template as TemplateModel
        
        query = select(TemplateModel).where(TemplateModel.id == template_id)
        
        # If not system admin and include_global is False, only allow global templates
        if not is_system_admin and not include_global:
            query = query.where(TemplateModel.tenant_id.is_(None))
        
        result = await db.execute(query)
        db_template = result.scalar_one_or_none()
    else:
        logging.getLogger(__name__).error("No tenant_id or clerk_org_id available, cannot fetch template")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No organization context available",
        )
    
    if not db_template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Template not found or you don't have access to it"
        )
        
    return _construct_admin_template_response(db_template)

@router.put(
    "/{template_id}", 
    response_model=AdminTemplate,
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_UPDATE))]
)
async def update_template(
    request: Request,
    template_id: UUID,
    template_in: AdminTemplateUpdate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details)
) -> AdminTemplate:
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant context required.")

    user_id_for_audit = UUID(current_user.user_id) if current_user.user_id else None
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    # If updating a unified template, validate the new structure
    existing_template = await crud_admin_template.get_by_id_and_tenant(
        db, id=template_id, tenant_id=UUID(str(tenant_id))
    )
    
    if existing_template and existing_template.category == AdminTemplateCategory.UNIFIED.value:
        # Merge existing template data with updates
        updated_params = existing_template.parameters or {}
        if template_in.parameters:
            updated_params.update(template_in.parameters)
        
        updated_response_format = existing_template.response_format or {}
        if template_in.response_format:
            updated_response_format.update(template_in.response_format)
        
        # Build unified template structure for validation
        unified_template = {
            "meta": {
                "key": template_in.key or existing_template.key,
                "endpoint_id": updated_params.get("endpoint_id"),
                "category": "unified"
            },
            "intent": updated_params.get("intent_config", {}),
            "action": updated_params.get("action_config", {}),
            "response": updated_response_format
        }
        
        # Initialize validator with credential manager
        credential_manager = CredentialManager(db)
        validator = UnifiedTemplateValidator(credential_manager=credential_manager)
        
        # Validate the template
        validation_result = validator.validate_completeness(unified_template)
        
        if not validation_result.is_valid:
            logger.warning(f"Unified template validation failed: {validation_result.errors}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Template validation failed",
                    "errors": validation_result.errors,
                    "warnings": validation_result.warnings
                }
            )
        
        # Log warnings if any
        if validation_result.warnings:
            logger.info(f"Template validation warnings: {validation_result.warnings}")
    
    updated_db_template = await crud_admin_template.update_template(
        db, template_id=template_id, obj_in=template_in, 
        tenant_id=UUID(str(tenant_id)), user_id=user_id_for_audit,
        system_admin=is_system_admin
    )
    
    if not updated_db_template:
        if await crud_admin_template.get_by_id_and_tenant(db, id=template_id, tenant_id=UUID(str(tenant_id))):
            # Template exists but update was denied due to protection status
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, 
                detail="Template is protected and cannot be modified by non-system administrators"
            )
        else:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Template not found")
    
    # Add vectorization in background task if it's an intent router template
    if updated_db_template.category == AdminTemplateCategory.INTENT_ROUTER.value:
        logger = logging.getLogger(__name__)
        logger.info(f"Adding vectorization task for updated template {template_id} with key {updated_db_template.key}")
        
        # Initialize vector indexer
        vector_indexer = VectorIndexer()
        
        # Determine index name based on tenant
        tenant_uuid = UUID(str(tenant_id))
        index_name = f"intent_idx_{tenant_uuid}_user"
        
        # Add background task to vectorize the updated template
        background_tasks.add_task(
            vector_indexer.upsert_template,
            db=db,
            template_id=str(updated_db_template.id),
            template_key=updated_db_template.key,
            template_category=updated_db_template.category,
            template_body=updated_db_template.body,
            template_description=updated_db_template.description,
            index_name=index_name
        )
            
    return _construct_admin_template_response(updated_db_template)

@router.delete(
    "/{template_id}", 
    response_model=AdminTemplate, # Returns the deleted template
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_DELETE))]
)
async def delete_template(
    request: Request,
    template_id: UUID,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details)
) -> AdminTemplate:
    tenant_id = getattr(request.state, 'tenant_id', None)
    if not tenant_id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant context required.")
    
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    # First check if template exists to differentiate between "not found" and "protected"
    template = await crud_admin_template.get_by_id_and_tenant(db, id=template_id, tenant_id=UUID(str(tenant_id)))
    if not template:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Template not found")
    
    # If template is protected and user is not system admin, deny access
    if template.protected and not is_system_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Template is protected and cannot be deleted by non-system administrators"
        )
    
    # Save template category and key before deletion for vector cleanup
    template_category = template.category
    template_key = template.key
        
    deleted_db_template = await crud_admin_template.remove_by_id_and_tenant(
        db, id=template_id, tenant_id=UUID(str(tenant_id)), system_admin=is_system_admin
    )
    
    if not deleted_db_template:
        # This should not happen since we already checked, but just in case
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete template")
    
    # Remove corresponding vector entry if it's an intent router template
    if template_category == AdminTemplateCategory.INTENT_ROUTER.value:
        logger = logging.getLogger(__name__)
        logger.info(f"Adding vector deletion task for template {template_id} with key {template_key}")
        
        # Initialize vector indexer
        vector_indexer = VectorIndexer()
        
        # Determine index name based on tenant
        tenant_uuid = UUID(str(tenant_id))
        index_name = f"intent_idx_{tenant_uuid}_user"
        
        # Add background task to delete the template vector
        background_tasks.add_task(
            vector_indexer.delete_template,
            template_id=str(template_id),
            index_name=index_name
        )
        
    return _construct_admin_template_response(deleted_db_template)

@router.get(
    "/{template_id}/versions", 
    response_model=List[AdminTemplateVersion], # Directly use AdminTemplateVersion schema list
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_READ_VERSIONS))]
)
async def list_template_versions(
    request: Request,
    template_id: UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details)
) -> List[AdminTemplateVersion]:
    logger = logging.getLogger(__name__)
    logger.info(f"Fetching versions for template {template_id}")
    
    tenant_id = getattr(request.state, 'tenant_id', None)
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    if not tenant_id and not is_system_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant context required.")

    if not tenant_id and is_system_admin:
        # System admin without tenant context - use a specific handling strategy
        from sqlalchemy.future import select

        from src.coherence.models.tenant import Tenant
        
        result = await db.execute(select(Tenant).limit(1))
        first_tenant = result.scalar_one_or_none()
        
        if not first_tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No tenants found in the system."
            )
        
        tenant_id = first_tenant.id
        logger.info(
            f"System admin accessing template versions for {template_id} without tenant context. Using first tenant {tenant_id} as context."
        )

    try:
        if not tenant_id:
            logger.error("No tenant_id available, cannot fetch template versions")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No tenant context available",
            )
            
        # Convert tenant_id to UUID
        tenant_uuid = UUID(str(tenant_id))
        
        # First verify we can access the template itself
        template = await crud_admin_template.get_by_id_and_tenant(
            db, id=template_id, tenant_id=tenant_uuid, include_global=True
        )
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, 
                detail=f"Template {template_id} not found or you don't have access to it"
            )
            
        # The CRUD method ensures template belongs to tenant before fetching versions
        db_versions = await crud_admin_template.get_versions_by_template_and_tenant(
            db, template_id=template_id, tenant_id=tenant_uuid
        )
        
        # Apply validation to each version to prevent errors
        validated_versions = []
        for version in db_versions:
            try:
                validated_version = AdminTemplateVersion.model_validate(version)
                validated_versions.append(validated_version)
            except Exception as e:
                logger.error(f"Error validating template version {version.version}: {str(e)}")
                # Continue with other versions instead of failing the whole request
                continue
        
        logger.info(f"Successfully retrieved {len(validated_versions)} versions for template {template_id}")
        return validated_versions
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error fetching template versions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching template versions: {str(e)}",
        ) 