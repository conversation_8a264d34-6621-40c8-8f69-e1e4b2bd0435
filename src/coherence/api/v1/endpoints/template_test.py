"""
API endpoint for testing templates with mock data.

This module provides endpoints for testing templates using their test_data
configuration without making actual API calls.
"""

import logging
from typing import Any, Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    RequirePermission,
    get_clerk_auth_details,
)
from src.coherence.crud.crud_admin_template import crud_admin_template
from src.coherence.db.deps import get_db as get_db_session
from src.coherence.intent_pipeline.parameter_extraction import ParameterExtractor
from src.coherence.models.template import Template, TemplateCategory
from src.coherence.schemas.admin_template import (
    AdminTemplate,
    TemplateTestRequest,
    TemplateTestResponse,
)
from src.coherence.services.permission_service import CoherencePermission
from src.coherence.template_system.crfs_formatter import CRFSFormatter

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post(
    "/{template_id}/test",
    response_model=TemplateTestResponse,
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_READ))],
)
async def test_template(
    template_id: UUID,
    test_request: TemplateTestRequest,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details),
) -> TemplateTestResponse:
    """
    Test a template with sample data or custom parameters.
    
    This endpoint allows testing templates without making actual API calls by:
    1. Using mock responses from test_data if available
    2. Validating parameter extraction and transformation
    3. Testing response formatting with CRFS
    
    Args:
        template_id: ID of the template to test
        test_request: Test parameters and configuration
        
    Returns:
        Test results including mock response and formatting
    """
    tenant_id = getattr(request.state, "tenant_id", None)
    clerk_org_id = getattr(request.state, "clerk_org_id", None)
    
    logger.info(
        f"Testing template {template_id} for tenant_id={tenant_id}, clerk_org_id={clerk_org_id}"
    )
    
    # Fetch the template
    stmt = select(Template).where(Template.id == template_id)
    
    # Add tenant filter if not a global template request
    if tenant_id:
        stmt = stmt.where(
            (Template.tenant_id == UUID(str(tenant_id))) | 
            (Template.scope == "global")
        )
    
    result = await db.execute(stmt)
    template = result.scalar_one_or_none()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Template {template_id} not found",
        )
    
    # Check if template is unified type
    if template.category != TemplateCategory.UNIFIED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only unified templates can be tested through this endpoint",
        )
    
    # Get test data from template
    test_data = template.test_data or {}
    mock_responses = test_data.get("mock_responses", {})
    sample_parameters = test_data.get("sample_parameters", {})
    
    # Use provided parameters or fall back to sample parameters
    test_parameters = test_request.parameters or sample_parameters
    
    if not test_parameters and not sample_parameters:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No test parameters provided and template has no sample_parameters",
        )
    
    # Determine which scenario to use (default to 'success')
    scenario = test_request.scenario or "success"
    
    # Get mock response for scenario
    mock_response = mock_responses.get(scenario)
    
    if not mock_response:
        # If no mock response, create a default one
        mock_response = {
            "status": "success",
            "message": f"Test execution of template {template.key}",
            "data": test_parameters,
        }
    
    # Validate parameters against template schema
    validation_errors = []
    if template.action_config:
        validation_rules = template.action_config.get("validation_rules", {})
        for param_name, rules in validation_rules.items():
            if rules.get("required") and param_name not in test_parameters:
                validation_errors.append(f"Missing required parameter: {param_name}")
    
    # Apply transformations if defined
    transformed_parameters = test_parameters.copy()
    if template.action_config:
        transformations = template.action_config.get("transformations", {})
        for param_name, transforms in transformations.items():
            if param_name in transformed_parameters:
                value = transformed_parameters[param_name]
                for transform in transforms:
                    if transform == "trim" and isinstance(value, str):
                        value = value.strip()
                    elif transform == "lowercase" and isinstance(value, str):
                        value = value.lower()
                    elif transform == "uppercase" and isinstance(value, str):
                        value = value.upper()
                    elif transform == "remove_non_numeric" and isinstance(value, str):
                        value = "".join(filter(str.isdigit, value))
                transformed_parameters[param_name] = value
    
    # Format response using CRFS if available
    formatted_response = None
    if template.response_format and template.response_format.get("crfs"):
        try:
            formatter = CRFSFormatter()
            formatted_response = formatter.format_response(
                response_data=mock_response,
                crfs_config=template.response_format["crfs"],
                user_preference="structured",  # Default to structured
            )
        except Exception as e:
            logger.warning(f"Failed to format response with CRFS: {e}")
            formatted_response = None
    
    # Build response
    response = TemplateTestResponse(
        template_id=template_id,
        template_key=template.key,
        scenario=scenario,
        parameters_provided=test_parameters,
        parameters_transformed=transformed_parameters,
        validation_errors=validation_errors,
        mock_response=mock_response,
        formatted_response=formatted_response,
        test_data_available=bool(test_data),
        scenarios_available=list(mock_responses.keys()) if mock_responses else [],
    )
    
    logger.info(
        f"Template test completed: template={template.key}, scenario={scenario}, "
        f"validation_errors={len(validation_errors)}, formatted={formatted_response is not None}"
    )
    
    return response


@router.get(
    "/{template_id}/test-scenarios",
    response_model=Dict[str, Any],
    dependencies=[Depends(RequirePermission(CoherencePermission.TEMPLATE_READ))],
)
async def get_test_scenarios(
    template_id: UUID,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: ClerkAuthDetails = Depends(get_clerk_auth_details),
) -> Dict[str, Any]:
    """
    Get available test scenarios and sample parameters for a template.
    
    Returns:
        Dictionary containing available scenarios and sample parameters
    """
    tenant_id = getattr(request.state, "tenant_id", None)
    
    # Fetch the template
    stmt = select(Template).where(Template.id == template_id)
    
    if tenant_id:
        stmt = stmt.where(
            (Template.tenant_id == UUID(str(tenant_id))) | 
            (Template.scope == "global")
        )
    
    result = await db.execute(stmt)
    template = result.scalar_one_or_none()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Template {template_id} not found",
        )
    
    test_data = template.test_data or {}
    
    return {
        "template_id": str(template_id),
        "template_key": template.key,
        "test_data_available": bool(test_data),
        "scenarios": list(test_data.get("mock_responses", {}).keys()),
        "sample_parameters": test_data.get("sample_parameters", {}),
        "parameter_schema": template.parameters if template.parameters else {},
    }