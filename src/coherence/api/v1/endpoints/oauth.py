"""
OAuth endpoints for API integrations.

These endpoints handle OAuth flows and token management
for OpenAPI integrations.
"""

import logging
import uuid
from typing import Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.dependencies.auth import get_tenant_from_api_key
from src.coherence.db.deps import get_db
from src.coherence.models.tenant import Tenant
from src.coherence.openapi_adapter.oauth_manager import OAuthError, get_oauth_manager

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/initialize", response_model=Dict)
async def initialize_oauth_flow(
    integration_id: uuid.UUID,
    flow_type: str = "authorization_code",
    scopes: Optional[str] = None,
    tenant: Tenant = Depends(get_tenant_from_api_key),
    db: AsyncSession = Depends(get_db),
):
    """
    Initialize an OAuth flow for an API integration.

    This endpoint starts the OAuth process and returns
    the authorization URL for redirect-based flows.
    """
    try:
        # Parse scopes if provided
        scope_list = scopes.split() if scopes else None

        # Get OAuth manager
        oauth_manager = await get_oauth_manager(db)

        # Initialize the flow
        result = await oauth_manager.initialize_oauth_flow(
            integration_id=integration_id,
            flow_type=flow_type,
            scopes=scope_list,
        )

        return result

    except OAuthError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except Exception as e:
        logger.error(f"Failed to initialize OAuth flow: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initialize OAuth flow",
        ) from e


@router.get("/callback")
async def oauth_callback(
    request: Request,
    state: str = Query(...),
    code: Optional[str] = Query(None),
    error: Optional[str] = Query(None),
    error_description: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
):
    """
    Handle OAuth callback for authorization code flow.

    This endpoint processes the OAuth redirect after user authorization.
    """
    try:
        # Get OAuth manager
        oauth_manager = await get_oauth_manager(db)

        # Complete the authorization
        error_msg = error_description if error_description else error
        result = await oauth_manager.complete_authorization(
            state=state,
            code=code,
            error=error_msg,
        )

        # Could return a success page or redirect to the application
        return {
            "success": True,
            "message": "Authorization completed successfully",
            "integration_id": result.get("integration_id"),
        }

    except OAuthError as e:
        logger.error(f"OAuth callback error: {str(e)}")
        return {
            "success": False,
            "message": str(e),
        }
    except Exception as e:
        logger.error(f"OAuth callback error: {str(e)}")
        return {
            "success": False,
            "message": "Failed to complete authorization",
        }


@router.post("/token/refresh", response_model=Dict)
async def refresh_oauth_token(
    integration_id: uuid.UUID,
    tenant: Tenant = Depends(get_tenant_from_api_key),
    db: AsyncSession = Depends(get_db),
):
    """
    Manually refresh an OAuth token.

    This endpoint can be used to force a token refresh
    before it expires.
    """
    try:
        # Get OAuth manager
        oauth_manager = await get_oauth_manager(db)

        # Fetch auth config
        result = await db.execute(
            "SELECT * FROM api_auth_configs WHERE integration_id = :integration_id",
            {"integration_id": integration_id},
        )
        auth_config = result.fetchone()

        if not auth_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Auth configuration not found",
            )

        # Extract token data
        credentials = auth_config.credentials or {}
        token_data = credentials.get("token", {})

        if not token_data or "refresh_token" not in token_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No refresh token available",
            )

        # Refresh the token
        new_token = await oauth_manager._refresh_token(
            integration_id=integration_id,
            auth_config=auth_config,
            token_data=token_data,
        )

        return {
            "success": True,
            "token_type": new_token.get("token_type", "Bearer"),
            "expires_in": new_token.get("expires_in"),
            "scope": new_token.get("scope"),
        }

    except OAuthError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to refresh token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh token",
        ) from e


@router.get("/config/{integration_id}", response_model=Dict)
async def get_oauth_config(
    integration_id: uuid.UUID,
    tenant: Tenant = Depends(get_tenant_from_api_key),
    db: AsyncSession = Depends(get_db),
):
    """
    Get OAuth configuration for an integration.

    This endpoint returns the OAuth configuration without
    sensitive credentials.
    """
    try:
        # Fetch auth config
        result = await db.execute(
            """
            SELECT ac.auth_type, ac.scopes, ac.expires_at,
                   CASE WHEN ac.expires_at IS NOT NULL 
                        THEN ac.expires_at > NOW()
                        ELSE FALSE
                   END as token_valid
            FROM api_auth_configs ac
            JOIN api_integrations ai ON ac.integration_id = ai.id
            WHERE ac.integration_id = :integration_id
            AND ai.tenant_id = :tenant_id
            """,
            {
                "integration_id": integration_id,
                "tenant_id": tenant.id,
            },
        )

        auth_config = result.fetchone()
        if not auth_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Auth configuration not found",
            )

        # Format response
        return {
            "integration_id": str(integration_id),
            "auth_type": auth_config.auth_type,
            "scopes": auth_config.scopes,
            "token_valid": auth_config.token_valid,
            "expires_at": auth_config.expires_at.isoformat()
            if auth_config.expires_at
            else None,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get OAuth config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get OAuth configuration",
        ) from e
