"""
API Endpoints for managing Organization API Keys.
These are typically used by an admin interface or system-to-system calls.
"""
import hashlib
import secrets
import uuid
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Path, Request, status
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.coherence.api.v1.dependencies.auth import (
    check_is_system_admin,  # Ensures only system admin (via master key) can operate
    get_org_api_key_principal,  # Used for master key auth for these admin endpoints
)
from src.coherence.db.deps import get_db
from src.coherence.models.tenant import (
    OrganizationAPIKey,  # The new model
    Tenant,  # To look up tenant by clerk_org_id
)
from src.coherence.schemas.tenant import (  # Schemas created earlier
    OrganizationAPIKeyCreateRequest,
    OrganizationAPIKeyCreateResponse,
    OrganizationAPIKeyRead,  # Added for completeness, though not in initial plan list
)

router = APIRouter(
    dependencies=[
        Depends(get_org_api_key_principal), # Authenticates using X-API-Key (master key)
        Depends(check_is_system_admin)      # Authorizes only system admins
    ]
)

# Note: The placeholder_auth_check function is now removed.
# All routes below will be protected by get_org_api_key_principal and check_is_system_admin.

@router.get(
    "/{clerk_org_id}/api-keys",
    response_model=List[OrganizationAPIKeyRead],
    summary="List API Keys for an Organization"
)
async def list_organization_api_keys(
    clerk_org_id: str = Path(..., description="The Clerk Organization ID"),
    db: AsyncSession = Depends(get_db)
    # Auth is handled by router dependencies
):
    """
    List all API keys for a given Clerk Organization ID.
    Accessible only by system admins (via master API key).
    """
    # Authorization (system admin) is handled by router-level dependencies.
    
    # Verify the tenant/organization exists by clerk_org_id
    tenant_result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id))
    tenant = tenant_result.scalar_one_or_none()
    if not tenant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found")

    result = await db.execute(
        select(OrganizationAPIKey)
        .where(OrganizationAPIKey.clerk_org_id == clerk_org_id)
        # .where(OrganizationAPIKey.revoked == False) # Optionally filter out revoked keys by default
        .order_by(OrganizationAPIKey.created_at.desc())
    )
    api_keys = result.scalars().all()
    return api_keys


@router.post(
    "/{clerk_org_id}/api-keys",
    response_model=OrganizationAPIKeyCreateResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new API Key for an Organization"
)
async def create_organization_api_key(
    key_create_request: OrganizationAPIKeyCreateRequest,
    request: Request, # Added to access request.state if needed for created_by
    clerk_org_id: str = Path(..., description="The Clerk Organization ID"),
    db: AsyncSession = Depends(get_db)
    # Auth is handled by router dependencies
):
    """
    Create a new API key for the specified Clerk Organization ID.
    The full API key is returned only once upon creation.
    Accessible only by system admins (via master API key).
    """
    # Authorization (system admin) is handled by router-level dependencies.
    # created_by should reflect system action or passed if BFF provides original user
    # For now, assume system action if master key is used.
    created_by_user_id = getattr(request.state, "clerk_user_id", "system_master_key_user") # Fallback if not set by specific user context

    # Verify the tenant/organization exists
    tenant_result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == clerk_org_id))
    tenant = tenant_result.scalar_one_or_none()
    if not tenant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found")

    api_key_value = f"coh_org_{secrets.token_urlsafe(32)}"
    key_hash = hashlib.sha256(api_key_value.encode()).hexdigest()
    key_prefix = api_key_value[:8] # Store the first 8 characters as prefix

    new_api_key = OrganizationAPIKey(
        clerk_org_id=clerk_org_id,
        name=key_create_request.name,
        permissions=key_create_request.permissions,
        expires_at=key_create_request.expires_at,
        key_hash=key_hash,
        key_prefix=key_prefix,
        created_by=created_by_user_id,
        revoked=False
    )

    db.add(new_api_key)
    try:
        await db.commit()
        await db.refresh(new_api_key)
    except IntegrityError: # pragma: no cover
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="API key could not be created. Potential hash collision or other constraint violation.",
        )
    
    return OrganizationAPIKeyCreateResponse(
        **new_api_key.__dict__, # type: ignore
        key=api_key_value # Return the full key value
    )


@router.delete(
    "/{clerk_org_id}/api-keys/{api_key_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Revoke an API Key for an Organization"
)
async def revoke_organization_api_key(
    clerk_org_id: str = Path(..., description="The Clerk Organization ID"),
    api_key_id: uuid.UUID = Path(..., description="The ID of the API key to revoke"),
    db: AsyncSession = Depends(get_db)
    # Auth is handled by router dependencies
):
    """
    Revoke (soft delete) an API key for the specified Clerk Organization ID.
    Accessible only by system admins (via master API key).
    """
    # Authorization (system admin) is handled by router-level dependencies.

    result = await db.execute(
        select(OrganizationAPIKey).where(
            OrganizationAPIKey.id == api_key_id,
            OrganizationAPIKey.clerk_org_id == clerk_org_id
        )
    )
    api_key = result.scalar_one_or_none()

    if not api_key:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="API Key not found or does not belong to this organization")

    if api_key.revoked:
        # Optionally, could return 200 OK with a message, or just 204 as it's idempotent in effect
        return # Already revoked

    api_key.revoked = True
    try:
        await db.commit()
        await db.refresh(api_key)
    except Exception: # pragma: no cover
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke API key."
        )
    return

# TODO: Add PUT endpoint for updating API key (e.g., name, permissions, expires_at)
# @router.put("/{clerk_org_id}/api-keys/{api_key_id}", response_model=OrganizationAPIKeyRead) ...