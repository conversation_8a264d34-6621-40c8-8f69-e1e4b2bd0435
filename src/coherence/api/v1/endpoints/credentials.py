"""
Credential management endpoints for API integrations.

These endpoints handle secure storage and management
of API credentials for OpenAPI integrations.
"""

import logging
import uuid
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel, Field
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    RequirePermission,
    get_clerk_auth_details,
)
from src.coherence.db.deps import get_db
from src.coherence.models.integration import AuthType
from src.coherence.openapi_adapter.credential_manager import (
    CredentialError,
    get_credential_manager,
)
from src.coherence.services.permission_service import CoherencePermission

logger = logging.getLogger(__name__)

router = APIRouter()


class ApiKeyCredentials(BaseModel):
    """Model for API key credentials."""

    key: str = Field(..., description="API key")
    header_name: str = Field(
        "X-API-Key", description="Name of the header for the API key"
    )
    parameter_name: Optional[str] = Field(
        None, description="Name of the query parameter for the API key"
    )
    in_header: bool = Field(True, description="Whether to send the key in the header")
    in_query: bool = Field(
        False, description="Whether to send the key in the query parameters"
    )


class BasicAuthCredentials(BaseModel):
    """Model for Basic authentication credentials."""

    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")


class BearerCredentials(BaseModel):
    """Model for Bearer authentication credentials."""

    token: str = Field(..., description="Bearer token")


class OAuth2Credentials(BaseModel):
    """Model for OAuth2 credentials."""

    client_id: str = Field(..., description="OAuth client ID")
    client_secret: str = Field(..., description="OAuth client secret")
    token_url: str = Field(..., description="Token endpoint URL")
    authorization_url: Optional[str] = Field(
        None, description="Authorization endpoint URL"
    )
    redirect_uri: Optional[str] = Field(
        None, description="Redirect URI for authorization code flow"
    )
    scopes: Optional[list] = Field(None, description="List of scopes to request")
    auth_params: Optional[Dict] = Field(
        None, description="Additional authorization parameters"
    )


class CredentialInfo(BaseModel):
    """Model for credential information."""
    integration_id: str
    auth_type: str
    has_credentials: bool
    created_at: Optional[str] = None


@router.put(
    "/{integration_id}/api-key",
    status_code=status.HTTP_200_OK,
)
async def store_api_key(
    request: Request,
    integration_id: uuid.UUID,
    credentials: ApiKeyCredentials,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.INTEGRATION_MANAGE_CREDENTIALS)),
):
    """
    Store API key credentials for an integration.
    """
    try:
        # Get tenant context
        tenant_id = getattr(request.state, 'tenant_id', None)
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tenant context required"
            )

        # Verify that the integration belongs to this tenant
        result = await db.execute(
            text("SELECT id FROM api_integrations WHERE id = :id AND tenant_id = :tenant_id"),
            {"id": integration_id, "tenant_id": tenant_id},
        )
        integration = result.fetchone()

        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found",
            )

        # Get credential manager
        credential_manager = await get_credential_manager(db)

        # Store the credentials
        await credential_manager.store_credentials(
            integration_id=integration_id,
            auth_type=AuthType.API_KEY,
            credentials=credentials.model_dump(),
        )

        return {"success": True, "message": "API key credentials stored successfully"}

    except CredentialError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except Exception as e:
        logger.error(f"Failed to store API key credentials: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to store credentials",
        ) from e


@router.put(
    "/{integration_id}/basic-auth",
    status_code=status.HTTP_200_OK,
)
async def store_basic_auth(
    request: Request,
    integration_id: uuid.UUID,
    credentials: BasicAuthCredentials,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.INTEGRATION_MANAGE_CREDENTIALS)),
):
    """
    Store Basic authentication credentials for an integration.
    """
    try:
        # Get tenant context
        tenant_id = getattr(request.state, 'tenant_id', None)
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tenant context required"
            )

        # Verify that the integration belongs to this tenant
        result = await db.execute(
            text("SELECT id FROM api_integrations WHERE id = :id AND tenant_id = :tenant_id"),
            {"id": integration_id, "tenant_id": tenant_id},
        )
        integration = result.fetchone()

        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found",
            )

        # Get credential manager
        credential_manager = await get_credential_manager(db)

        # Store the credentials
        await credential_manager.store_credentials(
            integration_id=integration_id,
            auth_type=AuthType.BASIC,
            credentials=credentials.model_dump(),
        )

        return {
            "success": True,
            "message": "Basic auth credentials stored successfully",
        }

    except CredentialError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except Exception as e:
        logger.error(f"Failed to store Basic auth credentials: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to store credentials",
        ) from e


@router.put(
    "/{integration_id}/bearer",
    status_code=status.HTTP_200_OK,
)
async def store_bearer_token(
    request: Request,
    integration_id: uuid.UUID,
    credentials: BearerCredentials,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.INTEGRATION_MANAGE_CREDENTIALS)),
):
    """
    Store Bearer authentication credentials for an integration.
    """
    try:
        # Get tenant context
        tenant_id = getattr(request.state, 'tenant_id', None)
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tenant context required"
            )

        # Verify that the integration belongs to this tenant
        result = await db.execute(
            text("SELECT id FROM api_integrations WHERE id = :id AND tenant_id = :tenant_id"),
            {"id": integration_id, "tenant_id": tenant_id},
        )
        integration = result.fetchone()

        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found",
            )

        # Get credential manager
        credential_manager = await get_credential_manager(db)

        # Store the credentials
        await credential_manager.store_credentials(
            integration_id=integration_id,
            auth_type=AuthType.BEARER,
            credentials=credentials.model_dump(),
        )

        return {"success": True, "message": "Bearer token stored successfully"}

    except CredentialError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except Exception as e:
        logger.error(f"Failed to store Bearer token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to store credentials",
        ) from e


@router.put(
    "/{integration_id}/oauth2",
    status_code=status.HTTP_200_OK,
)
async def store_oauth2_credentials(
    request: Request,
    integration_id: uuid.UUID,
    credentials: OAuth2Credentials,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.INTEGRATION_MANAGE_CREDENTIALS)),
):
    """
    Store OAuth2 credentials for an integration.
    """
    try:
        # Get tenant context
        tenant_id = getattr(request.state, 'tenant_id', None)
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tenant context required"
            )

        # Verify that the integration belongs to this tenant
        result = await db.execute(
            text("SELECT id FROM api_integrations WHERE id = :id AND tenant_id = :tenant_id"),
            {"id": integration_id, "tenant_id": tenant_id},
        )
        integration = result.fetchone()

        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found",
            )

        # Get credential manager
        credential_manager = await get_credential_manager(db)

        # Store the credentials
        await credential_manager.store_credentials(
            integration_id=integration_id,
            auth_type=AuthType.OAUTH2,
            credentials=credentials.model_dump(),
        )

        # Update scopes in auth_config
        if credentials.scopes:
            await db.execute(
                text("""
                UPDATE api_auth_configs
                SET scopes = :scopes
                WHERE integration_id = :integration_id
                """),
                {
                    "scopes": credentials.scopes,
                    "integration_id": integration_id,
                },
            )

            await db.commit()

        return {"success": True, "message": "OAuth2 credentials stored successfully"}

    except CredentialError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except Exception as e:
        logger.error(f"Failed to store OAuth2 credentials: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to store credentials",
        ) from e


@router.delete(
    "/{integration_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_credentials(
    request: Request,
    integration_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.INTEGRATION_MANAGE_CREDENTIALS)),
):
    """
    Delete credentials for an integration.
    """
    try:
        # Get tenant context
        tenant_id = getattr(request.state, 'tenant_id', None)
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tenant context required"
            )

        # Verify that the integration belongs to this tenant
        result = await db.execute(
            text("SELECT id FROM api_integrations WHERE id = :id AND tenant_id = :tenant_id"),
            {"id": integration_id, "tenant_id": tenant_id},
        )
        integration = result.fetchone()

        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found",
            )

        # Get credential manager
        credential_manager = await get_credential_manager(db)

        # Delete the credentials
        await credential_manager.delete_credentials(integration_id=integration_id)

        return None  # 204 No Content

    except CredentialError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except Exception as e:
        logger.error(f"Failed to delete credentials: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete credentials",
        ) from e


@router.get(
    "/{integration_id}",
    response_model=List[CredentialInfo],
)
async def get_credentials(
    request: Request,
    integration_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.INTEGRATION_READ)),
):
    """
    Get credential information for an integration.
    Returns a list of credential configurations without sensitive data.
    """
    try:
        # Get tenant_id from request state and check if user is system admin
        tenant_id = getattr(request.state, "tenant_id", None)
        is_system_admin = getattr(request.state, "is_system_admin", False)

        # Verify that the integration exists
        result = await db.execute(
            text("SELECT id, tenant_id FROM api_integrations WHERE id = :id"),
            {"id": integration_id},
        )
        integration = result.fetchone()

        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found",
            )

        # Verify permission - system admins can view any integration, others must own it
        if not is_system_admin:
            if not tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED, 
                    detail="Tenant context not found"
                )
                
            if integration.tenant_id != tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Integration not found or not owned by tenant",
                )
        
        # For system admins without a tenant context, log the access
        if is_system_admin and not tenant_id:
            logger.info(f"System admin accessing credential info for integration ID {integration_id} without tenant context")

        # Get credential manager
        await get_credential_manager(db)

        # Get credential info - just metadata, not the actual credentials
        result = await db.execute(
            text("SELECT auth_type FROM api_auth_configs WHERE integration_id = :integration_id"),
            {"integration_id": integration_id},
        )
        
        auth_config = result.fetchone()
        
        if not auth_config:
            # Return empty info if no credentials configured
            return []
            
        # Format the response
        return [
            CredentialInfo(
                integration_id=str(integration_id),
                auth_type=auth_config.auth_type,
                has_credentials=True,
                created_at=None,  # Remove created_at reference since column doesn't exist
            )
        ]

    except Exception as e:
        logger.error(f"Failed to get credential info: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve credential information",
        ) from e
