from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    get_clerk_auth_details,
)
from src.coherence.db.deps import get_db
from src.coherence.models.tenant import Tenant
from src.coherence.schemas.me import OrgDetails, UserSession
from src.coherence.services.permission_service import PermissionService

router = APIRouter()
permission_service = PermissionService()


@router.get("/me", response_model=UserSession)
async def read_current_user_session(
    clerk_auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """
    Provides an authoritative session blob for the authenticated user,
    including organization details from JWT, tenant details from DB,
    and Coherence permissions.
    """
    # Create a fallback organization for system admins if no org is available from Clerk
    org_id = clerk_auth_details.org_id
    org_name = clerk_auth_details.org_name
    org_role = clerk_auth_details.org_role
    org_slug = clerk_auth_details.org_slug

    # Provide system admin fallback organization details
    if clerk_auth_details.is_system_admin and not org_id:
        org_id = "org_fallback_for_system_admin"
        org_name = "System Admin Organization"
        org_role = "admin"

    org_details = OrgDetails(
        id=org_id,
        name=org_name,
        slug=org_slug,
        role=org_role,
        public_metadata=clerk_auth_details.org_metadata or {}
    )

    tenant_obj: Optional[Tenant] = None
    if clerk_auth_details.org_id:
        tenant_result = await db.execute(
            select(Tenant).where(Tenant.clerk_org_id == clerk_auth_details.org_id)
        )
        tenant_obj = tenant_result.scalar_one_or_none()

        if not tenant_obj and not clerk_auth_details.is_system_admin:
            # Non-system admins must have a provisioned tenant if an org_id is present
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not provisioned.",
            )
    
    # If it's a system admin and tenant_obj is None (e.g. system admin not part of an org, or org not in DB)
    # tenant part of response will be None, which is acceptable.

    permissions = permission_service.get_coherence_permissions(
        clerk_org_role=clerk_auth_details.org_role,
        is_system_admin=clerk_auth_details.is_system_admin,
    )

    return UserSession(
        org=org_details,
        tenant=tenant_obj, # Pydantic will convert Tenant model to TenantRead schema
        permissions=permissions,
    )