"""
Unified Template Management API.

Consolidates all template operations into a single endpoint with role-based permissions.
Supports both legacy template formats and new unified templates.
"""

import logging
import hashlib
from typing import Any, Dict, List, Optional
from uuid import UUID
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_

from src.coherence.api.v1.dependencies.auth import get_clerk_auth_details, RequirePermission
from src.coherence.db.deps import get_db
from src.coherence.models.template import Template, TemplateCategory, TemplateScope
from src.coherence.schemas.response import SuccessResponse
from src.coherence.template_system import (
    TemplateService, 
    UnifiedTemplateGenerator, 
    UIFormGenerator,
    CRFSFormatter
)
from src.coherence.core.llm.factory import LLMFactory
from src.coherence.services.permission_service import CoherencePermission

logger = logging.getLogger(__name__)

router = APIRouter()


# Pydantic schemas for unified templates
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List


class UnifiedTemplateCreate(BaseModel):
    """Schema for creating unified templates."""
    key: str = Field(..., description="Template key")
    meta: Dict[str, Any] = Field(..., description="Template metadata")
    intent: Dict[str, Any] = Field(..., description="Intent configuration")
    action: Dict[str, Any] = Field(..., description="Action configuration")
    parameters: Dict[str, Any] = Field(..., description="Parameters schema and UI fields")
    prompts: Dict[str, Any] = Field(..., description="Prompt configurations")
    response: Dict[str, Any] = Field(..., description="Response formatting configuration")
    docs_config: Dict[str, Any] = Field(..., description="Documentation configuration")
    description: Optional[str] = Field(None, description="Template description")
    protected: bool = Field(False, description="Whether template is protected")


class UnifiedTemplateUpdate(BaseModel):
    """Schema for updating unified templates."""
    meta: Optional[Dict[str, Any]] = None
    intent: Optional[Dict[str, Any]] = None
    action: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    prompts: Optional[Dict[str, Any]] = None
    response: Optional[Dict[str, Any]] = None
    docs_config: Optional[Dict[str, Any]] = None
    description: Optional[str] = None


class UnifiedTemplateResponse(BaseModel):
    """Schema for unified template responses."""
    id: str
    key: str
    category: str
    scope: str
    version: int
    meta: Dict[str, Any]
    intent: Dict[str, Any]
    action: Dict[str, Any]
    parameters: Dict[str, Any]
    prompts: Dict[str, Any]
    response: Dict[str, Any]
    docs_config: Dict[str, Any]
    description: Optional[str]
    protected: bool
    created_at: str
    updated_at: str


class TemplateGenerateRequest(BaseModel):
    """Schema for generating templates from OpenAPI."""
    operation: Dict[str, Any] = Field(..., description="OpenAPI operation object")
    path: str = Field(..., description="API path")
    method: str = Field(..., description="HTTP method")
    base_url: Optional[str] = Field("", description="Base URL")
    spec_info: Optional[Dict[str, Any]] = Field(None, description="OpenAPI spec info")


@router.get("", response_model=SuccessResponse[List[UnifiedTemplateResponse]])
async def list_templates(
    request: Request,
    db: AsyncSession = Depends(get_db),
    category: Optional[TemplateCategory] = Query(None, description="Filter by category"),
    scope: Optional[TemplateScope] = Query(None, description="Filter by scope"),
    search: Optional[str] = Query(None, description="Search in template keys and descriptions"),
    auth_details=Depends(get_clerk_auth_details),
    _perm_check=Depends(RequirePermission(CoherencePermission.TEMPLATE_READ))
):
    """List templates with filtering and search."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    try:
        service = TemplateService()
        
        # Build filter conditions
        filters = []
        if category:
            filters.append(Template.category == category)
        if scope:
            filters.append(Template.scope == scope)
        if search:
            filters.append(or_(
                Template.key.ilike(f"%{search}%"),
                Template.description.ilike(f"%{search}%")
            ))
        
        # Add tenant filtering for non-system admins
        if not is_system_admin and tenant_id:
            filters.append(or_(
                Template.tenant_id == tenant_id,
                Template.scope == TemplateScope.GLOBAL
            ))
        
        templates = await service.list_templates(db, filters=filters)
        
        # Convert to response format
        response_data = []
        for template in templates:
            response_data.append(UnifiedTemplateResponse(
                id=str(template.id),
                key=template.key,
                category=template.category,
                scope=template.scope,
                version=template.version,
                meta=template.meta or {"key": template.key, "version": template.version},
                intent=template.intent_config or {},
                action=template.action_config or {},
                parameters={
                    "schema": template.parameters or {},
                    "ui_fields": template.ui_fields or {}
                },
                prompts=template.prompts or {},
                response={
                    "crfs": template.response_format or {},
                    "error_mapping": {}
                },
                docs_config=template.docs_config or {},
                description=template.description,
                protected=template.protected,
                created_at=template.created_at.isoformat(),
                updated_at=template.updated_at.isoformat()
            ))
        
        return SuccessResponse(data=response_data)
        
    except Exception as e:
        logger.error(f"Error listing templates: {e}")
        raise HTTPException(status_code=500, detail="Failed to list templates")


@router.post("", response_model=SuccessResponse[UnifiedTemplateResponse])
async def create_unified_template(
    request: Request,
    template_data: UnifiedTemplateCreate,
    db: AsyncSession = Depends(get_db),
    auth_details=Depends(get_clerk_auth_details),
    _perm_check=Depends(RequirePermission(CoherencePermission.TEMPLATE_CREATE))
):
    """Create a new unified template."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    try:
        service = TemplateService()
        
        # Validate unified template structure
        generator = UnifiedTemplateGenerator()
        errors = generator.validate_unified_template({
            "meta": template_data.meta,
            "intent": template_data.intent,
            "action": template_data.action,
            "parameters": template_data.parameters,
            "prompts": template_data.prompts,
            "response": template_data.response,
            "docs_config": template_data.docs_config
        })
        
        if errors:
            raise HTTPException(status_code=400, detail=f"Template validation failed: {', '.join(errors)}")
        
        # Determine scope and tenant
        scope = TemplateScope.GLOBAL if is_system_admin else TemplateScope.TENANT
        template_tenant_id = None if is_system_admin else tenant_id
        
        # Create template
        template = await service.create_template(
            db=db,
            key=template_data.key,
            category=TemplateCategory.UNIFIED,
            scope=scope,
            tenant_id=template_tenant_id,
            body="",  # Unified templates don't use body field
            description=template_data.description,
            protected=template_data.protected and is_system_admin,  # Only system admins can create protected templates
            # Unified template fields
            endpoint_id=template_data.meta.get("endpoint_id"),
            intent_config=template_data.intent,
            action_config=template_data.action,
            ui_fields=template_data.parameters.get("ui_fields", {}),
            prompts=template_data.prompts,
            docs_config=template_data.docs_config,
            parameters=template_data.parameters.get("schema", {}),
            response_format=template_data.response.get("crfs", {}),
            created_by=auth_details.user_id
        )
        
        # Convert to response format
        response_data = UnifiedTemplateResponse(
            id=str(template.id),
            key=template.key,
            category=template.category,
            scope=template.scope,
            version=template.version,
            meta=template_data.meta,
            intent=template_data.intent,
            action=template_data.action,
            parameters=template_data.parameters,
            prompts=template_data.prompts,
            response=template_data.response,
            docs_config=template_data.docs_config,
            description=template.description,
            protected=template.protected,
            created_at=template.created_at.isoformat(),
            updated_at=template.updated_at.isoformat()
        )
        
        return SuccessResponse(data=response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating template: {e}")
        raise HTTPException(status_code=500, detail="Failed to create template")


@router.post("/generate", response_model=SuccessResponse[UnifiedTemplateResponse])
async def generate_template_from_openapi(
    request: Request,
    generate_request: TemplateGenerateRequest,
    db: AsyncSession = Depends(get_db),
    auth_details=Depends(get_clerk_auth_details),
    _perm_check=Depends(RequirePermission(CoherencePermission.TEMPLATE_CREATE))
):
    """Generate a unified template from OpenAPI specification."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    try:
        # Initialize generator with LLM factory for enhanced generation
        llm_factory = LLMFactory()
        generator = UnifiedTemplateGenerator(llm_factory)
        
        # Generate unified template
        template_data = generator.generate_from_openapi_endpoint(
            operation=generate_request.operation,
            path=generate_request.path,
            method=generate_request.method,
            base_url=generate_request.base_url or "",
            spec_info=generate_request.spec_info
        )
        
        # Create template in database
        service = TemplateService()
        scope = TemplateScope.GLOBAL if is_system_admin else TemplateScope.TENANT
        template_tenant_id = None if is_system_admin else tenant_id
        
        # Create template using the base create_template method
        # Convert Clerk user ID to a deterministic UUID for audit trail
        user_uuid = uuid.UUID(hashlib.md5(auth_details.user_id.encode()).hexdigest())
        
        template = await service.create_template(
            db=db,
            key=template_data["meta"]["key"],
            category=TemplateCategory.UNIFIED,
            scope=scope,
            tenant_id=template_tenant_id,
            body="",  # Unified templates don't use body field
            description=template_data["docs_config"].get("description"),
            parameters=template_data["parameters"]["schema"],
            response_format=template_data["response"]["crfs"],
            created_by=user_uuid
        )
        
        # Update template with unified fields directly
        template.endpoint_id = template_data["meta"].get("endpoint_id")
        template.intent_config = template_data["intent"]
        template.action_config = template_data["action"]
        template.ui_fields = template_data["parameters"]["ui_fields"]
        template.prompts = template_data["prompts"]
        template.docs_config = template_data["docs_config"]
        
        await db.commit()
        await db.refresh(template)
        
        # Convert to response format
        response_data = UnifiedTemplateResponse(
            id=str(template.id),
            key=template.key,
            category=template.category,
            scope=template.scope,
            version=template.version,
            meta=template_data["meta"],
            intent=template_data["intent"],
            action=template_data["action"],
            parameters=template_data["parameters"],
            prompts=template_data["prompts"],
            response=template_data["response"],
            docs_config=template_data["docs_config"],
            description=template.description,
            protected=template.protected,
            created_at=template.created_at.isoformat(),
            updated_at=template.updated_at.isoformat()
        )
        
        return SuccessResponse(data=response_data)
        
    except Exception as e:
        logger.error(f"Error generating template from OpenAPI: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate template")


@router.get("/{template_id}", response_model=SuccessResponse[UnifiedTemplateResponse])
async def get_template(
    template_id: UUID,
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details=Depends(get_clerk_auth_details),
    _perm_check=Depends(RequirePermission(CoherencePermission.TEMPLATE_READ))
):
    """Get a specific template by ID."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    try:
        service = TemplateService()
        template = await service.get_template_by_id(db, template_id)
        
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        # Check access permissions
        if not is_system_admin:
            if template.scope == TemplateScope.TENANT and template.tenant_id != tenant_id:
                raise HTTPException(status_code=403, detail="Access denied")
        
        # Convert to unified response format
        response_data = UnifiedTemplateResponse(
            id=str(template.id),
            key=template.key,
            category=template.category,
            scope=template.scope,
            version=template.version,
            meta=template.meta or {"key": template.key, "version": template.version},
            intent=template.intent_config or {},
            action=template.action_config or {},
            parameters={
                "schema": template.parameters or {},
                "ui_fields": template.ui_fields or {}
            },
            prompts=template.prompts or {},
            response={
                "crfs": template.response_format or {},
                "error_mapping": {}
            },
            docs_config=template.docs_config or {},
            description=template.description,
            protected=template.protected,
            created_at=template.created_at.isoformat(),
            updated_at=template.updated_at.isoformat()
        )
        
        return SuccessResponse(data=response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting template: {e}")
        raise HTTPException(status_code=500, detail="Failed to get template")


@router.get("/{template_id}/form-config", response_model=SuccessResponse[Dict[str, Any]])
async def get_template_form_config(
    template_id: UUID,
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details=Depends(get_clerk_auth_details),
    _perm_check=Depends(RequirePermission(CoherencePermission.TEMPLATE_READ))
):
    """Get form configuration for a template's parameters."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    try:
        service = TemplateService()
        template = await service.get_template_by_id(db, template_id)
        
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        # Check access permissions
        if not is_system_admin:
            if template.scope == TemplateScope.TENANT and template.tenant_id != tenant_id:
                raise HTTPException(status_code=403, detail="Access denied")
        
        # Generate form configuration
        template_data = {
            "meta": {"key": template.key},
            "parameters": {
                "schema": template.parameters or {},
                "ui_fields": template.ui_fields or {}
            },
            "prompts": template.prompts or {},
            "docs_config": template.docs_config or {}
        }
        
        form_config = UIFormGenerator.generate_form_config(template_data)
        
        return SuccessResponse(data=form_config)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting template form config: {e}")
        raise HTTPException(status_code=500, detail="Failed to get form configuration")


@router.delete("/{template_id}", response_model=SuccessResponse[Dict[str, str]])
async def delete_template(
    template_id: UUID,
    request: Request,
    db: AsyncSession = Depends(get_db),
    auth_details=Depends(get_clerk_auth_details),
    _perm_check=Depends(RequirePermission(CoherencePermission.TEMPLATE_DELETE))
):
    """Delete a template."""
    tenant_id = getattr(request.state, 'tenant_id', None)
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    
    try:
        service = TemplateService()
        template = await service.get_template_by_id(db, template_id)
        
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        # Check permissions
        if template.protected and not is_system_admin:
            raise HTTPException(status_code=403, detail="Cannot delete protected template")
        
        if not is_system_admin:
            if template.scope == TemplateScope.TENANT and template.tenant_id != tenant_id:
                raise HTTPException(status_code=403, detail="Access denied")
        
        await service.delete_template(db, template_id)
        
        return SuccessResponse(data={"message": "Template deleted successfully"})
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting template: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete template")