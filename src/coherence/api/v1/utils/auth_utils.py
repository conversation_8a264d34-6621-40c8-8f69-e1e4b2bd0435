"""
Utility functions for authentication and permissions checks.
"""

import logging

from fastapi import Request

logger = logging.getLogger(__name__)

# Constants
SYSTEM_WILDCARD_PERMISSION = "system:*"

def is_system_admin(request: Request) -> bool:
    """
    Check if the current request is from a system administrator.
    
    A user is considered a system admin if either:
    1. request.state.auth.is_system_admin is True, or
    2. request.state.auth.permissions contains the "system:*" wildcard permission
    
    Args:
        request: The FastAPI request object with populated auth state
        
    Returns:
        bool: True if the request is from a system admin, False otherwise
    """
    # First check the canonical is_system_admin flag
    is_system_admin_flag = getattr(request.state, "is_system_admin", False)
    
    if is_system_admin_flag:
        logger.debug("Request identified as system admin via is_system_admin flag")
        return True
    
    # Then check for the system:* wildcard permission
    permissions = getattr(request.state, "permissions", set())
    
    if permissions and SYSTEM_WILDCARD_PERMISSION in permissions:
        logger.debug(f"Request identified as system admin via '{SYSTEM_WILDCARD_PERMISSION}' permission")
        return True
        
    return False