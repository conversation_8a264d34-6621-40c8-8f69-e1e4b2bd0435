"""API router for the Coherence API."""

from fastapi import APIRouter

from src.coherence.api.v1.endpoints import (
    admin,
    admin_actions,
    admin_api_keys,
    admin_system,
    admin_templates,
    admin_workflows,
    credentials,
    debug,
    me,
    oauth,
    openapi,
    resolve,
    session_info,
    template_test,
    template_validation,
    templates,
    tenants,
    unified_templates,
    user_preferences,
)

# Import template_search if it exists, otherwise continue without it
try:
    from src.coherence.api.v1.endpoints import template_search
except ImportError:
    template_search = None

api_router = APIRouter()

api_router.include_router(session_info.router, prefix="/auth/session-info", tags=["auth"])
api_router.include_router(me.router, prefix="/me", tags=["auth"])
api_router.include_router(resolve.router, prefix="", tags=["resolve"])
api_router.include_router(debug.router, prefix="/debug", tags=["debug"])

# Template endpoints
api_router.include_router(templates.router, prefix="/templates", tags=["templates"])
api_router.include_router(unified_templates.router, prefix="/unified-templates", tags=["templates", "unified"])
api_router.include_router(template_validation.router, prefix="/templates", tags=["templates", "validation"])
api_router.include_router(template_test.router, prefix="/templates", tags=["templates", "test"])
# Include template_search router if it was imported successfully
if template_search:
    api_router.include_router(template_search.router, prefix="/templates", tags=["templates"])

# Admin endpoints
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
api_router.include_router(admin_templates.router, prefix="/admin/templates", tags=["admin", "templates"])
api_router.include_router(admin_workflows.router, prefix="/admin/workflows", tags=["admin", "workflows"])
api_router.include_router(admin_system.router, prefix="/admin/system", tags=["admin", "system"])
api_router.include_router(admin_api_keys.router, prefix="/admin/api-keys", tags=["admin", "api-keys"])

# Tenant management
api_router.include_router(tenants.router, prefix="/tenants", tags=["tenants"])

# User preferences
api_router.include_router(user_preferences.router, prefix="/user-preferences", tags=["user-preferences"])

# Credential management
api_router.include_router(credentials.router, prefix="/credentials", tags=["credentials"])

# OAuth
api_router.include_router(oauth.router, prefix="/oauth", tags=["oauth"])

# OpenAPI integration
api_router.include_router(openapi.router, prefix="/openapi", tags=["openapi"])

# Actions management and testing
api_router.include_router(admin_actions.router, prefix="/admin/actions", tags=["admin", "actions"])