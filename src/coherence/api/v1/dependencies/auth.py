"""
Authentication dependencies for FastAPI routes.

This module provides dependency functions for authenticating
requests based on API keys and enforcing tenant isolation.
"""

import hashlib
import logging

# Added for Clerk SDK integration
import os
import uuid
from datetime import (
    datetime,  # Not used in get_clerk_auth_details, consider removing if not used elsewhere in this snippet scope
    timezone,  # Add timezone for proper date comparison
)
from typing import Dict, Optional, Set

from clerk_backend_api import Clerk  # Corrected import
from clerk_backend_api import models as clerk_models  # Import models for error handling
from clerk_backend_api.jwks_helpers import (  # Added for new auth method
    AuthenticateRequestOptions,
)
from fastapi import Depends, Header, HTTPException, Request, status
from pydantic import BaseModel
from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.coherence.crud.crud_system_admin import (  # Import required system admin CRUD functions
    get_system_admin_api_key_by_hash,
    get_system_admin_by_id,
    update_system_admin_api_key,
)
from src.coherence.db.deps import get_db
from src.coherence.models.system_admin import SystemAdmin  # Import SystemAdmin models
from src.coherence.models.tenant import (  # Ensure all necessary models are imported
    APIKey,
    OrganizationAPIKey,
    Tenant,
)
from src.coherence.services.permission_service import (  # Not used in get_clerk_auth_details
    PermissionService,
)

logger = logging.getLogger(__name__)

# Initialize Clerk client - moved secret key retrieval into the dependency
clerk_client = Clerk()

class ClerkAuthDetails(BaseModel):
    user_id: str
    org_id: Optional[str] = None
    org_role: Optional[str] = None
    org_name: Optional[str] = None  # Add org_name field
    org_slug: Optional[str] = None
    org_metadata: Optional[Dict] = None # Add org_metadata field
    is_system_admin: bool

async def get_clerk_auth_details(
    request: Request,
    authorization: str = Header(None, description="Clerk JWT in 'Bearer <token>' format"),
    db: AsyncSession = Depends(get_db)
) -> ClerkAuthDetails:
    """
    Validates a Clerk JWT and extracts user and organization details.
    Sets these details and the corresponding Coherence Tenant in request.state.
    
    Expected JWT claims format:
    {
      "is_system_admin": boolean,    # Canonical boolean flag for system admin status
      "org_id": "org_xxx",           # Clerk organization ID if present
      "org_name": "Organization",    # Organization name if present
      "org_role": "admin",           # User's role in the organization (e.g., "admin", "member")
      "org_slug": "org-slug"         # Organization slug if present
    }
    
    Fallback to __session nested object is supported for backward compatibility.
    """
    clerk_secret_key = os.environ.get("CLERK_SECRET_KEY") # Get key at request time
    if not clerk_secret_key:
        logger.error("CRITICAL: CLERK_SECRET_KEY not found in environment at request time. Authentication will fail.")
        # This is a fatal misconfiguration for this auth method if the key is required by authenticate_request.
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Server misconfiguration: Missing secret for token validation."
        )

    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    parts = authorization.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token format. Use 'Bearer <token>'.",
            headers={"WWW-Authenticate": "Bearer"},
        )
    token = parts[1]

    try:
        # secret_key is now fetched locally within this function.
        auth_options = AuthenticateRequestOptions(
            secret_key=clerk_secret_key
        )
        
        request_state = clerk_client.authenticate_request(request, options=auth_options)

        if not request_state.is_signed_in:
            logger.warning(f"Clerk token validation failed: not signed in. Reason: {request_state.reason}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Authentication failed: {request_state.reason}",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        claims = request_state.payload
        if not claims:
            logger.error("Clerk token validated but payload (claims) is missing.")
            raise ValueError("Validated token has no payload.")

        # Add detailed logging of claims
        logger.info(f"Clerk token claims: {claims}")
        logger.info(f"Token claims keys: {list(claims.keys()) if claims else 'None'}")

        # Store raw claims in request state for debugging
        request.state.jwt_claims = claims

        user_id = claims.get("sub")
        if not user_id:
            raise ValueError("User ID (sub) not found in token claims")

        # Check if direct claims are available first - newer Clerk JWT templates put fields at root level
        org_id = claims.get("org_id")
        org_name = claims.get("org_name")
        org_role = claims.get("org_role")
        org_slug = claims.get("org_slug")

        # Fallback to session claims if direct claims aren't available
        session_claims = claims.get("__session", {})
        if not org_id:
            org_id = session_claims.get("org_id")
        if not org_name:
            org_name = session_claims.get("org_name")
        if not org_role:
            org_role = session_claims.get("org_role")
        if not org_slug:
            org_slug = session_claims.get("org_slug")

        org_metadata = session_claims.get("org_metadata", {}) # Default to empty dict if not present

        # Check for unprocessed template variables and log issues
        def has_template_markers(value):
            if not isinstance(value, str):
                return False
            return "{{" in value or "}}" in value

        if has_template_markers(org_id):
            logger.warning(f"JWT org_id contains template markers: {org_id}")
            org_id = None

        if has_template_markers(org_name):
            logger.warning(f"JWT org_name contains template markers: {org_name}")
            org_name = None

        if has_template_markers(org_role):
            logger.warning(f"JWT org_role contains template markers: {org_role}")
            org_role = None
        
        # Enhanced logging for specific claims from JWT's __session
        logger.info(f"JWT Claim - User ID (sub): {user_id}")
        logger.info(f"JWT __session Claim - Org ID: {org_id if org_id is not None else 'MISSING_OR_NONE'}")
        logger.info(f"JWT __session Claim - Org Name: {org_name if org_name is not None else 'MISSING_OR_NONE'}")
        logger.info(f"JWT __session Claim - Org Role: {org_role if org_role is not None else 'MISSING_OR_NONE'}")
        logger.info(f"JWT __session Claim - Org Slug: {org_slug if org_slug is not None else 'MISSING_OR_NONE'}")
        logger.info(f"JWT __session Claim - Org Metadata: {org_metadata if org_metadata is not None else 'MISSING_OR_NONE'}")
        
        # Determine if the user is a system admin based directly on JWT claim
        # 1. Check for direct is_system_admin flag in JWT (new canonical format)
        is_sys_admin_flag = claims.get("is_system_admin")
        if isinstance(is_sys_admin_flag, str):
            # Convert string 'true'/'false' to boolean
            is_sys_admin_flag = is_sys_admin_flag.lower() == 'true'
            
        # 2. Fall back to user_id based check only if is_system_admin is not in claims
        if is_sys_admin_flag is None:
            system_admin_clerk_user_id_env = os.environ.get("SYSTEM_ADMIN_CLERK_USER_ID")
            is_sys_admin_flag = bool(system_admin_clerk_user_id_env and user_id == system_admin_clerk_user_id_env)
            logger.info(f"Using fallback system admin check: user_id={user_id}, is_system_admin={is_sys_admin_flag}")
        
        # Ensure is_sys_admin_flag is a boolean
        is_sys_admin_flag = bool(is_sys_admin_flag)
        
        # Organization details are solely sourced from the JWT claims
        
        request.state.clerk_user_id = user_id
        request.state.clerk_org_id = org_id
        request.state.clerk_org_role = org_role
        request.state.rls_clerk_org_id = org_id 
        request.state.is_system_admin = is_sys_admin_flag

        if org_id:
            tenant_result = await db.execute(select(Tenant).where(Tenant.clerk_org_id == org_id))
            tenant = tenant_result.scalar_one_or_none()
            if tenant:
                request.state.tenant = tenant
                request.state.tenant_id = tenant.id 
                request.state.rls_tenant_id = str(tenant.id)
            else:
                # Auto-provision organization/tenant for authenticated users
                logger.info(f"Organization {org_id} not found. Auto-provisioning for user {user_id}.")
                
                try:
                    # Create new tenant with the Clerk organization details
                    new_tenant = Tenant(
                        name=org_name or f"Organization {org_id}",
                        clerk_org_id=org_id,
                        settings={"auto_provisioned": True, "is_admin": False}
                    )
                    db.add(new_tenant)
                    
                    # Need to flush to get the tenant ID for related records
                    await db.flush()
                    
                    # Create default tenant settings
                    from src.coherence.models.tenant import TenantSettings
                    tenant_settings = TenantSettings(
                        tenant_id=new_tenant.id,
                        tier1_threshold="0.85",
                        tier2_threshold="0.7",
                        llm_model="gpt-4",
                        embedding_model="text-embedding-ada-002",
                        max_requests_per_min="60",
                        max_tokens_per_month="1000000"
                    )
                    db.add(tenant_settings)
                    
                    # Commit the transaction
                    await db.commit()
                    await db.refresh(new_tenant)
                    
                    logger.info(f"Successfully auto-provisioned tenant {new_tenant.id} for Clerk org {org_id}")
                    
                    # Set the request state with the new tenant
                    request.state.tenant = new_tenant
                    request.state.tenant_id = new_tenant.id
                    request.state.rls_tenant_id = str(new_tenant.id)
                    
                except Exception as e:
                    await db.rollback()
                    logger.error(f"Failed to auto-provision tenant for org {org_id}: {e}")
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f"Failed to auto-provision organization. Please contact support."
                    ) from e
        else: 
            request.state.tenant = None
            request.state.tenant_id = None
            request.state.rls_tenant_id = None
        
        logger.info(f"Clerk auth successful: user_id={user_id}, org_id={org_id}, org_name={org_name}, org_role={org_role}, org_slug={org_slug}, org_metadata={org_metadata}, is_system_admin={is_sys_admin_flag}")
        return ClerkAuthDetails(
            user_id=user_id,
            org_id=org_id,
            org_role=org_role,
            org_name=org_name,
            org_slug=org_slug,
            org_metadata=org_metadata,
            is_system_admin=is_sys_admin_flag
        )
 
    except clerk_models.SDKError as err: # Catching Clerk specific SDK errors
        logger.warning(f"Clerk token validation SDKError: {err} (Token: {token[:20]}...)")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid token: {err}", # More specific error from the SDK
            headers={"WWW-Authenticate": "Bearer"},
        ) from err
    except ValueError as err: # Catch other ValueErrors (e.g., missing claims, misconfigured client)
        logger.warning(f"Clerk token validation ValueError: {err} (Token: {token[:20]}...)")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid token processing: {err}", 
            headers={"WWW-Authenticate": "Bearer"},
        ) from err
    except Exception as err:
        logger.error(f"Unexpected error during Clerk token validation: {err} (Token: {token[:20]}...)", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not validate authentication credentials."
        ) from err

async def get_org_api_key_principal(
    request: Request,
    x_api_key: str = Header(None, description="Organization API Key"),
    db: AsyncSession = Depends(get_db)
) -> Tenant: # Returns Tenant to maintain compatibility for now, can be a richer principal
    """
    Validates an API key from the 'organization_api_keys' table.
    Sets relevant details (Tenant, clerk_org_id, permissions) in request.state.
    """
    if x_api_key is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key (X-API-Key) required",
            headers={"WWW-Authenticate": "ApiKey"},
        )

    key_hash = hashlib.sha256(x_api_key.encode()).hexdigest()

    from src.coherence.core.config import settings
    if settings.SYSTEM_ADMIN_API_KEY and x_api_key == settings.SYSTEM_ADMIN_API_KEY:
        logger.info("System Admin Master Key used for M2M.")
        request.state.is_system_admin = True
        request.state.clerk_org_id = None
        request.state.rls_clerk_org_id = None
        request.state.tenant = None
        request.state.tenant_id = None
        request.state.rls_tenant_id = None
        request.state.api_key_permissions = ["system:*"] # All permissions
        
        first_tenant_res = await db.execute(select(Tenant).limit(1))
        first_tenant = first_tenant_res.scalar_one_or_none()
        if not first_tenant:
             logger.error("System Admin Key used, but no tenants exist for context.")
             raise HTTPException(status_code=500, detail="System Admin Key used, but no tenants exist for context.")
        request.state.tenant = first_tenant 
        logger.info(f"System Admin Master Key context set, using tenant {first_tenant.id} as placeholder.")
        return first_tenant

    org_api_key_result = await db.execute(
        select(OrganizationAPIKey)
        .where(OrganizationAPIKey.key_hash == key_hash)
        .where(OrganizationAPIKey.revoked is False)
    )
    org_api_key = org_api_key_result.scalar_one_or_none()

    if not org_api_key:
        logger.warning(f"Organization API Key not found or revoked. Prefix: {x_api_key[:8]}...")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid Organization API Key")

    if org_api_key.expires_at and org_api_key.expires_at < datetime.now(org_api_key.expires_at.tzinfo):
        logger.warning(f"Organization API Key expired. ID: {org_api_key.id}, Prefix: {x_api_key[:8]}...")
        # Consider auto-revoking: org_api_key.revoked = True; await db.commit()
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Organization API Key has expired")

    tenant_result = await db.execute(
        select(Tenant).where(Tenant.clerk_org_id == org_api_key.clerk_org_id)
    )
    tenant = tenant_result.scalar_one_or_none()

    if not tenant:
        logger.error(f"Organization {org_api_key.clerk_org_id} not found for API key ID {org_api_key.id}.")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Organization {org_api_key.clerk_org_id} not found for API key.")

    org_api_key.last_used_at = func.now()
    await db.commit()
    await db.refresh(org_api_key)

    request.state.tenant = tenant
    request.state.tenant_id = tenant.id
    request.state.rls_tenant_id = str(tenant.id)
    request.state.clerk_org_id = tenant.clerk_org_id
    request.state.rls_clerk_org_id = tenant.clerk_org_id
    request.state.api_key_permissions = org_api_key.permissions
    request.state.is_system_admin = False # Org API keys are not system admin keys
    
    logger.info(f"Org API Key auth successful: Key ID {org_api_key.id}, Org ID {tenant.clerk_org_id}")
    return tenant


async def authenticate_system_admin_api_key(
    request: Request,
    x_api_key: str = Header(..., description="System Administrator API Key"), # Use ... to make it required
    db: AsyncSession = Depends(get_db)
) -> SystemAdmin:
    """
    Authenticates a request using a System Administrator API Key.

    Validates the key against the `system_admin_api_keys` table,
    checks if it's active and not expired, updates its last used time,
    and returns the associated SystemAdmin object.
    """
    try:
        # Hash the API key for lookup
        key_hash = hashlib.sha256(x_api_key.encode()).hexdigest()
        
        # Get the API key from the database
        api_key = await get_system_admin_api_key_by_hash(db, key_hash=key_hash)
        
        if not api_key:
            logger.warning(f"System admin API key not found: {x_api_key[:8]}...")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid system admin API key",
                headers={"WWW-Authenticate": "ApiKey"},
            )
        
        # Check if the key is revoked
        if api_key.revoked:
            logger.warning(f"Revoked system admin API key used: {api_key.id}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key has been revoked",
                headers={"WWW-Authenticate": "ApiKey"},
            )
        
        # Check if the key is expired
        now = datetime.now(timezone.utc)
        if api_key.expires_at and api_key.expires_at < now:
            logger.warning(f"Expired system admin API key used: {api_key.id}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key has expired",
                headers={"WWW-Authenticate": "ApiKey"},
            )
        
        # Get the system admin
        system_admin = await get_system_admin_by_id(db, id=api_key.system_admin_id)
        
        if not system_admin:
            logger.error(f"System admin not found for API key: {api_key.id}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="System admin not found",
                headers={"WWW-Authenticate": "ApiKey"},
            )
        
        # Update the last used time
        await update_system_admin_api_key(
            db=db,
            db_obj=api_key,
            obj_in={"last_used_at": now}
        )
        
        # Set request state for RLS and other middleware
        request.state.is_system_admin = True
        request.state.system_admin = system_admin
        request.state.rls_is_admin = True  # System admins always have admin privileges
        
        logger.info(f"System admin authenticated via API key: {system_admin.id}")
        return system_admin
        
    except Exception as err:
        # Provide detailed logs for debugging but simple message to user
        logger.error(f"Error verifying Clerk JWT: {str(err)}")
        # Ensure consistent status code for auth failures
        raise HTTPException(status_code=401, detail="Invalid authentication credentials") from err


# ... (rest of the code remains the same)

async def get_tenant_from_api_key( # MARKED FOR DEPRECATION
    request: Request,
    x_api_key: str = Header(None),
    db: AsyncSession = Depends(get_db),
) -> Tenant:
    """
    (DEPRECATED) FastAPI dependency that extracts and validates the API key from the old 'api_keys' table.
    Prefer `get_clerk_auth_details` or `get_org_api_key_principal`.
    """
    logger.warning("DEPRECATED: get_tenant_from_api_key is called. This uses the old APIKey system.")
    if x_api_key is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required",
            headers={"WWW-Authenticate": "ApiKey"},
        )

    key_hash = hashlib.sha256(x_api_key.encode()).hexdigest()
    
    # Ensure this logger is the module-level one
    # logger.info(f"Debug: get_tenant_from_api_key received API key: {x_api_key[:8]}...")
    # logger.debug(f"Debug: API key hash: {key_hash}")

    from src.coherence.core.config import settings
    if settings.SYSTEM_ADMIN_API_KEY and x_api_key == settings.SYSTEM_ADMIN_API_KEY:
        logger.info("System admin key detected in (deprecated) get_tenant_from_api_key, bypassing database lookup for key itself.")
        result = await db.execute(select(Tenant).limit(1))
        tenant = result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No tenants found in the system for system admin key context.",
            )
        request.state.tenant = tenant
        request.state.tenant_id = tenant.id
        request.state.rls_tenant_id = str(tenant.id)
        request.state.tenant_is_admin = True 
        request.state.rls_is_admin = True
        request.state.is_system_admin = True
        logger.info(f"Using tenant {tenant.id} with system admin privileges via (deprecated) get_tenant_from_api_key.")
        return tenant

    try:
        result = await db.execute(
            select(APIKey).where(
                APIKey.key_hash == key_hash,
                APIKey.revoked.is_(False),
            )
        )
        api_key = result.scalar_one_or_none()

        if not api_key:
            logger.warning(f"Old API key not found in database: {x_api_key[:8]}...")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid API key (old system)",
                headers={"WWW-Authenticate": "ApiKey"},
            )

        logger.info(f"Old API key found: {api_key.id} for tenant {api_key.tenant_id}")
        tenant_result = await db.execute(select(Tenant).where(Tenant.id == api_key.tenant_id))
        tenant = tenant_result.scalar_one_or_none()

        if not tenant:
            logger.warning(f"Tenant not found for old API key: {api_key.id}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Tenant not found (old system)",
                headers={"WWW-Authenticate": "ApiKey"},
            )

        api_key.last_used_at = func.now()
        await db.commit()

        request.state.tenant = tenant
        request.state.tenant_id = tenant.id
        request.state.rls_tenant_id = str(tenant.id)
        tenant_settings = tenant.settings or {}
        is_admin = tenant_settings.get("is_admin", False)
        request.state.tenant_is_admin = is_admin
        request.state.rls_is_admin = is_admin
        request.state.is_system_admin = False # Old keys are not system admin keys unless it's THE system admin key

        logger.info(
            f"Authentication successful via (deprecated) get_tenant_from_api_key for tenant {tenant.id} (admin: {is_admin})"
        )
        return tenant
    except Exception as err:
        logger.error(f"Error retrieving Clerk organization: {str(err)}")
        # Use 500 for server errors when processing valid auth
        raise HTTPException(status_code=500, detail="Error processing organization details") from err

async def check_clerk_org_admin(request: Request):
    """
    Checks if the authenticated Clerk user has an admin role ('admin', 'owner') for their organization.
    Relies on a prior dependency to populate request.state.clerk_org_role and request.state.is_system_admin.
    """
    org_role = getattr(request.state, "clerk_org_role", None)
    is_system_admin_flag = getattr(request.state, "is_system_admin", False)

    if is_system_admin_flag: # System admins bypass org role checks
        logger.info(f"Org admin check bypassed for system admin. User ID: {getattr(request.state, 'clerk_user_id', 'N/A')}")
        return

    ORG_ADMIN_ROLES = ["admin", "owner"] 
    if not org_role or org_role.lower() not in ORG_ADMIN_ROLES:
        logger.warning(f"Org admin check failed. User role: {org_role}. User ID: {getattr(request.state, 'clerk_user_id', 'N/A')}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Organization admin privileges required."
        )
    logger.info(f"Org admin check passed. User role: {org_role}. User ID: {getattr(request.state, 'clerk_user_id', 'N/A')}")

async def check_is_system_admin(request: Request):
    """
    Checks if the current context is for a system admin.
    Relies on a prior auth dependency to set request.state.is_system_admin.
    """
    is_system_admin_flag = getattr(request.state, "is_system_admin", False)
    if not is_system_admin_flag:
        logger.warning(f"System admin check failed. User ID: {getattr(request.state, 'clerk_user_id', 'N/A')}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="System administrator privileges required."
        )
    logger.info(f"System admin check passed. User ID: {getattr(request.state, 'clerk_user_id', 'N/A')}")


async def check_admin_role(  # MARKED FOR DEPRECATION - Use check_clerk_org_admin or check_is_system_admin
    request: Request,
) -> None:
    """
    (DEPRECATED) Dependency that checks if the tenant (from request.state) has admin privileges
    based on the old tenant.settings.is_admin flag.
    Use check_clerk_org_admin or check_is_system_admin instead.
    """
    logger.warning("DEPRECATED: check_admin_role is called. Migrate to new auth checks (check_clerk_org_admin or check_is_system_admin).")

    current_tenant = getattr(request.state, "tenant", None)
    is_system_admin_flag = getattr(request.state, "is_system_admin", False)

    if is_system_admin_flag: # System admins inherently have admin role for this check's purpose
        logger.info("Deprecated check_admin_role bypassed for system admin.")
        # Ensure rls_is_admin is set if not already by the primary auth method
        if not hasattr(request.state, 'rls_is_admin') or not request.state.rls_is_admin:
            request.state.rls_is_admin = True
        return

    if not isinstance(current_tenant, Tenant):
        logger.error("Tenant context not found or invalid in request state for deprecated admin check.")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Tenant context not found or invalid in request state for admin check (deprecated).",
        )

    tenant_settings = current_tenant.settings or {}
    is_admin_in_settings = tenant_settings.get("is_admin", False)

    if not is_admin_in_settings:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required (legacy check)",
        )
    
    # Ensure rls_is_admin is set if not already by the primary auth method
    if not hasattr(request.state, 'rls_is_admin') or not request.state.rls_is_admin:
        request.state.rls_is_admin = True


async def get_current_tenant_id( # MARKED FOR REVIEW/DEPRECATION
    tenant: Tenant = Depends(get_tenant_from_api_key), # This dependency itself is deprecated
) -> uuid.UUID:
    """
    (REVIEW FOR DEPRECATION) Dependency that returns the current Coherence internal tenant ID.
    Relies on the deprecated get_tenant_from_api_key.
    Consider using clerk_org_id or tenant_id from request.state directly.
    """
    logger.warning("DEPRECATED: get_current_tenant_id is called. Consider using clerk_org_id or tenant_id from request.state.")
    if not tenant: # Should not happen if Depends works, but good for safety
        raise HTTPException(status_code=500, detail="Tenant not available in get_current_tenant_id")
    return tenant.id


async def system_admin_context( # MARKED FOR DEPRECATION - Functionality covered by check_is_system_admin and how master key is handled
    request: Request,
    api_key: str = Header(..., description="System admin API key"),
) -> None:
    """
    (DEPRECATED) Dependency that checks if the provided API key is a system admin key.
    Use `check_is_system_admin` after authentication via `get_clerk_auth_details` (for Clerk system admins)
    or `get_org_api_key_principal` (for master API key).
    """
    logger.warning("DEPRECATED: system_admin_context is called. Use check_is_system_admin with appropriate primary auth.")
    from src.coherence.core.config import settings

    # This check is now largely duplicated by how SYSTEM_ADMIN_API_KEY is handled in
    # get_tenant_from_api_key (deprecated) and get_org_api_key_principal.
    # The primary role of this function was to set request.state.is_system_admin.
    # If the primary auth dependencies correctly set this, this function is redundant.

    if not settings.SYSTEM_ADMIN_API_KEY or api_key != settings.SYSTEM_ADMIN_API_KEY:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid system admin API key (deprecated context)",
        )

    # Ensure state is set, though primary auth should do this.
    if not getattr(request.state, "is_system_admin", False):
        request.state.is_system_admin = True
    if not getattr(request.state, "rls_is_admin", False): # rls_is_admin is also important
         request.state.rls_is_admin = True
    
    # For RLS, system admin often means no specific tenant_id for filtering
    request.state.tenant_id = None
    request.state.rls_tenant_id = None


# New Coherence Permission Dependencies

async def get_current_user_coherence_permissions(
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    permission_service: PermissionService = Depends(PermissionService)
) -> Set[str]:
    """
    Returns the set of Coherence-specific permissions for the current authenticated user.
    """
    return permission_service.get_coherence_permissions(
        clerk_org_role=clerk_auth.org_role,
        is_system_admin=clerk_auth.is_system_admin
    )


class RequirePermission:
    """
    Dependency class to check if the authenticated user has a specific Coherence permission.
    Usage: Depends(RequirePermission(CoherencePermission.WORKFLOW_CREATE))
           or Depends(RequirePermission("integration:manage_credentials"))
    """
    def __init__(self, required_permission):
        # Accept either a CoherencePermission enum or a string
        if hasattr(required_permission, 'value'):
            self.required_permission_value = required_permission.value
        else:
            self.required_permission_value = required_permission

    async def __call__(
        self,
        request: Request, # Added request for logging user details if needed from state
        clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
        user_permissions: Set[str] = Depends(get_current_user_coherence_permissions)
    ):
        if self.required_permission_value not in user_permissions:
            logger.warning(
                f"Permission check FAILED for user '{clerk_auth.user_id}' "
                f"(Org: '{clerk_auth.org_id}', Role: '{clerk_auth.org_role}', SysAdmin: {clerk_auth.is_system_admin}). "
                f"Required permission: '{self.required_permission_value}'. "
                f"User has: {user_permissions}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permission: {self.required_permission_value}"
            )
        logger.info(
            f"Permission check PASSED for user '{clerk_auth.user_id}'. "
            f"Required: '{self.required_permission_value}'."
        )
