"""
Event Integration for Knowledge Graph

Integrates with Coherence's event system to automatically sync entities to the graph.
"""

import logging
import asyncio
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timezone
import json

from src.coherence.core.metrics import (
    INTENT_RESOLUTION_COUNT,
    WORKFLOW_EXECUTION_LATENCY,
    ACTION_EXECUTION_COUNT
)
from src.coherence.db.deps import get_db_from_session, get_async_db
from src.coherence.models.template import Template
from src.coherence.models.workflow import Workflow
from src.coherence.schemas.request import ResolveRequest
from src.coherence.monitoring.metrics_collector import MetricsCollector

from .async_graph_adapter import AsyncGraphAdapter
from .discovery import RelationshipDiscovery
from .learning import LearningOperations

logger = logging.getLogger(__name__)


class KGEventHandler:
    """
    Handles Coherence events and syncs them to the Knowledge Graph.
    
    Listens to:
    - Intent processing events
    - Template usage events
    - Workflow execution events
    - Learning/feedback events
    """
    
    def __init__(
        self,
        kg_host: str = "localhost",
        kg_port: int = 6381,
        metrics_collector: Optional[MetricsCollector] = None
    ):
        """
        Initialize event handler.
        
        Args:
            kg_host: FalkorDB host
            kg_port: FalkorDB port
            metrics_collector: Optional metrics collector
        """
        self.kg_host = kg_host
        self.kg_port = kg_port
        
        # Event handlers mapping
        self.event_handlers = {
            "intent.created": self._handle_intent_created,
            "intent.resolved": self._handle_intent_resolved,
            "template.used": self._handle_template_used,
            "workflow.started": self._handle_workflow_started,
            "workflow.completed": self._handle_workflow_completed,
            "workflow.failed": self._handle_workflow_failed,
            "api.called": self._handle_api_called,
            "pattern.detected": self._handle_pattern_detected,
            "feedback.received": self._handle_feedback_received
        }
        
        # Background tasks
        self._tasks = []
        self._running = False
        
    async def start(self):
        """Start event handler."""
        self._running = True
        logger.info("KG Event Handler started")
        
        # Start background tasks
        self._tasks.append(
            asyncio.create_task(self._aggregate_metrics_task())
        )
        
    async def stop(self):
        """Stop event handler."""
        self._running = False
        
        # Cancel background tasks
        for task in self._tasks:
            task.cancel()
            
        await asyncio.gather(*self._tasks, return_exceptions=True)
        logger.info("KG Event Handler stopped")
        
    async def handle_event(self, event_type: str, event_data: Dict[str, Any]):
        """
        Handle a Coherence event.
        
        Args:
            event_type: Type of event
            event_data: Event payload
        """
        handler = self.event_handlers.get(event_type)
        
        if handler:
            try:
                # Add timestamp if not present
                if "timestamp" not in event_data:
                    event_data["timestamp"] = datetime.now(timezone.utc)
                    
                # Handle event asynchronously
                await handler(event_data)
                
                # Record metrics
                ACTION_EXECUTION_COUNT.labels(
                    action_type="kg_event",
                    status="success"
                ).inc()
                    
            except Exception as e:
                logger.error(f"Error handling event {event_type}: {e}")
                ACTION_EXECUTION_COUNT.labels(
                    action_type="kg_event", 
                    status="failed"
                ).inc()
        else:
            logger.debug(f"No handler for event type: {event_type}")
            
    async def _handle_intent_created(self, event_data: Dict[str, Any]):
        """Handle intent creation event."""
        db = await get_db_from_session()
        try:
            async with AsyncGraphAdapter(db, self.kg_host, self.kg_port) as adapter:
                # Create intent request from event data
                intent = ResolveRequest(
                    user_id=event_data["user_id"],
                    message=event_data["message"],
                    role=event_data.get("role", "user"),
                    tenant_id=event_data.get("tenant_id", "default")
                )
                
                # Initial sync without resolution data
                await adapter._sync_single_intent(
                    intent,
                    {
                        "tenant_id": event_data.get("tenant_id"),
                        "session_id": event_data.get("session_id"),
                        "domain": "unknown",  # Will be updated on resolution
                        "confidence": 0.0
                    }
                )
        except Exception as e:
            logger.error(f"Error handling intent created event: {e}")
            raise
                
    async def _handle_intent_resolved(self, event_data: Dict[str, Any]):
        """Handle intent resolution event."""
        db = await get_db_from_session()
        try:
            async with AsyncGraphAdapter(db, self.kg_host, self.kg_port) as adapter:
                # Create intent request
                intent = ResolveRequest(
                    user_id=event_data["user_id"],
                    message=event_data["message"],
                    role=event_data.get("role", "user"),
                    tenant_id=event_data.get("tenant_id", "default")
                )
                
                # Sync with resolution data
                result = await adapter._sync_single_intent(
                    intent,
                    {
                        "tenant_id": event_data.get("tenant_id"),
                        "session_id": event_data.get("session_id"),
                        "domain": event_data.get("domain", "general"),
                        "confidence": event_data.get("confidence", 0.5),
                        "parameters": event_data.get("parameters", {}),
                        "template_id": event_data.get("template_id")
                    }
                )
                
                # Discover relationships
                if result.get("graph_id"):
                    discovery = RelationshipDiscovery(
                        adapter._kg,
                        adapter._node_service,
                        adapter._rel_service
                    )
                    
                    await asyncio.to_thread(
                        discovery.discover_all,
                        result["graph_id"],
                        "Intent",
                        result["node"]
                    )
        except Exception as e:
            logger.error(f"Error handling intent resolved event: {e}")
            raise
                    
    async def _handle_template_used(self, event_data: Dict[str, Any]):
        """Handle template usage event."""
        template_id = event_data.get("template_id")
        success = event_data.get("success", True)
        
        if not template_id:
            return
            
        async with get_async_db() as db:
            async with AsyncGraphAdapter(db, self.kg_host, self.kg_port) as adapter:
                # Update template usage metrics
                learning = LearningOperations(
                    adapter._kg,
                    adapter._node_service,
                    adapter._rel_service
                )
                
                await asyncio.to_thread(
                    learning.update_pattern_success,
                    template_id,
                    success,
                    event_data
                )
                
    async def _handle_workflow_started(self, event_data: Dict[str, Any]):
        """Handle workflow start event."""
        # Store workflow start time for duration calculation
        workflow_id = event_data.get("workflow_id")
        if workflow_id:
            # Could store in Redis for distributed systems
            event_data["_start_time"] = datetime.now(timezone.utc)
            
    async def _handle_workflow_completed(self, event_data: Dict[str, Any]):
        """Handle workflow completion event."""
        workflow_id = event_data.get("workflow_id")
        
        if not workflow_id:
            return
            
        async with get_async_db() as db:
            async with AsyncGraphAdapter(db, self.kg_host, self.kg_port) as adapter:
                # Calculate duration
                start_time = event_data.get("_start_time", event_data.get("timestamp"))
                end_time = event_data.get("timestamp", datetime.now(timezone.utc))
                duration_ms = int((end_time - start_time).total_seconds() * 1000)
                
                # Get workflow from database
                workflow = await db.get(Workflow, workflow_id)
                
                if workflow:
                    # Sync workflow to graph
                    execution_data = {
                        "intent_id": event_data.get("intent_id"),
                        "user_id": event_data.get("user_id"),
                        "session_id": event_data.get("session_id"),
                        "tenant_id": event_data.get("tenant_id"),
                        "template_id": event_data.get("template_id"),
                        "duration_ms": duration_ms,
                        "success": True,
                        "api_calls": event_data.get("api_calls", []),
                        "performance_metrics": event_data.get("metrics", {})
                    }
                    
                    # Sync workflow
                    result = await asyncio.to_thread(
                        adapter.sync_workflow,
                        workflow,
                        execution_data
                    )
                    
                    # Learn from execution
                    if result.get("graph_id"):
                        learning = LearningOperations(
                            adapter._kg,
                            adapter._node_service,
                            adapter._rel_service
                        )
                        
                        patterns = await asyncio.to_thread(
                            learning.learn_from_execution,
                            result["graph_id"],
                            execution_data
                        )
                        
                        logger.info(f"Learned {len(patterns)} patterns from workflow {workflow_id}")
                        
    async def _handle_workflow_failed(self, event_data: Dict[str, Any]):
        """Handle workflow failure event."""
        # Similar to completed but with failure analysis
        workflow_id = event_data.get("workflow_id")
        error = event_data.get("error")
        
        if workflow_id and error:
            async with get_async_db() as db:
                async with AsyncGraphAdapter(db, self.kg_host, self.kg_port) as adapter:
                    # Record failure pattern
                    learning = LearningOperations(
                        adapter._kg,
                        adapter._node_service,
                        adapter._rel_service
                    )
                    
                    await asyncio.to_thread(
                        learning.record_learning,
                        "workflow_failure",
                        workflow_id,
                        "Workflow",
                        0.9,
                        {
                            "error": str(error),
                            "error_type": type(error).__name__,
                            "context": event_data
                        }
                    )
                    
    async def _handle_api_called(self, event_data: Dict[str, Any]):
        """Handle API call event."""
        # API calls are typically recorded as part of workflow execution
        # This handler is for standalone API tracking
        pass
        
    async def _handle_pattern_detected(self, event_data: Dict[str, Any]):
        """Handle pattern detection event."""
        pattern_type = event_data.get("pattern_type")
        pattern_data = event_data.get("pattern_data")
        confidence = event_data.get("confidence", 0.7)
        
        if pattern_type and pattern_data:
            async with get_async_db() as db:
                async with AsyncGraphAdapter(db, self.kg_host, self.kg_port) as adapter:
                    # Create pattern node
                    pattern = await adapter._node_service.create_node(
                        "Pattern",
                        {
                            "type": pattern_type,
                            "pattern": json.dumps(pattern_data),
                            "confidence": confidence,
                            "source": event_data.get("source", "automatic"),
                            "domain": event_data.get("domain", "general")
                        }
                    )
                    
                    logger.info(f"Created pattern node: {pattern.id}")
                    
    async def _handle_feedback_received(self, event_data: Dict[str, Any]):
        """Handle user feedback event."""
        entity_id = event_data.get("entity_id")
        entity_type = event_data.get("entity_type")
        feedback_type = event_data.get("feedback_type")  # positive/negative
        
        if entity_id and entity_type:
            async with get_async_db() as db:
                async with AsyncGraphAdapter(db, self.kg_host, self.kg_port) as adapter:
                    # Update entity based on feedback
                    if entity_type == "template" and feedback_type:
                        learning = LearningOperations(
                            adapter._kg,
                            adapter._node_service,
                            adapter._rel_service
                        )
                        
                        success = feedback_type == "positive"
                        await asyncio.to_thread(
                            learning.update_pattern_success,
                            entity_id,
                            success,
                            {"feedback": event_data}
                        )
                        
    async def _aggregate_metrics_task(self):
        """Background task to aggregate metrics periodically."""
        while self._running:
            try:
                # Run every hour
                await asyncio.sleep(3600)
                
                async with get_async_db() as db:
                    async with AsyncGraphAdapter(db, self.kg_host, self.kg_port) as adapter:
                        # Aggregate API calls
                        await adapter.aggregate_api_calls()
                        
                        logger.info("Completed hourly metrics aggregation")
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics aggregation: {e}")
                
                
def create_event_handler(
    kg_host: str = "localhost",
    kg_port: int = 6381,
    metrics_collector: Optional[MetricsCollector] = None
) -> KGEventHandler:
    """
    Create and configure KG event handler.
    
    Args:
        kg_host: FalkorDB host
        kg_port: FalkorDB port
        metrics_collector: Optional metrics collector
        
    Returns:
        Configured event handler
    """
    return KGEventHandler(kg_host, kg_port, metrics_collector)


# Integration point for Coherence event bus
def register_kg_handlers(event_bus: Any, handler: KGEventHandler):
    """
    Register KG handlers with Coherence event bus.
    
    Args:
        event_bus: Coherence event bus
        handler: KG event handler
    """
    # Register all event types
    for event_type in handler.event_handlers.keys():
        event_bus.subscribe(
            event_type,
            lambda event_data, et=event_type: asyncio.create_task(
                handler.handle_event(et, event_data)
            )
        )
        
    logger.info(f"Registered {len(handler.event_handlers)} KG event handlers")