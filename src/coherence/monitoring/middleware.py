"""
Monitoring Middleware

This module provides middleware for automatically collecting metrics
during request processing.
"""

import logging
import time
from typing import Optional
from uuid import UUID, uuid4

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.types import ASGI<PERSON>pp

from src.coherence.core.config import settings
from src.coherence.core.metrics import REQUEST_COUNT, REQUEST_LATENCY
from src.coherence.monitoring.metrics_collector import TenantMetricsCollector

logger = logging.getLogger(__name__)


class MetricsMiddleware(BaseHTTPMiddleware):
    """
    Middleware for automatically collecting request metrics.

    This middleware records API request latency, error rates, and
    other metrics for each request, with tenant isolation.
    """

    def __init__(
        self,
        app: ASGIApp,
        exclude_paths: Optional[list[str]] = None,
    ):
        """
        Initialize the middleware.

        Args:
            app: The ASGI application
            exclude_paths: List of paths to exclude from metric collection
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or ["/metrics", "/health"]

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Process the request and collect metrics.

        Args:
            request: The incoming request
            call_next: The next middleware or endpoint handler

        Returns:
            The response from the next middleware or endpoint
        """
        # Skip excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)

        # Record start time for latency calculation
        start_time = time.time()

        # Process the request
        response = await call_next(request)

        # Calculate latency
        latency = time.time() - start_time

        # Get tenant ID from the request state or header
        tenant_id = None

        # First try to get from state (set by auth middleware)
        if hasattr(request.state, "tenant_id"):
            tenant_id = str(request.state.tenant_id)
        # Then try to get from headers (X-Tenant-ID)
        elif "X-Tenant-ID" in request.headers:
            try:
                # Validate it's a valid UUID
                tenant_id = str(UUID(request.headers["X-Tenant-ID"]))
            except ValueError:
                # If not valid, use default
                tenant_id = settings.DEFAULT_TENANT_ID
        # Finally fall back to default
        else:
            tenant_id = settings.DEFAULT_TENANT_ID

        # Get tenant-specific metrics collector with the extracted ID
        metrics = TenantMetricsCollector(tenant_id=tenant_id)

        # Add request ID for tracing if not present
        request_id = request.headers.get("X-Request-ID", str(uuid4()))

        # Record API request metrics using direct Prometheus metrics
        # Make sure tenant_id is not None or empty
        effective_tenant_id = tenant_id if tenant_id else settings.DEFAULT_TENANT_ID

        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.url.path,
            status=str(response.status_code),
            tenant_id=effective_tenant_id,
        ).inc()

        REQUEST_LATENCY.labels(
            method=request.method,
            endpoint=request.url.path,
            status=str(response.status_code),
            tenant_id=effective_tenant_id,
        ).observe(latency)

        # Also record using the metrics collector for backward compatibility
        metrics.record_api_request(
            latency=latency,
            endpoint=request.url.path,
            method=request.method,
            status=response.status_code,
            tenant_id=tenant_id,
            request_id=request_id,
        )

        # If it's an error response, record error metrics
        if response.status_code >= 400:
            error_type = (
                "server_error" if response.status_code >= 500 else "client_error"
            )
            metrics.record_error(
                error_type=error_type,
                component="api",
                endpoint=request.url.path,
                status_code=response.status_code,
                tenant_id=effective_tenant_id,  # Use the same effective tenant ID
                request_id=request_id,
            )

        return response
