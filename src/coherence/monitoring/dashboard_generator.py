"""
Dashboard Generator

This module provides utilities for generating Grafana dashboards
with tenant-specific panels and metrics.
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class DashboardGenerator:
    """
    Generates Grafana dashboards with tenant-specific metrics.

    This class provides methods for creating comprehensive dashboards
    for monitoring system health, errors, and performance metrics.
    """

    def __init__(
        self,
        output_dir: str,
        datasource_uid: str = "PBFA97CFB590B2093",
        base_dashboard: Optional[Dict[str, Any]] = None,
    ):
        """
        Initialize the dashboard generator.

        Args:
            output_dir: Directory to save generated dashboards
            datasource_uid: Prometheus datasource UID
            base_dashboard: Optional base dashboard template
        """
        self.output_dir = output_dir
        self.datasource_uid = datasource_uid

        # Default dashboard template
        self.base_dashboard = base_dashboard or {
            "annotations": {
                "list": [
                    {
                        "builtIn": 1,
                        "datasource": {"type": "grafana", "uid": "-- Grafana --"},
                        "enable": True,
                        "hide": True,
                        "iconColor": "rgba(0, 211, 255, 1)",
                        "name": "Annotations & Alerts",
                        "type": "dashboard",
                    }
                ]
            },
            "editable": True,
            "fiscalYearStartMonth": 0,
            "graphTooltip": 0,
            "links": [],
            "liveNow": False,
            "panels": [],
            "refresh": "10s",
            "schemaVersion": 38,
            "style": "dark",
            "tags": ["coherence"],
            "templating": {
                "list": [
                    {
                        "current": {
                            "selected": False,
                            "text": "All",
                            "value": "$__all",
                        },
                        "datasource": {
                            "type": "prometheus",
                            "uid": self.datasource_uid,
                        },
                        "definition": "label_values(tenant_id)",
                        "hide": 0,
                        "includeAll": True,
                        "label": "Tenant",
                        "multi": False,
                        "name": "tenant_id",
                        "options": [],
                        "query": {
                            "query": "label_values(tenant_id)",
                            "refId": "StandardVariableQuery",
                        },
                        "refresh": 1,
                        "regex": "",
                        "skipUrlSync": False,
                        "sort": 0,
                        "type": "query",
                    }
                ]
            },
            "time": {"from": "now-6h", "to": "now"},
            "timepicker": {},
            "timezone": "",
            "weekStart": "",
        }

    def _create_time_series_panel(
        self,
        title: str,
        query: str,
        unit: str = "short",
        description: str = "",
        x: int = 0,
        y: int = 0,
        width: int = 12,
        height: int = 8,
        panel_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Create a time series panel configuration.

        Args:
            title: Panel title
            query: PromQL query
            unit: Unit for display
            description: Panel description
            x: Grid X position
            y: Grid Y position
            width: Panel width
            height: Panel height
            panel_id: Optional panel ID

        Returns:
            Panel configuration dictionary
        """
        return {
            "datasource": {"type": "prometheus", "uid": self.datasource_uid},
            "fieldConfig": {
                "defaults": {
                    "color": {"mode": "palette-classic"},
                    "custom": {
                        "axisCenteredZero": False,
                        "axisColorMode": "text",
                        "axisLabel": "",
                        "axisPlacement": "auto",
                        "barAlignment": 0,
                        "drawStyle": "line",
                        "fillOpacity": 10,
                        "gradientMode": "none",
                        "hideFrom": {"legend": False, "tooltip": False, "viz": False},
                        "lineInterpolation": "linear",
                        "lineWidth": 1,
                        "pointSize": 5,
                        "scaleDistribution": {"type": "linear"},
                        "showPoints": "auto",
                        "spanNulls": False,
                        "stacking": {"group": "A", "mode": "none"},
                        "thresholdsStyle": {"mode": "off"},
                    },
                    "mappings": [],
                    "thresholds": {
                        "mode": "absolute",
                        "steps": [
                            {"color": "green", "value": None},
                            {"color": "red", "value": 80},
                        ],
                    },
                    "unit": unit,
                },
                "overrides": [],
            },
            "gridPos": {"h": height, "w": width, "x": x, "y": y},
            "id": panel_id or 1,
            "options": {
                "legend": {
                    "calcs": ["mean", "max", "sum"],
                    "displayMode": "table",
                    "placement": "bottom",
                    "showLegend": True,
                },
                "tooltip": {"mode": "single", "sort": "none"},
            },
            "targets": [
                {
                    "datasource": {"type": "prometheus", "uid": self.datasource_uid},
                    "editorMode": "code",
                    "expr": query,
                    "legendFormat": "{{label_format}}",
                    "range": True,
                    "refId": "A",
                }
            ],
            "title": title,
            "description": description,
            "type": "timeseries",
        }

    def _create_stat_panel(
        self,
        title: str,
        query: str,
        unit: str = "short",
        description: str = "",
        x: int = 0,
        y: int = 0,
        width: int = 6,
        height: int = 4,
        panel_id: Optional[int] = None,
        thresholds: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        Create a stat panel configuration.

        Args:
            title: Panel title
            query: PromQL query
            unit: Unit for display
            description: Panel description
            x: Grid X position
            y: Grid Y position
            width: Panel width
            height: Panel height
            panel_id: Optional panel ID
            thresholds: Optional threshold configuration

        Returns:
            Panel configuration dictionary
        """
        if thresholds is None:
            thresholds = [
                {"color": "green", "value": None},
                {"color": "red", "value": 80},
            ]

        return {
            "datasource": {"type": "prometheus", "uid": self.datasource_uid},
            "fieldConfig": {
                "defaults": {
                    "color": {"mode": "thresholds"},
                    "mappings": [],
                    "thresholds": {"mode": "absolute", "steps": thresholds},
                    "unit": unit,
                },
                "overrides": [],
            },
            "gridPos": {"h": height, "w": width, "x": x, "y": y},
            "id": panel_id or 1,
            "options": {
                "colorMode": "value",
                "graphMode": "area",
                "justifyMode": "auto",
                "orientation": "auto",
                "reduceOptions": {
                    "calcs": ["lastNotNull"],
                    "fields": "",
                    "values": False,
                },
                "textMode": "auto",
            },
            "pluginVersion": "10.0.3",
            "targets": [
                {
                    "datasource": {"type": "prometheus", "uid": self.datasource_uid},
                    "editorMode": "code",
                    "expr": query,
                    "legendFormat": "__auto",
                    "range": True,
                    "refId": "A",
                }
            ],
            "title": title,
            "description": description,
            "type": "stat",
        }

    def _create_pie_panel(
        self,
        title: str,
        query: str,
        description: str = "",
        x: int = 0,
        y: int = 0,
        width: int = 8,
        height: int = 8,
        panel_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Create a pie chart panel configuration.

        Args:
            title: Panel title
            query: PromQL query
            description: Panel description
            x: Grid X position
            y: Grid Y position
            width: Panel width
            height: Panel height
            panel_id: Optional panel ID

        Returns:
            Panel configuration dictionary
        """
        return {
            "datasource": {"type": "prometheus", "uid": self.datasource_uid},
            "fieldConfig": {
                "defaults": {
                    "color": {"mode": "palette-classic"},
                    "custom": {
                        "hideFrom": {"legend": False, "tooltip": False, "viz": False}
                    },
                    "mappings": [],
                },
                "overrides": [],
            },
            "gridPos": {"h": height, "w": width, "x": x, "y": y},
            "id": panel_id or 1,
            "options": {
                "legend": {
                    "displayMode": "list",
                    "placement": "right",
                    "showLegend": True,
                },
                "pieType": "pie",
                "reduceOptions": {
                    "calcs": ["lastNotNull"],
                    "fields": "",
                    "values": False,
                },
                "tooltip": {"mode": "single", "sort": "none"},
            },
            "targets": [
                {
                    "datasource": {"type": "prometheus", "uid": self.datasource_uid},
                    "editorMode": "code",
                    "expr": query,
                    "legendFormat": "{{label_format}}",
                    "range": True,
                    "refId": "A",
                }
            ],
            "title": title,
            "description": description,
            "type": "piechart",
        }

    def generate_system_health_dashboard(self) -> Dict[str, Any]:
        """
        Generate a system health dashboard.

        Returns:
            Dashboard configuration
        """
        dashboard = self.base_dashboard.copy()
        dashboard["title"] = "Coherence - System Health"
        dashboard["uid"] = "coherence-system-health"

        panels = []

        # API Request Rate
        panels.append(
            self._create_time_series_panel(
                title="API Request Rate",
                query='sum(rate(coherence_api_request_seconds_count{tenant_id=~"$tenant_id"}[5m])) by (endpoint)',
                description="Number of API requests per second by endpoint",
                unit="reqps",
                x=0,
                y=0,
                width=12,
                height=8,
                panel_id=1,
            )
        )

        # API Error Rate
        panels.append(
            self._create_time_series_panel(
                title="API Error Rate",
                query='sum(rate(coherence_error_by_endpoint_total{tenant_id=~"$tenant_id"}[5m])) by (endpoint)',
                description="Number of API errors per second by endpoint",
                unit="errors/s",
                x=12,
                y=0,
                width=12,
                height=8,
                panel_id=2,
            )
        )

        # API Latency
        panels.append(
            self._create_time_series_panel(
                title="API Latency (p95)",
                query='histogram_quantile(0.95, sum(rate(coherence_api_request_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, endpoint))',
                description="95th percentile API request latency by endpoint",
                unit="s",
                x=0,
                y=8,
                width=12,
                height=8,
                panel_id=3,
            )
        )

        # Database Query Latency
        panels.append(
            self._create_time_series_panel(
                title="Database Query Latency (p95)",
                query='histogram_quantile(0.95, sum(rate(coherence_database_query_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, operation))',
                description="95th percentile database query latency by operation",
                unit="s",
                x=12,
                y=8,
                width=12,
                height=8,
                panel_id=4,
            )
        )

        # Active Conversations
        panels.append(
            self._create_time_series_panel(
                title="Active Conversations",
                query='sum(coherence_active_conversations{tenant_id=~"$tenant_id"}) by (tenant_id)',
                description="Number of active conversations by tenant",
                unit="conversations",
                x=0,
                y=16,
                width=8,
                height=8,
                panel_id=5,
            )
        )

        # Cache Hit Ratio
        panels.append(
            self._create_time_series_panel(
                title="Cache Hit Ratio",
                query='coherence_cache_hit_ratio{tenant_id=~"$tenant_id"}',
                description="Cache hit ratio by cache type",
                unit="percentunit",
                x=8,
                y=16,
                width=8,
                height=8,
                panel_id=6,
            )
        )

        # Circuit Breaker State
        panels.append(
            self._create_time_series_panel(
                title="Circuit Breaker State",
                query='coherence_circuit_breaker_state{tenant_id=~"$tenant_id"}',
                description="Circuit breaker state (0=closed, 1=open, 2=half-open)",
                unit="state",
                x=16,
                y=16,
                width=8,
                height=8,
                panel_id=7,
            )
        )

        # Success Rate
        panels.append(
            self._create_stat_panel(
                title="Success Rate",
                query='(sum(rate(coherence_api_request_seconds_count{tenant_id=~"$tenant_id", status=~"2.."}[5m])) / sum(rate(coherence_api_request_seconds_count{tenant_id=~"$tenant_id"}[5m]))) * 100',
                description="Percentage of successful API requests (2xx status)",
                unit="percent",
                x=0,
                y=24,
                width=6,
                height=4,
                panel_id=8,
                thresholds=[
                    {"color": "red", "value": None},
                    {"color": "orange", "value": 90},
                    {"color": "green", "value": 99},
                ],
            )
        )

        # Total Error Count
        panels.append(
            self._create_stat_panel(
                title="Error Count (5m)",
                query='sum(increase(coherence_error_total{tenant_id=~"$tenant_id"}[5m]))',
                description="Total number of errors in the last 5 minutes",
                unit="errors",
                x=6,
                y=24,
                width=6,
                height=4,
                panel_id=9,
                thresholds=[
                    {"color": "green", "value": None},
                    {"color": "orange", "value": 10},
                    {"color": "red", "value": 50},
                ],
            )
        )

        # Intent Resolution Success
        panels.append(
            self._create_stat_panel(
                title="Intent Resolution Success",
                query='sum(rate(coherence_intent_resolution_total{tenant_id=~"$tenant_id", result="success"}[5m])) / sum(rate(coherence_intent_resolution_total{tenant_id=~"$tenant_id"}[5m])) * 100',
                description="Percentage of successful intent resolutions",
                unit="percent",
                x=12,
                y=24,
                width=6,
                height=4,
                panel_id=10,
                thresholds=[
                    {"color": "red", "value": None},
                    {"color": "orange", "value": 80},
                    {"color": "green", "value": 95},
                ],
            )
        )

        # Parameter Extraction Success
        panels.append(
            self._create_stat_panel(
                title="Parameter Extraction Success",
                query='sum(rate(coherence_parameter_extraction_success_total{tenant_id=~"$tenant_id"}[5m])) / (sum(rate(coherence_parameter_extraction_success_total{tenant_id=~"$tenant_id"}[5m])) + sum(rate(coherence_parameter_extraction_failure_total{tenant_id=~"$tenant_id"}[5m]))) * 100',
                description="Percentage of successful parameter extractions",
                unit="percent",
                x=18,
                y=24,
                width=6,
                height=4,
                panel_id=11,
                thresholds=[
                    {"color": "red", "value": None},
                    {"color": "orange", "value": 85},
                    {"color": "green", "value": 95},
                ],
            )
        )

        dashboard["panels"] = panels
        return dashboard

    def generate_error_dashboard(self) -> Dict[str, Any]:
        """
        Generate an error monitoring dashboard.

        Returns:
            Dashboard configuration
        """
        dashboard = self.base_dashboard.copy()
        dashboard["title"] = "Coherence - Error Monitoring"
        dashboard["uid"] = "coherence-error-monitoring"

        panels = []

        # Error Count by Type
        panels.append(
            self._create_time_series_panel(
                title="Error Count by Type",
                query='sum(rate(coherence_error_total{tenant_id=~"$tenant_id"}[5m])) by (error_type)',
                description="Number of errors per second by error type",
                unit="errors/s",
                x=0,
                y=0,
                width=12,
                height=8,
                panel_id=1,
            )
        )

        # Error Count by Component
        panels.append(
            self._create_time_series_panel(
                title="Error Count by Component",
                query='sum(rate(coherence_error_total{tenant_id=~"$tenant_id"}[5m])) by (component)',
                description="Number of errors per second by component",
                unit="errors/s",
                x=12,
                y=0,
                width=12,
                height=8,
                panel_id=2,
            )
        )

        # Error Count by Endpoint
        panels.append(
            self._create_time_series_panel(
                title="Error Count by Endpoint",
                query='sum(rate(coherence_error_by_endpoint_total{tenant_id=~"$tenant_id"}[5m])) by (endpoint)',
                description="Number of errors per second by API endpoint",
                unit="errors/s",
                x=0,
                y=8,
                width=12,
                height=8,
                panel_id=3,
            )
        )

        # Error Count by Status Code
        panels.append(
            self._create_time_series_panel(
                title="Error Count by Status Code",
                query='sum(rate(coherence_error_by_endpoint_total{tenant_id=~"$tenant_id"}[5m])) by (status_code)',
                description="Number of errors per second by HTTP status code",
                unit="errors/s",
                x=12,
                y=8,
                width=12,
                height=8,
                panel_id=4,
            )
        )

        # Fallback Strategy Usage
        panels.append(
            self._create_time_series_panel(
                title="Fallback Strategy Usage",
                query='sum(rate(coherence_fallback_strategy_usage_total{tenant_id=~"$tenant_id"}[5m])) by (strategy)',
                description="Number of fallback strategy activations per second by strategy",
                unit="activations/s",
                x=0,
                y=16,
                width=12,
                height=8,
                panel_id=5,
            )
        )

        # Fallback Strategy Success Rate
        panels.append(
            self._create_time_series_panel(
                title="Fallback Strategy Success Rate",
                query='sum(rate(coherence_fallback_strategy_success_total{tenant_id=~"$tenant_id"}[5m])) by (strategy) / sum(rate(coherence_fallback_strategy_usage_total{tenant_id=~"$tenant_id"}[5m])) by (strategy)',
                description="Success rate of fallback strategies by strategy",
                unit="percentunit",
                x=12,
                y=16,
                width=12,
                height=8,
                panel_id=6,
            )
        )

        # Circuit Breaker Trips
        panels.append(
            self._create_time_series_panel(
                title="Circuit Breaker Trips",
                query='sum(rate(coherence_circuit_breaker_trips_total{tenant_id=~"$tenant_id"}[5m])) by (service, circuit_breaker)',
                description="Number of circuit breaker trips per second by service and circuit breaker",
                unit="trips/s",
                x=0,
                y=24,
                width=12,
                height=8,
                panel_id=7,
            )
        )

        # Retry Attempts
        panels.append(
            self._create_time_series_panel(
                title="Retry Attempts",
                query='sum(rate(coherence_request_retry_attempts_total{tenant_id=~"$tenant_id"}[5m])) by (service, operation)',
                description="Number of retry attempts per second by service and operation",
                unit="retries/s",
                x=12,
                y=24,
                width=12,
                height=8,
                panel_id=8,
            )
        )

        # Error Distribution by Type
        panels.append(
            self._create_pie_panel(
                title="Error Distribution by Type",
                query='sum(increase(coherence_error_total{tenant_id=~"$tenant_id"}[5m])) by (error_type)',
                description="Distribution of errors by error type in the last 5 minutes",
                x=0,
                y=32,
                width=8,
                height=8,
                panel_id=9,
            )
        )

        # Error Distribution by Component
        panels.append(
            self._create_pie_panel(
                title="Error Distribution by Component",
                query='sum(increase(coherence_error_total{tenant_id=~"$tenant_id"}[5m])) by (component)',
                description="Distribution of errors by component in the last 5 minutes",
                x=8,
                y=32,
                width=8,
                height=8,
                panel_id=10,
            )
        )

        # Error Distribution by Endpoint
        panels.append(
            self._create_pie_panel(
                title="Error Distribution by Endpoint",
                query='sum(increase(coherence_error_by_endpoint_total{tenant_id=~"$tenant_id"}[5m])) by (endpoint)',
                description="Distribution of errors by API endpoint in the last 5 minutes",
                x=16,
                y=32,
                width=8,
                height=8,
                panel_id=11,
            )
        )

        dashboard["panels"] = panels
        return dashboard

    def generate_tenant_usage_dashboard(self) -> Dict[str, Any]:
        """
        Generate a tenant usage dashboard.

        Returns:
            Dashboard configuration
        """
        dashboard = self.base_dashboard.copy()
        dashboard["title"] = "Coherence - Tenant Usage"
        dashboard["uid"] = "coherence-tenant-usage"

        panels = []

        # Request Rate by Tenant
        panels.append(
            self._create_time_series_panel(
                title="Request Rate by Tenant",
                query='sum(rate(coherence_api_request_seconds_count{tenant_id=~"$tenant_id"}[5m])) by (tenant_id)',
                description="Number of API requests per second by tenant",
                unit="reqps",
                x=0,
                y=0,
                width=12,
                height=8,
                panel_id=1,
            )
        )

        # Error Rate by Tenant
        panels.append(
            self._create_time_series_panel(
                title="Error Rate by Tenant",
                query='sum(rate(coherence_error_by_type_total{tenant_id=~"$tenant_id"}[5m])) by (tenant_id)',
                description="Number of errors per second by tenant",
                unit="errors/s",
                x=12,
                y=0,
                width=12,
                height=8,
                panel_id=2,
            )
        )

        # Active Conversations by Tenant
        panels.append(
            self._create_time_series_panel(
                title="Active Conversations by Tenant",
                query='sum(coherence_active_conversations{tenant_id=~"$tenant_id"}) by (tenant_id)',
                description="Number of active conversations by tenant",
                unit="conversations",
                x=0,
                y=8,
                width=12,
                height=8,
                panel_id=3,
            )
        )

        # Intent Resolution Success by Tenant
        panels.append(
            self._create_time_series_panel(
                title="Intent Resolution Success by Tenant",
                query='sum(rate(coherence_intent_resolution_total{tenant_id=~"$tenant_id", result="success"}[5m])) by (tenant_id) / sum(rate(coherence_intent_resolution_total{tenant_id=~"$tenant_id"}[5m])) by (tenant_id)',
                description="Success rate of intent resolutions by tenant",
                unit="percentunit",
                x=12,
                y=8,
                width=12,
                height=8,
                panel_id=4,
            )
        )

        # LLM Token Usage by Tenant
        panels.append(
            self._create_time_series_panel(
                title="LLM Token Usage by Tenant",
                query='sum(rate(coherence_llm_token_usage_total{tenant_id=~"$tenant_id"}[5m])) by (tenant_id)',
                description="Number of LLM tokens used per second by tenant",
                unit="tokens/s",
                x=0,
                y=16,
                width=12,
                height=8,
                panel_id=5,
            )
        )

        # Average Conversation Turns by Tenant
        panels.append(
            self._create_time_series_panel(
                title="Average Conversation Turns by Tenant",
                query='avg(coherence_conversation_turns_sum{tenant_id=~"$tenant_id"}) by (tenant_id) / avg(coherence_conversation_turns_count{tenant_id=~"$tenant_id"}) by (tenant_id)',
                description="Average number of turns per conversation by tenant",
                unit="turns",
                x=12,
                y=16,
                width=12,
                height=8,
                panel_id=6,
            )
        )

        # Vector Index Size by Tenant
        panels.append(
            self._create_time_series_panel(
                title="Vector Index Size by Tenant",
                query='sum(coherence_vector_index_size{tenant_id=~"$tenant_id"}) by (tenant_id, collection)',
                description="Number of vectors in each collection by tenant",
                unit="vectors",
                x=0,
                y=24,
                width=12,
                height=8,
                panel_id=7,
            )
        )

        # Average Conversation Duration by Tenant
        panels.append(
            self._create_time_series_panel(
                title="Average Conversation Duration by Tenant",
                query='avg(coherence_conversation_duration_seconds_sum{tenant_id=~"$tenant_id"}) by (tenant_id) / avg(coherence_conversation_duration_seconds_count{tenant_id=~"$tenant_id"}) by (tenant_id)',
                description="Average duration of conversations by tenant",
                unit="s",
                x=12,
                y=24,
                width=12,
                height=8,
                panel_id=8,
            )
        )

        # Request Distribution by Tenant
        panels.append(
            self._create_pie_panel(
                title="Request Distribution by Tenant",
                query='sum(increase(coherence_api_request_seconds_count{tenant_id=~"$tenant_id"}[5m])) by (tenant_id)',
                description="Distribution of requests by tenant in the last 5 minutes",
                x=0,
                y=32,
                width=8,
                height=8,
                panel_id=9,
            )
        )

        # LLM Token Usage Distribution by Tenant
        panels.append(
            self._create_pie_panel(
                title="LLM Token Distribution by Tenant",
                query='sum(increase(coherence_llm_token_usage_total{tenant_id=~"$tenant_id"}[5m])) by (tenant_id)',
                description="Distribution of LLM token usage by tenant in the last 5 minutes",
                x=8,
                y=32,
                width=8,
                height=8,
                panel_id=10,
            )
        )

        # Error Distribution by Tenant
        panels.append(
            self._create_pie_panel(
                title="Error Distribution by Tenant",
                query='sum(increase(coherence_error_by_type_total{tenant_id=~"$tenant_id"}[5m])) by (tenant_id)',
                description="Distribution of errors by tenant in the last 5 minutes",
                x=16,
                y=32,
                width=8,
                height=8,
                panel_id=11,
            )
        )

        dashboard["panels"] = panels
        return dashboard

    def generate_performance_dashboard(self) -> Dict[str, Any]:
        """
        Generate a performance monitoring dashboard.

        Returns:
            Dashboard configuration
        """
        dashboard = self.base_dashboard.copy()
        dashboard["title"] = "Coherence - Performance"
        dashboard["uid"] = "coherence-performance"

        panels = []

        # API Request Latency (p50, p95, p99)
        panels.append(
            self._create_time_series_panel(
                title="API Request Latency",
                query='histogram_quantile(0.50, sum(rate(coherence_api_request_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, endpoint))',
                description="API request latency percentiles by endpoint",
                unit="s",
                x=0,
                y=0,
                width=12,
                height=8,
                panel_id=1,
            )
        )

        # Intent Resolution Latency by Tier
        panels.append(
            self._create_time_series_panel(
                title="Intent Resolution Latency by Tier",
                query='histogram_quantile(0.95, sum(rate(coherence_intent_resolution_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, tier))',
                description="95th percentile intent resolution latency by tier",
                unit="s",
                x=12,
                y=0,
                width=12,
                height=8,
                panel_id=2,
            )
        )

        # LLM Call Latency by Model
        panels.append(
            self._create_time_series_panel(
                title="LLM Call Latency by Model",
                query='histogram_quantile(0.95, sum(rate(coherence_llm_call_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, model))',
                description="95th percentile LLM call latency by model",
                unit="s",
                x=0,
                y=8,
                width=12,
                height=8,
                panel_id=3,
            )
        )

        # Parameter Extraction Latency by Method
        panels.append(
            self._create_time_series_panel(
                title="Parameter Extraction Latency by Method",
                query='histogram_quantile(0.95, sum(rate(coherence_parameter_extraction_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, method))',
                description="95th percentile parameter extraction latency by method",
                unit="s",
                x=12,
                y=8,
                width=12,
                height=8,
                panel_id=4,
            )
        )

        # Action Execution Latency by Intent
        panels.append(
            self._create_time_series_panel(
                title="Action Execution Latency by Intent",
                query='histogram_quantile(0.95, sum(rate(coherence_action_execution_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, intent))',
                description="95th percentile action execution latency by intent",
                unit="s",
                x=0,
                y=16,
                width=12,
                height=8,
                panel_id=5,
            )
        )

        # Database Query Latency by Operation
        panels.append(
            self._create_time_series_panel(
                title="Database Query Latency by Operation",
                query='histogram_quantile(0.95, sum(rate(coherence_database_query_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, operation))',
                description="95th percentile database query latency by operation",
                unit="s",
                x=12,
                y=16,
                width=12,
                height=8,
                panel_id=6,
            )
        )

        # Vector Search Latency by Collection
        panels.append(
            self._create_time_series_panel(
                title="Vector Search Latency by Collection",
                query='histogram_quantile(0.95, sum(rate(coherence_vector_search_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, collection))',
                description="95th percentile vector search latency by collection",
                unit="s",
                x=0,
                y=24,
                width=12,
                height=8,
                panel_id=7,
            )
        )

        # Embedding Call Latency by Model
        panels.append(
            self._create_time_series_panel(
                title="Embedding Call Latency by Model",
                query='histogram_quantile(0.95, sum(rate(coherence_embedding_call_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, model))',
                description="95th percentile embedding call latency by model",
                unit="s",
                x=12,
                y=24,
                width=12,
                height=8,
                panel_id=8,
            )
        )

        # Endpoint Performance Ranking (95th Percentile Latency)
        panels.append(
            self._create_stat_panel(
                title="Slowest Endpoints (p95 Latency)",
                query='topk(5, histogram_quantile(0.95, sum(rate(coherence_api_request_seconds_bucket{tenant_id=~"$tenant_id"}[5m])) by (le, endpoint)))',
                description="Top 5 slowest endpoints by 95th percentile latency",
                unit="s",
                x=0,
                y=32,
                width=12,
                height=8,
                panel_id=9,
            )
        )

        # LLM Token Usage Rate
        panels.append(
            self._create_time_series_panel(
                title="LLM Token Usage Rate",
                query='sum(rate(coherence_llm_token_usage_total{tenant_id=~"$tenant_id"}[5m])) by (model, operation)',
                description="LLM token usage rate by model and operation",
                unit="tokens/s",
                x=12,
                y=32,
                width=12,
                height=8,
                panel_id=10,
            )
        )

        dashboard["panels"] = panels
        return dashboard

    def save_dashboard(self, dashboard: Dict[str, Any], filename: str) -> None:
        """
        Save a dashboard to a file.

        Args:
            dashboard: Dashboard configuration
            filename: Output filename
        """
        try:
            # Ensure the output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Save the dashboard to a file
            filepath = os.path.join(self.output_dir, filename)
            with open(filepath, "w") as f:
                json.dump(dashboard, f, indent=2)

            logger.info(f"Dashboard saved to {filepath}")
        except Exception as e:
            logger.error(f"Error saving dashboard to {filename}: {e}")
            raise

    def generate_all_dashboards(self) -> None:
        """
        Generate and save all dashboards.
        """
        # System health dashboard
        system_health = self.generate_system_health_dashboard()
        self.save_dashboard(system_health, "coherence-system-health.json")

        # Error monitoring dashboard
        error_dashboard = self.generate_error_dashboard()
        self.save_dashboard(error_dashboard, "coherence-error-monitoring.json")

        # Tenant usage dashboard
        tenant_usage = self.generate_tenant_usage_dashboard()
        self.save_dashboard(tenant_usage, "coherence-tenant-usage.json")

        # Performance dashboard
        performance = self.generate_performance_dashboard()
        self.save_dashboard(performance, "coherence-performance.json")
