"""
Monitoring Module

This module provides utilities for monitoring the Coherence application,
including metrics collection, dashboard generation, and middleware for
automatically recording metrics.
"""

from src.coherence.monitoring.dashboard_generator import DashboardGenerator
from src.coherence.monitoring.metrics_collector import TenantMetricsCollector
from src.coherence.monitoring.middleware import MetricsMiddleware

__all__ = [
    "DashboardGenerator",
    "TenantMetricsCollector",
    "MetricsMiddleware",
]
