"""
Tenant-Specific Metrics Collector

This module provides utilities for collecting metrics with tenant isolation,
ensuring that metrics can be filtered and analyzed by tenant.
"""

import logging
from typing import Optional

from starlette.requests import Request

from src.coherence.core.metrics import (
    ACTION_EXECUTION_COUNT,
    ACTION_EXECUTION_LATENCY,
    ACTIVE_CONVERSATIONS,
    API_REQUEST_LATENCY,
    CACHE_HIT_RATIO,
    CIRCUIT_BREAKER_STATE,
    CIRCUIT_BREAKER_TRIPS,
    CONVERSATION_DURATION,
    CONVERSATION_TURNS,
    ERROR_BY_ENDPOINT,
    ERROR_BY_TYPE,
    ERROR_COUNT,
    ERROR_RATE,
    FALLBACK_STRATEGY_FAILURE,
    FALLBACK_STRATEGY_SUCCESS,
    FALLBACK_STRATEGY_USAGE,
    INTENT_CONFIDENCE,
    INTENT_RESOLUTION_COUNT,
    INTENT_RESOLUTION_LATENCY,
    LLM_CALL_LATENCY,
    LLM_TOKEN_USAGE,
    PARAMETER_EXTRACTION_FAILURE,
    PARAMETER_EXTRACTION_LATENCY,
    PARAMETER_EXTRACTION_SUCCESS,
    REQUEST_RETRY_ATTEMPTS,
    VECTOR_INDEX_SIZE,
    VECTOR_SEARCH_LATENCY,
)

logger = logging.getLogger(__name__)

DEFAULT_TENANT_ID = "00000000-0000-0000-0000-000000000000"


class TenantMetricsCollector:
    """
    Metrics collector with tenant isolation.

    This class provides methods for recording metrics with tenant context,
    ensuring that metrics can be filtered and analyzed by tenant.
    """

    def __init__(self, tenant_id: Optional[str] = None):
        """
        Initialize the metrics collector.

        Args:
            tenant_id: Optional tenant ID to use for all metrics
        """
        self.tenant_id = tenant_id or DEFAULT_TENANT_ID

    @classmethod
    def from_request(cls, request: Request) -> "TenantMetricsCollector":
        """
        Create a metrics collector from a FastAPI request.

        This method extracts the tenant ID from the request state
        to ensure metrics are properly attributed to the tenant.

        Args:
            request: The FastAPI request

        Returns:
            A metrics collector with the tenant ID from the request
        """
        tenant_id = None

        # Try to get tenant ID from request state
        if hasattr(request.state, "tenant_id"):
            tenant_id = str(request.state.tenant_id)

        return cls(tenant_id=tenant_id)

    def record_intent_resolution(
        self, latency: float, result: str, tier: str, confidence: Optional[float] = None
    ) -> None:
        """
        Record intent resolution metrics with tenant context.

        Args:
            latency: Time taken to resolve the intent (in seconds)
            result: Resolution result ("success", "failure", "timeout", etc.)
            tier: Resolution tier ("vector", "llm", "rag")
            confidence: Optional confidence score (0.0 to 1.0)
        """
        # Record intent resolution count
        INTENT_RESOLUTION_COUNT.labels(tenant_id=self.tenant_id, result=result).inc()

        # Record latency
        INTENT_RESOLUTION_LATENCY.labels(tier=tier, result=result).observe(latency)

        # Record confidence if provided
        if confidence is not None:
            INTENT_CONFIDENCE.labels(tier=tier).observe(confidence)

    def record_parameter_extraction(
        self,
        latency: float,
        method: str,
        success: bool = True,
        param_type: Optional[str] = None,
        failure_reason: Optional[str] = None,
    ) -> None:
        """
        Record parameter extraction metrics with tenant context.

        Args:
            latency: Time taken to extract parameters (in seconds)
            method: Extraction method ("pattern", "llm", etc.)
            success: Whether extraction was successful
            param_type: Type of parameter extracted
            failure_reason: Reason for failure if not successful
        """
        # Record latency
        PARAMETER_EXTRACTION_LATENCY.labels(method=method).observe(latency)

        # Record success or failure
        if success:
            PARAMETER_EXTRACTION_SUCCESS.labels(
                param_type=param_type or "unknown", method=method
            ).inc()
        else:
            PARAMETER_EXTRACTION_FAILURE.labels(
                param_type=param_type or "unknown",
                method=method,
                reason=failure_reason or "unknown",
            ).inc()

    def record_action_execution(self, latency: float, intent: str, result: str) -> None:
        """
        Record action execution metrics with tenant context.

        Args:
            latency: Time taken to execute the action (in seconds)
            intent: Intent that triggered the action
            result: Execution result ("success", "failure", etc.)
        """
        # Record count
        ACTION_EXECUTION_COUNT.labels(intent=intent, result=result).inc()

        # Record latency
        ACTION_EXECUTION_LATENCY.labels(intent=intent, result=result).observe(latency)

    def record_conversation_metrics(self, turns: int, duration: float) -> None:
        """
        Record conversation metrics with tenant context.

        Args:
            turns: Number of turns in the conversation
            duration: Duration of the conversation in seconds
        """
        CONVERSATION_TURNS.labels(tenant_id=self.tenant_id).observe(turns)
        CONVERSATION_DURATION.labels(tenant_id=self.tenant_id).observe(duration)

    def update_active_conversations(self, count: int) -> None:
        """
        Update the count of active conversations for a tenant.

        Args:
            count: Number of active conversations
        """
        ACTIVE_CONVERSATIONS.labels(tenant_id=self.tenant_id).set(count)

    def record_llm_usage(
        self,
        latency: float,
        provider: str,
        model: str,
        operation: str,
        token_count: int,
    ) -> None:
        """
        Record LLM usage metrics with tenant context.

        Args:
            latency: Time taken for the LLM call (in seconds)
            provider: LLM provider name
            model: LLM model name
            operation: Operation type ("completion", "embedding", etc.)
            token_count: Number of tokens used
        """
        # Record latency
        LLM_CALL_LATENCY.labels(
            provider=provider, model=model, operation=operation
        ).observe(latency)

        # Record token usage
        LLM_TOKEN_USAGE.labels(
            tenant_id=self.tenant_id,
            provider=provider,
            model=model,
            operation=operation,
        ).inc(token_count)

    def record_vector_search(self, latency: float, collection: str) -> None:
        """
        Record vector search metrics with tenant context.

        Args:
            latency: Time taken for the vector search (in seconds)
            collection: Vector collection name
        """
        VECTOR_SEARCH_LATENCY.labels(collection=collection).observe(latency)

    def update_vector_index_size(self, size: int, collection: str) -> None:
        """
        Update the size of a vector index for a tenant.

        Args:
            size: Number of vectors in the collection
            collection: Vector collection name
        """
        VECTOR_INDEX_SIZE.labels(tenant_id=self.tenant_id, collection=collection).set(
            size
        )

    def record_api_request(
        self,
        latency: float,
        endpoint: str,
        method: str,
        status: int,
        tenant_id: Optional[str] = None,
        request_id: Optional[str] = None,
    ) -> None:
        """
        Record API request metrics with tenant context.

        Args:
            latency: Time taken to process the request (in seconds)
            endpoint: API endpoint
            method: HTTP method
            status: HTTP status code
            tenant_id: Tenant ID (overrides the one in the collector)
            request_id: Request ID for tracing
        """
        # Use provided tenant_id if given, otherwise use the one from the collector
        tenant = tenant_id or self.tenant_id

        API_REQUEST_LATENCY.labels(
            endpoint=endpoint, method=method, status=str(status), tenant_id=tenant
        ).observe(latency)

    def record_cache_hit_ratio(self, ratio: float, cache_type: str) -> None:
        """
        Record cache hit ratio metrics with tenant context.

        Args:
            ratio: Cache hit ratio (0.0 to 1.0)
            cache_type: Type of cache ("intent", "vector", etc.)
        """
        CACHE_HIT_RATIO.labels(cache_type=cache_type).set(ratio)

    def record_error(
        self,
        error_type: str,
        component: str,
        endpoint: Optional[str] = None,
        status_code: int = 500,
        error_code: Optional[str] = None,
        tenant_id: Optional[str] = None,
        request_id: Optional[str] = None,
    ) -> None:
        """
        Record error metrics with tenant context.

        Args:
            error_type: Type of error
            component: Component where the error occurred
            endpoint: API endpoint where the error occurred
            status_code: HTTP status code
            error_code: Error code string
            tenant_id: Tenant ID (overrides the one in the collector)
            request_id: Request ID for tracing
        """
        # Use provided tenant_id if given, otherwise use the one from the collector
        tenant = tenant_id or self.tenant_id

        # Increment general error counter
        ERROR_COUNT.labels(
            component=component, error_type=error_type, tenant_id=tenant
        ).inc()

        # Update error rate
        ERROR_RATE.labels(component=component, tenant_id=tenant).observe(1.0)

        # Record endpoint-specific error if endpoint is provided
        if endpoint:
            method = "unknown"
            if "/" in endpoint and endpoint.startswith("/"):
                parts = endpoint.split("/")
                if len(parts) > 2:
                    # Extract method from URL pattern like /v1/resolve
                    method = parts[2] if len(parts) > 2 else parts[1]

            ERROR_BY_ENDPOINT.labels(
                endpoint=endpoint,
                method=method,
                status_code=status_code,
                tenant_id=tenant,
            ).inc()

        # Record error by type if error_code is provided
        if error_code:
            ERROR_BY_TYPE.labels(
                error_code=error_code,
                tenant_id=tenant,
            ).inc()

    def record_fallback_usage(
        self,
        strategy: str,
        component: str,
        success: bool = True,
    ) -> None:
        """
        Record fallback strategy metrics with tenant context.

        Args:
            strategy: Name of the fallback strategy
            component: Component where the fallback was used
            success: Whether the fallback succeeded
        """
        # Record general usage
        FALLBACK_STRATEGY_USAGE.labels(
            strategy=strategy,
            component=component,
        ).inc()

        # Record success or failure
        if success:
            FALLBACK_STRATEGY_SUCCESS.labels(
                strategy=strategy,
                component=component,
            ).inc()
        else:
            FALLBACK_STRATEGY_FAILURE.labels(
                strategy=strategy,
                component=component,
            ).inc()

    def record_circuit_breaker_state(
        self,
        service: str,
        circuit_breaker: str,
        state: str,
    ) -> None:
        """
        Record circuit breaker state metrics with tenant context.

        Args:
            service: Name of the service
            circuit_breaker: Name of the circuit breaker
            state: State of the circuit breaker (closed, open, half-open)
        """
        # Map state string to numeric value
        state_value = 0  # default: closed
        if state == "open":
            state_value = 1
        elif state == "half-open":
            state_value = 2

        CIRCUIT_BREAKER_STATE.labels(
            service=service,
            circuit_breaker=circuit_breaker,
        ).set(state_value)

        # If state changed to open, increment the trip counter
        if state == "open":
            CIRCUIT_BREAKER_TRIPS.labels(
                service=service,
                circuit_breaker=circuit_breaker,
            ).inc()

    def record_retry_attempt(
        self,
        service: str,
        operation: str,
    ) -> None:
        """
        Record retry attempt metrics with tenant context.

        Args:
            service: Name of the service
            operation: Operation being retried
        """
        REQUEST_RETRY_ATTEMPTS.labels(
            service=service,
            operation=operation,
        ).inc()
