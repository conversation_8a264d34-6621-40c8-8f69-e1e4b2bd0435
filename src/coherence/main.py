"""
Coherence - AI Middleware for Converting Natural Language to Actions

This module contains the FastAPI application entry point and configuration.
"""

import datetime
import logging
import os
import sys
from contextlib import asynccontextmanager

import fastapi
import starlette
from clerk_backend_api import Clerk
from fastapi import FastAPI, Response
from fastapi.middleware.cors import CORSMiddleware
from prometheus_client import CONTENT_TYPE_LATEST, generate_latest

from src.coherence.api.v1.api import api_router
from src.coherence.api.v1.endpoints import admin  # Import admin router directly
from src.coherence.core.config import settings
from src.coherence.core.errors import register_exception_handlers
from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.metrics import (
    METRICS_UP,  # Import from the centralized metrics module
)
from src.coherence.core.qdrant_client import get_qdrant_client
from src.coherence.core.redis_client import get_redis_client
from src.coherence.db import base
from src.coherence.db.session import engine
from src.coherence.middleware.audit_logging import AuditLoggingMiddleware
from src.coherence.middleware.error_handling import ErrorHandlingMiddleware
from src.coherence.middleware.rate_limiting import RateLimitingMiddleware
from src.coherence.middleware.tenant_context import TenantContextMiddleware
from src.coherence.monitoring.middleware import MetricsMiddleware
from src.coherence.openapi_adapter.health_monitor import get_api_health_monitor
from src.coherence.template_system.services.template_service import TemplateService

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Context manager for FastAPI application startup and shutdown events.

    Creates database tables, initializes clients, and performs cleanup on shutdown.
    """
    # Create db tables
    base.Base.metadata.create_all(bind=engine)

    # Initialize Qdrant client singleton - store the awaitable function
    app.state.qdrant_client = get_qdrant_client
    logger.info("Set up Qdrant client function")

    # Initialize Redis client - store the awaitable function
    app.state.redis_client = get_redis_client
    logger.info("Set up Redis client function")

    # Initialize LLM provider
    app.state.openai_provider = LLMFactory.create_provider(
        name=settings.LLM_PROVIDER,
        model=settings.LLM_MODEL,
        api_key=settings.OPENAI_API_KEY,
    )
    logger.info(f"Initialized LLM provider: {settings.LLM_PROVIDER}")

    # Set up template service and start template update listener
    template_service = TemplateService()
    (
        template_listener_task,
        template_listener_stop_event,
    ) = await template_service.start_template_update_listener()
    app.state.template_service = template_service
    app.state.template_listener_task = template_listener_task
    app.state.template_listener_stop_event = template_listener_stop_event
    logger.info("Started template update listener")
    
    # Set up and start API health monitoring
    health_monitor = await get_api_health_monitor()
    await health_monitor.start_monitoring()
    app.state.health_monitor = health_monitor
    logger.info("Started API health monitoring")

    # Initialize Clerk client if key is available
    clerk_secret_key = os.environ.get("CLERK_SECRET_KEY")
    if clerk_secret_key:
        try:
            # Initialize Clerk client without api_key parameter
            app.state.clerk_client = Clerk()
            logger.info("Initialized Clerk client")
        except Exception as err:
            logger.error(f"Failed to initialize Clerk client: {err}")
            app.state.clerk_client = None
    else:
        logger.warning("CLERK_SECRET_KEY not found in environment. Authentication using Clerk will not be available.")
        app.state.clerk_client = None
    yield

    # Cleanup
    # Stop API health monitoring
    if hasattr(app.state, "health_monitor") and app.state.health_monitor is not None:
        try:
            await app.state.health_monitor.stop_monitoring()
            logger.info("Stopped API health monitoring")
        except Exception as e:
            logger.error(f"Error stopping API health monitoring: {e}")
    
    # No need to close the client functions as they are just references to the factory functions
    logger.info("No cleanup needed for client factory functions")

    if hasattr(app.state, "openai_provider") and app.state.openai_provider is not None:
        try:
            # OpenAI doesn't need explicit cleanup
            logger.info("Closed LLM provider")
        except Exception as e:
            logger.error(f"Error closing LLM provider: {e}")
            
    # Handle Clerk client cleanup
    if hasattr(app.state, "clerk_client") and app.state.clerk_client is not None:
        # ClerkSDK does not have an explicit close method.
        logger.info("Clerk client does not require explicit cleanup.")
        
    # Stop the template listener
    if (
        hasattr(app.state, "template_listener_stop_event")
        and app.state.template_listener_stop_event is not None
    ):
        try:
            # Signal the listener to stop
            app.state.template_listener_stop_event.set()
            logger.info("Stopped template update listener")
        except Exception as e:
            logger.error(f"Error stopping template update listener: {e}")


app = FastAPI(
    title="Coherence API",
    description=__doc__,
    version="0.1.0",
    lifespan=lifespan,
)

# Register custom exception handlers
register_exception_handlers(app)

# Set up metrics instrumentation
try:
    # Try to import our optional instrumentator module using a more reliable path
    import importlib.util

    # Get the absolute path to the monitoring directory
    base_dir = os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    )
    instrumentator_path = os.path.join(base_dir, "monitoring", "instrumentator.py")

    logger.info(f"Checking for instrumentator file at: {instrumentator_path}")
    # Check if the file exists
    if os.path.exists(instrumentator_path):
        logger.info(
            f"Instrumentator file found at: {instrumentator_path}. Importing and attempting to apply."
        )

        # Import the module directly from the file path
        spec = importlib.util.spec_from_file_location(
            "instrumentator", instrumentator_path
        )
        instrumentator_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(instrumentator_module)

        # Apply instrumentation
        logger.info("Calling instrumentator_module.instrument_app(app)")
        instrumentator_module.instrument_app(app)
        logger.info("Successfully applied instrumentator_module.instrument_app(app)")
    else:
        logger.warning(
            f"Instrumentator file NOT found at: {instrumentator_path}. Skipping instrumentation."
        )
except Exception as e:
    logger.warning(
        f"Could not import or apply instrumentator: {e}. Using basic metrics instead.",
        exc_info=True,
    )

# Configure middleware
# The middleware stack is built from the middlewares list
# ErrorHandlingMiddleware should be first to catch all errors
middleware_config = [
    (
        ErrorHandlingMiddleware,
        {
            "exclude_paths": ["/health", "/metrics"],
            "log_level": logging.INFO,
        },
    ),
    (
        MetricsMiddleware,
        {
            "exclude_paths": ["/health", "/metrics"],
        },
    ),
    (TenantContextMiddleware, {}),
    (
        AuditLoggingMiddleware,
        {"exclude_paths": ["/health", "/metrics", "/admin/health", "/admin/metrics"]},
    ),
    (
        RateLimitingMiddleware,
        {
            "admin_rate_limit": 30,  # 30 admin requests per minute
            "status_rate_limit": 60,  # 60 status requests per minute
            "exclude_paths": ["/health", "/metrics", "/admin/health", "/admin/metrics"],
        },
    ),
]

# Add all middleware in the correct order
for middleware_class, options in middleware_config:
    app.add_middleware(middleware_class, **options)

# IMPORTANT: Clear FastAPI's internal middleware to force rebuild
# This prevents conflicts with the instrumentator
# app.middleware_stack = None  # Removing this experimental line

# Set all CORS enabled origins - hardcode the allowed origins to avoid parsing issues
# Also add the origins from settings if they exist
origins = ["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:8001"]
try:
    # Try to get origins from settings, but don't fail if there's an issue
    if hasattr(settings, "BACKEND_CORS_ORIGINS") and settings.BACKEND_CORS_ORIGINS:
        if isinstance(settings.BACKEND_CORS_ORIGINS, list):
            for origin in settings.BACKEND_CORS_ORIGINS:
                if str(origin) not in origins:
                    origins.append(str(origin))
        elif isinstance(settings.BACKEND_CORS_ORIGINS, str):
            if settings.BACKEND_CORS_ORIGINS not in origins:
                origins.append(settings.BACKEND_CORS_ORIGINS)
except Exception as e:
    logger.warning(f"Error processing CORS origins from settings: {e}")

# Log the configured origins
logger.info(f"Configuring CORS with origins: {origins}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Content-Type",
        "X-API-Key",
        "X-Tenant-ID",
        "X-User-ID",
        "Authorization",
    ],
    expose_headers=["Content-Type", "X-API-Key"],
)

print(
    f"DEV_DIRECT_PRINT_STDERR: app.user_middleware is: {app.user_middleware}",
    file=sys.stderr,
)
print(
    f"DEV_DIRECT_PRINT_STDERR: FastAPI version: {fastapi.__version__}", file=sys.stderr
)
print(
    f"DEV_DIRECT_PRINT_STDERR: Starlette version: {starlette.__version__}",
    file=sys.stderr,
)
sys.stderr.flush()  # Ensure all debug prints are flushed


# Load and register routers from the api.v1.endpoints module
@app.get("/health")
async def health_check():
    """Basic health check endpoint."""
    # Set metrics up gauge
    METRICS_UP.set(1.0)
    return {"status": "ok"}


@app.get("/health/deep")
async def deep_health_check():
    """
    Deep health check endpoint that verifies all dependencies are functional.
    Checks database, Redis, Qdrant, and LLM provider connectivity.
    """
    health_status = {
        "status": "ok",
        "details": {
            "database": {"status": "unknown"},
            "redis": {"status": "unknown"},
            "qdrant": {"status": "unknown"},
            "llm_provider": {"status": "unknown"},
        },
        "timestamp": str(datetime.datetime.now(datetime.timezone.utc)),
    }

    # Check database connectivity
    try:
        from sqlalchemy import text

        from src.coherence.db.session import async_session

        # Create and use an async session correctly
        db = async_session()
        try:
            # Execute a simple query - properly use SQLAlchemy text()
            result = await db.execute(text("SELECT 1"))
            # Just check the result exists, don't await it
            if result:
                health_status["details"]["database"] = {"status": "ok"}
        finally:
            # Always close the session
            await db.close()
    except Exception as e:
        health_status["details"]["database"] = {"status": "error", "error": str(e)}
        health_status["status"] = "degraded"

    # Check Redis connectivity
    try:
        # Create a fresh Redis client directly
        from src.coherence.core.config import settings
        from src.coherence.core.redis_client import RedisClient

        redis_url = (
            f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"
        )
        if settings.REDIS_PASSWORD:
            redis_url = f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"

        redis_client = RedisClient(redis_url)
        # Now we can access the redis connection
        if await redis_client.redis.ping():
            health_status["details"]["redis"] = {"status": "ok"}
    except Exception as e:
        health_status["details"]["redis"] = {"status": "error", "error": str(e)}
        health_status["status"] = "degraded"

    # Check Qdrant connectivity
    try:
        # Create a fresh Qdrant client directly
        from src.coherence.core.config import settings
        from src.coherence.core.qdrant_client import QdrantClient

        qdrant_client = QdrantClient(
            host=settings.QDRANT_HOST,
            port=settings.QDRANT_PORT,
            api_key=settings.QDRANT_API_KEY,
        )

        # Use the client directly
        if qdrant_client.client:
            # Get collections using the synchronous method
            collection_list = qdrant_client.client.get_collections()
            health_status["details"]["qdrant"] = {
                "status": "ok",
                "collections": len(collection_list.collections),
            }
    except Exception as e:
        health_status["details"]["qdrant"] = {"status": "error", "error": str(e)}
        health_status["status"] = "degraded"

    # Check LLM provider
    try:
        llm_provider = app.state.openai_provider
        if llm_provider:
            # Just check if the provider exists, don't make a real call to save tokens
            provider_type = llm_provider.__class__.__name__
            if provider_type == "MockLLMProvider" and settings.ENV == "production":
                health_status["details"]["llm_provider"] = {
                    "status": "warning",
                    "provider": provider_type,
                    "message": "Using mock provider in production environment",
                }
                health_status["status"] = "degraded"
            else:
                health_status["details"]["llm_provider"] = {
                    "status": "ok",
                    "provider": provider_type,
                    "model": getattr(llm_provider, "model", "unknown"),
                }
    except Exception as e:
        health_status["details"]["llm_provider"] = {"status": "error", "error": str(e)}
        health_status["status"] = "degraded"

    return health_status


@app.get("/generate-test-metrics")
async def generate_test_metrics():
    """Generate test metrics for Prometheus."""
    import random

    from src.coherence.core.metrics import (
        ACTION_EXECUTION_COUNT,
        ACTION_EXECUTION_LATENCY,
        ERROR_BY_ENDPOINT,
        ERROR_COUNT,
        ERROR_RATE,
        INTENT_CONFIDENCE,
        INTENT_RESOLUTION_COUNT,
        INTENT_RESOLUTION_LATENCY,
        LLM_CALL_LATENCY,
        LLM_TOKEN_USAGE,
        METRICS_UP,
        REQUEST_COUNT,
        REQUEST_LATENCY,
        VECTOR_SEARCH_LATENCY,
    )

    # Log the request
    logger.info("Generating test metrics")

    # Generate random tenant IDs for testing
    tenant_ids = [
        "00000000-0000-0000-0000-000000000000",  # Default tenant
        "11111111-1111-1111-1111-111111111111",  # Test tenant 1
        "22222222-2222-2222-2222-222222222222",  # Test tenant 2
    ]

    # Generate random endpoints
    endpoints = ["/v1/resolve", "/v1/continue", "/v1/status/123", "/admin/templates"]

    # Generate random HTTP methods
    methods = ["GET", "POST", "PUT", "DELETE"]

    # Generate random status codes
    status_codes = ["200", "201", "204", "400", "401", "403", "404", "500"]

    # Intent resolution tiers
    tiers = ["vector", "llm", "rag"]

    # Set metrics up
    METRICS_UP.set(1.0)

    # Generate HTTP request metrics
    for _ in range(20):
        tenant_id = random.choice(tenant_ids)
        endpoint = random.choice(endpoints)
        method = random.choice(methods)
        status = random.choice(status_codes)
        latency = random.uniform(0.01, 2.0)

        # Record the metrics
        REQUEST_COUNT.labels(
            tenant_id=tenant_id, endpoint=endpoint, method=method, status=status
        ).inc()

        REQUEST_LATENCY.labels(
            tenant_id=tenant_id, endpoint=endpoint, method=method, status=status
        ).observe(latency)

        # Record some error metrics (for some requests)
        if random.random() < 0.3:  # 30% chance of recording an error
            ERROR_COUNT.labels(
                component="api",
                error_type=random.choice(["client_error", "server_error"]),
                tenant_id=tenant_id,
            ).inc()

            ERROR_RATE.labels(component="api", tenant_id=tenant_id).observe(1.0)

            ERROR_BY_ENDPOINT.labels(
                endpoint=endpoint,
                method=method,
                status_code=status,
                tenant_id=tenant_id,
            ).inc()

    # Generate intent resolution metrics
    for _ in range(15):
        tier = random.choice(tiers)
        result = random.choice(["success", "failure", "timeout"])
        latency = random.uniform(0.1, 3.0)
        confidence = random.uniform(0.5, 1.0)
        tenant_id = random.choice(tenant_ids)

        INTENT_RESOLUTION_COUNT.labels(tenant_id=tenant_id, result=result).inc()

        INTENT_RESOLUTION_LATENCY.labels(tier=tier, result=result).observe(latency)

        INTENT_CONFIDENCE.labels(tier=tier).observe(confidence)

    # Generate action execution metrics
    for _ in range(10):
        intent = random.choice(["get_weather", "book_appointment", "check_balance"])
        result = random.choice(["success", "failure"])
        latency = random.uniform(0.2, 2.5)

        ACTION_EXECUTION_COUNT.labels(intent=intent, result=result).inc()

        ACTION_EXECUTION_LATENCY.labels(intent=intent, result=result).observe(latency)

    # Generate LLM metrics
    for _ in range(8):
        provider = random.choice(["openai", "anthropic", "mock"])
        model = random.choice(["gpt-4", "gpt-3.5-turbo", "claude-3"])
        operation = random.choice(["completion", "embedding"])
        latency = random.uniform(0.5, 4.0)
        tokens = random.randint(100, 2000)
        tenant_id = random.choice(tenant_ids)

        LLM_CALL_LATENCY.labels(
            provider=provider, model=model, operation=operation
        ).observe(latency)

        LLM_TOKEN_USAGE.labels(
            tenant_id=tenant_id, provider=provider, model=model, operation=operation
        ).inc(tokens)

    # Generate vector search metrics
    for _ in range(5):
        collection = random.choice(["intents", "embeddings", "parameters"])
        latency = random.uniform(0.001, 0.1)

        VECTOR_SEARCH_LATENCY.labels(collection=collection).observe(latency)

    return {"message": "Generated comprehensive test metrics for all categories"}


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint."""
    # Add a log entry to confirm this endpoint is being called
    logger.info("Metrics endpoint was called")

    # Update the health check metric
    METRICS_UP.set(1.0)

    # Generate the metrics response
    try:
        content = generate_latest()
        logger.debug(f"Generated metrics content size: {len(content)} bytes")
        return Response(content=content, media_type=CONTENT_TYPE_LATEST)
    except Exception as e:
        logger.error(f"Error generating metrics: {e}")
        return Response(content=f"Error generating metrics: {str(e)}", status_code=500)



# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

# Include admin router directly with /api prefix
app.include_router(admin.router, prefix="/api", tags=["admin"])


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("src.coherence.main:app", host="0.0.0.0", port=8000, reload=True)
