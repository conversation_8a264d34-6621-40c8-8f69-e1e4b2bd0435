"""
API Health Monitoring System

This module provides functionality for monitoring the health of integrated APIs,
tracking their status, and providing alerts when status changes occur.

Key features:
1. Regular health checks of integrated APIs
2. Status tracking (healthy, degraded, down)
3. Performance metrics collection
4. Alerting for status changes
5. Integration with circuit breaker
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

import httpx
from pydantic import BaseModel, Field

from src.coherence.core.redis_client import RedisClient, get_redis_client
from src.coherence.monitoring.metrics_collector import TenantMetricsCollector

logger = logging.getLogger(__name__)

# Define API health status enum
class ApiStatus(str, Enum):
    """Enumeration of possible API health statuses."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    DOWN = "down"
    UNKNOWN = "unknown"


class HealthCheckConfig(BaseModel):
    """Configuration for an API health check."""
    api_key: str
    endpoint: str
    method: str = "GET"
    headers: Dict[str, str] = Field(default_factory=dict)
    body: Optional[Dict[str, Any]] = None
    expected_status: int = 200
    expected_response_pattern: Optional[str] = None
    timeout_ms: int = 5000
    check_interval_seconds: int = 60
    consecutive_failures_threshold: int = 3
    consecutive_successes_for_recovery: int = 2


class HealthCheckResult(BaseModel):
    """Result of an API health check."""
    api_key: str
    timestamp: datetime
    status: ApiStatus
    response_time_ms: float
    status_code: Optional[int] = None
    error_message: Optional[str] = None
    consecutive_failures: int = 0
    consecutive_successes: int = 0


class ApiHealthMonitor:
    """
    Monitor the health of integrated APIs.
    
    This class provides functionality to:
    1. Register health checks for APIs
    2. Run regular health checks in the background
    3. Track API health status
    4. Generate alerts when API status changes
    5. Integrate with metrics and circuit breaker
    
    It supports multi-tenant isolation and configurable check intervals.
    """
    
    def __init__(
        self,
        redis_client: RedisClient,
        metrics_collector: Optional[TenantMetricsCollector] = None,
        http_client: Optional[httpx.AsyncClient] = None,
        status_ttl_days: int = 30,
        namespace: str = "api_health"
    ):
        """Initialize the API health monitor.
        
        Args:
            redis_client: Redis client for storing health check results
            metrics_collector: Optional metrics collector
            http_client: Optional HTTP client for making health check requests
            status_ttl_days: Number of days to retain status history
            namespace: Namespace prefix for Redis keys
        """
        self.redis = redis_client
        self.metrics = metrics_collector or TenantMetricsCollector()
        self.http_client = http_client or httpx.AsyncClient()
        self.status_ttl_days = status_ttl_days
        self.namespace = namespace
        self.health_checks: Dict[str, HealthCheckConfig] = {}
        self.last_status: Dict[str, ApiStatus] = {}
        self.is_running = False
        self.status_change_callbacks: List[Callable[[str, ApiStatus, ApiStatus], None]] = []
        
    async def register_health_check(
        self,
        config: HealthCheckConfig,
        tenant_id: Optional[uuid.UUID] = None
    ) -> None:
        """Register a health check for an API.
        
        Args:
            config: Health check configuration
            tenant_id: Optional tenant ID for multi-tenant isolation
        """
        check_id = self._get_check_id(config.api_key, tenant_id)
        self.health_checks[check_id] = config
        
        # Initialize status as unknown
        if check_id not in self.last_status:
            self.last_status[check_id] = ApiStatus.UNKNOWN
            
        logger.info(
            f"Registered health check for API: {config.api_key}",
            extra={
                "api_key": config.api_key,
                "endpoint": config.endpoint,
                "check_interval": config.check_interval_seconds,
                "tenant_id": str(tenant_id) if tenant_id else None
            }
        )
        
    async def deregister_health_check(
        self,
        api_key: str,
        tenant_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Deregister a health check for an API.
        
        Args:
            api_key: The API key identifier
            tenant_id: Optional tenant ID
            
        Returns:
            True if the health check was deregistered, False if not found
        """
        check_id = self._get_check_id(api_key, tenant_id)
        
        if check_id in self.health_checks:
            del self.health_checks[check_id]
            if check_id in self.last_status:
                del self.last_status[check_id]
                
            logger.info(
                f"Deregistered health check for API: {api_key}",
                extra={
                    "api_key": api_key,
                    "tenant_id": str(tenant_id) if tenant_id else None
                }
            )
            return True
        
        return False
        
    async def check_health(
        self,
        api_key: str,
        tenant_id: Optional[uuid.UUID] = None
    ) -> HealthCheckResult:
        """Check the health of an API immediately.
        
        Args:
            api_key: The API key identifier
            tenant_id: Optional tenant ID
            
        Returns:
            Health check result
            
        Raises:
            ValueError: If no health check is registered for the API
        """
        check_id = self._get_check_id(api_key, tenant_id)
        
        if check_id not in self.health_checks:
            raise ValueError(f"No health check registered for API: {api_key}")
            
        config = self.health_checks[check_id]
        previous_status = self.last_status.get(check_id, ApiStatus.UNKNOWN)
        
        return await self._perform_health_check(config, check_id, previous_status, tenant_id)
        
    async def get_status(
        self,
        api_key: str,
        tenant_id: Optional[uuid.UUID] = None
    ) -> ApiStatus:
        """Get the current health status of an API.
        
        Args:
            api_key: The API key identifier
            tenant_id: Optional tenant ID
            
        Returns:
            Current API status
        """
        check_id = self._get_check_id(api_key, tenant_id)
        
        # Check if we have the status in memory
        if check_id in self.last_status:
            return self.last_status[check_id]
            
        # Try to retrieve the last status from Redis
        status_key = f"{self.namespace}:status:{check_id}"
        stored_status = await self.redis.get(status_key)
        
        if stored_status:
            try:
                status = ApiStatus(stored_status.decode())
                # Update in-memory cache
                self.last_status[check_id] = status
                return status
            except ValueError:
                pass
                
        # Default to unknown if not found
        return ApiStatus.UNKNOWN
        
    async def get_status_history(
        self,
        api_key: str,
        tenant_id: Optional[uuid.UUID] = None,
        days: int = 7,
        limit: int = 100
    ) -> List[HealthCheckResult]:
        """Get historical health check results for an API.
        
        Args:
            api_key: The API key identifier
            tenant_id: Optional tenant ID
            days: Number of days of history to retrieve
            limit: Maximum number of results to return
            
        Returns:
            List of health check results, ordered by timestamp (newest first)
        """
        check_id = self._get_check_id(api_key, tenant_id)
        history_key = f"{self.namespace}:history:{check_id}"
        
        # Get timestamps from the sorted set
        since = datetime.now() - timedelta(days=days)
        min_score = since.timestamp()
        
        # Get entries from sorted set, newest first
        entries = await self.redis.zrevrangebyscore(
            history_key,
            min=min_score,
            max="+inf",
            start=0,
            num=limit,
            withscores=True
        )
        
        results = []
        for value, _ in entries:
            try:
                result_dict = json.loads(value)
                # Convert timestamp string to datetime
                result_dict["timestamp"] = datetime.fromisoformat(result_dict["timestamp"])
                results.append(HealthCheckResult(**result_dict))
            except (json.JSONDecodeError, KeyError) as e:
                logger.warning(f"Failed to parse health check result: {e}")
                
        return results
        
    async def register_status_change_callback(
        self,
        callback: Callable[[str, ApiStatus, ApiStatus], None]
    ) -> None:
        """Register a callback to be called when an API's status changes.
        
        Args:
            callback: Function to call when status changes. Takes arguments:
                     (api_key, old_status, new_status)
        """
        self.status_change_callbacks.append(callback)
        
    async def start_monitoring(self) -> None:
        """Start the background monitoring task for all registered APIs."""
        if self.is_running:
            logger.warning("API health monitoring is already running")
            return
            
        self.is_running = True
        
        # Create a background task
        asyncio.create_task(self._monitoring_loop())
        
        logger.info("Started API health monitoring")
        
    async def stop_monitoring(self) -> None:
        """Stop the background monitoring task."""
        self.is_running = False
        logger.info("Stopped API health monitoring")
        
    async def _monitoring_loop(self) -> None:
        """Background task for monitoring all registered APIs."""
        next_checks: Dict[str, float] = {}
        
        while self.is_running:
            current_time = time.time()
            
            # Check which APIs need to be checked
            for check_id, config in self.health_checks.items():
                # Get the next check time for this API
                next_check = next_checks.get(check_id, 0)
                
                # If it's time to check
                if current_time >= next_check:
                    # Extract tenant ID from check_id if present
                    tenant_id = None
                    if ":" in check_id:
                        api_key, tenant_id_str = check_id.split(":", 1)
                        try:
                            tenant_id = uuid.UUID(tenant_id_str)
                        except ValueError:
                            pass
                    
                    # Perform the health check
                    previous_status = self.last_status.get(check_id, ApiStatus.UNKNOWN)
                    try:
                        await self._perform_health_check(config, check_id, previous_status, tenant_id)
                        
                        # Update next check time
                        next_checks[check_id] = current_time + config.check_interval_seconds
                        
                    except Exception as e:
                        logger.exception(
                            f"Error performing health check for {config.api_key}",
                            extra={"error": str(e)}
                        )
                        # Still update next check time to avoid hammering failing APIs
                        next_checks[check_id] = current_time + config.check_interval_seconds
            
            # Sleep for a short time before the next iteration
            # This allows for more responsive stopping and new check registration
            await asyncio.sleep(1)
            
    async def _perform_health_check(
        self,
        config: HealthCheckConfig,
        check_id: str,
        previous_status: ApiStatus,
        tenant_id: Optional[uuid.UUID] = None
    ) -> HealthCheckResult:
        """Perform a health check for an API.
        
        Args:
            config: Health check configuration
            check_id: Check identifier
            previous_status: Previous API status
            tenant_id: Optional tenant ID
            
        Returns:
            Health check result
        """
        start_time = time.time()
        timeout = config.timeout_ms / 1000  # Convert to seconds
        
        # Initialize result with defaults
        result = HealthCheckResult(
            api_key=config.api_key,
            timestamp=datetime.now(),
            status=ApiStatus.UNKNOWN,
            response_time_ms=0,
            consecutive_failures=0,
            consecutive_successes=0
        )
        
        # Get failure and success counters from Redis
        counters_key = f"{self.namespace}:counters:{check_id}"
        counters = await self.redis.hgetall(counters_key)
        
        result.consecutive_failures = int(counters.get(b"failures", 0))
        result.consecutive_successes = int(counters.get(b"successes", 0))
        
        try:
            # Make the health check request
            if config.method.upper() == "GET":
                response = await self.http_client.get(
                    url=config.endpoint,
                    headers=config.headers,
                    timeout=timeout
                )
            elif config.method.upper() == "POST":
                response = await self.http_client.post(
                    url=config.endpoint,
                    headers=config.headers,
                    json=config.body,
                    timeout=timeout
                )
            else:
                # Default to a generic request method
                response = await self.http_client.request(
                    method=config.method,
                    url=config.endpoint,
                    headers=config.headers,
                    json=config.body if config.method.upper() != "GET" else None,
                    timeout=timeout
                )
                
            # Calculate response time
            end_time = time.time()
            result.response_time_ms = (end_time - start_time) * 1000
            
            # Record status code
            result.status_code = response.status_code
            
            # Check if the status code matches the expected status
            if response.status_code == config.expected_status:
                # Check response pattern if specified
                pattern_match = True
                if config.expected_response_pattern:
                    try:
                        response_text = response.text
                        pattern_match = config.expected_response_pattern in response_text
                    except Exception:
                        pattern_match = False
                        
                if pattern_match:
                    # Success
                    result.consecutive_successes += 1
                    result.consecutive_failures = 0
                    
                    # Determine status based on consecutive successes
                    if result.consecutive_successes >= config.consecutive_successes_for_recovery:
                        result.status = ApiStatus.HEALTHY
                    elif previous_status == ApiStatus.DOWN:
                        result.status = ApiStatus.DEGRADED
                    else:
                        result.status = previous_status
                else:
                    # Pattern match failure
                    result.consecutive_failures += 1
                    result.consecutive_successes = 0
                    result.error_message = "Response pattern not found"
                    
                    # Determine status based on consecutive failures
                    if result.consecutive_failures >= config.consecutive_failures_threshold:
                        result.status = ApiStatus.DOWN
                    else:
                        result.status = ApiStatus.DEGRADED
            else:
                # Status code mismatch
                result.consecutive_failures += 1
                result.consecutive_successes = 0
                result.error_message = f"Unexpected status code: {response.status_code}"
                
                # Determine status based on consecutive failures
                if result.consecutive_failures >= config.consecutive_failures_threshold:
                    result.status = ApiStatus.DOWN
                else:
                    result.status = ApiStatus.DEGRADED
                    
        except Exception as e:
            # Request failed
            end_time = time.time()
            result.response_time_ms = (end_time - start_time) * 1000
            result.consecutive_failures += 1
            result.consecutive_successes = 0
            result.error_message = str(e)
            
            # Determine status based on consecutive failures
            if result.consecutive_failures >= config.consecutive_failures_threshold:
                result.status = ApiStatus.DOWN
            else:
                result.status = ApiStatus.DEGRADED
                
        # Update counters in Redis
        await self.redis.hset(
            counters_key,
            mapping={
                "failures": result.consecutive_failures,
                "successes": result.consecutive_successes,
                "last_check": datetime.now().isoformat()
            }
        )
        
        # Store the result in history
        await self._store_result(result, check_id)
        
        # Update status if changed
        if result.status != previous_status:
            await self._update_status(result.status, previous_status, check_id, config.api_key, tenant_id)
            
        # Record metrics
        self._record_metrics(result, tenant_id)
        
        return result
        
    async def _store_result(self, result: HealthCheckResult, check_id: str) -> None:
        """Store a health check result in Redis.
        
        Args:
            result: Health check result
            check_id: Check identifier
        """
        history_key = f"{self.namespace}:history:{check_id}"
        
        # Convert result to JSON
        result_json = result.model_dump_json()
        
        # Store in sorted set with timestamp as score
        await self.redis.zadd(
            history_key,
            {result_json: result.timestamp.timestamp()}
        )
        
        # Set expiration (we don't want to keep results forever)
        expiration = int(timedelta(days=self.status_ttl_days).total_seconds())
        await self.redis.expire(history_key, expiration)
        
    async def _update_status(
        self,
        new_status: ApiStatus,
        previous_status: ApiStatus,
        check_id: str,
        api_key: str,
        tenant_id: Optional[uuid.UUID] = None
    ) -> None:
        """Update the status of an API and trigger callbacks if changed.
        
        Args:
            new_status: New API status
            previous_status: Previous API status
            check_id: Check identifier
            api_key: API key identifier
            tenant_id: Optional tenant ID
        """
        # Update in-memory cache
        self.last_status[check_id] = new_status
        
        # Store in Redis
        status_key = f"{self.namespace}:status:{check_id}"
        await self.redis.set(status_key, new_status.value)
        
        # Record status change in history
        change_key = f"{self.namespace}:changes:{check_id}"
        change_entry = json.dumps({
            "from": previous_status,
            "to": new_status,
            "timestamp": datetime.now().isoformat()
        })
        
        await self.redis.zadd(
            change_key,
            {change_entry: datetime.now().timestamp()}
        )
        
        # Set expiration
        expiration = int(timedelta(days=self.status_ttl_days).total_seconds())
        await self.redis.expire(change_key, expiration)
        
        # Log the status change
        logger.info(
            f"API status changed: {api_key} from {previous_status} to {new_status}",
            extra={
                "api_key": api_key,
                "previous_status": previous_status,
                "new_status": new_status,
                "tenant_id": str(tenant_id) if tenant_id else None
            }
        )
        
        # Record the state change in metrics
        self.metrics.record_circuit_breaker_state(
            service=api_key,
            circuit_breaker="health_monitor",
            state="open" if new_status == ApiStatus.DOWN else
                 "half-open" if new_status == ApiStatus.DEGRADED else "closed"
        )
        
        # Call status change callbacks
        for callback in self.status_change_callbacks:
            try:
                callback(api_key, previous_status, new_status)
            except Exception as e:
                logger.exception(
                    f"Error in status change callback for {api_key}",
                    extra={"error": str(e)}
                )
                
    def _record_metrics(
        self,
        result: HealthCheckResult,
        tenant_id: Optional[uuid.UUID] = None
    ) -> None:
        """Record metrics for a health check result.
        
        Args:
            result: Health check result
            tenant_id: Optional tenant ID for metrics
        """
        # Record API request metrics
        self.metrics.record_api_request(
            latency=result.response_time_ms / 1000,  # Convert to seconds
            endpoint=result.api_key,
            method="health_check",
            status=result.status_code or 0,
            tenant_id=str(tenant_id) if tenant_id else None
        )
        
        # If failed, record an error
        if result.status in [ApiStatus.DEGRADED, ApiStatus.DOWN]:
            self.metrics.record_error(
                error_type="api_health",
                component="health_monitor",
                endpoint=result.api_key,
                status_code=result.status_code or 500,
                error_code=f"health_{result.status.value}",
                tenant_id=str(tenant_id) if tenant_id else None
            )
            
    def _get_check_id(self, api_key: str, tenant_id: Optional[uuid.UUID] = None) -> str:
        """Generate a check ID for an API and tenant.
        
        Args:
            api_key: The API key identifier
            tenant_id: Optional tenant ID for multi-tenant isolation
            
        Returns:
            Check identifier string
        """
        if tenant_id:
            return f"{api_key}:{tenant_id}"
        else:
            return api_key


async def get_api_health_monitor() -> ApiHealthMonitor:
    """Factory function to create an ApiHealthMonitor.
    
    Returns:
        Initialized ApiHealthMonitor
    """
    redis_client = await get_redis_client()
    metrics_collector = TenantMetricsCollector()
    
    return ApiHealthMonitor(
        redis_client=redis_client,
        metrics_collector=metrics_collector
    )