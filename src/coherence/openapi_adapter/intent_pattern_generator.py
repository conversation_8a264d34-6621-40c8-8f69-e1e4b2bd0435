"""
Intent Pattern Generator using LLM for natural language pattern generation.

This module generates contextually appropriate intent patterns for API endpoints
using a Large Language Model to create natural language variations.
"""

import logging
from typing import Any, Dict, List, Optional

from src.coherence.core.llm.base import LLMProvider
from src.coherence.models.integration import APIEndpoint

logger = logging.getLogger(__name__)


class IntentPatternGenerator:
    """Generates natural language intent patterns for API endpoints using LLM."""
    
    def __init__(self, llm_provider: LLMProvider):
        """
        Initialize the intent pattern generator.
        
        Args:
            llm_provider: The LLM provider to use for pattern generation
        """
        self.llm_provider = llm_provider
    
    async def generate_patterns(
        self,
        endpoint: APIEndpoint,
        endpoint_spec: Dict[str, Any],
        integration_name: str,
        max_patterns: int = 10
    ) -> List[str]:
        """
        Generate natural language intent patterns for an API endpoint.
        
        Args:
            endpoint: The API endpoint model
            endpoint_spec: OpenAPI specification for the endpoint
            integration_name: Name of the API integration
            max_patterns: Maximum number of patterns to generate
            
        Returns:
            List of natural language intent patterns
        """
        try:
            # Build context from endpoint information
            prompt = self._build_prompt(
                endpoint, 
                endpoint_spec, 
                integration_name, 
                max_patterns
            )
            
            # Call LLM to generate patterns
            response = await self.llm_provider.generate(
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.7  # Some creativity for variety
            )
            
            # Parse patterns from response
            patterns = self._parse_patterns(response.content)
            
            # Validate and clean patterns
            valid_patterns = self._validate_patterns(patterns)
            
            return valid_patterns[:max_patterns]
            
        except Exception as e:
            logger.error(f"Error generating intent patterns: {e}")
            # Return empty list, caller should use fallback
            return []
    
    def _build_prompt(
        self,
        endpoint: APIEndpoint,
        endpoint_spec: Dict[str, Any],
        integration_name: str,
        max_patterns: int
    ) -> str:
        """Build the prompt for LLM pattern generation."""
        # Extract key information
        method = endpoint.method.upper()
        path = endpoint.path
        summary = endpoint_spec.get("summary", "")
        description = endpoint_spec.get("description", "")
        
        # Extract parameters
        parameters = endpoint_spec.get("parameters", [])
        param_info = self._format_parameters(parameters)
        
        # Extract response information
        responses = endpoint_spec.get("responses", {})
        response_info = self._format_responses(responses)
        
        prompt = f"""Generate natural language intent patterns for this API endpoint.

API Service: {integration_name}
HTTP Method: {method}
Path: {path}
Summary: {summary}
Description: {description}

Parameters:
{param_info}

Response Information:
{response_info}

Instructions:
1. Generate {max_patterns} diverse, natural ways users might ask for this functionality
2. Use conversational, everyday language that real users would actually say
3. Include variations with and without parameters
4. Focus on the actual purpose/intent, not technical details
5. Consider different phrasings and perspectives
6. Make patterns specific to this endpoint's functionality

Example format (one per line):
What are the active weather alerts?
Show me current weather warnings
Are there any severe weather alerts in my area?
Check for weather alerts near me

Generate patterns:"""
        
        return prompt
    
    def _format_parameters(self, parameters: List[Dict[str, Any]]) -> str:
        """Format parameter information for the prompt."""
        if not parameters:
            return "No parameters"
        
        param_lines = []
        for param in parameters:
            name = param.get("name", "")
            param_type = param.get("type", "string")
            required = param.get("required", False)
            description = param.get("description", "")
            
            line = f"- {name} ({param_type})"
            if required:
                line += " [REQUIRED]"
            if description:
                line += f": {description}"
            
            param_lines.append(line)
        
        return "\n".join(param_lines)
    
    def _format_responses(self, responses: Dict[str, Any]) -> str:
        """Format response information for the prompt."""
        if not responses:
            return "No response information available"
        
        response_lines = []
        for status_code, response_spec in responses.items():
            if status_code.startswith("2"):  # Success responses
                description = response_spec.get("description", "")
                response_lines.append(f"- {status_code}: {description}")
                
                # Add schema information if available
                content = response_spec.get("content", {})
                for content_type, content_spec in content.items():
                    if "schema" in content_spec:
                        schema = content_spec["schema"]
                        if "properties" in schema:
                            props = list(schema["properties"].keys())
                            response_lines.append(f"  Returns: {', '.join(props[:5])}")
        
        return "\n".join(response_lines) if response_lines else "Standard API response"
    
    def _parse_patterns(self, llm_response: str) -> List[str]:
        """Parse patterns from LLM response."""
        patterns = []
        
        # Split by newlines and clean up
        lines = llm_response.strip().split("\n")
        
        for line in lines:
            line = line.strip()
            # Skip empty lines and potential headers
            if not line or line.startswith("#") or line.startswith("Example"):
                continue
            
            # Remove bullet points or numbering
            if line.startswith("-"):
                line = line[1:].strip()
            elif line[0].isdigit() and line[1] in [".", ")"]:
                line = line[2:].strip()
            
            # Remove quotes if present
            line = line.strip('"\'')
            
            if line:
                patterns.append(line)
        
        return patterns
    
    def _validate_patterns(self, patterns: List[str]) -> List[str]:
        """Validate and filter patterns."""
        valid_patterns = []
        
        for pattern in patterns:
            # Skip if too short or too long
            if len(pattern) < 10 or len(pattern) > 200:
                continue
            
            # Skip if contains code-like elements
            if any(char in pattern for char in ["<", ">", "{", "}", "()", "[]"]):
                continue
            
            # Skip if it's just a technical description
            if any(term in pattern.lower() for term in ["api", "endpoint", "http", "get"]):
                continue
            
            valid_patterns.append(pattern)
        
        return valid_patterns
    
    def get_fallback_patterns(
        self,
        endpoint: APIEndpoint,
        integration_name: str
    ) -> List[str]:
        """
        Generate basic fallback patterns when LLM generation fails.
        
        Args:
            endpoint: The API endpoint
            integration_name: Name of the integration
            
        Returns:
            List of basic patterns
        """
        path_parts = endpoint.path.strip("/").split("/")
        resource_parts = [p for p in path_parts if not p.startswith("{")]
        
        if not resource_parts:
            resource_name = "data"
        else:
            resource_name = " ".join(resource_parts).replace("-", " ").replace("_", " ")
        
        method = endpoint.method.upper()
        
        if method == "GET":
            return [
                f"Get {resource_name} from {integration_name}",
                f"Show me {resource_name}",
                f"Find {resource_name}",
                f"Search for {resource_name}"
            ]
        elif method == "POST":
            return [
                f"Create new {resource_name}",
                f"Add {resource_name}",
                f"Submit {resource_name}"
            ]
        elif method in ["PUT", "PATCH"]:
            return [
                f"Update {resource_name}",
                f"Modify {resource_name}",
                f"Change {resource_name}"
            ]
        elif method == "DELETE":
            return [
                f"Delete {resource_name}",
                f"Remove {resource_name}"
            ]
        
        return [f"Access {resource_name} in {integration_name}"]