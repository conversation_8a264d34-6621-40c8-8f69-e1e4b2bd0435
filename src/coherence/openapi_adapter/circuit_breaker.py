"""
Circuit breaker implementation for API calls.

This module provides a circuit breaker pattern implementation
to prevent cascading failures when external services are experiencing issues.
"""

import asyncio
import logging
import time
from enum import Enum
from typing import Any, Callable, Dict, Optional, TypeVar, cast

# Define return type for decorated functions
T = TypeVar("T")

logger = logging.getLogger(__name__)


class CircuitState(str, Enum):
    """Circuit breaker states."""

    CLOSED = "closed"  # Normal operation, requests pass through
    OPEN = "open"  # Circuit is open, requests fail fast
    HALF_OPEN = "half_open"  # Testing if service is healthy again


class CircuitBreakerError(Exception):
    """Exception raised when circuit breaker prevents a call."""

    pass


class CircuitBreaker:
    """
    Circuit breaker implementation for API calls.

    This class implements the circuit breaker pattern to prevent
    cascading failures when external services are experiencing issues.
    """

    def __init__(
        self,
        name: str,
        failure_threshold: int = 5,
        recovery_timeout: float = 30.0,
        half_open_max_calls: int = 1,
        excluded_exceptions: Optional[list] = None,
    ):
        """Initialize the circuit breaker.

        Args:
            name: Circuit breaker name for identification
            failure_threshold: Number of consecutive failures before opening circuit
            recovery_timeout: Time in seconds to wait before attempting recovery
            half_open_max_calls: Maximum number of test calls in half-open state
            excluded_exceptions: Exceptions that should not count as failures
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.half_open_max_calls = half_open_max_calls
        self.excluded_exceptions = excluded_exceptions or []

        # State tracking
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0.0
        self.half_open_calls = 0

        # Lock for thread safety
        self._lock = asyncio.Lock()

    async def execute(self, func: Callable[..., T], *args: Any, **kwargs: Any) -> T:
        """
        Execute a function with circuit breaker protection.

        Args:
            func: Function to execute
            *args: Positional arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Function result

        Raises:
            CircuitBreakerError: If circuit is open
            Exception: Any exception raised by the function
        """
        # Check circuit state
        await self._check_state()

        try:
            # Execute the function
            result = await func(*args, **kwargs)

            # Reset failure count on success
            await self._on_success()

            return result

        except Exception as e:
            # Handle failure
            await self._on_failure(e)
            raise

    async def _check_state(self) -> None:
        """
        Check and possibly transition the circuit state.

        Raises:
            CircuitBreakerError: If circuit is open
        """
        async with self._lock:
            if self.state == CircuitState.OPEN:
                # Check if recovery timeout has elapsed
                if time.time() - self.last_failure_time >= self.recovery_timeout:
                    logger.info(
                        f"Circuit {self.name} transitioning from OPEN to HALF_OPEN after recovery timeout"
                    )
                    self.state = CircuitState.HALF_OPEN
                    self.half_open_calls = 0
                else:
                    # Circuit is open, fail fast
                    raise CircuitBreakerError(
                        f"Circuit {self.name} is OPEN until {self.last_failure_time + self.recovery_timeout}"
                    )

            if self.state == CircuitState.HALF_OPEN:
                # Limit the number of test calls
                if self.half_open_calls >= self.half_open_max_calls:
                    raise CircuitBreakerError(
                        f"Circuit {self.name} is HALF_OPEN and at test call limit"
                    )

                self.half_open_calls += 1

    async def _on_success(self) -> None:
        """Handle a successful call."""
        async with self._lock:
            if self.state == CircuitState.HALF_OPEN:
                # Successful test call, circuit can close
                logger.info(
                    f"Circuit {self.name} recovered, transitioning from HALF_OPEN to CLOSED"
                )
                self.state = CircuitState.CLOSED

            # Reset counters
            self.failure_count = 0
            self.half_open_calls = 0

    async def _on_failure(self, exception: Exception) -> None:
        """
        Handle a failed call.

        Args:
            exception: Exception raised by the call
        """
        # Check if this exception type should be excluded
        if any(
            isinstance(exception, exc_type) for exc_type in self.excluded_exceptions
        ):
            logger.debug(
                f"Circuit {self.name} ignoring excluded exception: {type(exception).__name__}"
            )
            return

        async with self._lock:
            current_time = time.time()

            if self.state == CircuitState.CLOSED:
                # Increment failure counter
                self.failure_count += 1

                # Check if threshold is reached
                if self.failure_count >= self.failure_threshold:
                    logger.warning(
                        f"Circuit {self.name} reached failure threshold ({self.failure_count}), "
                        f"transitioning from CLOSED to OPEN"
                    )
                    self.state = CircuitState.OPEN
                    self.last_failure_time = current_time

            elif self.state == CircuitState.HALF_OPEN:
                # Test call failed, circuit remains open
                logger.warning(
                    f"Circuit {self.name} test call failed, transitioning from HALF_OPEN to OPEN"
                )
                self.state = CircuitState.OPEN
                self.last_failure_time = current_time

    def get_state(self) -> Dict[str, Any]:
        """
        Get the current state of the circuit breaker.

        Returns:
            Dictionary with current state information
        """
        return {
            "name": self.name,
            "state": self.state,
            "failure_count": self.failure_count,
            "last_failure_time": self.last_failure_time,
            "failure_threshold": self.failure_threshold,
            "recovery_timeout": self.recovery_timeout,
        }
        
    def reset(self) -> None:
        """
        Manually reset the circuit breaker to closed state.
        
        This method is useful when an external system (like a health monitor)
        determines that the service is healthy again.
        """
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.half_open_calls = 0
        logger.info(f"Circuit {self.name} manually reset to CLOSED state")
        
    def trip(self) -> None:
        """
        Manually trip the circuit breaker to open state.
        
        This method is useful when an external system (like a health monitor)
        determines that the service is unhealthy.
        """
        self.state = CircuitState.OPEN
        self.last_failure_time = time.time()
        logger.warning(f"Circuit {self.name} manually tripped to OPEN state")
        
    async def __aenter__(self) -> "CircuitBreaker":
        """
        Async context manager entry.
        
        Returns:
            Self reference
            
        Raises:
            CircuitBreakerError: If circuit is open
        """
        await self._check_state()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """
        Async context manager exit.
        
        Args:
            exc_type: Exception type if an exception was raised in the context block
            exc_val: Exception value if an exception was raised in the context block
            exc_tb: Exception traceback if an exception was raised in the context block
        """
        if exc_type is not None:
            # An exception occurred, handle failure
            await self._on_failure(exc_val)
        else:
            # No exception, handle success
            await self._on_success()


# Circuit breaker registry for global access
_circuit_breakers: Dict[str, CircuitBreaker] = {}


def get_circuit_breaker(name: str) -> Optional[CircuitBreaker]:
    """
    Get a circuit breaker by name.

    Args:
        name: Name of the circuit breaker

    Returns:
        Circuit breaker instance or None if not found
    """
    return _circuit_breakers.get(name)


def register_circuit_breaker(circuit_breaker: CircuitBreaker) -> None:
    """
    Register a circuit breaker in the global registry.

    Args:
        circuit_breaker: Circuit breaker instance to register
    """
    _circuit_breakers[circuit_breaker.name] = circuit_breaker


def with_circuit_breaker(
    name: str,
    failure_threshold: int = 5,
    recovery_timeout: float = 30.0,
    excluded_exceptions: Optional[list] = None,
) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """
    Decorator to apply circuit breaker pattern to a function.

    Args:
        name: Circuit breaker name
        failure_threshold: Number of consecutive failures before opening circuit
        recovery_timeout: Time in seconds to wait before attempting recovery
        excluded_exceptions: Exceptions that should not count as failures

    Returns:
        Decorated function with circuit breaker protection
    """
    # Get or create circuit breaker
    circuit_breaker = get_circuit_breaker(name)
    if not circuit_breaker:
        circuit_breaker = CircuitBreaker(
            name=name,
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            excluded_exceptions=excluded_exceptions,
        )
        register_circuit_breaker(circuit_breaker)

    # Create decorator
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            return await circuit_breaker.execute(func, *args, **kwargs)

        return cast(Callable[..., T], wrapper)

    return decorator
