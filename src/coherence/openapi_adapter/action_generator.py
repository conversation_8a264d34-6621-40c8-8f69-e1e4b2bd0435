"""
Action generator for OpenAPI endpoints.

This module handles the generation of action classes from OpenAPI endpoints,
allowing dynamic execution of API operations.
"""

import hashlib
import json
import logging
import re
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from jinja2 import Template
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.models.generated_action import GeneratedAction, ValidationStatus
from src.coherence.models.integration import APIEndpoint, APIIntegration
from src.coherence.services.code_validator import CodeValidator
from src.coherence.openapi_adapter.intent_pattern_generator import IntentPatternGenerator
from src.coherence.core.llm.factory import LLMFactory

logger = logging.getLogger(__name__)


class ActionGenerator:
    """
    Generator for action classes from OpenAPI endpoints.

    This class handles:
    1. Code generation for action classes
    2. Parameter mapping between intents and API calls
    3. Response transformation and error handling
    4. Persistence and validation of generated code
    5. Template generation for API actions
    """

    def __init__(self, db: AsyncSession):
        """Initialize the action generator.

        Args:
            db: Database session for fetching API metadata
        """
        self.db = db
        self.code_validator = CodeValidator()
        self.pattern_generator = None  # Will be initialized asynchronously

    async def _ensure_initialized(self):
        """Ensure the pattern generator is initialized."""
        if self.pattern_generator is None:
            try:
                from src.coherence.core.config import settings
                llm_factory = LLMFactory()
                llm_provider = llm_factory.create_provider(
                    name=settings.LLM_PROVIDER,
                    model=settings.LLM_MODEL
                )
                self.pattern_generator = IntentPatternGenerator(llm_provider)
            except Exception as e:
                logger.warning(f"Could not initialize pattern generator: {e}. Using None.")
                self.pattern_generator = None

    async def generate_action(
        self,
        endpoint_id: uuid.UUID,
        tenant_id: uuid.UUID,
        integration_id: uuid.UUID = None,
        user_description: str = None,
        use_rag: bool = False,
    ) -> GeneratedAction:
        """
        Generate an action class for a specific endpoint.

        This is the primary method that:
        1. Fetches endpoint and integration metadata
        2. Generates the action class code
        3. Validates the generated code
        4. Persists the action to the database

        Args:
            endpoint_id: ID of the API endpoint
            tenant_id: ID of the tenant
            integration_id: Optional integration ID to use instead of fetching from endpoint
            user_description: Optional description of the action's purpose
            use_rag: Whether to use RAG for enhanced generation

        Returns:
            GeneratedAction: The persisted action record

        Raises:
            ValueError: If endpoint or integration not found
        """
        # Fetch endpoint data
        endpoint = await self.db.get(APIEndpoint, endpoint_id)
        if not endpoint:
            raise ValueError(f"Endpoint not found with ID: {endpoint_id}")

        # Use provided integration_id or fetch from endpoint
        if integration_id:
            integration = await self.db.get(APIIntegration, integration_id)
        else:
            integration = await self.db.get(APIIntegration, endpoint.integration_id)

        if not integration:
            raise ValueError(f"Integration not found for endpoint")

        # Generate action class code
        code = await self._generate_action_code(
            endpoint=endpoint,
            integration=integration,
            user_description=user_description,
            use_rag=use_rag,
        )

        # Validate the generated code
        validation_result = self.code_validator.validate(code)

        # Create and persist the action
        action = GeneratedAction(
            tenant_id=tenant_id,
            integration_id=integration.id,
            endpoint_id=endpoint_id,
            class_name=self._generate_class_name(
                endpoint.operation_id or endpoint.path, endpoint.path, endpoint.method
            ),
            code=code,
            validation_status=(
                ValidationStatus.VALID
                if validation_result.is_valid
                else ValidationStatus.INVALID
            ),
            validation_errors=validation_result.errors,
            metadata={
                "user_description": user_description,
                "use_rag": use_rag,
                "generation_timestamp": datetime.utcnow().isoformat(),
            },
        )

        self.db.add(action)
        await self.db.commit()
        await self.db.refresh(action)

        return action

    def _generate_class_name(
        self, operation_id: str = None, path: str = None, method: str = None
    ) -> str:
        """
        Generate a class name from endpoint metadata.

        Args:
            operation_id: OpenAPI operation ID
            path: API endpoint path
            method: HTTP method

        Returns:
            str: A valid Python class name
        """
        # Prefer operation_id if available
        if operation_id:
            base_name = operation_id
        else:
            # Fallback to path + method
            path_parts = path.strip("/").split("/")
            # Filter out parameters
            path_parts = [p for p in path_parts if not p.startswith("{")]
            base_name = "_".join(path_parts)
            if method:
                base_name = f"{method.lower()}_{base_name}"

        # Convert to camel case and ensure valid Python identifier
        words = re.split(r"[-_\s]+", base_name)
        class_name = "".join(word.capitalize() for word in words if word)

        # Ensure starts with a letter
        if class_name and not class_name[0].isalpha():
            class_name = f"Action{class_name}"

        return class_name or "GeneratedAction"

    async def _generate_action_code(
        self,
        endpoint: APIEndpoint,
        integration: APIIntegration,
        user_description: str = None,
        use_rag: bool = False,
    ) -> str:
        """
        Generate the actual action class code.

        This method:
        1. Extracts endpoint specification from OpenAPI
        2. Analyzes parameters and response schemas
        3. Generates parameter mapping logic
        4. Creates response transformation code
        5. Implements error handling
        6. Adds any custom logic based on user description

        Args:
            endpoint: The API endpoint
            integration: The API integration
            user_description: Optional user-provided description
            use_rag: Whether to use RAG for enhanced generation

        Returns:
            str: The generated Python code
        """
        # Extract endpoint specification from OpenAPI
        endpoint_spec = self._get_endpoint_spec(
            integration.openapi_spec, endpoint.path, endpoint.method.lower()
        )

        # Generate code from template
        class_name = self._generate_class_name(
            endpoint.operation_id or endpoint.path, endpoint.path, endpoint.method
        )

        # Extract parameters for the template
        parameters = self._extract_parameters(endpoint_spec)

        # Extract response information
        response_info = self._extract_response_info(endpoint_spec)

        # Generate parameter mapping code
        param_mapping = self._generate_parameter_mapping(parameters)

        # Generate response transformation code
        response_transform = self._generate_response_transformation(response_info)

        # Check if authentication is required
        security = endpoint_spec.get("security", [])
        requires_auth = bool(security) or bool(integration.openapi_spec.get("security", []))

        # Create the code template context
        template_context = {
            "class_name": class_name,
            "endpoint_path": endpoint.path,
            "method": endpoint.method.upper(),
            "base_url": integration.base_url,
            "description": endpoint.description or endpoint_spec.get("description", ""),
            "parameters": parameters,
            "param_mapping": param_mapping,
            "response_transform": response_transform,
            "requires_auth": requires_auth,
            "auth_type": integration.auth_config.auth_type.value if integration.auth_config and hasattr(integration.auth_config.auth_type, 'value') else integration.auth_config.auth_type if integration.auth_config and integration.auth_config.auth_type else "none",
            "user_description": user_description,
            "response_schema": response_info.get("schema", {}),
            "error_responses": self._extract_error_responses(endpoint_spec),
        }

        # Generate code from template
        code_template = Template(
            '''"""
Generated action for {{endpoint_path}} ({{method}}).

{{description}}
{% if user_description %}
User Description: {{user_description}}
{% endif %}
"""
import json
import logging
from typing import Any, Dict, Optional
from urllib.parse import urljoin

from src.coherence.actions.base import BaseAction, ActionResult

logger = logging.getLogger(__name__)


class {{class_name}}(BaseAction):
    """
    Action class for {{method}} {{endpoint_path}}.
    
    {{description}}
    """
    
    async def execute(self, intent_params: Dict[str, Any], context: Dict[str, Any]) -> ActionResult:
        """
        Execute the API call to {{endpoint_path}}.
        
        Args:
            intent_params: Parameters extracted from user intent
            context: Additional context (auth, tenant info, etc.)
            
        Returns:
            ActionResult: The result of the API call
        """
        try:
            # Extract and map parameters
            {{param_mapping}}
            
            # Build the full URL
            url = urljoin("{{base_url}}", "{{endpoint_path}}")
            
            # Perform parameter substitution in URL if needed
            if "{" in url:
                for param_name, param_value in api_params.items():
                    placeholder = "{" + param_name + "}"
                    if placeholder in url:
                        url = url.replace(placeholder, str(param_value))
                        # Remove from api_params since it's in the URL
                        del api_params[param_name]
            
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
            }
            
            {% if requires_auth %}
            # Add authentication
            auth_context = context.get("auth", {})
            if self.auth_type == "bearer":
                token = auth_context.get("access_token")
                if token:
                    headers["Authorization"] = f"Bearer {token}"
            elif self.auth_type == "api_key":
                api_key = auth_context.get("api_key")
                key_name = auth_context.get("key_name", "X-API-Key")
                if api_key:
                    headers[key_name] = api_key
            {% endif %}
            
            # Make the API call
            logger.info(f"Making {{method}} request to {url}")
            
            {% if method in ["GET", "DELETE"] %}
            response = await self.http_client.{{method.lower()}}(
                url,
                params=api_params,
                headers=headers,
            )
            {% else %}
            response = await self.http_client.{{method.lower()}}(
                url,
                json=api_params,
                headers=headers,
            )
            {% endif %}
            
            # Check response status
            if response.status_code >= 400:
                error_data = response.json() if response.text else {}
                return ActionResult(
                    success=False,
                    error=f"API error: {response.status_code}",
                    data=error_data,
                    metadata={
                        "status_code": response.status_code,
                        "url": url,
                        "method": "{{method}}",
                    }
                )
            
            # Parse and transform response
            response_data = response.json() if response.text else {}
            
            {{response_transform}}
            
            return ActionResult(
                success=True,
                data=transformed_data,
                metadata={
                    "status_code": response.status_code,
                    "url": url,
                    "method": "{{method}}",
                }
            )
            
        except Exception as e:
            logger.exception(f"Error executing {{class_name}}")
            return ActionResult(
                success=False,
                error=str(e),
                metadata={
                    "url": "{{base_url}}{{endpoint_path}}",
                    "method": "{{method}}",
                    "error_type": type(e).__name__,
                }
            )
    
    @property
    def endpoint_path(self) -> str:
        """Get the endpoint path."""
        return "{{endpoint_path}}"
    
    @property
    def http_method(self) -> str:
        """Get the HTTP method."""
        return "{{method}}"
    
    @property
    def auth_type(self) -> str:
        """Get the authentication type."""
        return "{{auth_type}}"
    
    @property
    def required_params(self) -> list:
        """Get the list of required parameters."""
        return {{[p.get("name") for p in parameters if p.get("required", False)]}}
    
    @property
    def optional_params(self) -> list:
        """Get the list of optional parameters."""
        return {{[p.get("name") for p in parameters if not p.get("required", False)]}}
'''
        )

        # Render the template
        return code_template.render(**template_context)

    def _generate_response_mapping(self, endpoint_spec: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate response mapping based on OpenAPI spec.
        
        Args:
            endpoint_spec: The endpoint specification from OpenAPI
            
        Returns:
            Response mapping configuration or None if no mapping needed
        """
        if not endpoint_spec:
            return None
            
        # Get the responses section from the spec
        responses = endpoint_spec.get("responses", {})
        if not responses:
            return None
            
        # Look for successful response codes (2xx)
        success_response = None
        for status_code, response_spec in responses.items():
            if status_code.startswith("2") or status_code == "default":
                success_response = response_spec
                break
                
        if not success_response:
            return None
            
        # Check if there's a content definition
        content = success_response.get("content", {})
        if not content:
            # No content definition means the response is likely direct
            return None
            
        # For now, we'll let the dynamic executor handle the raw response
        # This avoids making assumptions about response structure
        # The CRFS formatter can access data via result or raw_response as needed
        return None

    def _get_endpoint_spec(
        self, openapi_spec: Dict[str, Any], path: str, method: str
    ) -> Dict[str, Any]:
        """
        Extract the endpoint specification from OpenAPI spec.

        Args:
            openapi_spec: The full OpenAPI specification
            path: The API path
            method: The HTTP method (lowercase)

        Returns:
            Dict: The endpoint specification, or empty dict if not found
        """
        paths = openapi_spec.get("paths", {})
        path_spec = paths.get(path, {})
        endpoint_spec = path_spec.get(method, {})

        # Store the method and path for reference
        endpoint_spec["__method"] = method
        endpoint_spec["__path"] = path

        return endpoint_spec

    def _extract_parameters(self, endpoint_spec: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract and normalize parameters from endpoint specification.

        Args:
            endpoint_spec: The endpoint specification from OpenAPI

        Returns:
            List of parameter dictionaries with normalized structure
        """
        parameters = []
        
        # Get parameters from endpoint spec
        spec_params = endpoint_spec.get("parameters", [])
        
        for param in spec_params:
            # Resolve $ref if present
            if "$ref" in param:
                # In a real implementation, we'd resolve the reference
                # For now, we'll skip
                continue
                
            param_info = {
                "name": param.get("name"),
                "in": param.get("in", "query"),
                "description": param.get("description", ""),
                "required": param.get("required", False),
                "type": "string",  # Default type
                "format": None,
                "enum": None,
                "default": None,
                "example": None,
            }
            
            # Extract schema information
            schema = param.get("schema", {})
            if schema:
                param_info["type"] = schema.get("type", "string")
                param_info["format"] = schema.get("format")
                param_info["enum"] = schema.get("enum")
                param_info["default"] = schema.get("default")
                param_info["example"] = schema.get("example")
                
                # Handle numeric constraints
                if param_info["type"] in ["integer", "number"]:
                    param_info["minimum"] = schema.get("minimum")
                    param_info["maximum"] = schema.get("maximum")
                    
                # Handle string constraints
                if param_info["type"] == "string":
                    param_info["minLength"] = schema.get("minLength")
                    param_info["maxLength"] = schema.get("maxLength")
                    param_info["pattern"] = schema.get("pattern")
            
            parameters.append(param_info)
        
        # Extract request body parameters if present
        request_body = endpoint_spec.get("requestBody", {})
        if request_body:
            content = request_body.get("content", {})
            for content_type, content_spec in content.items():
                if "application/json" in content_type:
                    schema = content_spec.get("schema", {})
                    if schema.get("type") == "object":
                        properties = schema.get("properties", {})
                        required_props = schema.get("required", [])
                        
                        for prop_name, prop_schema in properties.items():
                            param_info = {
                                "name": prop_name,
                                "in": "body",
                                "description": prop_schema.get("description", ""),
                                "required": prop_name in required_props,
                                "type": prop_schema.get("type", "string"),
                                "format": prop_schema.get("format"),
                                "enum": prop_schema.get("enum"),
                                "default": prop_schema.get("default"),
                                "example": prop_schema.get("example"),
                            }
                            parameters.append(param_info)
                    break
        
        return parameters

    def _extract_response_info(self, endpoint_spec: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract response information from endpoint specification.

        Args:
            endpoint_spec: The endpoint specification

        Returns:
            Dict containing response schema and metadata
        """
        responses = endpoint_spec.get("responses", {})
        
        # Look for successful response (2xx)
        success_response = None
        for status_code, response_spec in responses.items():
            if status_code.startswith("2"):
                success_response = response_spec
                break
        
        if not success_response:
            return {"schema": {}, "description": ""}
        
        # Extract response schema
        content = success_response.get("content", {})
        schema = {}
        
        for content_type, content_spec in content.items():
            if "application/json" in content_type:
                schema = content_spec.get("schema", {})
                break
        
        return {
            "schema": schema,
            "description": success_response.get("description", ""),
            "headers": success_response.get("headers", {}),
        }

    def _extract_error_responses(self, endpoint_spec: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract error response information.

        Args:
            endpoint_spec: The endpoint specification

        Returns:
            List of error response specifications
        """
        responses = endpoint_spec.get("responses", {})
        error_responses = []
        
        for status_code, response_spec in responses.items():
            if not status_code.startswith("2"):
                error_info = {
                    "status_code": status_code,
                    "description": response_spec.get("description", ""),
                }
                
                # Extract error schema if available
                content = response_spec.get("content", {})
                for content_type, content_spec in content.items():
                    if "application/json" in content_type:
                        error_info["schema"] = content_spec.get("schema", {})
                        break
                
                error_responses.append(error_info)
        
        return error_responses

    def _generate_parameter_mapping(self, parameters: List[Dict[str, Any]]) -> str:
        """
        Generate code for mapping intent parameters to API parameters.

        Args:
            parameters: List of parameter specifications

        Returns:
            Python code string for parameter mapping
        """
        mapping_lines = ["api_params = {}"]
        
        for param in parameters:
            param_name = param["name"]
            param_in = param["in"]
            required = param["required"]
            default_value = param.get("default")
            
            if required:
                mapping_lines.append(f"""
# Extract required parameter: {param_name}
if "{param_name}" not in intent_params:
    return ActionResult(
        success=False,
        error="Missing required parameter: {param_name}",
        metadata={{"missing_param": "{param_name}"}}
    )
api_params["{param_name}"] = intent_params["{param_name}"]
""")
            else:
                if default_value is not None:
                    mapping_lines.append(f"""
# Extract optional parameter: {param_name} (default: {default_value})
api_params["{param_name}"] = intent_params.get("{param_name}", {repr(default_value)})
""")
                else:
                    mapping_lines.append(f"""
# Extract optional parameter: {param_name}
if "{param_name}" in intent_params:
    api_params["{param_name}"] = intent_params["{param_name}"]
""")
            
            # Add type validation if specified
            if param["type"] and param["type"] != "string":
                mapping_lines.append(f"""
# Validate type for {param_name}
if "{param_name}" in api_params:
    try:
        if "{param["type"]}" == "integer":
            api_params["{param_name}"] = int(api_params["{param_name}"])
        elif "{param["type"]}" == "number":
            api_params["{param_name}"] = float(api_params["{param_name}"])
        elif "{param["type"]}" == "boolean":
            api_params["{param_name}"] = bool(api_params["{param_name}"])
    except (ValueError, TypeError):
        return ActionResult(
            success=False,
            error=f"Invalid type for parameter {param_name}: expected {param["type"]}",
            metadata={{"param": "{param_name}", "expected_type": "{param["type"]}"}}
        )
""")
        
        return "\n".join(mapping_lines)

    def _generate_response_transformation(self, response_info: Dict[str, Any]) -> str:
        """
        Generate code for transforming API response to standard format.

        Args:
            response_info: Response schema and metadata

        Returns:
            Python code string for response transformation
        """
        schema = response_info.get("schema", {})
        
        # Default transformation (pass-through)
        transform_lines = ["transformed_data = response_data"]
        
        # If response has a specific structure, add extraction logic
        if schema.get("type") == "object":
            properties = schema.get("properties", {})
            
            # Check for common wrapper patterns
            if "data" in properties and len(properties) < 5:
                transform_lines = ["""
# Extract data from wrapper object
if "data" in response_data:
    transformed_data = response_data["data"]
else:
    transformed_data = response_data
"""]
            elif "results" in properties and len(properties) < 5:
                transform_lines = ["""
# Extract results from wrapper object
if "results" in response_data:
    transformed_data = response_data["results"]
else:
    transformed_data = response_data
"""]
            elif "items" in properties and len(properties) < 5:
                transform_lines = ["""
# Extract items from wrapper object
if "items" in response_data:
    transformed_data = response_data["items"]
else:
    transformed_data = response_data
"""]
        
        return "\n".join(transform_lines)

    def resolve_reference(self, ref: str, spec: Dict[str, Any]) -> Dict[str, Any]:
        """
        Resolve a JSON reference in the OpenAPI spec.
        
        Args:
            ref: The reference string (e.g., "#/components/schemas/Model")
            spec: The full OpenAPI specification
            
        Returns:
            The resolved component
            
        Raises:
            ValueError: If the reference cannot be resolved
        """
        if not ref.startswith("#/"):
            raise ValueError(f"Only local references are supported, got: {ref}")
        
        # Remove the leading #/ and split the path
        path_parts = ref[2:].split("/")
        
        # Navigate through the spec
        current = spec
        for part in path_parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                raise ValueError(f"Cannot resolve reference: {ref}")
        
        return current
    
    async def _generate_documentation_template_with_llm(
        self,
        endpoint_spec: Dict[str, Any],
        endpoint: APIEndpoint,
        integration: APIIntegration,
        template_key: str,
        intent_template_key: str,
        parameters: List[Dict[str, Any]],
        tenant_id: uuid.UUID,
        human_readable_name: str
    ) -> Dict[str, Any]:
        """
        Generate a documentation template using LLM for better natural language documentation.
        
        Args:
            endpoint_spec: OpenAPI operation specification
            endpoint: API endpoint model
            integration: API integration model
            template_key: Key of the action template
            intent_template_key: Key of the intent template
            parameters: Extracted parameters
            tenant_id: ID of the tenant
            human_readable_name: Human-readable name for the endpoint
            
        Returns:
            Template dictionary for documentation
        """
        # Ensure pattern generator is initialized
        await self._ensure_initialized()
        
        if not self.pattern_generator or not self.pattern_generator.llm_provider:
            # Fallback to basic documentation if LLM not available
            return self._generate_basic_documentation_template(
                endpoint_spec, endpoint, integration, template_key, 
                intent_template_key, parameters, tenant_id, human_readable_name
            )
        
        # Prepare context for LLM
        summary = endpoint_spec.get("summary", f"{endpoint.method} {endpoint.path}")
        description = endpoint_spec.get("description", "")
        operation_id = endpoint_spec.get("operationId", "")
        
        # Build prompt for LLM
        prompt = f"""Generate comprehensive API documentation for the following endpoint:

Endpoint: {endpoint.method.upper()} {endpoint.path}
Operation: {operation_id}
Summary: {summary}
Description: {description}
Integration: {integration.name}

Parameters:
{self._format_parameters_for_prompt(parameters)}

Responses:
{self._format_responses_for_prompt(endpoint_spec.get("responses", {}))}

Please generate documentation in the following JSON structure:
{{
    "endpoint": "{endpoint.path}",
    "method": "{endpoint.method.upper()}",
    "summary": "Clear, concise summary",
    "description": "Detailed description of what this endpoint does",
    "intent_examples": [
        "How do I use the {human_readable_name}?",
        "What parameters does {human_readable_name} need?",
        "Show me documentation for {human_readable_name}",
        "Explain how to use {endpoint.method} {endpoint.path}",
        "What does the {human_readable_name} API do?",
        "Help with {human_readable_name}",
        "Documentation for {human_readable_name}",
        "How to call {human_readable_name}",
        "Examples for {human_readable_name}"
    ],
    "parameters": [
        {{
            "name": "param_name",
            "description": "Clear description of the parameter",
            "type": "string|number|boolean|etc",
            "required": true|false,
            "examples": ["example1", "example2"],
            "constraints": {{"pattern": "regex", "minimum": 0, "maximum": 100}}
        }}
    ],
    "responses": [
        {{
            "status_code": 200,
            "description": "What this response means",
            "schema": {{"type": "object", "properties": {{}}}},
            "examples": {{}}
        }}
    ],
    "examples": [
        {{
            "title": "Example Use Case",
            "description": "When to use this",
            "request": "Natural language description of the request",
            "parameters": {{"param1": "value1"}},
            "response": {{"key": "value"}},
            "notes": "Additional helpful notes"
        }}
    ],
    "authentication": {{
        "required": true|false,
        "methods": ["api_key", "oauth2"],
        "description": "Authentication details"
    }},
    "tags": ["tag1", "tag2"]
}}

Make the documentation developer-friendly with:
1. Clear, helpful parameter descriptions
2. Practical examples showing real-world usage
3. Natural language explanations of what the endpoint does
4. Common use cases and best practices
5. Any gotchas or important notes
6. Include natural language variations of how users might ask for this documentation
"""

        try:
            # Use LLM to generate documentation
            response = await self.pattern_generator.llm_provider.generate(
                messages=[
                    {"role": "system", "content": "You are an expert API documentation writer. Generate clear, comprehensive, and developer-friendly documentation."},
                    {"role": "user", "content": prompt}
                ]
            )
            
            # Parse the LLM response
            import json
            documentation_content = json.loads(response.content)
            
            # Validate and clean up the documentation content
            documentation_content = self._validate_documentation_content(documentation_content)
            
            # Ensure we have intent examples
            if not documentation_content.get("intent_examples"):
                documentation_content["intent_examples"] = [
                    f"How do I use the {human_readable_name}?",
                    f"What parameters does {human_readable_name} need?",
                    f"Show me documentation for {human_readable_name}",
                    f"Explain how to use {endpoint.method} {endpoint.path}",
                    f"What does the {human_readable_name} API do?",
                    f"Help with {human_readable_name}",
                    f"Documentation for {human_readable_name}",
                    f"How to call {human_readable_name}",
                    f"Examples for {human_readable_name}",
                    f"How do I {summary.lower()}?",
                    f"Show me how to {summary.lower()}",
                    f"What is the {human_readable_name} API?",
                    f"{human_readable_name} documentation",
                    f"{summary} help"
                ]
            
        except Exception as e:
            logger.warning(f"Failed to generate documentation with LLM: {e}")
            # Fallback to basic documentation
            return self._generate_basic_documentation_template(
                endpoint_spec, endpoint, integration, template_key,
                intent_template_key, parameters, tenant_id, human_readable_name
            )
        
        # Generate searchable content for better vector search
        searchable_parts = [
            f"How to use {human_readable_name}",
            f"Documentation for {endpoint.method} {endpoint.path}",
            documentation_content.get("summary", ""),
            documentation_content.get("description", ""),
            f"Parameters: {', '.join(p['name'] for p in documentation_content.get('parameters', []))}",
            f"Tags: {', '.join(documentation_content.get('tags', []))}"
        ]
        searchable_content = " ".join(filter(None, searchable_parts))
        
        # Create the documentation template
        docs_template_key = f"docs_{template_key}"
        
        documentation_template = {
            "key": docs_template_key,
            "category": "documentation",
            "body": json.dumps(documentation_content, indent=2),  # Store as JSON string
            "scope": "tenant",
            "scope_id": tenant_id,
            "tenant_id": tenant_id,
            "version": 1,
            "description": f"API documentation for {human_readable_name}",
            "actions": None,  # Documentation templates don't have actions
            "parameters": None,  # Documentation templates don't use parameters
            "response_format": None,  # Documentation templates don't need response format
            "metadata": {
                "source": "openapi",
                "integration_id": str(integration.id),
                "endpoint_id": str(endpoint.id),
                "api_name": integration.name,
                "generated": True,
                "display_name": f"Docs: {human_readable_name}",
                "related_action_template": template_key,
                "related_intent_template": intent_template_key,
                "searchable_content": searchable_content,
                "documentation_content": documentation_content,
                "tags": documentation_content.get("tags", [])
            }
        }
        
        return documentation_template
    
    def _format_parameters_for_prompt(self, parameters: List[Dict[str, Any]]) -> str:
        """Format parameters for LLM prompt."""
        if not parameters:
            return "No parameters"
        
        param_descriptions = []
        for param in parameters:
            desc = f"- {param.get('name')} ({param.get('type', 'string')})"
            if param.get('required'):
                desc += " [REQUIRED]"
            if param.get('description'):
                desc += f": {param.get('description')}"
            if param.get('example'):
                desc += f" (example: {param.get('example')})"
            param_descriptions.append(desc)
        
        return "\n".join(param_descriptions)
    
    def _format_responses_for_prompt(self, responses: Dict[str, Any]) -> str:
        """Format responses for LLM prompt."""
        if not responses:
            return "No response documentation"
        
        response_descriptions = []
        for status_code, response in responses.items():
            desc = f"- {status_code}: {response.get('description', 'No description')}"
            if 'content' in response:
                content_types = list(response['content'].keys())
                desc += f" (returns: {', '.join(content_types)})"
            response_descriptions.append(desc)
        
        return "\n".join(response_descriptions)
    
    def _validate_documentation_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and ensure documentation content has required fields."""
        # Ensure required fields exist
        content.setdefault("endpoint", "")
        content.setdefault("method", "GET")
        content.setdefault("summary", "")
        content.setdefault("description", "")
        content.setdefault("parameters", [])
        content.setdefault("responses", [])
        content.setdefault("examples", [])
        content.setdefault("authentication", {"required": False, "methods": []})
        content.setdefault("tags", [])
        content.setdefault("intent_examples", [])
        
        # Validate parameters structure
        for param in content.get("parameters", []):
            param.setdefault("name", "")
            param.setdefault("description", "")
            param.setdefault("type", "string")
            param.setdefault("required", False)
            param.setdefault("examples", [])
            param.setdefault("constraints", {})
        
        # Validate responses structure
        for response in content.get("responses", []):
            response.setdefault("status_code", 200)
            response.setdefault("description", "")
            response.setdefault("schema", {})
            response.setdefault("examples", {})
        
        return content
    
    def _generate_basic_documentation_template(
        self,
        endpoint_spec: Dict[str, Any],
        endpoint: APIEndpoint,
        integration: APIIntegration,
        template_key: str,
        intent_template_key: str,
        parameters: List[Dict[str, Any]],
        tenant_id: uuid.UUID,
        human_readable_name: str
    ) -> Dict[str, Any]:
        """
        Generate a basic documentation template without LLM.
        Fallback method when LLM is not available.
        """
        # Extract basic information
        summary = endpoint_spec.get("summary", f"{endpoint.method} {endpoint.path}")
        description = endpoint_spec.get("description", "No description available")
        
        # Create parameter documentation
        param_docs = []
        for param in parameters:
            param_doc = {
                "name": param.get("name"),
                "description": param.get("description", ""),
                "type": param.get("type", "string"),
                "required": param.get("required", False),
                "examples": [param.get("example")] if param.get("example") else [],
                "constraints": {}
            }
            param_docs.append(param_doc)
        
        # Create response documentation
        responses = endpoint_spec.get("responses", {})
        response_docs = []
        
        for status_code, response_spec in responses.items():
            response_doc = {
                "status_code": int(status_code) if status_code.isdigit() else status_code,
                "description": response_spec.get("description", ""),
                "schema": {},
                "examples": {}
            }
            response_docs.append(response_doc)
        
        # Create intent examples for the documentation
        intent_examples = [
            f"How do I use the {human_readable_name}?",
            f"What parameters does {human_readable_name} need?",
            f"Show me documentation for {human_readable_name}",
            f"Explain how to use {endpoint.method} {endpoint.path}",
            f"What does the {human_readable_name} API do?",
            f"Help with {human_readable_name}",
            f"Documentation for {human_readable_name}",
            f"How to call {human_readable_name}",
            f"Examples for {human_readable_name}"
        ]
        
        # Add more specific examples based on the summary
        if summary:
            intent_examples.extend([
                f"How do I {summary.lower()}?",
                f"Show me how to {summary.lower()}",
                f"{summary} help"
            ])
        
        # Create basic documentation content
        documentation_content = {
            "endpoint": endpoint.path,
            "method": endpoint.method.upper(),
            "summary": summary,
            "description": description,
            "intent_examples": intent_examples,
            "parameters": param_docs,
            "responses": response_docs,
            "examples": [],
            "authentication": {
                "required": bool(endpoint_spec.get("security", [])),
                "methods": ["api_key"] if endpoint_spec.get("security") else [],
                "description": "API key required" if endpoint_spec.get("security") else "No authentication required"
            },
            "tags": endpoint_spec.get("tags", [])
        }
        
        # Generate searchable content
        searchable_content = f"Documentation for {human_readable_name} {endpoint.method} {endpoint.path}"
        
        # Create the documentation template
        docs_template_key = f"docs_{template_key}"
        
        return {
            "key": docs_template_key,
            "category": "documentation",
            "body": json.dumps(documentation_content, indent=2),
            "scope": "tenant",
            "scope_id": tenant_id,
            "tenant_id": tenant_id,
            "version": 1,
            "description": f"API documentation for {human_readable_name}",
            "actions": None,
            "parameters": None,
            "response_format": None,
            "metadata": {
                "source": "openapi",
                "integration_id": str(integration.id),
                "endpoint_id": str(endpoint.id),
                "api_name": integration.name,
                "generated": True,
                "display_name": f"Docs: {human_readable_name}",
                "related_action_template": template_key,
                "related_intent_template": intent_template_key,
                "searchable_content": searchable_content,
                "documentation_content": documentation_content,
                "tags": documentation_content.get("tags", [])
            }
        }

    async def _generate_action_templates(
        self,
        endpoint_id: uuid.UUID,
        tenant_id: uuid.UUID,
        api_key: str = None,
        base_url: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate template definitions for an API endpoint.
        
        Args:
            endpoint_id: ID of the API endpoint
            tenant_id: ID of the tenant
            api_key: Optional key to identify the API (used in template naming)
            
        Returns:
            List of template definitions ready to be created
        """
        # Ensure pattern generator is initialized
        await self._ensure_initialized()
        
        # Fetch endpoint and integration data
        endpoint = await self.db.get(APIEndpoint, endpoint_id)
        if not endpoint:
            raise ValueError(f"Endpoint not found with ID: {endpoint_id}")
            
        # Fetch integration with auth_config eagerly loaded
        from sqlalchemy.orm import selectinload
        result = await self.db.execute(
            select(APIIntegration)
            .options(selectinload(APIIntegration.auth_config))
            .where(APIIntegration.id == endpoint.integration_id)
        )
        integration = result.scalar_one_or_none()
        if not integration:
            raise ValueError(f"Integration not found with ID: {endpoint.integration_id}")
        
        # Use API key or generate one from integration name
        if not api_key:
            api_key = integration.name.lower().replace(" ", "_").replace("-", "_")
        
        # Get the endpoint specification from OpenAPI spec
        endpoint_spec = self._get_endpoint_spec(
            integration.openapi_spec, endpoint.path, endpoint.method.lower()
        )
        
        if not endpoint_spec:
            logger.warning(f"Endpoint spec not found for {endpoint.method} {endpoint.path}")
            return []
        
        # Generate operation name (for user-friendly display)
        operation_id = endpoint.operation_id or self._generate_class_name(
            endpoint.operation_id, endpoint.path, endpoint.method
        ).lower()
        
        # Create a more user-friendly operation name by cleaning up the operation ID
        # Convert camelCase or snake_case to space-separated words
        cleaned_operation = re.sub(r'([a-z0-9])([A-Z])', r'\1 \2', operation_id).lower()  # camelCase to spaces
        cleaned_operation = cleaned_operation.replace('_', ' ')  # snake_case to spaces
        
        # Create a human-readable operation name (capitalize words)
        human_readable_operation = ' '.join(word.capitalize() for word in cleaned_operation.split())
        
        # Prepare API name for the template name (clean the integration name)
        api_name = integration.name.replace(' ', '').replace('-', '')
        if len(api_name) > 15:  # Truncate very long API names
            api_name = api_name[:15]
            
        # Method name for prefix
        method_prefix = endpoint.method.lower()
        
        # Generate a unique but user-friendly template key
        # Format: api_<method>_<operation>_<id_short>
        # We'll include a short hash at the end to ensure uniqueness while keeping names readable
        id_hash = str(endpoint_id).replace('-', '')[-6:]  # Last 6 chars of ID without dashes
        template_key = f"api_{method_prefix}_{operation_id}_{id_hash}".replace("/", "_").replace("-", "_").lower()
        
        # Create an intent template key (prefix with intent_)
        intent_template_key = f"intent_{template_key}"
        
        # Store human readable name in metadata
        human_readable_name = f"{method_prefix.upper()} {human_readable_operation} ({api_name})"
        
        # Extract parameters for mapping
        parameters = self._extract_parameters(endpoint_spec)
        
        # Generate parameter mapping
        parameter_mapping = {}
        for param in parameters:
            param_name = param.get("name")
            param_mapping = {
                "source": "intent_param",
                "source_path": f"params.{param_name}",
                "target_path": param_name,
                "transformation": None  # Default: no transformation
            }
            parameter_mapping[param_name] = param_mapping
        
        # Generate response mapping based on the OpenAPI spec
        # Don't assume a specific response structure - let the spec guide us
        response_mapping = self._generate_response_mapping(endpoint_spec)
        
        # Create the action configuration
        action_config = {
            "api_key": api_key,
            "base_url": base_url or integration.base_url,
            "endpoint": endpoint.path,
            "endpoint_id": str(endpoint_id),
            "integration_id": str(integration.id),
            "method": endpoint.method.upper(),
            "authentication": {
                "type": integration.auth_config.auth_type.value if integration.auth_config and hasattr(integration.auth_config.auth_type, 'value') else integration.auth_config.auth_type if integration.auth_config and integration.auth_config.auth_type else "none",
                "credential_key": "api_key",
                "credential_source": "integration_config"
            },
            "parameter_mapping": parameter_mapping,
            "error_handling": {
                "retries": 2,
                "timeout_ms": 5000,
                "fallback_template": f"{template_key}_fallback"
            }
        }
        
        # Only include response_mapping if it's not None
        if response_mapping is not None:
            action_config["response_mapping"] = response_mapping
        
        # Extract a description from the endpoint spec or integration
        summary = endpoint_spec.get("summary", "")
        description = endpoint.description or endpoint_spec.get("description", f"Execute {endpoint.method} {endpoint.path}")
        
        # If operation_id is available, use it for the description
        if endpoint.operation_id:
            operation_display = endpoint.operation_id.replace("_", " ").replace("-", " ").title()
            description = f"{operation_display}: {description}" if description else operation_display
        
        # Convert parameters to schema form for template with rich metadata
        # Use a dictionary instead of a list for param_schema
        param_schema = {}
        for param in parameters:
            param_name = param.get("name")
            schema_param = {
                "type": param.get("type", "string"),
                "description": param.get("description", ""),
                "required": param.get("required", False),
                "default": param.get("default"),
                "format": param.get("format"),
                "example": param.get("example"),
                "location": param.get("in", "query")  # path, query, header, body
            }
            
            # Add enum values if available
            if param.get("enum"):
                schema_param["enum"] = param.get("enum")
                
            # Add constraints for numeric types
            if param.get("type") in ["integer", "number"]:
                if param.get("minimum") is not None:
                    schema_param["minimum"] = param.get("minimum")
                if param.get("maximum") is not None:
                    schema_param["maximum"] = param.get("maximum")
                    
            # Add constraints for string types
            if param.get("type") == "string":
                if param.get("minLength") is not None:
                    schema_param["minLength"] = param.get("minLength")
                if param.get("maxLength") is not None:
                    schema_param["maxLength"] = param.get("maxLength")
                if param.get("pattern"):
                    schema_param["pattern"] = param.get("pattern")
            
            if param_name:  # Only add if we have a valid parameter name
                param_schema[param_name] = schema_param
        
        # Generate a default response template body with more context about the API
        # Create parameter placeholders for the template body
        
        # Build a more informative default response template
        default_body = f"""Successfully executed {human_readable_name}.

API Response:
Here are the results:

{{{{ result | json_pretty }}}}

{{% if result.data %}}
{{% for key, value in result.data.items() %}}
- {{{{ key }}}}: {{{{ value }}}}
{{% endfor %}}
{{% endif %}}
"""
        
        # Create the action template definition with actions as a dictionary, not a list
        # Use "api_action" as the key for consistency with our fix script
        primary_template = {
            "key": template_key,
            "category": "response_gen",
            "body": default_body,
            "scope": "tenant",
            "scope_id": tenant_id,
            "tenant_id": tenant_id,
            "version": 1,
            "description": description,
            "actions": {"api_action": action_config},  # Dictionary, not list
            "parameters": param_schema,
            "response_format": self._generate_response_format(endpoint_spec, integration.name, integration.openapi_spec),
            "metadata": {
                "source": "openapi",
                "integration_id": str(integration.id),
                "endpoint_id": str(endpoint_id),
                "api_name": integration.name,
                "generated": True,
                "display_name": human_readable_name,
                "tags": endpoint_spec.get("tags", []),
            }
        }
        
        # Create a fallback template for error cases
        fallback_key = f"{template_key}_fallback"
        fallback_template = {
            "key": fallback_key,
            "category": "error_handler",
            "body": f"I'm sorry, I couldn't get the information you requested from the {integration.name} API due to a technical issue: {{{{ error_message }}}}",
            "scope": "tenant",
            "scope_id": tenant_id,
            "tenant_id": tenant_id,
            "version": 1,
            "description": f"Fallback response for {description}",
            "actions": {},  # Empty dictionary, not empty list
            "parameters": {},
            "metadata": {
                "source": "openapi",
                "integration_id": str(integration.id),
                "endpoint_id": str(endpoint_id),
                "api_name": integration.name,
                "generated": True,
                "display_name": "Fallback: " + human_readable_name,
                "tags": endpoint_spec.get("tags", []),
            }
        }
        
        # Generate intent template for vector matching
        intent_patterns = []
        
        # Use LLM to generate patterns if available
        if self.pattern_generator:
            try:
                # Generate patterns using LLM
                intent_patterns = await self.pattern_generator.generate_patterns(
                    endpoint=endpoint,
                    endpoint_spec=endpoint_spec,
                    integration_name=integration.name,
                    max_patterns=10
                )
                logger.info(f"Generated {len(intent_patterns)} patterns using LLM for {endpoint.path}")
            except Exception as e:
                logger.error(f"Error generating patterns with LLM: {e}")
                # Fall through to fallback patterns
        
        # Fallback to basic patterns if LLM generation failed or is unavailable
        if not intent_patterns:
            logger.info(f"Using fallback patterns for {endpoint.path}")
            if self.pattern_generator:
                intent_patterns = self.pattern_generator.get_fallback_patterns(
                    endpoint=endpoint,
                    integration_name=integration.name
                )
            else:
                # Ultimate fallback - basic patterns
                resource_name = endpoint.path.split("/")[-1]
                if resource_name.startswith("{"):
                    resource_name = endpoint.path.split("/")[-2]
                resource_name = resource_name.replace("-", " ")
                
                intent_patterns = [
                    f"Get {resource_name} from {integration.name}",
                    f"Show me {resource_name}",
                    f"Find {resource_name}",
                    f"Search for {resource_name}"
                ]
        
        # Note: Parameter-specific patterns are now handled by the LLM generator
        # which creates more natural, context-aware patterns
        
        # Generate examples in the format: "Query example" -> action_name(param="value")
        example_mappings = []
        operation_name = operation_id.replace("_", " ")
        for _i, pattern in enumerate(intent_patterns[:4]):  # Use up to 4 patterns for examples
            param_examples = []
            for _j, param in enumerate(parameters[:3]):  # Use up to 3 parameters
                param_name = param.get("name")
                
                # Use the example value we've already extracted
                example_value = param.get("example", f"example_{param_name}")
                
                param_examples.append(f'{param_name}="{example_value}"')
            
            example_mappings.append(f'"{pattern}" -> {operation_id}({", ".join(param_examples)})')
        
        # Create enhanced parameter documentation with schema details
        parameter_docs = []
        for param in parameters:
            param_name = param.get("name")
            param_desc = param.get("description", "")
            param_type = param.get("type", "string")
            param_required = "required" if param.get("required", False) else "optional"
            param_location = param.get("location", "query")
            param_example = param.get("example", "")
            
            # Build a rich parameter description
            param_doc = f"- {param_name}: {param_desc} (Type: {param_type}, {param_required}, in: {param_location})"
            
            # Add format if available
            if param.get("format"):
                param_doc += f", format: {param.get('format')}"
                
            # Add enum values if available
            if param.get("enum"):
                enum_values = ", ".join([str(v) for v in param.get("enum")])
                param_doc += f", allowed values: [{enum_values}]"
                
            # Add example value if available
            if param_example and param_example != f"example_{param_name}":
                param_doc += f", example: {param_example}"
                
            # Add min/max constraints if available
            constraints = []
            if param.get("minimum") is not None:
                constraints.append(f"min: {param.get('minimum')}")
            if param.get("maximum") is not None:
                constraints.append(f"max: {param.get('maximum')}")
            if param.get("minLength") is not None:
                constraints.append(f"minLength: {param.get('minLength')}")
            if param.get("maxLength") is not None:
                constraints.append(f"maxLength: {param.get('maxLength')}")
            
            if constraints:
                param_doc += f", constraints: {', '.join(constraints)}"
            
            parameter_docs.append(param_doc)
        
        # Create a detailed intent template body with examples
        intent_body = f"""{description}

API Endpoint: {endpoint.method} {endpoint.path}
Integration: {integration.name}

Intent Patterns:
{chr(10).join(f"- {pattern}" for pattern in intent_patterns[:10])}

Parameters:
{chr(10).join(parameter_docs) if parameter_docs else "No parameters required"}

Examples:
{chr(10).join(example_mappings[:4]) if example_mappings else "No examples available"}

Response Format:
The response will include the {integration.name} API data in a structured format.
"""
        
        intent_template = {
            "key": intent_template_key,
            "category": "intent_router",
            "body": intent_body,
            "scope": "tenant",
            "scope_id": tenant_id,
            "tenant_id": tenant_id,
            "version": 1,
            "description": f"Intent routing for {description}",
            # Actions should reference the main template key
            "actions": {"api_action": {
                "type": "template",
                "template_key": template_key
            }},
            "parameters": param_schema,
            "response_format": self._generate_response_format(endpoint_spec, integration.name, integration.openapi_spec),
            "metadata": {
                "source": "openapi",
                "integration_id": str(integration.id),
                "endpoint_id": str(endpoint_id),
                "api_name": integration.name,
                "generated": True,
                "display_name": "Intent: " + human_readable_name,
                "tags": endpoint_spec.get("tags", []),
                "intent_patterns": intent_patterns
            }
        }
        
        # Generate documentation template using LLM
        docs_template = await self._generate_documentation_template_with_llm(
            endpoint_spec=endpoint_spec,
            endpoint=endpoint,
            integration=integration,
            template_key=template_key,
            intent_template_key=intent_template_key,
            parameters=parameters,
            tenant_id=tenant_id,
            human_readable_name=human_readable_name
        )
        
        return [primary_template, fallback_template, intent_template, docs_template]

    def _generate_response_format(
        self, endpoint_spec: Dict[str, Any], integration_name: str, openapi_spec: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Generate CRFS response format configuration based on endpoint spec.
        
        Args:
            endpoint_spec: OpenAPI operation specification
            integration_name: Name of the API integration
            
        Returns:
            CRFS response format configuration dictionary
        """
        # Get endpoint metadata
        summary = endpoint_spec.get("summary", "API Response")
        description = endpoint_spec.get("description", "")
        operation_id = endpoint_spec.get("operationId", "")
        method = endpoint_spec.get("__method", "GET").upper()
        path = endpoint_spec.get("__path", "")
        
        # Get successful response (usually 200 or 201)
        responses = endpoint_spec.get("responses", {})
        success_response = responses.get("200", responses.get("201", {}))
        
        # Initialize CRFS structure - now using multi-format support
        crfs_config = {}
        
        # Store metadata at root level
        crfs_config["__metadata"] = {
            "title": summary,
            "description": description,
            "source": integration_name,
            "operation_id": operation_id,
            "method": method,
            "path": path
        }
        
        # If no successful response is defined, return basic template
        if not success_response:
            # Return single format wrapped in dict
            return {
                "application/json": {
                    "kind": "coherence-response",
                    "crfs_version": "2.0",
                    "metadata": crfs_config["__metadata"],
                    "format": {
                        "type": "template",
                        "template": f"The request to {integration_name} was successful.\n\n{{{{ result | json_pretty }}}}"
                    },
                    "error_template": "⚠️ API Error: {{ error.message }}"
                },
                "preferred": "application/json"
            }
            
        # Resolve $ref if present in success response
        if "$ref" in success_response and openapi_spec:
            try:
                success_response = self.resolve_reference(success_response["$ref"], openapi_spec)
            except ValueError as e:
                logger.warning(f"Failed to resolve response reference: {e}")
                # Return basic format
                return {
                    "application/json": {
                        "kind": "coherence-response",
                        "crfs_version": "2.0",
                        "metadata": crfs_config["__metadata"],
                        "format": {
                            "type": "template",
                            "template": f"The request to {integration_name} was successful.\n\n{{{{ result | json_pretty }}}}"
                        },
                        "error_template": "⚠️ API Error: {{ error.message }}"
                    },
                    "preferred": "application/json"
                }
        
        # Extract response schemas for all content types
        content = success_response.get("content", {})
        if not content:
            # No content definition
            return {
                "application/json": {
                    "kind": "coherence-response",
                    "crfs_version": "2.0",
                    "metadata": crfs_config["__metadata"],
                    "format": {
                        "type": "template",
                        "template": f"The request to {integration_name} was successful.\n\n{{{{ result | json_pretty }}}}"
                    },
                    "error_template": "⚠️ API Error: {{ error.message }}"
                },
                "preferred": "application/json"
            }
        
        # Generate CRFS format for each content type
        formats_by_content_type = {}
        preferred_content_type = None
        
        for content_type, content_spec in content.items():
            if "schema" in content_spec:
                response_schema = content_spec["schema"]
                
                # Resolve $ref in schema if present
                if "$ref" in response_schema and openapi_spec:
                    try:
                        response_schema = self.resolve_reference(response_schema["$ref"], openapi_spec)
                    except ValueError as e:
                        logger.warning(f"Failed to resolve schema reference for {content_type}: {e}")
                        continue
                
                # Generate format for this content type
                format_config = self._generate_single_format(
                    response_schema, content_type, integration_name, crfs_config["__metadata"]
                )
                
                if format_config:
                    formats_by_content_type[content_type] = format_config
                    
                    # Set first valid content type as preferred
                    if preferred_content_type is None:
                        preferred_content_type = content_type
        
        # If no valid formats, return basic template
        if not formats_by_content_type:
            return {
                "application/json": {
                    "kind": "coherence-response",
                    "crfs_version": "2.0",
                    "metadata": crfs_config["__metadata"],
                    "format": {
                        "type": "template",
                        "template": f"The request to {integration_name} was successful.\n\n{{{{ result | json_pretty }}}}"
                    },
                    "error_template": "⚠️ API Error: {{ error.message }}"
                },
                "preferred": "application/json"
            }
        
        # Add preferred content type
        formats_by_content_type["preferred"] = preferred_content_type
        
        return formats_by_content_type
    
    def _generate_single_format(
        self, response_schema: Dict[str, Any], content_type: str, 
        integration_name: str, metadata: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Generate CRFS format for a single content type.
        
        Args:
            response_schema: Schema for this response type
            content_type: Content type (e.g., "application/json")
            integration_name: Name of the API integration
            metadata: Metadata for the endpoint
            
        Returns:
            CRFS format configuration for this content type
        """
        # Initialize CRFS structure
        crfs_config = {
            "kind": "coherence-response",
            "crfs_version": "2.0",
            "metadata": metadata,
            "format": {
                "type": "template",
                "template": f"The request to {integration_name} was successful.\n\n{{{{ result | json_pretty }}}}"
            },
            "error_template": "⚠️ API Error: {{ error.message }}"
        }
        
        # Generate structured format based on response schema
        crfs_config["format"]["type"] = "structured"
        structure = {
            "header": metadata.get("title", "API Response"),
            "sections": []
        }
        crfs_config["format"]["structure"] = structure
        
        # Initialize summary fields
        summary_fields = []
        
        # Process all schemas dynamically - the CRFS formatter handles special characters
        self._process_standard_json_schema(response_schema, structure, summary_fields, crfs_config)
        
        # Always add an error handling section at the end
        structure["sections"].append({
            "type": "conditional",
            "conditions": {
                "error": {
                    "check": "result.get('error') is not None or result.get('success') is False",
                    "style": "error",
                    "content": "Error: {{ result.get('error', {}).get('message', 'Unknown error') }}"
                }
            }
        })
        
        return crfs_config
    
    def _process_standard_json_schema(
        self, response_schema: Dict[str, Any], structure: Dict[str, Any], summary_fields: List[Dict[str, Any]], crfs_config: Dict[str, Any]
    ):
        """Process standard JSON schema to create CRFS sections."""
        # Handle allOf schemas
        if "allOf" in response_schema:
            # Merge all schemas in allOf
            merged_properties = {}
            for sub_schema in response_schema.get("allOf", []):
                if isinstance(sub_schema, dict) and sub_schema.get("type") == "object":
                    merged_properties.update(sub_schema.get("properties", {}))
                elif isinstance(sub_schema, dict) and "properties" in sub_schema:
                    merged_properties.update(sub_schema.get("properties", {}))
            
            # Treat the merged result as an object
            if merged_properties:
                response_schema = {"type": "object", "properties": merged_properties}
        
        # Analyze response schema to create appropriate formatting
        if response_schema.get("type") == "object":
            properties = response_schema.get("properties", {})
            
            # Identify common response patterns
            array_props = []
            object_props = []
            simple_props = []
            
            for prop_name, prop_schema in properties.items():
                prop_type = prop_schema.get("type", "object")
                
                if prop_type == "array":
                    array_props.append((prop_name, prop_schema))
                elif prop_type == "object":
                    object_props.append((prop_name, prop_schema))
                else:
                    simple_props.append((prop_name, prop_schema))
            
            # Add summary fields for common properties
            summary_patterns = ["total", "count", "status", "success", "page", "page_size"]
            for prop_name, prop_schema in simple_props:
                if any(pattern in prop_name.lower() for pattern in summary_patterns):
                    summary_fields.append({
                        "key": prop_name,
                        "label": prop_name.replace("_", " ").title(),
                        "format": self._get_field_format(prop_schema),
                        "value": f"{{{{ result.get('{prop_name}') }}}}"
                    })
            
            # Handle list responses
            if array_props:
                for prop_name, prop_schema in array_props:
                    items_schema = prop_schema.get("items", {})
                    
                    # Handle special JSON-LD properties with @ symbol
                    title = prop_name.replace("_", " ").title()
                    if prop_name.startswith('@'):
                        # For JSON-LD properties, remove @ for title
                        title = prop_name[1:].capitalize()
                        # Use get() method for safe access instead of dot notation
                        data_path = f"result.get('{prop_name}')"
                        show_if = f"result.get('{prop_name}') and len(result.get('{prop_name}', [])) > 0"
                    else:
                        # Direct access to result
                        data_path = f"result.get('{prop_name}')"
                        show_if = f"result.get('{prop_name}') and len(result.get('{prop_name}', [])) > 0"
                    
                    section = {
                        "type": "list",
                        "title": title,
                        "data_path": data_path,
                        "conditionals": {
                            "show_if": show_if
                        }
                    }
                    
                    # Determine item format based on schema
                    if items_schema.get("type") == "object":
                        item_props = items_schema.get("properties", {})
                        key_fields = self._identify_key_fields(item_props)
                        
                        # Create a template for object items
                        item_parts = []
                        for field in key_fields[:5]:
                            display_name = field.replace("_", " ").title()
                            item_parts.append(f"{display_name}: {{{{ item.{field} }}}}")
                        
                        section["item_format"] = {
                            "template": " | ".join(item_parts) if item_parts else "{{ item }}"
                        }
                    else:
                        section["item_format"] = {
                            "template": "{{ item }}"
                        }
                    
                    structure["sections"].append(section)
            
            # Handle object properties
            for prop_name, prop_schema in object_props:
                # Handle special JSON-LD properties with @ symbol
                title = prop_name.replace("_", " ").title()
                if prop_name.startswith('@'):
                    # For JSON-LD properties, remove @ for title
                    title = prop_name[1:].capitalize()
                    # Use get() method for safe access instead of dot notation
                    data_path = f"result.get('{prop_name}')"
                    show_if = f"result.get('{prop_name}') is not None"
                else:
                    # Direct access to result
                    data_path = f"result.get('{prop_name}')"
                    show_if = f"result.get('{prop_name}') is not None"
                
                section = {
                    "type": "object",
                    "title": title,
                    "data_path": data_path,
                    "show_all": True,
                    "conditionals": {
                        "show_if": show_if
                    }
                }
                structure["sections"].append(section)
            
            # Handle remaining simple properties in a details section
            if simple_props:
                details_fields = []
                for prop_name, prop_schema in simple_props:
                    # Skip if already in summary
                    if not any(field["key"] == prop_name for field in summary_fields):
                        details_fields.append({
                            "key": prop_name,
                            "label": prop_name.replace("_", " ").title()
                        })
                
                if details_fields:
                    section = {
                        "type": "object",
                        "title": "Details",
                        "data_path": "result",
                        "fields": details_fields[:10]  # Limit to 10 fields
                    }
                    structure["sections"].append(section)
        
        elif response_schema.get("type") == "array":
            # Direct array response
            items_schema = response_schema.get("items", {})
            
            summary_fields.append({
                "key": "_count",
                "label": "Total Items",
                "format": "number",
                "value": "{{ result | length }}"
            })
            
            section = {
                "type": "list",
                "title": "Results",
                "data_path": "result",
                "item_format": {}
            }
            
            if items_schema.get("type") == "object":
                item_props = items_schema.get("properties", {})
                key_fields = self._identify_key_fields(item_props)
                
                item_parts = []
                for field in key_fields[:5]:
                    display_name = field.replace("_", " ").title()
                    item_parts.append(f"{display_name}: {{{{ item.{field} }}}}")
                
                section["item_format"]["template"] = " | ".join(item_parts) if item_parts else "{{ item }}"
            else:
                section["item_format"]["template"] = "{{ item }}"
            
            structure["sections"].append(section)
        
        else:
            # Simple response (string, number, etc.)
            structure["sections"].append({
                "type": "text",
                "content": "Response: {{ result }}"
            })
        
        # Add summary if we have fields
        if summary_fields:
            crfs_config["summary"] = {"fields": summary_fields}
        
        # Add conditional error section
        structure["sections"].append({
            "type": "conditional",
            "conditions": {
                "error": {
                    "check": "error is not None",
                    "content": "Error: {{ error.message }}",
                    "style": "error"
                }
            }
        })
    
    def _get_field_format(self, schema: Dict[str, Any]) -> str:
        """
        Determine the format type for a field based on its schema.
        
        Args:
            schema: Field schema from OpenAPI spec
            
        Returns:
            Format type for CRFS
        """
        field_type = schema.get("type", "string")
        field_format = schema.get("format", "")
        
        # Map OpenAPI formats to CRFS formats
        if field_type == "integer" or field_type == "number":
            if field_format == "currency" or "price" in schema.get("description", "").lower():
                return "currency"
            elif field_format == "percentage" or "percent" in schema.get("description", "").lower():
                return "percentage"
            else:
                return "number"
        elif field_type == "boolean":
            return "text"
        elif field_type == "string":
            if field_format in ["date", "date-time"]:
                return "datetime"
            else:
                return "text"
        else:
            return "text"
    
    def _identify_key_fields(self, properties: Dict[str, Any]) -> List[str]:
        """
        Identify key fields to display from object properties.
        
        Args:
            properties: Object properties from schema
            
        Returns:
            List of field names to display
        """
        key_fields = []
        
        # Priority order for common field names
        priority_fields = [
            "id", "name", "title", "code", "key",
            "status", "state", "type", "category",
            "date", "created_at", "updated_at",
            "amount", "value", "price", "total",
            "description", "summary"
        ]
        
        # First, add priority fields that exist
        for field in priority_fields:
            if field in properties:
                key_fields.append(field)
                if len(key_fields) >= 5:
                    break
        
        # If we don't have enough fields, add any remaining non-object fields
        if len(key_fields) < 5:
            for prop_name, prop_schema in properties.items():
                if prop_name not in key_fields:
                    prop_type = prop_schema.get("type", "object")
                    if prop_type != "object" and prop_type != "array":
                        key_fields.append(prop_name)
                        if len(key_fields) >= 5:
                            break
        
        return key_fields


async def get_action_generator(db) -> ActionGenerator:
    """Factory function to create an ActionGenerator instance.
    
    Args:
        db: Database session
        
    Returns:
        ActionGenerator instance
    """
    return ActionGenerator(db)
