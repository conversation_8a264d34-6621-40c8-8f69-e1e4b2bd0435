"""
Credential manager for OpenAPI integrations.

This module handles secure storage and retrieval of API credentials
including encryption and key management.
"""

import base64
import json
import logging
import os
import uuid
from typing import Any, Dict, Optional, Tuple

from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.core.config import get_settings
from src.coherence.core.security.kms_provider import (
    KMSProviderError,
    get_kms_provider,
)

logger = logging.getLogger(__name__)


class CredentialError(Exception):
    """Exception raised when credential operations fail."""

    pass


class CredentialManager:
    """
    Manager for secure credential storage and retrieval.

    This class handles:
    1. Encryption and decryption of API credentials
    2. Key management for credential encryption using KMS
    3. Secure storage of sensitive information
    """

    def __init__(self, db: AsyncSession):
        """Initialize the credential manager.

        Args:
            db: Database session for storing and retrieving credentials
        """
        self.db = db
        self.settings = get_settings()

        # Initialize KMS and encryption keys
        self._initialize_keys()

    def _initialize_keys(self) -> None:
        """Initialize encryption keys using KMS.

        Ensures encryption keys are available for credential encryption.
        Uses a KMS provider for secure key management.
        """
        try:
            # Attempt to get a KMS provider based on configuration
            self.kms_provider = get_kms_provider()
            logger.info(f"Using KMS provider: {self.kms_provider.__class__.__name__}")

            # Generate or retrieve data encryption keys
            # In a real production system, these could be cached and rotated periodically
            self.data_keys = {}
            self.current_key_id = "default"

            # We don't actually store the data keys - just the provider reference
            # The actual keys will be generated/retrieved on demand

        except Exception as e:
            # If KMS setup fails in production, this is a critical error
            if self.settings.ENV == "production":
                logger.critical(f"Failed to initialize KMS in production: {str(e)}")
                raise

            # In development, fall back to a local key
            logger.warning(
                f"KMS initialization failed: {str(e)}. Using local key in development."
            )

            # Generate a temporary key for development use only
            logger.warning(
                "Generating a temporary master key for development. This is not secure for production."
            )
            self.master_key = os.urandom(32)
            self.kms_provider = None  # No KMS provider available

            # Set up a simple data key structure for development
            self.data_keys = {"default": self.master_key}
            self.current_key_id = "default"

    async def encrypt_credentials(
        self, credentials: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], str, bytes]:
        """
        Encrypt API credentials for secure storage.

        Args:
            credentials: Dictionary with credentials to encrypt

        Returns:
            Tuple of (encrypted data dict, key ID, nonce)

        Raises:
            CredentialError: If encryption fails
        """
        try:
            # Convert credentials to JSON string
            credentials_json = json.dumps(credentials)
            plaintext = credentials_json.encode()

            # Choose key ID
            key_id = self.current_key_id

            # Encryption context with additional metadata
            context = {
                "purpose": "api_credentials",
                "version": "1",
            }

            # Use KMS if available
            if self.kms_provider:
                try:
                    # Generate a data key using KMS
                    data_key = self.kms_provider.generate_data_key(context=context)

                    # Use the plaintext data key to encrypt the credentials
                    # We'll store the encrypted data key alongside the ciphertext
                    aesgcm = AESGCM(data_key["plaintext"])
                    nonce = os.urandom(12)  # 96 bits
                    ciphertext = aesgcm.encrypt(nonce, plaintext, None)

                    # Encode the encrypted data key and ciphertext for storage
                    encrypted_data = {
                        "encrypted": base64.b64encode(ciphertext).decode(),
                        "encrypted_key": base64.b64encode(
                            data_key["ciphertext"]
                        ).decode(),
                        "nonce": base64.b64encode(nonce).decode(),
                        "kms": True,
                        "version": 2,  # KMS-based format
                    }

                    # Return with empty nonce since it's included in the encrypted_data
                    return encrypted_data, key_id, b""

                except KMSProviderError as e:
                    # If we're in production, fail on KMS errors
                    if self.settings.ENV == "production":
                        logger.error(f"KMS encryption failed in production: {str(e)}")
                        raise

                    # In dev, fall back to local encryption
                    logger.warning(
                        f"KMS encryption failed, falling back to local: {str(e)}"
                    )

            # Local encryption fallback (for development only)
            # Should never run in production due to the checks above
            if not self.kms_provider or self.settings.ENV != "production":
                encryption_key = self.data_keys[key_id]
                nonce = os.urandom(12)  # 96 bits

                # Encrypt using local key
                aesgcm = AESGCM(encryption_key)
                ciphertext = aesgcm.encrypt(nonce, plaintext, None)

                # Encode as base64 for storage
                encrypted_data = {
                    "encrypted": base64.b64encode(ciphertext).decode(),
                    "version": 1,  # Legacy format
                }

                return encrypted_data, key_id, nonce

            # Shouldn't reach here in production
            raise CredentialError("No encryption method available")

        except Exception as e:
            logger.error(f"Credential encryption failed: {str(e)}")
            raise CredentialError(f"Failed to encrypt credentials: {str(e)}") from e

    async def decrypt_credentials(
        self, encrypted_data: Dict[str, Any], key_id: str, nonce: bytes
    ) -> Dict[str, Any]:
        """
        Decrypt API credentials.

        Args:
            encrypted_data: Dictionary with encrypted credentials
            key_id: ID of the key used for encryption
            nonce: Nonce used for encryption (may be empty for KMS-based encryption)

        Returns:
            Dictionary with decrypted credentials

        Raises:
            CredentialError: If decryption fails
        """
        try:
            # Validate input
            if (
                not isinstance(encrypted_data, dict)
                or "encrypted" not in encrypted_data
            ):
                raise CredentialError("Invalid encrypted data format")

            # Check the version to determine decryption method
            version = encrypted_data.get("version", 1)

            # KMS-based decryption (version 2)
            if version == 2 and encrypted_data.get("kms", False):
                if not self.kms_provider:
                    raise CredentialError(
                        "KMS provider not available but required for decryption"
                    )

                try:
                    # Extract components
                    ciphertext = base64.b64decode(encrypted_data["encrypted"])
                    encrypted_key = base64.b64decode(encrypted_data["encrypted_key"])
                    stored_nonce = base64.b64decode(encrypted_data["nonce"])

                    # Encryption context that was used during encryption
                    context = {
                        "purpose": "api_credentials",
                        "version": "1",
                    }

                    # Decrypt the data key using KMS
                    plaintext_key = self.kms_provider.decrypt(
                        encrypted_key, context=context
                    )

                    # Use the decrypted data key to decrypt the credentials
                    aesgcm = AESGCM(plaintext_key)
                    plaintext = aesgcm.decrypt(stored_nonce, ciphertext, None)

                except KMSProviderError as e:
                    logger.error(f"KMS decryption failed: {str(e)}")
                    raise CredentialError(f"KMS decryption failed: {str(e)}") from e

            # Legacy local decryption (version 1)
            else:
                # Get local encryption key
                encryption_key = self.data_keys.get(key_id)
                if not encryption_key:
                    raise CredentialError(f"Encryption key not found: {key_id}")

                ciphertext = base64.b64decode(encrypted_data["encrypted"])

                # Use the provided nonce (legacy format stored it separately)
                if not nonce:
                    raise CredentialError("Nonce is required for legacy decryption")

                # Decrypt the credentials
                aesgcm = AESGCM(encryption_key)
                plaintext = aesgcm.decrypt(nonce, ciphertext, None)

            # Parse JSON
            return json.loads(plaintext.decode())

        except Exception as e:
            logger.error(f"Credential decryption failed: {str(e)}")
            raise CredentialError(f"Failed to decrypt credentials: {str(e)}") from e

    async def store_credentials(
        self, integration_id: uuid.UUID, auth_type: str, credentials: Dict[str, Any]
    ) -> None:
        """
        Securely store API credentials.

        Args:
            integration_id: ID of the API integration
            auth_type: Authentication type
            credentials: Dictionary with credentials to store

        Raises:
            CredentialError: If credential storage fails
        """
        try:
            # Encrypt the credentials
            encrypted_data, key_id, nonce = await self.encrypt_credentials(credentials)

            # Update the database
            await self.db.execute(
                text("""
                UPDATE api_auth_configs
                SET credentials = :credentials,
                    encryption_key_id = :key_id,
                    encryption_nonce = :nonce
                WHERE integration_id = :integration_id
                """),
                {
                    "credentials": encrypted_data,
                    "key_id": key_id,
                    "nonce": nonce,
                    "integration_id": integration_id,
                },
            )

            # If no rows were updated, insert a new record
            if self.db.rowcount == 0:
                await self.db.execute(
                    text("""
                    INSERT INTO api_auth_configs 
                    (integration_id, auth_type, credentials, encryption_key_id, encryption_nonce)
                    VALUES (:integration_id, :auth_type, :credentials, :key_id, :nonce)
                    """),
                    {
                        "integration_id": integration_id,
                        "auth_type": auth_type,
                        "credentials": encrypted_data,
                        "key_id": key_id,
                        "nonce": nonce,
                    },
                )

            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to store credentials: {str(e)}")
            raise CredentialError(f"Failed to store credentials: {str(e)}") from e

    async def retrieve_credentials(
        self, integration_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve and decrypt API credentials.

        Args:
            integration_id: ID of the API integration

        Returns:
            Dictionary with decrypted credentials or None if not found

        Raises:
            CredentialError: If credential retrieval fails
        """
        try:
            # Query the database
            result = await self.db.execute(
                text("""
                SELECT credentials, encryption_key_id, encryption_nonce
                FROM api_auth_configs
                WHERE integration_id = :integration_id
                """),
                {"integration_id": integration_id},
            )

            auth_config = result.fetchone()
            if not auth_config or not auth_config.credentials:
                return None

            # Check if credentials are encrypted
            if (
                isinstance(auth_config.credentials, dict)
                and "encrypted" in auth_config.credentials
                and auth_config.encryption_key_id
                and auth_config.encryption_nonce
            ):
                # Decrypt the credentials
                return await self.decrypt_credentials(
                    encrypted_data=auth_config.credentials,
                    key_id=auth_config.encryption_key_id,
                    nonce=auth_config.encryption_nonce,
                )
            else:
                # Unencrypted credentials (legacy or development)
                logger.warning(
                    f"Unencrypted credentials found for integration: {integration_id}"
                )
                return auth_config.credentials

        except Exception as e:
            logger.error(f"Failed to retrieve credentials: {str(e)}")
            raise CredentialError(f"Failed to retrieve credentials: {str(e)}") from e

    async def update_credentials(
        self, integration_id: uuid.UUID, credentials: Dict[str, Any]
    ) -> None:
        """
        Update specific fields in API credentials.

        Args:
            integration_id: ID of the API integration
            credentials: Dictionary with credential fields to update

        Raises:
            CredentialError: If credential update fails
        """
        try:
            # Retrieve existing credentials
            existing = await self.retrieve_credentials(integration_id)
            if not existing:
                # Nothing to update
                logger.warning(
                    f"No credentials found to update for integration: {integration_id}"
                )
                return

            # Merge with new credentials
            updated = {**existing, **credentials}

            # Get auth type
            result = await self.db.execute(
                "SELECT auth_type FROM api_auth_configs WHERE integration_id = :integration_id",
                {"integration_id": integration_id},
            )
            auth_config = result.fetchone()

            if not auth_config:
                raise CredentialError(
                    f"Auth config not found for integration: {integration_id}"
                )

            # Store the updated credentials
            await self.store_credentials(
                integration_id=integration_id,
                auth_type=auth_config.auth_type,
                credentials=updated,
            )

        except Exception as e:
            logger.error(f"Failed to update credentials: {str(e)}")
            raise CredentialError(f"Failed to update credentials: {str(e)}") from e

    async def delete_credentials(self, integration_id: uuid.UUID) -> None:
        """
        Delete API credentials.

        Args:
            integration_id: ID of the API integration

        Raises:
            CredentialError: If credential deletion fails
        """
        try:
            # Delete from database
            await self.db.execute(
                """
                UPDATE api_auth_configs
                SET credentials = NULL,
                    encryption_key_id = NULL,
                    encryption_nonce = NULL
                WHERE integration_id = :integration_id
                """,
                {"integration_id": integration_id},
            )

            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to delete credentials: {str(e)}")
            raise CredentialError(f"Failed to delete credentials: {str(e)}") from e


# Factory function
async def get_credential_manager(db: AsyncSession) -> CredentialManager:
    """
    Factory function to create a CredentialManager.

    Args:
        db: Database session

    Returns:
        Initialized CredentialManager
    """
    return CredentialManager(db)
