"""
OpenAPI adapter module for importing and processing API specifications.

This module provides functionality for integrating with external APIs:
1. OpenAPI schema parsing and integration
2. Dynamic API action execution
3. Credential management and authentication
4. Circuit breaker for resilience
5. Response caching for performance
6. Health monitoring for reliability
"""

from src.coherence.openapi_adapter.adapter import OpenAPIAdapter
from src.coherence.openapi_adapter.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerError,
)
from src.coherence.openapi_adapter.credential_manager import (
    CredentialManager,
    get_credential_manager,
)
from src.coherence.openapi_adapter.dynamic_executor import (
    DynamicActionExecutor,
    get_dynamic_executor,
)
from src.coherence.openapi_adapter.health_monitor import (
    ApiHealthMonitor,
    ApiStatus,
    get_api_health_monitor,
)
from src.coherence.openapi_adapter.response_cache import (
    ApiResponseCache,
    get_api_response_cache,
)

__all__ = [
    "OpenAPIAdapter", 
    "DynamicActionExecutor", 
    "get_dynamic_executor",
    "CircuitBreaker", 
    "CircuitBreakerError",
    "ApiResponseCache", 
    "get_api_response_cache",
    "ApiHealthMonitor", 
    "ApiStatus", 
    "get_api_health_monitor",
    "CredentialManager", 
    "get_credential_manager"
]
