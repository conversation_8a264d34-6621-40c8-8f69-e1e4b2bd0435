"""
OpenAPI specification adapter implementation.

Since Coherence's adapter system is built around OpenAPI, this adapter
serves as a pass-through implementation that provides validation and
normalization of OpenAPI specifications.
"""

import logging
from typing import Any, Dict, List

from src.coherence.openapi_adapter.spec_adapters.base import EndpointInfo, SpecAdapter

logger = logging.getLogger(__name__)


class OpenAPIAdapter(SpecAdapter):
    """Adapter for OpenAPI specifications (pass-through with validation).
    
    This adapter validates and normalizes OpenAPI specifications but doesn't
    perform format conversion since the system already uses OpenAPI internally.
    """
    
    def adapt(self, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize an OpenAPI specification.
        
        Args:
            spec: The OpenAPI specification to validate
            
        Returns:
            The validated OpenAPI specification (possibly with minor normalization)
            
        Raises:
            ValueError: If the specification is invalid
        """
        # Basic validation
        if not isinstance(spec, dict):
            logger.error("OpenAPI spec must be a valid dictionary")
            raise ValueError("OpenAPI spec must be a valid dictionary")

        # Check for OpenAPI version
        openapi_version = spec.get("openapi", spec.get("swagger", ""))
        if not openapi_version:
            logger.error("Missing OpenAPI/Swagger version")
            raise ValueError("Missing OpenAPI/Swagger version in specification")
            
        # OpenAPI 3.0+ validation
        if "openapi" in spec:
            if not openapi_version.startswith(("3.0", "3.1")):
                logger.warning(f"Unsupported OpenAPI version: {openapi_version}. Using best-effort conversion.")
                
            # Validate required sections
            if "info" not in spec:
                logger.error("Missing 'info' section in OpenAPI spec")
                raise ValueError("Missing 'info' section in OpenAPI spec")
                
            if "paths" not in spec:
                logger.error("Missing 'paths' section in OpenAPI spec")
                raise ValueError("Missing 'paths' section in OpenAPI spec")
                
        # Swagger 2.0 validation and normalization
        elif "swagger" in spec and spec["swagger"] == "2.0":
            logger.info("Converting Swagger 2.0 to OpenAPI 3.0 format")
            spec = self._convert_swagger2_to_openapi3(spec)
            
        return spec
    
    def extract_auth_config(self, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Extract authentication configuration from OpenAPI spec.
        
        Args:
            spec: The OpenAPI specification
            
        Returns:
            Dict containing auth configuration details
        """
        auth_config = {
            "auth_type": None,
            "security_schemes": {}
        }
        
        # Extract security schemes from components section (OpenAPI 3.0+)
        if "components" in spec and "securitySchemes" in spec["components"]:
            security_schemes = spec["components"]["securitySchemes"]
            
            # Determine primary auth type based on security schemes
            for _name, scheme in security_schemes.items():
                if not isinstance(scheme, dict):
                    continue
                    
                scheme_type = scheme.get("type", "").lower()
                
                if scheme_type == "oauth2":
                    auth_config["auth_type"] = "oauth2"
                    auth_config["oauth2_config"] = {
                        "flows": scheme.get("flows", {}),
                        "scopes": self._extract_scopes(scheme)
                    }
                    break
                    
                elif scheme_type == "apikey":
                    auth_config["auth_type"] = "apikey"
                    auth_config["apikey_config"] = {
                        "name": scheme.get("name", ""),
                        "in": scheme.get("in", "header")
                    }
                    break
                    
                elif scheme_type == "http":
                    scheme_scheme = scheme.get("scheme", "").lower()
                    if scheme_scheme == "bearer":
                        auth_config["auth_type"] = "bearer"
                    elif scheme_scheme == "basic":
                        auth_config["auth_type"] = "basic"
                    break
            
            # Store all security schemes for reference
            auth_config["security_schemes"] = security_schemes
            
        # Fallback to securityDefinitions (OpenAPI 2.0)
        elif "securityDefinitions" in spec:
            security_schemes = spec["securityDefinitions"]
            
            # Process in the same way as OpenAPI 3.0
            for _name, scheme in security_schemes.items():
                if not isinstance(scheme, dict):
                    continue
                    
                scheme_type = scheme.get("type", "").lower()
                
                if scheme_type == "oauth2":
                    auth_config["auth_type"] = "oauth2"
                    auth_config["oauth2_config"] = {
                        "flow": scheme.get("flow", ""),
                        "authorization_url": scheme.get("authorizationUrl", ""),
                        "token_url": scheme.get("tokenUrl", ""),
                        "scopes": scheme.get("scopes", {})
                    }
                    break
                    
                elif scheme_type == "apiKey":
                    auth_config["auth_type"] = "apikey"
                    auth_config["apikey_config"] = {
                        "name": scheme.get("name", ""),
                        "in": scheme.get("in", "header")
                    }
                    break
                    
                elif scheme_type == "basic":
                    auth_config["auth_type"] = "basic"
                    break
            
            # Store all security schemes for reference
            auth_config["security_schemes"] = security_schemes
        
        return auth_config
    
    def _extract_scopes(self, scheme: Dict[str, Any]) -> Dict[str, str]:
        """Extract OAuth2 scopes from a security scheme.
        
        Args:
            scheme: The security scheme object
            
        Returns:
            Dictionary of scope names to descriptions
        """
        scopes = {}
        
        if "flows" in scheme:
            flows = scheme["flows"]
            
            # Check common OAuth2 flows
            for flow_type in ["implicit", "password", "clientCredentials", "authorizationCode"]:
                if flow_type in flows and "scopes" in flows[flow_type]:
                    scopes.update(flows[flow_type]["scopes"])
        # OpenAPI 2.0 compatibility
        elif "scopes" in scheme:
            scopes = scheme["scopes"]
        
        return scopes
    
    def extract_endpoints(self, spec: Dict[str, Any]) -> List[EndpointInfo]:
        """Extract endpoints from OpenAPI spec.
        
        Args:
            spec: The OpenAPI specification
            
        Returns:
            List of endpoint information objects
        """
        endpoints = []
        paths = spec.get("paths", {})
        
        for path, path_item in paths.items():
            if not isinstance(path_item, dict):
                continue
                
            for method, operation in path_item.items():
                if method not in ["get", "post", "put", "delete", "patch", "options", "head"]:
                    continue
                    
                if not isinstance(operation, dict):
                    continue
                
                # Generate operation ID if missing
                operation_id = operation.get("operationId") or f"{method}_{path.replace('/', '_')}"
                
                # Extract parameters, adjusting for OpenAPI 2.0 vs 3.0
                parameters = operation.get("parameters", [])
                request_body = operation.get("requestBody")
                
                # In OpenAPI 2.0, body parameters are in parameters array
                if not request_body and parameters:
                    for param in parameters:
                        if isinstance(param, dict) and param.get("in") == "body":
                            request_body = {
                                "description": param.get("description", ""),
                                "required": param.get("required", False),
                                "content": {
                                    "application/json": {
                                        "schema": param.get("schema", {})
                                    }
                                }
                            }
                            # Remove body param from parameters list
                            parameters = [p for p in parameters if not (isinstance(p, dict) and p.get("in") == "body")]
                            break
                
                endpoint = EndpointInfo(
                    path=path,
                    method=method.upper(),
                    operation_id=operation_id,
                    summary=operation.get("summary", ""),
                    description=operation.get("description", ""),
                    parameters=parameters,
                    request_body=request_body,
                    responses=operation.get("responses", {}),
                    security=operation.get("security", []),
                    deprecated=operation.get("deprecated", False),
                    tags=operation.get("tags", [])
                )
                
                endpoints.append(endpoint)
        
        return endpoints
    
    def _convert_swagger2_to_openapi3(self, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Convert a Swagger 2.0 specification to OpenAPI 3.0 format.
        
        Args:
            spec: The Swagger 2.0 specification
            
        Returns:
            OpenAPI 3.0 compatible specification
        """
        # Create a copy to avoid modifying the original
        result = spec.copy()
        
        # Update version
        result.pop("swagger", None)
        result["openapi"] = "3.0.0"
        
        # Migrate security definitions to components
        if "securityDefinitions" in result:
            if "components" not in result:
                result["components"] = {}
                
            result["components"]["securitySchemes"] = result.pop("securityDefinitions")
        
        # Migrate path parameters to the current convention
        if "paths" in result:
            for _path, path_item in result["paths"].items():
                if not isinstance(path_item, dict):
                    continue
                    
                for method, operation in path_item.items():
                    if method not in ["get", "post", "put", "delete", "patch", "options", "head"]:
                        continue
                        
                    if not isinstance(operation, dict):
                        continue
                    
                    # Convert consumes/produces to requestBody and responses
                    consumes = operation.pop("consumes", None) or spec.get("consumes")
                    produces = operation.pop("produces", None) or spec.get("produces")
                    
                    # Migrate parameters for request body
                    if "parameters" in operation:
                        body_param = None
                        for param in operation["parameters"]:
                            if param.get("in") == "body":
                                body_param = param
                                break
                                
                        if body_param:
                            content_type = consumes[0] if consumes else "application/json"
                            operation["requestBody"] = {
                                "description": body_param.get("description", ""),
                                "required": body_param.get("required", False),
                                "content": {
                                    content_type: {
                                        "schema": body_param.get("schema", {})
                                    }
                                }
                            }
                            
                            # Remove body param from parameters
                            operation["parameters"] = [
                                p for p in operation["parameters"] 
                                if p.get("in") != "body"
                            ]
                            
                    # Update response objects
                    if "responses" in operation:
                        for _status_code, response in operation["responses"].items():
                            if isinstance(response, dict) and "schema" in response:
                                content_type = produces[0] if produces else "application/json"
                                schema = response.pop("schema")
                                response["content"] = {
                                    content_type: {
                                        "schema": schema
                                    }
                                }
        
        return result