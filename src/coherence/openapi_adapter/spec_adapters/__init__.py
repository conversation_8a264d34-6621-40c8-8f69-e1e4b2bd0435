"""
Adapter classes for converting different API specification formats.

This package provides a flexible adapter system for different API specification
formats including OpenAPI, FAPI (Financial-grade API), and BAPI (Business API).
"""

from src.coherence.openapi_adapter.spec_adapters.adapter_factory import (
    SpecAdapterFactory,
)
from src.coherence.openapi_adapter.spec_adapters.bapi_adapter import BAPIAdapter
from src.coherence.openapi_adapter.spec_adapters.base import EndpointInfo, SpecAdapter
from src.coherence.openapi_adapter.spec_adapters.fapi_adapter import FAPIAdapter
from src.coherence.openapi_adapter.spec_adapters.openapi_adapter import OpenAPIAdapter

__all__ = [
    "SpecAdapter",
    "EndpointInfo",
    "SpecAdapterFactory",
    "OpenAPIAdapter",
    "FAPIAdapter",
    "BAPIAdapter",
]