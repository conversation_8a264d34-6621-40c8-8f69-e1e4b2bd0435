"""
Base adapter interface for API specification formats.

Defines the common interface that all API specification format adapters must implement.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class EndpointInfo(BaseModel):
    """Standardized endpoint information model.
    
    This model normalizes endpoint information across different API specification formats.
    """
    path: str
    method: str
    operation_id: str
    summary: str = ""
    description: str = ""
    parameters: List[Dict[str, Any]] = []
    request_body: Optional[Dict[str, Any]] = None
    responses: Dict[str, Any] = {}
    security: List[Dict[str, Any]] = []
    deprecated: bool = False
    tags: List[str] = []
    
    class Config:
        """Pydantic model configuration."""
        extra = "allow"  # Allow extra fields for format-specific information


class SpecAdapter(ABC):
    """Base abstract adapter for converting API specifications to standardized format.
    
    This abstract class defines the interface that all API specification format adapters
    must implement to convert specifications to the OpenAPI format used by Coherence.
    """
    
    @abstractmethod
    def adapt(self, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Convert a specification to OpenAPI 3.0 format.
        
        Args:
            spec: The original API specification
            
        Returns:
            Dict containing an OpenAPI 3.0 compatible specification
            
        Raises:
            ValueError: If the specification is invalid or cannot be converted
        """
        pass
    
    @abstractmethod
    def extract_auth_config(self, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Extract authentication configuration from the specification.
        
        Args:
            spec: The original API specification
            
        Returns:
            Dict containing auth configuration details including:
            - auth_type: Primary authentication type
            - security_schemes: All available security schemes
            - Additional format-specific auth details
            
        Raises:
            ValueError: If auth configuration cannot be extracted
        """
        pass
    
    @abstractmethod
    def extract_endpoints(self, spec: Dict[str, Any]) -> List[EndpointInfo]:
        """Extract endpoints from the specification.
        
        Args:
            spec: The original API specification
            
        Returns:
            List of endpoint information objects with standardized structure
            
        Raises:
            ValueError: If endpoints cannot be extracted
        """
        pass