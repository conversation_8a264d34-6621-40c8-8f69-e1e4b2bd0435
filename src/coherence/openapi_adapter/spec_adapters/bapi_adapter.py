"""
BAPI (Business API) specification adapter implementation.

Converts BAPI specifications to OpenAPI 3.0 format compatible with Coherence.
"""

import logging
from typing import Any, Dict, List

from src.coherence.openapi_adapter.spec_adapters.base import EndpointInfo, SpecAdapter

logger = logging.getLogger(__name__)


class BAPIAdapter(SpecAdapter):
    """Adapter for Business API (BAPI) specifications.
    
    This adapter converts BAPI specifications to OpenAPI 3.0 format,
    handling the service-based organization and business operation models
    defined by the Business API specification.
    """
    
    def adapt(self, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Convert BAPI specification to OpenAPI 3.0 format.
        
        Args:
            spec: The BAPI specification to convert
            
        Returns:
            OpenAPI 3.0 compatible specification
            
        Raises:
            ValueError: If the specification is invalid or cannot be converted
        """
        # Basic validation
        if not isinstance(spec, dict):
            logger.error("BAPI spec must be a valid dictionary")
            raise ValueError("BAPI spec must be a valid dictionary")
            
        # Create a base OpenAPI structure
        openapi_spec = {
            "openapi": "3.0.0",
            "info": {
                "title": spec.get("info", {}).get("title", "BAPI API"),
                "version": spec.get("info", {}).get("version", "1.0.0"),
                "description": spec.get("info", {}).get("description", "Converted from BAPI specification")
            },
            "paths": {},
            "components": {
                "schemas": {},
                "securitySchemes": {}
            }
        }
        
        # Copy over servers if present
        if "servers" in spec:
            openapi_spec["servers"] = spec["servers"]
        
        # Map BAPI services to OpenAPI paths
        if "services" in spec:
            for service in spec["services"]:
                if not isinstance(service, dict):
                    continue
                    
                service_name = service.get("name", "")
                
                if "operations" not in service or not service_name:
                    continue
                
                for operation in service["operations"]:
                    if not isinstance(operation, dict):
                        continue
                        
                    operation_name = operation.get("name", "")
                    if not operation_name:
                        continue
                        
                    method = operation.get("method", "post").lower()
                    
                    # Construct path from service and operation names unless explicitly provided
                    path = operation.get("path", f"/{service_name}/{operation_name}")
                    
                    if path not in openapi_spec["paths"]:
                        openapi_spec["paths"][path] = {}
                    
                    # Convert operation to OpenAPI operation
                    openapi_spec["paths"][path][method] = {
                        "operationId": operation.get("id", operation_name),
                        "summary": operation.get("summary", operation_name),
                        "description": operation.get("description", ""),
                        "parameters": self._convert_parameters(operation.get("parameters", [])),
                        "responses": self._convert_responses(operation.get("responses", {})),
                        "tags": operation.get("tags", [service_name]) if "tags" in operation else [service_name]
                    }
                    
                    # Add request body if input is defined
                    if "input" in operation:
                        openapi_spec["paths"][path][method]["requestBody"] = {
                            "content": {
                                "application/json": {
                                    "schema": operation["input"]
                                }
                            },
                            "required": operation.get("input_required", True)
                        }
                    
                    # Add security requirements if present
                    if "security" in operation:
                        openapi_spec["paths"][path][method]["security"] = operation["security"]
        
        # Map BAPI authentication to OpenAPI security schemes
        if "authentication" in spec:
            auth_methods = spec["authentication"]
            
            for auth_name, auth_method in auth_methods.items():
                if not isinstance(auth_method, dict):
                    continue
                    
                auth_type = auth_method.get("type", "").lower()
                
                if auth_type == "apikey":
                    openapi_spec["components"]["securitySchemes"][auth_name] = {
                        "type": "apiKey",
                        "in": auth_method.get("in", "header"),
                        "name": auth_method.get("name", "X-API-Key"),
                        "description": auth_method.get("description", "")
                    }
                elif auth_type == "oauth2":
                    openapi_spec["components"]["securitySchemes"][auth_name] = {
                        "type": "oauth2",
                        "flows": self._convert_oauth_flows(auth_method.get("flows", {})),
                        "description": auth_method.get("description", "")
                    }
                elif auth_type == "basic":
                    openapi_spec["components"]["securitySchemes"][auth_name] = {
                        "type": "http",
                        "scheme": "basic",
                        "description": auth_method.get("description", "")
                    }
                # Handle JWT auth (common in BAPI)
                elif auth_type == "jwt":
                    openapi_spec["components"]["securitySchemes"][auth_name] = {
                        "type": "http",
                        "scheme": "bearer",
                        "bearerFormat": "JWT",
                        "description": auth_method.get("description", "")
                    }
        
        # Add any data models from the BAPI spec
        if "models" in spec:
            for model_name, model in spec["models"].items():
                openapi_spec["components"]["schemas"][model_name] = model
        
        # Add BAPI-specific extensions
        openapi_spec["x-bapi-version"] = spec.get("business_api", spec.get("bapi_version", "1.0"))
        if "domain" in spec:
            openapi_spec["x-bapi-domain"] = spec["domain"]
        
        return openapi_spec
    
    def _convert_parameters(self, bapi_params: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert BAPI parameters to OpenAPI parameters.
        
        Args:
            bapi_params: List of BAPI parameters
            
        Returns:
            List of OpenAPI-compatible parameters
        """
        openapi_params = []
        
        for param in bapi_params:
            if not isinstance(param, dict):
                continue
                
            openapi_param = {
                "name": param.get("name", ""),
                "in": param.get("in", "query"),
                "required": param.get("required", False),
                "description": param.get("description", "")
            }
            
            # Add schema if present
            if "schema" in param:
                openapi_param["schema"] = param["schema"]
            elif "type" in param:
                # Create simple schema from type
                openapi_param["schema"] = {
                    "type": param["type"]
                }
                
                if "format" in param:
                    openapi_param["schema"]["format"] = param["format"]
                
                # Add enum values if present
                if "enum" in param:
                    openapi_param["schema"]["enum"] = param["enum"]
            
            openapi_params.append(openapi_param)
        
        return openapi_params
    
    def _convert_responses(self, bapi_responses: Dict[str, Any]) -> Dict[str, Any]:
        """Convert BAPI responses to OpenAPI responses.
        
        Args:
            bapi_responses: Dict of BAPI response objects
            
        Returns:
            Dict of OpenAPI-compatible responses
        """
        openapi_responses = {}
        
        for status_code, response in bapi_responses.items():
            if not isinstance(response, dict):
                continue
                
            openapi_responses[status_code] = {
                "description": response.get("description", ""),
            }
            
            # Add content if output is defined
            if "output" in response:
                openapi_responses[status_code]["content"] = {
                    response.get("content_type", "application/json"): {
                        "schema": response["output"]
                    }
                }
            
            # Add headers if defined
            if "headers" in response:
                openapi_responses[status_code]["headers"] = response["headers"]
        
        return openapi_responses
    
    def _convert_oauth_flows(self, bapi_flows: Dict[str, Any]) -> Dict[str, Any]:
        """Convert BAPI OAuth flows to OpenAPI OAuth flows.
        
        Args:
            bapi_flows: Dict of BAPI OAuth flow objects
            
        Returns:
            Dict of OpenAPI-compatible OAuth flows
        """
        openapi_flows = {}
        
        # Map BAPI flow names to OpenAPI flow names
        flow_mapping = {
            "authorization_code": "authorizationCode",
            "client_credentials": "clientCredentials",
            "implicit": "implicit"
        }
        
        for bapi_flow_name, flow in bapi_flows.items():
            if not isinstance(flow, dict):
                continue
                
            openapi_flow_name = flow_mapping.get(bapi_flow_name, bapi_flow_name)
            
            openapi_flows[openapi_flow_name] = {
                "scopes": flow.get("scopes", {})
            }
            
            # Add authorization URL if present
            if "authorization_url" in flow:
                openapi_flows[openapi_flow_name]["authorizationUrl"] = flow["authorization_url"]
            
            # Add token URL if present
            if "token_url" in flow:
                openapi_flows[openapi_flow_name]["tokenUrl"] = flow["token_url"]
            
            # Add refresh URL if present
            if "refresh_url" in flow:
                openapi_flows[openapi_flow_name]["refreshUrl"] = flow["refresh_url"]
        
        return openapi_flows
    
    def extract_auth_config(self, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Extract authentication configuration from BAPI spec.
        
        Args:
            spec: The BAPI specification
            
        Returns:
            Dict containing auth configuration details
        """
        auth_config = {
            "auth_type": None,
            "security_schemes": {}
        }
        
        # Extract authentication methods
        if "authentication" in spec:
            auth_methods = spec["authentication"]
            
            # First check for OAuth2
            for _name, method in auth_methods.items():
                if not isinstance(method, dict):
                    continue
                    
                auth_type = method.get("type", "").lower()
                
                if auth_type == "oauth2":
                    auth_config["auth_type"] = "oauth2"
                    auth_config["oauth2_config"] = {
                        "flows": self._convert_oauth_flows(method.get("flows", {})),
                        "scopes": self._extract_bapi_scopes(method)
                    }
                    break
                
                # Then check for JWT bearer
                elif auth_type == "jwt":
                    auth_config["auth_type"] = "bearer"
                    auth_config["jwt_config"] = {
                        "format": "JWT",
                        "alg": method.get("algorithm", "RS256"),
                        "audience": method.get("audience")
                    }
                    break
                
                # Then check for API Key
                elif auth_type == "apikey":
                    auth_config["auth_type"] = "apikey"
                    auth_config["apikey_config"] = {
                        "name": method.get("name", "X-API-Key"),
                        "in": method.get("in", "header")
                    }
                    break
                
                # Then check for Basic Auth
                elif auth_type == "basic":
                    auth_config["auth_type"] = "basic"
                    break
            
            # Store all auth methods for reference
            auth_config["security_schemes"] = auth_methods
        
        return auth_config
    
    def _extract_bapi_scopes(self, method: Dict[str, Any]) -> Dict[str, str]:
        """Extract OAuth2 scopes from BAPI authentication method.
        
        Args:
            method: The BAPI authentication method
            
        Returns:
            Dict of scope names to descriptions
        """
        scopes = {}
        
        if "flows" in method:
            flows = method["flows"]
            
            for _flow_name, flow in flows.items():
                if isinstance(flow, dict) and "scopes" in flow:
                    scopes.update(flow["scopes"])
        
        return scopes
    
    def extract_endpoints(self, spec: Dict[str, Any]) -> List[EndpointInfo]:
        """Extract endpoints from BAPI spec.
        
        Args:
            spec: The BAPI specification
            
        Returns:
            List of endpoint information objects
        """
        endpoints = []
        
        if "services" in spec:
            for service in spec["services"]:
                if not isinstance(service, dict):
                    continue
                    
                service_name = service.get("name", "")
                
                if "operations" not in service or not service_name:
                    continue
                
                for operation in service["operations"]:
                    if not isinstance(operation, dict):
                        continue
                        
                    operation_name = operation.get("name", "")
                    if not operation_name:
                        continue
                        
                    method = operation.get("method", "post").upper()
                    
                    # Construct path from service and operation names unless explicitly provided
                    path = operation.get("path", f"/{service_name}/{operation_name}")
                    
                    # Create request body object if input schema is provided
                    request_body = None
                    if "input" in operation:
                        request_body = {
                            "content": {
                                "application/json": {
                                    "schema": operation["input"]
                                }
                            },
                            "required": operation.get("input_required", True)
                        }
                    
                    # Create responses object
                    responses = {}
                    for status_code, response in operation.get("responses", {}).items():
                        if not isinstance(response, dict):
                            continue
                            
                        resp_obj = {
                            "description": response.get("description", "")
                        }
                        
                        # Add content if output schema is present
                        if "output" in response:
                            resp_obj["content"] = {
                                response.get("content_type", "application/json"): {
                                    "schema": response["output"]
                                }
                            }
                        
                        responses[status_code] = resp_obj
                    
                    endpoint = EndpointInfo(
                        path=path,
                        method=method,
                        operation_id=operation.get("id", operation_name),
                        summary=operation.get("summary", operation_name),
                        description=operation.get("description", ""),
                        parameters=operation.get("parameters", []),
                        request_body=request_body,
                        responses=responses,
                        security=operation.get("security", []),
                        deprecated=operation.get("deprecated", False),
                        tags=operation.get("tags", [service_name]) if "tags" in operation else [service_name],
                        # BAPI-specific fields
                        bapi_service=service_name,
                        bapi_operation=operation_name,
                        bapi_domain=spec.get("domain"),
                        bapi_idempotent=operation.get("idempotent", False)
                    )
                    
                    endpoints.append(endpoint)
        
        return endpoints