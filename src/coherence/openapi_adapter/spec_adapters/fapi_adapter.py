"""
FAPI (Financial-grade API) specification adapter implementation.

Converts FAPI specifications to OpenAPI 3.0 format compatible with Coherence.
"""

import logging
from typing import Any, Dict, List

from src.coherence.openapi_adapter.spec_adapters.base import EndpointInfo, SpecAdapter

logger = logging.getLogger(__name__)


class FAPIAdapter(SpecAdapter):
    """Adapter for Financial-grade API (FAPI) specifications.
    
    This adapter converts FAPI specifications to OpenAPI 3.0 format,
    handling the special security and authentication requirements defined
    by the Financial-grade API specification.
    """
    
    def adapt(self, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Convert FAPI specification to OpenAPI 3.0 format.
        
        Args:
            spec: The FAPI specification to convert
            
        Returns:
            OpenAPI 3.0 compatible specification
            
        Raises:
            ValueError: If the specification is invalid or cannot be converted
        """
        # Basic validation
        if not isinstance(spec, dict):
            logger.error("FAPI spec must be a valid dictionary")
            raise ValueError("FAPI spec must be a valid dictionary")
            
        # Create a base OpenAPI structure
        openapi_spec = {
            "openapi": "3.0.0",
            "info": {
                "title": spec.get("info", {}).get("title", "FAPI API"),
                "version": spec.get("info", {}).get("version", "1.0.0"),
                "description": spec.get("info", {}).get("description", "Converted from FAPI specification")
            },
            "paths": {},
            "components": {
                "schemas": {},
                "securitySchemes": {}
            }
        }
        
        # Copy over servers if present
        if "servers" in spec:
            openapi_spec["servers"] = spec["servers"]
        elif "host" in spec:
            # Handle server from host (legacy format)
            scheme = spec.get("schemes", ["https"])[0] if "schemes" in spec else "https"
            host = spec["host"]
            base_path = spec.get("basePath", "")
            openapi_spec["servers"] = [
                {"url": f"{scheme}://{host}{base_path}"}
            ]
        
        # Map FAPI paths to OpenAPI paths
        if "endpoints" in spec:
            for endpoint in spec["endpoints"]:
                if not isinstance(endpoint, dict):
                    continue
                    
                path = endpoint.get("path", "")
                method = endpoint.get("method", "").lower()
                
                if not path or not method:
                    continue
                
                if path not in openapi_spec["paths"]:
                    openapi_spec["paths"][path] = {}
                
                # Convert endpoint to OpenAPI operation
                openapi_spec["paths"][path][method] = {
                    "operationId": endpoint.get("id", f"{method}_{path.replace('/', '_')}"),
                    "summary": endpoint.get("summary", ""),
                    "description": endpoint.get("description", ""),
                    "parameters": self._convert_parameters(endpoint.get("parameters", [])),
                    "responses": self._convert_responses(endpoint.get("responses", {})),
                    "tags": endpoint.get("tags", [])
                }
                
                # Add request body if present
                if "request_body" in endpoint:
                    openapi_spec["paths"][path][method]["requestBody"] = self._convert_request_body(
                        endpoint["request_body"]
                    )
                
                # Add security requirements if present
                if "security" in endpoint:
                    openapi_spec["paths"][path][method]["security"] = endpoint["security"]
        
        # Map FAPI security to OpenAPI security schemes
        if "security_profiles" in spec:
            for profile_name, profile in spec["security_profiles"].items():
                if not isinstance(profile, dict):
                    continue
                    
                profile_type = profile.get("type", "").lower()
                
                if profile_type == "oauth2":
                    openapi_spec["components"]["securitySchemes"][profile_name] = {
                        "type": "oauth2",
                        "flows": self._convert_oauth_flows(profile.get("flows", {})),
                        "description": profile.get("description", ""),
                        "x-fapi-profile": profile.get("profile", ""),
                        "x-fapi-requires-pkce": profile.get("requires_pkce", False)
                    }
                elif profile_type == "mtls":
                    # Map MTLS to standard OpenAPI http with custom x-mtls extension
                    openapi_spec["components"]["securitySchemes"][profile_name] = {
                        "type": "http",
                        "scheme": "bearer",
                        "description": "Mutual TLS Authentication",
                        "x-mtls": True
                    }
                # Handle other FAPI-specific security types
                elif profile_type == "jwsig":
                    openapi_spec["components"]["securitySchemes"][profile_name] = {
                        "type": "http",
                        "scheme": "bearer",
                        "description": "JWS signature verification",
                        "x-jws-signature": profile.get("signature_type", "detached")
                    }
        
        # Add any data models from the FAPI spec
        if "data_models" in spec:
            for model_name, model in spec["data_models"].items():
                openapi_spec["components"]["schemas"][model_name] = model
        
        # Add FAPI-specific extensions
        openapi_spec["x-fapi-version"] = spec.get("financial_api", spec.get("fapi_version", "1.0"))
        if "fapi_security_profile" in spec:
            openapi_spec["x-fapi-security-profile"] = spec["fapi_security_profile"]
        
        return openapi_spec
    
    def _convert_parameters(self, fapi_params: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert FAPI parameters to OpenAPI parameters.
        
        Args:
            fapi_params: List of FAPI parameters
            
        Returns:
            List of OpenAPI-compatible parameters
        """
        openapi_params = []
        
        for param in fapi_params:
            if not isinstance(param, dict):
                continue
                
            openapi_param = {
                "name": param.get("name", ""),
                "in": param.get("location", param.get("in", "query")),
                "required": param.get("required", False),
                "description": param.get("description", "")
            }
            
            # Add schema if present
            if "schema" in param:
                openapi_param["schema"] = param["schema"]
            elif "type" in param:
                # Create simple schema from type
                openapi_param["schema"] = {
                    "type": param["type"]
                }
                
                if "format" in param:
                    openapi_param["schema"]["format"] = param["format"]
            
            openapi_params.append(openapi_param)
        
        return openapi_params
    
    def _convert_request_body(self, fapi_request_body: Dict[str, Any]) -> Dict[str, Any]:
        """Convert FAPI request body to OpenAPI request body.
        
        Args:
            fapi_request_body: FAPI request body object
            
        Returns:
            OpenAPI-compatible request body
        """
        if not isinstance(fapi_request_body, dict):
            return {}
            
        content_type = fapi_request_body.get("content_type", "application/json")
        schema = fapi_request_body.get("schema", {})
        
        return {
            "description": fapi_request_body.get("description", ""),
            "required": fapi_request_body.get("required", False),
            "content": {
                content_type: {
                    "schema": schema
                }
            }
        }
    
    def _convert_responses(self, fapi_responses: Dict[str, Any]) -> Dict[str, Any]:
        """Convert FAPI responses to OpenAPI responses.
        
        Args:
            fapi_responses: Dict of FAPI response objects
            
        Returns:
            Dict of OpenAPI-compatible responses
        """
        openapi_responses = {}
        
        for status_code, response in fapi_responses.items():
            if not isinstance(response, dict):
                continue
                
            openapi_responses[status_code] = {
                "description": response.get("description", ""),
            }
            
            # Add content if present
            if "content_type" in response and "schema" in response:
                openapi_responses[status_code]["content"] = {
                    response["content_type"]: {
                        "schema": response["schema"]
                    }
                }
            # Support FAPI response with output field
            elif "output" in response:
                openapi_responses[status_code]["content"] = {
                    "application/json": {
                        "schema": response["output"]
                    }
                }
        
        return openapi_responses
    
    def _convert_oauth_flows(self, fapi_flows: Dict[str, Any]) -> Dict[str, Any]:
        """Convert FAPI OAuth flows to OpenAPI OAuth flows.
        
        Args:
            fapi_flows: Dict of FAPI OAuth flow objects
            
        Returns:
            Dict of OpenAPI-compatible OAuth flows
        """
        openapi_flows = {}
        
        # Map FAPI flow names to OpenAPI flow names
        flow_mapping = {
            "authorization_code": "authorizationCode",
            "client_credentials": "clientCredentials",
            "implicit": "implicit",
            "password": "password"
        }
        
        for fapi_flow_name, flow in fapi_flows.items():
            if not isinstance(flow, dict):
                continue
                
            openapi_flow_name = flow_mapping.get(fapi_flow_name, fapi_flow_name)
            
            openapi_flows[openapi_flow_name] = {
                "scopes": flow.get("scopes", {})
            }
            
            # Add authorization URL if present
            if "authorization_url" in flow:
                openapi_flows[openapi_flow_name]["authorizationUrl"] = flow["authorization_url"]
            
            # Add token URL if present
            if "token_url" in flow:
                openapi_flows[openapi_flow_name]["tokenUrl"] = flow["token_url"]
            
            # Add refresh URL if present
            if "refresh_url" in flow:
                openapi_flows[openapi_flow_name]["refreshUrl"] = flow["refresh_url"]
        
        return openapi_flows
    
    def extract_auth_config(self, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Extract authentication configuration from FAPI spec.
        
        Args:
            spec: The FAPI specification
            
        Returns:
            Dict containing auth configuration details
        """
        auth_config = {
            "auth_type": None,
            "security_schemes": {}
        }
        
        # Extract security profiles
        if "security_profiles" in spec:
            # First, check for OAuth2 (most common in FAPI)
            for _name, profile in spec["security_profiles"].items():
                if not isinstance(profile, dict):
                    continue
                    
                profile_type = profile.get("type", "").lower()
                
                if profile_type == "oauth2":
                    auth_config["auth_type"] = "oauth2"
                    auth_config["oauth2_config"] = {
                        "flows": self._convert_oauth_flows(profile.get("flows", {})),
                        "scopes": self._extract_fapi_scopes(profile)
                    }
                    # Store additional FAPI-specific OAuth2 attributes
                    auth_config["oauth2_config"]["fapi_profile"] = profile.get("profile", "")
                    auth_config["oauth2_config"]["requires_pkce"] = profile.get("requires_pkce", False)
                    auth_config["oauth2_config"]["token_endpoint_auth_method"] = profile.get(
                        "token_endpoint_auth_method", "client_secret_basic"
                    )
                    break
                
                # Check for MTLS (common in FAPI)
                elif profile_type == "mtls":
                    auth_config["auth_type"] = "mtls"
                    auth_config["mtls_config"] = {
                        "client_cert_required": True,
                        "description": profile.get("description", "")
                    }
                    break
                    
                # Check for JWS Signature (used in FAPI)
                elif profile_type == "jwsig":
                    auth_config["auth_type"] = "bearer"
                    auth_config["jws_config"] = {
                        "signature_type": profile.get("signature_type", "detached"),
                        "required_headers": profile.get("required_headers", [])
                    }
                    break
            
            # Store all security profiles for reference
            auth_config["security_schemes"] = spec["security_profiles"]
        
        return auth_config
    
    def _extract_fapi_scopes(self, profile: Dict[str, Any]) -> Dict[str, str]:
        """Extract OAuth2 scopes from FAPI security profile.
        
        Args:
            profile: The FAPI security profile
            
        Returns:
            Dict of scope names to descriptions
        """
        scopes = {}
        
        if "flows" in profile:
            flows = profile["flows"]
            
            for _flow_name, flow in flows.items():
                if isinstance(flow, dict) and "scopes" in flow:
                    scopes.update(flow["scopes"])
        
        return scopes
    
    def extract_endpoints(self, spec: Dict[str, Any]) -> List[EndpointInfo]:
        """Extract endpoints from FAPI spec.
        
        Args:
            spec: The FAPI specification
            
        Returns:
            List of endpoint information objects
        """
        endpoints = []
        
        if "endpoints" in spec:
            for endpoint_data in spec["endpoints"]:
                if not isinstance(endpoint_data, dict):
                    continue
                    
                path = endpoint_data.get("path", "")
                method = endpoint_data.get("method", "").upper()
                
                if not path or not method:
                    continue
                
                # Create request body object if needed
                request_body = None
                if "request_body" in endpoint_data:
                    request_body = {
                        "description": endpoint_data["request_body"].get("description", ""),
                        "required": endpoint_data["request_body"].get("required", False),
                        "content": {
                            endpoint_data["request_body"].get("content_type", "application/json"): {
                                "schema": endpoint_data["request_body"].get("schema", {})
                            }
                        }
                    }
                
                # Create standardized responses
                responses = {}
                for status_code, response in endpoint_data.get("responses", {}).items():
                    if not isinstance(response, dict):
                        continue
                        
                    resp_obj = {
                        "description": response.get("description", "")
                    }
                    
                    # Add content if present
                    if "content_type" in response and "schema" in response:
                        resp_obj["content"] = {
                            response["content_type"]: {
                                "schema": response["schema"]
                            }
                        }
                    
                    responses[status_code] = resp_obj
                
                endpoint = EndpointInfo(
                    path=path,
                    method=method,
                    operation_id=endpoint_data.get("id", f"{method.lower()}_{path.replace('/', '_')}"),
                    summary=endpoint_data.get("summary", ""),
                    description=endpoint_data.get("description", ""),
                    parameters=endpoint_data.get("parameters", []),
                    request_body=request_body,
                    responses=responses,
                    security=endpoint_data.get("security", []),
                    deprecated=endpoint_data.get("deprecated", False),
                    tags=endpoint_data.get("tags", []),
                    # FAPI-specific fields
                    fapi_requires_consent=endpoint_data.get("requires_consent", False),
                    fapi_idempotent=endpoint_data.get("idempotent", False)
                )
                
                endpoints.append(endpoint)
        
        return endpoints