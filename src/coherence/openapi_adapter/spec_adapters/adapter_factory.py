"""
Factory for creating the appropriate specification adapter.

This module provides a factory class for creating specification adapters
based on the detected format of an API specification.
"""

import logging
from typing import Any, Dict

from src.coherence.openapi_adapter.spec_adapters.bapi_adapter import BAPIAdapter
from src.coherence.openapi_adapter.spec_adapters.base import SpecAdapter
from src.coherence.openapi_adapter.spec_adapters.fapi_adapter import FAPIAdapter
from src.coherence.openapi_adapter.spec_adapters.openapi_adapter import OpenAPIAdapter
from src.coherence.services.spec_format_detector import SpecFormat, SpecFormatDetector

logger = logging.getLogger(__name__)


class SpecAdapterFactory:
    """Factory for creating the appropriate specification adapter.
    
    This factory detects the format of an API specification and returns
    the appropriate adapter for converting it to OpenAPI 3.0 format.
    """
    
    @staticmethod
    def create_adapter(spec: Dict[str, Any]) -> SpecAdapter:
        """Create the appropriate adapter based on the specification format.
        
        Args:
            spec: The API specification
            
        Returns:
            SpecAdapter: The appropriate adapter for the specification
            
        Raises:
            ValueError: If the specification format is not supported
        """
        spec_format = SpecFormatDetector.detect_format(spec)
        logger.info(f"Detected specification format: {spec_format}")
        
        if spec_format == SpecFormat.OPENAPI:
            return OpenAPIAdapter()
        elif spec_format == SpecFormat.FAPI:
            return FAPIAdapter()
        elif spec_format == SpecFormat.BAPI:
            return BAPIAdapter()
        else:
            logger.error(f"Unsupported specification format: {spec_format}")
            raise ValueError(f"Unsupported specification format: {spec_format}")
    
    @staticmethod
    def create_adapter_for_format(spec_format: SpecFormat) -> SpecAdapter:
        """Create an adapter for a specific format without detection.
        
        Args:
            spec_format: The API specification format enum value
            
        Returns:
            SpecAdapter: The appropriate adapter for the format
            
        Raises:
            ValueError: If the specification format is not supported
        """
        if spec_format == SpecFormat.OPENAPI:
            return OpenAPIAdapter()
        elif spec_format == SpecFormat.FAPI:
            return FAPIAdapter()
        elif spec_format == SpecFormat.BAPI:
            return BAPIAdapter()
        else:
            logger.error(f"Unsupported specification format: {spec_format}")
            raise ValueError(f"Unsupported specification format: {spec_format}")