"""
Response caching system for API actions.

This module provides caching mechanisms for API responses to:
1. Reduce load on external APIs
2. Improve response times for frequently accessed data
3. Support configurable TTL based on templates
4. Provide cache invalidation patterns
"""

import hashlib
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from src.coherence.core.redis_client import RedisClient, get_redis_client

logger = logging.getLogger(__name__)


class ApiResponseCache:
    """
    Cache API responses to reduce external API calls.
    
    This class provides caching functionality for the template-driven
    API action system, with support for:
    - TTL-based expiration configurable per API call
    - Namespace isolation for multi-tenant deployments
    - Pattern-based cache invalidation
    - Metrics for cache hit/miss rates
    
    Attributes:
        redis: Redis client for cache storage
        namespace: Namespace prefix for cache keys
        default_ttl: Default TTL in seconds for cached responses
    """
    
    def __init__(
        self, 
        redis_client: RedisClient,
        namespace: str = "api_cache",
        default_ttl: int = 300
    ):
        """Initialize the API response cache.
        
        Args:
            redis_client: Redis client for cache storage
            namespace: Namespace prefix for cache keys
            default_ttl: Default TTL in seconds for cached responses
        """
        self.redis = redis_client
        self.namespace = namespace
        self.default_ttl = default_ttl
        self.metrics = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "invalidations": 0
        }
        
    async def get(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get a cached API response.
        
        Args:
            cache_key: The cache key
            
        Returns:
            The cached response or None if not found
        """
        key = f"{self.namespace}:{cache_key}"
        cached = await self.redis.get(key)
        
        if cached:
            self.metrics["hits"] += 1
            try:
                return json.loads(cached)
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in cache for key: {cache_key}")
                return None
        
        self.metrics["misses"] += 1
        return None
        
    async def set(
        self, 
        cache_key: str, 
        response: Dict[str, Any], 
        ttl_seconds: Optional[int] = None
    ) -> bool:
        """Cache an API response.
        
        Args:
            cache_key: The cache key
            response: The response to cache
            ttl_seconds: Time-to-live in seconds (uses default if None)
            
        Returns:
            True if the cache set operation succeeded
        """
        key = f"{self.namespace}:{cache_key}"
        ttl = ttl_seconds if ttl_seconds is not None else self.default_ttl
        
        try:
            serialized = json.dumps(response)
            success = await self.redis.set(key, serialized, expire=ttl)
            if success:
                self.metrics["sets"] += 1
                
                # Store the key in a sorted set for management purposes
                # This allows listing all keys for a pattern
                await self.redis.zadd(
                    f"{self.namespace}:keys",
                    {cache_key: datetime.now().timestamp()}
                )
                
            return success
        except Exception as e:
            logger.error(f"Error caching response for {cache_key}: {str(e)}")
            return False
        
    async def invalidate(self, pattern: str) -> int:
        """Invalidate cached responses matching a pattern.
        
        Args:
            pattern: The pattern to match cache keys
            
        Returns:
            Number of keys invalidated
        """
        try:
            # Find all keys that match the pattern
            keys = await self._get_matching_keys(pattern)
            count = 0
            
            if keys:
                # Delete the keys
                full_keys = [f"{self.namespace}:{key}" for key in keys]
                count = await self.redis.delete(*full_keys)
                
                # Remove from management set
                await self.redis.zrem(f"{self.namespace}:keys", *keys)
                
                self.metrics["invalidations"] += count
                logger.info(f"Invalidated {count} cached responses matching '{pattern}'")
            
            return count
        except Exception as e:
            logger.error(f"Error invalidating cache for pattern {pattern}: {str(e)}")
            return 0
            
    async def invalidate_by_api(self, api_key: str) -> int:
        """Invalidate all cached responses for a specific API.
        
        Args:
            api_key: The API key to invalidate
            
        Returns:
            Number of keys invalidated
        """
        return await self.invalidate(f"{api_key}:")
        
    async def invalidate_by_endpoint(self, api_key: str, endpoint: str) -> int:
        """Invalidate cached responses for a specific API endpoint.
        
        Args:
            api_key: The API key
            endpoint: The endpoint path
            
        Returns:
            Number of keys invalidated
        """
        # Normalize endpoint for cache key
        endpoint_part = endpoint.replace("/", "_").lstrip("_")
        return await self.invalidate(f"{api_key}:{endpoint_part}")
            
    async def clear_all(self) -> int:
        """Clear all cached responses.
        
        Returns:
            Number of keys cleared
        """
        return await self.invalidate("*")
    
    async def get_metrics(self) -> Dict[str, int]:
        """Get cache metrics.
        
        Returns:
            Dictionary with hit/miss metrics
        """
        total = self.metrics["hits"] + self.metrics["misses"]
        hit_ratio = self.metrics["hits"] / total if total > 0 else 0
        
        return {
            **self.metrics,
            "hit_ratio": hit_ratio
        }
        
    async def _get_matching_keys(self, pattern: str) -> List[str]:
        """Get cache keys matching a pattern.
        
        Args:
            pattern: Pattern to match
            
        Returns:
            List of matching keys
        """
        # We use the management set to get keys
        keys = []
        cursor = "0"
        
        if pattern == "*":
            # Get all keys in the sorted set
            return await self.redis.zrange(f"{self.namespace}:keys", 0, -1)
        
        # Otherwise, scan the sorted set entries
        cursor, keys = await self.redis.zscan(f"{self.namespace}:keys", cursor=0, match=pattern)
        return [k for k in keys if isinstance(k, str)]

    @staticmethod
    def generate_cache_key(
        api_key: str, 
        endpoint: str, 
        method: str, 
        params: Dict[str, Any],
        query_params: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate a cache key for an API request.
        
        Args:
            api_key: The API key identifier
            endpoint: The API endpoint
            method: HTTP method
            params: Request parameters
            query_params: Additional query parameters
            
        Returns:
            Cache key string
        """
        # Normalize endpoint for consistency
        endpoint = endpoint.replace("/", "_").lstrip("_")
        
        # Only consider request body for non-GET methods
        if method.upper() == "GET":
            params_key = ApiResponseCache._hash_dict(query_params or {})
        else:
            # Sort and normalize parameters
            params_key = ApiResponseCache._hash_dict(params)
            
        # Combine all parts
        return f"{api_key}:{endpoint}:{method}:{params_key}"
    
    @staticmethod
    def _hash_dict(d: Dict[str, Any]) -> str:
        """Create a deterministic hash of a dictionary.
        
        Args:
            d: Dictionary to hash
            
        Returns:
            Hash string
        """
        if not d:
            return "empty"
            
        # Sort items for deterministic serialization
        serialized = json.dumps(d, sort_keys=True)
        return hashlib.md5(serialized.encode()).hexdigest()


async def get_api_response_cache() -> ApiResponseCache:
    """Factory function to create an ApiResponseCache.
    
    Returns:
        Initialized ApiResponseCache
    """
    redis_client = await get_redis_client()
    return ApiResponseCache(redis_client)