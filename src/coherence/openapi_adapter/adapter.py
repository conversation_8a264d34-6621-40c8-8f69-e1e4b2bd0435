"""
OpenAPI adapter for importing and generating actions from API specifications.
"""

import logging
import uuid
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.models.integration import (
    APIAuthConfig,
    APIEndpoint,
    APIIntegration,
    APIRateLimit,
    AuthType,
    IntegrationStatus,
)

logger = logging.getLogger(__name__)


class OpenAPIParseError(Exception):
    """Exception raised when parsing an OpenAPI specification fails."""

    pass


class OpenAPIAdapter:
    """Adapter for importing and generating actions from OpenAPI specifications.

    This class handles:
    1. Validation and import of OpenAPI specs
    2. Extraction of endpoints and authentication methods
    3. Generation of action classes for API endpoints
    4. Management of API credentials
    """

    def __init__(self, db: AsyncSession):
        """Initialize the OpenAPI adapter.

        Args:
            db: Database session for storing API integrations
        """
        self.db = db

    async def import_spec(
        self, tenant_id: uuid.UUID, name: str, spec_data: Dict
    ) -> Dict:
        """Import and validate an OpenAPI specification.

        Args:
            tenant_id: ID of the tenant importing the specification
            name: Name for this API integration
            spec_data: Raw OpenAPI specification as a dictionary

        Returns:
            Dictionary with integration details including extracted endpoints

        Raises:
            OpenAPIParseError: If the specification is invalid or cannot be parsed
        """
        try:
            # Validate OpenAPI spec
            api_spec = await self._validate_spec(spec_data)

            # Create integration record
            integration = APIIntegration(
                tenant_id=tenant_id,
                name=name,
                version=api_spec.get("info", {}).get("version", "unknown"),
                openapi_spec=spec_data,
                base_url=self._extract_base_url(api_spec),
                status=IntegrationStatus.DRAFT,
            )

            # Add to database
            self.db.add(integration)
            await self.db.flush()

            # Extract endpoints and create endpoint records
            endpoints = await self._extract_endpoints(integration.id, api_spec)

            # Extract authentication methods
            auth_methods = self._extract_auth_methods(api_spec)

            # If auth methods are present, create auth config
            if auth_methods:
                auth_config = APIAuthConfig(
                    integration_id=integration.id,
                    auth_type=self._determine_primary_auth_type(auth_methods),
                )
                self.db.add(auth_config)

            await self.db.commit()

            return {
                "integration_id": str(integration.id),
                "tenant_id": tenant_id,  # Add missing tenant_id
                "name": integration.name,
                "version": integration.version,
                "base_url": integration.base_url,
                "endpoints": [
                    {
                        "id": str(endpoint.id),
                        "path": endpoint.path,
                        "method": endpoint.method,
                        "operation_id": endpoint.operation_id,
                    }
                    for endpoint in endpoints
                ],
                "auth_methods": auth_methods,
            }

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error importing OpenAPI spec: {str(e)}")
            raise OpenAPIParseError(
                f"Failed to import OpenAPI specification: {str(e)}"
            ) from e

    async def _validate_spec(self, spec_data: Dict) -> Dict:
        """Validate an OpenAPI specification.

        Args:
            spec_data: OpenAPI specification as a dictionary

        Returns:
            Validated specification

        Raises:
            OpenAPIParseError: If validation fails
        """
        # Basic validation
        if not isinstance(spec_data, dict):
            raise OpenAPIParseError("OpenAPI spec must be a valid dictionary")

        # Check OpenAPI version
        openapi_version = spec_data.get("openapi", "")
        if not openapi_version.startswith(("3.0", "3.1")):
            raise OpenAPIParseError(
                f"Unsupported OpenAPI version: {openapi_version}. Only 3.0.x and 3.1.x are supported"
            )

        # Check required fields
        if "info" not in spec_data:
            raise OpenAPIParseError("Missing 'info' section in OpenAPI spec")

        if "paths" not in spec_data:
            raise OpenAPIParseError("Missing 'paths' section in OpenAPI spec")

        if "title" not in spec_data.get("info", {}):
            raise OpenAPIParseError("Missing 'title' in info section")

        # Spec is valid enough to proceed
        return spec_data

    def _extract_base_url(self, api_spec: Dict) -> Optional[str]:
        """Extract the base URL from an OpenAPI specification.

        Args:
            api_spec: Validated OpenAPI specification

        Returns:
            Base URL string or None if not found
        """
        # Try to extract from servers section (OpenAPI 3.0+)
        servers = api_spec.get("servers", [])
        if servers and isinstance(servers, list) and len(servers) > 0:
            url = servers[0].get("url")
            if url:
                # Parse URL to extract base components
                parsed = urlparse(url)
                if parsed.netloc:
                    scheme = parsed.scheme or "https"
                    return f"{scheme}://{parsed.netloc}"

        # Fallback to host + basePath (OpenAPI 2.0 compatibility)
        host = api_spec.get("host")
        if host:
            scheme = api_spec.get("schemes", ["https"])[0]
            base_path = api_spec.get("basePath", "")
            return f"{scheme}://{host}{base_path}"

        return None

    async def _extract_endpoints(
        self, integration_id: uuid.UUID, api_spec: Dict
    ) -> List[APIEndpoint]:
        """Extract and create endpoint records from an OpenAPI specification.

        Args:
            integration_id: ID of the API integration
            api_spec: Validated OpenAPI specification

        Returns:
            List of created APIEndpoint objects
        """
        endpoints = []
        paths = api_spec.get("paths", {})

        for path, path_item in paths.items():
            # Skip non-dict path items and parameters at path level
            if not isinstance(path_item, dict) or path == "parameters":
                continue

            # Process each HTTP method in the path
            for method, operation in path_item.items():
                # Skip non-HTTP methods (parameters, servers, etc.)
                if method not in [
                    "get",
                    "post",
                    "put",
                    "delete",
                    "patch",
                    "options",
                    "head",
                ]:
                    continue

                if not isinstance(operation, dict):
                    logger.warning(f"Skipping invalid operation for {method} {path}")
                    continue

                # Extract operation details
                operation_id = (
                    operation.get("operationId") or f"{method}_{path.replace('/', '_')}"
                )
                
                # Extract OpenAPI operation details
                summary = operation.get("summary", "")
                description = operation.get("description", "")
                tags = operation.get("tags", [])
                deprecated = operation.get("deprecated", False)
                
                # Store the operation snippet for UI display
                openapi_snippet = {
                    "summary": summary,
                    "description": description,
                    "tags": tags,
                    "deprecated": deprecated,
                    "parameters": operation.get("parameters", []),
                    "requestBody": operation.get("requestBody"),
                    "responses": operation.get("responses", {}),
                    "security": operation.get("security", [])
                }

                # Create endpoint record with new fields
                endpoint = APIEndpoint(
                    integration_id=integration_id,
                    path=path,
                    method=method.upper(),
                    operation_id=operation_id,
                    summary=summary,
                    description=description,
                    tags=tags,
                    deprecated=deprecated,
                    openapi_snippet=openapi_snippet,
                    enabled=True,
                )

                # Add to database and endpoints list
                self.db.add(endpoint)
                await self.db.flush()  # Flush to get the endpoint.id
                endpoints.append(endpoint)

                # Add default rate limit
                rate_limit = APIRateLimit(
                    endpoint_id=endpoint.id,
                    requests_per_min=60,  # Default
                    burst_size=5,  # Default
                    cooldown_sec=60,  # Default
                )
                self.db.add(rate_limit)

        await self.db.flush()
        return endpoints

    def _extract_auth_methods(self, api_spec: Dict) -> List[Dict]:
        """Extract authentication methods from an OpenAPI specification.

        Args:
            api_spec: Validated OpenAPI specification

        Returns:
            List of authentication methods as dictionaries
        """
        auth_methods = []

        # Check for security schemes in components (OpenAPI 3.0+)
        security_schemes = api_spec.get("components", {}).get("securitySchemes", {})

        # Fallback to securityDefinitions (OpenAPI 2.0)
        if not security_schemes:
            security_schemes = api_spec.get("securityDefinitions", {})

        # Process each security scheme
        for _, scheme in security_schemes.items():
            if not isinstance(scheme, dict):
                continue

            scheme_type = scheme.get("type", "").lower()

            if scheme_type == "apikey":
                auth_methods.append(
                    {
                        "type": "api_key",
                        "name": scheme.get("name", ""),
                        "in": scheme.get("in", "header"),
                        "description": scheme.get("description", ""),
                    }
                )

            elif scheme_type == "http":
                scheme_scheme = scheme.get("scheme", "").lower()

                if scheme_scheme == "bearer":
                    auth_methods.append(
                        {"type": "bearer", "description": scheme.get("description", "")}
                    )

                elif scheme_scheme == "basic":
                    auth_methods.append(
                        {"type": "basic", "description": scheme.get("description", "")}
                    )

            elif scheme_type == "oauth2":
                flows = {}

                # OpenAPI 3.0 flows
                if "flows" in scheme:
                    flows = scheme.get("flows", {})
                # OpenAPI 2.0 flow
                else:
                    flow_type = scheme.get("flow", "")
                    if flow_type:
                        flows[flow_type] = {
                            "authorizationUrl": scheme.get("authorizationUrl", ""),
                            "tokenUrl": scheme.get("tokenUrl", ""),
                            "scopes": scheme.get("scopes", {}),
                        }

                auth_methods.append(
                    {
                        "type": "oauth2",
                        "flows": flows,
                        "description": scheme.get("description", ""),
                    }
                )

        return auth_methods

    def _determine_primary_auth_type(self, auth_methods: List[Dict]) -> AuthType:
        """Determine the primary authentication type from available methods.

        Args:
            auth_methods: List of authentication methods

        Returns:
            Primary AuthType enum value
        """
        if not auth_methods:
            return AuthType.API_KEY  # Default

        # Priority order: OAuth2 > Bearer > API Key > Basic > Custom
        for auth in auth_methods:
            auth_type = auth.get("type", "")

            if auth_type == "oauth2":
                return AuthType.OAUTH2

            elif auth_type == "bearer":
                return AuthType.BEARER

            elif auth_type == "api_key":
                return AuthType.API_KEY

            elif auth_type == "basic":
                return AuthType.BASIC

        return AuthType.CUSTOM
    
    async def generate_action_templates(
        self, 
        integration_id: uuid.UUID,
        api_key: str,
        tenant_id: uuid.UUID
    ) -> List[Dict[str, Any]]:
        """Generate action templates from an OpenAPI specification.
        
        Args:
            integration_id: ID of the imported API integration
            api_key: The key to identify the API (used in template naming)
            tenant_id: The tenant ID that owns these templates
            
        Returns:
            A list of template definitions ready to be created
            
        Raises:
            ValueError: If the integration is not found
        """
        # Fetch integration
        integration = await self.db.get(APIIntegration, integration_id)
        if not integration:
            raise ValueError(f"Integration not found with ID: {integration_id}")
        
        # Query for all endpoints associated with this integration
        result = await self.db.execute(
            text("SELECT * FROM api_endpoints WHERE integration_id = :integration_id"),
            {"integration_id": integration_id},
        )
        endpoints = result.fetchall()
        
        # Store generated templates
        templates = []
        
        for endpoint in endpoints:
            # Get the endpoint specification from OpenAPI spec
            endpoint_spec = self._get_endpoint_spec(
                integration.openapi_spec, endpoint.path, endpoint.method.lower()
            )
            
            if not endpoint_spec:
                logger.warning(
                    f"Endpoint spec not found for {endpoint.method} {endpoint.path}"
                )
                continue
            
            # Generate template key in the format expected by the UI: endpoint_<endpoint_id>_<operation>
            operation_id = endpoint.operation_id or self._generate_action_name(
                endpoint.operation_id, endpoint.path, endpoint.method
            )
            template_key = f"endpoint_{endpoint.id}_{operation_id}".replace("/", "_").replace("-", "_").lower()
            
            # Extract parameters for mapping
            parameters = self._extract_parameters(
                integration.openapi_spec, endpoint.path, endpoint.method.lower()
            )
            
            # Generate parameter mapping template
            parameter_mapping = self._generate_parameter_mapping(parameters)
            
            # Generate response mapping template
            response_mapping = self._generate_response_mapping(endpoint_spec)
            
            # Extract authentication method
            auth_config = self._generate_auth_config(integration)
            
            # Create main template for this API action
            action_config = {
                "api_key": api_key,
                "integration_id": str(integration_id),
                "endpoint_id": str(endpoint.id),
                "endpoint": endpoint.path,
                "method": endpoint.method.upper(),
                "parameter_mapping": parameter_mapping,
                "response_mapping": response_mapping,
                "authentication": auth_config,
                "error_handling": {
                    "timeout_ms": 5000,
                    "retries": 2,
                    "fallback_template": f"{template_key}_fallback"
                }
            }
            
            # Generate a description from the OpenAPI spec
            description = self._generate_description(
                integration.openapi_spec, endpoint.path, endpoint.method.lower()
            )
            
            # Generate a default response template
            default_body = self._generate_default_body(endpoint_spec, response_mapping)
            
            # Generate response format configuration
            response_format = self._generate_response_format(endpoint_spec, response_mapping)
            
            # Create the action template definition with actions as a dictionary, not a list
            primary_template = {
                "key": template_key,
                "category": "response_gen",
                "body": default_body,
                "scope": "tenant",
                "scope_id": tenant_id,
                "tenant_id": tenant_id,
                "version": 1,
                "description": description,
                "actions": {"api_action": action_config},  # Dictionary, not list
                "parameters": self._convert_parameters_to_schema(parameters),
                "response_format": response_format,
                "metadata": {
                    "source": "openapi",
                    "integration_id": str(integration_id),
                    "endpoint_id": str(endpoint.id),
                    "api_name": integration.name,
                    "generated": True,
                    "tags": endpoint_spec.get("tags", []),
                }
            }
            
            templates.append(primary_template)
            
            # Create a fallback template for error cases
            fallback_template = {
                "key": f"{template_key}_fallback",
                "category": "error_handler",
                "body": f"I'm sorry, I couldn't get the information you requested from the {integration.name} API due to a technical issue: {{{{ error_message }}}}",
                "scope": "tenant",
                "scope_id": tenant_id,
                "tenant_id": tenant_id,
                "version": 1,
                "description": f"Fallback response for {description}",
                "actions": {},  # Empty dictionary, not list
                "parameters": {}
            }
            
            templates.append(fallback_template)
            
            # Create an intent router template for vector matching
            intent_body = self._generate_intent_template_body(
                operation_id=operation_id,
                endpoint_spec=endpoint_spec,
                integration_name=integration.name,
                parameters=parameters
            )
            
            intent_template = {
                "key": f"{template_key}_intent",
                "category": "intent_router",
                "body": intent_body,
                "scope": "tenant",
                "scope_id": tenant_id,
                "tenant_id": tenant_id,
                "version": 1,
                "description": f"Intent matcher for {description}",
                "actions": {},  # No actions needed for intent templates
                "parameters": self._convert_parameters_to_schema(parameters),
                "metadata": {
                    "source": "openapi",
                    "integration_id": str(integration_id),
                    "endpoint_id": str(endpoint.id),
                    "api_name": integration.name,
                    "generated": True,
                    "tags": endpoint_spec.get("tags", []),
                    "vector_indexable": True,  # Flag for vector indexing
                    "response_template_key": template_key  # Link to the response template
                }
            }
            
            templates.append(intent_template)
        
        return templates
    
    def _generate_parameter_mapping(self, parameters: List[Dict]) -> Dict[str, str]:
        """Generate parameter mapping from intent parameters to API parameters.
        
        Args:
            parameters: List of API parameters
            
        Returns:
            Dictionary mapping API parameters to template expressions
        """
        param_mapping = {}
        
        for param in parameters:
            param_name = param.get("name")
            param_in = param.get("in")
            
            if not param_name:
                continue
                
            # Create different mappings based on parameter location
            if param_in == "path":
                param_mapping[param_name] = f"{{{{ parameters.{param_name} }}}}"
            elif param_in == "query":
                param_mapping[param_name] = f"{{{{ parameters.{param_name} }}}}"
            elif param_in == "header":
                # Headers are typically handled separately via authentication
                continue
            elif param_in == "body":
                param_mapping["body"] = "{{ parameters.body }}"
        
        return param_mapping
    
    def _generate_response_mapping(self, endpoint_spec: Dict) -> Dict[str, str]:
        """Generate response mapping from API response to template variables.
        
        Args:
            endpoint_spec: OpenAPI operation specification
            
        Returns:
            Dictionary mapping template variables to API response paths
        """
        response_mapping = {}
        
        # Extract response schema for 200 response
        responses = endpoint_spec.get("responses", {})
        success_response = responses.get("200") or responses.get("201") or responses.get("2XX") or next(iter(responses.values()), {})
        
        if not success_response:
            return {"raw_response": "{{ response }}"}
            
        # Extract content schema
        content = success_response.get("content", {})
        for media_type, media_info in content.items():
            if "application/json" in media_type:
                schema = media_info.get("schema", {})
                
                # If it's an object with properties, map each property
                if schema.get("type") == "object" and "properties" in schema:
                    for prop_name, _ in schema.get("properties", {}).items():
                        response_mapping[prop_name] = f"{{{{ response.{prop_name} }}}}"
                        
                # If it's an array, map the array and its first item
                elif schema.get("type") == "array":
                    response_mapping["items"] = "{{ response }}"
                    response_mapping["first_item"] = "{{ response[0] if response|length > 0 else {} }}"
                    
                    # Try to map fields of the first item if it's an object
                    items_schema = schema.get("items", {})
                    if items_schema.get("type") == "object" and "properties" in items_schema:
                        for prop_name in items_schema.get("properties", {}).keys():
                            response_mapping[f"first_{prop_name}"] = f"{{{{ response[0].{prop_name} if response|length > 0 else '' }}}}"
                
                break
        
        # Fallback: If we couldn't extract a meaningful mapping, use the raw response
        if not response_mapping:
            response_mapping["raw_response"] = "{{ response }}"
            
        return response_mapping
    
    def _generate_auth_config(self, integration: APIIntegration) -> Dict[str, Any]:
        """Generate authentication configuration for the API.
        
        Args:
            integration: API integration
            
        Returns:
            Authentication configuration dictionary
        """
        # Query for auth config
        auth_query = text(
            "SELECT * FROM api_auth_configs WHERE integration_id = :integration_id LIMIT 1"
        )
        auth_result = self.db.sync_execute(
            auth_query, {"integration_id": integration.id}
        )
        auth_config = auth_result.fetchone()
        
        if not auth_config:
            # Default to empty auth config
            return {
                "type": "none",
                "value": None
            }
            
        auth_type = auth_config.auth_type
        
        if auth_type == "api_key":
            return {
                "type": "api_key",
                "location": "header",  # Default, could be query or header
                "name": "X-API-Key",   # Default name, should be customized
                "value": "{{ credentials." + integration.name.lower().replace(" ", "_") + " }}"
            }
        elif auth_type == "bearer":
            return {
                "type": "bearer",
                "value": "{{ credentials." + integration.name.lower().replace(" ", "_") + "_token }}"
            }
        elif auth_type == "basic":
            return {
                "type": "basic",
                "username": "{{ credentials." + integration.name.lower().replace(" ", "_") + "_username }}",
                "password": "{{ credentials." + integration.name.lower().replace(" ", "_") + "_password }}"
            }
        elif auth_type == "oauth2":
            return {
                "type": "oauth2",
                "token": "{{ credentials." + integration.name.lower().replace(" ", "_") + "_oauth_token }}"
            }
        else:
            return {
                "type": "none",
                "value": None
            }
    
    def _generate_default_body(self, endpoint_spec: Dict, response_mapping: Dict[str, str]) -> str:
        """Generate a default response template body.
        
        Args:
            endpoint_spec: OpenAPI operation specification
            response_mapping: Response mapping dictionary
            
        Returns:
            Default template body
        """
        # Extract operation details
        summary = endpoint_spec.get("summary", "")
        description = endpoint_spec.get("description", "")
        
        # Create a descriptive template based on the operation
        template_parts = []
        
        if summary:
            template_parts.append(f"# {summary}")
            
        if description:
            template_parts.append(description)
            
        # Add response fields based on response mapping
        template_parts.append("\nHere's the information you requested:")
        
        # Generate a nice response format based on available fields
        for field, _ in response_mapping.items():
            if field != "raw_response" and field != "items" and field != "first_item":
                template_parts.append(f"- {field.replace('_', ' ').title()}: {{{{ results.default.{field} }}}}")
                
        return "\n\n".join(template_parts)
    
    def _generate_response_format(self, endpoint_spec: Dict, response_mapping: Dict[str, str]) -> Dict[str, Any]:
        """Generate response formatting configuration.
        
        Args:
            endpoint_spec: OpenAPI operation specification
            response_mapping: Response mapping dictionary
            
        Returns:
            Response format configuration
        """
        # Analyze the expected response structure
        responses = endpoint_spec.get("responses", {})
        success_response = responses.get("200", responses.get("201", {}))
        
        format_config = {
            "type": "structured",
            "sections": [],
            "filters": ["json_pretty"],
            "conditionals": {}
        }
        
        # Generate standard sections
        summary = endpoint_spec.get("summary", "")
        if summary:
            format_config["sections"].append({
                "name": "header",
                "type": "text",
                "content": summary,
                "style": "heading"
            })
        
        # Add a status section
        format_config["sections"].append({
            "name": "status",
            "type": "conditional",
            "conditions": {
                "success": {
                    "check": "result.success == true",
                    "content": "The API request to {{ results.default.integration_name }} was successful.",
                    "style": "success"
                },
                "error": {
                    "check": "result.success == false",
                    "content": "The API request failed: {{ result.error.message }}",
                    "style": "error"
                }
            }
        })
        
        # Add endpoint info
        format_config["sections"].append({
            "name": "endpoint_info",
            "type": "text",
            "content": "Endpoint: {{ results.default.method | upper }} {{ results.default.path }}",
            "conditionals": {
                "show_if": "results.default.status_code >= 200 and results.default.status_code < 300"
            }
        })
        
        # Analyze response schema to determine formatting
        if "content" in success_response:
            content = success_response["content"]
            json_content = content.get("application/json", {})
            schema = json_content.get("schema", {})
            
            # Determine if response is an array, object, or primitive
            if schema.get("type") == "array":
                format_config["sections"].append({
                    "name": "results",
                    "type": "list",
                    "source": "result.data",
                    "item_format": {
                        "type": "object",
                        "show_keys": True,
                        "style": "compact"
                    }
                })
            elif schema.get("type") == "object":
                properties = schema.get("properties", {})
                if properties:
                    # Object with known properties
                    format_config["sections"].append({
                        "name": "results",
                        "type": "object",
                        "source": "result.data",
                        "show_keys": True,
                        "key_order": list(properties.keys()),
                        "key_formatting": {
                            key: key.replace("_", " ").title()
                            for key in properties.keys()
                        }
                    })
                else:
                    # Generic object
                    format_config["sections"].append({
                        "name": "results",
                        "type": "json",
                        "source": "result",
                        "indent": 2
                    })
            else:
                # Primitive or unknown type
                format_config["sections"].append({
                    "name": "results",
                    "type": "raw",
                    "source": "result"
                })
        
        # Add pagination information if available
        if response_mapping.get("has_more") or response_mapping.get("next_page"):
            format_config["sections"].append({
                "name": "pagination",
                "type": "conditional",
                "conditions": {
                    "has_more": {
                        "check": "result.data.has_more == true",
                        "content": "More results available. Use page={{ result.data.next_page }} to continue.",
                        "style": "info"
                    }
                }
            })
        
        return format_config
        
    def _generate_intent_template_body(
        self,
        operation_id: str,
        endpoint_spec: Dict,
        integration_name: str,
        parameters: List[Dict] = None
    ) -> str:
        """Generate an intent template body with LLM-accessible examples.
        
        Args:
            operation_id: The OpenAPI operation ID
            endpoint_spec: The OpenAPI endpoint specification
            integration_name: The name of the integration
            parameters: Optional list of parameters for the operation
            
        Returns:
            Intent template body with LLM-accessible examples
        """
        # Extract operation information
        summary = endpoint_spec.get("summary", "")
        description = endpoint_spec.get("description", "")
        path = endpoint_spec.get("path", "")
        method = endpoint_spec.get("method", "").upper()
        
        # Create a readable operation name
        operation_name = operation_id.replace("_", " ").replace("-", " ")
        
        # Generate examples using variations of the operation and parameters
        examples = self._generate_intent_examples(operation_name, summary, description, integration_name, parameters)
        
        # Create the intent template body with LLM-accessible examples
        template_body = f"""# Intent: {operation_name.title()}

## Description
This intent matches user requests to {description or operation_name} using the {integration_name} API.

## Path and Method
- Path: {path}
- Method: {method}

## LLM-Accessible Examples
Here are examples of how users might express this intent:

{examples}

## API Details
- Integration: {integration_name}
- Operation: {operation_id}
"""

        # Add parameter section if parameters are provided
        if parameters and len(parameters) > 0:
            params_section = ["## Parameters"]
            for param in parameters:
                param_name = param.get("name", "")
                param_desc = param.get("description", "")
                param_required = param.get("required", False)
                params_section.append(f"- {param_name}: {param_desc} {'(Required)' if param_required else '(Optional)'}")
            
            template_body += "\n\n" + "\n".join(params_section)
        
        return template_body
        
    def _generate_intent_examples(
        self,
        operation_name: str,
        summary: str,
        description: str,
        integration_name: str,
        parameters: List[Dict] = None
    ) -> str:
        """Generate LLM-accessible examples for an intent template.
        
        Args:
            operation_name: The operation name in readable format
            summary: The operation summary
            description: The operation description
            integration_name: The name of the integration
            parameters: Optional list of parameters for the operation
            
        Returns:
            Formatted string with examples
        """
        examples = []
        
        # Basic examples based on operation name and integration
        examples.extend([
            f"- I need to {operation_name.lower()}",
            f"- Can you {operation_name.lower()} for me",
            f"- {operation_name.capitalize()}",
            f"- {integration_name} {operation_name.lower()}",
            f"- Use {integration_name} to {operation_name.lower()}"
        ])
        
        # Add examples based on summary if available
        if summary:
            examples.extend([
                f"- {summary}",
                f"- I want to {summary.lower()}",
                f"- Can you help me {summary.lower()}"
            ])
        
        # Generate examples with parameter variations if parameters provided
        if parameters and len(parameters) > 0:
            # Extract parameter names for a few examples
            param_names = [p.get("name") for p in parameters if p.get("name") != "body"][:2]  # Use up to 2 parameters
            
            if param_names:
                param_examples = []
                for param in param_names:
                    # Convert snake_case or camelCase to spaces
                    readable_param = param.replace("_", " ").replace("-", " ")
                    
                    param_examples.extend([
                        f"- {operation_name} with {readable_param} X",
                        f"- Find {operation_name.lower()} where {readable_param} is Y",
                        f"- {operation_name} for {readable_param} Z"
                    ])
                
                examples.extend(param_examples)
        
        # Join all examples
        return "\n".join(examples)
    
    def _convert_parameters_to_schema(self, parameters: List[Dict]) -> Dict[str, Any]:
        """Convert API parameters to template parameter schema.
        
        Args:
            parameters: List of API parameters
            
        Returns:
            Parameter schema dictionary
        """
        schema = {}
        
        for param in parameters:
            param_name = param.get("name")
            param_schema = param.get("schema", {})
            
            if not param_name or param_name == "body":
                continue
                
            schema[param_name] = {
                "type": param_schema.get("type", "string"),
                "description": param.get("description", f"Parameter {param_name}"),
                "required": param.get("required", False)
            }
            
            # Add enum values if available
            if "enum" in param_schema:
                schema[param_name]["enum"] = param_schema["enum"]
                
            # Add format if available
            if "format" in param_schema:
                schema[param_name]["format"] = param_schema["format"]
                
        return schema
    
    def _get_endpoint_spec(self, openapi_spec: Dict, path: str, method: str) -> Optional[Dict]:
        """Extract endpoint specification from OpenAPI spec.
        
        Args:
            openapi_spec: OpenAPI specification
            path: API path
            method: HTTP method (lowercase)
            
        Returns:
            Endpoint specification or None if not found
        """
        paths = openapi_spec.get("paths", {})
        path_item = paths.get(path, {})
        return path_item.get(method)

    async def generate_actions(self, integration_id: uuid.UUID) -> List[Dict]:
        """Generate action classes and intent mappings from an API integration.

        Args:
            integration_id: ID of the API integration

        Returns:
            List of generated actions
        """
        # Fetch integration and endpoints
        integration = await self.db.get(APIIntegration, integration_id)
        if not integration:
            raise ValueError(f"Integration not found with ID: {integration_id}")

        # Query for all endpoints associated with this integration
        result = await self.db.execute(
            text("SELECT * FROM api_endpoints WHERE integration_id = :integration_id"),
            {"integration_id": integration_id},
        )
        endpoints = result.fetchall()

        # Generate action for each endpoint
        actions = []
        for endpoint in endpoints:
            action_name = self._generate_action_name(
                endpoint.operation_id, endpoint.path, endpoint.method
            )

            # Extract parameters from OpenAPI spec for this endpoint
            parameters = self._extract_parameters(
                integration.openapi_spec, endpoint.path, endpoint.method
            )

            actions.append(
                {
                    "endpoint_id": str(endpoint.id),
                    "action_name": action_name,
                    "class_name": self._to_class_name(action_name),
                    "parameters": parameters,
                    "description": self._generate_description(
                        integration.openapi_spec, endpoint.path, endpoint.method
                    ),
                }
            )

            # Update endpoint with action class name
            await self.db.execute(
                text("UPDATE api_endpoints SET action_class_name = :class_name WHERE id = :id"),
                {"class_name": self._to_class_name(action_name), "id": endpoint.id},
            )

        await self.db.commit()
        return actions

    def _generate_action_name(self, operation_id: str, path: str, method: str) -> str:
        """Generate a readable action name from operation details.

        Args:
            operation_id: OpenAPI operationId or None
            path: API endpoint path
            method: HTTP method

        Returns:
            Readable action name
        """
        if operation_id:
            # Convert camelCase to snake_case
            import re

            name = re.sub(r"([a-z0-9])([A-Z])", r"\1_\2", operation_id).lower()
            return name

        # Generate from path and method
        # Convert /users/{id}/posts to users_id_posts
        path_name = path.strip("/").replace("/", "_").replace("{", "").replace("}", "")
        return f"{method.lower()}_{path_name}"

    def _to_class_name(self, action_name: str) -> str:
        """Convert an action name to a class name.

        Args:
            action_name: Action name in snake_case

        Returns:
            Class name in PascalCase
        """
        return "".join(word.capitalize() for word in action_name.split("_"))

    def _extract_parameters(self, api_spec: Dict, path: str, method: str) -> List[Dict]:
        """Extract parameters for an endpoint from the OpenAPI spec.

        Args:
            api_spec: OpenAPI specification
            path: API endpoint path
            method: HTTP method

        Returns:
            List of parameters with flattened schemas for UI consumption
        """
        parameters = []

        # Get operation from spec
        operation = api_spec.get("paths", {}).get(path, {}).get(method.lower())
        if not operation:
            return parameters

        # Process path and query parameters
        for param in operation.get("parameters", []):
            if not isinstance(param, dict):
                continue

            # Skip invalid parameters
            if "name" not in param or "in" not in param:
                continue

            param_in = param.get("in")
            if param_in not in ["path", "query", "header"]:
                continue

            # Extract and resolve schema
            schema = param.get("schema", {})
            if not schema and "type" in param:
                # OpenAPI 2.0 compatibility
                schema = {"type": param.get("type")}

            # Resolve schema references if needed
            resolved_schema = self._resolve_schema_reference(api_spec, schema)
            
            # Extract parameter type from schema
            param_type = self._extract_type_from_schema(resolved_schema)

            parameters.append(
                {
                    "name": param.get("name"),
                    "in": param_in,
                    "type": param_type,
                    "required": param.get(
                        "required", param_in == "path"
                    ),  # Path params are always required
                    "description": param.get("description", ""),
                    "schema": resolved_schema,
                    "enum": resolved_schema.get("enum"),
                    "format": resolved_schema.get("format"),
                    "default": resolved_schema.get("default"),
                    "example": resolved_schema.get("example"),
                }
            )

        # Process request body parameters (OpenAPI 3.0+)
        request_body = operation.get("requestBody")
        if isinstance(request_body, dict):
            content = request_body.get("content", {})
            for media_type, media_obj in content.items():
                if not isinstance(media_obj, dict):
                    continue

                schema = media_obj.get("schema")
                if not schema:
                    continue

                # Resolve schema reference
                resolved_schema = self._resolve_schema_reference(api_spec, schema)
                
                # If the schema is an object with properties, flatten them
                if resolved_schema.get("type") == "object" and "properties" in resolved_schema:
                    required_props = resolved_schema.get("required", [])
                    
                    for prop_name, prop_schema in resolved_schema["properties"].items():
                        prop_resolved = self._resolve_schema_reference(api_spec, prop_schema)
                        prop_type = self._extract_type_from_schema(prop_resolved)
                        
                        parameters.append(
                            {
                                "name": prop_name,
                                "in": "body",
                                "type": prop_type,
                                "required": prop_name in required_props,
                                "description": prop_resolved.get("description", f"Request body property: {prop_name}"),
                                "schema": prop_resolved,
                                "enum": prop_resolved.get("enum"),
                                "format": prop_resolved.get("format"),
                                "default": prop_resolved.get("default"),
                                "example": prop_resolved.get("example"),
                                "media_type": media_type,
                            }
                        )
                else:
                    # For non-object schemas, add as a single body parameter
                    param_type = self._extract_type_from_schema(resolved_schema)
                    
                    parameters.append(
                        {
                            "name": "body",
                            "in": "body",
                            "type": param_type,
                            "required": request_body.get("required", False),
                            "description": request_body.get("description", "Request body"),
                            "schema": resolved_schema,
                            "enum": resolved_schema.get("enum"),
                            "format": resolved_schema.get("format"),
                            "default": resolved_schema.get("default"),
                            "example": resolved_schema.get("example"),
                            "media_type": media_type,
                        }
                    )
                break  # Take the first content type

        # Process body parameter (OpenAPI 2.0)
        elif "body" in operation.get("parameters", []):
            for param in operation.get("parameters", []):
                if param.get("in") == "body":
                    schema = param.get("schema", {})
                    resolved_schema = self._resolve_schema_reference(api_spec, schema)
                    param_type = self._extract_type_from_schema(resolved_schema)
                    
                    parameters.append(
                        {
                            "name": param.get("name", "body"),
                            "in": "body",
                            "type": param_type,
                            "required": param.get("required", False),
                            "description": param.get("description", "Request body"),
                            "schema": resolved_schema,
                            "enum": resolved_schema.get("enum"),
                            "format": resolved_schema.get("format"),
                            "default": resolved_schema.get("default"),
                            "example": resolved_schema.get("example"),
                        }
                    )
                    break

        return parameters

    def _resolve_schema_reference(self, api_spec: Dict, schema: Dict) -> Dict:
        """Resolve $ref references in OpenAPI schemas.
        
        Args:
            api_spec: The full OpenAPI specification
            schema: Schema object that may contain $ref
            
        Returns:
            Resolved schema object
        """
        if not isinstance(schema, dict):
            return schema
            
        # Handle $ref references
        if "$ref" in schema:
            ref_path = schema["$ref"]
            
            # Only handle internal references for now
            if ref_path.startswith("#/"):
                # Parse the reference path
                path_parts = ref_path[2:].split("/")  # Remove #/ prefix
                
                # Navigate through the spec
                current = api_spec
                for part in path_parts:
                    if isinstance(current, dict) and part in current:
                        current = current[part]
                    else:
                        # Reference not found, return original schema
                        return schema
                
                # Recursively resolve in case the referenced schema has more refs
                return self._resolve_schema_reference(api_spec, current)
        
        # Handle allOf, anyOf, oneOf by taking the first schema for simplicity
        if "allOf" in schema and isinstance(schema["allOf"], list) and len(schema["allOf"]) > 0:
            # Merge all schemas in allOf
            merged = {}
            for sub_schema in schema["allOf"]:
                resolved_sub = self._resolve_schema_reference(api_spec, sub_schema)
                if isinstance(resolved_sub, dict):
                    merged.update(resolved_sub)
            return merged
            
        if "anyOf" in schema and isinstance(schema["anyOf"], list) and len(schema["anyOf"]) > 0:
            # Take the first option for UI simplicity
            return self._resolve_schema_reference(api_spec, schema["anyOf"][0])
            
        if "oneOf" in schema and isinstance(schema["oneOf"], list) and len(schema["oneOf"]) > 0:
            # Take the first option for UI simplicity
            return self._resolve_schema_reference(api_spec, schema["oneOf"][0])
        
        return schema

    def _extract_type_from_schema(self, schema: Dict) -> str:
        """Extract a simple type string from an OpenAPI schema.
        
        Args:
            schema: OpenAPI schema object
            
        Returns:
            Simple type string for UI consumption
        """
        if not isinstance(schema, dict):
            return "string"
            
        schema_type = schema.get("type", "string")
        
        # Handle specific formats
        if schema_type == "string":
            format_type = schema.get("format")
            if format_type in ["date", "date-time", "email", "uri", "uuid"]:
                return format_type
        elif schema_type == "number":
            format_type = schema.get("format")
            if format_type == "float":
                return "float"
            elif format_type == "double":
                return "double"
        elif schema_type == "integer":
            format_type = schema.get("format")
            if format_type in ["int32", "int64"]:
                return format_type
                
        # Handle enums
        if "enum" in schema:
            return "enum"
            
        return schema_type

    def _generate_description(self, api_spec: Dict, path: str, method: str) -> str:
        """Generate a description for an endpoint.

        Args:
            api_spec: OpenAPI specification
            path: API endpoint path
            method: HTTP method

        Returns:
            Description string
        """
        # Get operation from spec
        operation = api_spec.get("paths", {}).get(path, {}).get(method.lower())
        if not operation:
            return f"{method.upper()} {path}"

        # Use provided summary and description
        summary = operation.get("summary", "")
        description = operation.get("description", "")

        if summary and description:
            return f"{summary}\n\n{description}"
        elif summary:
            return summary
        elif description:
            return description
        else:
            return f"{method.upper()} {path}"


# Factory function
async def get_openapi_adapter(db: AsyncSession) -> OpenAPIAdapter:
    """Factory function to create an OpenAPIAdapter.

    Args:
        db: Database session

    Returns:
        Initialized OpenAPIAdapter
    """
    return OpenAPIAdapter(db)
