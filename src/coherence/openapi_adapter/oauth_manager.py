"""
OAuth manager for OpenAPI integrations.

This module handles OAuth 2.0 authentication flows, token management, 
and refresh operations for OpenAPI integrations.
"""

import asyncio
import base64
import json
import logging
import time
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, Optional

import aiohttp
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.core.config import get_settings
from src.coherence.models.integration import APIAuthConfig, APIIntegration, AuthType

logger = logging.getLogger(__name__)


class OAuthError(Exception):
    """Exception raised when OAuth operations fail."""

    pass


class OAuthManager:
    """
    Manager for OAuth 2.0 flows and token management.

    This class handles:
    1. Authorization code flow for OAuth 2.0
    2. Client credentials flow for OAuth 2.0
    3. Token refresh and rotation
    4. Secure storage and retrieval of credentials
    """

    def __init__(self, db: AsyncSession):
        """Initialize the OAuth manager.

        Args:
            db: Database session for storing and retrieving credentials
        """
        self.db = db
        self.settings = get_settings()

    async def initialize_oauth_flow(
        self, integration_id: uuid.UUID, flow_type: str, scopes: Optional[list] = None
    ) -> Dict[str, Any]:
        """
        Initialize an OAuth 2.0 flow for an API integration.

        Args:
            integration_id: ID of the API integration
            flow_type: OAuth flow type ('authorization_code' or 'client_credentials')
            scopes: Optional list of requested scopes

        Returns:
            Dictionary with flow details (including authorization URL for auth code flow)

        Raises:
            OAuthError: If the flow initialization fails
        """
        # Fetch integration and auth config
        integration = await self.db.get(APIIntegration, integration_id)
        if not integration:
            raise OAuthError(f"Integration not found with ID: {integration_id}")

        # Query for auth config
        result = await self.db.execute(
            "SELECT * FROM api_auth_configs WHERE integration_id = :integration_id",
            {"integration_id": integration_id},
        )
        auth_config = result.fetchone()

        if not auth_config:
            raise OAuthError(
                f"No auth configuration found for integration: {integration_id}"
            )

        if auth_config.auth_type != AuthType.OAUTH2:
            raise OAuthError(
                f"Integration auth type is not OAuth2: {auth_config.auth_type}"
            )

        # Get OAuth configuration from credentials
        oauth_config = auth_config.credentials or {}

        if not oauth_config:
            raise OAuthError("OAuth configuration not found in credentials")

        # Handle different flow types
        if flow_type == "authorization_code":
            return await self._initialize_auth_code_flow(
                integration, auth_config, oauth_config, scopes
            )
        elif flow_type == "client_credentials":
            return await self._initialize_client_credentials_flow(
                integration, auth_config, oauth_config, scopes
            )
        else:
            raise OAuthError(f"Unsupported OAuth flow type: {flow_type}")

    async def _initialize_auth_code_flow(
        self,
        integration: APIIntegration,
        auth_config: APIAuthConfig,
        oauth_config: Dict[str, Any],
        scopes: Optional[list] = None,
    ) -> Dict[str, Any]:
        """
        Initialize an OAuth 2.0 authorization code flow.

        Args:
            integration: API integration details
            auth_config: Authentication configuration
            oauth_config: OAuth specific configuration
            scopes: Optional list of requested scopes

        Returns:
            Dictionary with authorization URL and state

        Raises:
            OAuthError: If the flow initialization fails
        """
        # Get required configuration
        auth_url = oauth_config.get("authorization_url")
        if not auth_url:
            raise OAuthError("Missing authorization_url in OAuth configuration")

        client_id = oauth_config.get("client_id")
        if not client_id:
            raise OAuthError("Missing client_id in OAuth configuration")

        redirect_uri = oauth_config.get("redirect_uri")
        if not redirect_uri:
            # Use default redirect URI if not specified
            redirect_uri = f"{self.settings.api_base_url}/v1/oauth/callback"

        # Create a unique state for CSRF protection
        state = str(uuid.uuid4())

        # Determine scopes
        requested_scopes = scopes or oauth_config.get("scopes", [])
        scope_str = " ".join(requested_scopes) if requested_scopes else ""

        # Store state and configuration for later validation
        await self._store_oauth_state(
            integration.id,
            state,
            {
                "flow_type": "authorization_code",
                "redirect_uri": redirect_uri,
                "scopes": requested_scopes,
            },
        )

        # Construct the authorization URL
        params = {
            "client_id": client_id,
            "response_type": "code",
            "redirect_uri": redirect_uri,
            "state": state,
        }

        if scope_str:
            params["scope"] = scope_str

        # Add any custom parameters
        custom_params = oauth_config.get("auth_params", {})
        if custom_params and isinstance(custom_params, dict):
            params.update(custom_params)

        # Build query string (simple version)
        query = "&".join([f"{k}={v}" for k, v in params.items()])
        auth_url_with_params = f"{auth_url}?{query}"

        return {
            "flow_type": "authorization_code",
            "authorization_url": auth_url_with_params,
            "state": state,
        }

    async def _initialize_client_credentials_flow(
        self,
        integration: APIIntegration,
        auth_config: APIAuthConfig,
        oauth_config: Dict[str, Any],
        scopes: Optional[list] = None,
    ) -> Dict[str, Any]:
        """
        Initialize an OAuth 2.0 client credentials flow.

        Args:
            integration: API integration details
            auth_config: Authentication configuration
            oauth_config: OAuth specific configuration
            scopes: Optional list of requested scopes

        Returns:
            Dictionary with token details

        Raises:
            OAuthError: If the flow initialization fails
        """
        # Get required configuration
        token_url = oauth_config.get("token_url")
        if not token_url:
            raise OAuthError("Missing token_url in OAuth configuration")

        client_id = oauth_config.get("client_id")
        if not client_id:
            raise OAuthError("Missing client_id in OAuth configuration")

        client_secret = oauth_config.get("client_secret")
        if not client_secret:
            raise OAuthError("Missing client_secret in OAuth configuration")

        # Determine scopes
        requested_scopes = scopes or oauth_config.get("scopes", [])
        scope_str = " ".join(requested_scopes) if requested_scopes else ""

        # Execute token request directly
        token_result = await self._execute_token_request(
            token_url=token_url,
            grant_type="client_credentials",
            client_id=client_id,
            client_secret=client_secret,
            scope=scope_str,
        )

        # Store the token
        await self._store_token(
            integration_id=integration.id,
            token_data=token_result,
            scopes=requested_scopes,
        )

        return {
            "flow_type": "client_credentials",
            "token_type": token_result.get("token_type", "Bearer"),
            "expires_in": token_result.get("expires_in"),
            "scope": token_result.get("scope"),
        }

    async def complete_authorization(
        self, state: str, code: str, error: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Complete an OAuth 2.0 authorization code flow with the returned code.

        Args:
            state: State parameter from the authorization response
            code: Authorization code from the authorization response
            error: Error message if authorization failed

        Returns:
            Dictionary with token details

        Raises:
            OAuthError: If the authorization completion fails
        """
        if error:
            raise OAuthError(f"Authorization failed: {error}")

        # Validate state and retrieve stored information
        state_info = await self._retrieve_oauth_state(state)
        if not state_info:
            raise OAuthError("Invalid or expired state")

        integration_id = state_info.get("integration_id")
        if not integration_id:
            raise OAuthError("Missing integration_id in state info")

        # Fetch integration and auth config
        integration = await self.db.get(APIIntegration, uuid.UUID(integration_id))
        if not integration:
            raise OAuthError(f"Integration not found with ID: {integration_id}")

        result = await self.db.execute(
            "SELECT * FROM api_auth_configs WHERE integration_id = :integration_id",
            {"integration_id": integration_id},
        )
        auth_config = result.fetchone()

        if not auth_config:
            raise OAuthError(
                f"No auth configuration found for integration: {integration_id}"
            )

        # Get OAuth configuration
        oauth_config = auth_config.credentials or {}

        # Get required configuration
        token_url = oauth_config.get("token_url")
        if not token_url:
            raise OAuthError("Missing token_url in OAuth configuration")

        client_id = oauth_config.get("client_id")
        if not client_id:
            raise OAuthError("Missing client_id in OAuth configuration")

        client_secret = oauth_config.get("client_secret")
        if not client_secret:
            raise OAuthError("Missing client_secret in OAuth configuration")

        redirect_uri = state_info.get("redirect_uri") or oauth_config.get(
            "redirect_uri"
        )
        if not redirect_uri:
            raise OAuthError(
                "Missing redirect_uri in state info and OAuth configuration"
            )

        # Exchange the code for a token
        token_result = await self._execute_token_request(
            token_url=token_url,
            grant_type="authorization_code",
            client_id=client_id,
            client_secret=client_secret,
            code=code,
            redirect_uri=redirect_uri,
        )

        # Store the token
        await self._store_token(
            integration_id=integration.id,
            token_data=token_result,
            scopes=state_info.get("scopes", []),
        )

        # Clean up the state
        await self._delete_oauth_state(state)

        return {
            "integration_id": str(integration.id),
            "token_type": token_result.get("token_type", "Bearer"),
            "expires_in": token_result.get("expires_in"),
            "scope": token_result.get("scope"),
        }

    async def get_authorization_header(
        self, integration_id: uuid.UUID
    ) -> Optional[Dict[str, str]]:
        """
        Get an authorization header for an API request.

        Args:
            integration_id: ID of the API integration

        Returns:
            Dictionary with authorization header or None if not found

        Raises:
            OAuthError: If token retrieval or refresh fails
        """
        # Fetch auth config
        result = await self.db.execute(
            "SELECT * FROM api_auth_configs WHERE integration_id = :integration_id",
            {"integration_id": integration_id},
        )
        auth_config = result.fetchone()

        if not auth_config:
            logger.warning(
                f"No auth configuration found for integration: {integration_id}"
            )
            return None

        # Handle different auth types
        if auth_config.auth_type == AuthType.OAUTH2:
            # Fetch and potentially refresh the token
            token_data = await self._get_valid_token(integration_id, auth_config)
            if not token_data:
                logger.warning(
                    f"No valid token found for integration: {integration_id}"
                )
                return None

            token_type = token_data.get("token_type", "Bearer")
            access_token = token_data.get("access_token")

            if not access_token:
                logger.warning(
                    f"Missing access token for integration: {integration_id}"
                )
                return None

            return {"Authorization": f"{token_type} {access_token}"}

        elif auth_config.auth_type == AuthType.BEARER:
            token = (
                auth_config.credentials.get("token")
                if auth_config.credentials
                else None
            )
            if not token:
                logger.warning(
                    f"Missing bearer token for integration: {integration_id}"
                )
                return None

            return {"Authorization": f"Bearer {token}"}

        elif auth_config.auth_type == AuthType.API_KEY:
            api_key = (
                auth_config.credentials.get("key") if auth_config.credentials else None
            )
            header_name = (
                auth_config.credentials.get("header_name", "X-API-Key")
                if auth_config.credentials
                else "X-API-Key"
            )

            if not api_key:
                logger.warning(f"Missing API key for integration: {integration_id}")
                return None

            return {header_name: api_key}

        elif auth_config.auth_type == AuthType.BASIC:
            username = (
                auth_config.credentials.get("username")
                if auth_config.credentials
                else None
            )
            password = (
                auth_config.credentials.get("password")
                if auth_config.credentials
                else None
            )

            if not username or not password:
                logger.warning(
                    f"Missing basic auth credentials for integration: {integration_id}"
                )
                return None

            auth_str = f"{username}:{password}"
            encoded = base64.b64encode(auth_str.encode()).decode()
            return {"Authorization": f"Basic {encoded}"}

        else:
            logger.warning(f"Unsupported auth type: {auth_config.auth_type}")
            return None

    async def _get_valid_token(
        self, integration_id: uuid.UUID, auth_config: APIAuthConfig
    ) -> Optional[Dict[str, Any]]:
        """
        Get a valid OAuth token, refreshing if necessary.

        Args:
            integration_id: ID of the API integration
            auth_config: Authentication configuration

        Returns:
            Dictionary with token data or None if unavailable

        Raises:
            OAuthError: If token refresh fails
        """
        # Extract current token data
        token_data = {}
        if auth_config.credentials and "token" in auth_config.credentials:
            token_data = auth_config.credentials.get("token", {})

        # Check if we have a token
        if not token_data or "access_token" not in token_data:
            logger.warning(f"No token available for integration: {integration_id}")
            return None

        # Check if token is expired
        expires_at = token_data.get("expires_at")
        if expires_at and expires_at < time.time() + 60:  # 60-second buffer
            logger.info(f"Token expired for integration: {integration_id}, refreshing")
            # Refresh the token
            token_data = await self._refresh_token(
                integration_id, auth_config, token_data
            )

        return token_data

    async def _refresh_token(
        self,
        integration_id: uuid.UUID,
        auth_config: APIAuthConfig,
        token_data: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Refresh an OAuth token.

        Args:
            integration_id: ID of the API integration
            auth_config: Authentication configuration
            token_data: Current token data

        Returns:
            Dictionary with refreshed token data

        Raises:
            OAuthError: If token refresh fails
        """
        # Check if we have a refresh token
        refresh_token = token_data.get("refresh_token")
        if not refresh_token:
            raise OAuthError("No refresh token available for refresh")

        # Get OAuth configuration
        oauth_config = auth_config.credentials or {}

        # Get required configuration
        token_url = oauth_config.get("token_url")
        if not token_url:
            raise OAuthError("Missing token_url in OAuth configuration")

        client_id = oauth_config.get("client_id")
        if not client_id:
            raise OAuthError("Missing client_id in OAuth configuration")

        client_secret = oauth_config.get("client_secret")
        if not client_secret:
            raise OAuthError("Missing client_secret in OAuth configuration")

        # Execute refresh token request
        try:
            token_result = await self._execute_token_request(
                token_url=token_url,
                grant_type="refresh_token",
                client_id=client_id,
                client_secret=client_secret,
                refresh_token=refresh_token,
            )

            # Store the new token
            await self._store_token(
                integration_id=integration_id,
                token_data=token_result,
                scopes=auth_config.scopes,
            )

            return token_result

        except Exception as e:
            logger.error(f"Token refresh failed: {str(e)}")
            raise OAuthError(f"Failed to refresh token: {str(e)}") from e

    async def _execute_token_request(
        self,
        token_url: str,
        grant_type: str,
        client_id: str,
        client_secret: str,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Execute an OAuth token request.

        Args:
            token_url: Token endpoint URL
            grant_type: OAuth grant type
            client_id: OAuth client ID
            client_secret: OAuth client secret
            **kwargs: Additional parameters for the token request

        Returns:
            Dictionary with token response

        Raises:
            OAuthError: If the token request fails
        """
        # Prepare request data
        data = {
            "grant_type": grant_type,
            "client_id": client_id,
        }

        # Add grant-specific parameters
        if grant_type == "authorization_code":
            data["code"] = kwargs.get("code")
            data["redirect_uri"] = kwargs.get("redirect_uri")
        elif grant_type == "refresh_token":
            data["refresh_token"] = kwargs.get("refresh_token")
        elif grant_type == "client_credentials" and "scope" in kwargs:
            data["scope"] = kwargs.get("scope")

        # Add any additional params
        for key, value in kwargs.items():
            if (
                key not in ["code", "redirect_uri", "refresh_token"]
                and value is not None
            ):
                data[key] = value

        # Determine auth method (basic auth or client_secret in body)
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        auth = None

        # Try to detect preferred auth method from the token URL
        if "auth_method" in kwargs:
            auth_method = kwargs.get("auth_method")
        else:
            # Default to basic auth
            auth_method = "basic"

        if auth_method == "basic":
            auth = aiohttp.BasicAuth(client_id, client_secret)
        else:
            data["client_secret"] = client_secret

        # Execute request
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    token_url, data=data, headers=headers, auth=auth, timeout=30
                ) as response:
                    if response.status >= 400:
                        error_text = await response.text()
                        logger.error(
                            f"Token request failed with status {response.status}: {error_text}"
                        )
                        raise OAuthError(
                            f"Token request failed with status {response.status}: {error_text}"
                        )

                    token_response = await response.json()

                    # Validate the response
                    if "error" in token_response:
                        error_desc = token_response.get(
                            "error_description", token_response["error"]
                        )
                        raise OAuthError(f"Token request error: {error_desc}")

                    if "access_token" not in token_response:
                        raise OAuthError("Token response missing access_token")

                    # Add expires_at based on expires_in
                    expires_in = token_response.get("expires_in")
                    if expires_in and isinstance(expires_in, (int, float)):
                        token_response["expires_at"] = time.time() + float(expires_in)

                    return token_response

        except asyncio.TimeoutError as e:
            raise OAuthError("Token request timed out") from e
        except aiohttp.ClientError as e:
            raise OAuthError(f"Token request failed: {str(e)}") from e
        except Exception as e:
            raise OAuthError(f"Token request error: {str(e)}") from e

    async def _store_token(
        self,
        integration_id: uuid.UUID,
        token_data: Dict[str, Any],
        scopes: Optional[list] = None,
    ) -> None:
        """
        Store an OAuth token securely.

        Args:
            integration_id: ID of the API integration
            token_data: Token data to store
            scopes: Optional list of associated scopes

        Raises:
            OAuthError: If token storage fails
        """
        try:
            # Fetch current auth config
            result = await self.db.execute(
                "SELECT * FROM api_auth_configs WHERE integration_id = :integration_id",
                {"integration_id": integration_id},
            )
            auth_config = result.fetchone()

            if not auth_config:
                raise OAuthError(
                    f"No auth configuration found for integration: {integration_id}"
                )

            # Update credentials with token
            credentials = auth_config.credentials or {}
            credentials["token"] = token_data

            # Calculate token expiration
            expires_in = token_data.get("expires_in")
            expires_at = None
            if expires_in and isinstance(expires_in, (int, float)):
                expires_at = datetime.now() + timedelta(seconds=int(expires_in))

            # Update database
            await self.db.execute(
                """
                UPDATE api_auth_configs 
                SET credentials = :credentials,
                    scopes = :scopes,
                    refresh_token = :refresh_token,
                    expires_at = :expires_at
                WHERE integration_id = :integration_id
                """,
                {
                    "credentials": credentials,
                    "scopes": scopes or auth_config.scopes,
                    "refresh_token": token_data.get("refresh_token"),
                    "expires_at": expires_at,
                    "integration_id": integration_id,
                },
            )

            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to store token: {str(e)}")
            raise OAuthError(f"Failed to store token: {str(e)}") from e

    async def _store_oauth_state(
        self, integration_id: uuid.UUID, state: str, data: Dict[str, Any]
    ) -> None:
        """
        Store OAuth state for later validation.

        Args:
            integration_id: ID of the API integration
            state: State string for CSRF protection
            data: Additional data to store with the state

        Raises:
            OAuthError: If state storage fails
        """
        try:
            # Add integration ID to the data
            state_data = data.copy()
            state_data["integration_id"] = str(integration_id)
            state_data["created_at"] = time.time()

            # Use Redis instead of in-memory for production
            # For simplicity, we'll use a database table here
            await self.db.execute(
                """
                INSERT INTO oauth_states (state, data, expires_at) 
                VALUES (:state, :data, :expires_at)
                ON CONFLICT (state) DO UPDATE 
                SET data = :data, expires_at = :expires_at
                """,
                {
                    "state": state,
                    "data": json.dumps(state_data),
                    "expires_at": datetime.now()
                    + timedelta(minutes=30),  # 30-minute TTL
                },
            )

            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to store OAuth state: {str(e)}")
            raise OAuthError(f"Failed to store OAuth state: {str(e)}") from e

    async def _retrieve_oauth_state(self, state: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve and validate stored OAuth state.

        Args:
            state: State string from the authorization response

        Returns:
            Dictionary with state data or None if invalid or expired

        Raises:
            OAuthError: If state retrieval fails
        """
        try:
            # Query the state
            result = await self.db.execute(
                """
                SELECT data FROM oauth_states
                WHERE state = :state AND expires_at > :now
                """,
                {
                    "state": state,
                    "now": datetime.now(),
                },
            )

            state_record = result.fetchone()
            if not state_record:
                return None

            # Parse the data
            return json.loads(state_record.data)

        except Exception as e:
            logger.error(f"Failed to retrieve OAuth state: {str(e)}")
            return None

    async def _delete_oauth_state(self, state: str) -> None:
        """
        Delete an OAuth state after use.

        Args:
            state: State string to delete

        Raises:
            OAuthError: If state deletion fails
        """
        try:
            await self.db.execute(
                "DELETE FROM oauth_states WHERE state = :state",
                {"state": state},
            )

            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to delete OAuth state: {str(e)}")


# Factory function
async def get_oauth_manager(db: AsyncSession) -> OAuthManager:
    """
    Factory function to create an OAuthManager.

    Args:
        db: Database session

    Returns:
        Initialized OAuthManager
    """
    return OAuthManager(db)
