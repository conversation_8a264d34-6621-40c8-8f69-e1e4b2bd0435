"""
Dynamic executor for API actions based on template configurations.

This module provides a mechanism to execute API calls defined in templates,
handling parameter mapping, authentication, and error recovery.
"""

import json
import logging
import uuid
from typing import Any, Dict, Optional

import httpx
from jinja2 import Template

from src.coherence.openapi_adapter.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerError,
)
from src.coherence.openapi_adapter.credential_manager import CredentialManager
from src.coherence.openapi_adapter.health_monitor import (
    ApiHealthMonitor,
    ApiStatus,
    get_api_health_monitor,
)
from src.coherence.openapi_adapter.response_cache import (
    ApiResponseCache,
    get_api_response_cache,
)
from src.coherence.openapi_adapter.retry import RetryableError, with_retry
from src.coherence.template_system.engine.renderer import TemplateRenderer

logger = logging.getLogger(__name__)


class DynamicActionExecutor:
    """Execute API actions based on template configurations.
    
    This class handles:
    1. Parameter mapping between intents and API calls
    2. Authentication and credential management
    3. Request execution with retry and circuit breaker protection
    4. Response transformation and error handling
    """
    
    def __init__(
        self, 
        credential_manager: CredentialManager,
        http_client: Optional[httpx.AsyncClient] = None,
        template_renderer: Optional[TemplateRenderer] = None,
        response_cache: Optional[ApiResponseCache] = None,
        health_monitor: Optional[ApiHealthMonitor] = None
    ):
        """Initialize the dynamic action executor.
        
        Args:
            credential_manager: Manager for API credentials
            http_client: Optional HTTP client for making requests
            template_renderer: Optional template renderer for parameter mapping
            response_cache: Optional cache for API responses
            health_monitor: Optional health monitor for API health checking
        """
        self.credential_manager = credential_manager
        self.http_client = http_client or httpx.AsyncClient()
        self.template_renderer = template_renderer or TemplateRenderer()
        self.response_cache = response_cache
        self.health_monitor = health_monitor
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        
    async def execute(
        self, 
        action_config: Dict[str, Any],
        parameters: Dict[str, Any],
        tenant_id: uuid.UUID,
        response_format: Optional[Dict[str, Any]] = None,
        preferred_content_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """Execute an API action based on template configuration.
        
        Args:
            action_config: The action configuration from the template
            parameters: The intent parameters
            tenant_id: The tenant ID for credential retrieval
            response_format: Optional response format configuration with content types
            preferred_content_type: Optional preferred content type for Accept header
            tenant_id: The tenant ID for credential lookup
            
        Returns:
            The API response or error information
        """
        api_key = action_config.get("api_key", "default")
        
        try:
            # Log the incoming parameters
            logger.debug(f"DynamicExecutor received parameters: {parameters}")
            
            # Map parameters using template expressions
            mapped_params = await self._map_parameters(action_config, parameters)
            
            # Log the mapped parameters
            logger.debug(f"Mapped parameters: {mapped_params}")
            
            # Build request components
            url = await self._build_url(action_config, mapped_params)
            headers = await self._build_headers(action_config, tenant_id, parameters, preferred_content_type)
            body = await self._build_body(action_config, mapped_params)
            
            # Get method
            method = action_config.get("method", "GET").upper()
            endpoint = action_config.get("endpoint", "")
            
            # Check if caching is enabled for this action
            caching_config = action_config.get("caching", {})
            use_cache = caching_config.get("enabled", True)
            cache_ttl = caching_config.get("ttl_seconds", None)  # Use default if None
            
            # Check cache for GET requests if caching is enabled
            if use_cache and method == "GET" and self.response_cache is not None:
                # Generate cache key
                query_params = mapped_params if "?" not in url else None
                cache_key = ApiResponseCache.generate_cache_key(
                    api_key=api_key,
                    endpoint=endpoint,
                    method=method,
                    params={},  # Empty for GET
                    query_params=query_params
                )
                
                # Check cache
                cached_response = await self.response_cache.get(cache_key)
                if cached_response:
                    logger.info(
                        f"Cache hit for {api_key}:{endpoint}",
                        extra={"tenant_id": str(tenant_id), "api_key": api_key}
                    )
                    
                    # Map response using the response mapping
                    mapped_response = await self._map_response(
                        action_config, cached_response.get("data", {})
                    )
                    
                    # Add cache metadata
                    return {
                        "success": True,
                        "status_code": cached_response.get("status_code", 200),
                        "result": mapped_response,
                        "raw_response": cached_response.get("data", {}),
                        "headers": cached_response.get("headers", {}),
                        "content_type": cached_response.get("headers", {}).get("content-type"),
                        "cached": True
                    }
            
            # Configure timeout and other request parameters
            timeout = action_config.get("error_handling", {}).get("timeout_ms", 5000) / 1000
            
            # Get or create a circuit breaker for this API
            if api_key not in self.circuit_breakers:
                failure_threshold = action_config.get("error_handling", {}).get("failure_threshold", 5)
                recovery_timeout = action_config.get("error_handling", {}).get("recovery_timeout", 60)
                self.circuit_breakers[api_key] = CircuitBreaker(
                    name=f"api-{api_key}",
                    failure_threshold=failure_threshold,
                    recovery_timeout=recovery_timeout
                )
            
            circuit_breaker = self.circuit_breakers[api_key]
            
            # Check API health status if health monitor is available
            if self.health_monitor:
                try:
                    api_status = await self.health_monitor.get_status(api_key, tenant_id)
                    
                    # If API is DOWN, fail fast without making the request
                    if api_status == ApiStatus.DOWN:
                        logger.warning(
                            f"API {api_key} is marked as DOWN, failing request without attempting",
                            extra={"tenant_id": str(tenant_id), "api_key": api_key}
                        )
                        return await self._handle_fallback(
                            action_config, parameters, tenant_id, 
                            error_type="api_health_down",
                            error_message=f"API {api_key} is currently unavailable (marked as DOWN)"
                        )
                    
                    # If API is DEGRADED, we'll still try the request but log a warning
                    elif api_status == ApiStatus.DEGRADED:
                        logger.info(
                            f"API {api_key} is marked as DEGRADED, proceeding with caution",
                            extra={"tenant_id": str(tenant_id), "api_key": api_key}
                        )
                        
                except Exception as e:
                    # Log but continue if health check fails
                    logger.warning(
                        f"Failed to check health status for API {api_key}: {str(e)}",
                        extra={"tenant_id": str(tenant_id), "api_key": api_key, "error": str(e)}
                    )
            
            # Define function for retry decorator
            max_retries = action_config.get("error_handling", {}).get("retries", 2)
            
            @with_retry(
                max_attempts=max_retries + 1,  # +1 because the first attempt is not a retry
                backoff_strategy="exponential",
                base_delay=1.0,
                max_delay=10.0,
                jitter=True
            )
            async def execute_request():
                """Execute the HTTP request with the configured parameters."""
                
                # Determine query parameters based on URL and method
                query_params = mapped_params if method == "GET" and "?" not in url else None
                
                # Determine body payload based on method
                body_payload = None if method == "GET" else body
                
                # Log detailed request information for integration testing
                logger.info(
                    f"Executing external API request for integration test:\n"
                    f"  Method: {method}\n"
                    f"  URL: {url}\n"
                    f"  Headers: {headers}\n"
                    f"  Query Params: {query_params if query_params else 'None'}\n"
                    f"  Body: {body_payload if body_payload else 'None'}"
                )
                
                if method == "GET":
                    response = await self.http_client.get(
                        url=url,
                        headers=headers,
                        params=mapped_params if "?" not in url else None,
                        timeout=timeout
                    )
                elif method == "POST":
                    response = await self.http_client.post(
                        url=url,
                        headers=headers,
                        json=body,
                        timeout=timeout
                    )
                elif method == "PUT":
                    response = await self.http_client.put(
                        url=url,
                        headers=headers,
                        json=body,
                        timeout=timeout
                    )
                elif method == "DELETE":
                    response = await self.http_client.delete(
                        url=url,
                        headers=headers,
                        json=body,
                        timeout=timeout
                    )
                elif method == "PATCH":
                    response = await self.http_client.patch(
                        url=url,
                        headers=headers,
                        json=body,
                        timeout=timeout
                    )
                else:
                    # Fallback to generic request method
                    response = await self.http_client.request(
                        method=method,
                        url=url,
                        headers=headers,
                        params=mapped_params if method == "GET" and "?" not in url else None,
                        json=body if method != "GET" else None,
                        timeout=timeout
                    )
                
                # Check for HTTP errors
                response.raise_for_status()
                
                # Parse response
                try:
                    result = response.json()
                except json.JSONDecodeError:
                    result = response.text
                
                return {
                    "status_code": response.status_code,
                    "data": result,
                    "headers": dict(response.headers),
                }
            
            # Execute with circuit breaker protection
            try:
                async with circuit_breaker:
                    response_data = await execute_request()
                    
                    # Map response using the response mapping
                    mapped_response = await self._map_response(
                        action_config, response_data["data"]
                    )
                    
                    # Cache the response if caching is enabled and it's a GET request
                    if (use_cache and method == "GET" and 
                        response_data["status_code"] >= 200 and 
                        response_data["status_code"] < 300 and
                        self.response_cache is not None):
                        
                        # Generate cache key
                        query_params = mapped_params if "?" not in url else None
                        cache_key = ApiResponseCache.generate_cache_key(
                            api_key=api_key,
                            endpoint=endpoint,
                            method=method,
                            params={},  # Empty for GET
                            query_params=query_params
                        )
                        
                        # Cache the response
                        await self.response_cache.set(
                            cache_key=cache_key,
                            response=response_data,
                            ttl_seconds=cache_ttl
                        )
                        logger.debug(
                            f"Cached response for {api_key}:{endpoint}",
                            extra={"tenant_id": str(tenant_id), "api_key": api_key}
                        )
                    
                    return {
                        "success": True,
                        "status_code": response_data["status_code"],
                        "result": mapped_response,
                        "raw_response": response_data["data"],
                        "headers": response_data["headers"],
                        "content_type": response_data["headers"].get("content-type"),
                        "cached": False
                    }
                    
            except CircuitBreakerError as e:
                logger.warning(
                    f"Circuit breaker open for {api_key}: {str(e)}",
                    extra={"tenant_id": str(tenant_id), "api_key": api_key}
                )
                
                # Update health monitor if available
                if self.health_monitor:
                    try:
                        # Register a health check config if not already registered
                        # This allows the health monitor to start checking this API
                        from src.coherence.openapi_adapter.health_monitor import (
                            HealthCheckConfig,
                        )
                        health_config = HealthCheckConfig(
                            api_key=api_key,
                            endpoint=url,
                            method=method,
                            headers=headers,
                            expected_status=200,
                            check_interval_seconds=60
                        )
                        await self.health_monitor.register_health_check(health_config, tenant_id)
                        
                        # Update API status to reflect circuit breaker state
                        await self.health_monitor.register_status_change_callback(
                            self._on_api_status_change
                        )
                    except Exception as health_error:
                        logger.warning(
                            f"Failed to update health monitor for {api_key}: {str(health_error)}",
                            extra={"tenant_id": str(tenant_id), "api_key": api_key}
                        )
                
                return await self._handle_fallback(
                    action_config, parameters, tenant_id, 
                    error_type="circuit_breaker",
                    error_message=str(e)
                )
                
            except httpx.HTTPStatusError as e:
                status_code = e.response.status_code
                
                # Determine if this error should be retried
                if status_code in [429, 500, 502, 503, 504]:
                    logger.warning(
                        f"Retryable HTTP error: {status_code} for {api_key}",
                        extra={"tenant_id": str(tenant_id), "api_key": api_key}
                    )
                    raise RetryableError(f"HTTP {status_code}: {str(e)}") from e
                
                # Handle other HTTP errors
                logger.error(
                    f"HTTP error: {status_code} for {api_key}: {str(e)}",
                    extra={"tenant_id": str(tenant_id), "api_key": api_key}
                )
                
                try:
                    error_data = e.response.json()
                except (json.JSONDecodeError, AttributeError):
                    try:
                        error_data = {"message": e.response.text}
                    except AttributeError:
                        error_data = {"message": str(e)}
                
                return {
                    "success": False,
                    "status_code": status_code,
                    "error": {
                        "type": "http_error",
                        "message": f"HTTP Error {status_code}",
                        "details": error_data
                    }
                }
                
            except RetryableError:
                # Let retry decorator handle this
                raise
                
            except httpx.RequestError as e:
                # Network-level errors (connection errors, timeouts, etc.)
                logger.error(
                    f"Request error for {api_key}: {str(e)}",
                    extra={"tenant_id": str(tenant_id), "api_key": api_key}
                )
                raise RetryableError(f"Network error: {str(e)}") from e
                
        except Exception as e:
            # Catch-all for any other errors
            logger.exception(
                f"Error executing action for {api_key}",
                extra={"tenant_id": str(tenant_id), "api_key": api_key}
            )
            
            return {
                "success": False,
                "error": {
                    "type": "execution_error",
                    "message": f"Failed to execute API request: {str(e)}",
                    "details": {"exception": str(e)}
                }
            }
    
    async def _map_parameters(
        self, action_config: Dict[str, Any], parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Map parameters from intent to API parameters using template expressions.
        
        Args:
            action_config: The action configuration from the template
            parameters: The intent parameters
            
        Returns:
            Mapped parameters for the API request
        """
        if "parameter_mapping" not in action_config:
            return parameters
        
        param_mapping = action_config["parameter_mapping"]
        mapped_params = {}
        
        # Set up the template context
        context = {"parameters": parameters}
        
        # Process each parameter mapping
        for api_param, mapping_value in param_mapping.items():
            # Check if this is an object with source/source_path structure
            if isinstance(mapping_value, dict) and "source" in mapping_value:
                source = mapping_value.get("source")
                source_path = mapping_value.get("source_path", "")
                
                if source == "intent_param":
                    # Extract value from parameters using source_path
                    # source_path might be something like "params.terms"
                    if source_path.startswith("params."):
                        param_name = source_path[7:]  # Strip "params."
                        if param_name in parameters:
                            value = parameters[param_name]
                            if value is not None:
                                mapped_params[api_param] = value
                    else:
                        # Direct parameter name
                        if source_path in parameters:
                            value = parameters[source_path]
                            if value is not None:
                                mapped_params[api_param] = value
                else:
                    logger.warning(f"Unknown source type for parameter {api_param}: {source}")
                    
            elif isinstance(mapping_value, str) and "{{" in mapping_value:
                # This is a Jinja2 expression
                try:
                    template = Template(mapping_value)
                    mapped_value = template.render(**context)
                    
                    # Skip parameters that weren't provided (empty strings)
                    if mapped_value and mapped_value.strip():
                        mapped_params[api_param] = mapped_value
                        
                except Exception as e:
                    logger.warning(
                        f"Error mapping parameter {api_param}: {str(e)}",
                        extra={"template": mapping_value}
                    )
            else:
                # Static value or unsupported format
                if not isinstance(mapping_value, dict):
                    mapped_params[api_param] = mapping_value
                else:
                    logger.warning(f"Unsupported mapping format for {api_param}: {mapping_value}")
                
        return mapped_params
    
    async def _build_url(
        self, action_config: Dict[str, Any], mapped_params: Dict[str, Any]
    ) -> str:
        """Build the full URL for the API request.
        
        Args:
            action_config: The action configuration from the template
            mapped_params: The mapped parameters
            
        Returns:
            Full URL for the API request
        """
        # Get base API information
        integration_id = action_config.get("integration_id")
        endpoint = action_config.get("endpoint", "")
        
        # If no integration ID is provided, use the endpoint as-is
        if not integration_id:
            return endpoint
        
        # Query for the integration details from the database
        try:
            import logging  # As per provided code
            from typing import Optional  # As per provided code

            from sqlalchemy import text
            from sqlalchemy.ext.asyncio import (
                AsyncSession,  # Added as it might be implicitly needed by get_db_from_session
            )

            from src.coherence.db.deps import (
                get_db_from_session,  # As per provided code
            )

            logger = logging.getLogger(__name__) # As per provided code

            chosen_base_url: Optional[str] = None
            db_base_url_candidate: Optional[str] = None
            integration_record = None # Initialize
            base_url_for_construction: str # Declare type

            # Database query block
            try:
                db: AsyncSession = await get_db_from_session()
                
                # If we have an endpoint_id (UUID), use it directly
                endpoint_uuid = action_config.get("endpoint_id")
                if endpoint_uuid:
                    integration_result = await db.execute(
                        text("""
                        SELECT ai.base_url, ai.openapi_spec, ai.spec_format, ae.path, ae.method, ae.operation_id
                        FROM api_integrations ai
                        LEFT JOIN api_endpoints ae ON ae.integration_id = ai.id
                        WHERE ai.id = :integration_id
                        AND ae.id = :endpoint_uuid
                        """),
                        {
                            "integration_id": str(integration_id),
                            "endpoint_uuid": str(endpoint_uuid)
                        }
                    )
                else:
                    # Fallback to path-based lookup if no UUID
                    integration_result = await db.execute(
                        text("""
                        SELECT ai.base_url, ai.openapi_spec, ai.spec_format, ae.path, ae.method, ae.operation_id
                        FROM api_integrations ai
                        LEFT JOIN api_endpoints ae ON ae.integration_id = ai.id
                        WHERE ai.id = :integration_id
                        AND (ae.operation_id = :endpoint_id OR (ae.path = :endpoint_path AND ae.method = :method))
                        """),
                        {
                            "integration_id": str(integration_id),
                            "endpoint_id": endpoint.lstrip('/'),
                            "endpoint_path": endpoint,
                            "method": action_config.get("method", "GET")
                        }
                    )
                integration_record = integration_result.fetchone()
            except Exception as e:
                logger.error(f"Database error fetching integration details for ID '{integration_id}': {str(e)}")
                # Fallback if DB query fails, to prevent NoneType errors later if integration_record is None
                # chosen_base_url will remain None, leading to default "https://api.example.com" later

            if integration_record:
                if integration_record.path:
                    logger.info(f"Using path from registered API endpoint record: {integration_record.path}")
                    endpoint = integration_record.path

                if integration_record.base_url and isinstance(integration_record.base_url, str) and integration_record.base_url.strip():
                    db_base_url_candidate = integration_record.base_url.strip()
                
                chosen_base_url = db_base_url_candidate # P4, might be overridden
                
                if integration_record.openapi_spec and isinstance(integration_record.openapi_spec, dict):
                    openapi_spec = integration_record.openapi_spec
                    spec_derived_url: Optional[str] = None
                    spec_url_source: Optional[str] = None

                    servers = openapi_spec.get("servers", [])
                    if servers and isinstance(servers, list) and len(servers) > 0:
                        temp_url = servers[0].get("url")
                        if temp_url and isinstance(temp_url, str) and "://" in temp_url.strip():
                            spec_derived_url = temp_url.strip()
                            spec_url_source = "openapi_spec.servers[0].url (P1)"
                        elif temp_url:
                            logger.warning(f"Invalid or empty URL in openapi_spec.servers[0].url: '{temp_url}' for integration {integration_id}")
                    
                    if not spec_derived_url:
                        info_contact_spec = openapi_spec.get("info", {}).get("contact", {})
                        if isinstance(info_contact_spec, dict):
                            temp_url = info_contact_spec.get("url")
                            if temp_url and isinstance(temp_url, str) and "://" in temp_url.strip():
                                spec_derived_url = temp_url.strip()
                                spec_url_source = "openapi_spec.info.contact.url (P2)"
                            elif temp_url:
                                logger.warning(f"Invalid or empty URL in openapi_spec.info.contact.url: '{temp_url}' for integration {integration_id}")
                    
                    if not spec_derived_url:
                        if "host" in openapi_spec:
                            host = openapi_spec.get("host")
                            base_path_val = openapi_spec.get("basePath", "")
                            schemes = openapi_spec.get("schemes", ["https"])
                            
                            if host and isinstance(host, str) and host.strip():
                                scheme = schemes[0] if schemes and isinstance(schemes, list) and schemes[0] else "https"
                                temp_url = f"{scheme}://{host.strip()}{base_path_val if base_path_val else ''}"
                                spec_derived_url = temp_url
                                spec_url_source = "Swagger 2.0 spec host/basePath (P3)"
                            elif host:
                                logger.warning(f"Invalid or empty host in Swagger 2.0 spec: '{host}' for integration {integration_id}")
                    
                    if spec_derived_url:
                        logger.info(f"Using URL from {spec_url_source}: {spec_derived_url} for integration {integration_id}")
                        chosen_base_url = spec_derived_url
                    elif db_base_url_candidate:
                        logger.info(f"No higher priority URL found in OpenAPI spec for integration {integration_id}, using base_url from database (P4): {db_base_url_candidate}")
                    # else: chosen_base_url remains None if db_base_url_candidate was also None.

                    paths = openapi_spec.get("paths", {})
                    method_lower = action_config.get("method", "GET").lower()
                    current_endpoint_for_match = endpoint.lstrip('/')
                    for path_key, methods_in_path in paths.items():
                        # Ensure methods_in_path is a dict before trying to access method_lower key
                        if isinstance(methods_in_path, dict) and current_endpoint_for_match in path_key.lstrip('/') and method_lower in methods_in_path:
                            logger.info(f"Found matching path in OpenAPI spec for integration {integration_id}, updating endpoint to: {path_key}")
                            endpoint = path_key
                            break
                elif db_base_url_candidate:
                    logger.info(f"No OpenAPI spec found for integration {integration_id}, using base_url from database (P4): {db_base_url_candidate}")
            # End of `if integration_record:`
            
            if not chosen_base_url:
                chosen_base_url = "https://api.example.com" # P5 Fallback
                logger.warning(
                    f"No valid base_url found from DB or OpenAPI spec for integration_id '{integration_id}'. "
                    f"Using default: {chosen_base_url}"
                )
            
            base_url_for_construction = chosen_base_url
        
        except Exception as e: # Catching errors during DB query or initial processing
            logger.error(f"Error processing integration details or spec for integration_id '{integration_id}': {str(e)}")
            base_url_for_construction = "https://api.example.com" # Default fallback
        
        # Ensure proper URL construction
        if base_url_for_construction.endswith('/'):
            base_url_for_construction = base_url_for_construction[:-1]
            
        if not endpoint.startswith('/'):
            endpoint = '/' + endpoint
            
        url = base_url_for_construction + endpoint
        
        for param_name, param_value in mapped_params.items():
            placeholder = "{" + param_name + "}"
            if placeholder in url:
                url = url.replace(placeholder, str(param_value))
                # mapped_params.pop(param_name, None) # This was in the original, consider if it's needed.
                                                # If mapped_params is used later for query params,
                                                # popping path params is correct.
        
        return url
    
    async def _build_headers(
        self, 
        action_config: Dict[str, Any], 
        tenant_id: uuid.UUID,
        parameters: Dict[str, Any],
        preferred_content_type: Optional[str] = None
    ) -> Dict[str, str]:
        """Build headers for the API request, including authentication.
        
        Args:
            action_config: The action configuration from the template
            tenant_id: The tenant ID for credential lookup
            parameters: Original request parameters
            preferred_content_type: Optional preferred content type for Accept header
            
        Returns:
            Headers for the API request
        """
        headers = {
            "Accept": preferred_content_type or "application/json",
            "User-Agent": "Coherence/1.0"
        }
        
        # Add content-type for non-GET requests
        if action_config.get("method", "GET") != "GET":
            headers["Content-Type"] = "application/json"
        
        # Add authentication if configured
        auth_config = action_config.get("authentication", {})
        auth_type = auth_config.get("type")
        
        if auth_type == "api_key":
            # API key authentication
            key_name = auth_config.get("name", "X-API-Key")
            key_location = auth_config.get("location", "header")
            
            if key_location == "header":
                # Get API key from credentials
                api_key_template = auth_config.get("value", "")
                
                if "{{" in api_key_template:
                    # Get credentials from credential manager
                    api_name = action_config.get("api_key", "default")
                    integration_id = action_config.get("integration_id")
                    
                    try:
                        if integration_id:
                            credentials = await self.credential_manager.retrieve_credentials(
                                uuid.UUID(integration_id)
                            )
                        else:
                            # Mock credentials for testing
                            credentials = {api_name: "default_credential"}
                        
                        if credentials:
                            context = {
                                "credentials": credentials,
                                "parameters": parameters
                            }
                            
                            # Render the template
                            template = Template(api_key_template)
                            api_key = template.render(**context)
                            
                            if api_key:
                                headers[key_name] = api_key
                        else:
                            logger.warning(f"No credentials found for API key '{api_name}', proceeding without authentication")
                    except Exception as e:
                        # Log error but continue without auth - some APIs may work without it
                        logger.warning(f"Error retrieving credentials for auth: {str(e)}, proceeding without authentication")
                else:
                    # Static API key
                    static_key = auth_config.get("value", "")
                    if static_key:
                        headers[key_name] = static_key
                
        elif auth_type == "bearer":
            # Bearer token authentication
            token_template = auth_config.get("value", "")
            
            if "{{" in token_template:
                # Get credentials
                api_name = action_config.get("api_key", "default")
                integration_id = action_config.get("integration_id")
                
                try:
                    if integration_id:
                        credentials = await self.credential_manager.retrieve_credentials(
                            uuid.UUID(integration_id)
                        )
                    else:
                        # Mock credentials for testing
                        credentials = {api_name: "default_credential"}
                    
                    if credentials:
                        context = {
                            "credentials": credentials,
                            "parameters": parameters
                        }
                        
                        # Render the template
                        template = Template(token_template)
                        token = template.render(**context)
                        
                        if token:
                            headers["Authorization"] = f"Bearer {token}"
                    else:
                        logger.warning(f"No credentials found for Bearer token '{api_name}', proceeding without authentication")
                except Exception as e:
                    # Log error but continue without auth - some APIs may work without it
                    logger.warning(f"Error retrieving credentials for Bearer auth: {str(e)}, proceeding without authentication")
            elif token_template:
                # Static token
                headers["Authorization"] = f"Bearer {token_template}"
                    
        elif auth_type == "basic":
            # Basic authentication
            username_template = auth_config.get("username", "")
            password_template = auth_config.get("password", "")
            
            if "{{" in username_template or "{{" in password_template:
                # Get credentials
                api_name = action_config.get("api_key", "default")
                integration_id = action_config.get("integration_id")
                
                try:
                    if integration_id:
                        credentials = await self.credential_manager.retrieve_credentials(
                            uuid.UUID(integration_id)
                        )
                    else:
                        # Mock credentials for testing
                        credentials = {api_name: "default_credential"}
                    
                    if credentials:
                        context = {
                            "credentials": credentials,
                            "parameters": parameters
                        }
                        
                        # Render the templates
                        username_tmpl = Template(username_template)
                        password_tmpl = Template(password_template)
                        
                        username = username_tmpl.render(**context)
                        password = password_tmpl.render(**context)
                        
                        if username and password:
                            import base64
                            auth_str = f"{username}:{password}"
                            encoded = base64.b64encode(auth_str.encode()).decode()
                            headers["Authorization"] = f"Basic {encoded}"
                    else:
                        logger.warning(f"No credentials found for Basic auth '{api_name}', proceeding without authentication")
                except Exception as e:
                    # Log error but continue without auth - some APIs may work without it
                    logger.warning(f"Error retrieving credentials for Basic auth: {str(e)}, proceeding without authentication")
            elif username_template and password_template:
                # Static username/password
                import base64
                auth_str = f"{username_template}:{password_template}"
                encoded = base64.b64encode(auth_str.encode()).decode()
                headers["Authorization"] = f"Basic {encoded}"
        
        return headers
    
    async def _build_body(
        self, action_config: Dict[str, Any], mapped_params: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Build the request body for the API request.
        
        Args:
            action_config: The action configuration from the template
            mapped_params: The mapped parameters
            
        Returns:
            Request body for the API request or None for GET requests
        """
        # No body for GET requests
        if action_config.get("method", "GET") == "GET":
            return None
        
        # If there's a specific body parameter, use that
        if "body" in mapped_params:
            body_value = mapped_params.pop("body")
            
            # If body is a string that looks like JSON, parse it
            if isinstance(body_value, str) and body_value.strip().startswith("{"):
                try:
                    return json.loads(body_value)
                except json.JSONDecodeError:
                    return body_value
            
            return body_value
        
        # Otherwise, use all remaining parameters as the body
        return mapped_params
    
    async def _map_response(
        self, action_config: Dict[str, Any], response_data: Any
    ) -> Dict[str, Any]:
        """Map API response to template variables using response mapping.
        
        Args:
            action_config: The action configuration from the template
            response_data: The raw API response data
            
        Returns:
            Mapped response for template rendering
        """
        if "response_mapping" not in action_config or action_config.get("response_mapping") is None:
            # If no mapping is specified, return the data directly
            # This allows the API response to be available as-is
            return response_data
        
        response_mapping = action_config["response_mapping"]
        mapped_response = {}
        
        # Set up the template context
        context = {"response": response_data}
        
        # Process each response mapping
        for result_key, value_template in response_mapping.items():
            if isinstance(value_template, str) and "{{" in value_template:
                # This is a Jinja2 expression
                try:
                    template = Template(value_template)
                    mapped_value = template.render(**context)
                    mapped_response[result_key] = mapped_value
                except Exception as e:
                    logger.warning(
                        f"Error mapping response field {result_key}: {str(e)}",
                        extra={"template": value_template}
                    )
                    mapped_response[result_key] = None
            else:
                # Static value
                mapped_response[result_key] = value_template
                
        return mapped_response
    
    def _on_api_status_change(self, api_key: str, old_status: ApiStatus, new_status: ApiStatus) -> None:
        """Callback for API status changes from health monitor.
        
        Args:
            api_key: The API key that changed status
            old_status: Previous API status
            new_status: New API status
        """
        # If API went from DOWN/DEGRADED to HEALTHY, reset the circuit breaker
        if old_status in (ApiStatus.DOWN, ApiStatus.DEGRADED) and new_status == ApiStatus.HEALTHY:
            if api_key in self.circuit_breakers:
                # Reset the circuit breaker
                self.circuit_breakers[api_key].reset()
                logger.info(f"Circuit breaker reset for {api_key} due to health recovery")
                
        # If API went from HEALTHY to DOWN, trip the circuit breaker
        elif old_status == ApiStatus.HEALTHY and new_status == ApiStatus.DOWN:
            if api_key in self.circuit_breakers:
                # Trip the circuit breaker
                self.circuit_breakers[api_key].trip()
                logger.warning(f"Circuit breaker tripped for {api_key} due to health monitor DOWN status")
    
    async def _handle_fallback(
        self,
        action_config: Dict[str, Any],
        parameters: Dict[str, Any],
        tenant_id: uuid.UUID,
        error_type: str = "unknown",
        error_message: str = "Unknown error"
    ) -> Dict[str, Any]:
        """Handle fallback when API request fails.
        
        Args:
            action_config: The action configuration from the template
            parameters: The intent parameters
            tenant_id: The tenant ID
            error_type: Type of error that occurred
            error_message: Error message
            
        Returns:
            Fallback response
        """
        # Log the fallback
        logger.warning(
            f"Using fallback for API {action_config.get('api_key', 'unknown')}: {error_type} - {error_message}",
            extra={"tenant_id": str(tenant_id)}
        )
        
        return {
            "success": False,
            "error": {
                "type": error_type,
                "message": error_message,
                "fallback_used": True
            },
            "fallback": {
                "template": action_config.get("error_handling", {}).get("fallback_template")
            }
        }


async def get_dynamic_executor(
    credential_manager: CredentialManager,
    http_client: Optional[httpx.AsyncClient] = None,
    template_renderer: Optional[TemplateRenderer] = None,
    response_cache: Optional[ApiResponseCache] = None,
    health_monitor: Optional[ApiHealthMonitor] = None
) -> DynamicActionExecutor:
    """Factory function to create a DynamicActionExecutor.
    
    Args:
        credential_manager: Manager for API credentials
        http_client: Optional HTTP client for making requests
        template_renderer: Optional template renderer for parameter mapping
        response_cache: Optional cache for API responses
        health_monitor: Optional health monitor for API health checking
        
    Returns:
        Initialized DynamicActionExecutor
    """
    # Create response cache if not provided
    if response_cache is None:
        response_cache = await get_api_response_cache()
        
    # Create health monitor if not provided
    if health_monitor is None:
        health_monitor = await get_api_health_monitor()
        
    return DynamicActionExecutor(
        credential_manager=credential_manager,
        http_client=http_client,
        template_renderer=template_renderer,
        response_cache=response_cache,
        health_monitor=health_monitor
    )