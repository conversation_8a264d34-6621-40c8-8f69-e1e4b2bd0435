"""
Intent mapper for OpenAPI operations.

This module generates intent definitions from OpenAPI operations,
including examples and parameter mappings.
"""

import logging
import uuid
from typing import Any, Dict, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.intent_pipeline.schemas.intent import (
    IntentDefinition,
    ParameterDefinition,
    ParameterType,
)
from src.coherence.models.integration import APIEndpoint, APIIntegration
from src.coherence.template_system.services.template_service import (
    TemplateService,
)

logger = logging.getLogger(__name__)


class IntentMapper:
    """
    Mapper for generating intents from OpenAPI operations.

    This class handles:
    1. Creation of intent definitions from API endpoints
    2. Generation of natural language examples
    3. Mapping of API parameters to intent parameters
    4. Integration with the intent pipeline
    """

    def __init__(self, db: AsyncSession):
        """Initialize the intent mapper.

        Args:
            db: Database session for fetching API metadata
        """
        self.db = db
        self.template_service = TemplateService()  # Initialize TemplateService without db

    async def generate_intent(
        self, endpoint_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> IntentDefinition:
        """
        Generate an intent definition from an API endpoint.

        Args:
            endpoint_id: ID of the API endpoint
            tenant_id: ID of the tenant

        Returns:
            Generated intent definition

        Raises:
            ValueError: If the endpoint is not found
        """
        # Fetch endpoint and integration data
        endpoint = await self.db.get(APIEndpoint, endpoint_id)
        if not endpoint:
            raise ValueError(f"Endpoint not found with ID: {endpoint_id}")

        integration = await self.db.get(APIIntegration, endpoint.integration_id)
        if not integration:
            raise ValueError(
                f"Integration not found with ID: {endpoint.integration_id}"
            )

        # Extract endpoint details from OpenAPI spec
        endpoint_spec = self._get_endpoint_spec(
            integration.openapi_spec, endpoint.path, endpoint.method.lower()
        )

        if not endpoint_spec:
            raise ValueError(
                f"Endpoint spec not found for {endpoint.method} {endpoint.path}"
            )

        # Generate intent name and description
        intent_name = self._generate_intent_name(
            integration.name, endpoint.operation_id, endpoint.path, endpoint.method
        )

        description = self._generate_description(
            integration.name, endpoint_spec, endpoint.path, endpoint.method
        )

        # Extract parameters and map to intent parameters
        api_parameters = self._extract_parameters(endpoint_spec)
        intent_parameters = await self._map_to_intent_parameters(api_parameters)

        # Generate example phrases
        examples = await self._generate_examples(
            integration.name,
            intent_name,
            description,
            endpoint.path,
            endpoint.method,
            intent_parameters,
            tenant_id,
        )

        # Create the intent definition
        intent = IntentDefinition(
            name=intent_name,
            description=description,
            examples=examples,
            parameters={param.name: param for param in intent_parameters},
            required_fields=set(
                param.name for param in intent_parameters if param.required
            ),
            action_class=endpoint.action_class_name,
            is_fallback=False,
            group=integration.name,
        )

        return intent

    async def generate_intents_for_integration(
        self, integration_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> List[IntentDefinition]:
        """
        Generate intent definitions for all endpoints in an integration.

        Args:
            integration_id: ID of the API integration
            tenant_id: ID of the tenant

        Returns:
            List of generated intent definitions

        Raises:
            ValueError: If the integration is not found
        """
        # Fetch integration data
        integration = await self.db.get(APIIntegration, integration_id)
        if not integration:
            raise ValueError(f"Integration not found with ID: {integration_id}")

        # Fetch all enabled endpoints for the integration
        result = await self.db.execute(
            "SELECT * FROM api_endpoints WHERE integration_id = :integration_id AND enabled = true",
            {"integration_id": integration_id},
        )
        endpoints = result.fetchall()

        # Generate intents for each endpoint
        intents = []
        for endpoint in endpoints:
            try:
                intent = await self.generate_intent(endpoint.id, tenant_id)
                intents.append(intent)
            except Exception as e:
                logger.warning(
                    f"Failed to generate intent for endpoint {endpoint.id}: {str(e)}"
                )

        return intents

    def _get_endpoint_spec(
        self, openapi_spec: Dict[str, Any], path: str, method: str
    ) -> Optional[Dict[str, Any]]:
        """
        Extract the endpoint specification from the OpenAPI document.

        Args:
            openapi_spec: Full OpenAPI specification
            path: API path
            method: HTTP method (lowercase)

        Returns:
            Endpoint specification or None if not found
        """
        paths = openapi_spec.get("paths", {})
        path_item = paths.get(path, {})

        return path_item.get(method)

    def _generate_intent_name(
        self, integration_name: str, operation_id: Optional[str], path: str, method: str
    ) -> str:
        """
        Generate an intent name from operation details.

        Args:
            integration_name: Name of the API integration
            operation_id: OpenAPI operationId or None
            path: API path
            method: HTTP method

        Returns:
            Intent name (snake_case)
        """
        # Clean the integration name for use in intent name
        clean_integration = integration_name.lower().replace(" ", "_")

        if operation_id:
            # Convert camelCase to snake_case
            import re

            name = re.sub(r"([a-z0-9])([A-Z])", r"\1_\2", operation_id).lower()
            return f"{clean_integration}__{name}"

        # Generate from path and method
        path_segments = [
            s
            for s in path.split("/")
            if s and not (s.startswith("{") and s.endswith("}"))
        ]
        path_name = "_".join(path_segments)

        return f"{clean_integration}__{method.lower()}_{path_name}"

    def _generate_description(
        self,
        integration_name: str,
        endpoint_spec: Dict[str, Any],
        path: str,
        method: str,
    ) -> str:
        """
        Generate a human-friendly description from endpoint details.

        Args:
            integration_name: Name of the API integration
            endpoint_spec: OpenAPI operation specification
            path: API path
            method: HTTP method

        Returns:
            Description string
        """
        # Use existing description or summary if available
        summary = endpoint_spec.get("summary", "")
        description = endpoint_spec.get("description", "")

        if summary and description:
            return f"{summary} - {integration_name}"
        elif summary:
            return f"{summary} - {integration_name}"
        elif description:
            return f"{description} - {integration_name}"

        # Generate description from method and path
        method_verbs = {
            "get": "Get",
            "post": "Create",
            "put": "Update",
            "patch": "Update",
            "delete": "Delete",
        }

        verb = method_verbs.get(method.lower(), method.capitalize())

        # Extract resource from path
        path_segments = [
            s
            for s in path.split("/")
            if s and not (s.startswith("{") and s.endswith("}"))
        ]
        resource = " ".join(path_segments).title() if path_segments else "resource"

        # Handle singular form for readability
        if resource.endswith("s") and len(resource) > 1:
            resource_singular = resource[:-1]
        else:
            resource_singular = resource

        return f"{verb} {resource_singular} from {integration_name}"

    def _extract_parameters(
        self, endpoint_spec: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Extract parameter definitions from endpoint spec.

        Args:
            endpoint_spec: OpenAPI operation specification

        Returns:
            List of parameter definitions
        """
        parameters = []

        # Path, query, and header parameters
        for param in endpoint_spec.get("parameters", []):
            if not isinstance(param, dict) or "name" not in param:
                continue

            parameters.append(
                {
                    "name": param.get("name"),
                    "type": param.get("schema", {}).get("type", "string"),
                    "required": param.get("required", param.get("in") == "path"),
                    "description": param.get("description", ""),
                    "location": param.get("in", "query"),
                    "schema": param.get("schema", {}),
                }
            )

        # Request body (OpenAPI 3.0+)
        request_body = endpoint_spec.get("requestBody", {})
        if request_body:
            content = request_body.get("content", {})
            for _content_type, content_schema in content.items():
                schema = content_schema.get("schema", {})
                properties = schema.get("properties", {})

                # For top-level properties in the body
                for prop_name, prop_schema in properties.items():
                    parameters.append(
                        {
                            "name": prop_name,
                            "type": prop_schema.get("type", "string"),
                            "required": prop_name in schema.get("required", []),
                            "description": prop_schema.get("description", ""),
                            "location": "body",
                            "schema": prop_schema,
                        }
                    )

                break  # Just use the first content type

        return parameters

    async def _map_to_intent_parameters(
        self, api_parameters: List[Dict[str, Any]]
    ) -> List[ParameterDefinition]:
        """
        Map API parameters to intent parameters.

        Args:
            api_parameters: Parameters from the OpenAPI spec

        Returns:
            List of intent parameter definitions
        """
        intent_parameters = []

        for param in api_parameters:
            logger.debug(f"Processing parameter: {param}")
            
            # Map API parameter type to intent parameter type
            openapi_type = param.get("type", "string")
            schema = param.get("schema", {})
            
            logger.debug(f"Parameter type mapping - openapi_type: {openapi_type}, schema: {schema}")
            
            param_type = self._map_to_parameter_type(openapi_type, schema)
            logger.debug(f"Mapped parameter type: {param_type}")

            # Create parameter definition
            param_def = ParameterDefinition(
                name=param.get("name", ""),
                type=param_type,
                required=param.get("required", False),
                description=param.get("description", "")
                or f"{param.get('name')} parameter",
                prompt=f"What is the value for {param.get('name')}?",
                options=self._extract_options(schema),
                validation_regex=self._extract_pattern(schema),
                unit=self._extract_unit(schema),
            )

            intent_parameters.append(param_def)

        return intent_parameters

    def _map_to_parameter_type(self, openapi_type: str, schema: Dict[str, Any] = None) -> ParameterType:
        """
        Map OpenAPI type to intent parameter type.

        Args:
            openapi_type: Type from OpenAPI schema
            schema: Optional schema to check for format

        Returns:
            Corresponding ParameterType enum value
        """
        try:
            # Check for date format first
            if schema and schema.get("format"):
                format_type = schema["format"].lower()
                logger.debug(f"Found format type: {format_type}")
                
                if format_type == "date":
                    return ParameterType.DATE
                elif format_type == "date-time":
                    return ParameterType.DATETIME
                elif format_type == "time":
                    return ParameterType.TIME

            # Check for numeric types
            if openapi_type == "integer":
                logger.debug("Mapping integer type to NUMBER")
                return ParameterType.NUMBER
            elif openapi_type == "number":
                logger.debug("Mapping number type to NUMBER")
                return ParameterType.NUMBER

            # Check for other specific types
            type_mapping = {
                "string": ParameterType.STRING,
                "boolean": ParameterType.BOOLEAN,
                "array": ParameterType.STRING,  # Arrays are handled as strings in natural language
                "object": ParameterType.STRING,  # Objects are handled as strings in natural language
            }

            result = type_mapping.get(openapi_type.lower(), ParameterType.STRING)
            logger.debug(f"Mapped type {openapi_type} to {result}")
            return result

        except Exception as e:
            logger.error(f"Error in type mapping: {str(e)}")
            raise

    def _extract_options(
        self, schema: Dict[str, Any]
    ) -> Optional[List[Dict[str, str]]]:
        """
        Extract options from schema enum.

        Args:
            schema: Parameter schema

        Returns:
            List of options or None
        """
        enum_values = schema.get("enum")
        if not enum_values or not isinstance(enum_values, list):
            return None

        return [{"value": str(val), "label": str(val)} for val in enum_values]

    def _extract_pattern(self, schema: Dict[str, Any]) -> Optional[str]:
        """
        Extract validation pattern from schema.

        Args:
            schema: Parameter schema

        Returns:
            Validation regex or None
        """
        return schema.get("pattern")

    def _extract_unit(self, schema: Dict[str, Any]) -> Optional[str]:
        """
        Extract unit from schema.

        Args:
            schema: Parameter schema

        Returns:
            Unit string or None
        """
        # Handle date formats
        if schema.get("format") == "date":
            return "date"
        elif schema.get("format") == "date-time":
            return "datetime"
        elif schema.get("format") == "email":
            return "email"

        # Check for unit in extensions
        for key, value in schema.items():
            if key.startswith("x-") and "unit" in key:
                return str(value)

        return None

    async def _generate_examples(
        self,
        integration_name: str,
        intent_name: str,
        description: str,
        path: str,
        method: str,
        parameters: List[ParameterDefinition],
        tenant_id: uuid.UUID,
    ) -> List[str]:
        """
        Generate natural language examples for an intent.

        Args:
            integration_name: Name of the API integration
            intent_name: Name of the intent
            description: Intent description
            path: API path
            method: HTTP method
            parameters: Intent parameters
            tenant_id: ID of the tenant

        Returns:
            List of example phrases
        """
        try:
            # Get template using the database session
            template = await self.template_service.get_template(
                db=self.db,
                tenant_id=tenant_id,
                category="intent_router",
                key="openapi_example_generator",
            )

            if template:
                # Use template to generate examples
                result = await self.template_service.render_template(
                    db=self.db,  # Pass db session here as well
                    template_id=template.id,
                    context={
                        "integration_name": integration_name,
                        "intent_name": intent_name,
                        "description": description,
                        "path": path,
                        "method": method,
                        "parameters": [p.model_dump() for p in parameters],
                    },
                )

                if result and isinstance(result, list):
                    return result[:5]  # Limit to 5 examples
        except Exception as e:
            logger.warning(f"Failed to generate examples using template: {str(e)}")

        # Fallback to basic examples if template fails or template not found
        return self._generate_basic_examples(
            integration_name, description, method, parameters
        )

    def _generate_basic_examples(
        self,
        integration_name: str,
        description: str,
        method: str,
        parameters: List[ParameterDefinition],
    ) -> List[str]:
        """
        Generate basic examples when template-based generation fails.

        Args:
            integration_name: Name of the API integration
            description: Intent description
            method: HTTP method
            parameters: Intent parameters

        Returns:
            List of basic example phrases
        """
        examples = []

        # Action verbs by method
        method_verbs = {
            "get": ["get", "fetch", "retrieve", "show", "find"],
            "post": ["create", "add", "post", "make", "submit"],
            "put": ["update", "change", "modify", "edit", "replace"],
            "patch": ["update", "modify", "change", "patch", "edit"],
            "delete": ["delete", "remove", "destroy", "cancel", "eliminate"],
        }

        # Get applicable verbs
        verbs = method_verbs.get(method.lower(), ["access"])

        # First example: simplest form
        examples.append(f"{verbs[0]} from {integration_name}")

        # Second example: with description
        examples.append(description)

        # Third example: alternate verb
        if len(verbs) > 1:
            examples.append(f"{verbs[1]} from {integration_name}")

        # Generate examples with parameters if available
        required_params = [p for p in parameters if p.required]
        if required_params:
            param_example = f"{verbs[0]} from {integration_name} with "
            param_example += ", ".join(
                [f"{p.name}=example{i}" for i, p in enumerate(required_params)]
            )
            examples.append(param_example)

        # Add one more specific example if we have parameters
        if parameters:
            examples.append(f"Use {integration_name} to {verbs[0]} data")

        return examples[:5]  # Limit to 5 examples


# Factory function
async def get_intent_mapper(db: AsyncSession) -> IntentMapper:
    """
    Factory function to create an IntentMapper.

    Args:
        db: Database session

    Returns:
        Initialized IntentMapper
    """
    return IntentMapper(db)
