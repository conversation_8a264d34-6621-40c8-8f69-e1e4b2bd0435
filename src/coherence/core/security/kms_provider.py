"""
KMS integration for secure key management.

This module provides interfaces and implementations for
key management services to secure sensitive data.
"""

import abc
import base64
import os
from typing import Dict, Optional

import boto3
import structlog
from botocore.exceptions import ClientError, NoCredentialsError

from src.coherence.core.config import get_settings

logger = structlog.get_logger(__name__)


class KMSProviderError(Exception):
    """Exception raised when KMS operations fail."""

    pass


class KMSProvider(abc.ABC):
    """
    Abstract base class for KMS providers.

    Defines the interface for encrypting and decrypting data
    using a Key Management Service.
    """

    @abc.abstractmethod
    def encrypt(
        self, plaintext: bytes, context: Optional[Dict[str, str]] = None
    ) -> bytes:
        """
        Encrypt data using KMS.

        Args:
            plaintext: The data to encrypt
            context: Optional encryption context for additional security

        Returns:
            Encrypted ciphertext

        Raises:
            KMSProviderError: If encryption fails
        """
        pass

    @abc.abstractmethod
    def decrypt(
        self, ciphertext: bytes, context: Optional[Dict[str, str]] = None
    ) -> bytes:
        """
        Decrypt data using KMS.

        Args:
            ciphertext: The encrypted data
            context: The same encryption context used for encryption

        Returns:
            Decrypted plaintext

        Raises:
            KMSProviderError: If decryption fails
        """
        pass

    @abc.abstractmethod
    def generate_data_key(
        self, context: Optional[Dict[str, str]] = None
    ) -> Dict[str, bytes]:
        """
        Generate a data encryption key.

        The returned key can be used for local encryption operations.

        Args:
            context: Optional encryption context for additional security

        Returns:
            Dictionary with plaintext and encrypted versions of the data key

        Raises:
            KMSProviderError: If key generation fails
        """
        pass


class LocalKMSProvider(KMSProvider):
    """
    Local implementation of the KMS provider interface.

    For development and testing only - not secure for production use.
    """

    def __init__(self, key_id: Optional[str] = None):
        """
        Initialize the local KMS provider.

        Args:
            key_id: Optional identifier for the master key
        """
        self.master_key = os.environ.get("LOCAL_MASTER_KEY")

        if not self.master_key:
            settings = get_settings()
            self.master_key = getattr(settings, "local_master_key", None)

        if not self.master_key:
            # Generate a key for development use only
            logger.warning(
                "Generating a temporary master key for development. This is not secure for production."
            )
            self.master_key = base64.b64encode(os.urandom(32)).decode()

        # Convert to bytes if it's a string
        if isinstance(self.master_key, str):
            self.master_key = base64.b64decode(self.master_key)

    def encrypt(
        self, plaintext: bytes, context: Optional[Dict[str, str]] = None
    ) -> bytes:
        """
        Encrypt data using the local master key.

        Args:
            plaintext: The data to encrypt
            context: Ignored in the local implementation

        Returns:
            Encrypted ciphertext
        """
        from cryptography.hazmat.primitives.ciphers.aead import AESGCM

        # Generate a random nonce (12 bytes for AES-GCM)
        nonce = os.urandom(12)

        # Encrypt the data
        aes = AESGCM(self.master_key)
        ciphertext = aes.encrypt(nonce, plaintext, None)

        # Prepend the nonce to the ciphertext
        return nonce + ciphertext

    def decrypt(
        self, ciphertext: bytes, context: Optional[Dict[str, str]] = None
    ) -> bytes:
        """
        Decrypt data using the local master key.

        Args:
            ciphertext: The encrypted data
            context: Ignored in the local implementation

        Returns:
            Decrypted plaintext

        Raises:
            KMSProviderError: If decryption fails
        """
        from cryptography.hazmat.primitives.ciphers.aead import AESGCM

        try:
            # Extract the nonce from the first 12 bytes
            nonce = ciphertext[:12]
            actual_ciphertext = ciphertext[12:]

            # Decrypt the data
            aes = AESGCM(self.master_key)
            plaintext = aes.decrypt(nonce, actual_ciphertext, None)

            return plaintext
        except Exception as e:
            raise KMSProviderError(f"Failed to decrypt data: {str(e)}") from e

    def generate_data_key(
        self, context: Optional[Dict[str, str]] = None
    ) -> Dict[str, bytes]:
        """
        Generate a data encryption key.

        Args:
            context: Ignored in the local implementation

        Returns:
            Dictionary with plaintext and encrypted versions of the data key
        """
        # Generate a random 32-byte key (256 bits)
        plaintext_key = os.urandom(32)

        # Encrypt the key with the master key
        encrypted_key = self.encrypt(plaintext_key)

        return {"plaintext": plaintext_key, "ciphertext": encrypted_key}


class AWSKMSProvider(KMSProvider):
    """
    AWS KMS implementation.

    Uses AWS Key Management Service for secure key operations.
    """

    def __init__(self, key_id: Optional[str] = None, region: Optional[str] = None):
        """
        Initialize the AWS KMS provider.

        Args:
            key_id: The ARN or ID of the KMS key
            region: AWS region where the KMS key is located
        """
        self.settings = get_settings()

        # Get key ID from constructor, environment, or settings
        self.key_id = key_id
        if not self.key_id:
            self.key_id = os.environ.get("AWS_KMS_KEY_ID")
        if not self.key_id:
            self.key_id = getattr(self.settings, "aws_kms_key_id", None)

        if not self.key_id:
            raise KMSProviderError("AWS KMS key ID not provided")

        # Get region from constructor, environment, or settings
        self.region = region
        if not self.region:
            self.region = os.environ.get("AWS_REGION")
        if not self.region:
            self.region = getattr(self.settings, "aws_region", "us-east-1")

        # Initialize the KMS client
        try:
            self.kms_client = boto3.client("kms", region_name=self.region)
            logger.info(f"Initialized AWS KMS client for region {self.region}")
        except (NoCredentialsError, ClientError) as e:
            logger.error(f"Failed to initialize AWS KMS client: {str(e)}")
            raise KMSProviderError(
                f"Failed to initialize AWS KMS client: {str(e)}"
            ) from e

    def encrypt(
        self, plaintext: bytes, context: Optional[Dict[str, str]] = None
    ) -> bytes:
        """
        Encrypt data using AWS KMS.

        Args:
            plaintext: The data to encrypt
            context: Encryption context for additional security

        Returns:
            Encrypted ciphertext

        Raises:
            KMSProviderError: If encryption fails
        """
        try:
            kwargs = {"KeyId": self.key_id, "Plaintext": plaintext}

            # Add encryption context if provided
            if context:
                kwargs["EncryptionContext"] = context

            # Encrypt the data
            response = self.kms_client.encrypt(**kwargs)

            return response["CiphertextBlob"]
        except Exception as e:
            logger.error(f"AWS KMS encrypt failed: {str(e)}")
            raise KMSProviderError(
                f"Failed to encrypt data with AWS KMS: {str(e)}"
            ) from e

    def decrypt(
        self, ciphertext: bytes, context: Optional[Dict[str, str]] = None
    ) -> bytes:
        """
        Decrypt data using AWS KMS.

        Args:
            ciphertext: The encrypted data
            context: Encryption context that was used for encryption

        Returns:
            Decrypted plaintext

        Raises:
            KMSProviderError: If decryption fails
        """
        try:
            kwargs = {"CiphertextBlob": ciphertext}

            # Add encryption context if provided
            if context:
                kwargs["EncryptionContext"] = context

            # Decrypt the data
            response = self.kms_client.decrypt(**kwargs)

            return response["Plaintext"]
        except Exception as e:
            logger.error(f"AWS KMS decrypt failed: {str(e)}")
            raise KMSProviderError(
                f"Failed to decrypt data with AWS KMS: {str(e)}"
            ) from e

    def generate_data_key(
        self, context: Optional[Dict[str, str]] = None
    ) -> Dict[str, bytes]:
        """
        Generate a data encryption key using AWS KMS.

        Args:
            context: Encryption context for additional security

        Returns:
            Dictionary with plaintext and encrypted versions of the data key

        Raises:
            KMSProviderError: If key generation fails
        """
        try:
            kwargs = {"KeyId": self.key_id, "KeySpec": "AES_256"}

            # Add encryption context if provided
            if context:
                kwargs["EncryptionContext"] = context

            # Generate the data key
            response = self.kms_client.generate_data_key(**kwargs)

            return {
                "plaintext": response["Plaintext"],
                "ciphertext": response["CiphertextBlob"],
            }
        except Exception as e:
            logger.error(f"AWS KMS generate_data_key failed: {str(e)}")
            raise KMSProviderError(
                f"Failed to generate data key with AWS KMS: {str(e)}"
            ) from e


# Factory function to create the appropriate KMS provider
def get_kms_provider(provider_type: Optional[str] = None) -> KMSProvider:
    """
    Create a KMS provider based on configuration.

    Args:
        provider_type: Type of KMS provider ("aws", "azure", "gcp", "local")

    Returns:
        Initialized KMS provider

    Raises:
        ValueError: If the provider type is not supported
    """
    settings = get_settings()

    # Determine provider type from input, environment, or settings
    if not provider_type:
        provider_type = os.environ.get("KMS_PROVIDER_TYPE")

    if not provider_type:
        provider_type = getattr(settings, "kms_provider_type", "local")

    # Create the appropriate provider
    provider_type = provider_type.lower()

    if provider_type == "aws":
        key_id = os.environ.get("AWS_KMS_KEY_ID") or getattr(
            settings, "aws_kms_key_id", None
        )
        region = os.environ.get("AWS_REGION") or getattr(
            settings, "aws_region", "us-east-1"
        )

        if not key_id:
            logger.warning(
                "AWS KMS key ID not configured. Falling back to local provider."
            )
            return LocalKMSProvider()

        try:
            return AWSKMSProvider(key_id=key_id, region=region)
        except Exception as e:
            logger.error(
                f"Failed to initialize AWS KMS provider: {str(e)}. Falling back to local provider."
            )
            if settings.ENV == "production":
                raise ValueError("Cannot use local KMS provider in production") from e
            return LocalKMSProvider()

    elif provider_type == "azure":
        # Future implementation for Azure Key Vault
        logger.warning(
            "Azure KMS provider not implemented. Falling back to local provider."
        )
        if settings.ENV == "production":
            raise ValueError("Cannot use local KMS provider in production")
        return LocalKMSProvider()

    elif provider_type == "gcp":
        # Future implementation for Google Cloud KMS
        logger.warning(
            "GCP KMS provider not implemented. Falling back to local provider."
        )
        if settings.ENV == "production":
            raise ValueError("Cannot use local KMS provider in production")
        return LocalKMSProvider()

    elif provider_type == "local":
        # Check if we're in production
        if settings.ENV == "production":
            logger.error("Local KMS provider is not secure for production use.")
            raise ValueError("Cannot use local KMS provider in production")

        return LocalKMSProvider()

    else:
        raise ValueError(f"Unsupported KMS provider type: {provider_type}")
