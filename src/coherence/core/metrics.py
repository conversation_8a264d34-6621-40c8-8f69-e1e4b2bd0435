"""
Prometheus metrics for monitoring Coherence performance and usage.

This module defines metrics that track the performance of the intent
resolution pipeline, parameter extraction, and action execution.
"""

from typing import Optional

from prometheus_client import Counter, Gauge, Histogram, Summary

# Basic health check metric
METRICS_UP = Gauge("coherence_metrics_up", "Indicates that metrics endpoint is up")

# Intent Resolution Metrics
INTENT_RESOLUTION_COUNT = Counter(
    "coherence_intent_resolution_total",
    "Total number of intent resolution attempts",
    ["tenant_id", "result"],
)

INTENT_RESOLUTION_LATENCY = Histogram(
    "coherence_intent_resolution_seconds",
    "Intent resolution latency in seconds",
    ["tier", "result"],
    buckets=(0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0),
)

INTENT_CONFIDENCE = Histogram(
    "coherence_intent_confidence",
    "Confidence scores for intent matches",
    ["tier"],
    buckets=(0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.95, 0.99),
)

# Parameter Extraction Metrics
PARAMETER_EXTRACTION_LATENCY = Histogram(
    "coherence_parameter_extraction_seconds",
    "Parameter extraction latency in seconds",
    ["method"],  # "pattern", "llm"
    buckets=(0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0),
)

PARAMETER_EXTRACTION_SUCCESS = Counter(
    "coherence_parameter_extraction_success_total",
    "Total number of successful parameter extractions",
    ["param_type", "method"],
)

PARAMETER_EXTRACTION_FAILURE = Counter(
    "coherence_parameter_extraction_failure_total",
    "Total number of failed parameter extractions",
    ["param_type", "method", "reason"],
)

# Action Execution Metrics
ACTION_EXECUTION_LATENCY = Histogram(
    "coherence_action_execution_seconds",
    "Action execution latency in seconds",
    ["intent", "result"],
    buckets=(0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0),
)

ACTION_EXECUTION_COUNT = Counter(
    "coherence_action_execution_total",
    "Total number of action executions",
    ["intent", "result"],
)

# Conversation Metrics
CONVERSATION_TURNS = Histogram(
    "coherence_conversation_turns",
    "Number of turns in a conversation",
    ["tenant_id"],
    buckets=(1, 2, 3, 4, 5, 7, 10, 15, 20),
)

CONVERSATION_DURATION = Histogram(
    "coherence_conversation_duration_seconds",
    "Duration of conversations in seconds",
    ["tenant_id"],
    buckets=(1, 5, 15, 30, 60, 120, 300, 600),
)

ACTIVE_CONVERSATIONS = Gauge(
    "coherence_active_conversations",
    "Number of active conversations",
    ["tenant_id"],
)

# LLM and Embedding Metrics
LLM_CALL_LATENCY = Histogram(
    "coherence_llm_call_seconds",
    "LLM API call latency in seconds",
    ["provider", "model", "operation"],
    buckets=(0.1, 0.25, 0.5, 1.0, 2.0, 3.0, 5.0, 10.0),
)

LLM_TOKEN_USAGE = Counter(
    "coherence_llm_token_usage_total",
    "Total number of tokens used in LLM calls",
    ["tenant_id", "provider", "model", "operation"],
)

EMBEDDING_METRICS = Histogram(
    "coherence_embedding_call_seconds",
    "Embedding API call latency in seconds",
    ["provider", "model"],
    buckets=(0.05, 0.1, 0.25, 0.5, 1.0, 2.0, 3.0, 5.0),
)

# Vector Database Metrics
VECTOR_SEARCH_LATENCY = Histogram(
    "coherence_vector_search_seconds",
    "Vector search latency in seconds",
    ["collection"],
    buckets=(0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5),
)

VECTOR_UPSERT_LATENCY = Histogram(
    "coherence_vector_upsert_seconds",
    "Vector upsert latency in seconds",
    ["collection"],
    buckets=(0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5),
)

VECTOR_DELETE_LATENCY = Histogram(
    "coherence_vector_delete_seconds",
    "Vector delete latency in seconds",
    ["collection"],
    buckets=(0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5),
)

VECTOR_INDEX_SIZE = Gauge(
    "coherence_vector_index_size",
    "Number of vectors in each collection",
    ["tenant_id", "collection"],
)

# System Health Metrics
API_REQUEST_LATENCY = Histogram(
    "coherence_api_request_seconds",
    "API request latency in seconds",
    ["endpoint", "method", "status", "tenant_id"],
    buckets=(0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0),
)

# FastAPI metrics
REQUEST_COUNT = Counter(
    "coherence_http_requests_total",
    "Total HTTP requests",
    ["method", "endpoint", "status", "tenant_id"],
)

REQUEST_LATENCY = Histogram(
    "coherence_http_request_duration_seconds",
    "HTTP request latency in seconds",
    ["method", "endpoint", "status", "tenant_id"],
    buckets=(0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0),
)

DATABASE_QUERY_LATENCY = Histogram(
    "coherence_database_query_seconds",
    "Database query latency in seconds",
    ["operation"],
    buckets=(0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5),
)

CACHE_HIT_RATIO = Gauge(
    "coherence_cache_hit_ratio",
    "Cache hit ratio",
    ["cache_type"],
)

# Error Metrics
ERROR_COUNT = Counter(
    "coherence_error_total",
    "Total number of errors",
    ["component", "error_type", "tenant_id"],
)

ERROR_BY_TYPE = Counter(
    "coherence_error_by_type_total",
    "Total number of errors by error type",
    ["error_code", "tenant_id"],
)

ERROR_BY_ENDPOINT = Counter(
    "coherence_error_by_endpoint_total",
    "Total number of errors by API endpoint",
    ["endpoint", "method", "status_code", "tenant_id"],
)

FALLBACK_STRATEGY_USAGE = Counter(
    "coherence_fallback_strategy_usage_total",
    "Number of times a fallback strategy was used",
    ["strategy", "component"],
)

FALLBACK_STRATEGY_SUCCESS = Counter(
    "coherence_fallback_strategy_success_total",
    "Number of times a fallback strategy succeeded",
    ["strategy", "component"],
)

FALLBACK_STRATEGY_FAILURE = Counter(
    "coherence_fallback_strategy_failure_total",
    "Number of times a fallback strategy failed",
    ["strategy", "component"],
)

CIRCUIT_BREAKER_TRIPS = Counter(
    "coherence_circuit_breaker_trips_total",
    "Number of times a circuit breaker tripped",
    ["service", "circuit_breaker"],
)

CIRCUIT_BREAKER_STATE = Gauge(
    "coherence_circuit_breaker_state",
    "Current state of circuit breakers (0=closed, 1=open, 2=half-open)",
    ["service", "circuit_breaker"],
)

REQUEST_RETRY_ATTEMPTS = Counter(
    "coherence_request_retry_attempts_total",
    "Number of retry attempts for external service requests",
    ["service", "operation"],
)

ERROR_RATE = Summary(
    "coherence_error_rate",
    "Rate of errors over time",
    ["component", "tenant_id"],
)


def increment_error_counter(
    error_type: str,
    endpoint: Optional[str] = None,
    component: str = "api",
    status_code: int = 500,
    tenant_id: Optional[str] = None,
    error_code: Optional[str] = None,
) -> None:
    """
    Increment the error counters for monitoring.

    Args:
        error_type: Type of the error (exception class name)
        endpoint: API endpoint where the error occurred
        component: Component where the error occurred
        status_code: HTTP status code
        tenant_id: ID of the tenant
        error_code: Error code string
    """
    # Use default tenant if none provided
    from src.coherence.core.config import settings

    tenant = tenant_id if tenant_id else settings.DEFAULT_TENANT_ID

    # Increment general error counter
    ERROR_COUNT.labels(
        component=component, error_type=error_type, tenant_id=tenant
    ).inc()

    # Increment endpoint-specific counter if endpoint is provided
    if endpoint:
        method = "unknown"
        if "/" in endpoint and endpoint.startswith("/"):
            parts = endpoint.split("/")
            if len(parts) > 2:
                # Extract method from URL pattern like /v1/resolve
                method = parts[2] if len(parts) > 2 else parts[1]

        ERROR_BY_ENDPOINT.labels(
            endpoint=endpoint, method=method, status_code=status_code, tenant_id=tenant
        ).inc()

    # Increment error by type counter if error_code is provided
    if error_code and tenant_id:
        ERROR_BY_TYPE.labels(
            error_code=error_code,
            tenant_id=tenant_id,
        ).inc()
    elif error_code:
        ERROR_BY_TYPE.labels(
            error_code=error_code,
            tenant_id="unknown",
        ).inc()


def record_fallback_usage(
    strategy: str,
    component: str,
    success: bool = True,
) -> None:
    """
    Record metrics for fallback strategy usage.

    Args:
        strategy: Name of the fallback strategy
        component: Component where the fallback was used
        success: Whether the fallback succeeded
    """
    # Record general usage
    FALLBACK_STRATEGY_USAGE.labels(
        strategy=strategy,
        component=component,
    ).inc()

    # Record success or failure
    if success:
        FALLBACK_STRATEGY_SUCCESS.labels(
            strategy=strategy,
            component=component,
        ).inc()
    else:
        FALLBACK_STRATEGY_FAILURE.labels(
            strategy=strategy,
            component=component,
        ).inc()


def record_circuit_breaker_state(
    service: str,
    circuit_breaker: str,
    state: str,
) -> None:
    """
    Record the state of a circuit breaker.

    Args:
        service: Name of the service
        circuit_breaker: Name of the circuit breaker
        state: State of the circuit breaker (closed, open, half-open)
    """
    # Map state string to numeric value
    state_value = 0  # default: closed
    if state == "open":
        state_value = 1
    elif state == "half-open":
        state_value = 2

    CIRCUIT_BREAKER_STATE.labels(
        service=service,
        circuit_breaker=circuit_breaker,
    ).set(state_value)

    # If state changed to open, increment the trip counter
    if state == "open":
        CIRCUIT_BREAKER_TRIPS.labels(
            service=service,
            circuit_breaker=circuit_breaker,
        ).inc()


def record_retry_attempt(
    service: str,
    operation: str,
) -> None:
    """
    Record a retry attempt for an external service request.

    Args:
        service: Name of the service
        operation: Operation being retried
    """
    REQUEST_RETRY_ATTEMPTS.labels(
        service=service,
        operation=operation,
    ).inc()