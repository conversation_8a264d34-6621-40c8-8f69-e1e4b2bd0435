"""
Coherence Error Framework

This module provides a comprehensive error taxonomy and handling system
for the Coherence application.
"""

from src.coherence.core.errors.base import (
    AuthenticationError,
    AuthorizationError,
    CoherenceError,
    ConfigurationError,
    DatabaseError,
    ExternalServiceError,
    InternalError,
    LLMServiceError,
    RateLimitError,
    ResourceConflictError,
    ResourceNotFoundError,
    ValidationError,
)
from src.coherence.core.errors.handlers import register_exception_handlers

__all__ = [
    "CoherenceError",
    "ValidationError",
    "AuthenticationError",
    "AuthorizationError",
    "ResourceNotFoundError",
    "ResourceConflictError",
    "ExternalServiceError",
    "LLMServiceError",
    "DatabaseError",
    "ConfigurationError",
    "RateLimitError",
    "InternalError",
    "register_exception_handlers",
]
