"""
Structured Error Logging

This module provides utilities for structured error logging to ensure
that errors are logged consistently across the application.
"""

import functools
import logging
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union, cast

from src.coherence.core.errors.base import CoherenceError

logger = logging.getLogger(__name__)

# Type variables for function signatures
T = TypeVar("T")
R = TypeVar("R")


def log_error(
    error: Union[CoherenceError, Exception],
    log_level: int = logging.ERROR,
    include_traceback: bool = True,
    extra_context: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Log an error with consistent formatting and context.

    Args:
        error: The error to log
        log_level: The logging level to use
        include_traceback: Whether to include the traceback
        extra_context: Additional context to include in the log
    """
    extra = extra_context or {}

    # Extract more information for CoherenceError instances
    if isinstance(error, CoherenceError):
        error_type = type(error).__name__
        error_message = error.message
        error_code = error.error_code
        status_code = error.status_code
        details = error.details

        # Add the CoherenceError-specific fields to the extra context
        extra.update(
            {
                "error_code": error_code,
                "status_code": status_code,
            }
        )

        if details:
            extra["error_details"] = details

        # Use the original exception for the traceback if it exists
        if include_traceback and error.original_exception:
            exc_info = error.original_exception
        elif include_traceback:
            exc_info = error
        else:
            exc_info = None
    else:
        # For standard exceptions, use the exception type and message
        error_type = type(error).__name__
        error_message = str(error)

        # Include the traceback if requested
        if include_traceback:
            exc_info = error
        else:
            exc_info = None

    # Add the error type to the extra context
    extra["error_type"] = error_type

    # Log the error with the appropriate level
    logger.log(
        log_level,
        f"{error_type}: {error_message}",
        extra=extra,
        exc_info=exc_info,
    )


def with_error_logging(
    log_level: int = logging.ERROR,
    include_traceback: bool = True,
    extra_context: Optional[Dict[str, Any]] = None,
    reraise: bool = True,
) -> Callable[[Callable[..., R]], Callable[..., R]]:
    """
    Decorator that adds structured error logging to a function.

    Args:
        log_level: The logging level to use
        include_traceback: Whether to include the traceback
        extra_context: Additional context to include in the log
        reraise: Whether to re-raise the exception after logging

    Returns:
        A decorator function
    """

    def decorator(func: Callable[..., R]) -> Callable[..., R]:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> R:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Build the context for the log
                context = extra_context.copy() if extra_context else {}
                context.update(
                    {
                        "function": func.__name__,
                        "module": func.__module__,
                    }
                )

                # Log the error
                log_error(
                    error=e,
                    log_level=log_level,
                    include_traceback=include_traceback,
                    extra_context=context,
                )

                # Re-raise the exception if requested
                if reraise:
                    raise

                # Otherwise return None
                return cast(R, None)

        return wrapper

    return decorator


def log_exceptions(
    logger_instance: Optional[logging.Logger] = None,
    level: int = logging.ERROR,
    message: Optional[str] = None,
    exception_types: Optional[List[Type[Exception]]] = None,
    include_traceback: bool = True,
    include_args: bool = False,
    include_kwargs: bool = False,
) -> Callable[[Callable[..., R]], Callable[..., R]]:
    """
    Decorator that logs exceptions raised by a function.

    Args:
        logger_instance: The logger to use (defaults to module logger)
        level: The logging level to use
        message: Custom message to log (defaults to the exception message)
        exception_types: List of exception types to catch (defaults to Exception)
        include_traceback: Whether to include the traceback
        include_args: Whether to include function arguments in the log
        include_kwargs: Whether to include function keyword arguments in the log

    Returns:
        A decorator function
    """
    if exception_types is None:
        exception_types = [Exception]

    log = logger_instance or logger

    def decorator(func: Callable[..., R]) -> Callable[..., R]:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> R:
            try:
                return func(*args, **kwargs)
            except tuple(exception_types) as e:
                # Build the extra context
                extra: Dict[str, Any] = {
                    "function": func.__name__,
                    "module": func.__module__,
                    "error_type": type(e).__name__,
                }

                # Add function arguments if requested
                if include_args:
                    extra["args"] = args

                # Add function keyword arguments if requested
                if include_kwargs:
                    extra["kwargs"] = kwargs

                # Determine the message to log
                log_message = (
                    message if message else f"Exception in {func.__name__}: {str(e)}"
                )

                # Log the exception
                log.log(
                    level,
                    log_message,
                    extra=extra,
                    exc_info=e if include_traceback else None,
                )

                # Re-raise the exception
                raise

        return wrapper

    return decorator
