"""
Error Handler Registration

This module provides functions to register exception handlers with FastAPI.
These handlers ensure that all errors are converted to a standardized format
before being returned to the client.
"""

import logging
import traceback
import uuid
from typing import Any, Dict, Union

from fastapi import FastAP<PERSON>, Request, status
from fastapi.exceptions import HTTPException, RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError as PydanticValidationError
from sqlalchemy.exc import SQLAlchemyError

from src.coherence.core.config import settings
from src.coherence.core.errors.base import (
    AuthenticationError,
    AuthorizationError,
    CoherenceError,
    DatabaseError,
    ResourceNotFoundError,
    ValidationError,
)

logger = logging.getLogger(__name__)


def _generate_request_id() -> str:
    """Generate a unique request ID for error tracking."""
    return str(uuid.uuid4())


def _get_request_id(request: Request) -> str:
    """
    Get the request ID from the request state or generate a new one.

    Args:
        request: The FastAPI request object

    Returns:
        The request ID string
    """
    if not hasattr(request.state, "request_id"):
        request.state.request_id = _generate_request_id()
    return request.state.request_id


def _create_error_response(
    error: Union[CoherenceError, Exception],
    status_code: int,
    request: Request,
) -> JSONResponse:
    """
    Create a standardized error response.

    Args:
        error: The error that occurred
        status_code: HTTP status code to return
        request: The FastAPI request object

    Returns:
        A JSONResponse with standardized error information
    """
    request_id = _get_request_id(request)

    # If this is a CoherenceError, use its built-in serialization
    if isinstance(error, CoherenceError):
        error_dict = error.to_dict()
        error_dict["request_id"] = request_id
        return JSONResponse(
            status_code=error.status_code,
            content={"error": error_dict},
        )

    # For other exceptions, create a generic error response
    error_type = type(error).__name__
    error_message = str(error) or "An unexpected error occurred"

    error_dict = {
        "error_code": "coherence.internal_error",
        "message": error_message,
        "type": error_type,
        "request_id": request_id,
    }

    # Only include stack traces in non-production environments
    if settings.ENV != "production":
        error_dict["stack_trace"] = traceback.format_exception(
            type(error), error, error.__traceback__
        )

    return JSONResponse(
        status_code=status_code,
        content={"error": error_dict},
    )


async def http_exception_handler(
    request: Request, exc: HTTPException
) -> JSONResponse:
    """
    Handle FastAPI HTTP exceptions.

    Args:
        request: The FastAPI request
        exc: The HTTP exception

    Returns:
        A standardized error response
    """
    # Determine the log level based on the status code
    if exc.status_code >= 500:
        log_func = logger.error
    elif exc.status_code >= 400:
        log_func = logger.warning
    else:
        log_func = logger.info

    # Log the error with appropriate level
    log_func(
        f"HTTP exception: {exc.status_code} - {exc.detail}",
        extra={
            "request_id": _get_request_id(request),
            "status_code": exc.status_code,
            "detail": exc.detail,
        },
    )

    # Create a standardized error response while preserving the original status code
    error_dict = {
        "error_code": f"http_{exc.status_code}",
        "message": exc.detail or f"HTTP error {exc.status_code}",
        "type": "HTTPException",
        "request_id": _get_request_id(request),
    }

    return JSONResponse(
        status_code=exc.status_code,  # Preserve the original status code
        content={"error": error_dict},
    )


async def validation_exception_handler(
    request: Request, exc: RequestValidationError
) -> JSONResponse:
    """
    Handle FastAPI request validation errors.

    Args:
        request: The FastAPI request
        exc: The validation exception

    Returns:
        A standardized error response
    """
    errors: Dict[str, Any] = {}

    # Convert Pydantic validation errors to field_errors
    for error in exc.errors():
        loc = error.get("loc", [])
        if len(loc) > 1:
            field = ".".join(str(item) for item in loc[1:])
            errors.setdefault(field, []).append(error.get("msg"))

    # Create a ValidationError with the field errors
    coherence_error = ValidationError(
        message="Request validation failed",
        field_errors=errors,
        original_exception=exc,
    )

    # Log the error
    logger.warning(
        f"Validation error: {coherence_error.message}",
        extra={
            "request_id": _get_request_id(request),
            "validation_errors": errors,
        },
    )

    return _create_error_response(coherence_error, coherence_error.status_code, request)


async def pydantic_validation_exception_handler(
    request: Request, exc: PydanticValidationError
) -> JSONResponse:
    """
    Handle Pydantic validation errors.

    Args:
        request: The FastAPI request
        exc: The validation exception

    Returns:
        A standardized error response
    """
    errors: Dict[str, Any] = {}

    # Convert Pydantic validation errors to field_errors
    for error in exc.errors():
        loc = error.get("loc", ())
        if loc:
            field = ".".join(str(item) for item in loc)
            errors.setdefault(field, []).append(error.get("msg"))

    # Create a ValidationError with the field errors
    coherence_error = ValidationError(
        message="Data validation failed",
        field_errors=errors,
        original_exception=exc,
    )

    # Log the error
    logger.warning(
        f"Pydantic validation error: {coherence_error.message}",
        extra={
            "request_id": _get_request_id(request),
            "validation_errors": errors,
        },
    )

    return _create_error_response(coherence_error, coherence_error.status_code, request)


async def sqlalchemy_exception_handler(
    request: Request, exc: SQLAlchemyError
) -> JSONResponse:
    """
    Handle SQLAlchemy database errors.

    Args:
        request: The FastAPI request
        exc: The SQLAlchemy exception

    Returns:
        A standardized error response
    """
    # Create a DatabaseError
    coherence_error = DatabaseError(
        message="Database operation failed",
        original_exception=exc,
    )

    # Log the error
    logger.error(
        f"Database error: {str(exc)}",
        extra={
            "request_id": _get_request_id(request),
            "error_type": type(exc).__name__,
        },
        exc_info=exc,
    )

    return _create_error_response(coherence_error, coherence_error.status_code, request)


async def coherence_exception_handler(
    request: Request, exc: CoherenceError
) -> JSONResponse:
    """
    Handle Coherence-specific errors.

    Args:
        request: The FastAPI request
        exc: The Coherence exception

    Returns:
        A standardized error response
    """
    # Determine the log level based on the error status code
    if exc.status_code >= 500:
        log_func = logger.error
    elif exc.status_code >= 400:
        log_func = logger.warning
    else:
        log_func = logger.info

    # Log the error with appropriate level
    log_func(
        f"{type(exc).__name__}: {exc.message}",
        extra={
            "request_id": _get_request_id(request),
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
        },
        exc_info=exc.original_exception or exc if exc.status_code >= 500 else None,
    )

    return _create_error_response(exc, exc.status_code, request)


async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle generic unhandled exceptions.

    Args:
        request: The FastAPI request
        exc: The exception

    Returns:
        A standardized error response
    """
    # Log the error
    logger.error(
        f"Unhandled exception: {type(exc).__name__}: {str(exc)}",
        extra={
            "request_id": _get_request_id(request),
        },
        exc_info=exc,
    )

    return _create_error_response(exc, status.HTTP_500_INTERNAL_SERVER_ERROR, request)


def register_exception_handlers(app: FastAPI) -> None:
    """
    Register all exception handlers with the FastAPI app.

    Args:
        app: The FastAPI application
    """
    # Register HTTPException handler FIRST (before the generic Exception handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    
    # Register FastAPI validation exceptions
    app.add_exception_handler(RequestValidationError, validation_exception_handler)

    # Register Pydantic validation exceptions
    app.add_exception_handler(
        PydanticValidationError, pydantic_validation_exception_handler
    )

    # Register SQLAlchemy exceptions
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)

    # Register Coherence-specific exceptions
    app.add_exception_handler(CoherenceError, coherence_exception_handler)
    app.add_exception_handler(ValidationError, coherence_exception_handler)
    app.add_exception_handler(AuthenticationError, coherence_exception_handler)
    app.add_exception_handler(AuthorizationError, coherence_exception_handler)
    app.add_exception_handler(ResourceNotFoundError, coherence_exception_handler)
    app.add_exception_handler(DatabaseError, coherence_exception_handler)

    # Register a catch-all handler for unhandled exceptions (MUST BE LAST)
    app.add_exception_handler(Exception, generic_exception_handler)
