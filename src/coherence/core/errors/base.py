"""
Base Error Classes

This module defines the base error classes for the Coherence error taxonomy.
These classes are used throughout the application to provide structured
error handling and consistent error reporting.
"""

from typing import Any, Dict, List, Optional, Union


class CoherenceError(Exception):
    """
    Base class for all Coherence application errors.

    All application-specific exceptions should inherit from this class
    to ensure consistent error handling throughout the application.
    """

    status_code = 500
    error_code = "coherence.error"
    default_message = "An unexpected error occurred"

    def __init__(
        self,
        message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        status_code: Optional[int] = None,
        error_code: Optional[str] = None,
        original_exception: Optional[Exception] = None,
    ):
        """
        Initialize a CoherenceError.

        Args:
            message: A human-readable error message
            details: Additional error details (for structured error reporting)
            status_code: HTTP status code override
            error_code: Error code override
            original_exception: Original exception if this is a wrapper
        """
        self.message = message or self.default_message
        self.details = details or {}
        self.error_code = error_code or self.error_code
        self.status_code = status_code or self.status_code
        self.original_exception = original_exception

        # Call Exception.__init__ with the message
        super().__init__(self.message)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the error to a dictionary for JSON serialization.

        Returns:
            A dictionary representation of the error
        """
        error_dict: Dict[str, Any] = {
            "error_code": self.error_code,
            "message": self.message,
        }

        if self.details:
            error_dict["details"] = self.details

        if self.original_exception:
            error_dict["type"] = type(self.original_exception).__name__

        return error_dict


class ValidationError(CoherenceError):
    """
    Raised when input validation fails.

    Used for schema validation errors, parameter validation issues,
    and other input validation failures.
    """

    status_code = 400
    error_code = "coherence.validation_error"
    default_message = "Invalid input data"

    def __init__(
        self,
        message: Optional[str] = None,
        field_errors: Optional[Dict[str, Union[str, List[str]]]] = None,
        **kwargs: Any,
    ):
        """
        Initialize a ValidationError.

        Args:
            message: A human-readable error message
            field_errors: Dictionary of field-specific validation errors
            **kwargs: Additional error details
        """
        details = kwargs.pop("details", {})
        if field_errors:
            details["field_errors"] = field_errors

        super().__init__(message, details=details, **kwargs)


class AuthenticationError(CoherenceError):
    """
    Raised when authentication fails.

    Used for invalid credentials, expired tokens, and other
    authentication-related issues.
    """

    status_code = 401
    error_code = "coherence.authentication_error"
    default_message = "Authentication failed"


class AuthorizationError(CoherenceError):
    """
    Raised when the authenticated user lacks necessary permissions.

    Used when a user is authenticated but doesn't have sufficient
    permissions to access a resource or perform an action.
    """

    status_code = 403
    error_code = "coherence.authorization_error"
    default_message = "You do not have permission to perform this action"


class ResourceNotFoundError(CoherenceError):
    """
    Raised when a requested resource cannot be found.

    Used when trying to access or manipulate a resource that does
    not exist.
    """

    status_code = 404
    error_code = "coherence.resource_not_found"
    default_message = "Resource not found"

    def __init__(
        self,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        message: Optional[str] = None,
        **kwargs: Any,
    ):
        """
        Initialize a ResourceNotFoundError.

        Args:
            resource_type: Type of resource that was not found
            resource_id: ID of the resource that was not found
            message: A human-readable error message
            **kwargs: Additional error details
        """
        details = kwargs.pop("details", {})

        if resource_type:
            details["resource_type"] = resource_type

        if resource_id:
            details["resource_id"] = resource_id

        if resource_type and resource_id and not message:
            message = f"{resource_type} with ID '{resource_id}' not found"
        elif resource_type and not message:
            message = f"{resource_type} not found"

        super().__init__(message, details=details, **kwargs)


class ResourceConflictError(CoherenceError):
    """
    Raised when a resource conflict occurs.

    Used when trying to create a resource that already exists or
    update a resource in a way that conflicts with existing data.
    """

    status_code = 409
    error_code = "coherence.resource_conflict"
    default_message = "Resource conflict"


class ExternalServiceError(CoherenceError):
    """
    Raised when an external service call fails.

    Base class for errors related to external service calls.
    """

    status_code = 502
    error_code = "coherence.external_service_error"
    default_message = "External service error"

    def __init__(
        self,
        service_name: Optional[str] = None,
        message: Optional[str] = None,
        **kwargs: Any,
    ):
        """
        Initialize an ExternalServiceError.

        Args:
            service_name: Name of the external service
            message: A human-readable error message
            **kwargs: Additional error details
        """
        details = kwargs.pop("details", {})

        if service_name:
            details["service"] = service_name

        if service_name and not message:
            message = f"Error calling external service: {service_name}"

        super().__init__(message, details=details, **kwargs)


class LLMServiceError(ExternalServiceError):
    """
    Raised when an LLM service call fails.

    Used for errors related to calls to LLM providers like OpenAI.
    """

    error_code = "coherence.llm_service_error"
    default_message = "LLM service error"

    def __init__(
        self, model: Optional[str] = None, message: Optional[str] = None, **kwargs: Any
    ):
        """
        Initialize an LLMServiceError.

        Args:
            model: LLM model being used
            message: A human-readable error message
            **kwargs: Additional error details
        """
        details = kwargs.pop("details", {})
        # Default service_name for LLMServiceError is "LLM Service"
        service_name = kwargs.pop("service_name", "LLM Service")

        if model:
            details["model"] = model

        # Use the provided message or the default message
        actual_message = message or self.default_message

        super().__init__(
            service_name=service_name,
            message=actual_message,
            details=details,
            **kwargs,
        )


class DatabaseError(CoherenceError):
    """
    Raised when a database operation fails.

    Used for errors related to database operations like
    connection failures, query errors, and constraint violations.
    """

    status_code = 500
    error_code = "coherence.database_error"
    default_message = "Database operation failed"


class ConfigurationError(CoherenceError):
    """
    Raised when there is a configuration problem.

    Used for missing or invalid configuration settings.
    """

    status_code = 500
    error_code = "coherence.configuration_error"
    default_message = "Configuration error"


class RateLimitError(CoherenceError):
    """
    Raised when a rate limit is exceeded.

    Used for API rate limiting, throttling, or quota issues.
    """

    status_code = 429
    error_code = "coherence.rate_limit_error"
    default_message = "Rate limit exceeded"

    def __init__(
        self,
        limit: Optional[int] = None,
        reset_after: Optional[int] = None,
        message: Optional[str] = None,
        **kwargs: Any,
    ):
        """
        Initialize a RateLimitError.

        Args:
            limit: The rate limit that was exceeded
            reset_after: Seconds until the rate limit resets
            message: A human-readable error message
            **kwargs: Additional error details
        """
        details = kwargs.pop("details", {})

        if limit is not None:
            details["limit"] = limit

        if reset_after is not None:
            details["reset_after"] = reset_after

        super().__init__(message, details=details, **kwargs)


class InternalError(CoherenceError):
    """
    Raised when an unexpected internal error occurs.

    Used for unrecoverable internal errors that are not covered
    by more specific error types.
    """

    status_code = 500
    error_code = "coherence.internal_error"
    default_message = "Internal server error"


class NotImplementedFeatureError(CoherenceError):
    """
    Raised when a feature is not yet implemented.

    Used during development to mark features that are planned
    but not yet implemented.
    """

    status_code = 501
    error_code = "coherence.not_implemented"
    default_message = "This feature is not yet implemented"


class EmbeddingDimensionMismatchError(CoherenceError):
    """
    Raised when an embedding dimension mismatch is detected.
    
    Used when the generated embedding vector dimensions don't match
    the expected dimensions from the configuration.
    """
    
    status_code = 400
    error_code = "coherence.embedding_dimension_mismatch"
    default_message = "Embedding dimension mismatch"
    
    def __init__(
        self,
        message: Optional[str] = None,
        expected_dimension: Optional[int] = None,
        actual_dimension: Optional[int] = None,
        model: Optional[str] = None,
        **kwargs: Any,
    ):
        """
        Initialize an EmbeddingDimensionMismatchError.
        
        Args:
            message: A human-readable error message
            expected_dimension: Expected embedding dimension
            actual_dimension: Actual embedding dimension
            model: Embedding model being used
            **kwargs: Additional error details
        """
        details = kwargs.pop("details", {})
        
        if expected_dimension is not None:
            details["expected_dimension"] = expected_dimension
            
        if actual_dimension is not None:
            details["actual_dimension"] = actual_dimension
            
        if model:
            details["model"] = model
            
        if not message and expected_dimension is not None and actual_dimension is not None:
            message = f"Expected embedding dimension {expected_dimension}, got {actual_dimension}"
        
        super().__init__(message, details=details, **kwargs)
