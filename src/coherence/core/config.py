"""
Application configuration settings loaded from environment variables.
"""

import secrets
from functools import lru_cache
from typing import Any, List, Optional, Union

from pydantic import PostgresDsn, ValidationInfo, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.

    All settings will be automatically loaded from environment variables
    with the prefix COHERENCE_ (e.g., COHERENCE_SECRET_KEY).
    """

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_prefix="COHERENCE_",
        case_sensitive=False,
        extra="ignore",
    )

    # API Configuration
    API_V1_STR: str = "/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)

    # CORS Settings
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:3003"]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        # Special case for wildcard
        if v == "*":
            return ["*"]

        if isinstance(v, str):
            if v.startswith("[") and v.endswith("]"):
                # Handle JSON-like array format
                import json

                try:
                    return json.loads(v)
                except json.JSONDecodeError:
                    # Try with single quotes replaced by double quotes
                    try:
                        v_fixed = v.replace("'", '"')
                        return json.loads(v_fixed)
                    except json.JSONDecodeError:
                        pass
            # Handle comma-separated format
            return [i.strip() for i in v.split(",") if i.strip()]
        elif isinstance(v, list):
            return v
        return []

    # Database Configuration
    POSTGRES_SERVER: str = "localhost"  # Changed for local script execution
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "coherence"
    POSTGRES_PORT: int = 5433  # Changed to match Docker exposed port
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None
    DB_POOL_SIZE: int = 5
    DB_MAX_OVERFLOW: int = 10
    DB_ECHO: bool = False
    DB_CONNECT_RETRY: int = 3
    SEED_DEFAULT_TENANT: bool = True
    DEFAULT_TENANT_NAME: str = "Default"

    @field_validator("SQLALCHEMY_DATABASE_URI", mode="before")
    def assemble_db_connection(cls, v: Optional[str], values: ValidationInfo) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme="postgresql+psycopg2",
            username=values.data.get("POSTGRES_USER"),
            password=values.data.get("POSTGRES_PASSWORD"),
            host=values.data.get("POSTGRES_SERVER"),
            port=values.data.get("POSTGRES_PORT"),
            path=f"{values.data.get('POSTGRES_DB') or ''}",
        )

    # Redis Configuration
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None

    # Vector Database (Qdrant) Configuration
    QDRANT_HOST: str = "localhost"
    QDRANT_PORT: int = 6335  # Adjusted to match docker-compose port mapping (6335:6333)
    QDRANT_GRPC_PORT: int = 6336  # Adjusted to match docker-compose port mapping (6336:6334)
    QDRANT_API_KEY: Optional[str] = None
    
    @property
    def QDRANT_URL(self) -> str:
        """Constructs the Qdrant URL from host and port."""
        return f"http://{self.QDRANT_HOST}:{self.QDRANT_PORT}"

    # LLM Configuration
    LLM_PROVIDER: str = "openai"
    LLM_MODEL: str = "gpt-4o"
    OPENAI_API_KEY: Optional[str] = None

    # Embedding Configuration
    EMBEDDING_MODEL: str = "text-embedding-3-small"
    EMBEDDING_DIMENSION: int = 384

    # Template Configuration
    DEFAULT_TEMPLATE_KEY: str = "INTENT_ROUTER_V1"

    # Metrics and Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 8001

    # Authentication and Tenant Settings
    TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    DEFAULT_TENANT_ID: str = "00000000-0000-0000-0000-000000000000"
    SYSTEM_ADMIN_API_KEY: Optional[str] = None
    
    # JWT Settings
    JWT_SECRET_KEY: str = SECRET_KEY  # Default to the app's SECRET_KEY
    JWT_ALGORITHM: str = "HS256"      # Default algorithm for JWT

    # Tiered Intent Resolution
    TIER1_THRESHOLD: float = 0.85
    TIER2_THRESHOLD: float = 0.75

    # Parameter Completion Settings
    MAX_COMPLETION_ROUNDS: int = 3
    PARAM_COMPLETION_MODEL: str = "gpt-4o"

    # Application Information
    PROJECT_NAME: str = "Coherence"
    VERSION: str = "0.1.0"

    # Environment
    ENV: str = "development"

    # Debugging
    DEBUG_ENDPOINTS: bool = True  # Set to False in production


# singleton instance used everywhere else
settings = Settings()


# --- NEW ---------------------------------------------
@lru_cache
def get_settings() -> Settings:
    """
    Returns the singleton Settings object.

    FastAPI dependencies and any late-binding code can call this instead of
    importing the global `settings` variable.
    """
    return settings


# ------------------------------------------------------

__all__ = ["settings", "get_settings"]
