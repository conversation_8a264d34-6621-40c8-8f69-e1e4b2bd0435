"""
Base classes for LLM provider integration.

This module defines abstract base classes for integrating
different LLM providers (OpenAI, Anthropic, local models, etc.)
with a consistent interface.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional


class LLMResponse:
    """
    Response from an LLM generation request.

    Attributes:
        content: Generated text content
        usage: Token usage information
        model: Model used for generation
        finish_reason: Reason the generation stopped
        metadata: Additional provider-specific metadata
    """

    def __init__(
        self,
        content: str,
        usage: Optional[Dict[str, int]] = None,
        model: Optional[str] = None,
        finish_reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the LLM response.

        Args:
            content: Generated text content
            usage: Token usage information
            model: Model used for generation
            finish_reason: Reason the generation stopped
            metadata: Additional provider-specific metadata
        """
        self.content = content
        self.usage = usage or {}
        self.model = model
        self.finish_reason = finish_reason
        self.metadata = metadata or {}


class LLMProvider(ABC):
    """
    Abstract base class for LLM providers.

    This class defines the interface that all LLM providers
    must implement for compatibility with the Coherence pipeline.
    """

    @abstractmethod
    async def generate(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        user: Optional[str] = None,
    ) -> LLMResponse:
        """Generate a completion from a list of messages.

        Args:
            messages: List of messages in the conversation
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature (0.0-2.0)
            user: User identifier for billing and monitoring

        Returns:
            LLMResponse with the generated content
        """
        pass

    @abstractmethod
    async def generate_embedding(
        self,
        text: str,
    ) -> List[float]:
        """Generate an embedding vector for a text string.

        Args:
            text: Text to generate embedding for

        Returns:
            List of floats representing the embedding vector
        """
        pass

    @abstractmethod
    async def close(self) -> None:
        """Close any resources held by the provider."""
        pass
