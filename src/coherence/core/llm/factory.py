"""
Factory for creating LLM providers.

This module provides a factory for creating different LLM providers
based on configuration, making it easy to switch between providers.
"""

from typing import Any, Optional

import structlog
from fastapi import HTTPException, status

from src.coherence.core.llm.base import <PERSON><PERSON><PERSON>ider
from src.coherence.core.llm.providers.mock_provider import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.coherence.core.llm.providers.openai_provider import OpenAIProvider
from src.coherence.core.metrics import increment_error_counter

logger = structlog.get_logger(__name__)


class LLMProviderUnavailableError(HTTPException):
    """Exception raised when an LLM provider is unavailable."""

    def __init__(self, provider_name: str, reason: str, original_error: Optional[Exception] = None):
        """Initialize the exception.
        
        Args:
            provider_name: Name of the LLM provider
            reason: Reason for unavailability
            original_error: Original exception that caused the error
        """
        detail = f"LLM provider '{provider_name}' is unavailable: {reason}"
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=detail,
        )
        self.provider_name = provider_name
        self.reason = reason
        self.original_error = original_error


class LLMFactory:
    """
    Factory for creating LLM provider instances.

    This class creates and configures LLM providers based on
    the requested provider name and configuration.
    """

    @classmethod
    def create_provider(
        cls,
        name: str,
        model: str,
        api_key: Optional[str] = None,
        fail_fast: bool = True,
        **kwargs: Any,
    ) -> LLMProvider:
        """Create a new LLM provider instance.

        Args:
            name: Provider name (e.g., "openai", "anthropic")
            model: Model name to use
            api_key: API key for the provider
            fail_fast: If True, raise HTTP 503 when provider is unavailable
                       instead of silently downgrading to mock provider
            **kwargs: Additional provider-specific configuration

        Returns:
            Configured LLM provider instance

        Raises:
            LLMProviderUnavailableError: If provider is unavailable and fail_fast is True
            ValueError: If the provider is not supported
        """
        # Normalize the provider name
        provider_name = name.lower().strip()
        from src.coherence.core.config import settings

        # Create the appropriate provider
        if provider_name == "openai":
            if not api_key:
                api_key = settings.OPENAI_API_KEY

            # Ensure api_key is not None
            if api_key is None:
                error_msg = "OpenAI API key is required but not provided"
                logger.error(error_msg)
                increment_error_counter(
                    error_type="LLMProviderUnavailableError",
                    component="llm_factory",
                    error_code="coherence.llm_service_error",
                    status_code=503,
                )
                
                if fail_fast:
                    raise LLMProviderUnavailableError(
                        provider_name="openai",
                        reason="API key is missing",
                    )
                else:
                    # Only use mock provider as fallback in non-production
                    if settings.ENV != "production":
                        logger.warning(
                            "Falling back to MockLLMProvider due to missing OpenAI API key",
                            model=model,
                            environment=settings.ENV,
                        )
                        return MockLLMProvider(model=model, **kwargs)
                    else:
                        # In production, never fall back to mock
                        raise LLMProviderUnavailableError(
                            provider_name="openai",
                            reason="API key is missing and cannot fall back to mock in production",
                        )

            try:
                logger.info(
                    "Creating OpenAI provider",
                    model=model,
                    has_api_key=bool(api_key),
                )

                return OpenAIProvider(
                    api_key=api_key,  # api_key is guaranteed to be a str here
                    model=model,
                    **kwargs,
                )
            except Exception as e:
                error_msg = f"Failed to initialize OpenAI provider: {str(e)}"
                logger.error(
                    error_msg,
                    model=model,
                    error=str(e),
                    error_type=type(e).__name__,
                )
                increment_error_counter(
                    error_type="LLMProviderUnavailableError",
                    component="llm_factory",
                    error_code="coherence.llm_service_error",
                    status_code=503,
                )

                if fail_fast:
                    raise LLMProviderUnavailableError(
                        provider_name="openai",
                        reason=f"Provider initialization failed: {str(e)}",
                        original_error=e,
                    ) from e
                else:
                    # Only use mock provider as fallback in non-production
                    if settings.ENV != "production":
                        logger.warning(
                            "Falling back to MockLLMProvider due to OpenAI provider initialization failure",
                            model=model,
                            environment=settings.ENV,
                            error=str(e),
                        )
                        return MockLLMProvider(model=model, **kwargs)
                    else:
                        # In production, never fall back to mock
                        raise LLMProviderUnavailableError(
                            provider_name="openai",
                            reason=f"Provider initialization failed: {str(e)} and cannot fall back to mock in production",
                            original_error=e,
                        ) from e

        # Add other providers here as needed
        # elif provider_name == "anthropic":
        #     return AnthropicProvider(...)
        # elif provider_name == "mistral":
        #     return MistralProvider(...)

        # If "mock" provider is explicitly requested
        elif provider_name == "mock":
            # Check if we're in production environment
            if settings.ENV == "production":
                error_msg = "MockLLMProvider must not be used in production environment"
                logger.error(error_msg)
                increment_error_counter(
                    error_type="LLMProviderUnavailableError",
                    component="llm_factory",
                    error_code="coherence.llm_service_error",
                    status_code=503,
                )
                
                raise LLMProviderUnavailableError(
                    provider_name="mock",
                    reason="Mock provider cannot be used in production",
                )

            logger.info(
                "Creating Mock LLM provider",
                model=model,
                environment=settings.ENV,
            )
            return MockLLMProvider(
                model=model,
                **kwargs,
            )

        # If no matching provider, raise an error
        error_msg = f"Unsupported LLM provider: {name}"
        logger.error(error_msg)
        increment_error_counter(
            error_type="LLMProviderUnavailableError",
            component="llm_factory",
            error_code="coherence.llm_service_error",
            status_code=503,
        )
        
        if fail_fast:
            raise LLMProviderUnavailableError(
                provider_name=name,
                reason="Provider not supported",
            )
        else:
            # Only use mock provider as fallback in non-production
            if settings.ENV != "production":
                logger.warning(
                    f"Falling back to MockLLMProvider due to unsupported provider: {name}",
                    model=model,
                    environment=settings.ENV,
                )
                return MockLLMProvider(model=model, **kwargs)
            else:
                # In production, never fall back to mock
                raise LLMProviderUnavailableError(
                    provider_name=name,
                    reason="Provider not supported and cannot fall back to mock in production",
                )

    @classmethod
    def get_default_provider(cls, fail_fast: bool = True) -> LLMProvider:
        """Get the default LLM provider based on settings.

        Args:
            fail_fast: If True, raise HTTP 503 when provider is unavailable
                       instead of silently downgrading to mock provider

        Returns:
            Default LLM provider instance
            
        Raises:
            LLMProviderUnavailableError: If provider is unavailable and fail_fast is True
        """
        from src.coherence.core.config import settings

        return cls.create_provider(
            name=settings.LLM_PROVIDER,
            model=settings.LLM_MODEL,
            api_key=settings.OPENAI_API_KEY,
            fail_fast=fail_fast,
        )