"""
Mock LLM provider for testing purposes.

This module provides a mock implementation of the LLM provider interface
that can be used for testing without requiring actual API keys.
"""

import json
from typing import Any, Dict, List, Optional

import structlog

from src.coherence.core.llm.base import <PERSON><PERSON><PERSON>ider, LLMResponse

logger = structlog.get_logger(__name__)


class Mock<PERSON><PERSON><PERSON>ider(LLMProvider):
    """
    Mock implementation of the LLM provider interface for testing.

    Returns predefined responses based on input patterns.
    """

    def __init__(
        self,
        model: str = "mock-model",
        **kwargs: Any,
    ):
        """Initialize the mock provider.

        Args:
            model: Model name (ignored, just for compatibility)
            **kwargs: Additional configuration (ignored)
        """
        self.model = model
        logger.info("Initialized MockLLMProvider", model=model)

    async def generate(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        user: Optional[str] = None,
    ) -> LLMResponse:
        """Generate a mock response based on the input messages.

        Args:
            messages: List of messages in the conversation
            max_tokens: Maximum number of tokens (ignored)
            temperature: Sampling temperature (ignored)
            user: User identifier (ignored)

        Returns:
            LLMResponse with a predefined content
        """
        logger.info("Mock LLM called", messages_count=len(messages))

        # Extract the last user message
        last_message = messages[-1]["content"] if messages else ""

        # Generate different responses based on the input patterns
        if "weather" in last_message.lower() or "forecast" in last_message.lower():
            # Extract location from message if available
            location = "New York"  # Default
            if "in " in last_message:
                location_part = last_message.split("in ", 1)[1].strip()
                if location_part:
                    location = location_part.split()[0]
                    if len(location_part.split()) > 1:
                        location += " " + location_part.split()[1]
                    location = location.rstrip("?.,;!")

            content = json.dumps(
                {
                    "intent": "get_weather",
                    "confidence": 0.95,
                    "filled": {"location": location},
                    "missing": [],
                }
            )
        elif any(
            word in last_message.lower()
            for word in ["weight", "blood pressure", "heart rate", "health"]
        ):
            # Determine health metric type
            metric_type = "weight"
            value = "150 pounds"

            if "blood pressure" in last_message.lower():
                metric_type = "blood_pressure"
                value = "120/80"
            elif "heart rate" in last_message.lower():
                metric_type = "heart_rate"
                value = "75 bpm"

            # Try to extract value from message
            for word in last_message.split():
                if word.replace(".", "").isdigit():
                    value = word
                    if "pound" in last_message.lower():
                        value += " pounds"
                    elif "kg" in last_message.lower():
                        value += " kg"
                elif "/" in word and all(part.isdigit() for part in word.split("/")):
                    value = word

            content = json.dumps(
                {
                    "intent": "log_health_metric",
                    "confidence": 0.92,
                    "filled": {"metric_type": metric_type, "value": value},
                    "missing": [],
                }
            )
        else:
            # Default response for unknown messages
            content = json.dumps(
                {"intent": None, "confidence": 0.3, "filled": {}, "missing": []}
            )

        return LLMResponse(
            content=content,
            usage={"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30},
            model=self.model,
            finish_reason="stop",
            metadata={"id": "mock-response-id"},
        )

    async def generate_embedding(
        self,
        text: str,
    ) -> List[float]:
        """Generate a mock embedding vector.

        Args:
            text: Text to generate embedding for (ignored)

        Returns:
            List of floats representing a mock embedding vector
        """
        logger.info("Mock embedding generated", text_length=len(text))

        # Generate a simple mock embedding
        # Normally embeddings would be semantic vectors,
        # but for testing we just need something consistent
        return [0.1] * 384

    async def close(self) -> None:
        """Close the mock provider (no-op)."""
        logger.info("Closing MockLLMProvider")
        pass
