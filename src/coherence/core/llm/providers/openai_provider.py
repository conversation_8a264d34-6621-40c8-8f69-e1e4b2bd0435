"""
OpenAI provider implementation for the LLM interface.

This module implements the LLM interface for OpenAI's API,
supporting both chat completions and embeddings.
"""

import time
from typing import Any, Dict, List, Literal, Optional, Type, TypeVar, Union

# Third-party imports
import structlog
from openai import APIError, AsyncOpenAI, NotGiven
from openai.types.chat import (
    ChatCompletionAssistantMessageParam,
    ChatCompletionFunctionMessageParam,
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionToolMessageParam,
    ChatCompletionUserMessageParam,
)
from pydantic import BaseModel

# Local application imports
from src.coherence.core.config import settings
from src.coherence.core.llm.base import LLMProvider, LLMResponse
from src.coherence.core.metrics import LLM_CALL_LATENCY, LLM_TOKEN_USAGE

logger = structlog.get_logger(__name__)

# TypeVar for Pydantic models
T_BaseModel = TypeVar("T_BaseModel", bound=BaseModel)
# TypeVar for the output of _execute_parse_call, which is the raw parsed object from SDK
T_ParsedObj = TypeVar("T_ParsedObj")


class OpenAIProvider(LLMProvider):
    """
    OpenAI implementation of the LLM provider interface.
    All calls are routed through client.responses.parse().

    Attributes:
        client: AsyncOpenAI client
        model: Default model to use
        embedding_model: Model to use for embeddings
        temperature: Default sampling temperature (0.0-2.0)
    """

    def __init__(
        self,
        api_key: str,
        model: str = "gpt-4o",
        embedding_model: str = None,
        temperature: float = 0.7,  # Added default temperature
    ):
        """Initialize the OpenAI provider.

        Args:
            api_key: OpenAI API key
            model: Default model to use for completions
            embedding_model: Model to use for embeddings
            temperature: Default sampling temperature
        """
        self.client = AsyncOpenAI(api_key=api_key)
        self.model = model
        self.embedding_model = embedding_model or settings.EMBEDDING_MODEL
        self.temperature = temperature

    def _prepare_openai_messages(
        self, messages: List[Dict[str, str]]
    ) -> List[ChatCompletionMessageParam]:
        """Helper to convert list of dicts to OpenAI's message format."""
        openai_messages: List[ChatCompletionMessageParam] = []
        for msg in messages:
            role = msg.get("role", "")
            content = msg.get("content", "")
            if role == "system":
                openai_messages.append(
                    ChatCompletionSystemMessageParam(role="system", content=content)
                )
            elif role == "user":
                openai_messages.append(
                    ChatCompletionUserMessageParam(role="user", content=content)
                )
            elif role == "assistant":
                openai_messages.append(
                    ChatCompletionAssistantMessageParam(
                        role="assistant", content=content
                    )
                )
            elif role == "tool":
                tool_call_id = msg.get("tool_call_id", "")
                if not tool_call_id:
                    logger.error(
                        "Tool message missing tool_call_id", message_content=content
                    )
                    raise ValueError("Tool message must have a tool_call_id")
                openai_messages.append(
                    ChatCompletionToolMessageParam(
                        role="tool", content=content, tool_call_id=tool_call_id
                    )
                )
            elif role == "function":
                name = msg.get("name", "")
                if not name:
                    logger.error(
                        "Function message missing name", message_content=content
                    )
                    raise ValueError("Function message must have a name")
                openai_messages.append(
                    ChatCompletionFunctionMessageParam(
                        role="function", content=content, name=name
                    )
                )
            else:
                logger.warning(
                    f"Unknown message role: {role} - skipping message.",
                    message_content=content,
                )
        return openai_messages

    async def _execute_parse_call(
        self,
        messages: List[Dict[str, str]],
        text_format_spec: Union[Type[T_BaseModel], Literal["text"]],
        model_override: Optional[str] = None,
        temperature_override: Optional[float] = None,
        max_tokens_override: Optional[int] = None,
        user: Optional[str] = None,
    ) -> T_ParsedObj:  # Returns the raw parsed object from SDK
        """Core private method to call client.responses.parse()."""
        start_time = time.time()
        actual_model = model_override or self.model
        effective_temperature = (
            temperature_override
            if temperature_override is not None
            else self.temperature
        )

        openai_messages = self._prepare_openai_messages(messages)
        if not openai_messages:
            # Handle case where all messages were invalid or list was empty
            logger.error("No valid messages to send to OpenAI after preparation.")
            # Decide on appropriate error or empty response object
            raise ValueError("Cannot make API call with no valid messages.")

        operation_type = (
            "structured_model"
            if isinstance(text_format_spec, type)
            and issubclass(text_format_spec, BaseModel)
            else "text_completion"
        )
        output_model_name = (
            text_format_spec.__name__ if isinstance(text_format_spec, type) else "text"
        )

        try:
            logger.info(
                f"Calling OpenAI responses.parse with model {actual_model} for format: {output_model_name}",
                num_messages=len(openai_messages),
            )
            api_params = {
                "model": actual_model,
                "messages": openai_messages,
                "text_format": text_format_spec,
                "temperature": effective_temperature,
            }
            if max_tokens_override is not None:
                api_params["max_tokens"] = max_tokens_override
            # Use NotGiven for optional parameters if they are None
            api_params["user"] = user if user is not None else NotGiven()

            # The return type of client.responses.parse is an object from which
            # .output_parsed and ._raw_response can be accessed.
            # Let's type hint it as T_ParsedObj for now.
            parsed_sdk_object: T_ParsedObj = await self.client.responses.parse(
                **api_params
            )

            call_time = time.time() - start_time
            LLM_CALL_LATENCY.labels(
                provider="openai",
                model=actual_model,
                operation=f"{operation_type}_parse_success",
            ).observe(call_time)

            # Token usage might be available on parsed_sdk_object or its _raw_response attribute
            raw_response_for_usage = getattr(parsed_sdk_object, "_raw_response", None)
            if (
                raw_response_for_usage
                and hasattr(raw_response_for_usage, "usage")
                and raw_response_for_usage.usage
            ):
                LLM_TOKEN_USAGE.labels(
                    tenant_id="global",
                    provider="openai",
                    model=actual_model,
                    operation=operation_type,
                ).inc(raw_response_for_usage.usage.total_tokens)

            logger.info(
                f"Successfully called responses.parse for {output_model_name}. Call time: {call_time:.2f}s"
            )
            return parsed_sdk_object

        except APIError as e:  # More specific error handling
            logger.error(
                "OpenAI API error during responses.parse",
                error=str(e),
                error_type=type(e).__name__,
                model=actual_model,
                text_format_spec=output_model_name,
                status_code=e.status_code,
                messages_count=len(openai_messages),
            )
            call_time = time.time() - start_time
            LLM_CALL_LATENCY.labels(
                provider="openai",
                model=actual_model,
                operation=f"{operation_type}_parse_apierror",
            ).observe(call_time)
            raise
        except Exception as e:
            logger.error(
                "Unexpected error during responses.parse",
                error=str(e),
                error_type=type(e).__name__,
                model=actual_model,
                text_format_spec=output_model_name,
                messages_count=len(openai_messages),
            )
            call_time = time.time() - start_time
            LLM_CALL_LATENCY.labels(
                provider="openai",
                model=actual_model,
                operation=f"{operation_type}_parse_error",
            ).observe(call_time)
            raise

    async def generate(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        user: Optional[str] = None,
    ) -> LLMResponse:
        """Generate a completion from a list of messages."""
        openai_messages = self._prepare_openai_messages(messages)
        if not openai_messages:
            logger.warning(
                "No valid messages to send to OpenAI after preparation in generate call."
            )
            return LLMResponse(
                content="", raw_response=None, cost=0.0, model_name=self.model
            )

        effective_temperature = (
            temperature if temperature is not None else self.temperature
        )

        api_params: Dict[str, Any] = {
            "model": self.model,
            "messages": openai_messages,
            "temperature": effective_temperature,
        }
        if max_tokens is not None:
            api_params["max_tokens"] = max_tokens
        if user is not None:
            api_params["user"] = user

        try:
            logger.debug(
                f"Calling OpenAI chat.completions.create with model {api_params['model']}",
                num_messages=len(openai_messages),
                temperature=api_params["temperature"],
                max_tokens=api_params.get("max_tokens"),
            )

            completion = await self.client.chat.completions.create(**api_params)

            content = ""
            prompt_tokens = None
            completion_tokens = None

            if completion.choices and completion.choices[0].message:
                content = completion.choices[0].message.content or ""

            if completion.usage:
                prompt_tokens = completion.usage.prompt_tokens
                completion_tokens = completion.usage.completion_tokens
                # Cost calculation would go here if pricing is available

            return LLMResponse(
                content=content,
                usage={
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                }
                if prompt_tokens is not None and completion_tokens is not None
                else None,
                model=api_params["model"],
                finish_reason=completion.choices[0].finish_reason
                if completion.choices and completion.choices[0].finish_reason
                else None,
                metadata={
                    "openai_completion_dump": completion.model_dump(),
                    "cost_placeholder": 0.0,
                },
            )
        except APIError as e:
            logger.error(
                "OpenAI API error during chat.completions.create",
                error_message=str(e),
                status_code=e.status_code if hasattr(e, "status_code") else None,
                error_type=e.type if hasattr(e, "type") else None,
                model=api_params["model"],
            )
            raise RuntimeError(
                f"OpenAI API error: {str(e)}"
            ) from e  # Replace with CoherenceLLMError if available
        except Exception as e:
            logger.error(
                "Unexpected error during OpenAI chat.completions.create",
                error_message=str(e),
                model=api_params["model"],
            )
            raise RuntimeError(
                f"Unexpected error with OpenAI provider: {str(e)}"
            ) from e  # Replace with CoherenceLLMError

    async def close(self) -> None:
        """Close any resources held by the provider, like the HTTP client."""
        logger.debug("Closing OpenAIProvider's HTTP client.")
        await self.client.close()

    async def generate_text(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        user: Optional[str] = None,
    ) -> LLMResponse:
        """Generates a plain text response using OpenAI's API via responses.parse.

        Args:
            messages: List of messages in the conversation.
            max_tokens: Maximum number of tokens to generate.
            temperature: Sampling temperature (0.0-2.0).
            user: User identifier for billing and monitoring.

        Returns:
            LLMResponse with the generated text content and metadata.
        """
        parsed_sdk_object = await self._execute_parse_call(
            messages=messages,
            text_format_spec="text",
            model_override=self.model,  # Can allow override if generate_text needs different default
            temperature_override=temperature,
            max_tokens_override=max_tokens,
            user=user,
        )

        content = getattr(parsed_sdk_object, "output_parsed", "")
        if not isinstance(content, str):
            logger.warning(
                f"Expected string from 'text' format, got {type(content)}. Coercing to string.",
                raw_output=content,
            )
            content = str(content)  # Ensure it's a string

        raw_response = getattr(parsed_sdk_object, "_raw_response", None)
        usage_data = None
        finish_reason_str = None
        response_id = None
        response_model = None

        if raw_response:
            if hasattr(raw_response, "usage") and raw_response.usage:
                usage_data = {
                    "prompt_tokens": raw_response.usage.prompt_tokens,
                    "completion_tokens": raw_response.usage.completion_tokens,
                    "total_tokens": raw_response.usage.total_tokens,
                }
            if hasattr(raw_response, "choices") and raw_response.choices:
                finish_reason_str = raw_response.choices[0].finish_reason
            response_id = getattr(raw_response, "id", None)
            response_model = getattr(
                raw_response, "model", self.model
            )  # Default to requested model

        return LLMResponse(
            content=content,
            usage=usage_data,
            model=response_model or self.model,
            finish_reason=finish_reason_str,
            metadata={"id": response_id} if response_id else None,
        )

    async def generate_model(
        self,
        messages: List[Dict[str, str]],
        output_model: Type[T_BaseModel],
        model_override: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        user: Optional[str] = None,
    ) -> T_BaseModel:
        """Generates a structured Pydantic model response using OpenAI's API.

        IMPORTANT: The 'messages' payload MUST include instructions guiding the LLM
        to produce a JSON output that conforms to the schema of the 'output_model'.

        Args:
            messages: List of messages in the conversation.
            output_model: The Pydantic model class to parse the response into.
            model_override: Optional specific OpenAI model to use for this call.
            temperature: Optional temperature for sampling.
            max_tokens: Optional max tokens for the response.
            user: User identifier for billing and monitoring.

        Returns:
            An instance of the provided Pydantic 'output_model'.
        """
        parsed_sdk_object = await self._execute_parse_call(
            messages=messages,
            text_format_spec=output_model,
            model_override=model_override,  # Uses provider's default if None
            temperature_override=temperature,
            max_tokens_override=max_tokens,
            user=user,
        )

        # Assuming output_parsed contains the Pydantic model instance
        model_instance = getattr(parsed_sdk_object, "output_parsed", None)
        if not isinstance(model_instance, output_model):
            logger.error(
                f"Output parsed is not an instance of {output_model.__name__}. Got {type(model_instance)}.",
                raw_output=model_instance,
            )
            # This case should ideally be handled by an error from responses.parse if schema validation fails
            # Or, it implies a bug in our type hinting or SDK behavior understanding.
            raise TypeError(
                f"Failed to parse response into {output_model.__name__}. Check LLM output and prompt."
            )

        return model_instance

    async def generate_embedding(
        self,
        text: str,
        dimensions: Optional[int] = None,
        **kwargs: Any,
    ) -> List[float]:
        """Generate an embedding vector using OpenAI's embeddings API.

        Args:
            text: Text to generate embedding for
            dimensions: Optional dimension size for the embedding vector.
                        Defaults to settings.EMBEDDING_DIMENSION if not provided.
            **kwargs: Additional parameters to pass to the embeddings API

        Returns:
            List of floats representing the embedding vector

        Raises:
            EmbeddingDimensionMismatchError: If the returned embedding dimensions don't match expected
            APIError: If the OpenAI API call fails
        """
        from src.coherence.core.config import settings
        from src.coherence.core.errors.base import EmbeddingDimensionMismatchError
        
        start_time = time.time()
        
        # Use provided dimensions or settings default
        embedding_dimensions = dimensions or settings.EMBEDDING_DIMENSION
        
        logger.info(
            "Generating embedding vector with OpenAI",
            text_length=len(text),
            model=self.embedding_model,
            requested_dimensions=embedding_dimensions,
            default_dimensions=settings.EMBEDDING_DIMENSION,
            provider="OpenAIProvider"
        )

        try:
            # Prepare parameters for API call
            api_params = {
                "model": self.embedding_model,
                "input": text,
                "dimensions": embedding_dimensions,
                **kwargs,
            }
            
            logger.debug(
                "Calling OpenAI embeddings API",
                model=self.embedding_model,
                dimensions=embedding_dimensions,
                kwargs=list(kwargs.keys()) if kwargs else []
            )
            
            # Call the OpenAI embeddings API with dimensions parameter
            api_start_time = time.time()
            response = await self.client.embeddings.create(**api_params)
            api_time = time.time() - api_start_time
            
            logger.info(
                "OpenAI embeddings API call completed",
                api_time_ms=int(api_time * 1000),
                model=self.embedding_model,
                total_tokens=response.usage.total_tokens if response.usage else None
            )

            # Extract the embedding vector
            embedding = response.data[0].embedding
            actual_dimension = len(embedding)
            
            logger.info(
                "Embedding vector generated",
                model=self.embedding_model,
                actual_dimension=actual_dimension,
                expected_dimension=embedding_dimensions,
                dimensions_match=(actual_dimension == embedding_dimensions)
            )
            
            # Fail-fast guard: verify the embedding dimensions
            if actual_dimension != embedding_dimensions:
                logger.error(
                    "Embedding dimension mismatch detected",
                    expected_dimension=embedding_dimensions,
                    actual_dimension=actual_dimension,
                    model=self.embedding_model,
                    provider="OpenAIProvider"
                )
                
                raise EmbeddingDimensionMismatchError(
                    expected_dimension=embedding_dimensions,
                    actual_dimension=actual_dimension,
                    model=self.embedding_model,
                    message=f"Expected {embedding_dimensions}, got {actual_dimension} "
                    f"from model {self.embedding_model}"
                )

            # Record metrics
            call_time = time.time() - start_time
            LLM_CALL_LATENCY.labels(
                provider="openai",
                model=self.embedding_model,
                operation="embedding",
            ).observe(call_time)

            # Record token usage if available
            if response.usage:
                LLM_TOKEN_USAGE.labels(
                    tenant_id="global",  # Replace with actual tenant ID in production
                    provider="openai",
                    model=self.embedding_model,
                    operation="embedding",
                ).inc(response.usage.total_tokens)
                
                logger.debug(
                    "Token usage recorded",
                    total_tokens=response.usage.total_tokens,
                    model=self.embedding_model
                )

            return embedding

        except APIError as e:
            # Calculate total time for metrics
            call_time = time.time() - start_time
            
            # Log detailed error information
            logger.error(
                "OpenAI embedding API error",
                error=str(e),
                error_type=type(e).__name__,
                model=self.embedding_model,
                text_length=len(text),
                dimensions=embedding_dimensions,
                status_code=getattr(e, "status_code", None),
                error_time_ms=int(call_time * 1000)
            )

            # Record metrics for failed calls
            LLM_CALL_LATENCY.labels(
                provider="openai",
                model=self.embedding_model,
                operation="embedding_error",
            ).observe(call_time)

            # Re-raise the exception
            raise
        except Exception as e:
            # Handle unexpected errors
            call_time = time.time() - start_time
            
            logger.error(
                "Unexpected error in embedding generation",
                error=str(e),
                error_type=type(e).__name__,
                model=self.embedding_model,
                dimensions=embedding_dimensions,
                error_time_ms=int(call_time * 1000)
            )
            
            # Record metrics
            LLM_CALL_LATENCY.labels(
                provider="openai",
                model=self.embedding_model,
                operation="embedding_unexpected_error",
            ).observe(call_time)
            
            # Re-raise
            raise
