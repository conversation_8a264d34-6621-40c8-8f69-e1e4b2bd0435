"""
Qdrant vector database client for vector search operations.

This module provides an interface to the Qdrant vector database
for storing and searching vector embeddings.
"""

from typing import Any, Dict, List, Optional, Union

from prometheus_client import Counter
from qdrant_client import QdrantClient as QdrantClientBase
from qdrant_client.models import (
    Distance,
    <PERSON>Condition,
    Filter,
    MatchValue,
    PayloadSchemaType,
    PointStruct,
    VectorParams,
)

from src.coherence.core.config import settings
from src.coherence.core.metrics import (
    VECTOR_DELETE_LATENCY,
    VECTOR_INDEX_SIZE,
    VECTOR_SEARCH_LATENCY,
    VECTOR_UPSERT_LATENCY,
)

# Create new metric for dimension errors
VECTOR_UPSERT_DIMENSION_ERROR = Counter(
    "vector_upsert_dimension_error",
    "Count of dimension mismatch errors during vector upserts",
    ["collection"]
)
import structlog

logger = structlog.get_logger()

# Global instance for dependency injection
_qdrant_client_instance = None


async def get_qdrant_client(mock_for_testing: bool = False) -> "QdrantClient":
    """
    Get or create a Qdrant client instance.
    
    This function is used as a FastAPI dependency to provide a singleton 
    instance of the QdrantClient throughout the application.
    
    Args:
        mock_for_testing: If True, creates a mock client that can work without a running Qdrant server
    
    Returns:
        QdrantClient: A singleton instance of the QdrantClient
    """
    global _qdrant_client_instance
    if _qdrant_client_instance is None:
        _qdrant_client_instance = QdrantClient(mock_for_testing=mock_for_testing)
    return _qdrant_client_instance


class QdrantClient:
    """Qdrant client wrapper for vector operations."""

    def __init__(self, mock_for_testing: bool = False):
        """
        Initialize the Qdrant client.
        
        Args:
            mock_for_testing: If True, creates a mock client that can work without a running Qdrant server
        """
        self.mock_mode = mock_for_testing
        if not mock_for_testing:
            try:
                self.client = QdrantClientBase(
                    url=settings.QDRANT_URL,
                    api_key=settings.QDRANT_API_KEY if settings.QDRANT_API_KEY else None,
                    timeout=10.0,
                )
            except Exception as e:
                logger.warning(f"Failed to initialize Qdrant client: {str(e)}. Using mock mode instead.")
                self.mock_mode = True
                
        # Default vector size for embeddings from config
        self.embedding_dimension = settings.EMBEDDING_DIMENSION

    async def list_collections(self) -> List[str]:
        """
        List all collections in Qdrant.

        Returns:
            List of collection names
        """
        if self.mock_mode:
            logger.info("Using mock mode for list_collections")
            return ["intent_idx_mock_tenant_user", "template_idx_global"]
            
        try:
            collections = self.client.get_collections()
            return [collection.name for collection in collections.collections]
        except Exception as e:
            logger.error(f"Failed to list collections: {str(e)}", exc_info=True)
            return []

    async def collection_exists(self, collection_name: str) -> bool:
        """
        Check if a collection exists.

        Args:
            collection_name: Name of the collection to check

        Returns:
            True if the collection exists, False otherwise
        """
        try:
            collections = await self.list_collections()
            return collection_name in collections
        except Exception as e:
            logger.error(
                f"Failed to check if collection exists: {str(e)}",
                exc_info=True
            )
            return False
            
    async def get_collection(self, collection_name: str) -> Any:
        """
        Get collection information.

        Args:
            collection_name: Name of the collection to get

        Returns:
            Collection information if the collection exists, None otherwise
        """
        if self.mock_mode:
            logger.info("Using mock mode for get_collection")
            # Return a mock collection object with the standard dimension
            class MockCollectionConfig:
                class MockParams:
                    class MockVectors:
                        size = settings.EMBEDDING_DIMENSION
                    vectors = MockVectors()
                params = MockParams()
            
            class MockCollection:
                name = collection_name
                config = MockCollectionConfig()
            
            return MockCollection()
            
        try:
            return self.client.get_collection(collection_name=collection_name)
        except Exception as e:
            logger.error(
                f"Failed to get collection {collection_name}: {str(e)}",
                exc_info=True
            )
            return None
            
    async def _verify_dimensions(self, collection: str, expected: int) -> Optional[int]:
        """
        Verify that a collection's vector dimensions match the expected dimensions.
        
        Args:
            collection: Name of the collection to verify
            expected: Expected vector dimensions
            
        Returns:
            Optional[int]: The actual dimensions if collection exists, None otherwise
            
        Raises:
            ValueError: If the collection's dimensions don't match the expected dimensions
        """
        info = await self.get_collection(collection)
        if info is None:
            return None  # Collection doesn't exist, will be created with correct dimensions
            
        size = info.config.params.vectors.size
        if size != expected:
            error_msg = f"{collection} expects {size}-d vectors, but we are configured for {expected}-d vectors"
            logger.error("Vector dimension mismatch", 
                        collection=collection,
                        expected_dimension=expected,
                        actual_dimension=size)
            
            # Increment error metric
            VECTOR_UPSERT_DIMENSION_ERROR.labels(collection=collection).inc()
            
            # Add the actual dimension to the ValueError to help with reporting
            raise ValueError(error_msg, size, expected)
        
        logger.debug("Vector dimensions verified", 
                    collection=collection,
                    dimension=expected)
        
        return size

    async def create_intent_collection(
        self,
        tenant_id: str,
        role: str,
        vector_size: int = None,
    ) -> bool:
        """
        Create a new collection for intent vectors.

        Args:
            tenant_id: Tenant ID for multi-tenant isolation
            role: Role for the intent collection (e.g., 'user', 'system')
            vector_size: Size of the vector embeddings (default: self.embedding_dimension)

        Returns:
            True if the collection was created successfully, False otherwise
        """
        collection_name = f"intent_idx_{tenant_id}_{role}"
        vector_size = vector_size or self.embedding_dimension

        # Check if collection already exists
        if await self.collection_exists(collection_name):
            return True

        try:
            # Create the collection
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=vector_size,
                    distance=Distance.COSINE,
                ),
            )

            # Create payload index for intent name
            self.client.create_payload_index(
                collection_name=collection_name,
                field_name="intent",
                field_schema=PayloadSchemaType.KEYWORD,
            )

            logger.info(f"Created intent collection: {collection_name}")
            return True
        except Exception as e:
            logger.error(
                f"Failed to create intent collection {collection_name}: {str(e)}",
                exc_info=True
            )
            return False

    async def create_template_collection(
        self,
        collection_name: str,
        vector_size: int = None,
    ) -> bool:
        """
        Create a new collection for template vectors.

        Args:
            collection_name: Name of the collection to create
            vector_size: Size of the vector embeddings (default: self.embedding_dimension)

        Returns:
            True if the collection was created successfully, False otherwise
        """
        vector_size = vector_size or self.embedding_dimension

        # Check if collection already exists
        if await self.collection_exists(collection_name):
            return True

        try:
            # Create the collection
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=vector_size,
                    distance=Distance.COSINE,
                ),
            )

            # Create payload indexes for efficient filtering
            self.client.create_payload_index(
                collection_name=collection_name,
                field_name="template_id",
                field_schema=PayloadSchemaType.KEYWORD,
            )

            self.client.create_payload_index(
                collection_name=collection_name,
                field_name="template_key",
                field_schema=PayloadSchemaType.KEYWORD,
            )

            self.client.create_payload_index(
                collection_name=collection_name,
                field_name="template_category",
                field_schema=PayloadSchemaType.KEYWORD,
            )

            logger.info(f"Created template collection: {collection_name}")
            return True
        except Exception as e:
            logger.error(
                f"Failed to create template collection {collection_name}: {str(e)}",
                exc_info=True
            )
            return False

    async def upsert_intent_vectors(
        self,
        tenant_id: str,
        role: str,
        vectors: List[Dict[str, Any]],
    ) -> bool:
        """
        Insert or update intent vectors in a collection.

        Args:
            tenant_id: Tenant ID for multi-tenant isolation
            role: Role for the intent collection (e.g., 'user', 'system')
            vectors: List of vectors to upsert with format:
                     {"id": str, "vector": List[float], "metadata": Dict[str, Any]}

        Returns:
            True if the vectors were upserted successfully, False otherwise
        """
        collection_name = f"intent_idx_{tenant_id}_{role}"

        # Ensure collection exists
        if not await self.collection_exists(collection_name):
            success = await self.create_intent_collection(tenant_id, role)
            if not success:
                return False
                
        try:
            # Verify vector dimensions before upsert
            if len(vectors) > 0 and "vector" in vectors[0]:
                sample_vector_dimension = len(vectors[0]["vector"])
                await self._verify_dimensions(collection_name, sample_vector_dimension)
            
            with VECTOR_UPSERT_LATENCY.labels(collection=collection_name).time():
                # Convert vectors to Qdrant points
                points = [
                    PointStruct(
                        id=vector["id"],
                        vector=vector["vector"],
                        payload=vector["metadata"],
                    )
                    for vector in vectors
                ]

                # Upsert the points
                self.client.upsert(
                    collection_name=collection_name,
                    points=points,
                )

            # Update metrics
            VECTOR_INDEX_SIZE.labels(
                tenant_id=tenant_id,
                collection=collection_name,
            ).set(len(vectors))

            logger.info(
                f"Upserted {len(vectors)} intent vectors in {collection_name}"
            )
            return True
        except ValueError as e:
            # Specific handling for dimension mismatch errors
            logger.error(
                "Vector dimension mismatch detected during intent vector upsert",
                collection=collection_name,
                error=str(e),
                tenant_id=tenant_id,
                role=role
            )
            return False
        except Exception as e:
            logger.error(
                f"Failed to upsert intent vectors to {collection_name}: {str(e)}",
                exc_info=True
            )
            return False

    async def upsert_template_vectors(
        self,
        collection_name: str,
        points: List[Dict[str, Any]],
    ) -> bool:
        """
        Insert or update template vectors in a collection.

        Args:
            collection_name: Name of the collection to upsert vectors into
            points: List of points to upsert with format:
                   {"id": str, "vector": List[float], "metadata": Dict[str, Any]}

        Returns:
            True if the vectors were upserted successfully, False otherwise
        """
        # Ensure collection exists
        if not await self.collection_exists(collection_name):
            success = await self.create_template_collection(collection_name)
            if not success:
                return False

        try:
            # Verify vector dimensions before upsert
            if len(points) > 0 and "vector" in points[0]:
                sample_vector_dimension = len(points[0]["vector"])
                await self._verify_dimensions(collection_name, sample_vector_dimension)
                
            with VECTOR_UPSERT_LATENCY.labels(collection=collection_name).time():
                # Convert points to Qdrant points
                qdrant_points = [
                    PointStruct(
                        id=point["id"],
                        vector=point["vector"],
                        payload=point["metadata"],
                    )
                    for point in points
                ]

                # Upsert the points
                self.client.upsert(
                    collection_name=collection_name,
                    points=qdrant_points,
                )

            # Update metrics
            VECTOR_INDEX_SIZE.labels(
                collection=collection_name,
                tenant_id="template", # Using a fixed value for templates
            ).set(len(points))

            logger.info(
                f"Upserted {len(points)} template vectors in {collection_name}"
            )
            return True
        except ValueError as e:
            # Specific handling for dimension mismatch errors
            logger.error(
                "Vector dimension mismatch detected during template vector upsert",
                collection=collection_name,
                error=str(e)
            )
            return False
        except Exception as e:
            logger.error(
                f"Failed to upsert template vectors to {collection_name}: {str(e)}",
                exc_info=True
            )
            return False

    async def search(
        self,
        collection_name: str,
        query_vector: List[float],
        filter: Optional[Filter] = None,
        limit: int = 5,
        score_threshold: Optional[float] = None,
    ) -> List[Dict[str, Any]]:
        """
        Search for similar vectors in a collection.

        Args:
            collection_name: Name of the collection to search
            query_vector: Vector to search for
            filter: Optional filter to apply to the search
            limit: Maximum number of results to return
            score_threshold: Minimum similarity score threshold

        Returns:
            List of search results with scores and metadata
        """
        try:
            # Check if collection exists
            if not await self.collection_exists(collection_name):
                logger.warning(f"Collection {collection_name} does not exist")
                return []

            with VECTOR_SEARCH_LATENCY.labels(collection=collection_name).time():
                scored_points = self.client.search(
                    collection_name=collection_name,
                    query_vector=query_vector,
                    query_filter=filter,
                    limit=limit,
                    score_threshold=score_threshold,
                )

            # Convert ScoredPoint objects to dictionaries
            results = []
            for point in scored_points:
                result = {
                    "id": point.id,
                    "score": point.score,
                    **point.payload,
                }
                results.append(result)

            return results
        except Exception as e:
            logger.error(
                f"Failed to search collection {collection_name}: {str(e)}",
                exc_info=True
            )
            return []

    async def search_intents(
        self,
        tenant_id: str,
        role: str,
        query_vector: List[float],
        limit: int = 5,
        score_threshold: Optional[float] = None,
        intent_filter: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Search for intent matches.

        Args:
            tenant_id: Tenant ID for multi-tenant isolation
            role: Role for the intent collection (e.g., 'user', 'system')
            query_vector: Vector to search for
            limit: Maximum number of results to return
            score_threshold: Minimum similarity score threshold
            intent_filter: Optional intent name to filter by

        Returns:
            List of search results with scores and metadata
        """
        collection_name = f"intent_idx_{tenant_id}_{role}"
        
        # Build filter if intent_filter is specified
        filter = None
        if intent_filter:
            filter = Filter(
                must=[
                    FieldCondition(
                        key="intent",
                        match=MatchValue(value=intent_filter),
                    )
                ]
            )

        # Try tenant-specific collection first
        results = await self.search(
            collection_name=collection_name,
            query_vector=query_vector,
            filter=filter,
            limit=limit,
            score_threshold=score_threshold,
        )

        # If no results, try default collection
        if not results:
            default_collection = f"intent_idx_default_{role}"
            results = await self.search(
                collection_name=default_collection,
                query_vector=query_vector,
                filter=filter,
                limit=limit,
                score_threshold=score_threshold,
            )

        return results

    async def search_templates(
        self,
        collection_name: str,
        query_vector: List[float],
        filter: Optional[Dict[str, Any]] = None,
        limit: int = 5,
        score_threshold: Optional[float] = None,
    ) -> List[Dict[str, Any]]:
        """
        Search for template matches.

        Args:
            collection_name: Name of the collection to search
            query_vector: Vector to search for
            filter: Optional filter dictionary to apply to the search
            limit: Maximum number of results to return
            score_threshold: Minimum similarity score threshold

        Returns:
            List of search results with scores and metadata
        """
        # Convert filter dictionary to Qdrant Filter if provided
        qdrant_filter = None
        if filter:
            conditions = []
            for key, value in filter.items():
                if isinstance(value, dict) and "$eq" in value:
                    conditions.append(
                        FieldCondition(
                            key=key,
                            match=MatchValue(value=value["$eq"]),
                        )
                    )
            if conditions:
                qdrant_filter = Filter(must=conditions)

        # Search the collection
        results = await self.search(
            collection_name=collection_name,
            query_vector=query_vector,
            filter=qdrant_filter,
            limit=limit,
            score_threshold=score_threshold,
        )

        return results

    async def delete_intent_vectors(
        self,
        tenant_id: str,
        role: str,
        intent: Optional[str] = None,
    ) -> bool:
        """
        Delete intent vectors from a collection.

        Args:
            tenant_id: Tenant ID for multi-tenant isolation
            role: Role for the intent collection (e.g., 'user', 'system')
            intent: Optional intent name to delete specific intents

        Returns:
            True if the vectors were deleted successfully, False otherwise
        """
        collection_name = f"intent_idx_{tenant_id}_{role}"

        # Check if collection exists
        if not await self.collection_exists(collection_name):
            return True  # Nothing to delete

        try:
            with VECTOR_DELETE_LATENCY.labels(collection=collection_name).time():
                if intent:
                    # Delete specific intent
                    filter = Filter(
                        must=[
                            FieldCondition(
                                key="intent",
                                match=MatchValue(value=intent),
                            )
                        ]
                    )
                    self.client.delete(
                        collection_name=collection_name,
                        points_selector=filter,
                    )
                else:
                    # Delete entire collection
                    self.client.delete_collection(
                        collection_name=collection_name,
                    )

            logger.info(
                f"Deleted intent vectors from {collection_name}"
                + (f" for intent {intent}" if intent else "")
            )
            return True
        except Exception as e:
            logger.error(
                f"Failed to delete intent vectors from {collection_name}: {str(e)}",
                exc_info=True
            )
            return False

    async def delete_template_vectors(
        self,
        collection_name: str,
        points_selector: Union[Filter, Dict[str, Any]],
    ) -> bool:
        """
        Delete template vectors from a collection.

        Args:
            collection_name: Name of the collection to delete vectors from
            points_selector: Selector for points to delete (Filter or dict with "ids" key)

        Returns:
            True if the vectors were deleted successfully, False otherwise
        """
        # Check if collection exists
        if not await self.collection_exists(collection_name):
            return True  # Nothing to delete

        try:
            with VECTOR_DELETE_LATENCY.labels(collection=collection_name).time():
                # Handle different types of selectors
                if isinstance(points_selector, dict) and "ids" in points_selector:
                    # Delete by IDs
                    self.client.delete(
                        collection_name=collection_name,
                        points_selector=points_selector["ids"],
                    )
                elif isinstance(points_selector, Filter):
                    # Delete by filter
                    self.client.delete(
                        collection_name=collection_name,
                        points_selector=points_selector,
                    )
                else:
                    # Assume it's a filter dictionary
                    conditions = []
                    for key, value in points_selector.items():
                        if isinstance(value, dict) and "$eq" in value:
                            conditions.append(
                                FieldCondition(
                                    key=key,
                                    match=MatchValue(value=value["$eq"]),
                                )
                            )
                    if conditions:
                        filter = Filter(must=conditions)
                        self.client.delete(
                            collection_name=collection_name,
                            points_selector=filter,
                        )

            logger.info(f"Deleted template vectors from {collection_name}")
            return True
        except Exception as e:
            logger.error(
                f"Failed to delete template vectors from {collection_name}: {str(e)}",
                exc_info=True
            )
            return False