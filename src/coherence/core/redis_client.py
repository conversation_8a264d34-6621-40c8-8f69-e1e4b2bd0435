"""
Redis client for caching and state management.

This module provides a Redis client for:
- Caching frequently accessed data
- Storing conversation state between requests
- Managing distributed locks
- Implementing rate limiting
"""

import asyncio
import json
import logging
from typing import Any, Callable, Dict, List, Optional, Tuple

import redis.asyncio as redis

from src.coherence.core.config import settings

logger = logging.getLogger(__name__)


class RedisClient:
    """
    Async Redis client for Coherence.

    Provides high-level methods for common Redis operations
    used throughout the Coherence middleware.

    Attributes:
        redis: Async Redis client
        prefix: Key prefix for multi-tenant isolation
    """

    def __init__(self, url: str, prefix: str = "coherence"):
        """Initialize the Redis client.

        Args:
            url: Redis connection URL
            prefix: Key prefix for namespace isolation
        """
        self.redis = redis.from_url(url)
        self.prefix = prefix

    def _get_key(self, key: str) -> str:
        """Get a namespaced key.

        Args:
            key: Original key

        Returns:
            Namespaced key with prefix
        """
        return f"{self.prefix}:{key}"

    async def get(self, key: str) -> Optional[str]:
        """Get a value from Redis.

        Args:
            key: Key to retrieve

        Returns:
            Value or None if not found
        """
        full_key = self._get_key(key)
        return await self.redis.get(full_key)

    async def set(
        self,
        key: str,
        value: str,
        expire: Optional[int] = None,
    ) -> bool:
        """Set a value in Redis.

        Args:
            key: Key to set
            value: Value to store
            expire: Optional expiration time in seconds

        Returns:
            True if successful
        """
        full_key = self._get_key(key)
        return await self.redis.set(full_key, value, ex=expire)

    async def delete(self, key: str) -> int:
        """Delete a key from Redis.

        Args:
            key: Key to delete

        Returns:
            Number of keys deleted
        """
        full_key = self._get_key(key)
        return await self.redis.delete(full_key)

    async def exists(self, key: str) -> bool:
        """Check if a key exists in Redis.

        Args:
            key: Key to check

        Returns:
            True if the key exists
        """
        full_key = self._get_key(key)
        return bool(await self.redis.exists(full_key))

    async def get_json(self, key: str) -> Optional[Dict[str, Any]]:
        """Get a JSON value from Redis.

        Args:
            key: Key to retrieve

        Returns:
            Parsed JSON value or None if not found
        """
        value = await self.get(key)
        if value:
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return None
        return None

    async def set_json(
        self,
        key: str,
        value: Dict[str, Any],
        expire: Optional[int] = None,
    ) -> bool:
        """Set a JSON value in Redis.

        Args:
            key: Key to set
            value: Value to store
            expire: Optional expiration time in seconds

        Returns:
            True if successful
        """
        json_value = json.dumps(value)
        return await self.set(key, json_value, expire=expire)

    async def acquire_lock(
        self,
        lock_name: str,
        token: str,
        expire: int = 10,
    ) -> bool:
        """Acquire a distributed lock.

        Args:
            lock_name: Name of the lock
            token: Unique token for lock ownership
            expire: Lock expiration time in seconds

        Returns:
            True if lock was acquired
        """
        full_key = self._get_key(f"lock:{lock_name}")
        return bool(await self.redis.set(full_key, token, nx=True, ex=expire))

    async def release_lock(self, lock_name: str, token: str) -> bool:
        """Release a distributed lock.

        Args:
            lock_name: Name of the lock
            token: Unique token for lock ownership

        Returns:
            True if lock was released
        """
        full_key = self._get_key(f"lock:{lock_name}")

        # Only release if we own the lock
        current_token = await self.redis.get(full_key)
        if current_token == token:
            await self.redis.delete(full_key)
            return True
        return False

    async def increment(self, key: str, amount: int = 1) -> int:
        """Increment a counter in Redis.

        Args:
            key: Counter key
            amount: Amount to increment by

        Returns:
            New counter value
        """
        full_key = self._get_key(key)
        return await self.redis.incrby(full_key, amount)

    async def publish(self, channel: str, message: Any) -> int:
        """Publish a message to a Redis channel.

        Args:
            channel: Channel name
            message: Message to publish (will be JSON-encoded)

        Returns:
            Number of clients that received the message
        """
        full_channel = self._get_key(channel)

        # Convert message to JSON if it's not a string
        if not isinstance(message, str):
            message = json.dumps(message)

        return await self.redis.publish(full_channel, message)
        
    async def hset(self, key: str, mapping: Dict[str, Any]) -> int:
        """Set multiple hash fields to multiple values.
        
        Args:
            key: Hash key
            mapping: Dictionary of field/value pairs
            
        Returns:
            Number of fields that were added
        """
        full_key = self._get_key(key)
        return await self.redis.hset(full_key, mapping=mapping)
        
    async def hgetall(self, key: str) -> Dict[bytes, bytes]:
        """Get all fields and values in a hash.
        
        Args:
            key: Hash key
            
        Returns:
            Dictionary of field/value pairs
        """
        full_key = self._get_key(key)
        return await self.redis.hgetall(full_key)
        
    async def zadd(self, key: str, mapping: Dict[str, float]) -> int:
        """Add one or more members to a sorted set, or update their scores.
        
        Args:
            key: Sorted set key
            mapping: Dictionary of member/score pairs
            
        Returns:
            Number of elements added
        """
        full_key = self._get_key(key)
        return await self.redis.zadd(full_key, mapping)
        
    async def zrange(self, key: str, start: int, end: int) -> List[bytes]:
        """Return a range of members in a sorted set.
        
        Args:
            key: Sorted set key
            start: Starting index
            end: Ending index
            
        Returns:
            List of members in range
        """
        full_key = self._get_key(key)
        return await self.redis.zrange(full_key, start, end)
        
    async def zrevrangebyscore(
        self, 
        key: str, 
        min: float, 
        max: float, 
        start: int = 0, 
        num: Optional[int] = None,
        withscores: bool = False
    ) -> List:
        """Return a range of members in a sorted set by score, with scores in descending order.
        
        Args:
            key: Sorted set key
            min: Minimum score
            max: Maximum score
            start: Starting offset
            num: Number of elements to return
            withscores: Return scores along with elements
            
        Returns:
            List of members or (member, score) tuples if withscores is True
        """
        full_key = self._get_key(key)
        
        # Build optional arguments
        kwargs = {}
        if num is not None:
            kwargs["num"] = num
        if withscores:
            kwargs["withscores"] = True
            
        return await self.redis.zrevrangebyscore(full_key, max, min, start, **kwargs)
        
    async def zscan(self, key: str, cursor: int = 0, match: Optional[str] = None) -> Tuple[int, List]:
        """Incrementally iterate sorted set elements and associated scores.
        
        Args:
            key: Sorted set key
            cursor: Cursor position
            match: Pattern to match
            
        Returns:
            Tuple of (next_cursor, results)
        """
        full_key = self._get_key(key)
        
        # Build optional arguments
        kwargs = {}
        if match:
            kwargs["match"] = match
            
        return await self.redis.zscan(full_key, cursor, **kwargs)
        
    async def zrem(self, key: str, *members: str) -> int:
        """Remove one or more members from a sorted set.
        
        Args:
            key: Sorted set key
            *members: Members to remove
            
        Returns:
            Number of members removed
        """
        full_key = self._get_key(key)
        return await self.redis.zrem(full_key, *members)
        
    async def expire(self, key: str, seconds: int) -> bool:
        """Set a key's time to live in seconds.
        
        Args:
            key: Key
            seconds: Time to live in seconds
            
        Returns:
            True if the timeout was set
        """
        full_key = self._get_key(key)
        return bool(await self.redis.expire(full_key, seconds))

    async def subscribe(self, *channels: str) -> redis.client.PubSub:
        """Subscribe to one or more Redis channels.

        Args:
            *channels: Channel names

        Returns:
            Redis PubSub object
        """
        # Convert channel names to full keys
        full_channels = [self._get_key(channel) for channel in channels]

        # Create PubSub object
        pubsub = self.redis.pubsub()

        # Subscribe to channels
        await pubsub.subscribe(*full_channels)

        return pubsub

    async def listen_for_messages(
        self,
        pubsub: redis.client.PubSub,
        handler: Callable[[str, Any], None],
        stop_event: Optional[asyncio.Event] = None,
    ) -> None:
        """Listen for messages on subscribed channels.

        Args:
            pubsub: Redis PubSub object
            handler: Function to handle received messages (channel, message)
            stop_event: Optional event to signal stopping the listener
        """
        prefix_len = (
            len(self.prefix) + 1
        )  # prefix + ':' length for channel name trimming

        try:
            while True:
                if stop_event and stop_event.is_set():
                    break

                # Get next message with timeout
                message = await pubsub.get_message(
                    ignore_subscribe_messages=True, timeout=1.0
                )

                if message:
                    try:
                        # Extract channel name without prefix
                        channel = message["channel"].decode("utf-8")[prefix_len:]

                        # Parse message data (often JSON)
                        data = message["data"].decode("utf-8")
                        try:
                            data = json.loads(data)
                        except json.JSONDecodeError:
                            # Keep as string if not valid JSON
                            pass

                        # Call handler
                        await handler(channel, data)
                    except Exception as e:
                        logger.error(f"Error processing Redis message: {e}")

                # Add small delay to prevent CPU overuse
                await asyncio.sleep(0.01)
        finally:
            # Ensure we close the pubsub connection
            await pubsub.close()

    async def close(self) -> None:
        """Close the Redis connection."""
        if hasattr(self, "redis") and self.redis is not None:
            await self.redis.close()


async def get_redis_client() -> RedisClient:
    """Factory function to create a RedisClient.

    Returns:
        Initialized RedisClient
    """
    redis_url = (
        f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"
    )
    if settings.REDIS_PASSWORD:
        redis_url = f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"

    return RedisClient(redis_url)
