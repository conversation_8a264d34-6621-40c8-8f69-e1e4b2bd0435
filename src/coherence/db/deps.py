"""
Database dependencies for FastAPI dependency injection.
"""

import uuid
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator, Optional

from fastapi import Request
from sqlalchemy import event
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.db.session import async_session
from src.coherence.middleware.tenant_context import SQLAlchemyTenantContext

# Note: We no longer use a global session to avoid concurrency issues


async def get_db(request: Request) -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency that provides an async database session with tenant context.

    Args:
        request: The current FastAPI request

    Yields:
        AsyncSession: A SQLAlchemy async database session with tenant context

    Example:
        ```python
        @app.get("/items/")
        async def read_items(db: AsyncSession = Depends(get_db)):
            items = await crud.get_items(db)
            return items
        ```
    """
    session = async_session()

    try:
        # Apply tenant context if request is provided
        if request is not None:
            # For async sessions, we need to apply the listener to the connection pool
            # Get raw connection when it's checked out from the pool
            engine = session.sync_session.get_bind().engine

            @event.listens_for(engine, "checkout")
            def receive_checkout(dbapi_connection, connection_record, connection_proxy):
                # Set tenant context from request
                tenant_id = getattr(request.state, "rls_tenant_id", None)
                clerk_org_id = getattr(request.state, "rls_clerk_org_id", None)
                is_system_admin = getattr(request.state, "is_system_admin", False)
                is_admin = getattr(request.state, "rls_is_admin", False)

                # Log the context values for debugging
                import logging

                logger = logging.getLogger(__name__)
                logger.debug(
                    f"Setting tenant context: tenant_id={tenant_id}, clerk_org_id={clerk_org_id}, "
                    f"is_system_admin={is_system_admin}, is_admin={is_admin}"
                )

                # Apply the tenant context to the connection
                SQLAlchemyTenantContext.set_context_for_connection(
                    dbapi_connection,
                    connection_record,
                    tenant_id=tenant_id,
                    clerk_org_id=clerk_org_id,
                    is_system_admin=is_system_admin,
                    is_admin=is_admin,
                )

            # Store the listener function to remove it later
            session._checkout_listener = receive_checkout

        yield session
    except Exception:
        # Ensure session is rolled back on error
        await session.rollback()
        raise
    finally:
        # Remove the event listener explicitly if it was registered
        if request is not None and hasattr(session, "_checkout_listener"):
            checkout_listener = session._checkout_listener
            engine = session.sync_session.get_bind().engine
            event.remove(engine, "checkout", checkout_listener)
        await session.close()


@asynccontextmanager
async def get_admin_db_session(
    tenant_id: Optional[uuid.UUID] = None,
) -> AsyncGenerator[AsyncSession, None]:
    """
    Async context manager that provides a database session with admin privileges.

    This session bypasses row-level security by setting the is_admin flag to True.

    Args:
        tenant_id: Optional tenant ID to scope the session to

    Yields:
        AsyncSession: A SQLAlchemy async database session with admin privileges

    Example:
        ```python
        async with get_admin_db_session() as admin_db:
            # This will see all rows regardless of tenant
            results = await admin_db.execute(query)
        ```
    """
    session = async_session()

    try:
        # For async sessions, we need to apply the listener to the connection pool
        # Get raw connection when it's checked out from the pool
        engine = session.sync_session.get_bind().engine

        @event.listens_for(engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            # Set admin context for this session, optionally with a tenant_id
            SQLAlchemyTenantContext.set_context_for_connection(
                dbapi_connection,
                connection_record,
                tenant_id=tenant_id,
                clerk_org_id=None,  # No specific clerk org in admin session
                is_system_admin=True,  # This is a system admin session
                is_admin=True,  # For backward compatibility
            )

        # Store the listener function to remove it later
        session._checkout_listener = receive_checkout

        yield session
    except Exception:
        # Ensure session is rolled back on error
        await session.rollback()
        raise
    finally:
        # Remove the event listener explicitly if it was registered
        if hasattr(session, "_checkout_listener"):
            checkout_listener = session._checkout_listener
            engine = session.sync_session.get_bind().engine
            event.remove(engine, "checkout", checkout_listener)
        await session.close()


async def get_db_from_session() -> AsyncSession:
    """
    Get a database session outside of a request context.
    
    This is useful for services that need database access but don't have
    a FastAPI request context, such as background tasks or internal services.
    
    The session will have system admin privileges to bypass row-level security.
    
    Returns:
        AsyncSession: A SQLAlchemy async database session with system admin privileges
    """
    # Create a new session each time to avoid concurrency issues
    session = async_session()
    
    # Set up admin privileges for the session
    engine = session.sync_session.get_bind().engine
    
    @event.listens_for(engine, "checkout")
    def receive_checkout(dbapi_connection, connection_record, connection_proxy):
        # Set admin context for this session
        SQLAlchemyTenantContext.set_context_for_connection(
            dbapi_connection,
            connection_record,
            tenant_id=None,
            clerk_org_id=None,
            is_system_admin=True,
            is_admin=True,
        )
    
    # Store the listener function on the session
    session._checkout_listener = receive_checkout
    
    return session
