"""
Database session management.
"""

import logging

from sqlalchemy import create_engine, event
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import sessionmaker

from src.coherence.core.config import settings
from src.coherence.middleware.tenant_context import (
    current_tenant_id_var,
    is_system_admin_var,
)

logger = logging.getLogger(__name__)

# Create database connection strings
db_url = str(settings.SQLALCHEMY_DATABASE_URI)
async_db_url = db_url.replace("postgresql+psycopg2", "postgresql+asyncpg")

# Create database engines with connection pools
engine = create_engine(
    db_url,
    pool_pre_ping=True,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    echo=settings.DB_ECHO,
)

async_engine = create_async_engine(
    async_db_url,
    pool_pre_ping=True,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    echo=settings.DB_ECHO,
)

# Create session factories
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
async_session = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False,
)


# Event listener for synchronous engine
def set_session_tenant_params_sync(dbapi_connection, connection_record):
    """Sets PostgreSQL session variables for the synchronous engine."""
    tenant_id = current_tenant_id_var.get()
    is_admin = is_system_admin_var.get()
    cursor = None  # Initialize cursor to None
    try:
        cursor = dbapi_connection.cursor()
        if tenant_id is not None:
            logger.debug(f"Sync engine: Setting app.current_tenant_id to {tenant_id}")
            cursor.execute(f"SET app.current_tenant_id = '{tenant_id}'")
        else:
            logger.debug("Sync engine: Resetting app.current_tenant_id")
            cursor.execute("RESET app.current_tenant_id") # Or SET app.current_tenant_id = NULL

        logger.debug(f"Sync engine: Setting app.is_system_admin to {str(is_admin).lower()}")
        cursor.execute(f"SET app.is_system_admin = '{str(is_admin).lower()}'")
        # dbapi_connection.commit() # Not typically needed for SET commands
    except Exception as e:
        logger.error(f"Error setting session parameters for sync engine: {e}")
        # Optionally rollback if the connection is in a transaction, though SET usually doesn't start one.
        # if hasattr(dbapi_connection, "rollback"):
        #     dbapi_connection.rollback()
        raise # Re-raise the exception to signal failure
    finally:
        if cursor:
            cursor.close()

event.listen(engine, "connect", set_session_tenant_params_sync)


# Event listener for asynchronous engine
async def set_session_tenant_params_async(dbapi_connection, connection_record):
    """Sets PostgreSQL session variables for the asynchronous engine."""
    tenant_id = current_tenant_id_var.get()
    is_admin = is_system_admin_var.get()
    # For asyncpg, operations are typically done directly on the connection
    try:
        if tenant_id is not None:
            logger.debug(f"Async engine: Setting app.current_tenant_id to {tenant_id}")
            await dbapi_connection.execute("SET app.current_tenant_id = $1", str(tenant_id))
        else:
            logger.debug("Async engine: Resetting app.current_tenant_id")
            await dbapi_connection.execute("RESET app.current_tenant_id") # Or SET app.current_tenant_id = NULL

        logger.debug(f"Async engine: Setting app.is_system_admin to {str(is_admin).lower()}")
        await dbapi_connection.execute("SET app.is_system_admin = $1", str(is_admin).lower())
    except Exception as e:
        logger.error(f"Error setting session parameters for async engine: {e}")
        # Asyncpg connections might not have a rollback method in the same way,
        # error handling might involve releasing the connection if it's tainted.
        raise # Re-raise the exception


# For async_engine, the event is 'do_connect' for asyncpg
# However, 'connect' is also listed for async engines in some contexts,
# but 'do_connect' is more specific for the DBAPI part.
# Let's try 'connect' first as it's more general, if it fails, 'do_connect' is the alternative.
# For asyncpg, the dbapi_connection is the asyncpg connection itself.
event.listen(async_engine.sync_engine, "connect", set_session_tenant_params_sync)
# The above line is likely incorrect for async_engine.
# The correct way to listen to async engine events, especially for asyncpg,
# is often on the pool or directly if the dialect supports it.
# Let's use the 'connect' event on the async_engine directly,
# and the listener must be an async function.

# Corrected listener for async_engine:
@event.listens_for(async_engine.sync_engine, "connect")
def receive_connect(dbapi_connection, connection_record):
    """
    Listens for synchronous 'connect' events on the underlying sync_engine
    of the async_engine and applies session parameters.
    This is a common pattern when using async_sessionmaker with a sync driver part.
    """
    set_session_tenant_params_sync(dbapi_connection, connection_record)

# If the async_engine directly supports an async 'connect' or 'do_connect' event
# that passes an async-compatible dbapi_connection, that would be:
# @event.listens_for(async_engine, "do_connect") # or "connect" if it's an async event
# async def receive_async_connect(dbapi_connection, connection_record):
# await set_session_tenant_params_async(dbapi_connection, connection_record)
# For now, relying on the sync_engine's connect event is safer if the async version is tricky.
# The prompt implies setting for *all* connections, so both engines should be covered.

# Note: The get_db dependency is now in deps.py
