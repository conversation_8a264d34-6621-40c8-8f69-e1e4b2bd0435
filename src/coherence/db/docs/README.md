# Coherence Database Schema

This document provides an overview of the Coherence database schema, which follows a multi-tenant architecture with proper isolation between tenants.

## Database Overview

The Coherence database is designed with these core principles:

1. **Multi-tenant isolation** - Each tenant's data is properly isolated
2. **Template inheritance** - Templates can be shared across tenants and customized
3. **OpenAPI integration** - External services can be integrated via OpenAPI specs
4. **Versioning support** - Templates maintain version history for auditing

## Schema Diagram

```
┌─────────────┐     ┌──────────────┐     ┌───────────────────┐
│   Tenants   │─────┤   API Keys   │     │  Tenant Settings  │
└─────────────┘     └──────────────┘     └───────────────────┘
       │                                            │
       │                                            │
       │                ┌─────────────┐             │
       └───────────────┤  Templates   │─────────────┘
                       └─────────────┘
                             │
              ┌──────────────┼──────────────┐
              │              │              │
    ┌─────────────────┐ ┌──────────┐ ┌────────────────┐
    │Template Versions│ │Template  │ │   Template     │
    └─────────────────┘ │   Tests  │ │ Dependencies   │
                        └──────────┘ └────────────────┘
                                           
       ┌────────────────┐
       │API Integrations│
       └────────────────┘
                │
       ┌────────┴───────┐
       │                │
┌─────────────┐ ┌──────────────┐
│API Endpoints│ │API Auth Config│
└─────────────┘ └──────────────┘
       │
┌─────────────┐
│API Rate Limit│
└─────────────┘
```

## Tables Description

### Tenant Management

#### `tenants`
- `id` (UUID, PK) - Unique identifier
- `name` (String) - Tenant name
- `industry_pack` (String) - Industry-specific template pack
- `compliance_tier` (String) - Compliance level
- `settings` (JSONB) - Tenant-specific settings
- `created_at` (Timestamp) - Creation timestamp
- `updated_at` (Timestamp) - Last update timestamp

#### `api_keys`
- `id` (UUID, PK) - Unique identifier
- `tenant_id` (UUID, FK) - Reference to tenant
- `label` (String) - Human-readable label
- `key_hash` (String) - Secure hash of the API key
- `created_at` (Timestamp) - Creation timestamp
- `last_used_at` (Timestamp) - Last usage timestamp
- `revoked` (Boolean) - Revocation status
- `expires_at` (Timestamp) - Expiration timestamp

#### `tenant_settings`
- `tenant_id` (UUID, PK/FK) - Reference to tenant
- `tier1_threshold` (String) - Tier 1 intent recognition threshold
- `tier2_threshold` (String) - Tier 2 intent recognition threshold
- `llm_model` (String) - Default LLM model
- `embedding_model` (String) - Default embedding model
- `max_requests_per_min` (String) - Rate limit for tenant
- `max_tokens_per_month` (String) - Token quota for tenant
- `settings` (JSONB) - Additional settings
- `created_at` (Timestamp) - Creation timestamp
- `updated_at` (Timestamp) - Last update timestamp

### Template Management

#### `templates`
- `id` (UUID, PK) - Unique identifier
- `scope` (Enum) - Template scope (global, pack, tenant)
- `scope_id` (UUID) - Identifier for the scope (null for global)
- `tenant_id` (UUID, FK) - Reference to tenant (null for global/pack)
- `key` (String) - Template key (unique within scope)
- `category` (Enum) - Template category (intent_router, param_complete, etc.)
- `version` (Integer) - Current version
- `language` (String) - Template language code
- `body` (Text) - Template content
- `description` (String) - Template description
- `actions` (JSONB) - Available actions (for intent_router templates)
- `parameters` (JSONB) - Parameter schema
- `created_at` (Timestamp) - Creation timestamp
- `updated_at` (Timestamp) - Last update timestamp
- `created_by` (UUID) - User who created the template

#### `template_versions`
- `template_id` (UUID, PK/FK) - Reference to template
- `version` (Integer, PK) - Version number
- `body` (Text) - Template content at this version
- `actions` (JSONB) - Actions at this version
- `parameters` (JSONB) - Parameters at this version
- `editor_id` (UUID) - User who edited this version
- `edited_at` (Timestamp) - Edit timestamp
- `change_reason` (String) - Reason for the change

#### `template_tests`
- `id` (UUID, PK) - Unique identifier
- `template_id` (UUID, FK) - Reference to template
- `test_name` (String) - Test name
- `test_input` (JSONB) - Test input data
- `expected_output` (JSONB) - Expected output
- `created_at` (Timestamp) - Creation timestamp
- `last_run_at` (Timestamp) - Last test run timestamp
- `last_result` (Boolean) - Last test result (pass/fail)

#### `template_dependencies`
- `id` (UUID, PK) - Unique identifier
- `template_id` (UUID, FK) - Reference to dependent template
- `depends_on_template_id` (UUID, FK) - Reference to parent template
- `dependency_type` (Enum) - Type of dependency (extends, includes)

### API Integration

#### `api_integrations`
- `id` (UUID, PK) - Unique identifier
- `tenant_id` (UUID, FK) - Reference to tenant
- `name` (String) - Integration name
- `version` (String) - API version
- `openapi_spec` (JSONB) - OpenAPI specification
- `base_url` (String) - Base URL for the API
- `created_at` (Timestamp) - Creation timestamp
- `updated_at` (Timestamp) - Last update timestamp
- `status` (Enum) - Integration status (draft, active, disabled)

#### `api_auth_configs`
- `integration_id` (UUID, PK/FK) - Reference to integration
- `auth_type` (Enum) - Authentication type (api_key, oauth2, etc.)
- `credentials` (JSONB) - Credentials (encrypted)
- `scopes` (JSONB) - Auth scopes
- `refresh_token` (String) - Refresh token (encrypted)
- `expires_at` (Timestamp) - Token expiration timestamp

#### `api_endpoints`
- `id` (UUID, PK) - Unique identifier
- `integration_id` (UUID, FK) - Reference to integration
- `path` (String) - Endpoint path
- `method` (String) - HTTP method
- `operation_id` (String) - OpenAPI operation ID
- `action_class_name` (String) - Generated action class name
- `intent_id` (String) - Mapped intent ID
- `enabled` (Boolean) - Endpoint enabled status

#### `api_rate_limits`
- `id` (UUID, PK) - Unique identifier
- `endpoint_id` (UUID, FK) - Reference to endpoint
- `requests_per_min` (Integer) - Maximum requests per minute
- `burst_size` (Integer) - Burst size for rate limiting
- `cooldown_sec` (Integer) - Cooldown period after rate limit exceeded

## Multi-tenant Isolation

The database implements multi-tenant isolation through:

1. **Foreign key constraints** - Tenant records are linked to all tenant-specific data
2. **API key authentication** - Each tenant has its own API keys
3. **Row-level security** - Postgres RLS policies further enforce isolation at the database level

## Template Inheritance

Templates support inheritance through:

1. **Scope hierarchy** - global → industry pack → tenant specific
2. **Template dependencies** - Templates can extend or include other templates
3. **Versioning** - All template changes are tracked with version history

## Migration Management

Database migrations are managed using Alembic:

1. **Initial setup** - Creates tenant and API key tables
2. **Template extension** - Adds template management tables
3. **OpenAPI integration** - Adds API integration tables

## Usage

To apply migrations:

```bash
alembic upgrade head
```

To create a new migration:

```bash
alembic revision --autogenerate -m "description"
```

To reset the database (development only):

```bash
alembic downgrade base
alembic upgrade head
```