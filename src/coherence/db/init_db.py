"""
Database initialization and seeding.
"""

import logging

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.core.config import settings
from src.coherence.db.base import Base
from src.coherence.db.session import async_engine
from src.coherence.models.tenant import Tenant

logger = logging.getLogger(__name__)


async def init_db(db: AsyncSession) -> None:
    """
    Initialize database and seed initial data.

    Args:
        db: Async database session
    """
    # Create default tenant if configured
    if settings.SEED_DEFAULT_TENANT:
        # Check if default tenant exists
        result = await db.execute(select(Tenant).limit(1))
        if not result.scalars().first():
            default_tenant = Tenant(
                name=settings.DEFAULT_TENANT_NAME,
                industry_pack="default",
                compliance_tier="standard",
            )
            db.add(default_tenant)
            await db.commit()
            logger.info(
                f"Created default tenant: {default_tenant.name} ({default_tenant.id})"
            )


async def create_tables() -> None:
    """
    Create all database tables.

    This should only be used for development and testing.
    For production, use Alembic migrations.
    """
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    logger.info("Created database tables")
