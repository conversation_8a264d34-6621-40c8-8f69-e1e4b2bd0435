"""
Template models for the Coherence template management system.
"""

import uuid
from datetime import datetime
from typing import TYPE_CHECKING, Dict, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Index, Integer, String, func
from sqlalchemy.dialects.postgresql import ENUM, JSONB, TEXT, UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.coherence.db.base_class import Base

if TYPE_CHECKING:
    from src.coherence.models.tenant import Tenant


# Define literal types for enums to support type checking
from enum import Enum


# Template scope enum
class TemplateScope(str, Enum):
    """Enum for template scope values."""

    GLOBAL = "global"  # System-wide templates
    PACK = "pack"  # Industry-specific templates
    TENANT = "tenant"  # Tenant-specific templates


# Template category enum
class TemplateCategory(str, Enum):
    """Enum for template category values."""

    INTENT_ROUTER = "intent_router"  # Used for intent recognition
    PARAM_COMPLETE = "param_complete"  # Used for parameter extraction
    RETRIEVAL = "retrieval"  # Used for RAG context integration
    RESPONSE_GEN = "response_gen"  # Used for response generation
    ERROR_HANDLER = "error_handler"  # Used for error handling
    ACTION = "action"  # Used for API action execution
    DOCUMENTATION = "documentation"  # Used for API documentation
    UNIFIED = "unified"  # New unified template type containing all components


# SQLAlchemy enum types
template_scope = ENUM(
    "global",  # System-wide templates
    "pack",  # Industry-specific templates
    "tenant",  # Tenant-specific templates
    name="template_scope",
    create_type=False,
)

# Template category enum type
template_category = ENUM(
    "intent_router",  # Used for intent recognition
    "param_complete",  # Used for parameter extraction
    "retrieval",  # Used for RAG context integration
    "response_gen",  # Used for response generation
    "error_handler",  # Used for error handling
    "action",  # Used for API action execution
    "documentation",  # Used for API documentation
    "unified",  # New unified template type containing all components
    name="template_category",
    create_type=False,
)


class Template(Base):
    """
    Template model for storing parameterized prompts and configurations.

    Templates support a hierarchical structure with inheritance:
    global templates → industry pack templates → tenant templates
    """

    __tablename__ = "templates"

    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )

    # Template hierarchy
    scope: Mapped[TemplateScope] = mapped_column(
        template_scope, nullable=False, default="tenant"
    )
    scope_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True), nullable=True
    )  # NULL for global
    tenant_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        nullable=True,  # NULL for global and pack templates
    )

    # Template metadata
    key: Mapped[str] = mapped_column(String, nullable=False)
    category: Mapped[TemplateCategory] = mapped_column(
        template_category, nullable=False
    )
    version: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
    language: Mapped[str] = mapped_column(String(5), nullable=False, default="en")
    protected: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False, 
        server_default="false", comment="When true, template cannot be edited or deleted by regular admins")
    
    # Unified template fields
    endpoint_id: Mapped[Optional[str]] = mapped_column(
        String, nullable=True, comment="Endpoint identifier for unified templates (e.g., 'GET_/api/weather')"
    )
    intent_config: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True, comment="Intent patterns and configuration for unified templates"
    )
    action_config: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True, comment="API action configuration (method, path, auth, retries)"
    )
    ui_fields: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True, comment="UI field definitions for parameter forms"
    )
    prompts: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True, comment="System, extraction, and completion prompts"
    )
    docs_config: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True, comment="Documentation configuration and examples"
    )

    # Template content
    body: Mapped[str] = mapped_column(TEXT, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    actions: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True
    )  # For intent_router templates
    parameters: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True
    )  # Schema for parameters
    response_format: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True
    )  # Response formatting configuration
    test_data: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True, comment="Test data and mock responses for development"
    )  # Test data for offline development and testing

    # Audit fields
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
    )
    created_by: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True), nullable=True
    )

    # Relationships
    tenant: Mapped[Optional["Tenant"]] = relationship(
        "Tenant", back_populates="templates"
    )
    versions: Mapped[List["TemplateVersion"]] = relationship(
        "TemplateVersion",
        back_populates="template",
        cascade="all, delete-orphan",
    )
    dependencies: Mapped[List["TemplateDependency"]] = relationship(
        "TemplateDependency",
        back_populates="template",
        foreign_keys="TemplateDependency.template_id",
        cascade="all, delete-orphan",
    )
    dependents: Mapped[List["TemplateDependency"]] = relationship(
        "TemplateDependency",
        back_populates="depends_on",
        foreign_keys="TemplateDependency.depends_on_template_id",
        cascade="all, delete-orphan",
    )
    tests: Mapped[List["TemplateTest"]] = relationship(
        "TemplateTest",
        back_populates="template",
        cascade="all, delete-orphan",
    )

    # Indexes for efficient lookup
    __table_args__ = (
        Index(
            "ix_templates_scope_scope_id_key_version",
            scope,
            scope_id,
            key,
            version,
            unique=True,
        ),
        Index("ix_templates_tenant_id", tenant_id),
    )


class TemplateVersion(Base):
    """
    Version history for templates.

    Stores the full content of each template version for audit
    and rollback purposes.
    """

    __tablename__ = "template_versions"

    # Columns
    template_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("templates.id", ondelete="CASCADE"),
        primary_key=True,
    )
    version: Mapped[int] = mapped_column(Integer, primary_key=True)
    body: Mapped[str] = mapped_column(TEXT, nullable=False)
    actions: Mapped[Optional[Dict[str, object]]] = mapped_column(JSONB, nullable=True)
    parameters: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True
    )
    response_format: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True
    )
    
    # Unified template version fields
    endpoint_id: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    intent_config: Mapped[Optional[Dict[str, object]]] = mapped_column(JSONB, nullable=True)
    action_config: Mapped[Optional[Dict[str, object]]] = mapped_column(JSONB, nullable=True)
    ui_fields: Mapped[Optional[Dict[str, object]]] = mapped_column(JSONB, nullable=True)
    prompts: Mapped[Optional[Dict[str, object]]] = mapped_column(JSONB, nullable=True)
    docs_config: Mapped[Optional[Dict[str, object]]] = mapped_column(JSONB, nullable=True)
    test_data: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True, comment="Test data and mock responses for development"
    )
    
    editor_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True), nullable=True
    )
    edited_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    change_reason: Mapped[Optional[str]] = mapped_column(String, nullable=True)

    # Relationships
    template: Mapped["Template"] = relationship("Template", back_populates="versions")


class TemplateTest(Base):
    """
    Test cases for templates.

    Defines input/output tests to validate template behavior
    during development and after changes.
    """

    __tablename__ = "template_tests"

    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    template_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("templates.id", ondelete="CASCADE"),
        nullable=False,
    )
    test_name: Mapped[str] = mapped_column(String, nullable=False)
    test_input: Mapped[Dict[str, object]] = mapped_column(JSONB, nullable=False)
    expected_output: Mapped[Dict[str, object]] = mapped_column(JSONB, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    last_run_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    last_result: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)

    # Relationships
    template: Mapped["Template"] = relationship("Template", back_populates="tests")


class DependencyType(str, Enum):
    """Enum for template dependency type values."""

    EXTENDS = "extends"  # Template extends/inherits from another template
    INCLUDES = "includes"  # Template includes another template


class TemplateDependency(Base):
    """
    Dependencies between templates.

    Tracks relationships like "extends" and "includes" between templates
    to manage inheritance and updates.
    """

    __tablename__ = "template_dependencies"

    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    template_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("templates.id", ondelete="CASCADE"),
        nullable=False,
    )
    depends_on_template_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("templates.id", ondelete="CASCADE"),
        nullable=False,
    )
    dependency_type: Mapped[DependencyType] = mapped_column(
        ENUM("extends", "includes", name="dependency_type"),
        nullable=False,
    )

    # Relationships
    template: Mapped["Template"] = relationship(
        "Template",
        back_populates="dependencies",
        foreign_keys=[template_id],
    )
    depends_on: Mapped["Template"] = relationship(
        "Template",
        back_populates="dependents",
        foreign_keys=[depends_on_template_id],
    )
