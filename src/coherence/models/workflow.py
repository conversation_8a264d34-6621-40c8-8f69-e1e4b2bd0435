import uuid
from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, Text
from sqlalchemy.dialects.postgresql import JSON<PERSON>, UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

# Assuming a Base is defined in your db setup, e.g., in src/coherence/db/base_class.py
# For now, let's define a local Base for this file to be self-contained.
# Replace this with your actual Base from your project's db setup.
try:
    from src.coherence.db.base_class import Base
except ImportError:
    Base = declarative_base()


class Workflow(Base):
    __tablename__ = "workflows"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey('tenants.id'), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    is_enabled = Column(Boolean, default=True, nullable=False)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    steps = relationship("WorkflowStep", back_populates="workflow", cascade="all, delete-orphan", order_by="WorkflowStep.order")
    
    # Relationship to Tenant (optional, if you need to access tenant object directly from workflow)
    # tenant = relationship("Tenant", back_populates="workflows")


class WorkflowStep(Base):
    __tablename__ = "workflow_steps"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    workflow_id = Column(UUID(as_uuid=True), ForeignKey('workflows.id'), nullable=False, index=True)
    
    name = Column(String(255), nullable=False)
    step_type = Column(String(100), nullable=False)  # e.g., 'action', 'condition', 'integration'
    config = Column(JSONB, nullable=True, default=lambda: {}) # Or Text if JSONB is not preferred/available
    order = Column(Integer, nullable=False)

    workflow = relationship("Workflow", back_populates="steps")

    __table_args__ = (
        # Consider adding a unique constraint for order within a workflow if needed
        # UniqueConstraint('workflow_id', 'order', name='uq_workflow_step_order'),
    ) 