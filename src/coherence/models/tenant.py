"""
Tenant model definitions for multi-tenant isolation.
"""

import uuid
from datetime import datetime
from typing import TYPE_CHECKING, Dict, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Index, String, func
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.coherence.db.base_class import Base

if TYPE_CHECKING:
    from src.coherence.models.template import Template
    # OrganizationAPIKey will be defined in this file, direct reference is okay


class Tenant(Base):
    """
    Tenant model representing a customer or organization.

    Each tenant has isolated data, templates, and settings.
    """

    # Table name
    __tablename__ = "tenants"

    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    clerk_org_id: Mapped[Optional[str]] = mapped_column(String, nullable=True, unique=True)
    industry_pack: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    compliance_tier: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    settings: Mapped[Optional[Dict[str, str]]] = mapped_column(JSONB, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
    )

    # Relationships
    api_keys: Mapped[List["APIKey"]] = relationship(
        "APIKey", back_populates="tenant", cascade="all, delete-orphan"
    )
    templates: Mapped[List["Template"]] = relationship(
        "Template", back_populates="tenant"
    )
    organization_api_keys: Mapped[List["OrganizationAPIKey"]] = relationship(
        "OrganizationAPIKey",
        primaryjoin="Tenant.clerk_org_id == OrganizationAPIKey.clerk_org_id",
        foreign_keys="[OrganizationAPIKey.clerk_org_id]", # Explicitly define foreign keys for the join
        back_populates="tenant_organization",
        cascade="all, delete-orphan",
        lazy="selectin" # Using selectin loading for potentially multiple keys per org
    )


class APIKey(Base):
    """
    API key for tenant authentication.

    Each tenant can have multiple API keys with different labels.
    """

    # Table name
    __tablename__ = "api_keys"

    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    tenant_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        nullable=False,
    )
    label: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    key_hash: Mapped[str] = mapped_column(String, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    last_used_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    revoked: Mapped[bool] = mapped_column(Boolean, default=False)
    expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )

    # Relationships
    tenant: Mapped["Tenant"] = relationship("Tenant", back_populates="api_keys")

    # Indexes
    __table_args__ = (
        Index("ix_api_keys_key_hash", key_hash, unique=True),
        Index("ix_api_keys_tenant_id", tenant_id),
    )


class TenantSettings(Base):
    """
    Tenant-specific settings and configuration.

    Stores settings that can be customized per tenant, such as
    intent thresholds, LLM preferences, and rate limits.
    """

    # Table name
    __tablename__ = "tenant_settings"

    # Columns
    tenant_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        primary_key=True,
    )
    tier1_threshold: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    tier2_threshold: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    llm_model: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    embedding_model: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    max_requests_per_min: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    max_tokens_per_month: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    settings: Mapped[Optional[Dict[str, str]]] = mapped_column(JSONB, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
    )


class OrganizationAPIKey(Base):
    """
    API key for Clerk organizations, replacing the old tenant-specific APIKey.
    """
    __tablename__ = "organization_api_keys"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid()
    )
    clerk_org_id: Mapped[str] = mapped_column(String, nullable=False, index=True)
    name: Mapped[str] = mapped_column(String, nullable=False)
    key_hash: Mapped[str] = mapped_column(String, nullable=False, unique=True)
    key_prefix: Mapped[str] = mapped_column(String(8), nullable=False, index=True)
    permissions: Mapped[Optional[Dict]] = mapped_column(JSONB, nullable=True) # Using Dict for JSONB
    created_by: Mapped[str] = mapped_column(String, nullable=False) # Clerk User ID
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    last_used_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    revoked: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Relationship to Tenant (Organization)
    # This assumes Tenant.clerk_org_id is unique and populated.
    tenant_organization: Mapped["Tenant"] = relationship(
        "Tenant",
        primaryjoin="OrganizationAPIKey.clerk_org_id == Tenant.clerk_org_id",
        foreign_keys="[OrganizationAPIKey.clerk_org_id]", # Explicitly define foreign keys for the join
        back_populates="organization_api_keys",
        lazy="joined" # Or select, depending on access patterns
    )

    __table_args__ = (
        Index("ix_organization_api_keys_clerk_org_id_name", "clerk_org_id", "name"),
        # Potentially a ForeignKeyConstraint to tenants.clerk_org_id if it's always guaranteed.
    )
