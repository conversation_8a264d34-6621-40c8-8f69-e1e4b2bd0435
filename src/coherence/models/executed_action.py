"""
Models for tracking executed actions and conversation states.

This module defines the persistence layer for tracking actions
executed during conversations, including parameter states and documentation requests.
"""

from enum import Enum
from typing import Dict, Optional, TYPE_CHECKING
from datetime import datetime

from sqlalchemy import Column, DateTime, ForeignKey, Index, String, Text, Boolean, JSON, Integer
from sqlalchemy.dialects.postgresql import ENUM, UUID
from sqlalchemy.sql import text

from src.coherence.db.base_class import Base

if TYPE_CHECKING:
    pass


class ActionType(str, Enum):
    """Type of action executed."""
    
    API_CALL = "api_call"  # Action that executes API calls
    DOCUMENTATION = "documentation"  # Documentation retrieval action
    SYSTEM = "system"  # System actions (e.g., error handling)


class ParameterCollectionState(str, Enum):
    """State of parameter collection for an action."""
    
    NOT_STARTED = "not_started"  # No parameters collected yet
    IN_PROGRESS = "in_progress"  # Some parameters collected, more needed
    COMPLETED = "completed"  # All required parameters collected
    FAILED = "failed"  # Parameter collection failed


class ExecutedAction(Base):
    """
    Store for actions executed during conversations.
    
    Tracks the actions taken during user conversations, including
    parameter collection state, API calls made, and responses generated.
    """

    __tablename__ = "executed_actions"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=text("gen_random_uuid()"))

    # Foreign keys
    tenant_id = Column(
        UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False
    )
    user_id = Column(UUID(as_uuid=True), nullable=False)
    template_id = Column(
        UUID(as_uuid=True),
        ForeignKey("templates.id", ondelete="SET NULL"),
        nullable=True,
    )

    # Action identification
    conversation_id = Column(String, nullable=False)
    intent_name = Column(String, nullable=False)
    action_type = Column(
        ENUM(ActionType, name="action_type"),
        nullable=False,
    )

    # Parameter tracking
    parameter_state = Column(
        ENUM(ParameterCollectionState, name="parameter_collection_state"),
        default=ParameterCollectionState.NOT_STARTED,
        nullable=False,
    )
    parameters_collected = Column(JSON, nullable=True)  # Parameters collected so far
    parameters_missing = Column(JSON, nullable=True)  # Parameters still needed
    parameter_collection_rounds = Column(Integer, default=0)  # Number of collection attempts

    # Documentation-specific fields
    is_documentation_request = Column(Boolean, default=False)
    documentation_endpoint = Column(String, nullable=True)  # API endpoint being documented
    documentation_method = Column(String, nullable=True)  # HTTP method being documented
    documentation_response_format = Column(String, nullable=True)  # Format of doc response

    # Execution details
    api_responses = Column(JSON, nullable=True)  # Responses from API calls
    final_response = Column(Text, nullable=True)  # Final response to user
    error_message = Column(Text, nullable=True)  # Error message if action failed
    execution_time_ms = Column(Integer, nullable=True)  # Time taken to execute

    # Tracking
    created_at = Column(DateTime(timezone=True), server_default=text("now()"))
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Indexes
    __table_args__ = (
        Index("ix_executed_actions_conversation_id", conversation_id),
        Index("ix_executed_actions_tenant_id", tenant_id),
        Index("ix_executed_actions_user_id", user_id),
        Index("ix_executed_actions_created_at", created_at),
    )

    def __repr__(self):
        return f"<ExecutedAction(id={self.id}, intent='{self.intent_name}', type='{self.action_type}', state='{self.parameter_state}')>"