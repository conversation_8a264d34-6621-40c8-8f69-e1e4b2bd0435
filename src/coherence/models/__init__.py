"""
Import all models here to ensure they're discovered by SQLAlchemy.
"""

# Import models to make them available when importing from models package
from .admin_workflow import AdminWorkflow
from .audit_log import AuditLog
from .generated_action import GeneratedAction
from .executed_action import ExecutedAction
from .integration import (
    APIIntegration, 
    APIAuthConfig, 
    APIEndpoint, 
    APIRateLimit, 
    APIOriginalSpec,
    IntegrationStatus,
    SpecFormat,
    AuthType
)
from .organization import Organization
from .system_admin import SystemAdmin, SystemAdminAPIKey
from .template import (
    DependencyType,
    Template,
    TemplateCategory,
    TemplateDependency,
    TemplateScope,
    TemplateTest,
    TemplateVersion,
)
from .tenant import APIKey, OrganizationAPIKey, Tenant, TenantSettings
from .user import User
from .workflow import Workflow, WorkflowStep
from .workflow_status import WorkflowStatus

# Note: llm_schemas.py is assumed to contain Pydantic schemas or other non-SQLAlchemy models
# and is not imported here. If it contains SQLAlchemy models, they should be added.

__all__ = [
    "APIKey",
    "OrganizationAPIKey",
    "Tenant",
    "TenantSettings",
    "DependencyType",
    "Template",
    "TemplateCategory",
    "TemplateDependency",
    "TemplateScope",
    "TemplateTest",
    "TemplateVersion",
    "AuditLog",
    "GeneratedAction",
    "ExecutedAction",
    "APIIntegration",
    "APIAuthConfig",
    "APIEndpoint",
    "APIRateLimit",
    "APIOriginalSpec",
    "IntegrationStatus",
    "SpecFormat",
    "AuthType",
    "WorkflowStatus",
    "Workflow",
    "WorkflowStep",
    "AdminWorkflow",
    "User",
    "Organization",
    "SystemAdmin",
    "SystemAdminAPIKey",
]
