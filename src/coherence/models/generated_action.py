"""
Models for storing and tracking generated action classes.

This module defines the persistence layer for storing code
generated from OpenAPI specifications.
"""

from enum import Enum
from typing import TYPE_CHECKING

from sqlalchemy import Column, DateTime, ForeignKey, Index, String, Text
from sqlalchemy.dialects.postgresql import ENUM, UUID
from sqlalchemy.sql import text

from src.coherence.db.base_class import Base

if TYPE_CHECKING:
    pass


class ValidationStatus(str, Enum):
    """Status of code validation."""

    PENDING = "pending"  # Code has not been validated yet
    VALID = "valid"  # Code has been successfully validated
    INVALID = "invalid"  # Code has validation errors
    OUTDATED = "outdated"  # Code is valid but the endpoint has changed


class GeneratedAction(Base):
    """
    Store for action classes generated from OpenAPI specs.

    Persists the Python code generated for API endpoints and
    tracks its validation status and version.
    """

    __tablename__ = "generated_actions"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=text("gen_random_uuid()"))

    # Foreign keys
    endpoint_id = Column(
        UUID(as_uuid=True),
        ForeignKey("api_endpoints.id", ondelete="CASCADE"),
        nullable=False,
    )
    tenant_id = Column(
        UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False
    )

    # Metadata
    class_name = Column(String, nullable=False)
    version = Column(String, nullable=False)  # Semantic version or hash
    generated_at = Column(DateTime(timezone=True), server_default=text("now()"))

    # Validation information
    validation_status = Column(
        ENUM(
            ValidationStatus.PENDING.value,
            ValidationStatus.VALID.value,
            ValidationStatus.INVALID.value,
            ValidationStatus.OUTDATED.value,
            name="validation_status",
            create_type=False,  # Use existing type created in migration
        ),
        default=ValidationStatus.PENDING.value,
        nullable=False,
    )
    validation_message = Column(Text, nullable=True)
    last_validated_at = Column(DateTime(timezone=True), nullable=True)

    # Action code
    code = Column(Text, nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=text("now()"))
    updated_at = Column(
        DateTime(timezone=True), server_default=text("now()"), onupdate=text("now()")
    )

    # Indexes
    __table_args__ = (
        Index(
            "ix_generated_actions_endpoint_id_version",
            endpoint_id,
            version,
            unique=True,
        ),
        Index("ix_generated_actions_tenant_id", tenant_id),
    )

    def __repr__(self):
        return f"<GeneratedAction(id={self.id}, class_name='{self.class_name}', validation_status='{self.validation_status}')>"
