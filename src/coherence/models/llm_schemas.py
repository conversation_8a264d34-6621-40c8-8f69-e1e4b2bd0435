from typing import Any, List, Optional

from pydantic import BaseModel, Field

from ..schemas.mixins import TenantMixin


class ExtractedParameter(TenantMixin, BaseModel):
    """Represents a single parameter extracted by the LLM for an action."""

    name: str = Field(
        description="The name of the parameter as expected by the action/function."
    )
    value: Any = Field(
        description="The value of the parameter extracted from the user's input."
    )
    parameter_type: Optional[str] = Field(
        None,
        description="Optional: The data type of the parameter (e.g., 'string', 'integer', 'boolean').",
    )


class ResolvedAction(TenantMixin, BaseModel):
    """
    Represents an action successfully resolved by the LLM,
    including the action name and all extracted parameters.
    """

    action_name: str = Field(
        description="The unique identifier or name of the action to be executed."
    )
    parameters: Optional[List[ExtractedParameter]] = Field(
        default_factory=list,
        description="A list of parameters required for the action, with their extracted values.",
    )
    # Optional fields for richer information:
    # confidence_score: Optional[float] = Field(None, description="LLM's confidence in the extracted action and parameters (0.0 to 1.0).")
    # original_query: Optional[str] = Field(None, description="The user query that led to this resolved action, for logging/debugging.")
    # reasoning: Optional[str] = Field(None, description="Brief explanation from the LLM on how it arrived at this action (if feasible).")
