from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.sql import func

from ..db.base_class import Base


class AdminWorkflow(Base):
    __tablename__ = "admin_workflows"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    is_enabled = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Foreign Keys
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    updated_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    organization_id = Column(Integer, Foreign<PERSON>ey("organizations.id"), nullable=True, index=True)

    # Relationships
    # created_by = relationship("User", foreign_keys=[created_by_id]) # Keep relationships commented for now if User/Organization models are not fully ready/imported
    # updated_by = relationship("User", foreign_keys=[updated_by_id])
    # organization = relationship("Organization", back_populates="admin_workflows") 

    # Placeholder for workflow definition (e.g., JSON or link to another table)
    # definition = Column(JSON, nullable=True) # If storing definition directly

    def __repr__(self):
        return f"<AdminWorkflow(id={self.id}, name=\'{self.name}\')>"

# Ensure your User and Organization models are defined.
# For example, in models/user.py:
# class User(Base):
#     __tablename__ = "users"
#     id = Column(Integer, primary_key=True, index=True) # or UUID
#     # ... other fields
#
# In models/organization.py:
# class Organization(Base):
#     __tablename__ = "organizations"
#     id = Column(Integer, primary_key=True, index=True)
#     # admin_workflows = relationship("AdminWorkflow", back_populates="organization") # If using backref
#     # ... other fields

# Remember to add this model to your alembic environment and generate a migration.
# In your alembic/env.py, ensure the model is imported or its metadata is known:
# from app.models.admin_workflow import AdminWorkflow 
# target_metadata = Base.metadata 