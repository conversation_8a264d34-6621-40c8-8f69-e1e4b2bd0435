"""
Models for API integrations via OpenAPI specifications.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING, Any, Dict, List, Optional

from sqlalchemy import (
    Boolean,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    LargeBinary,
    String,
    Text,
    func,
)
from sqlalchemy.dialects.postgresql import ENUM, JSONB, UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.coherence.db.base_class import Base

if TYPE_CHECKING:
    pass


# Define enum types for integration status and auth type
class IntegrationStatus(str, Enum):
    """Enum for API integration status values."""

    DRAFT = "draft"  # Initial setup, not yet configured
    ACTIVE = "active"  # Active and ready to use
    DISABLED = "disabled"  # Disabled by admin or due to errors


class SpecFormat(str, Enum):
    """Enum for API specification format values."""
    
    OPENAPI = "openapi"  # OpenAPI specification
    FAPI = "fapi"        # Financial-grade API specification
    BAPI = "bapi"        # Business API specification


class AuthType(str, Enum):
    """Enum for API authentication type values."""

    API_KEY = "api_key"  # API key authentication
    OAUTH2 = "oauth2"  # OAuth 2.0 authentication
    BEARER = "bearer"  # Bearer token authentication
    BASIC = "basic"  # Basic HTTP authentication
    CUSTOM = "custom"  # Custom authentication


class APIIntegration(Base):
    """
    Integration with an external API via OpenAPI specification.

    An API integration connects Coherence to external services
    by generating actions from API endpoints.
    """

    __tablename__ = "api_integrations"

    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    tenant_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        nullable=False,
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    version: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    openapi_spec: Mapped[Dict[str, object]] = mapped_column(JSONB, nullable=False)
    base_url: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
    )
    status: Mapped[IntegrationStatus] = mapped_column(
        ENUM("draft", "active", "disabled", name="integration_status"),
        default="draft",
        nullable=False,
    )
    spec_format: Mapped[SpecFormat] = mapped_column(
        ENUM("openapi", "fapi", "bapi", name="spec_format"),
        default="openapi",
        nullable=False,
    )

    # Relationships
    auth_config: Mapped[Optional["APIAuthConfig"]] = relationship(
        "APIAuthConfig",
        back_populates="integration",
        uselist=False,
        cascade="all, delete-orphan",
    )
    endpoints: Mapped[List["APIEndpoint"]] = relationship(
        "APIEndpoint",
        back_populates="integration",
        cascade="all, delete-orphan",
    )
    original_spec: Mapped[Optional["APIOriginalSpec"]] = relationship(
        "APIOriginalSpec",
        back_populates="integration",
        uselist=False,
        cascade="all, delete-orphan",
    )

    # Indexes
    __table_args__ = (
        Index("ix_api_integrations_tenant_id_name", tenant_id, name, unique=True),
    )


class APIAuthConfig(Base):
    """
    Authentication configuration for an API integration.

    Stores credentials and auth settings for connecting to
    external APIs securely.
    """

    __tablename__ = "api_auth_configs"

    # Columns
    integration_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("api_integrations.id", ondelete="CASCADE"),
        primary_key=True,
    )
    auth_type: Mapped[AuthType] = mapped_column(
        ENUM(
            "api_key",
            "oauth2",
            "bearer",
            "basic",
            "custom",
            name="auth_type",
        ),
        nullable=False,
    )
    credentials: Mapped[Optional[Dict[str, object]]] = mapped_column(
        JSONB, nullable=True
    )  # Encrypted in database
    scopes: Mapped[Optional[List[str]]] = mapped_column(JSONB, nullable=True)
    refresh_token: Mapped[Optional[str]] = mapped_column(
        String, nullable=True
    )  # Encrypted in database
    expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    encryption_key_id: Mapped[Optional[str]] = mapped_column(
        String, nullable=True
    )  # ID of encryption key used
    encryption_nonce: Mapped[Optional[bytes]] = mapped_column(
        LargeBinary, nullable=True
    )  # Nonce for encryption

    # Relationships
    integration: Mapped["APIIntegration"] = relationship(
        "APIIntegration", back_populates="auth_config"
    )


class APIEndpoint(Base):
    """
    Endpoint from an API integration mapped to an intent.

    Represents a single API operation that can be invoked
    as an action in response to a user intent.
    """

    __tablename__ = "api_endpoints"

    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    integration_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("api_integrations.id", ondelete="CASCADE"),
        nullable=False,
    )
    path: Mapped[str] = mapped_column(String, nullable=False)
    method: Mapped[str] = mapped_column(String, nullable=False)
    operation_id: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    action_class_name: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    intent_id: Mapped[Optional[str]] = mapped_column(
        String, nullable=True
    )  # Link to intent name
    intent_name: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    intent_description: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    intent_examples: Mapped[Optional[List[str]]] = mapped_column(JSONB, nullable=True)
    intent_parameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSONB, nullable=True)
    intent_required_fields: Mapped[Optional[List[str]]] = mapped_column(JSONB, nullable=True)
    intent_generated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    intent_generation_status: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    enabled: Mapped[bool] = mapped_column(Boolean, default=True)
    
    # New fields from OpenAPI spec
    summary: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    description: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    tags: Mapped[Optional[List[str]]] = mapped_column(JSONB, nullable=True)
    deprecated: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # OpenAPI specification excerpt for this endpoint
    openapi_snippet: Mapped[Optional[Dict[str, object]]] = mapped_column(JSONB, nullable=True)

    # Relationships
    integration: Mapped["APIIntegration"] = relationship(
        "APIIntegration", back_populates="endpoints"
    )
    rate_limits: Mapped[List["APIRateLimit"]] = relationship(
        "APIRateLimit",
        back_populates="endpoint",
        cascade="all, delete-orphan",
    )

    # Indexes
    __table_args__ = (
        Index(
            "ix_api_endpoints_integration_id_path_method",
            integration_id,
            path,
            method,
            unique=True,
        ),
    )


class APIRateLimit(Base):
    """
    Rate limiting configuration for API endpoints.

    Defines limits on how frequently an endpoint can be called
    to prevent abuse and respect API provider limits.
    """

    __tablename__ = "api_rate_limits"

    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    endpoint_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("api_endpoints.id", ondelete="CASCADE"),
        nullable=False,
    )
    requests_per_min: Mapped[int] = mapped_column(Integer, nullable=False, default=60)
    burst_size: Mapped[int] = mapped_column(Integer, nullable=False, default=5)
    cooldown_sec: Mapped[int] = mapped_column(Integer, nullable=False, default=60)

    # Relationships
    endpoint: Mapped["APIEndpoint"] = relationship(
        "APIEndpoint", back_populates="rate_limits"
    )


class APIOriginalSpec(Base):
    """
    Original API specification for non-OpenAPI formats.
    
    Stores the original specification content before conversion to OpenAPI,
    allowing for format-specific features and future improvements.
    """
    
    __tablename__ = "api_original_specs"
    
    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    integration_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("api_integrations.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
    )
    format: Mapped[str] = mapped_column(String, nullable=False)
    content: Mapped[Dict[str, object]] = mapped_column(JSONB, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    
    # Relationships
    integration: Mapped["APIIntegration"] = relationship(
        "APIIntegration", back_populates="original_spec"
    )
