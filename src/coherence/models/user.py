from sqlalchemy import Column, DateTime, Integer, String
from sqlalchemy.sql import func

from ..db.base_class import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True) # Primary key as Integer
    clerk_user_id = Column(String, unique=True, index=True, nullable=True) # Clerk User ID
    email = Column(String, unique=True, index=True, nullable=True) 
    # Add other user-specific fields here, e.g., name, avatar_url, etc.
    # For now, keeping it minimal.

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    def __repr__(self):
        return f"<User(id={self.id}, clerk_user_id='{self.clerk_user_id}')>" 