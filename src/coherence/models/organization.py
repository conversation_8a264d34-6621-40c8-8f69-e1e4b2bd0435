from sqlalchemy import Column, DateTime, Integer, String
from sqlalchemy.sql import func

from ..db.base_class import Base


class Organization(Base):
    __tablename__ = "organizations"

    id = Column(Integer, primary_key=True, index=True) # Primary key as Integer
    clerk_org_id = Column(String, unique=True, index=True, nullable=True) # Clerk Organization ID
    name = Column(String, nullable=False, index=True)
    # Add other organization-specific fields here.

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # If AdminWorkflow has a back-populating relationship:
    # admin_workflows = relationship("AdminWorkflow", back_populates="organization")

    def __repr__(self):
        return f"<Organization(id={self.id}, name='{self.name}')>" 