"""
Audit log model for system-wide event tracking
"""
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, Column, ForeignKey, Text
from sqlalchemy.dialects.postgresql import JSON<PERSON>, UUID
from sqlalchemy.sql import text

from src.coherence.db.base_class import Base


class AuditLog(Base):
    """
    Audit log for tracking system events.

    Records actions taken within the system with associated metadata.
    Enables compliance, security monitoring, and troubleshooting.
    """

    __tablename__ = "audit_log"
    __table_args__ = {"extend_existing": True}

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=text("gen_random_uuid()"))

    # Required fields
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    action = Column(Text, nullable=False)

    # Optional fields
    user_id = Column(UUID(as_uuid=True), nullable=True)
    timestamp = Column(TIMESTAMP(timezone=True), server_default=text("now()"))
    details = Column(JSONB)

    def __repr__(self):
        return f"<AuditLog(id={self.id}, action='{self.action}')>"
