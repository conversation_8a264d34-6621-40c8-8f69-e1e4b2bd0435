import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Foreign<PERSON>ey, String, func
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship

from src.coherence.db.base import Base  # Assuming Base is in coherence.db.base


class SystemAdmin(Base):
    __tablename__ = "system_admins"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    clerk_user_id = Column(String, unique=True, nullable=False)
    created_at = Column(DateTime, server_default=func.now(), nullable=False)
    created_by = Column(String, nullable=False)  # Stores the clerk_user_id of the creator

    # Relationship to API keys - cascade will automatically delete related API keys when admin is deleted
    api_keys = relationship("SystemAdminAPIKey", back_populates="system_admin", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<SystemAdmin(id={self.id}, clerk_user_id='{self.clerk_user_id}')>"


class SystemAdminAPIKey(Base):
    __tablename__ = "system_admin_api_keys"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    key_hash = Column(String, unique=True, nullable=False, index=True)
    name = Column(String, nullable=False)
    permissions = Column(JSONB, nullable=True)  # For potential future granular permissions
    created_by = Column(String, nullable=False)  # clerk_user_id of the SystemAdmin creator
    created_at = Column(DateTime, server_default=func.now(), nullable=False)
    last_used_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)
    revoked = Column(Boolean, default=False, nullable=False)

    # Foreign key to SystemAdmin
    system_admin_id = Column(UUID(as_uuid=True), ForeignKey("system_admins.id", ondelete="CASCADE"), nullable=False)
    # Relationship back to SystemAdmin
    system_admin = relationship("SystemAdmin", back_populates="api_keys")

    def __repr__(self):
        return f"<SystemAdminAPIKey(id={self.id}, name='{self.name}', revoked={self.revoked})>"