"""
Workflow status model for tracking asynchronous operations
"""
from sqlalchemy import <PERSON><PERSON><PERSON>TAMP, Column, Float, ForeignKey, Text
from sqlalchemy.dialects.postgresql import JSON<PERSON>, UUID
from sqlalchemy.sql import text

from src.coherence.db.base_class import Base


class WorkflowStatus(Base):
    """
    Status tracker for asynchronous workflows.

    Provides a way to monitor the progress and state of long-running
    operations that span multiple steps or services.
    """

    __tablename__ = "workflow_status"
    __table_args__ = {"extend_existing": True}

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=text("gen_random_uuid()"))

    # Required fields
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    status = Column(Text, nullable=False)

    # Optional fields
    progress = Column(Float)
    current_step = Column(Text)
    result = Column(JSONB)
    updated_at = Column(TIMESTAMP(timezone=True), server_default=text("now()"))

    def __repr__(self):
        return f"<WorkflowStatus(id={self.id}, status='{self.status}', progress={self.progress})>"
