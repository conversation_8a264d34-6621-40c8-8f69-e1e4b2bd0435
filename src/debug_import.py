"""
Focused debug script to test the specific import that's failing.
"""

import importlib.util
import os
import sys


def check_module_exists(module_path):
    """Check if a module file exists at the given path."""
    print(f"Checking if module exists: {module_path}")
    exists = os.path.exists(module_path)
    print(f"  Exists: {exists}")
    return exists


def main():
    print("Python Path (sys.path):")
    for path in sys.path:
        print(f"  - {path}")

    print("\nCurrent Working Directory:")
    print(f"  {os.getcwd()}")

    # Check if the physical files exist
    base_path = "/app/src/coherence/core/llm/base.py"
    factory_path = "/app/src/coherence/core/llm/factory.py"

    check_module_exists(base_path)
    check_module_exists(factory_path)

    # Try to load the module directly without importing
    print("\nTrying to load modules directly:")

    try:
        spec = importlib.util.spec_from_file_location("base", base_path)
        importlib.util.module_from_spec(spec)
        print("✅ Created module object for base.py")
    except Exception as e:
        print(f"❌ Failed to create module for base.py: {e}")

    try:
        spec = importlib.util.spec_from_file_location("factory", factory_path)
        importlib.util.module_from_spec(spec)
        print("✅ Created module object for factory.py")

        # Examine the source code of factory.py to see the imports
        with open(factory_path, "r") as f:
            print("\nImport statements in factory.py:")
            for line in f:
                if line.strip().startswith("from") or line.strip().startswith("import"):
                    print(f"  {line.strip()}")
    except Exception as e:
        print(f"❌ Failed to create module for factory.py: {e}")


if __name__ == "__main__":
    main()
