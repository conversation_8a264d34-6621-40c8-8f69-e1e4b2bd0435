"""
Detailed debug script to verify specific imports that are failing.
"""

import importlib
import os
import sys


def test_import(import_path):
    """Test if an import works and print the result."""
    try:
        module = importlib.import_module(import_path)
        print(f"✅ 'import {import_path}' succeeded")
        return module
    except ImportError as e:
        print(f"❌ 'import {import_path}' failed: {e}")
        return None


def main():
    print("Python Path (sys.path):")
    for path in sys.path:
        print(f"  - {path}")

    print("\nCurrent Working Directory:")
    print(f"  {os.getcwd()}")

    print("\nTrying specific imports from the error trace:")

    # Test the imports in the order they appear in the error trace
    test_import("src.coherence.main")
    test_import("src.coherence.api.v1.api")
    test_import("src.coherence.api.v1.endpoints")
    test_import("src.coherence.intent_pipeline.orchestrator")
    test_import("src.coherence.core.llm.factory")

    # Test the specific import that's failing
    test_import("coherence.core.llm.base")

    # Test alternative import paths
    test_import("src.coherence.core.llm.base")

    # Check if the module file exists
    base_path = "/app/src/coherence/core/llm/base.py"
    print(f"\nChecking if file exists: {base_path}")
    print(f"  Exists: {os.path.exists(base_path)}")

    # Print the directory contents
    llm_dir = "/app/src/coherence/core/llm"
    print(f"\nContents of directory: {llm_dir}")
    try:
        files = os.listdir(llm_dir)
        for file in files:
            print(f"  - {file}")
    except Exception as e:
        print(f"  Error listing directory: {e}")


if __name__ == "__main__":
    main()
