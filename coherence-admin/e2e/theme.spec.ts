import { test, expect } from '@playwright/test';

test('Theme displays properly in dark and light modes', async ({ page }) => {
  // Visit the home page
  await page.goto('/');
  
  // Take a screenshot in default dark mode
  await page.screenshot({ path: 'e2e-screenshots/dark-mode.png' });

  // Find and click the theme toggle
  const themeButton = page.getByRole('button', { name: 'Toggle theme' });
  await themeButton.click();
  
  // Take a screenshot in light mode
  await page.screenshot({ path: 'e2e-screenshots/light-mode.png' });
  
  // Verify the theme toggle still exists after changing modes
  await expect(themeButton).toBeVisible();
});