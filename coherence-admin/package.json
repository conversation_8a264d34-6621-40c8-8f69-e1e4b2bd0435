{"name": "coherence-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@clerk/nextjs": "^6.19.2", "@hookform/resolvers": "^5.0.1", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "js-yaml": "^4.1.0", "lucide-react": "^0.509.0", "monaco-editor": "^0.52.2", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-markdown": "^10.1.0", "swr": "^2.3.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@clerk/types": "^4.58.0", "@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.0", "@testing-library/react-hooks": "^8.0.1", "@types/jest": "^29.5.12", "@types/js-yaml": "^4.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "playwright": "^1.42.1", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}