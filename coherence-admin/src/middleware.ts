import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

const isPublicRoute = createRouteMatcher([
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/api/auth/me',
  '/api/auth/session-info',
  '/api/debug-token', // Add debug token endpoint
  // Add any other genuinely public routes here
]);

const isOrgSelectionRoute = createRouteMatcher([
  '/org-selection',
]);

export default clerkMiddleware(async (auth, req) => {
  if (!isPublicRoute(req)) {
    // If the route is not public, protect it.
    auth.protect();
    
    // After protection, check if user has an organization selected
    const { orgId } = await auth();
    
    // If no organization is selected and not on org selection page, redirect there
    if (!orgId && !isOrgSelectionRoute(req)) {
      return NextResponse.redirect(new URL('/org-selection', req.url));
    }
  }
});

export const config = {
  // This matcher is a common default that Clerk recommends.
  // It protects all routes including api/trpc routes.
  // See https://clerk.com/docs/references/nextjs/clerk-middleware for more details.
  matcher: [
    '/((?!.+\.[\w]+$|_next).*)', // Matches all routes except static files and _next internal routes
    '/', // Match the root
    '/(api|trpc)(.*)', // Match API routes
  ],
};