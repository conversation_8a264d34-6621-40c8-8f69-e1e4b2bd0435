'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@clerk/nextjs';
import useSWR from 'swr';

// Updated type: An array of permission strings
type CoherencePermissions = string[];

// Define custom error type with status and info properties
interface ApiError extends Error {
  status?: number;
  info?: Record<string, unknown>;
}

// Window extension for development tools and for token refresh timeout
interface CustomWindow extends Window {
  adminSessionData?: Record<string, unknown>;
  _tokenRefreshTimeout?: NodeJS.Timeout;
}

// Actor with public metadata type
interface ActorWithMetadata {
  publicMetadata?: {
    isSystemAdmin?: boolean;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

interface OrganizationDetails {
  id: string; // Assuming id is always present if org exists
  name?: string;
  role?: string;
  slug?: string | null;
  public_metadata?: Record<string, unknown>;
  // Add other organization-specific fields if needed
  [key: string]: unknown; // Prefer unknown over any
}

interface TenantDetails {
  id: string; // Assuming id is always present if tenant exists
  name?: string;
  // other relevant tenant fields
}

interface MeResponseData {
  org: OrganizationDetails | null;
  tenant: TenantDetails | null;
  permissions: CoherencePermissions | null;
}

interface AdminSession {
  userId: string | null;
  sessionId: string | null;
  actor: Record<string, unknown> | null;
  token: string | null;

  // Data from /api/auth/me
  organization: OrganizationDetails | null;
  tenant: TenantDetails | null;
  permissions: CoherencePermissions | null;

  // SWR states & derived states
  isLoading: boolean;
  error: Error | null;
  isAuthenticated: boolean;
}

const defaultSessionValue: AdminSession = {
  userId: null,
  sessionId: null,
  actor: null,
  token: null,
  organization: null,
  tenant: null,
  permissions: null,
  isLoading: true, // Initially loading until Clerk and SWR resolve
  error: null,
  isAuthenticated: false,
};

const AdminSessionContext = createContext<AdminSession>(defaultSessionValue);

export const useAdminSession = () => {
  const context = useContext(AdminSessionContext);
  if (context === undefined) {
    throw new Error('useAdminSession must be used within an AdminSessionProvider');
  }
  return context;
};

interface AdminSessionProviderProps {
  children: ReactNode;
}

export const AdminSessionProvider = ({ children }: AdminSessionProviderProps) => {
  const { isSignedIn, userId, sessionId, getToken, actor, isLoaded: isClerkLoaded } = useAuth();
  const [authToken, setAuthToken] = useState<string | null>(null);

  useEffect(() => {
    if (isClerkLoaded && isSignedIn && getToken) {
      // Function to decode JWT and get expiry time
      const getTokenExpiryTime = (token: string): number | null => {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          return payload.exp ? payload.exp * 1000 : null; // Convert to milliseconds
        } catch (e) {
          console.error('[AdminSessionContext] Failed to decode token:', e);
          return null;
        }
      };

      // Function to refresh the token
      const refreshToken = async (force = false) => {
        console.log('[AdminSessionContext] Refreshing auth token');
        try {
          // Skip cache only when forced (for emergency refreshes)
          const token = await getToken({ skipCache: force });
          
          if (token) {
            setAuthToken(token);
            console.log('[AdminSessionContext] Token refreshed successfully');
            
            // Schedule next refresh at 75% of token lifetime
            const expiryTime = getTokenExpiryTime(token);
            if (expiryTime) {
              const now = Date.now();
              const lifetimeRemaining = expiryTime - now;
              // Refresh at 75% of remaining lifetime or at least 1 minute before expiry
              const refreshDelay = Math.max(lifetimeRemaining * 0.25, lifetimeRemaining - 60000);
              
              console.log(`[AdminSessionContext] Token expires in ${Math.round(lifetimeRemaining/1000)}s, next refresh in ${Math.round(refreshDelay/1000)}s`);
              
              // Clear any existing timeout and set a new one
              const customWindow = window as unknown as CustomWindow;
              if (customWindow._tokenRefreshTimeout) {
                clearTimeout(customWindow._tokenRefreshTimeout);
              }
              
              customWindow._tokenRefreshTimeout = setTimeout(() => refreshToken(), refreshDelay);
            } else {
              // Fallback to 2-minute refresh if we can't determine expiry
              console.log('[AdminSessionContext] Could not determine token expiry, using 2-minute refresh interval');
              const customWindow = window as unknown as CustomWindow;
              if (customWindow._tokenRefreshTimeout) {
                clearTimeout(customWindow._tokenRefreshTimeout);
              }
              customWindow._tokenRefreshTimeout = setTimeout(() => refreshToken(), 2 * 60 * 1000);
            }
          } else {
            console.warn('[AdminSessionContext] getToken returned null token');
            setAuthToken(null);
          }
        } catch (err) {
          console.error('[AdminSessionContext] Failed to refresh token:', err);
          setAuthToken(null);
          
          // Try again in 30 seconds on failure
          const customWindow = window as unknown as CustomWindow;
          if (customWindow._tokenRefreshTimeout) {
            clearTimeout(customWindow._tokenRefreshTimeout);
          }
          customWindow._tokenRefreshTimeout = setTimeout(() => refreshToken(true), 30 * 1000);
        }
      };
      
      // Initial token fetch - don't skip cache for first load
      refreshToken(false);
      
      // Also set up a backup refresh interval of 2 minutes
      // This helps ensure tokens are refreshed even if the dynamic scheduling fails
      const backupRefreshInterval = setInterval(() => {
        if (authToken) {
          const expiryTime = getTokenExpiryTime(authToken);
          const timeUntilExpiry = expiryTime ? expiryTime - Date.now() : null;
          
          if (timeUntilExpiry !== null) {
            // Log the time remaining until expiry in minutes and seconds
            console.log(`[AdminSessionContext] Backup check: Token expires in ${Math.floor(timeUntilExpiry/60000)}m ${Math.floor((timeUntilExpiry % 60000)/1000)}s`);
            
            // If less than 5 minutes remaining, trigger a refresh
            if (timeUntilExpiry < 5 * 60 * 1000) {
              console.log('[AdminSessionContext] Backup refresh triggered - token expiring soon');
              refreshToken(false);
            }
          } else {
            // If we can't determine expiry, refresh anyway to be safe
            console.log('[AdminSessionContext] Backup refresh triggered - unknown expiry');
            refreshToken(false);
          }
        }
      }, 2 * 60 * 1000);
      
      return () => {
        const customWindow = window as unknown as CustomWindow;
        if (customWindow._tokenRefreshTimeout) {
          clearTimeout(customWindow._tokenRefreshTimeout);
        }
        clearInterval(backupRefreshInterval);
      };
    } else if (isClerkLoaded && !isSignedIn) {
      setAuthToken(null);
    }
  }, [isClerkLoaded, isSignedIn, getToken, authToken]);

  const fetcher = async (url: string) => {
    console.log('[AdminSessionContext] Fetching:', url);
    
    if (!getToken) {
      throw new Error('getToken function not available');
    }
    
    // Try with template first, then fallback to default, and always skip cache
    let token = null;
    try {
      // Log environment details for debugging
      console.log('[AdminSessionContext] Environment:', {
        NODE_ENV: process.env.NODE_ENV,
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
        API_URL: process.env.API_URL, // Only accessible in Node.js context
        CLERK_SESSION_TOKEN_TEMPLATE: process.env.CLERK_SESSION_TOKEN_TEMPLATE
      });
      
      // Use the configured template name if available
      const templateName = process.env.CLERK_SESSION_TOKEN_TEMPLATE || 'coherence_session';
      console.log(`[AdminSessionContext] Attempting to get token with template: ${templateName}`);
      
      // Always skip cache to ensure we get a fresh token
      token = await getToken({ template: templateName, skipCache: true });
      console.log('[AdminSessionContext] Got token with template:', !!token);
    } catch (error) {
      console.log('[AdminSessionContext] Template token failed, trying default:', error);
    }
    
    if (!token) {
      try {
        // Always skip cache to ensure we get a fresh token
        token = await getToken({ skipCache: true });
        console.log('[AdminSessionContext] Got default token:', !!token);
      } catch (error) {
        console.error('[AdminSessionContext] Default token failed:', error);
      }
    }
    
    // If we still don't have a token, try one last time with different options
    if (!token) {
      try {
        // Last resort attempt to get a token
        token = await getToken();
        console.log('[AdminSessionContext] Last resort token attempt:', !!token);
      } catch (error) {
        console.error('[AdminSessionContext] All token attempts failed:', error);
      }
    }
    
    if (!token) {
      const authError = new Error('Authentication token not available for /api/auth/me');
      (authError as ApiError).status = 401;
      throw authError;
    }
    
    console.log('[AdminSessionContext] Making request with token (length):', token.length);
    
    // Log the first and last 10 characters of the token for debugging
    if (token.length > 20) {
      console.log(`[AdminSessionContext] Token preview: ${token.substring(0, 10)}...${token.substring(token.length - 10)}`);
    }
    
    // Explicitly include the Authorization header
    const res = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    console.log('[AdminSessionContext] Response status:', res.status);
    
    if (!res.ok) {
      const error = new Error(`Failed to fetch session data from ${url}`);
      try {
        const errorData = await res.json();
        (error as ApiError).info = errorData;
        console.error('[AdminSessionContext] Error response:', errorData);
      } catch { 
        console.error('[AdminSessionContext] Non-JSON error response');
      }
      (error as ApiError).status = res.status;
      throw error;
    }
    
    const data = await res.json();
    console.log('[AdminSessionContext] Successfully fetched data:', {
      hasOrg: !!data.org,
      hasTenant: !!data.tenant,
      hasPermissions: !!data.permissions
    });
    
    return data;
  };

  const { 
    data: meData, 
    error: swrError, 
    isLoading: isSWRFetchingMe 
  } = useSWR<MeResponseData>(
    isClerkLoaded && isSignedIn && userId ? '/api/auth/me' : null,
    fetcher,
    {
      shouldRetryOnError: false, // Important for 401/403/404 errors
      revalidateOnFocus: false, // Prevent constant revalidation
      dedupingInterval: 5000, // Dedupe requests within 5 seconds
    }
  );

  // Ensure all variables used here are booleans
  const isLoading: boolean = !isClerkLoaded || (!!isSignedIn && !!userId && !!isSWRFetchingMe);
  
  let determinedIsAuthenticated = false;
  if (!isLoading && isClerkLoaded && isSignedIn && !swrError && meData) {
  // For system admins, only require org.id (tenant can be null)
  if (meData.org?.id) {
    determinedIsAuthenticated = true;
    }
    // Log the authentication decision for debugging
    console.log('[AdminSessionContext] Authentication check:', {
      hasOrg: !!meData.org?.id,
      hasTenant: !!meData.tenant?.id,
      isAuthenticated: determinedIsAuthenticated,
      isSystemAdmin: meData.permissions?.includes?.('system:*'),
      orgId: meData.org?.id,
      tenantId: meData.tenant?.id
    });
  }

  const sessionContextValue: AdminSession = {
    userId: userId || null,
    sessionId: sessionId || null,
    actor: actor || null,
    token: authToken,

    organization: meData?.org || null,
    tenant: meData?.tenant || null,
    permissions: meData?.permissions || null,

    isLoading,
    error: swrError || null,
    isAuthenticated: determinedIsAuthenticated,
  };

  // Log key session data for devtools
  useEffect(() => {
    if (!isLoading) { // Only log when not actively loading initial state
      if (sessionContextValue.isAuthenticated && meData) {
        const devtoolData = {
          userId: sessionContextValue.userId,
          clerkActor: sessionContextValue.actor,
          organization: sessionContextValue.organization,
          tenant: sessionContextValue.tenant,
          permissions: sessionContextValue.permissions,
          isSystemAdmin: actor ? (actor as ActorWithMetadata).publicMetadata?.isSystemAdmin : undefined, // Example, adjust based on actual actor structure
          fullApiMeResponse: meData,
        };
        console.log('🔑 Admin Session Data (from /api/auth/me via SWR):', devtoolData);

        if (process.env.NODE_ENV !== 'production') {
          ((window as unknown) as CustomWindow).adminSessionData = devtoolData;
          console.log('ℹ️ Admin session data available as `window.adminSessionData`');
        }
      } else {
        console.log('🔑 Admin Session: Not authenticated or session data not available.');
        if (swrError) {
        console.error('SWR Error fetching /api/auth/me:', {
        message: swrError.message,
        status: (swrError as ApiError).status,
        info: (swrError as ApiError).info,
        });
          
        // Provide specific guidance based on error type
          if ((swrError as ApiError).status === 401) {
            console.log('💡 Suggestion: Check if you have selected an organization in Clerk');
            console.log('💡 Try visiting /org-selection to select an organization');
          } else if ((swrError as ApiError).status === 500) {
            console.log('💡 Suggestion: Check if the backend API is running and accessible');
            console.log('💡 Environment API_URL:', process.env.NEXT_PUBLIC_API_URL);
          }
        } else if (isClerkLoaded && isSignedIn && !meData && !isSWRFetchingMe) { 
            console.warn('Admin Session: Clerk signed in, but /api/auth/me data is missing (org or tenant might be null).');
            console.log('💡 Try refreshing the page or logging out and back in');
        }
        if (process.env.NODE_ENV !== 'production') {
          const customWindow = (window as unknown) as CustomWindow;
          if (customWindow.adminSessionData) {
            delete customWindow.adminSessionData;
            console.log('ℹ️ `window.adminSessionData` cleared.');
          }
        }
      }
    }
  }, [
    isLoading, 
    sessionContextValue.isAuthenticated, 
    meData, 
    swrError, 
    sessionContextValue.userId, 
    sessionContextValue.actor,
    sessionContextValue.organization,
    sessionContextValue.tenant,
    sessionContextValue.permissions,
    actor, // from useAuth, for isSystemAdmin check
    isClerkLoaded, // for logging context
    isSignedIn, // for logging context
    isSWRFetchingMe // for logging context
  ]);

  return (
    <AdminSessionContext.Provider value={sessionContextValue}>
      {children}
    </AdminSessionContext.Provider>
  );
};