// Debug utility to check Clerk JWT claims

// Declare <PERSON> types for TypeScript
declare global {
  interface Window {
    Clerk?: {
      session?: {
        getToken: (options?: { template?: string }) => Promise<string | null>;
      };
    };
  }
}

export async function debugClerkToken() {
  if (typeof window === 'undefined') return;
  
  try {
    // Get the token from <PERSON> with the specific template
    const token = await window.Clerk?.session?.getToken({ template: 'default' });
    
    if (!token) {
      console.log('No token available');
      return;
    }
    
    // Decode the JWT (just for debugging - never do this in production for validation!)
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.log('Invalid JWT format');
      return;
    }
    
    // Decode the payload
    const payload = JSON.parse(atob(parts[1]));
    
    console.log('JWT Claims:', payload);
    console.log('Organization ID:', payload.org_id);
    console.log('Organization Name:', payload.org_name);
    console.log('Organization Role:', payload.org_role);
    console.log('Organization Slug:', payload.org_slug);
    
    // Check if these are still template strings
    if (payload.org_id && payload.org_id.includes('{{')) {
      console.warn('⚠️ org_id is still a template string:', payload.org_id);
    }
    if (payload.org_name && payload.org_name.includes('{{')) {
      console.warn('⚠️ org_name is still a template string:', payload.org_name);
    }
    if (payload.org_role && payload.org_role.includes('{{')) {
      console.warn('⚠️ org_role is still a template string:', payload.org_role);
    }
    if (payload.org_slug && payload.org_slug.includes('{{')) {
      console.warn('⚠️ org_slug is still a template string:', payload.org_slug);
    }
    
  } catch (error) {
    console.error('Error debugging token:', error);
  }
}
