import { ThemeToggle } from "./ui/ThemeToggle";

export function CyberWaveDemo() {
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-4xl font-display text-cw-foreground">
          Cyber Wave Theme
        </h1>
        <ThemeToggle />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Color Palette Section */}
        <div className="bg-black/20 backdrop-blur-sm p-6 rounded-lg border border-cw-accent-left/30">
          <h2 className="text-2xl font-display mb-4 text-cw-accent-right">Color Palette</h2>
          
          <div className="grid grid-cols-2 gap-2">
            <div className="bg-cw-n-red p-3 rounded text-black font-mono">nRed</div>
            <div className="bg-cw-b-red p-3 rounded text-black font-mono">bRed</div>
            
            <div className="bg-cw-n-green p-3 rounded text-black font-mono">nGreen</div>
            <div className="bg-cw-b-green p-3 rounded text-black font-mono">bGreen</div>
            
            <div className="bg-cw-n-blue p-3 rounded text-black font-mono">nBlue</div>
            <div className="bg-cw-b-blue p-3 rounded text-black font-mono">bBlue</div>
            
            <div className="bg-cw-n-magenta p-3 rounded text-black font-mono">nMagenta</div>
            <div className="bg-cw-b-magenta p-3 rounded text-black font-mono">bMagenta</div>
          </div>
        </div>

        {/* Typography Section */}
        <div className="bg-black/20 backdrop-blur-sm p-6 rounded-lg border border-cw-accent-right/30">
          <h2 className="text-2xl font-display mb-4 text-cw-accent-left">Typography</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-sm text-cw-n-yellow mb-1 font-mono">Display Font (Orbitron)</h3>
              <p className="font-display text-xl">CYBER WAVE THEME</p>
            </div>
            
            <div>
              <h3 className="text-sm text-cw-n-yellow mb-1 font-mono">Mono Font (JetBrains Mono)</h3>
              <p className="font-mono text-xl">console.log("Hello World");</p>
            </div>
          </div>
        </div>

        {/* Gradients Section */}
        <div className="bg-black/20 backdrop-blur-sm p-6 rounded-lg border border-cw-accent-left/30">
          <h2 className="text-2xl font-display mb-4 text-cw-accent-right">Gradients</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-sm text-cw-n-yellow mb-1 font-mono">Background Gradient</h3>
              <div className="h-16 bg-gradient-cw rounded-md"></div>
            </div>
            
            <div>
              <h3 className="text-sm text-cw-n-yellow mb-1 font-mono">Accent Gradient</h3>
              <div className="h-16 bg-gradient-cw-accent rounded-md"></div>
            </div>
          </div>
        </div>

        {/* Animations Section */}
        <div className="bg-black/20 backdrop-blur-sm p-6 rounded-lg border border-cw-accent-right/30">
          <h2 className="text-2xl font-display mb-4 text-cw-accent-left">Animations</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-sm text-cw-n-yellow mb-1 font-mono">Pulse Accent</h3>
              <div className="h-12 bg-gradient-cw-accent rounded-md animate-pulse-accent flex items-center justify-center font-display">
                PULSING
              </div>
            </div>
            
            <div>
              <h3 className="text-sm text-cw-n-yellow mb-1 font-mono">Wave Slide</h3>
              <div className="h-12 bg-gradient-cw-accent rounded-md overflow-hidden">
                <div className="animate-wave-slide flex">
                  {Array.from({ length: 10 }).map((_, i) => (
                    <span key={i} className="font-display whitespace-nowrap mx-4">CYBER WAVE</span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}