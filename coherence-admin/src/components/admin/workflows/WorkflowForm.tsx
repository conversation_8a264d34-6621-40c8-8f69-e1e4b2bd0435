'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";

// Simplified step schema for now - will be expanded
/* // Commenting out unused schema
const workflowStepSchema = z.object({
  name: z.string().min(1, "Step name is required."),
  step_type: z.string(), // basic string for now
  // config: z.record(z.any()), // Too complex for initial form, handle later
  order: z.number().int().positive(),
});
*/

const workflowFormSchema = z.object({
  name: z.string().min(1, "Workflow name is required.").max(255, "Name cannot exceed 255 characters."),
  description: z.string().max(1000, "Description cannot exceed 1000 characters.").optional(),
  is_enabled: z.boolean().default(true).optional(),
  // steps: z.array(workflowStepSchema).default([]), // Placeholder - complex UI needed for this
});

export type WorkflowFormValues = z.infer<typeof workflowFormSchema>;

interface WorkflowFormProps {
  initialData?: Partial<WorkflowFormValues>;
  onSubmit: (values: WorkflowFormValues) => void;
  isLoading?: boolean;
}

const WorkflowForm: React.FC<WorkflowFormProps> = ({ initialData, onSubmit, isLoading }) => {
  const form = useForm<WorkflowFormValues>({
    resolver: zodResolver(workflowFormSchema),
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || undefined,
      is_enabled: initialData?.is_enabled ?? true,
      // steps: initialData?.steps || [],
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Workflow Name</FormLabel>
              <FormControl>
                <Input placeholder="E.g., Patient Onboarding" {...field} />
              </FormControl>
              <FormDescription>
                A clear and concise name for this workflow.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe the purpose of this workflow..."
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                An optional description of what this workflow does.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="is_enabled"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">
                  Enable Workflow
                </FormLabel>
                <FormDescription>
                  If disabled, this workflow will not be available for execution.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* Steps field (placeholder/simplified) will go here */}

        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : (initialData ? 'Save Changes' : 'Create Workflow')}
        </Button>
      </form>
    </Form>
  );
};

export default WorkflowForm; 