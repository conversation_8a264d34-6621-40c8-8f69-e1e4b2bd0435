'use client';

import React, { useState, useEffect, ReactNode } from 'react';
import { useAdminSession } from '@/context/AdminSessionContext';
import { Button } from '@/components/ui/button';
import { RefreshCcw } from 'lucide-react';

interface DataContainerProps<T> {
  children: (data: T | null) => ReactNode;
  fetchData: (token: string, orgId?: string) => Promise<T>;
  loadingComponent?: ReactNode;
  errorComponent?: (error: Error, retry: () => void) => ReactNode;
  dependencies?: any[];
  showRefreshButton?: boolean;
}

/**
 * A generic data container component that handles data fetching, loading states,
 * and token refresh issues throughout the admin site.
 * 
 * This component prevents constant data refetching when tokens are refreshed.
 */
export function DataContainer<T>({
  children,
  fetchData,
  loadingComponent = <div className="p-6 flex justify-center items-center min-h-[calc(100vh-theme(spacing.24))]"><p className="text-lg text-gray-600">Loading data...</p></div>,
  errorComponent,
  dependencies = [],
  showRefreshButton = true,
}: DataContainerProps<T>) {
  const adminSession = useAdminSession();
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [manualRefreshCounter, setManualRefreshCounter] = useState(0);

  // Define the refresh function outside useEffect so it can be passed to error component
  const refreshData = () => {
    setDataLoaded(false);
    setManualRefreshCounter(prev => prev + 1);
  };

  useEffect(() => {
    if (adminSession.isLoading) {
      return;
    }

    if (!adminSession.isAuthenticated) {
      setIsLoading(false);
      return;
    }

    // If data is already loaded and we're not forcing a refresh, don't fetch again
    if (dataLoaded && data && !adminSession.isLoading && manualRefreshCounter === 0) {
      return;
    }

    const loadData = async () => {
      if (!adminSession.token) {
        console.log("No token available, skipping data fetch");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const result = await fetchData(
          adminSession.token,
          adminSession.tenant?.id
        );
        setData(result);
        setDataLoaded(true);
        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err : new Error(String(err)));
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [
    adminSession.isLoading,
    adminSession.isAuthenticated,
    adminSession.token,
    adminSession.tenant?.id,
    dataLoaded,
    manualRefreshCounter,
    fetchData,
    data,
    ...dependencies
  ]);

  if (adminSession.isLoading) {
    return <>{loadingComponent}</>;
  }

  if (!adminSession.isAuthenticated) {
    return (
      <div className="p-6">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded" role="alert">
          <strong className="font-bold">Authentication Required: </strong>
          <span className="block sm:inline">Please log in to access this page.</span>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return <>{loadingComponent}</>;
  }

  if (error) {
    if (errorComponent) {
      return <>{errorComponent(error, refreshData)}</>;
    }
    
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error.message}</span>
        </div>
        <Button onClick={refreshData} variant="outline">
          <RefreshCcw className="mr-2 h-4 w-4" /> Retry
        </Button>
      </div>
    );
  }

  return (
    <>
      {showRefreshButton && (
        <div className="flex justify-end mb-4">
          <Button
            variant="outline"
            onClick={refreshData}
            className="text-blue-600"
            disabled={isLoading}
          >
            <RefreshCcw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Refreshing...' : 'Refresh Data'}
          </Button>
        </div>
      )}
      {children(data)}
    </>
  );
}