'use client';

import React, { ReactNode } from 'react';
import { useAdminSession } from '@/context/AdminSessionContext'; // Assuming alias @ is configured for src
import { useRouter } from 'next/navigation'; // Changed from 'next/router' to 'next/navigation' for App Router

interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermissions?: string[]; // e.g., ['canManageUsers', 'canAccessSettings.general']
  fallbackComponent?: ReactNode; // Optional: Component to show if not authorized
  redirectPath?: string; // Optional: Path to redirect if not authorized and no fallback
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermissions,
  fallbackComponent,
  redirectPath = '/unauthorized', // Default redirect path
}) => {
  const { isAuthenticated, permissions, isLoading, error } = useAdminSession();
  const router = useRouter();

  if (isLoading) {
    // You might want a more sophisticated loading spinner here
    return <div>Loading session...</div>;
  }

  if (error) {
    // Handle error state, perhaps redirect to an error page or show a message
    console.error("Error in session:", error);
    // Potentially redirect to a generic error page or show an inline error
    return <div>Error loading session. Please try again later.</div>;
  }

  if (!isAuthenticated) {
    // Not authenticated, redirect to sign-in or a public page
    // Clerk's middleware should ideally handle this, but this is a safeguard.
    // Forcing a redirect to sign-in if not caught by middleware.
    // Consider if Clerk's <RedirectToSignIn /> or similar is more appropriate here
    // depending on how ClerkProvider is set up.
    // router.push('/sign-in'); // Or use Clerk's redirect mechanism
    // For now, let Clerk middleware handle the main redirect.
    // If we reach here and not authenticated, it might be an edge case or misconfiguration.
    // Showing a generic message or redirecting to a safe public page.
    if (typeof window !== 'undefined') { // Ensure router.push is called client-side
        router.push(redirectPath || '/sign-in');
    }
    return fallbackComponent || <div>Redirecting to sign in...</div>; // Or a more specific "Access Denied"
  }

  if (requiredPermissions && requiredPermissions.length > 0) {
    const { permissions, isSystemAdmin } = useAdminSession();
    
    // If there are no permissions loaded and they're required
    if (!permissions && !isSystemAdmin) {
      // Permissions are required but not loaded yet or user has no permissions object
      if (fallbackComponent) return <>{fallbackComponent}</>;
      if (typeof window !== 'undefined') router.push(redirectPath);
      return <div>Access Denied: Permissions not available.</div>;
    }

    // Add debug logging
    console.log('Required permissions:', requiredPermissions);
    console.log('Available permissions:', permissions);
    
    // System admins automatically have all permissions
    if (isSystemAdmin) {
      console.log('User is system admin, granting access');
      return <>{children}</>;
    }
    
    // Check for system:* wildcard permission
    if (Array.isArray(permissions) && permissions.includes('system:*')) {
      console.log('User has system:* wildcard permission, granting access');
      return <>{children}</>;
    }
    
    // Check user has all required permissions
    const hasAllRequiredPermissions = Array.isArray(permissions)
      ? requiredPermissions.every(rp => permissions.includes(rp))
      : false;

    if (!hasAllRequiredPermissions) {
      if (fallbackComponent) return <>{fallbackComponent}</>;
      if (typeof window !== 'undefined') router.push(redirectPath);
      return <div>Access Denied: You do not have the required permissions.</div>;
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;