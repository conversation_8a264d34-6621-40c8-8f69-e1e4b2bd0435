'use client';

import React, { useState, useEffect } from 'react';
import { EnhancedParameterSchema } from '@/lib/types/enhanced-parameters';
import apiClient from '@/lib/apiClient';
import { ChevronDown, ChevronRight, Code, Info, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ParameterDetailsProps {
  endpointId: string;
  token: string;
  orgId?: string;
  compact?: boolean;
}

export const ParameterDetails: React.FC<ParameterDetailsProps> = ({
  endpointId,
  token,
  orgId,
  compact = false
}) => {
  const [parameters, setParameters] = useState<EnhancedParameterSchema[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(!compact);

  useEffect(() => {
    if (!expanded && compact) return;
    
    const fetchParameters = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const data = await apiClient(`/v1/openapi/endpoints/${endpointId}/enhanced-parameters`, {
          token,
          orgId
        });
        setParameters(data.enhanced_parameters || []);
      } catch (err) {
        console.error('Failed to fetch enhanced parameters:', err);
        setError('Failed to load parameter details');
      } finally {
        setLoading(false);
      }
    };

    fetchParameters();
  }, [endpointId, token, orgId, expanded, compact]);

  const renderParameterConstraints = (param: EnhancedParameterSchema) => {
    const constraints: React.ReactNode[] = [];

    if (param.required) {
      constraints.push(
        <span key="required" className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
          Required
        </span>
      );
    }

    if (param.enum && param.enum.length > 0) {
      constraints.push(
        <span key="enum" className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
          Enum: {param.enum.join(', ')}
        </span>
      );
    }

    if (param.format) {
      constraints.push(
        <span key="format" className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
          Format: {param.format}
        </span>
      );
    }

    if (param.pattern) {
      constraints.push(
        <span key="pattern" className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
          Pattern: <code className="font-mono text-xs">{param.pattern}</code>
        </span>
      );
    }

    if (param.minimum !== undefined || param.maximum !== undefined) {
      const rangeText = `${param.minimum !== undefined ? param.minimum : '∞'} - ${param.maximum !== undefined ? param.maximum : '∞'}`;
      constraints.push(
        <span key="range" className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          Range: {rangeText}
        </span>
      );
    }

    if (param.min_length !== undefined || param.max_length !== undefined) {
      const lengthText = `Length: ${param.min_length !== undefined ? param.min_length : '0'} - ${param.max_length !== undefined ? param.max_length : '∞'}`;
      constraints.push(
        <span key="length" className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400">
          {lengthText}
        </span>
      );
    }

    return constraints;
  };

  const getParameterTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'string':
      case 'email':
      case 'password':
      case 'uuid':
        return 'text-green-600 dark:text-green-400';
      case 'number':
      case 'integer':
      case 'float':
      case 'double':
        return 'text-blue-600 dark:text-blue-400';
      case 'boolean':
        return 'text-purple-600 dark:text-purple-400';
      case 'array':
        return 'text-orange-600 dark:text-orange-400';
      case 'object':
        return 'text-red-600 dark:text-red-400';
      case 'enum':
        return 'text-pink-600 dark:text-pink-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  if (compact && !expanded) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setExpanded(true)}
        className="text-xs"
      >
        <ChevronRight className="h-3 w-3 mr-1" />
        View Parameters ({parameters.length || '?'})
      </Button>
    );
  }

  if (loading) {
    return (
      <div className="p-4 text-center text-sm text-muted-foreground">
        Loading parameter details...
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-sm text-red-600 dark:text-red-400">
        <AlertCircle className="h-4 w-4 inline mr-1" />
        {error}
      </div>
    );
  }

  if (parameters.length === 0) {
    return (
      <div className="p-4 text-center text-sm text-muted-foreground">
        No parameters defined for this endpoint
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {compact && (
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-semibold">Parameters</h4>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpanded(false)}
            className="text-xs"
          >
            <ChevronDown className="h-3 w-3 mr-1" />
            Hide
          </Button>
        </div>
      )}

      {parameters.map((param) => (
        <div
          key={`${param.in_location}-${param.name}`}
          className="p-3 bg-muted/50 rounded-lg border border-border"
        >
          <div className="flex items-start justify-between mb-2">
            <div>
              <span className="font-mono text-sm font-medium">{param.name}</span>
              <span className={`ml-2 text-xs font-medium ${getParameterTypeColor(param.type)}`}>
                {param.type}
              </span>
              <span className="ml-2 text-xs text-muted-foreground">
                in {param.in_location}
              </span>
            </div>
            {param.deprecated && (
              <span className="text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 px-2 py-0.5 rounded">
                Deprecated
              </span>
            )}
          </div>

          {param.description && (
            <p className="text-xs text-muted-foreground mb-2">{param.description}</p>
          )}

          <div className="flex flex-wrap gap-1">
            {renderParameterConstraints(param)}
          </div>

          {(param.default !== undefined || param.example !== undefined) && (
            <div className="mt-2 space-y-1">
              {param.default !== undefined && (
                <div className="text-xs">
                  <span className="text-muted-foreground">Default:</span>{' '}
                  <code className="font-mono bg-muted px-1 py-0.5 rounded">
                    {JSON.stringify(param.default)}
                  </code>
                </div>
              )}
              {param.example !== undefined && (
                <div className="text-xs">
                  <span className="text-muted-foreground">Example:</span>{' '}
                  <code className="font-mono bg-muted px-1 py-0.5 rounded">
                    {JSON.stringify(param.example)}
                  </code>
                </div>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ParameterDetails;