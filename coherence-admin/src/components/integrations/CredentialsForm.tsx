'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Save, RefreshCcw, Key, AlertCircle, Info } from 'lucide-react';
import { SchemaAwareInput } from '@/components/forms/SchemaAwareInput';
import { EnhancedParameterSchema } from '@/lib/types/enhanced-parameters';
import apiClient from '@/lib/apiClient';

interface ApiAuthConfig {
  integration_id: string;
  auth_type: 'api_key' | 'oauth2' | 'bearer' | 'basic' | 'custom';
  credentials?: Record<string, any> | null;
  scopes?: string[] | null;
  expires_at?: string | null;
}

interface CredentialsFormProps {
  integrationId: string;
  integrationName: string;
  authConfig?: ApiAuthConfig | null;
  openApiSpec: Record<string, any>;
  token: string;
  orgId?: string;
  onSaveSuccess?: () => void;
}

export const CredentialsForm: React.FC<CredentialsFormProps> = ({
  integrationId,
  integrationName,
  authConfig,
  openApiSpec,
  token,
  orgId,
  onSaveSuccess
}) => {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState<string | null>(null);
  const [authSchemaParams, setAuthSchemaParams] = useState<EnhancedParameterSchema[]>([]);

  // Extract auth parameters from OpenAPI spec
  useEffect(() => {
    const extractAuthParameters = () => {
      const params: EnhancedParameterSchema[] = [];
      const securitySchemes = openApiSpec.components?.securitySchemes || {};

      Object.entries(securitySchemes).forEach(([schemeName, scheme]: [string, any]) => {
        if (scheme.type === 'apiKey') {
          params.push({
            name: scheme.name || 'api_key',
            type: 'string',
            format: 'password',
            in_location: scheme.in || 'header',
            required: true,
            description: scheme.description || `API Key for ${schemeName}`,
            example: scheme.example,
            pattern: scheme.pattern,
            min_length: scheme.minLength,
            max_length: scheme.maxLength
          });
        } else if (scheme.type === 'http' && scheme.scheme === 'bearer') {
          params.push({
            name: 'bearer_token',
            type: 'string',
            format: 'password',
            in_location: 'header',
            required: true,
            description: scheme.description || 'Bearer token for authentication',
            example: scheme.bearerFormat ? `JWT or ${scheme.bearerFormat} token` : undefined
          });
        } else if (scheme.type === 'http' && scheme.scheme === 'basic') {
          params.push(
            {
              name: 'username',
              type: 'string',
              in_location: 'header',
              required: true,
              description: 'Username for basic authentication'
            },
            {
              name: 'password',
              type: 'string',
              format: 'password',
              in_location: 'header',
              required: true,
              description: 'Password for basic authentication'
            }
          );
        } else if (scheme.type === 'oauth2') {
          // For OAuth2, we typically need client credentials
          params.push(
            {
              name: 'client_id',
              type: 'string',
              in_location: 'body',
              required: true,
              description: 'OAuth2 Client ID'
            },
            {
              name: 'client_secret',
              type: 'string',
              format: 'password',
              in_location: 'body',
              required: true,
              description: 'OAuth2 Client Secret'
            }
          );
          
          // Add scopes if available
          if (scheme.flows) {
            const scopes = Object.values(scheme.flows).reduce((acc: string[], flow: any) => {
              if (flow.scopes) {
                return [...acc, ...Object.keys(flow.scopes)];
              }
              return acc;
            }, []);
            
            if (scopes.length > 0) {
              params.push({
                name: 'scopes',
                type: 'array',
                in_location: 'body',
                required: false,
                description: 'OAuth2 scopes to request',
                enum: scopes,
                default: scopes
              });
            }
          }
        }
      });

      // If no security schemes found, default to API key
      if (params.length === 0 && authConfig?.auth_type === 'api_key') {
        params.push({
          name: 'api_key',
          type: 'string',
          format: 'password',
          in_location: 'header',
          required: true,
          description: 'API Key for authentication'
        });
      }

      setAuthSchemaParams(params);
    };

    extractAuthParameters();
  }, [openApiSpec, authConfig]);

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
    
    // Clear error for this field
    if (errors[fieldName]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    authSchemaParams.forEach(param => {
      if (param.required && !formData[param.name]) {
        newErrors[param.name] = `${param.name} is required`;
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setSaving(true);
    setSaveError(null);
    setSaveSuccess(null);

    try {
      // For now, we'll use the existing API key endpoint
      // TODO: Update to support all credential types
      if (authConfig?.auth_type === 'api_key' && formData.api_key) {
        await apiClient(`/admin/credentials/${integrationId}/api-key`, {
          method: 'PUT',
          token: token,
          body: JSON.stringify({ key: formData.api_key }),
        });
      } else {
        // Generic credential save endpoint (to be implemented)
        await apiClient(`/v1/admin/integrations/${integrationId}/credentials`, {
          method: 'PUT',
          token: token,
          orgId: orgId,
          body: JSON.stringify({
            auth_type: authConfig?.auth_type || 'api_key',
            credentials: formData
          }),
        });
      }

      setSaveSuccess('Credentials saved successfully');
      setFormData({}); // Clear form after successful save
      
      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (err) {
      console.error('Error saving credentials:', err);
      setSaveError(err instanceof Error ? err.message : 'Failed to save credentials');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Authentication Type Info */}
      <div className="bg-muted p-4 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <Info className="h-4 w-4 text-muted-foreground" />
          <h3 className="font-semibold text-sm">Authentication Type</h3>
        </div>
        <p className="text-sm text-muted-foreground">
          {authConfig?.auth_type ? (
            <>This integration uses <span className="font-medium capitalize">{authConfig.auth_type.replace('_', ' ')}</span> authentication.</>
          ) : (
            'No authentication type detected. Using default API key authentication.'
          )}
        </p>
      </div>

      {/* Current Credentials Status */}
      {authConfig?.credentials && Object.keys(authConfig.credentials).length > 0 && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <Key className="h-4 w-4 text-green-600 dark:text-green-400" />
            <p className="text-sm font-medium text-green-700 dark:text-green-300">
              Credentials are currently configured
            </p>
          </div>
          <p className="text-xs text-green-600 dark:text-green-400 mt-1">
            Existing credentials will be replaced when you save new ones.
          </p>
        </div>
      )}

      {/* Success/Error Messages */}
      {saveError && (
        <div className="bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded relative" role="alert">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            <span>{saveError}</span>
          </div>
        </div>
      )}
      
      {saveSuccess && (
        <div className="bg-green-100 dark:bg-green-900/20 border border-green-400 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded relative" role="alert">
          <span>{saveSuccess}</span>
        </div>
      )}

      {/* Credential Fields */}
      <div className="space-y-4">
        {authSchemaParams.length > 0 ? (
          authSchemaParams.map((param) => (
            <div key={param.name}>
              <Label htmlFor={param.name} className="block mb-2">
                {param.description || param.name}
                {param.required && <span className="text-red-500 ml-1">*</span>}
              </Label>
              <SchemaAwareInput
                parameter={param}
                value={formData[param.name]}
                onChange={(value) => handleFieldChange(param.name, value)}
                error={errors[param.name]}
                disabled={saving}
              />
              {param.example && !errors[param.name] && (
                <p className="text-xs text-muted-foreground mt-1">
                  Example: {param.example}
                </p>
              )}
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">
              No authentication parameters detected in the OpenAPI specification.
            </p>
          </div>
        )}
      </div>

      {/* Save Button */}
      {authSchemaParams.length > 0 && (
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={saving || Object.keys(formData).length === 0}
          >
            {saving ? (
              <span className="flex items-center">
                <RefreshCcw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </span>
            ) : (
              <span className="flex items-center">
                <Save className="mr-2 h-4 w-4" />
                Save Credentials
              </span>
            )}
          </Button>
        </div>
      )}

      {/* Additional Help Text */}
      <div className="mt-6 p-4 bg-muted rounded-lg">
        <p className="text-xs text-muted-foreground">
          <strong>Note:</strong> Credentials are encrypted and stored securely. They cannot be viewed once saved.
          You'll need to re-enter them if you want to update them.
        </p>
      </div>
    </div>
  );
};

export default CredentialsForm;