'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Eye, FileJson, Shield, Globe, Server, Info } from 'lucide-react';
import MonacoEditor from '@monaco-editor/react';

interface SchemaViewerProps {
  openApiSpec: Record<string, any>;
  integrationName: string;
}

export const SchemaViewer: React.FC<SchemaViewerProps> = ({
  openApiSpec,
  integrationName
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'full' | 'info' | 'security' | 'servers' | 'paths'>('info');

  const getSecuritySchemes = () => {
    return openApiSpec.components?.securitySchemes || {};
  };

  const getServers = () => {
    return openApiSpec.servers || [];
  };

  const getApiInfo = () => {
    return openApiSpec.info || {};
  };

  const getPaths = () => {
    return Object.keys(openApiSpec.paths || {}).length;
  };

  const getOperationCount = () => {
    let count = 0;
    const paths = openApiSpec.paths || {};
    Object.values(paths).forEach((path: any) => {
      count += Object.keys(path).filter(method => 
        ['get', 'post', 'put', 'delete', 'patch', 'options', 'head'].includes(method)
      ).length;
    });
    return count;
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'info':
        const info = getApiInfo();
        return (
          <div className="space-y-4">
            {info.title && (
              <div>
                <h3 className="text-sm font-semibold mb-1">API Title</h3>
                <p className="text-sm text-muted-foreground">{info.title}</p>
              </div>
            )}
            {info.version && (
              <div>
                <h3 className="text-sm font-semibold mb-1">Version</h3>
                <p className="text-sm text-muted-foreground">{info.version}</p>
              </div>
            )}
            {info.description && (
              <div>
                <h3 className="text-sm font-semibold mb-1">Description</h3>
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">{info.description}</p>
              </div>
            )}
            {info.termsOfService && (
              <div>
                <h3 className="text-sm font-semibold mb-1">Terms of Service</h3>
                <a href={info.termsOfService} target="_blank" rel="noopener noreferrer" 
                   className="text-sm text-primary hover:underline">
                  {info.termsOfService}
                </a>
              </div>
            )}
            {info.contact && (
              <div>
                <h3 className="text-sm font-semibold mb-1">Contact</h3>
                <div className="text-sm text-muted-foreground">
                  {info.contact.name && <p>{info.contact.name}</p>}
                  {info.contact.email && (
                    <a href={`mailto:${info.contact.email}`} className="text-primary hover:underline">
                      {info.contact.email}
                    </a>
                  )}
                  {info.contact.url && (
                    <a href={info.contact.url} target="_blank" rel="noopener noreferrer" 
                       className="text-primary hover:underline block">
                      {info.contact.url}
                    </a>
                  )}
                </div>
              </div>
            )}
            {info.license && (
              <div>
                <h3 className="text-sm font-semibold mb-1">License</h3>
                <p className="text-sm text-muted-foreground">
                  {info.license.name}
                  {info.license.url && (
                    <a href={info.license.url} target="_blank" rel="noopener noreferrer" 
                       className="text-primary hover:underline ml-2">
                      View License
                    </a>
                  )}
                </p>
              </div>
            )}
          </div>
        );

      case 'security':
        const schemes = getSecuritySchemes();
        return (
          <div className="space-y-4">
            {Object.entries(schemes).length > 0 ? (
              Object.entries(schemes).map(([name, scheme]: [string, any]) => (
                <div key={name} className="p-3 bg-muted rounded-lg">
                  <h4 className="font-semibold text-sm mb-2">{name}</h4>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Type:</span> {scheme.type}</p>
                    {scheme.scheme && (
                      <p><span className="font-medium">Scheme:</span> {scheme.scheme}</p>
                    )}
                    {scheme.bearerFormat && (
                      <p><span className="font-medium">Bearer Format:</span> {scheme.bearerFormat}</p>
                    )}
                    {scheme.flows && (
                      <p><span className="font-medium">OAuth Flows:</span> {Object.keys(scheme.flows).join(', ')}</p>
                    )}
                    {scheme.in && (
                      <p><span className="font-medium">Location:</span> {scheme.in}</p>
                    )}
                    {scheme.name && (
                      <p><span className="font-medium">Parameter Name:</span> {scheme.name}</p>
                    )}
                    {scheme.description && (
                      <p className="text-muted-foreground mt-2">{scheme.description}</p>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-muted-foreground">No security schemes defined</p>
            )}
          </div>
        );

      case 'servers':
        const servers = getServers();
        return (
          <div className="space-y-4">
            {servers.length > 0 ? (
              servers.map((server: any, index: number) => (
                <div key={index} className="p-3 bg-muted rounded-lg">
                  <h4 className="font-semibold text-sm mb-2">Server {index + 1}</h4>
                  <div className="space-y-1 text-sm">
                    <p className="font-mono">{server.url}</p>
                    {server.description && (
                      <p className="text-muted-foreground">{server.description}</p>
                    )}
                    {server.variables && Object.keys(server.variables).length > 0 && (
                      <div className="mt-2">
                        <p className="font-medium mb-1">Variables:</p>
                        {Object.entries(server.variables).map(([varName, varData]: [string, any]) => (
                          <div key={varName} className="ml-2">
                            <span className="font-mono text-xs">{varName}</span>: {varData.default}
                            {varData.enum && ` (options: ${varData.enum.join(', ')})`}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-muted-foreground">No servers defined</p>
            )}
          </div>
        );

      case 'paths':
        const paths = openApiSpec.paths || {};
        const pathCount = Object.keys(paths).length;
        const operationCount = getOperationCount();
        
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-2xl font-bold">{pathCount}</p>
                <p className="text-sm text-muted-foreground">Total Paths</p>
              </div>
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-2xl font-bold">{operationCount}</p>
                <p className="text-sm text-muted-foreground">Total Operations</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-semibold">Path Overview</h4>
              <div className="max-h-64 overflow-y-auto space-y-1">
                {Object.entries(paths).map(([path, methods]: [string, any]) => (
                  <div key={path} className="text-xs">
                    <span className="font-mono">{path}</span>
                    <span className="ml-2 text-muted-foreground">
                      ({Object.keys(methods).filter(m => 
                        ['get', 'post', 'put', 'delete', 'patch', 'options', 'head'].includes(m)
                      ).join(', ').toUpperCase()})
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'full':
        return (
          <div className="h-[500px]">
            <MonacoEditor
              height="100%"
              language="json"
              theme="vs-dark"
              value={JSON.stringify(openApiSpec, null, 2)}
              options={{
                readOnly: true,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                fontSize: 12,
              }}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
      >
        <Eye className="mr-2 h-4 w-4" />
        View Schema Details
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>OpenAPI Schema - {integrationName}</DialogTitle>
          </DialogHeader>

          <div className="flex space-x-2 border-b">
            <Button
              variant={activeTab === 'info' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab('info')}
              className="text-xs"
            >
              <Info className="h-3 w-3 mr-1" />
              API Info
            </Button>
            <Button
              variant={activeTab === 'security' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab('security')}
              className="text-xs"
            >
              <Shield className="h-3 w-3 mr-1" />
              Security
            </Button>
            <Button
              variant={activeTab === 'servers' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab('servers')}
              className="text-xs"
            >
              <Server className="h-3 w-3 mr-1" />
              Servers
            </Button>
            <Button
              variant={activeTab === 'paths' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab('paths')}
              className="text-xs"
            >
              <Globe className="h-3 w-3 mr-1" />
              Paths
            </Button>
            <Button
              variant={activeTab === 'full' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab('full')}
              className="text-xs"
            >
              <FileJson className="h-3 w-3 mr-1" />
              Full Schema
            </Button>
          </div>

          <div className="flex-1 overflow-y-auto p-4">
            {renderContent()}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SchemaViewer;