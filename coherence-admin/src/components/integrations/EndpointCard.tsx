'use client';

import React, { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronRight, AlertTriangle, Clock, Zap, Code2, Hash } from 'lucide-react';
import { ParameterDetails } from './ParameterDetails';

interface RateLimit {
  id: string;
  requests_per_min: number;
  burst_size: number;
  cooldown_sec: number;
}

interface ApiEndpoint {
  id: string;
  integration_id: string;
  path: string;
  method: string;
  operation_id?: string | null;
  summary?: string;
  description?: string;
  tags?: string[];
  deprecated?: boolean;
  enabled: boolean;
  rate_limits: RateLimit[];
  openapi_snippet?: Record<string, any>;
}

interface EndpointCardProps {
  endpoint: ApiEndpoint;
  integrationName: string;
  apiType: string;
  onToggle: (endpointId: string, enabled: boolean) => void;
  showEnhancedDetails?: boolean;
  token?: string;
  orgId?: string;
}

export const EndpointCard: React.FC<EndpointCardProps> = ({
  endpoint,
  integrationName,
  apiType,
  onToggle,
  showEnhancedDetails = true,
  token,
  orgId
}) => {
  const [expanded, setExpanded] = useState(false);
  const [showParameters, setShowParameters] = useState(false);

  const getMethodColor = (method: string) => {
    const m = method.toLowerCase();
    if (m === 'get') return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200 dark:border-blue-800';
    if (m === 'post') return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border border-green-200 dark:border-green-800';
    if (m === 'put') return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800';
    if (m === 'delete') return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200 dark:border-red-800';
    return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 border border-gray-200 dark:border-gray-800';
  };

  const hasParameters = () => {
    if (!endpoint.openapi_snippet) return false;
    const snippet = endpoint.openapi_snippet;
    return !!(snippet.parameters?.length || snippet.requestBody);
  };

  const getParameterCount = () => {
    if (!endpoint.openapi_snippet) return 0;
    const snippet = endpoint.openapi_snippet;
    let count = snippet.parameters?.length || 0;
    
    // Add request body properties count
    if (snippet.requestBody?.content?.['application/json']?.schema?.properties) {
      count += Object.keys(snippet.requestBody.content['application/json'].schema.properties).length;
    }
    
    return count;
  };

  return (
    <div className="border border-border rounded-lg hover:shadow-md transition-all">
      {/* Main Header */}
      <div className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center flex-1">
            <span className={`px-2 py-1 text-xs font-bold rounded uppercase mr-2 ${getMethodColor(endpoint.method)}`}>
              {endpoint.method}
            </span>
            <span className="text-lg font-semibold font-mono flex-1">{endpoint.path}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id={`endpoint-enabled-${endpoint.id}`}
              checked={endpoint.enabled}
              onCheckedChange={(checked) => onToggle(endpoint.id, checked)}
            />
            <span className={`text-xs font-semibold ${endpoint.enabled ? 'text-primary' : 'text-muted-foreground'}`}>
              {endpoint.enabled ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>

        {/* Tags */}
        {endpoint.tags && endpoint.tags.length > 0 && (
          <div className="mb-2 flex flex-wrap gap-1">
            {endpoint.tags.map((tag, index) => (
              <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200 dark:border-blue-800 text-xs font-medium rounded-full">
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* Summary */}
        {endpoint.summary && (
          <p className="text-sm text-muted-foreground font-medium mb-2">{endpoint.summary}</p>
        )}

        {/* Deprecated Warning */}
        {endpoint.deprecated && (
          <div className="mb-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-yellow-700 dark:text-yellow-400 text-xs rounded-md flex items-center">
            <AlertTriangle className="h-3 w-3 mr-1 flex-shrink-0" />
            <span className="font-medium">Deprecated:</span>&nbsp;This endpoint may be removed in future versions.
          </div>
        )}

        {/* Quick Stats */}
        <div className="flex flex-wrap gap-2 text-xs">
          {hasParameters() && (
            <div className="flex items-center text-muted-foreground">
              <Code2 className="h-3 w-3 mr-1" />
              {getParameterCount()} parameter{getParameterCount() !== 1 ? 's' : ''}
            </div>
          )}
          {endpoint.operation_id && (
            <div className="flex items-center text-muted-foreground">
              <Hash className="h-3 w-3 mr-1" />
              {endpoint.operation_id}
            </div>
          )}
          {endpoint.rate_limits && endpoint.rate_limits.length > 0 && (
            <div className="flex items-center text-muted-foreground">
              <Zap className="h-3 w-3 mr-1" />
              Rate limited
            </div>
          )}
        </div>

        {/* Expand/Collapse Button */}
        {(endpoint.description || showEnhancedDetails) && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpanded(!expanded)}
            className="mt-3 text-xs w-full justify-between"
          >
            <span>View Details</span>
            {expanded ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
          </Button>
        )}
      </div>

      {/* Expanded Details */}
      {expanded && (
        <div className="border-t border-border p-4 space-y-4">
          {/* Description */}
          {endpoint.description && (
            <div>
              <h4 className="text-sm font-semibold mb-1">Description</h4>
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">{endpoint.description}</p>
            </div>
          )}

          {/* Enhanced Parameter Details */}
          {showEnhancedDetails && token && hasParameters() && (
            <div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowParameters(!showParameters)}
                className="mb-2 text-xs"
              >
                {showParameters ? <ChevronDown className="h-3 w-3 mr-1" /> : <ChevronRight className="h-3 w-3 mr-1" />}
                Parameters & Schema
              </Button>
              
              {showParameters && (
                <ParameterDetails
                  endpointId={endpoint.id}
                  token={token}
                  orgId={orgId}
                  compact={false}
                />
              )}
            </div>
          )}

          {/* Rate Limits */}
          {endpoint.rate_limits && endpoint.rate_limits.length > 0 && (
            <div>
              <h4 className="text-sm font-semibold mb-2 flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                Rate Limits
              </h4>
              <div className="space-y-1">
                {endpoint.rate_limits.map(rl => (
                  <div key={rl.id} className="text-xs bg-muted p-2 rounded flex items-center justify-between">
                    <span>
                      <span className="font-mono font-medium">{rl.requests_per_min}</span> requests/min
                    </span>
                    <span className="text-muted-foreground">
                      Burst: {rl.burst_size} • Cooldown: {rl.cooldown_sec}s
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Metadata */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="font-medium text-muted-foreground">Integration:</span>{' '}
              <span>{integrationName}</span>
            </div>
            <div>
              <span className="font-medium text-muted-foreground">API Type:</span>{' '}
              <span className="capitalize">{apiType}</span>
            </div>
            {endpoint.action_class_name && (
              <div className="col-span-2">
                <span className="font-medium text-muted-foreground">Action Class:</span>{' '}
                <span className="font-mono">{endpoint.action_class_name}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default EndpointCard;