'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronDown, ChevronRight, Code2, Al<PERSON>Triangle, Eye } from 'lucide-react';
import { ParameterDetails } from './ParameterDetails';

interface ApiEndpoint {
  id: string;
  path: string;
  method: string;
  summary?: string;
  description?: string;
  tags?: string[];
  deprecated?: boolean;
  enabled: boolean;
  openapi_snippet?: Record<string, any>;
}

interface EndpointSelectorProps {
  endpoints: ApiEndpoint[];
  selectedEndpoints: string[];
  onSelectionChange: (endpointId: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  token?: string;
  orgId?: string;
}

export const EndpointSelector: React.FC<EndpointSelectorProps> = ({
  endpoints,
  selectedEndpoints,
  onSelectionChange,
  onSelectAll,
  token,
  orgId
}) => {
  const [expandedEndpoints, setExpandedEndpoints] = useState<Set<string>>(new Set());

  const toggleExpanded = (endpointId: string) => {
    setExpandedEndpoints(prev => {
      const newSet = new Set(prev);
      if (newSet.has(endpointId)) {
        newSet.delete(endpointId);
      } else {
        newSet.add(endpointId);
      }
      return newSet;
    });
  };

  const getMethodColor = (method: string) => {
    const m = method.toLowerCase();
    if (m === 'get') return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200 dark:border-blue-800';
    if (m === 'post') return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border border-green-200 dark:border-green-800';
    if (m === 'put') return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800';
    if (m === 'delete') return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200 dark:border-red-800';
    return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 border border-gray-200 dark:border-gray-800';
  };

  const hasParameters = (endpoint: ApiEndpoint) => {
    if (!endpoint.openapi_snippet) return false;
    const snippet = endpoint.openapi_snippet;
    return !!(snippet.parameters?.length || snippet.requestBody);
  };

  const getParameterCount = (endpoint: ApiEndpoint) => {
    if (!endpoint.openapi_snippet) return 0;
    const snippet = endpoint.openapi_snippet;
    let count = snippet.parameters?.length || 0;
    
    // Add request body properties count
    if (snippet.requestBody?.content?.['application/json']?.schema?.properties) {
      count += Object.keys(snippet.requestBody.content['application/json'].schema.properties).length;
    }
    
    return count;
  };

  const selectAll = selectedEndpoints.length === endpoints.length;

  return (
    <div className="space-y-4">
      {/* Select All Header */}
      <div className="flex justify-between items-center pb-3 border-b border-border">
        <h3 className="text-lg font-semibold">
          Select Endpoints for Generation
        </h3>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="selectAll"
            checked={selectAll}
            onCheckedChange={(checked) => onSelectAll(checked as boolean)}
            className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
          />
          <label htmlFor="selectAll" className="text-sm font-medium cursor-pointer">
            Select All ({endpoints.length})
          </label>
        </div>
      </div>

      {/* Selected Count */}
      <div className="flex items-center">
        <span className="text-sm font-medium">
          <span className="px-2 py-1 rounded-full bg-primary/20 mr-1 text-primary border border-primary/40">
            {selectedEndpoints.length}
          </span> 
          endpoint(s) selected
        </span>
      </div>

      {/* Endpoint List */}
      <div className="space-y-2">
        {endpoints.map((endpoint) => {
          const isExpanded = expandedEndpoints.has(endpoint.id);
          const isSelected = selectedEndpoints.includes(endpoint.id);
          const paramCount = getParameterCount(endpoint);

          return (
            <div 
              key={endpoint.id} 
              className={`rounded-lg border transition-all ${
                isSelected 
                  ? 'border-primary/50 bg-primary/5' 
                  : 'border-border hover:border-primary/30'
              }`}
            >
              {/* Main Endpoint Row */}
              <div className="p-4">
                <div className="flex items-start gap-3">
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={(checked) => onSelectionChange(endpoint.id, checked as boolean)}
                    className="mt-1 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                  />
                  
                  <div className="flex-1">
                    <div className="flex items-center flex-wrap gap-2 mb-1">
                      <span className={`px-2 py-1 text-xs font-bold rounded uppercase ${getMethodColor(endpoint.method)}`}>
                        {endpoint.method}
                      </span>
                      <span className="font-mono text-sm font-medium">{endpoint.path}</span>
                      {endpoint.deprecated && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          Deprecated
                        </span>
                      )}
                      {!endpoint.enabled && (
                        <span className="text-xs text-muted-foreground">(Disabled)</span>
                      )}
                    </div>
                    
                    {endpoint.summary && (
                      <p className="text-sm text-muted-foreground mb-1">{endpoint.summary}</p>
                    )}
                    
                    {endpoint.tags && endpoint.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-2">
                        {endpoint.tags.map((tag, idx) => (
                          <span 
                            key={idx} 
                            className="px-2 py-0.5 bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 border border-purple-200 dark:border-purple-800 text-xs rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    <div className="flex items-center gap-3 text-xs text-muted-foreground">
                      {hasParameters(endpoint) && (
                        <span className="flex items-center">
                          <Code2 className="h-3 w-3 mr-1" />
                          {paramCount} parameter{paramCount !== 1 ? 's' : ''}
                        </span>
                      )}
                      {token && hasParameters(endpoint) && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(endpoint.id)}
                          className="h-auto p-0 text-xs hover:text-primary"
                        >
                          {isExpanded ? (
                            <>
                              <ChevronDown className="h-3 w-3 mr-1" />
                              Hide Parameters
                            </>
                          ) : (
                            <>
                              <Eye className="h-3 w-3 mr-1" />
                              View Parameters
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Expanded Parameters */}
              {isExpanded && token && (
                <div className="border-t border-border p-4 bg-gray-50 dark:bg-gray-800/50">
                  <ParameterDetails
                    endpointId={endpoint.id}
                    token={token}
                    orgId={orgId}
                    compact={false}
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default EndpointSelector;