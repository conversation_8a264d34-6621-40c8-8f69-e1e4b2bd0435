'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import apiClient from '@/lib/apiClient';
import { PlayIcon, ClockIcon, CodeIcon, RefreshCcw } from 'lucide-react';

interface Parameter {
  name: string;
  type: string;
  description?: string;
  required: boolean;
  schema?: any;
}

interface ActionTestConsoleProps {
  actionConfig: any;
  integrationId: string;
  token: string;
  orgId: string;
  parameters?: Parameter[];
}

const ActionTestConsole: React.FC<ActionTestConsoleProps> = ({
  actionConfig,
  integrationId,
  token,
  orgId,
  parameters = []
}) => {
  const [activeTab, setActiveTab] = useState('parameters');
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionError, setExecutionError] = useState<string | null>(null);
  const [response, setResponse] = useState<any>(null);
  const [requestData, setRequestData] = useState<any>(null);
  const [historicalTests, setHistoricalTests] = useState<any[]>([]);
  const [formSchema, setFormSchema] = useState<z.ZodObject<any>>(z.object({}));
  const [parameterSpec, setParameterSpec] = useState<Record<string, any>>({});
  
  // Store current form values in ref to preserve across schema changes
  const formValuesRef = React.useRef<Record<string, any>>({});
  
  // Initialize/reset form schema when actionConfig or parameters change
  // but preserve values where possible
  useEffect(() => {
    // Save current form values before schema changes
    if (form) {
      const currentValues = form.getValues();
      if (Object.keys(currentValues).length > 0) {
        formValuesRef.current = { ...currentValues };
      }
    }
    
    // Generate the appropriate form schema first
    if (parameters && parameters.length > 0) {
      generateFormSchema(parameters);
    } else if (actionConfig?.parameters) {
      generateFormSchema(actionConfig.parameters);
    } else {
      // Default empty schema
      setFormSchema(z.object({
        _raw: z.string().optional().describe('Raw JSON Parameters')
      }));
      setParameterSpec({
        _raw: {
          type: 'object',
          description: 'Enter parameters as JSON object'
        }
      });
    }
    
    // Only reset response-related state, not form values
    setExecutionError(null);
    
    // Log for debugging
    console.log('ActionTestConsole: Endpoint updated', {
      method: actionConfig?.method,
      endpoint: actionConfig?.endpoint,
      parameterCount: parameters?.length || 0
    });
  }, [actionConfig, parameters]);
  
  // Initialize the form with persisted values if available
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: Object.keys(formValuesRef.current).length > 0 
      ? formValuesRef.current 
      : {},
    mode: 'onChange',
  });

  // After schema is updated, restore form values that match the new schema
  useEffect(() => {
    if (Object.keys(formValuesRef.current).length > 0) {
      // Filter saved values to only include fields in the current schema
      const fieldsToRestore = Object.keys(formValuesRef.current).filter(key => 
        Object.keys(parameterSpec).includes(key)
      );
      
      // Build an object with just the values to restore
      const valuesToRestore = fieldsToRestore.reduce((acc, key) => {
        acc[key] = formValuesRef.current[key];
        return acc;
      }, {} as Record<string, any>);
      
      // Only reset if we have values to restore
      if (Object.keys(valuesToRestore).length > 0) {
        setTimeout(() => {
          // Use setTimeout to ensure this runs after form has fully initialized with new schema
          form.reset(valuesToRestore);
        }, 0);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [parameterSpec]);

  const generateFormSchema = (params: Parameter[]) => {
    const schemaMap: Record<string, any> = {};
    const specMap: Record<string, any> = {};
    
    // Add a raw JSON input option
    schemaMap._raw = z.string().optional().describe('Raw JSON Parameters');
    specMap._raw = {
      type: 'object',
      description: 'Enter parameters as JSON object'
    };
    
    params.forEach(param => {
      // Map parameter types to Zod validators
      let validator: any;
      
      // Handle enum types first
      if (param.schema?.enum || param.type === 'enum') {
        const enumValues = param.schema?.enum || [];
        if (enumValues.length > 0) {
          validator = z.enum(enumValues as [string, ...string[]]);
          if (!param.required) validator = validator.optional();
        } else {
          validator = z.string();
          if (!param.required) validator = validator.optional();
        }
      } else {
        // Handle other types
        switch (param.type.toLowerCase()) {
          case 'string':
          case 'date':
          case 'date-time':
          case 'email':
          case 'uri':
          case 'uuid':
            validator = z.string();
            if (!param.required) validator = validator.optional();
            break;
          case 'number':
          case 'float':
          case 'double':
            validator = z.number();
            if (!param.required) validator = validator.optional();
            break;
          case 'integer':
          case 'int32':
          case 'int64':
            validator = z.number().int();
            if (!param.required) validator = validator.optional();
            break;
          case 'boolean':
            validator = z.boolean();
            if (!param.required) validator = validator.optional();
            break;
          case 'array':
            // For array types, we'll treat them as JSON strings in the UI
            validator = z.string();
            if (!param.required) validator = validator.optional();
            break;
          case 'object':
            // For object types, we'll treat them as JSON strings in the UI
            validator = z.string();
            if (!param.required) validator = validator.optional();
            break;
          default:
            validator = z.string();
            if (!param.required) validator = validator.optional();
        }
      }
      
      // Add description
      if (param.description) {
        validator = validator.describe(param.description);
      }
      
      schemaMap[param.name] = validator;
      specMap[param.name] = {
        type: param.type,
        description: param.description || '',
        required: param.required,
        schema: param.schema,
        enum: param.schema?.enum,
        format: param.schema?.format,
        example: param.schema?.example,
        default: param.schema?.default
      };
    });
    
    setFormSchema(z.object(schemaMap));
    setParameterSpec(specMap);
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    // Store the current scroll position before any changes
    const scrollPos = window.scrollY;
    
    setIsExecuting(true);
    setExecutionError(null);
    
    try {
      // Handle raw JSON parameters if provided
      let parameters: Record<string, any> = {};
      
      if (values._raw) {
        try {
          parameters = JSON.parse(values._raw);
        } catch (e) {
          setExecutionError('Invalid JSON in raw parameters');
          setIsExecuting(false);
          return;
        }
      } else {
        // Remove _raw and use the rest as parameters
        const { _raw, ...rest } = values;
        
        // Process parameters to handle arrays and objects that are JSON strings
        parameters = {};
        for (const [key, value] of Object.entries(rest)) {
          if (value === undefined || value === null || value === '') {
            // Skip empty values
            continue;
          }
          
          // Find the parameter spec for this field
          const spec = parameterSpec[key];
          
          // If it's an array or object type and the value is a string, try to parse it
          if (spec && (spec.type === 'array' || spec.type === 'object') && typeof value === 'string') {
            try {
              parameters[key] = JSON.parse(value);
            } catch (e) {
              // If parsing fails, use the string value and let the API handle it
              parameters[key] = value;
            }
          } else {
            parameters[key] = value;
          }
        }
      }
      
      // Save the form values to the ref for persistence
      formValuesRef.current = { ...values };
      
      // Prepare the request
      const testRequest = {
        action_config: actionConfig,
        parameters,
        integration_id: integrationId
      };

      // Store request data for display
      setRequestData(testRequest);

      // Execute the action test
      const result = await apiClient('/admin/actions/test', {
        method: 'POST',
        body: JSON.stringify(testRequest),
        token,
        orgId
      });

      // Add to history with timestamp
      const historyItem = {
        timestamp: new Date().toISOString(),
        request: testRequest,
        response: result
      };
      
      setHistoricalTests(prev => [historyItem, ...prev].slice(0, 10));
      
      // Set response
      setResponse(result);
      
      // Store current scroll position
      setCurrentScrollPos(window.scrollY);
      
      // Switch to response tab
      setActiveTab('response');
    } catch (error) {
      console.error('Error executing action:', error);
      
      let errorMessage = 'An unexpected error occurred while executing the action';
      let errorDetails = {};
      
      if (error instanceof Error) {
        errorMessage = error.message;
        // Check if there's additional error data available
        if ((error as any).errorData) {
          errorDetails = (error as any).errorData;
          // Extract more specific error messages if available
          if ((error as any).errorData.detail) {
            errorMessage = (error as any).errorData.detail;
          } else if ((error as any).errorData.message) {
            errorMessage = (error as any).errorData.message;
          }
        }
      }
      
      setExecutionError(errorMessage);
      
      setResponse({
        success: false,
        error: {
          message: errorMessage,
          details: errorDetails
        }
      });
      
      // Store current scroll position
      setCurrentScrollPos(window.scrollY);
      
      // Switch to response tab  
      setActiveTab('response');
    } finally {
      setIsExecuting(false);
    }
  };

  // Function to get readable parameter type
  const getParameterTypeLabel = (type: string, schema?: any) => {
    if (schema?.enum) {
      return `Select from options`;
    }
    
    switch (type.toLowerCase()) {
      case 'string': return 'Text';
      case 'date': return 'Date (YYYY-MM-DD)';
      case 'date-time': return 'Date & Time (ISO 8601)';
      case 'email': return 'Email Address';
      case 'uri': return 'URL/URI';
      case 'uuid': return 'UUID';
      case 'number':
      case 'float':
      case 'double': return 'Number (decimal)';
      case 'integer':
      case 'int32':
      case 'int64': return 'Integer';
      case 'boolean': return 'True/False';
      case 'array': return 'Array (JSON format)';
      case 'object': return 'Object (JSON format)';
      case 'enum': return 'Select from options';
      default: return type;
    }
  };

  // Use a different approach for tab switching to avoid scroll jumps
  const [currentScrollPos, setCurrentScrollPos] = useState(0);
  
  // Update active tab without using external ref
  const handleTabChange = (value: string) => {
    // Store current scroll position in state
    setCurrentScrollPos(window.scrollY);
    // Update tab
    setActiveTab(value);
  };
  
  // Use effect to restore scroll position after tab change
  useEffect(() => {
    if (currentScrollPos > 0) {
      // Reset scroll position after a minimal delay
      const timer = setTimeout(() => {
        window.scrollTo(0, currentScrollPos);
      }, 0);
      
      return () => clearTimeout(timer);
    }
  }, [activeTab, currentScrollPos]);

  // Simple renderer for code/JSON display
  const renderJSON = (data: any) => {
    return (
      <pre className="bg-muted p-4 rounded-md overflow-auto whitespace-pre-wrap max-h-[400px] text-sm font-mono text-foreground border border-border">
        {JSON.stringify(data, null, 2)}
      </pre>
    );
  };

  return (
    <div className="border rounded-md p-4">
      <h2 className="text-xl font-semibold mb-4">Action Test Console</h2>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="mb-4">
          <TabsTrigger value="parameters">
            <PlayIcon className="h-4 w-4 mr-2" />
            Parameters
          </TabsTrigger>
          <TabsTrigger value="request">
            <CodeIcon className="h-4 w-4 mr-2" />
            Request
          </TabsTrigger>
          <TabsTrigger value="response">
            <RefreshCcw className="h-4 w-4 mr-2" />
            Response
          </TabsTrigger>
          <TabsTrigger value="history">
            <ClockIcon className="h-4 w-4 mr-2" />
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="parameters" className="space-y-4">
          {executionError && (
            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
              <div className="flex">
                <div>
                  <p className="text-sm text-red-700">
                    {executionError}
                  </p>
                </div>
              </div>
            </div>
          )}
          
          <div className="mb-4 bg-blue-50 border-l-4 border-blue-400 p-4">
            <p className="text-sm text-blue-700">
              Test <span className="font-bold">{actionConfig?.method || 'GET'} {actionConfig?.endpoint || ''}</span>
            </p>
            {actionConfig?.description && (
              <p className="mt-1 text-sm text-blue-600">{actionConfig.description}</p>
            )}
          </div>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* Raw JSON option */}
              <FormField
                control={form.control}
                name="_raw"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Raw JSON Parameters (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='{
  "param1": "value1",
  "param2": 42
}'
                        className="font-mono"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter parameters as a JSON object, or use the form fields below.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="mb-2 mt-6">
                <h3 className="text-sm font-semibold">Parameter Fields</h3>
                <p className="text-xs text-muted-foreground">
                  Fill in the fields below, or use the raw JSON input above.
                </p>
              </div>
              
              {/* Dynamic form fields for each parameter */}
              {Object.entries(parameterSpec)
                .filter(([key]) => key !== '_raw') // Skip the raw input field
                .map(([name, spec]) => (
                  <FormField
                    key={name}
                    control={form.control}
                    name={name as any}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {name}
                          {spec.required && <span className="text-red-500 ml-1">*</span>}
                        </FormLabel>
                        <FormControl>
                          <>
                            {spec.type === 'boolean' ? (
                              <select
                                className="w-full p-2 border border-border rounded-md bg-background text-foreground"
                                {...field}
                                onChange={(e) => field.onChange(e.target.value === 'true')}
                              >
                                <option value="">Select...</option>
                                <option value="true">True</option>
                                <option value="false">False</option>
                              </select>
                            ) : spec.enum && spec.enum.length > 0 ? (
                              <select
                                className="w-full p-2 border border-border rounded-md bg-background text-foreground"
                                {...field}
                                onChange={(e) => field.onChange(e.target.value)}
                              >
                                <option value="">Select an option...</option>
                                {spec.enum.map((option: string) => (
                                  <option key={option} value={option}>
                                    {option}
                                  </option>
                                ))}
                              </select>
                            ) : spec.type === 'array' || spec.type === 'object' ? (
                              <Textarea
                                placeholder={`Enter ${spec.type} as JSON (e.g., ${spec.type === 'array' ? '["item1", "item2"]' : '{"key": "value"}'})`}
                                className="font-mono"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(e.target.value);
                                }}
                              />
                            ) : (
                              <Input
                                type={
                                  spec.type === 'number' || spec.type === 'integer' || 
                                  spec.type === 'float' || spec.type === 'double' ||
                                  spec.type === 'int32' || spec.type === 'int64' ? 'number' :
                                  spec.type === 'email' ? 'email' :
                                  spec.type === 'date' ? 'date' :
                                  spec.type === 'date-time' ? 'datetime-local' :
                                  'text'
                                }
                                placeholder={
                                  spec.example ? `Example: ${spec.example}` :
                                  spec.default !== undefined ? `Default: ${spec.default}` :
                                  `Enter ${getParameterTypeLabel(spec.type, spec.schema)}`
                                }
                                {...field}
                                onChange={(e) => {
                                  // Attempt to convert numbers if the type is number/integer
                                  if (['number', 'integer', 'float', 'double', 'int32', 'int64'].includes(spec.type)) {
                                    const num = parseFloat(e.target.value);
                                    field.onChange(!isNaN(num) ? num : e.target.value);
                                  } else {
                                    field.onChange(e.target.value);
                                  }
                                }}
                              />
                            )}
                          </>
                        </FormControl>
                        <FormDescription>
                          {spec.description}
                          {spec.format && (
                            <span className="block text-xs text-muted-foreground mt-1">
                              Format: {spec.format}
                            </span>
                          )}
                          {spec.default !== undefined && (
                            <span className="block text-xs text-muted-foreground mt-1">
                              Default: {String(spec.default)}
                            </span>
                          )}
                          {spec.example !== undefined && (
                            <span className="block text-xs text-muted-foreground mt-1">
                              Example: {String(spec.example)}
                            </span>
                          )}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}
              
              <div className="pt-4">
                <Button type="submit" disabled={isExecuting}>
                  {isExecuting ? (
                    <>
                      <RefreshCcw className="mr-2 h-4 w-4 animate-spin" />
                      Executing...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="mr-2 h-4 w-4" />
                      Execute Action
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>

        <TabsContent value="request">
          <div className="space-y-4">
            <div className="mb-2">
              <h3 className="text-sm font-medium">Request Details</h3>
              <p className="text-sm text-muted-foreground">
                {actionConfig?.method || 'GET'} {actionConfig?.endpoint || ''}
              </p>
            </div>
            {requestData ? (
              renderJSON(requestData)
            ) : (
              <div className="p-4 border border-dashed border-border rounded-md">
                <p className="text-muted-foreground text-center">
                  Execute the action to see the request details
                </p>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="response">
          <div className="space-y-4">
            {response ? (
              <>
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <h3 className="text-sm font-medium">Response</h3>
                    <p className="text-sm text-muted-foreground">
                      Status: {response.status_code || 'N/A'}
                      {response.cached && ' (Cached)'}
                    </p>
                  </div>
                  <div>
                    {response.success ? (
                      <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                        Success
                      </span>
                    ) : (
                      <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
                        Error
                      </span>
                    )}
                  </div>
                </div>
                {renderJSON(response.result || response.error || response)}
                {/* If headers are available, show them */}
                {response.headers && Object.keys(response.headers).length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Response Headers</h4>
                    <div className="bg-muted p-3 rounded-md max-h-40 overflow-y-auto border border-border">
                      {Object.entries(response.headers).map(([key, value]) => (
                        <div key={key} className="grid grid-cols-3 text-sm mb-1">
                          <span className="font-medium text-foreground">{key}:</span>
                          <span className="col-span-2 text-muted-foreground">{String(value)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="p-4 border border-dashed border-border rounded-md">
                <p className="text-muted-foreground text-center">
                  Execute the action to see the response
                </p>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="history">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Test History</h3>
            {historicalTests.length > 0 ? (
              <div className="space-y-4">
                {historicalTests.map((test, index) => (
                  <div key={index} className="border border-border rounded-md p-3">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <span className="text-xs text-muted-foreground">
                          {new Date(test.timestamp).toLocaleString()}
                        </span>
                        <h4 className="text-sm font-medium">
                          {test.request.action_config.method}{' '}
                          {test.request.action_config.endpoint}
                        </h4>
                      </div>
                      <div>
                        {test.response.success ? (
                          <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                            Success
                          </span>
                        ) : (
                          <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
                            Error
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      <div>
                        <h5 className="text-xs font-medium mb-1">Parameters</h5>
                        <pre className="text-xs bg-muted p-2 rounded-md overflow-auto max-h-20 text-foreground border border-border">
                          {JSON.stringify(test.request.parameters, null, 2)}
                        </pre>
                      </div>
                      <div>
                        <h5 className="text-xs font-medium mb-1">Response</h5>
                        <pre className="text-xs bg-muted p-2 rounded-md overflow-auto max-h-20 text-foreground border border-border">
                          {JSON.stringify(
                            test.response.result || test.response.error,
                            null,
                            2
                          )}
                        </pre>
                      </div>
                    </div>
                    <div className="mt-2 flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Store current scroll position
                          const scrollPos = window.scrollY;
                          
                          // Load this test's parameters into the form
                          if (test.request.parameters) {
                            // Set raw parameters as string
                            form.setValue(
                              '_raw',
                              JSON.stringify(test.request.parameters, null, 2)
                            );
                            
                            // Save to reference to ensure persistence
                            formValuesRef.current = {
                              _raw: JSON.stringify(test.request.parameters, null, 2)
                            };
                            
                            // Store current scroll position
                            setCurrentScrollPos(window.scrollY);
                            
                            // Switch to parameters tab
                            setActiveTab('parameters');
                          }
                        }}
                      >
                        <RefreshCcw className="mr-1 h-3 w-3" />
                        Rerun
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 border border-dashed border-border rounded-md">
                <p className="text-muted-foreground text-center">No test history available</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ActionTestConsole;