'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Use dynamic import to avoid hydration issues
const Editor = dynamic(() => import('@monaco-editor/react').then(mod => mod.Editor), {
  ssr: false
});

interface TemplateEditorProps {
  initialValue: string;
  onChange?: (value: string) => void;
  language?: string;
  height?: string;
  theme?: 'vs-dark' | 'light';
  readOnly?: boolean;
}

/**
 * Monaco-based code editor for editing templates
 */
const TemplateEditor: React.FC<TemplateEditorProps> = ({
  initialValue,
  onChange,
  language = 'json',
  height = '500px',
  theme = 'light',
  readOnly = false
}) => {
  const [editorValue, setEditorValue] = useState(initialValue);

  useEffect(() => {
    setEditorValue(initialValue);
  }, [initialValue]);

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      setEditorValue(value);
      if (onChange) {
        onChange(value);
      }
    }
  };

  return (
    <div className="border rounded-md overflow-hidden">
      <Editor
        height={height}
        language={language}
        value={editorValue}
        theme={theme === 'vs-dark' ? 'vs-dark' : 'vs-light'}
        onChange={handleEditorChange}
        options={{
          minimap: { enabled: true },
          fontSize: 14,
          wordWrap: 'on',
          readOnly,
          automaticLayout: true,
          scrollBeyondLastLine: false,
          lineNumbers: 'on',
          scrollbar: {
            vertical: 'auto',
            horizontal: 'auto'
          }
        }}
      />
    </div>
  );
};

export default TemplateEditor;