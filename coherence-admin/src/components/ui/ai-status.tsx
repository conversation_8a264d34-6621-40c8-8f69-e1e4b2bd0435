import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const aiStatusVariants = cva(
  "inline-flex items-center gap-2 rounded-full px-3 py-1 text-xs font-mono font-medium transition-all duration-300",
  {
    variants: {
      status: {
        idle: "bg-cw-n-black/50 text-cw-n-white border border-cw-n-black",
        processing: "bg-cw-accent-left/20 text-cw-accent-left border border-cw-accent-left/40 ai-processing",
        active: "bg-cw-n-green/20 text-cw-n-green border border-cw-n-green/40 ai-status-active",
        error: "bg-cw-n-red/20 text-cw-n-red border border-cw-n-red/40",
        warning: "bg-cw-n-yellow/20 text-cw-n-yellow border border-cw-n-yellow/40",
        thinking: "bg-cw-accent-right/20 text-cw-accent-right border border-cw-accent-right/40 animate-pulse-ai",
      },
      size: {
        sm: "text-xs px-2 py-1",
        default: "text-xs px-3 py-1",
        lg: "text-sm px-4 py-2",
      },
    },
    defaultVariants: {
      status: "idle",
      size: "default",
    },
  }
)

export interface AIStatusProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof aiStatusVariants> {
  children?: React.ReactNode
}

function AIStatus({ className, status, size, children, ...props }: AIStatusProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return (
          <div className="w-2 h-2 bg-current rounded-full animate-ai-scan relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent animate-ai-scan"></div>
          </div>
        )
      case 'active':
        return <div className="w-2 h-2 bg-current rounded-full animate-pulse-ai"></div>
      case 'thinking':
        return (
          <div className="flex gap-0.5">
            <div className="w-1 h-1 bg-current rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
            <div className="w-1 h-1 bg-current rounded-full animate-pulse" style={{ animationDelay: '150ms' }}></div>
            <div className="w-1 h-1 bg-current rounded-full animate-pulse" style={{ animationDelay: '300ms' }}></div>
          </div>
        )
      case 'error':
        return <div className="w-2 h-2 bg-current rounded-full"></div>
      case 'warning':
        return <div className="w-2 h-2 bg-current rounded-full"></div>
      case 'idle':
      default:
        return <div className="w-2 h-2 bg-current rounded-full opacity-50"></div>
    }
  }

  const getStatusText = () => {
    if (children) return children
    
    switch (status) {
      case 'processing': return 'PROCESSING'
      case 'active': return 'ACTIVE'
      case 'thinking': return 'THINKING'
      case 'error': return 'ERROR'
      case 'warning': return 'WARNING'
      case 'idle': 
      default: return 'IDLE'
    }
  }

  return (
    <div className={cn(aiStatusVariants({ status, size, className }))} {...props}>
      {getStatusIcon()}
      <span className="font-display tracking-wider">
        {getStatusText()}
      </span>
    </div>
  )
}

export { AIStatus, aiStatusVariants }