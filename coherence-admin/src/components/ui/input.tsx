import * as React from "react"

import { cn } from "@/lib/utils"

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-cw-foreground/40 selection:bg-cw-accent-left selection:text-cw-foreground bg-cw-bg-top/80 border-cw-b-cyan/60 flex h-9 w-full min-w-0 rounded-md border backdrop-blur-sm px-3 py-1 text-base text-cw-foreground shadow-sm transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-cw-bg-top/50 md:text-sm font-mono",
        "focus-visible:border-cw-accent-left focus-visible:ring-cw-accent-left/30 focus-visible:ring-[3px]",
        "aria-invalid:ring-cw-n-red/30 aria-invalid:border-cw-n-red",
        className
      )}
      {...props}
    />
  )
}

export { Input }
