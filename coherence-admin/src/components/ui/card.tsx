import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const cardVariants = cva(
  "rounded-lg shadow-md transition-all duration-300",
  {
    variants: {
      variant: {
        default: "bg-cw-bg-top text-cw-foreground border border-cw-b-cyan/50",
        gradient: "bg-gradient-to-br from-cw-bg-top to-cw-bg-bottom/70 text-cw-foreground border border-cw-b-cyan/50",
        outline: "bg-transparent border border-cw-b-cyan/80 text-cw-foreground backdrop-blur-sm",
        cyber: "relative bg-cw-bg-top/80 backdrop-blur-sm border border-cw-b-cyan/70 text-cw-foreground overflow-hidden group",
        accent: "bg-cw-bg-top/90 border border-cw-accent-left/50 text-cw-foreground",
        secondary: "bg-cw-bg-top/90 border border-cw-accent-right/50 text-cw-foreground",
        ghost: "bg-transparent border-transparent text-cw-foreground/90",
        destructive: "bg-cw-bg-top/90 border border-cw-n-red/50 text-cw-n-red",
        // AI-First Enhanced Variants
        glass: "glass-morphism text-cw-foreground hover:glass-morphism-strong group relative overflow-hidden",
        "glass-strong": "glass-morphism-strong text-cw-foreground hover:shadow-3xl group relative overflow-hidden",
        "glass-subtle": "glass-morphism-subtle text-cw-foreground hover:glass-morphism group relative overflow-hidden",
        "ai-processing": "bg-cw-bg-top/90 border border-cw-accent-left/70 text-cw-foreground ai-processing relative overflow-hidden",
        "ai-active": "bg-cw-bg-top text-cw-foreground border border-cw-n-green/60 ai-status-active relative",
        "neural": "bg-cw-bg-top/95 text-cw-foreground border border-cw-b-cyan/40 neural-grid relative overflow-hidden",
        "data-flow": "bg-cw-bg-top/90 text-cw-foreground border border-cw-accent-right/60 data-flow relative overflow-hidden",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
        xl: "p-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {}

function Card({ className, variant, size, ...props }: CardProps) {
  // Special elements for the cyber variant
  if (variant === 'cyber') {
    return (
      <div className={cn(cardVariants({ variant, size, className }))} {...props}>
        {props.children}
        <div className="absolute inset-0 bg-gradient-cw-accent opacity-0 group-hover:opacity-10 transition-opacity duration-500 pointer-events-none"></div>
        <div className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-cw-accent-left to-cw-accent-right transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500 pointer-events-none"></div>
      </div>
    )
  }

  // Enhanced glassmorphism variants
  if (variant === 'glass' || variant === 'glass-strong' || variant === 'glass-subtle') {
    return (
      <div className={cn(cardVariants({ variant, size, className }))} {...props}>
        {props.children}
        <div className="absolute inset-0 bg-gradient-to-br from-cw-accent-left/5 to-cw-accent-right/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none rounded-lg"></div>
        <div className="absolute -inset-1 bg-gradient-to-r from-cw-accent-left/20 to-cw-accent-right/20 opacity-0 group-hover:opacity-30 transition-opacity duration-700 pointer-events-none rounded-lg blur-xl -z-10"></div>
      </div>
    )
  }

  // AI processing status indicator
  if (variant === 'ai-processing') {
    return (
      <div className={cn(cardVariants({ variant, size, className }))} {...props}>
        {props.children}
        <div className="absolute top-2 right-2 w-2 h-2 bg-cw-accent-left rounded-full animate-pulse-ai"></div>
        <div className="absolute bottom-0 left-0 h-0.5 w-full bg-gradient-to-r from-transparent via-cw-accent-left to-transparent animate-ai-scan"></div>
      </div>
    )
  }

  // AI active status with enhanced indicators
  if (variant === 'ai-active') {
    return (
      <div className={cn(cardVariants({ variant, size, className }))} {...props}>
        {props.children}
        <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-cw-n-green/50 via-cw-n-green to-cw-n-green/50"></div>
      </div>
    )
  }

  // Neural network pattern background
  if (variant === 'neural') {
    return (
      <div className={cn(cardVariants({ variant, size, className }))} {...props}>
        {props.children}
        <div className="absolute inset-0 opacity-20 animate-neural-pulse pointer-events-none">
          <div className="absolute top-4 left-4 w-1 h-1 bg-cw-accent-left rounded-full"></div>
          <div className="absolute top-8 right-6 w-1 h-1 bg-cw-accent-right rounded-full"></div>
          <div className="absolute bottom-6 left-8 w-1 h-1 bg-cw-n-cyan rounded-full"></div>
          <div className="absolute bottom-4 right-4 w-1 h-1 bg-cw-n-magenta rounded-full"></div>
        </div>
      </div>
    )
  }

  // Data flow visualization
  if (variant === 'data-flow') {
    return (
      <div className={cn(cardVariants({ variant, size, className }))} {...props}>
        {props.children}
        <div className="absolute right-2 top-2 bottom-2 w-1 bg-gradient-to-b from-transparent via-cw-accent-right to-transparent animate-data-stream"></div>
      </div>
    )
  }
  
  // Special elements for accent/secondary variants
  if (variant === 'accent' || variant === 'secondary') {
    const gradientColors = variant === 'accent' 
      ? 'from-cw-accent-left/10 to-transparent'
      : 'from-cw-accent-right/10 to-transparent';
      
    return (
      <div className={cn(cardVariants({ variant, size, className }))} {...props}>
        {props.children}
        <div className={`absolute inset-0 bg-gradient-to-r ${gradientColors} opacity-40 pointer-events-none rounded-lg`}></div>
      </div>
    )
  }

  return (
    <div className={cn(cardVariants({ variant, size, className }))} {...props} />
  )
}

function CardHeader({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("flex flex-col space-y-1.5", className)}
      {...props}
    />
  )
}

function CardTitle({
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h3
      className={cn("font-display text-2xl font-semibold leading-none tracking-tight", className)}
      {...props}
    />
  )
}

function CardDescription({
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p
      className={cn("text-sm text-cw-foreground/70 font-mono", className)}
      {...props}
    />
  )
}

function CardContent({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return <div className={cn("pt-6", className)} {...props} />
}

function CardFooter({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("flex items-center pt-6", className)}
      {...props}
    />
  )
}

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, cardVariants }