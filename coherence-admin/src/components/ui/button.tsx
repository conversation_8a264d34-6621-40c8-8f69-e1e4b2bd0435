import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border border-transparent",
  {
    variants: {
      variant: {
        default:
          "bg-cw-accent-left text-cw-foreground shadow-md hover:bg-gradient-to-tr hover:from-cw-accent-left hover:to-cw-accent-right border-cw-accent-left/50",
        destructive:
          "bg-cw-n-red text-cw-foreground shadow-md hover:bg-cw-n-red/90 focus-visible:ring-cw-n-red/20 border-cw-n-red/50",
        outline:
          "border bg-cw-bg-top/30 backdrop-blur-sm shadow-md hover:bg-cw-accent-left/10 hover:text-cw-foreground border-cw-b-cyan/60 text-cw-foreground/90",
        secondary:
          "bg-cw-accent-right text-cw-foreground shadow-md hover:bg-cw-accent-right/80 border-cw-accent-right/50",
        ghost:
          "hover:bg-cw-accent-left/10 hover:text-cw-foreground hover:border-cw-b-cyan/30 text-cw-foreground/90",
        link: "text-cw-accent-left underline-offset-4 hover:underline hover:text-cw-accent-right border-0 transition-colors",
        cyber: 
          "relative bg-cw-bg-top/80 text-cw-foreground shadow-md border border-cw-b-cyan/60 overflow-hidden group backdrop-blur-sm",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  }) {
  const Comp = asChild ? Slot : "button"

  // For cyber variant, add special effects
  if (variant === 'cyber') {
    return (
      <Comp
        data-slot="button"
        className={cn(buttonVariants({ variant, size, className }))}
        {...props}
      >
        {props.children}
        <span className="absolute inset-0 bg-gradient-to-tr from-cw-accent-left to-cw-accent-right opacity-0 group-hover:opacity-20 transition-opacity duration-500"></span>
        <span className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-cw-accent-left to-cw-accent-right transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></span>
      </Comp>
    )
  }

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants }
