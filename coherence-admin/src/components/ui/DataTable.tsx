"use client"

import * as React from "react"
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  // Add these if you want to implement sorting, filtering, pagination later
  // getSortedRowModel,
  // getFilteredRowModel,
  // getPaginationRowModel,
  // SortingState,
  // ColumnFiltersState,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  loading?: boolean; // Add loading prop
  // You might also want to add an error prop later as per PRD:
  // error?: Error | null;
  onRowHover?: (row: TData | null) => void; // Add callback for row hover
  onRowClick?: (row: TData) => void; // Add callback for row click
}

export function DataTable<TData, TValue>({
  columns,
  data,
  loading, // Destructure loading prop
  onRowHover, // Destructure onRowHover callback
  onRowClick, // Destructure onRowClick callback
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Add these if you implement more features:
    // onSortingChange: setSorting, // if you add sorting state
    // getSortedRowModel: getSortedRowModel(),
    // onColumnFiltersChange: setColumnFilters, // if you add filtering state
    // getFilteredRowModel: getFilteredRowModel(),
    // getPaginationRowModel: getPaginationRowModel(), // if you add pagination
    // state: {
    //   sorting, // if you add sorting state
    //   columnFilters, // if you add filtering state
    // },
  })

  // Handle loading state
  if (loading) {
    // Cyberpunk-styled skeleton loader
    return (
      <div className="rounded-md border border-cw-b-cyan/50 p-4 bg-cw-bg-top/80 backdrop-blur-sm">
        <div className="animate-pulse">
          <div className="h-8 bg-cw-accent-left/20 rounded w-full mb-4"></div> {/* Header placeholder */}
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-10 bg-cw-accent-right/10 rounded w-full mb-2 border border-cw-b-cyan/20"></div> /* Row placeholder */
          ))}
        </div>
      </div>
    );
  }
  
  // TODO: Optionally handle error state here if you add an error prop
  // if (error) { ... }

  // Log the columns and data for debugging
  console.log('DataTable columns:', columns);
  console.log('DataTable data sample:', data.length > 0 ? data[0] : 'No data');

  return (
    <div className="rounded-md border border-cw-b-cyan/50 card-cyberpunk p-0 overflow-hidden">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id} className="bg-gradient-to-r from-cw-bg-top to-cw-bg-top/95">
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id} className="whitespace-nowrap text-cw-foreground/90">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                )
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
                className={`hover:bg-cw-accent-left/10 transition-colors ${onRowClick ? 'cursor-pointer' : ''}`}
                onMouseEnter={() => onRowHover?.(row.original)}
                onMouseLeave={() => onRowHover?.(null)}
                onClick={() => onRowClick?.(row.original)}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id} className="font-mono text-cw-foreground/80">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center font-mono text-cw-foreground/60">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {/* DataTableToolbar and DataTablePagination would go here or outside if you add them later */}
    </div>
  )
}
