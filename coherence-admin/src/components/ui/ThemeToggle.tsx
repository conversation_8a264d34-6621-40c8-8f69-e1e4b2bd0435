"use client";

import { useTheme } from "@/context/ThemeProvider";
import { Moon, Sun } from "lucide-react";

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className="relative group p-2 rounded-full border border-cw-b-cyan overflow-hidden transition-all duration-300"
      aria-label="Toggle theme"
    >
      <div className="absolute inset-0 bg-gradient-cw-accent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      
      <div className="relative z-10">
        {theme === "dark" ? (
          <Sun className="h-5 w-5 text-foreground transition-transform duration-300 group-hover:rotate-90" />
        ) : (
          <Moon className="h-5 w-5 text-foreground transition-transform duration-300 group-hover:-rotate-90" />
        )}
      </div>
      
      <span className="absolute bottom-0 left-0 h-0.5 w-full bg-gradient-to-r from-primary to-accent transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></span>
    </button>
  );
}