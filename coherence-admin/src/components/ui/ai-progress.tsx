import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const progressVariants = cva(
  "relative w-full overflow-hidden rounded-full transition-all duration-300",
  {
    variants: {
      variant: {
        default: "bg-cw-n-black/50",
        neural: "bg-cw-n-black/50 neural-grid",
        gradient: "bg-gradient-to-r from-cw-bg-top to-cw-n-black/50",
        glow: "bg-cw-n-black/50 shadow-lg",
      },
      size: {
        sm: "h-1",
        default: "h-2",
        lg: "h-3",
        xl: "h-4",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const progressBarVariants = cva(
  "h-full transition-all duration-500 ease-out relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-cw-accent-left to-cw-accent-right",
        neural: "bg-gradient-to-r from-cw-accent-left via-cw-n-cyan to-cw-accent-right",
        gradient: "bg-gradient-to-r from-cw-accent-left via-cw-n-magenta to-cw-accent-right",
        glow: "bg-gradient-to-r from-cw-accent-left to-cw-accent-right shadow-[0_0_10px_rgba(0,121,114,0.5)]",
        processing: "bg-gradient-to-r from-cw-accent-left to-cw-accent-right ai-processing",
        thinking: "bg-gradient-to-r from-cw-accent-right via-cw-n-magenta to-cw-accent-left animate-data-stream",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface AIProgressProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'children'>,
    VariantProps<typeof progressVariants> {
  value?: number
  max?: number
  indeterminate?: boolean
  label?: string
  showValue?: boolean
}

function AIProgress({ 
  className, 
  variant, 
  size, 
  value = 0, 
  max = 100, 
  indeterminate = false,
  label,
  showValue = false,
  ...props 
}: AIProgressProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  
  const getProgressVariant = () => {
    if (indeterminate) return 'processing'
    if (variant === 'neural') return 'neural'
    if (variant === 'gradient') return 'gradient'
    if (variant === 'glow') return 'glow'
    return 'default'
  }

  return (
    <div className="space-y-2">
      {(label || showValue) && (
        <div className="flex justify-between items-center">
          {label && (
            <span className="text-sm font-mono text-cw-foreground/80 font-display tracking-wider">
              {label}
            </span>
          )}
          {showValue && !indeterminate && (
            <span className="text-xs font-mono text-cw-accent-left font-semibold">
              {Math.round(percentage)}%
            </span>
          )}
          {indeterminate && (
            <span className="text-xs font-mono text-cw-accent-left font-semibold animate-pulse">
              PROCESSING...
            </span>
          )}
        </div>
      )}
      
      <div className={cn(progressVariants({ variant, size, className }))} {...props}>
        <div
          className={cn(progressBarVariants({ variant: getProgressVariant() }))}
          style={{
            width: indeterminate ? '100%' : `${percentage}%`,
          }}
        >
          {/* Animated shimmer effect for active processing */}
          {(indeterminate || percentage > 0) && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-ai-scan"></div>
          )}
          
          {/* Neural network dots for neural variant */}
          {variant === 'neural' && (
            <div className="absolute inset-0 flex items-center justify-around opacity-60">
              <div className="w-1 h-1 bg-cw-n-cyan rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
              <div className="w-1 h-1 bg-cw-n-magenta rounded-full animate-pulse" style={{ animationDelay: '200ms' }}></div>
              <div className="w-1 h-1 bg-cw-n-yellow rounded-full animate-pulse" style={{ animationDelay: '400ms' }}></div>
            </div>
          )}
          
          {/* Glow effect for glow variant */}
          {variant === 'glow' && (
            <div className="absolute inset-0 bg-gradient-to-r from-cw-accent-left/30 to-cw-accent-right/30 blur-sm animate-pulse"></div>
          )}
        </div>
        
        {/* Background pattern for neural variant */}
        {variant === 'neural' && (
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 left-1/4 w-px h-full bg-cw-accent-left"></div>
            <div className="absolute top-0 left-2/4 w-px h-full bg-cw-accent-right"></div>
            <div className="absolute top-0 left-3/4 w-px h-full bg-cw-n-cyan"></div>
          </div>
        )}
      </div>
    </div>
  )
}

export { AIProgress, progressVariants, progressBarVariants }