'use client';

import React, { useState, useRef, useEffect } from 'react';

interface ChatInputProps {
  onSendMessage: (message: string) => Promise<void>;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

/**
 * Input component for the chat interface
 * 
 * Provides a text input and send button for user messages
 */
const ChatInput: React.FC<ChatInputProps> = ({ 
  onSendMessage, 
  isLoading = false,
  disabled = false,
  placeholder = "Type a message..."
}) => {
  const [message, setMessage] = useState('');
  const inputRef = useRef<HTMLTextAreaElement>(null);
  
  // Auto-focus the input when the component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);
  
  // Handle message submission
  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    
    if (!message.trim() || isLoading || disabled) {
      return;
    }
    
    // Send the message
    await onSendMessage(message);
    
    // Clear the input
    setMessage('');
  };
  
  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };
  
  // Auto-resize the input as the user types, using requestAnimationFrame for better performance
  const handleTextareaInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const target = e.target;
    window.requestAnimationFrame(() => {
      target.style.height = '0';
      target.style.height = `${Math.min(target.scrollHeight, 150)}px`;
    });
  };
  
  return (
    <form 
      onSubmit={handleSubmit} 
      className="flex items-end gap-2"
      aria-label="Message input form"
    >
      <div className="relative flex-1">
        <textarea
          ref={inputRef}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          onInput={handleTextareaInput}
          placeholder={placeholder}
          disabled={isLoading || disabled}
          aria-label="Message input"
          aria-placeholder={placeholder}
          aria-disabled={isLoading || disabled}
          className="w-full border border-border bg-background text-foreground rounded-lg py-3 px-4 pr-10 resize-none overflow-y-hidden max-h-[150px] min-h-[45px] focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary disabled:bg-muted disabled:text-muted-foreground"
          style={{ height: '45px', overflowY: message.split('\n').length > 1 || message.length > 100 ? 'auto' : 'hidden' }}
          rows={1}
        />
        {(isLoading || disabled) && (
          <div className="sr-only" aria-live="assertive">
            {isLoading ? 'Sending message...' : 'Input is currently disabled'}
          </div>
        )}
      </div>
      
      <button
        type="submit"
        disabled={!message.trim() || isLoading || disabled}
        className={`px-6 h-[45px] rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${
          !message.trim() || isLoading || disabled
            ? 'bg-muted text-muted-foreground cursor-not-allowed'
            : 'bg-primary text-primary-foreground hover:bg-primary/90'
        } transition-colors flex items-center justify-center`}
        aria-label={isLoading ? 'Sending message' : 'Send message'}
      >
        {isLoading ? (
          <div 
            className="h-5 w-5 rounded-full border-2 border-primary-foreground border-t-transparent animate-spin"
            role="status"
            aria-label="Loading"
          ></div>
        ) : (
          <span>Send</span>
        )}
      </button>
      
      {/* Keyboard shortcut hint for screen readers */}
      <div className="sr-only">
        Press Enter to send. Use Shift+Enter for a new line.
      </div>
    </form>
  );
};

export default ChatInput;