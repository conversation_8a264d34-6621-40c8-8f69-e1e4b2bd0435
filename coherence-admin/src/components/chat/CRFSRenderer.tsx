import React, { useState } from 'react';
import { cn } from '@/lib/utils';

interface CRFSSection {
  id: string;
  type: 'text' | 'conditional' | 'list' | 'object' | 'json';
  title?: string;
  style?: 'normal' | 'heading' | 'success' | 'error' | 'info' | 'warning';
  content?: string;
  data_path?: string;
  conditionals?: {
    show_if?: string;
    hide_if?: string;
  };
  item_format?: {
    template: string;
    style: 'bullet' | 'numbered';
  };
  fields?: Array<{
    key: string;
    label: string;
  }>;
  show_all?: boolean;
  indent?: number;
}

interface CRFSMetadata {
  version: string;
  kind: string;
  available_formats?: string[];
  auto_select_enabled?: boolean;
  default_format?: string;
  supports_streaming?: boolean;
  supports_mixed_formats?: boolean;
}

interface CRFSRendererProps {
  content: string;
  sections?: CRFSSection[];
  metadata?: CRFSMetadata;
  rawResponse?: any;
  className?: string;
}

const CRFSRenderer: React.FC<CRFSRendererProps> = ({
  content,
  sections,
  metadata,
  rawResponse,
  className
}) => {
  const [viewMode, setViewMode] = useState<'formatted' | 'raw'>('formatted');
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const renderSection = (section: CRFSSection) => {
    const isExpanded = expandedSections.has(section.id);
    
    return (
      <div key={section.id} className="crfs-section mb-6">
        {section.title && (
          <div
            className={cn(
              "crfs-header flex items-center justify-between cursor-pointer",
              "text-cyan-400 hover:text-cyan-300 transition-colors",
              "mb-3"
            )}
            onClick={() => toggleSection(section.id)}
          >
            <h3 className="text-lg font-semibold">{section.title}</h3>
            <span className="text-sm">
              {isExpanded ? '▼' : '▶'}
            </span>
          </div>
        )}
        
        <div className={cn(
          "crfs-content transition-all duration-300",
          !section.title || isExpanded ? "block" : "hidden"
        )}>
          {renderSectionContent(section)}
        </div>
      </div>
    );
  };

  const renderSectionContent = (section: CRFSSection) => {
    const baseClasses = "crfs-content-wrapper";
    
    switch (section.type) {
      case 'text':
        return (
          <div className={cn(baseClasses, getSectionStyleClasses(section.style))}>
            <div className="prose prose-invert max-w-none">
              <p className="text-gray-300">{section.content}</p>
            </div>
          </div>
        );
      
      case 'json':
        return (
          <div className={cn(baseClasses, "crfs-json-section")}>
            <pre className={cn(
              "bg-gray-900/80 border border-purple-500/30 rounded-lg p-4",
              "text-green-400 font-mono text-sm overflow-x-auto",
              "hover:border-purple-400/50 transition-all duration-300"
            )}>
              <code>{JSON.stringify(rawResponse, null, section.indent || 2)}</code>
            </pre>
          </div>
        );
      
      case 'list':
        return (
          <div className={cn(baseClasses, "crfs-list-section")}>
            <ul className="space-y-2">
              {/* List rendering would need actual data from rawResponse */}
              <li className="flex items-start space-x-2">
                <span className="text-cyan-400 mt-1">▸</span>
                <span className="text-gray-300">List item content</span>
              </li>
            </ul>
          </div>
        );
      
      case 'object':
        return (
          <div className={cn(baseClasses, "crfs-object-section")}>
            <div className="space-y-2">
              {section.fields?.map((field) => (
                <div key={field.key} className="flex items-start space-x-3">
                  <span className="text-purple-400 font-semibold min-w-0 flex-shrink-0">
                    {field.label}:
                  </span>
                  <span className="text-gray-300">
                    {/* Field value would come from rawResponse */}
                    Field value
                  </span>
                </div>
              ))}
            </div>
          </div>
        );
      
      default:
        return (
          <div className={cn(baseClasses, "text-gray-300")}>
            {section.content || 'No content available'}
          </div>
        );
    }
  };

  const getSectionStyleClasses = (style?: string) => {
    switch (style) {
      case 'heading':
        return "text-2xl font-bold text-cyan-400 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent";
      case 'success':
        return "text-green-400 bg-green-900/20 border-l-4 border-green-500 pl-4 rounded-r-lg";
      case 'error':
        return "text-red-400 bg-red-900/20 border-l-4 border-red-500 pl-4 rounded-r-lg";
      case 'info':
        return "text-blue-400 bg-blue-900/20 border-l-4 border-blue-500 pl-4 rounded-r-lg";
      case 'warning':
        return "text-yellow-400 bg-yellow-900/20 border-l-4 border-yellow-500 pl-4 rounded-r-lg";
      default:
        return "";
    }
  };

  const renderRawView = () => {
    return (
      <div className="crfs-raw-view">
        <pre className={cn(
          "bg-gray-900/80 border border-purple-500/30 rounded-lg p-4",
          "text-green-400 font-mono text-sm overflow-x-auto whitespace-pre-wrap",
          "hover:border-purple-400/50 transition-all duration-300"
        )}>
          <code>{typeof content === 'string' ? content : JSON.stringify(content, null, 2)}</code>
        </pre>
      </div>
    );
  };

  const renderFormattedView = () => {
    if (sections && sections.length > 0) {
      return (
        <div className="crfs-formatted-view space-y-4">
          {sections.map(renderSection)}
        </div>
      );
    }
    
    // Fallback to content rendering
    return (
      <div className="crfs-formatted-fallback">
        <div className="prose prose-invert max-w-none">
          <div className="text-gray-300 whitespace-pre-wrap">{content}</div>
        </div>
      </div>
    );
  };

  return (
    <div className={cn("crfs-renderer", className)}>
      {/* CRFS Header with metadata and controls */}
      <div className="crfs-header-controls flex justify-between items-center mb-4">
        <div className="crfs-metadata flex items-center space-x-4">
          {metadata && (
            <>
              <span className="text-xs text-cyan-400 font-mono">
                CRFS v{metadata.version}
              </span>
              {metadata.auto_select_enabled && (
                <span className="text-xs text-purple-400 bg-purple-900/20 px-2 py-1 rounded">
                  Auto-select
                </span>
              )}
              {metadata.supports_streaming && (
                <span className="text-xs text-blue-400 bg-blue-900/20 px-2 py-1 rounded">
                  Streaming
                </span>
              )}
            </>
          )}
        </div>
        
        <div className="crfs-view-controls">
          <button
            onClick={() => setViewMode(viewMode === 'formatted' ? 'raw' : 'formatted')}
            className={cn(
              "text-xs px-3 py-1 rounded border transition-all duration-200",
              "hover:bg-gray-800/50",
              viewMode === 'formatted'
                ? "text-cyan-400 border-cyan-500/30 bg-cyan-900/10"
                : "text-purple-400 border-purple-500/30 bg-purple-900/10"
            )}
          >
            {viewMode === 'formatted' ? 'Show Raw' : 'Show Formatted'}
          </button>
        </div>
      </div>

      {/* CRFS Content */}
      <div className="crfs-content">
        {viewMode === 'formatted' ? renderFormattedView() : renderRawView()}
      </div>
    </div>
  );
};

export default CRFSRenderer;