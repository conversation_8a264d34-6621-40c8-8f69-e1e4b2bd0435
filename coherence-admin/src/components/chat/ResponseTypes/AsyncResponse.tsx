'use client';

import React from 'react';

interface AsyncResponseProps {
  workflowId: string;
  statusUrl: string;
}

/**
 * Component for rendering an "async" type response
 * 
 * Displays information about an asynchronous operation
 */
const AsyncResponse: React.FC<AsyncResponseProps> = ({
  workflowId,
  statusUrl
}) => {
  return (
    <div 
      className="mt-4 bg-blue-50 p-4 rounded-lg border border-blue-200"
      role="status"
      aria-live="polite"
    >
      <div className="flex items-center gap-3 mb-2">
        <div 
          className="h-5 w-5 rounded-full border-2 border-blue-600 border-t-transparent animate-spin"
          role="progressbar"
          aria-label="Operation in progress"
          aria-valuenow={0}
          aria-valuemin={0}
          aria-valuemax={100}
        ></div>
        <h4 className="text-sm font-medium text-blue-800" id="async-heading">
          Working on your request...
        </h4>
      </div>
      
      <p className="text-xs text-blue-600 mb-1" id="async-description">
        This operation is running in the background and may take some time to complete.
      </p>
      
      <div className="text-xs text-gray-500 flex flex-wrap gap-x-3">
        <span className="sr-only">Workflow ID:</span>
        <span>{workflowId}</span>
      </div>
    </div>
  );
};

export default AsyncResponse;