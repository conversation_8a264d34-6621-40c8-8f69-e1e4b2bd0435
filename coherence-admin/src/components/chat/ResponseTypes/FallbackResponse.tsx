'use client';

import React from 'react';

interface FallbackResponseProps {
  message: string;
  suggestions?: string[];
}

/**
 * Component for rendering a "fallback" type response
 * 
 * Displays an error message when the system couldn't understand the request
 */
const FallbackResponse: React.FC<FallbackResponseProps> = ({
  message,
  suggestions = []
}) => {
  return (
    <div 
      className="mt-4 bg-black/40 p-4 rounded-lg border border-cw-n-yellow/60 backdrop-blur-sm"
      role="alert"
      aria-labelledby="fallback-heading"
    >
      <div className="flex items-start gap-2 mb-2">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          fill="none" 
          viewBox="0 0 24 24" 
          strokeWidth={1.5} 
          stroke="currentColor" 
          className="w-5 h-5 text-cw-n-yellow mt-0.5"
          aria-hidden="true"
        >
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
        </svg>
        <div>
          <h4 id="fallback-heading" className="text-sm font-mono font-medium text-cw-n-yellow">
            I couldn't understand that request
          </h4>
          <p className="text-xs text-cw-b-white mt-1">{message}</p>
        </div>
      </div>
      
      {suggestions.length > 0 && (
        <div className="mt-3">
          <h5 id="suggestions-heading" className="text-xs font-mono font-medium text-cw-n-cyan mb-1">Try asking:</h5>
          <ul 
            className="text-xs text-cw-b-white space-y-1 pl-5 list-disc"
            aria-labelledby="suggestions-heading"
          >
            {suggestions.map((suggestion, index) => (
              <li key={index}>{suggestion}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FallbackResponse;