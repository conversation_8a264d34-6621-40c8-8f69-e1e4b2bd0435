'use client';

import React from 'react';

interface Option {
  id: string;
  text: string;
}

interface ClarificationResponseProps {
  options: Option[];
  onSelect: (intentId: string) => Promise<void>;
}

/**
 * Component for rendering an "intent_clarification" type response
 * 
 * Displays options for the user to choose from when intent is ambiguous
 */
const ClarificationResponse: React.FC<ClarificationResponseProps> = ({
  options,
  onSelect
}) => {
  const [selectedId, setSelectedId] = React.useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleSelect = async (id: string) => {
    if (isSubmitting) return;
    
    setSelectedId(id);
    setIsSubmitting(true);
    
    try {
      await onSelect(id);
    } catch (error) {
      console.error('Error selecting intent:', error);
      setSelectedId(null);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!options || options.length === 0) {
    return null;
  }

  return (
    <div 
      className="mt-4 bg-cw-bg-top/90 p-4 rounded-lg border border-cw-b-cyan/60 backdrop-blur-sm"
      role="radiogroup"
      aria-labelledby="clarification-heading"
      aria-describedby="clarification-description"
    >
      <h4 
        id="clarification-heading"
        className="text-sm font-mono font-medium text-cw-foreground mb-2"
      >
        Did you mean:
      </h4>
      
      <div id="clarification-description" className="sr-only">
        Select one of the following options to clarify your intention.
      </div>
      
      <div className="space-y-2">
        {options.map((option) => (
          <button
            key={option.id}
            className={`w-full text-left p-3 rounded-lg text-sm font-mono transition-colors ${
              selectedId === option.id
                ? 'bg-cw-accent-left text-cw-foreground border border-cw-accent-left/70'
                : 'bg-cw-bg-top/80 border border-cw-b-cyan/60 text-cw-foreground hover:bg-cw-accent-left/20'
            } ${isSubmitting ? 'cursor-not-allowed opacity-70' : ''}`}
            onClick={() => handleSelect(option.id)}
            disabled={isSubmitting}
            role="radio"
            aria-checked={selectedId === option.id}
            aria-label={`Select option: ${option.text}`}
          >
            <div className="flex items-center justify-between">
              <span>{option.text}</span>
              {selectedId === option.id && isSubmitting && (
                <div 
                  className="h-4 w-4 rounded-full border-2 border-white border-t-transparent animate-spin"
                  role="status"
                  aria-label="Loading"
                ></div>
              )}
            </div>
          </button>
        ))}
      </div>
      
      {/* Status announcement for screen readers */}
      {isSubmitting && (
        <div className="sr-only" aria-live="polite">
          Processing your selection...
        </div>
      )}
    </div>
  );
};

export default ClarificationResponse;