'use client';

import React, { useState, useRef, useEffect } from 'react';

interface AskResponseProps {
  field: string;
  question: string;
  onSubmit: (field: string, value: any) => Promise<void>;
}

/**
 * Component for rendering an "ask" type response
 * 
 * Displays a question to the user and provides an input for answering
 */
const AskResponse: React.FC<AskResponseProps> = ({
  field,
  question,
  onSubmit
}) => {
  const [value, setValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-focus the input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!value.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onSubmit(field, value);
      setValue('');
    } catch (error) {
      console.error('Error submitting field response:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div 
      className="mt-4 bg-black/40 p-4 rounded-lg border border-cw-n-blue/60 backdrop-blur-sm"
      role="form"
      aria-labelledby={`question-${field}`}
    >
      <h4 
        id={`question-${field}`}
        className="text-sm font-mono font-medium text-cw-n-blue mb-2"
      >
        {question || `Please provide a value for ${field}`}
      </h4>

      <form 
        onSubmit={handleSubmit} 
        className="flex gap-2"
        aria-label={`Response form for ${field}`}
      >
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          className="flex-1 border border-cw-b-cyan/50 bg-black/50 rounded py-2 px-3 text-sm text-cw-b-white font-mono focus:outline-none focus:ring-2 focus:ring-cw-n-blue focus:border-cw-n-blue"
          placeholder={`Enter ${field}...`}
          disabled={isSubmitting}
          aria-label={field}
          aria-required="true"
          aria-invalid={isSubmitting}
          aria-describedby={`${field}-description`}
        />
        <button
          type="submit"
          disabled={!value.trim() || isSubmitting}
          className={`px-3 py-2 rounded text-cw-foreground text-sm font-mono focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cw-n-blue ${
            !value.trim() || isSubmitting
              ? 'bg-cw-n-black cursor-not-allowed opacity-50'
              : 'bg-cw-accent-left hover:bg-cw-accent-left/80'
          } transition-colors`}
          aria-label={isSubmitting ? "Submitting..." : "Submit response"}
        >
          {isSubmitting ? (
            <div 
              className="h-4 w-4 rounded-full border-2 border-white border-t-transparent animate-spin"
              role="status"
              aria-label="Loading"
            ></div>
          ) : (
            'Submit'
          )}
        </button>
      </form>
      
      {/* Hidden description for screen readers */}
      <div id={`${field}-description`} className="sr-only">
        Enter a value for the {field} field and press submit or Enter to continue the conversation.
      </div>
    </div>
  );
};

export default AskResponse;