# Chat Interface for Coherence Admin

## Overview

The Chat Interface is a powerful tool for interacting with the Coherence intent pipeline, template system, and API actions directly from the admin interface. It provides real-time feedback on how the system processes natural language queries and allows administrators to test and improve the underlying system.

## Features

- **Conversational Interface**: Natural conversation with the Coherence engine
- **Multi-tier Intent Resolution**: See how the system processes queries through vector matching, LLM routing, or RAG
- **Parameter Collection**: Test parameter extraction and follow-up questions
- **Async Operation Support**: Monitor long-running operations with progress tracking
- **Intent Clarification**: Handle ambiguous queries with option selection
- **Accessible Design**: Fully keyboard navigable with screen reader support
- **Real-time Feedback**: Immediate response with appropriate loading states

## Components

### Core Components

1. **ChatInput**: Input component for user messages
   - Supports multi-line input via Shift+Enter
   - Automatically resizes based on content
   - Provides loading state during message processing

2. **ChatMessage**: Displays individual messages in the conversation
   - Renders different message types (user/system)
   - Supports Markdown formatting for system responses
   - Handles special response types with specialized components

3. **ChatContainer**: Layout container for the chat interface
   - Manages message positioning and scrolling
   - Provides visual structure for the conversation

### Response Type Components

1. **AskResponse**: Handles parameter collection
   - Displays questions to the user
   - Provides input field for answering
   - Validates user responses

2. **AsyncResponse**: Manages long-running operations
   - Shows progress tracking
   - Provides status information during processing

3. **ClarificationResponse**: Handles ambiguous intents
   - Presents options for the user to choose from
   - Provides visual feedback during selection

4. **FallbackResponse**: Displayed when intent recognition fails
   - Shows error messages
   - Provides suggestions for rephrasing

## State Management

The `useChat` hook provides comprehensive state management for the chat interface:

- **Message History**: Tracks all messages in the conversation
- **Conversation ID**: Maintains conversation context across exchanges
- **Loading States**: Manages UI state during API calls
- **Error Handling**: Provides robust error recovery mechanisms
- **Workflow Tracking**: Monitors async operations with automatic polling

## API Integration

The chat interface integrates with the Coherence backend through three main endpoints:

1. **`/v1/resolve`**: Process new messages and identify intents
2. **`/v1/continue`**: Continue an existing conversation with additional information
3. **`/v1/status/{workflow_id}`**: Check the status of asynchronous workflows

These endpoints are accessed through the `chatClient` utility, which handles authentication, error handling, and response processing.

## Usage

To use the chat interface:

1. Access the Chat page from the admin dashboard navigation
2. Type a message in the input field and press Enter or click Send
3. Review the system's response and provide additional information if prompted
4. Use the "New Conversation" button to reset the conversation state

## Integration with Other Admin Features

The chat interface can be used to test and validate other components of the Coherence system:

- **Templates**: See how template changes affect intent recognition and response generation
- **Integrations**: Test API integrations by formulating queries that trigger specific endpoints
- **Workflows**: Monitor the execution of complex, multi-step workflows

## Accessibility

The chat interface is designed to be accessible to all users:

- **Keyboard Navigation**: All interactive elements are focusable and operable with a keyboard
- **Screen Reader Support**: ARIA attributes and proper semantics for assistive technology
- **Color Contrast**: All text meets WCAG AA contrast requirements
- **Focus Management**: Proper focus trapping and restoration during interactions
- **Status Announcements**: Live regions for dynamic content updates

## Testing

The chat interface is thoroughly tested with:

- **Unit Tests**: Individual component testing
- **Integration Tests**: Ensuring components work together correctly
- **Accessibility Tests**: Validating ARIA attributes and keyboard navigation
- **Mock API Tests**: Verifying API integration with mock data

## Future Enhancements

Potential future improvements to the chat interface:

1. **File Attachments**: Allow file uploads in the conversation
2. **Conversation Export**: Export conversation history for sharing or reporting
3. **Template Debugging**: Show which templates were used for response generation
4. **Intent Confidence**: Display confidence scores for intent matching
5. **Voice Input**: Add speech-to-text capabilities for voice interaction
6. **Session Persistence**: Save and restore conversations between sessions
7. **Admin Annotations**: Allow admins to annotate conversations for training