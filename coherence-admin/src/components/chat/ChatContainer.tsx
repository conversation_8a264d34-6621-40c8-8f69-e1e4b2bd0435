'use client';

import React from 'react';

interface ChatContainerProps {
  children: React.ReactNode;
}

/**
 * Container component for the chat messages
 * 
 * Provides the scrollable container and overall layout for the chat interface
 */
const ChatContainer: React.FC<ChatContainerProps> = ({ children }) => {
  return (
    <div 
      className="h-full overflow-y-auto p-4 bg-background"
      role="log"
      aria-label="Chat conversation"
      aria-live="polite"
    >
      <div className="max-w-3xl mx-auto space-y-4">
        {children}
      </div>
    </div>
  );
};

export default ChatContainer;