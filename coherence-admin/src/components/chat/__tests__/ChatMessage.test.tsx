import React from 'react';
import { render, screen } from '@testing-library/react';
import ChatMessage from '../ChatMessage';
import { Message } from '@/lib/hooks/useChat';

// Mock the response type components
jest.mock('../ResponseTypes/AskResponse', () => {
  return function MockAskResponse() {
    return <div data-testid="ask-response">Ask Response Mock</div>;
  };
});

jest.mock('../ResponseTypes/AsyncResponse', () => {
  return function MockAsyncResponse() {
    return <div data-testid="async-response">Async Response Mock</div>;
  };
});

jest.mock('../ResponseTypes/FallbackResponse', () => {
  return function MockFallbackResponse() {
    return <div data-testid="fallback-response">Fallback Response Mock</div>;
  };
});

jest.mock('../ResponseTypes/ClarificationResponse', () => {
  return function MockClarificationResponse() {
    return <div data-testid="clarification-response">Clarification Response Mock</div>;
  };
});

// Mock ReactMarkdown
jest.mock('react-markdown', () => {
  return function MockReactMarkdown({ children }: { children: string }) {
    return <div data-testid="markdown">{children}</div>;
  };
});

describe('ChatMessage', () => {
  const mockOnFieldResponse = jest.fn();
  const mockOnIntentSelection = jest.fn();
  
  const baseUserMessage: Message = {
    id: '1',
    content: 'Test user message',
    timestamp: new Date('2023-01-01T12:00:00Z'),
    isUser: true,
    status: 'sent'
  };
  
  const baseSystemMessage: Message = {
    id: '2',
    content: 'Test system message',
    timestamp: new Date('2023-01-01T12:01:00Z'),
    isUser: false,
    status: 'sent'
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('renders a user message correctly', () => {
    render(
      <ChatMessage 
        message={baseUserMessage} 
        onFieldResponse={mockOnFieldResponse}
        onIntentSelection={mockOnIntentSelection}
      />
    );
    
    expect(screen.getByText('Test user message')).toBeInTheDocument();
    expect(screen.getByText('12:00')).toBeInTheDocument();
    expect(screen.getByRole('article')).toHaveClass('bg-blue-600');
  });
  
  it('renders a system message correctly', () => {
    render(
      <ChatMessage 
        message={baseSystemMessage} 
        onFieldResponse={mockOnFieldResponse}
        onIntentSelection={mockOnIntentSelection}
      />
    );
    
    expect(screen.getByTestId('markdown')).toHaveTextContent('Test system message');
    expect(screen.getByText('12:01')).toBeInTheDocument();
    expect(screen.getByRole('article')).toHaveClass('bg-white');
  });
  
  it('renders a sending status indicator', () => {
    const sendingMessage = { ...baseUserMessage, status: 'sending' };
    
    render(
      <ChatMessage 
        message={sendingMessage} 
        onFieldResponse={mockOnFieldResponse}
        onIntentSelection={mockOnIntentSelection}
      />
    );
    
    expect(screen.getByText('Sending...')).toBeInTheDocument();
  });
  
  it('renders an error message when present', () => {
    const errorMessage = { ...baseUserMessage, error: 'Test error message' };
    
    render(
      <ChatMessage 
        message={errorMessage} 
        onFieldResponse={mockOnFieldResponse}
        onIntentSelection={mockOnIntentSelection}
      />
    );
    
    expect(screen.getByText('Test error message')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });
  
  it('renders an ask response component for ask response type', () => {
    const askMessage = { 
      ...baseSystemMessage, 
      responseType: 'ask' as const,
      responseData: { field: 'test_field', question: 'Test question?' }
    };
    
    render(
      <ChatMessage 
        message={askMessage} 
        onFieldResponse={mockOnFieldResponse}
        onIntentSelection={mockOnIntentSelection}
      />
    );
    
    expect(screen.getByTestId('ask-response')).toBeInTheDocument();
  });
  
  it('renders an async response component for async response type', () => {
    const asyncMessage = { 
      ...baseSystemMessage, 
      responseType: 'async' as const,
      responseData: { workflow_id: '123', status_url: '/status/123' }
    };
    
    render(
      <ChatMessage 
        message={asyncMessage} 
        onFieldResponse={mockOnFieldResponse}
        onIntentSelection={mockOnIntentSelection}
      />
    );
    
    expect(screen.getByTestId('async-response')).toBeInTheDocument();
  });
  
  it('renders a fallback response component for fallback response type', () => {
    const fallbackMessage = { 
      ...baseSystemMessage, 
      responseType: 'fallback' as const
    };
    
    render(
      <ChatMessage 
        message={fallbackMessage} 
        onFieldResponse={mockOnFieldResponse}
        onIntentSelection={mockOnIntentSelection}
      />
    );
    
    expect(screen.getByTestId('fallback-response')).toBeInTheDocument();
  });
  
  it('renders a clarification response component for intent_clarification response type', () => {
    const clarificationMessage = { 
      ...baseSystemMessage, 
      responseType: 'intent_clarification' as const,
      responseData: { options: [{ id: '1', text: 'Option 1' }] }
    };
    
    render(
      <ChatMessage 
        message={clarificationMessage} 
        onFieldResponse={mockOnFieldResponse}
        onIntentSelection={mockOnIntentSelection}
      />
    );
    
    expect(screen.getByTestId('clarification-response')).toBeInTheDocument();
  });
});