import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ChatClient from '@/app/admin/chat/ChatClient';
import * as AdminSessionContext from '@/context/AdminSessionContext';
import * as useChatHook from '@/lib/hooks/useChat';

// Mock the hooks
jest.mock('@/lib/hooks/useChat');
jest.mock('@/context/AdminSessionContext');

describe('ChatClient', () => {
  const mockUseChatReturn = {
    state: {
      messages: [],
      isLoading: false,
      error: null,
      activeField: null,
      activeWorkflows: {},
      responseData: null,
    },
    sendMessage: jest.fn(),
    resetConversation: jest.fn(),
    handleFieldResponse: jest.fn(),
    handleIntentSelection: jest.fn(),
  };

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup default mocks
    (useChatHook.useChat as jest.Mock).mockReturnValue(mockUseChatReturn);
    
    (AdminSessionContext.useAdminSession as jest.Mock).mockReturnValue({
      permissions: [],
      isLoading: false,
      isAuthenticated: true,
      organization: { id: 'org_123', name: 'Test Org' },
    });
  });

  it('should render the chat interface', () => {
    render(<ChatClient userId="user_123" />);
    
    // Check for the header
    expect(screen.getByText('Coherence Chat')).toBeInTheDocument();
    
    // Check for the "New Conversation" button
    expect(screen.getByText('New Conversation')).toBeInTheDocument();
  });

  it('should display an initial empty state when no messages', () => {
    render(<ChatClient userId="user_123" />);
    
    expect(screen.getByText('Start a conversation')).toBeInTheDocument();
  });

  it('should display API key error for regular users', () => {
    // Set up the error in chat state
    const errorState = {
      ...mockUseChatReturn,
      state: {
        ...mockUseChatReturn.state,
        error: 'API key required. Please create an API key for your organization.',
      },
    };
    
    (useChatHook.useChat as jest.Mock).mockReturnValue(errorState);
    
    // Render with regular user permissions (no system:*)
    (AdminSessionContext.useAdminSession as jest.Mock).mockReturnValue({
      permissions: ['user:read', 'user:write'],
      isLoading: false,
      isAuthenticated: true,
      organization: { id: 'org_123', name: 'Test Org' },
    });
    
    render(<ChatClient userId="user_123" />);
    
    // Check that the error message is shown
    expect(screen.getByText('Authentication Error')).toBeInTheDocument();
    expect(screen.getByText('API key required. Please create an API key for your organization.')).toBeInTheDocument();
  });

  it('should hide API key error for system admins', () => {
    // Set up the error in chat state
    const errorState = {
      ...mockUseChatReturn,
      state: {
        ...mockUseChatReturn.state,
        error: 'API key required. Please create an API key for your organization.',
      },
    };
    
    (useChatHook.useChat as jest.Mock).mockReturnValue(errorState);
    
    // Render with system admin permissions
    (AdminSessionContext.useAdminSession as jest.Mock).mockReturnValue({
      permissions: ['system:*', 'user:read', 'user:write'],
      isLoading: false,
      isAuthenticated: true,
      organization: { id: 'org_123', name: 'Test Org' },
    });
    
    render(<ChatClient userId="user_123" />);
    
    // Error message should not be shown for system admins
    expect(screen.queryByText('Authentication Error')).not.toBeInTheDocument();
    expect(screen.queryByText('API key required. Please create an API key for your organization.')).not.toBeInTheDocument();
  });

  it('should show other types of errors even for system admins', () => {
    // Set up a different type of error
    const errorState = {
      ...mockUseChatReturn,
      state: {
        ...mockUseChatReturn.state,
        error: 'Server error occurred while processing request.',
      },
    };
    
    (useChatHook.useChat as jest.Mock).mockReturnValue(errorState);
    
    // Render with system admin permissions
    (AdminSessionContext.useAdminSession as jest.Mock).mockReturnValue({
      permissions: ['system:*'],
      isLoading: false,
      isAuthenticated: true,
      organization: { id: 'org_123', name: 'Test Org' },
    });
    
    render(<ChatClient userId="user_123" />);
    
    // Non-API key errors should still be shown
    expect(screen.getByText('Error')).toBeInTheDocument();
    expect(screen.getByText('Server error occurred while processing request.')).toBeInTheDocument();
  });

  it('should enable chat input for system admins even with API key error', () => {
    // Set up the error in chat state
    const errorState = {
      ...mockUseChatReturn,
      state: {
        ...mockUseChatReturn.state,
        error: 'API key required. Please create an API key for your organization.',
      },
    };
    
    (useChatHook.useChat as jest.Mock).mockReturnValue(errorState);
    
    // Render with system admin permissions
    (AdminSessionContext.useAdminSession as jest.Mock).mockReturnValue({
      permissions: ['system:*'],
      isLoading: false,
      isAuthenticated: true,
      organization: { id: 'org_123', name: 'Test Org' },
    });
    
    render(<ChatClient userId="user_123" />);
    
    // For system admins, the input should still be enabled
    const inputElement = screen.getByPlaceholderText('Type a message...');
    expect(inputElement).not.toBeDisabled();
  });

  it('should disable chat input for regular users with API key error', () => {
    // Set up the error in chat state
    const errorState = {
      ...mockUseChatReturn,
      state: {
        ...mockUseChatReturn.state,
        error: 'API key required. Please create an API key for your organization.',
      },
    };
    
    (useChatHook.useChat as jest.Mock).mockReturnValue(errorState);
    
    // Render with regular user permissions
    (AdminSessionContext.useAdminSession as jest.Mock).mockReturnValue({
      permissions: ['user:read'],
      isLoading: false,
      isAuthenticated: true,
      organization: { id: 'org_123', name: 'Test Org' },
    });
    
    // Mock disabled prop to be accessible in the test
    jest.mock('@/components/chat/ChatInput', () => ({ disabled }: { disabled: boolean }) => (
      <div data-testid="chat-input" data-disabled={disabled}>
        <input placeholder="Type a message..." disabled={disabled} />
      </div>
    ));
    
    render(<ChatClient userId="user_123" />);
    
    // For regular users, the input should be disabled
    const inputElement = screen.getByPlaceholderText('Type a message...');
    expect(inputElement).toBeDisabled();
  });
});