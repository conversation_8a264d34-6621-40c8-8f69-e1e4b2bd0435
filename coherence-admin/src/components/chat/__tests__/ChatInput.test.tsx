import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ChatInput from '../ChatInput';

describe('ChatInput', () => {
  const mockSendMessage = jest.fn(() => Promise.resolve());

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    render(<ChatInput onSendMessage={mockSendMessage} />);
    
    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /send message/i })).toBeInTheDocument();
  });

  it('handles text input correctly', async () => {
    render(<ChatInput onSendMessage={mockSendMessage} />);
    
    const input = screen.getByRole('textbox');
    await userEvent.type(input, 'Test message');
    
    expect(input).toHaveValue('Test message');
  });

  it('disables submit when input is empty', () => {
    render(<ChatInput onSendMessage={mockSendMessage} />);
    
    const submitButton = screen.getByRole('button', { name: /send message/i });
    expect(submitButton).toBeDisabled();
  });

  it('enables submit when input has content', async () => {
    render(<ChatInput onSendMessage={mockSendMessage} />);
    
    const input = screen.getByRole('textbox');
    const submitButton = screen.getByRole('button', { name: /send message/i });
    
    await userEvent.type(input, 'Test message');
    expect(submitButton).not.toBeDisabled();
  });

  it('calls onSendMessage when form is submitted', async () => {
    render(<ChatInput onSendMessage={mockSendMessage} />);
    
    const input = screen.getByRole('textbox');
    const submitButton = screen.getByRole('button', { name: /send message/i });
    
    await userEvent.type(input, 'Test message');
    await userEvent.click(submitButton);
    
    expect(mockSendMessage).toHaveBeenCalledWith('Test message');
  });

  it('clears input after message is sent', async () => {
    render(<ChatInput onSendMessage={mockSendMessage} />);
    
    const input = screen.getByRole('textbox');
    const submitButton = screen.getByRole('button', { name: /send message/i });
    
    await userEvent.type(input, 'Test message');
    await userEvent.click(submitButton);
    
    await waitFor(() => {
      expect(input).toHaveValue('');
    });
  });

  it('sends message when Enter is pressed', async () => {
    render(<ChatInput onSendMessage={mockSendMessage} />);
    
    const input = screen.getByRole('textbox');
    
    await userEvent.type(input, 'Test message');
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' });
    
    expect(mockSendMessage).toHaveBeenCalledWith('Test message');
  });

  it('does not send message when Shift+Enter is pressed', async () => {
    render(<ChatInput onSendMessage={mockSendMessage} />);
    
    const input = screen.getByRole('textbox');
    
    await userEvent.type(input, 'Test message');
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter', shiftKey: true });
    
    expect(mockSendMessage).not.toHaveBeenCalled();
  });

  it('shows loading state while sending message', async () => {
    // Create a mock function that doesn't resolve immediately
    const delayedMockSendMessage = jest.fn(() => new Promise(resolve => {
      setTimeout(resolve, 100);
    }));
    
    render(<ChatInput onSendMessage={delayedMockSendMessage} />);
    
    const input = screen.getByRole('textbox');
    const submitButton = screen.getByRole('button', { name: /send message/i });
    
    await userEvent.type(input, 'Test message');
    await userEvent.click(submitButton);
    
    // Should show loading state
    expect(screen.getByRole('status')).toBeInTheDocument();
    
    // Wait for the promise to resolve
    await waitFor(() => {
      expect(delayedMockSendMessage).toHaveBeenCalledWith('Test message');
    });
  });

  it('disables input when disabled prop is true', () => {
    render(<ChatInput onSendMessage={mockSendMessage} disabled={true} />);
    
    const input = screen.getByRole('textbox');
    const submitButton = screen.getByRole('button');
    
    expect(input).toBeDisabled();
    expect(submitButton).toBeDisabled();
  });

  it('uses custom placeholder when provided', () => {
    const customPlaceholder = 'Type your custom message...';
    render(<ChatInput onSendMessage={mockSendMessage} placeholder={customPlaceholder} />);
    
    const input = screen.getByRole('textbox');
    
    expect(input).toHaveAttribute('placeholder', customPlaceholder);
  });
});