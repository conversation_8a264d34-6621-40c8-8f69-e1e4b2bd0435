'use client';

import React from 'react';
import { Message } from '@/lib/hooks/useChat';
import ReactMarkdown from 'react-markdown';
import AskResponse from './ResponseTypes/AskResponse';
import AsyncResponse from './ResponseTypes/AsyncResponse';
import FallbackResponse from './ResponseTypes/FallbackResponse';
import ClarificationResponse from './ResponseTypes/ClarificationResponse';
import CRFSRenderer from './CRFSRenderer';

interface ChatMessageProps {
  message: Message;
  onFieldResponse: (field: string, value: any) => Promise<void>;
  onIntentSelection: (intentId: string) => Promise<void>;
}

/**
 * Component for rendering a single chat message
 * 
 * Handles different message types and styling based on sender
 */
const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  onFieldResponse,
  onIntentSelection
}) => {
  // Determine message type-specific component
  const renderResponseByType = () => {
    if (!message.responseType) return null;

    switch (message.responseType) {
      case 'ask':
        return (
          <AskResponse
            field={message.responseData?.field || ''}
            question={message.responseData?.question || ''}
            onSubmit={onFieldResponse}
          />
        );
      
      case 'async':
        return (
          <AsyncResponse
            workflowId={message.responseData?.workflow_id || ''}
            statusUrl={message.responseData?.status_url || ''}
          />
        );
      
      case 'fallback':
        return (
          <FallbackResponse
            message={message.content}
            suggestions={message.responseData?.suggestions || []}
          />
        );
      
      case 'intent_clarification':
        return (
          <ClarificationResponse
            options={message.responseData?.options || []}
            onSelect={onIntentSelection}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div className={`flex w-full ${message.isUser ? 'justify-end' : 'justify-start'}`}>
      <div
        className={`p-4 max-w-[75%] sm:max-w-[60%] relative ${
          message.isUser
            ? 'bg-gradient-to-r from-cw-accent-left to-cw-accent-right text-white rounded-2xl rounded-br-md shadow-lg chat-message-user message-bubble-user'
            : 'bg-black/60 backdrop-blur-sm shadow border border-cw-b-cyan/60 text-cw-b-white rounded-2xl rounded-bl-md chat-message-assistant message-bubble-assistant'
        }`}
        aria-label={message.isUser ? 'Your message' : 'System response'}
        role="article"
      >
      {/* User/System indicator for accessibility */}
      <div className="sr-only">{message.isUser ? 'You' : 'System'}</div>
      
      {/* Message content */}
      <div 
        className={`prose prose-sm max-w-none`}
        tabIndex={0}  // Makes long text content accessible via keyboard
      >
        {message.isUser ? (
          <p className="m-0 text-white font-medium">​{message.content}</p>
        ) : (
          <div className="text-cw-b-white">
            {message.crfsMetadata ? (
              <CRFSRenderer
                content={message.content}
                metadata={message.crfsMetadata}
                rawResponse={message.rawResponse}
                className="crfs-message-content"
              />
            ) : (
              <ReactMarkdown>{message.content}</ReactMarkdown>
            )}
          </div>
        )}
      </div>

      {/* Response type-specific components */}
      {!message.isUser && renderResponseByType()}

      {/* Message status indicator */}
      {message.status === 'sending' && (
        <div className={`mt-2 text-xs font-mono ${message.isUser ? 'text-white/70' : 'text-cw-b-cyan'}`} aria-live="polite">
          <span className="sr-only">Status:</span> Sending...
        </div>
      )}

      {/* Error message */}
      {message.error && (
        <div 
          className={`mt-2 text-xs font-mono ${message.isUser ? 'text-white/90' : 'text-cw-n-red'}`}
          role="alert"
          aria-live="assertive"
        >
          {message.error}
        </div>
      )}

      {/* Timestamp */}
      <div
        className={`text-xs mt-1 ${
          message.isUser ? 'text-white/60' : 'text-cw-b-cyan/70'
        } font-mono`}
        aria-label={`Sent at ${message.timestamp.toLocaleTimeString()}`}
      >
        {message.timestamp.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit'
        })}
      </div>
      </div>
    </div>
  );
};

export default ChatMessage;