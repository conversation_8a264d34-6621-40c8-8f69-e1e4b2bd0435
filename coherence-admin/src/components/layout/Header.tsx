'use client';

import React from 'react';
import { User<PERSON>utton, useUser, useOrganizationList } from '@clerk/nextjs';
import Link from 'next/link';
import { useAdminSession } from '@/context/AdminSessionContext';

const Header: React.FC = () => {
  const { isSignedIn } = useUser();
  const { organization } = useAdminSession();
  const { setActive } = useOrganizationList();

  return (
    <header className="glass-morphism-strong border-b border-cw-b-cyan/30 py-3 px-4 shadow-xl relative overflow-hidden">
      {/* AI-First Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-cw-accent-left/5 via-transparent to-cw-accent-right/5 pointer-events-none"></div>
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cw-accent-left/50 to-transparent"></div>
      
      <div className="max-w-7xl mx-auto flex justify-between items-center relative z-10">
        <div className="flex items-center">
          <Link href="/admin" className="flex items-center group accent-glow-strong rounded-lg px-3 py-1">
            {/* Enhanced AI-themed logo */}
            <div className="relative mr-3">
              <svg
                className="h-8 w-8 text-primary group-hover:text-accent transition-colors duration-300"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z"></path>
                <path d="M12 10c-1.103 0-2 .897-2 2s.897 2 2 2 2-.897 2-2-.897-2-2-2z"></path>
                <path d="M13 5.422a7.476 7.476 0 0 0-1 .004C8.25 5.665 5.69 8.259 5.5 12c-.08 1.579.392 3.115 1.32 4.34a1 1 0 0 0 1.573-1.238 4.572 4.572 0 0 1-.895-2.902c.126-2.553 2.001-4.52 4.526-4.68.823-.027 1.654.187 2.393.627a1 1 0 1 0 .996-1.734 6.466 6.466 0 0 0-2.413-.991z"></path>
              </svg>
              {/* AI Processing Ring */}
              <div className="absolute inset-0 rounded-full border border-cw-accent-left/30 animate-ai-scan"></div>
              {/* Status Dots */}
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-cw-n-green rounded-full animate-pulse-ai"></div>
            </div>
            <span className="text-lg font-display font-bold text-cw-foreground tracking-wider w-[176px] overflow-hidden text-ellipsis whitespace-nowrap group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-cw-accent-left group-hover:to-cw-accent-right group-hover:bg-clip-text transition-all duration-300">
              COHERENCE
            </span>
          </Link>

          {organization && (
            <div className="ml-6 pl-6 border-l border-cw-b-cyan/50 relative">
              {/* Organization Status Indicator */}
              <div className="absolute -left-px top-0 bottom-0 w-px bg-gradient-to-b from-cw-accent-left/50 via-cw-n-green/50 to-cw-accent-right/50 animate-data-stream"></div>
              <span className="text-sm text-muted-foreground font-mono">ORG:</span>
              <span className="ml-2 text-sm font-mono font-medium text-accent bg-cw-accent-right/10 px-2 py-1 rounded border border-cw-accent-right/30">
                {organization.name}
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-6">
          {/* AI System Status */}
          <div className="flex items-center gap-2 px-3 py-1 glass-morphism-subtle rounded-full">
            <div className="w-2 h-2 bg-cw-n-green rounded-full animate-pulse-ai"></div>
            <span className="text-xs font-mono text-cw-n-green font-semibold tracking-wider">ACTIVE</span>
          </div>

          <Link 
            href="/org-selection" 
            className="text-sm text-foreground/80 hover:text-primary transition-all duration-300 accent-glow px-3 py-1 rounded-md glass-morphism-subtle border border-cw-b-cyan/20 hover:border-cw-accent-left/50 font-mono tracking-wide"
          >
            SWITCH ORG
          </Link>

          {isSignedIn ? (
            <div className="relative">
              <div className="glass-morphism-strong rounded-full p-1 border border-cw-accent-left/30 hover:border-cw-accent-left/60 transition-all duration-300 accent-glow">
                <UserButton
                  afterSignOutUrl="/"
                  appearance={{
                    elements: {
                      userButtonAvatarBox: "w-8 h-8 border border-cw-accent-right/30"
                    }
                  }}
                />
              </div>
              {/* User Online Indicator */}
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-cw-n-green rounded-full border-2 border-cw-bg-top animate-pulse-ai"></div>
            </div>
          ) : (
            <Link
              href="/sign-in"
              className="px-4 py-2 rounded-md glass-morphism-strong text-cw-foreground hover:glass-morphism border border-cw-accent-left/50 hover:border-cw-accent-left shadow-xl transition-all duration-300 accent-glow font-mono tracking-wider"
            >
              SIGN IN
            </Link>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;