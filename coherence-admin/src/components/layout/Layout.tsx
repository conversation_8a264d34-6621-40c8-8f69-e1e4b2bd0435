import React, { ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import Header from './Header';
import Sidebar from './Sidebar';
import Footer from './Footer';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const pathname = usePathname();
  const isChatPage = pathname === '/admin/chat';
  
  return (
    <div className="flex h-screen bg-gradient-cw overflow-hidden neural-grid">
      <Sidebar />
      <main className={`flex-1 ${isChatPage ? 'flex flex-col overflow-hidden' : 'overflow-y-auto'}`}>
        {isChatPage ? (
          children
        ) : (
          <div className="p-6 relative">
            {/* AI-First Background Effects */}
            <div className="absolute inset-0 bg-gradient-to-br from-cw-accent-left/3 via-transparent to-cw-accent-right/3 pointer-events-none"></div>
            <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cw-accent-left/30 to-transparent"></div>
            
            {/* Enhanced Main Content Container */}
            <div className="relative w-full max-w-[1400px] mx-auto glass-morphism rounded-xl shadow-2xl p-8 accent-glow">
              {/* Status Indicator */}
              <div className="absolute top-4 right-4 w-2 h-2 bg-cw-n-green rounded-full animate-pulse-ai"></div>
              
              {/* Data Flow Indicator */}
              <div className="absolute left-0 top-8 bottom-8 w-px bg-gradient-to-b from-transparent via-cw-accent-left/50 to-transparent"></div>
              
              {children}
            </div>
            
            {/* Floating AI Elements */}
            <div className="absolute bottom-6 right-6 opacity-20 pointer-events-none">
              <div className="w-1 h-1 bg-cw-accent-left rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
              <div className="w-1 h-1 bg-cw-accent-right rounded-full animate-pulse mt-2" style={{ animationDelay: '500ms' }}></div>
              <div className="w-1 h-1 bg-cw-n-cyan rounded-full animate-pulse mt-2" style={{ animationDelay: '1000ms' }}></div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default Layout;