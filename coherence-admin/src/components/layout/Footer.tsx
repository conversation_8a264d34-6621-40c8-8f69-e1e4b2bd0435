import React from 'react';
import Link from 'next/link';

const Footer: React.FC = () => {
  return (
    <footer className="bg-card/80 backdrop-blur-sm border-t border-border py-6 mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto">
          <div>
            <h3 className="text-sm font-display font-semibold text-primary uppercase tracking-wider mb-4">Admin</h3>
            <ul className="space-y-3">
              <li><Link href="/admin" className="text-foreground/80 hover:text-accent text-sm accent-glow px-2 py-1 rounded-md inline-block">Dashboard</Link></li>
              <li><Link href="/org-selection" className="text-foreground/80 hover:text-accent text-sm accent-glow px-2 py-1 rounded-md inline-block">Organizations</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="text-sm font-display font-semibold text-primary uppercase tracking-wider mb-4">Resources</h3>
            <ul className="space-y-3">
              <li><Link href="/admin/templates" className="text-foreground/80 hover:text-accent text-sm accent-glow px-2 py-1 rounded-md inline-block">Templates</Link></li>
              <li><Link href="/admin/workflows" className="text-foreground/80 hover:text-accent text-sm accent-glow px-2 py-1 rounded-md inline-block">Workflows</Link></li>
              <li><Link href="/admin/integrations" className="text-foreground/80 hover:text-accent text-sm accent-glow px-2 py-1 rounded-md inline-block">Integrations</Link></li>
            </ul>
          </div>
        </div>
        <div className="mt-8 pt-8 border-t border-border">
          <p className="font-mono text-muted-foreground text-sm text-center">
            &copy; 2025 SynapseDx Limited. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;