'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Calendar, Eye, EyeOff, Upload } from 'lucide-react';
import { EnhancedParameterSchema, ValidationResult } from '@/lib/types/enhanced-parameters';

interface SchemaAwareInputProps {
  parameter: EnhancedParameterSchema;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  className?: string;
  disabled?: boolean;
}

/**
 * Schema-aware input component that renders appropriate form controls
 * based on OpenAPI parameter schema constraints and types.
 */
export const SchemaAwareInput: React.FC<SchemaAwareInputProps> = ({
  parameter,
  value,
  onChange,
  error,
  className = '',
  disabled = false,
}) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const [fileUploadError, setFileUploadError] = React.useState<string | null>(null);

  // Get appropriate input type based on parameter format and type
  const getInputType = (): string => {
    if (parameter.type === 'boolean') return 'checkbox';
    if (parameter.format === 'password') return showPassword ? 'text' : 'password';
    if (parameter.format === 'email') return 'email';
    if (parameter.format === 'uri') return 'url';
    if (parameter.format === 'date') return 'date';
    if (parameter.format === 'date-time') return 'datetime-local';
    if (parameter.type === 'number' || parameter.type === 'integer') return 'number';
    if (parameter.format === 'binary' || parameter.format === 'byte') return 'file';
    return 'text';
  };

  // Get placeholder text with examples and defaults
  const getPlaceholder = (): string => {
    if (parameter.example) {
      return `Example: ${parameter.example}`;
    }
    if (parameter.default !== undefined && parameter.default !== null) {
      return `Default: ${parameter.default}`;
    }
    if (parameter.enum && parameter.enum.length > 0) {
      return `Choose from: ${parameter.enum.slice(0, 3).join(', ')}${parameter.enum.length > 3 ? '...' : ''}`;
    }
    return parameter.description || `Enter ${parameter.type}`;
  };

  // Validate parameter value against schema constraints
  const validateValue = (val: any): ValidationResult => {
    const result: ValidationResult = { valid: true, errors: [], warnings: [] };
    
    // Check required
    if (parameter.required && (val === undefined || val === null || val === '')) {
      result.valid = false;
      result.errors.push(`${parameter.name} is required`);
      return result;
    }

    // Skip validation for empty optional values
    if (!parameter.required && (val === undefined || val === null || val === '')) {
      return result;
    }

    // Type-specific validation
    if (parameter.type === 'string' && typeof val === 'string') {
      // String length constraints
      if (parameter.min_length !== undefined && val.length < parameter.min_length) {
        result.valid = false;
        result.errors.push(`Minimum length is ${parameter.min_length} characters`);
      }
      if (parameter.max_length !== undefined && val.length > parameter.max_length) {
        result.valid = false;
        result.errors.push(`Maximum length is ${parameter.max_length} characters`);
      }
      
      // Pattern validation
      if (parameter.pattern) {
        try {
          const regex = new RegExp(parameter.pattern);
          if (!regex.test(val)) {
            result.valid = false;
            result.errors.push(`Value must match pattern: ${parameter.pattern}`);
          }
        } catch (e) {
          result.warnings.push('Invalid pattern in schema');
        }
      }
    }

    // Numeric validation
    if ((parameter.type === 'number' || parameter.type === 'integer') && typeof val === 'number') {
      if (parameter.minimum !== undefined && val < parameter.minimum) {
        result.valid = false;
        result.errors.push(`Minimum value is ${parameter.minimum}`);
      }
      if (parameter.maximum !== undefined && val > parameter.maximum) {
        result.valid = false;
        result.errors.push(`Maximum value is ${parameter.maximum}`);
      }
      if (parameter.exclusive_minimum !== undefined && val <= parameter.exclusive_minimum) {
        result.valid = false;
        result.errors.push(`Value must be greater than ${parameter.exclusive_minimum}`);
      }
      if (parameter.exclusive_maximum !== undefined && val >= parameter.exclusive_maximum) {
        result.valid = false;
        result.errors.push(`Value must be less than ${parameter.exclusive_maximum}`);
      }
      if (parameter.multiple_of !== undefined && val % parameter.multiple_of !== 0) {
        result.valid = false;
        result.errors.push(`Value must be a multiple of ${parameter.multiple_of}`);
      }
    }

    // Enum validation
    if (parameter.enum && !parameter.enum.includes(val)) {
      result.valid = false;
      result.errors.push(`Value must be one of: ${parameter.enum.join(', ')}`);
    }

    return result;
  };

  // Handle value change with validation
  const handleChange = (newValue: any) => {
    onChange(newValue);
  };

  // Handle file upload
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFileUploadError(null);
      // For binary parameters, we might want to read as base64 or just store file info
      if (parameter.format === 'binary') {
        const reader = new FileReader();
        reader.onload = () => {
          onChange(reader.result);
        };
        reader.onerror = () => {
          setFileUploadError('Failed to read file');
        };
        reader.readAsDataURL(file);
      } else {
        // For other file formats, just store the file name
        onChange(file.name);
      }
    }
  };

  // Render enum as select dropdown
  if (parameter.enum && parameter.enum.length > 0) {
    return (
      <div className={`space-y-1 ${className}`}>
        <select
          className="w-full p-2 border border-border rounded-md bg-background text-foreground disabled:opacity-50"
          value={value || ''}
          onChange={(e) => handleChange(e.target.value)}
          disabled={disabled}
        >
          <option value="">
            {parameter.required ? 'Select an option...' : 'None (optional)'}
          </option>
          {parameter.enum.map((option: any) => (
            <option key={String(option)} value={String(option)}>
              {String(option)}
            </option>
          ))}
        </select>
        {error && <div className="text-sm text-red-600">{error}</div>}
      </div>
    );
  }

  // Render boolean as checkbox
  if (parameter.type === 'boolean') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <input
          type="checkbox"
          checked={Boolean(value)}
          onChange={(e) => handleChange(e.target.checked)}
          disabled={disabled}
          className="rounded border-border"
        />
        <Label className="text-sm">
          {parameter.description || parameter.name}
        </Label>
        {error && <div className="text-sm text-red-600 ml-2">{error}</div>}
      </div>
    );
  }

  // Render file input for binary parameters
  if (parameter.format === 'binary' || parameter.format === 'byte') {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center space-x-2">
          <Input
            type="file"
            onChange={handleFileChange}
            disabled={disabled}
            className="flex-1"
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              const input = document.createElement('input');
              input.type = 'file';
              input.onchange = handleFileChange;
              input.click();
            }}
            disabled={disabled}
          >
            <Upload className="h-4 w-4" />
          </Button>
        </div>
        {fileUploadError && (
          <div className="text-sm text-red-600">{fileUploadError}</div>
        )}
        {error && <div className="text-sm text-red-600">{error}</div>}
      </div>
    );
  }

  // Render array/object as textarea
  if (parameter.type === 'array' || parameter.type === 'object') {
    return (
      <div className={`space-y-1 ${className}`}>
        <Textarea
          placeholder={
            parameter.type === 'array' 
              ? 'Enter array as JSON (e.g., ["item1", "item2"])'
              : 'Enter object as JSON (e.g., {"key": "value"})'
          }
          value={typeof value === 'string' ? value : JSON.stringify(value || '', null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              handleChange(parsed);
            } catch {
              // Keep as string if not valid JSON
              handleChange(e.target.value);
            }
          }}
          disabled={disabled}
          className="font-mono"
          rows={4}
        />
        {error && <div className="text-sm text-red-600">{error}</div>}
      </div>
    );
  }

  // Render password field with toggle
  if (parameter.format === 'password') {
    return (
      <div className={`relative ${className}`}>
        <Input
          type={showPassword ? 'text' : 'password'}
          value={value || ''}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={getPlaceholder()}
          disabled={disabled}
          min={parameter.minimum}
          max={parameter.maximum}
          minLength={parameter.min_length}
          maxLength={parameter.max_length}
          pattern={parameter.pattern}
          className="pr-10"
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
          disabled={disabled}
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4" />
          ) : (
            <Eye className="h-4 w-4" />
          )}
        </button>
        {error && <div className="text-sm text-red-600 mt-1">{error}</div>}
      </div>
    );
  }

  // Render date field with calendar icon
  if (parameter.format === 'date' || parameter.format === 'date-time') {
    return (
      <div className={`relative ${className}`}>
        <Input
          type={getInputType()}
          value={value || ''}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={getPlaceholder()}
          disabled={disabled}
          className="pr-10"
        />
        <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
        {error && <div className="text-sm text-red-600 mt-1">{error}</div>}
      </div>
    );
  }

  // Default text/number input
  return (
    <div className={`space-y-1 ${className}`}>
      <Input
        type={getInputType()}
        value={value || ''}
        onChange={(e) => {
          const val = e.target.value;
          // Convert to number for numeric types
          if (parameter.type === 'number' || parameter.type === 'integer') {
            const num = parameter.type === 'integer' ? parseInt(val) : parseFloat(val);
            handleChange(isNaN(num) ? val : num);
          } else {
            handleChange(val);
          }
        }}
        placeholder={getPlaceholder()}
        disabled={disabled}
        min={parameter.minimum}
        max={parameter.maximum}
        step={parameter.type === 'integer' ? 1 : parameter.multiple_of || 'any'}
        minLength={parameter.min_length}
        maxLength={parameter.max_length}
        pattern={parameter.pattern}
      />
      {error && <div className="text-sm text-red-600">{error}</div>}
    </div>
  );
};

export default SchemaAwareInput;