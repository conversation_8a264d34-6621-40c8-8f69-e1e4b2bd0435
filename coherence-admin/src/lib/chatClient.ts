import { api } from "./apiClient";
import type { ApiClientOptions } from "./apiClient";

/**
 * ResolveResponse defines the structure of responses from the /v1/resolve and /v1/continue endpoints
 */
export interface ResolveResponse {
  kind: 'reply' | 'ask' | 'async' | 'fallback' | 'intent_clarification';
  text?: string;
  field?: string;
  question?: string;
  workflow_id?: string;
  status_url?: string;
  options?: Array<{
    id: string;
    text: string;
  }>;
  [key: string]: any;
}

/**
 * WorkflowStatus defines the structure of responses from the /v1/status/{workflow_id} endpoint
 */
export interface WorkflowStatus {
  workflow_id: string;
  status: 'running' | 'completed' | 'failed';
  progress: number;
  current_step?: string;
  result?: any;
}

/**
 * Validates if a string is in UUID format
 */
function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Ensures the input is a properly formatted UUID
 * If it's already a valid UUID, returns it unchanged
 * If not, generates a new UUID v4 with a warning
 */
function ensureUUID(value?: string): string {
  if (!value) {
    // Generate a new UUID
    return crypto.randomUUID();
  }
  
  if (isValidUUID(value)) {
    return value;
  }
  
  console.warn(`Value "${value}" is not a valid UUID. Using a generated UUID instead.`);
  return crypto.randomUUID();
}

/**
 * Send a new message to the chat API
 */
export async function sendMessage(
  message: string,
  userId: string,
  conversationId?: string,
  context?: Record<string, any>,
  options?: ApiClientOptions
): Promise<ResolveResponse> {
  // Ensure UUID values are correctly formatted
  const validUserId = ensureUUID(userId);
  const validConversationId = conversationId ? ensureUUID(conversationId) : undefined;
  
  return api.post<ResolveResponse>('/v1/resolve', {
    message,
    user_id: validUserId,
    conversation_id: validConversationId,
    context,
    role: 'user',
  }, options);
}

/**
 * Continue a conversation with additional information
 */
export async function continueConversation(
  message: string,
  conversationId: string,
  userId: string,
  field?: string,
  options?: ApiClientOptions
): Promise<ResolveResponse> {
  // Ensure UUID values are correctly formatted
  const validUserId = ensureUUID(userId);
  const validConversationId = ensureUUID(conversationId);
  
  return api.post<ResolveResponse>('/v1/continue', {
    message,
    conversation_id: validConversationId,
    user_id: validUserId,
    field,
    role: 'user',
  }, options);
}

/**
 * Check the status of an asynchronous workflow
 */
export async function checkWorkflowStatus(
  workflowId: string,
  options?: ApiClientOptions
): Promise<WorkflowStatus> {
  return api.get<WorkflowStatus>(`/v1/status/${workflowId}`, options);
}