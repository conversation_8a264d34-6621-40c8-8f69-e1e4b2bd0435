import type { OrganizationResource } from "@clerk/types";

/**
 * Represents the user's session information in the admin application.
 * This will be populated using Clerk's authentication context.
 */
export interface AdminSession {
  userId: string; // Clerk User ID
  clerkOrgId: string; // Active Clerk Organization ID
  organization?: OrganizationResource; // Clerk Organization object
  // Add other session-specific fields as needed, e.g., permissions
  // For example, if you still need to access the raw JWT token:
  // getToken: () => Promise<string | null>;
}

/**
 * Represents an API Key as defined on the frontend.
 * It should align with the backend's representation, now including clerkOrgId.
 */
export interface ApiKey {
  id: string;
  key?: string; // The key itself, might be undefined if not shown after creation
  name: string;
  clerkOrgId: string; // The Clerk Organization ID this key belongs to
  createdAt: string;
  lastUsedAt?: string | null;
  // Add other API key-specific fields as needed
}

// Example of a refactored or removed Tenant-related interface.
// If you had a Tenant interface like this:
/*
export interface OldTenant {
  id: string; // Previous tenant ID (e.g., UUID from your DB)
  name: string;
  // ... other tenant-specific fields
}
*/
// It would now be superseded by using clerk<PERSON>rgId and the OrganizationResource from Clerk.
// You might have specific settings previously tied to a Tenant that now need to be
// associated with a clerkOrgId. For example:
/*
export interface OrganizationSettings {
  clerkOrgId: string;
  customTheme?: string;
  featureFlags?: Record<string, boolean>;
}
*/

// Add other shared types below as the application evolves.