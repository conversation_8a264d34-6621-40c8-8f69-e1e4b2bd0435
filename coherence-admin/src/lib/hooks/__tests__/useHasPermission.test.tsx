import React from 'react';
import { render, screen } from '@testing-library/react';
import { useHasPermission, useHasAllPermissions, useHasAnyPermission } from '../useHasPermission';
import { AdminSessionContext } from '@/context/AdminSessionContext';

// Mock the useAdminSession hook directly
jest.mock('@/context/AdminSessionContext', () => ({
  useAdminSession: jest.fn()
}));

// Create test components that use our hooks
function TestPermission({ permission }: { permission: string }) {
  const hasPermission = useHasPermission(permission);
  return <div data-testid="result">{hasPermission ? 'Yes' : 'No'}</div>;
}

function TestAllPermissions({ permissions }: { permissions: string[] }) {
  const hasAllPermissions = useHasAllPermissions(permissions);
  return <div data-testid="result">{hasAllPermissions ? 'Yes' : 'No'}</div>;
}

function TestAnyPermission({ permissions }: { permissions: string[] }) {
  const hasAnyPermission = useHasAnyPermission(permissions);
  return <div data-testid="result">{hasAnyPermission ? 'Yes' : 'No'}</div>;
}

// Helper to setup the mock session
function mockSessionContext(mockData: any) {
  const mockUseAdminSession = jest.requireMock('@/context/AdminSessionContext').useAdminSession;
  mockUseAdminSession.mockImplementation(() => ({
    userId: 'test-user',
    sessionId: 'test-session',
    actor: null,
    organization: null,
    tenant: null,
    permissions: [],
    isSystemAdmin: false,
    isLoading: false,
    error: null,
    isAuthenticated: true,
    ...mockData
  }));
}

describe('Permission Hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('useHasPermission', () => {
    it('should return true when user has the specified permission', () => {
      mockSessionContext({
        permissions: ['workflow:read', 'template:read'],
        isSystemAdmin: false
      });
      
      render(<TestPermission permission="workflow:read" />);
      expect(screen.getByTestId('result')).toHaveTextContent('Yes');
    });
    
    it('should return false when user does not have the specified permission', () => {
      mockSessionContext({
        permissions: ['workflow:read'],
        isSystemAdmin: false
      });
      
      render(<TestPermission permission="template:read" />);
      expect(screen.getByTestId('result')).toHaveTextContent('No');
    });
    
    it('should return true for any permission if user is system admin', () => {
      mockSessionContext({
        permissions: [], // No specific permissions
        isSystemAdmin: true
      });
      
      render(<TestPermission permission="some:random:permission" />);
      expect(screen.getByTestId('result')).toHaveTextContent('Yes');
    });
    
    it('should return true for any permission if user has system:* wildcard', () => {
      mockSessionContext({
        permissions: ['system:*', 'workflow:read'],
        isSystemAdmin: false
      });
      
      render(<TestPermission permission="template:create" />);
      expect(screen.getByTestId('result')).toHaveTextContent('Yes');
    });
  });

  describe('useHasAllPermissions', () => {
    it('should return true when user has all specified permissions', () => {
      mockSessionContext({
        permissions: ['workflow:read', 'template:read', 'integration:read'],
        isSystemAdmin: false
      });
      
      render(<TestAllPermissions permissions={['workflow:read', 'template:read']} />);
      expect(screen.getByTestId('result')).toHaveTextContent('Yes');
    });
    
    it('should return false when user is missing any specified permission', () => {
      mockSessionContext({
        permissions: ['workflow:read', 'template:read'],
        isSystemAdmin: false
      });
      
      render(<TestAllPermissions permissions={['workflow:read', 'template:read', 'integration:read']} />);
      expect(screen.getByTestId('result')).toHaveTextContent('No');
    });
    
    it('should return true for any permissions if user is system admin', () => {
      mockSessionContext({
        permissions: [],
        isSystemAdmin: true
      });
      
      render(<TestAllPermissions permissions={['workflow:create', 'template:delete', 'integration:update']} />);
      expect(screen.getByTestId('result')).toHaveTextContent('Yes');
    });
  });

  describe('useHasAnyPermission', () => {
    it('should return true when user has at least one specified permission', () => {
      mockSessionContext({
        permissions: ['workflow:read'],
        isSystemAdmin: false
      });
      
      render(<TestAnyPermission permissions={['workflow:read', 'template:read']} />);
      expect(screen.getByTestId('result')).toHaveTextContent('Yes');
    });
    
    it('should return false when user has none of the specified permissions', () => {
      mockSessionContext({
        permissions: ['integration:read'],
        isSystemAdmin: false
      });
      
      render(<TestAnyPermission permissions={['workflow:read', 'template:read']} />);
      expect(screen.getByTestId('result')).toHaveTextContent('No');
    });
    
    it('should return true for any permissions if user is system admin', () => {
      mockSessionContext({
        permissions: [],
        isSystemAdmin: true
      });
      
      render(<TestAnyPermission permissions={['workflow:create', 'template:delete']} />);
      expect(screen.getByTestId('result')).toHaveTextContent('Yes');
    });
  });
});