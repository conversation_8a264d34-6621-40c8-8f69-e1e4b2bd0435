import { renderHook, act, waitFor } from '@testing-library/react';
import { useChat } from '../../hooks/useChat';
import * as chatClient from '../../chatClient';

// Mock the chat client
jest.mock('../../chatClient', () => ({
  sendMessage: jest.fn(),
  continueConversation: jest.fn(),
  checkWorkflowStatus: jest.fn()
}));

// Mock uuid generation to get predictable IDs
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('test-uuid')
}));

describe('useChat', () => {
  const mockUserId = 'test-user-id';
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    (chatClient.sendMessage as jest.Mock).mockResolvedValue({
      kind: 'reply',
      text: 'System response',
      conversation_id: 'test-conversation-id'
    });
    
    (chatClient.continueConversation as jest.Mock).mockResolvedValue({
      kind: 'reply',
      text: 'Follow-up response',
      conversation_id: 'test-conversation-id'
    });
    
    (chatClient.checkWorkflowStatus as jest.Mock).mockResolvedValue({
      workflow_id: 'test-workflow-id',
      status: 'completed',
      progress: 1,
      result: 'Workflow completed'
    });
    
    // Mock Date.now() to return a consistent timestamp
    jest.spyOn(Date, 'now').mockImplementation(() => 1609459200000); // 2021-01-01
  });
  
  it('should initialize with empty state', () => {
    const { result } = renderHook(() => useChat(mockUserId));
    
    expect(result.current.state).toEqual({
      messages: [],
      conversationId: null,
      isLoading: false,
      activeWorkflows: {},
      error: null,
    });
  });
  
  it('should send a message and update state correctly', async () => {
    const { result } = renderHook(() => useChat(mockUserId));
    
    await act(async () => {
      await result.current.sendMessage('Hello world');
    });
    
    // Should update messages with user message and system response
    expect(result.current.state.messages).toHaveLength(2);
    expect(result.current.state.messages[0].content).toBe('Hello world');
    expect(result.current.state.messages[0].isUser).toBe(true);
    expect(result.current.state.messages[1].content).toBe('System response');
    expect(result.current.state.messages[1].isUser).toBe(false);
    
    // Should update conversation ID
    expect(result.current.state.conversationId).toBe('test-conversation-id');
    
    // Should call the API with correct parameters
    expect(chatClient.sendMessage).toHaveBeenCalledWith('Hello world', mockUserId);
  });
  
  it('should continue a conversation correctly', async () => {
    const { result } = renderHook(() => useChat(mockUserId));
    
    // First send a message to set up conversation ID
    await act(async () => {
      await result.current.sendMessage('Hello world');
    });
    
    // Then continue the conversation
    await act(async () => {
      await result.current.sendMessage('Follow up message');
    });
    
    // Should call continueConversation with correct parameters
    expect(chatClient.continueConversation).toHaveBeenCalledWith(
      'Follow up message', 
      'test-conversation-id', 
      mockUserId
    );
    
    // Should have 4 messages now (2 from user, 2 from system)
    expect(result.current.state.messages).toHaveLength(4);
  });
  
  it('should handle field responses correctly', async () => {
    const { result } = renderHook(() => useChat(mockUserId));
    
    // Set up conversation first
    await act(async () => {
      await result.current.sendMessage('Hello world');
    });
    
    // Handle field response
    await act(async () => {
      await result.current.handleFieldResponse('name', 'John Doe');
    });
    
    // Should call continueConversation with field parameter
    expect(chatClient.continueConversation).toHaveBeenCalledWith(
      'John Doe', 
      'test-conversation-id', 
      mockUserId,
      'name'
    );
    
    // Should add user message with field value
    expect(result.current.state.messages[2].content).toBe('John Doe');
    expect(result.current.state.messages[2].isUser).toBe(true);
  });
  
  it('should handle intent selection correctly', async () => {
    // Mock the last message having options
    const { result } = renderHook(() => useChat(mockUserId));
    
    // Create a mock intent_clarification response
    (chatClient.sendMessage as jest.Mock).mockResolvedValueOnce({
      kind: 'intent_clarification',
      options: [
        { id: 'intent1', text: 'Option 1' },
        { id: 'intent2', text: 'Option 2' }
      ],
      conversation_id: 'test-conversation-id'
    });
    
    // Send message to get clarification response
    await act(async () => {
      await result.current.sendMessage('Ambiguous message');
    });
    
    // Select an intent
    await act(async () => {
      await result.current.handleIntentSelection('intent1');
    });
    
    // Should call continueConversation with intent_selection
    expect(chatClient.continueConversation).toHaveBeenCalledWith(
      'intent1', 
      'test-conversation-id', 
      mockUserId,
      'intent_selection'
    );
    
    // Should add user message with selection text
    expect(result.current.state.messages[2].content).toContain('Selected:');
    expect(result.current.state.messages[2].isUser).toBe(true);
  });
  
  it('should handle async workflow responses', async () => {
    // Mock an async response
    (chatClient.sendMessage as jest.Mock).mockResolvedValueOnce({
      kind: 'async',
      text: 'Processing your request...',
      workflow_id: 'test-workflow-id',
      status_url: '/v1/status/test-workflow-id',
      conversation_id: 'test-conversation-id'
    });
    
    const { result } = renderHook(() => useChat(mockUserId));
    
    await act(async () => {
      await result.current.sendMessage('Start workflow');
    });
    
    // Should add the workflow to active workflows
    expect(result.current.state.activeWorkflows).toHaveProperty('test-workflow-id');
    
    // Wait for polling to complete
    await waitFor(() => {
      expect(chatClient.checkWorkflowStatus).toHaveBeenCalledWith('test-workflow-id');
    });
    
    // Should add completion message when workflow completes
    await waitFor(() => {
      expect(result.current.state.messages).toHaveLength(3);
      expect(result.current.state.messages[2].content).toBe('Workflow completed');
    });
    
    // Should remove the workflow from active workflows
    await waitFor(() => {
      expect(result.current.state.activeWorkflows).not.toHaveProperty('test-workflow-id');
    });
  });
  
  it('should reset conversation correctly', async () => {
    const { result } = renderHook(() => useChat(mockUserId));
    
    // Add some messages first
    await act(async () => {
      await result.current.sendMessage('Hello world');
    });
    
    // Reset the conversation
    act(() => {
      result.current.resetConversation();
    });
    
    // State should be back to initial
    expect(result.current.state).toEqual({
      messages: [],
      conversationId: null,
      isLoading: false,
      activeWorkflows: {},
      error: null,
    });
  });
  
  it('should handle errors correctly', async () => {
    // Mock API error
    (chatClient.sendMessage as jest.Mock).mockRejectedValueOnce(new Error('API Error'));
    
    const { result } = renderHook(() => useChat(mockUserId));
    
    await act(async () => {
      await result.current.sendMessage('Hello world');
    });
    
    // Should set error state
    expect(result.current.state.error).toBeTruthy();
    expect(result.current.state.isLoading).toBe(false);
    
    // User message should still be in the list
    expect(result.current.state.messages).toHaveLength(1);
    expect(result.current.state.messages[0].content).toBe('Hello world');
  });
});