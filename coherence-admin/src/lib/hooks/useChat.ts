import { useState, useReducer, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import * as chatClient from '../chatClient';
import { ResolveResponse, WorkflowStatus } from '../chatClient';
import { useAdminSession } from '@/context/AdminSessionContext';

// Define CRFS metadata interface
interface CRFSMetadata {
  version: string;
  kind: string;
  available_formats?: string[];
  auto_select_enabled?: boolean;
  default_format?: string;
  supports_streaming?: boolean;
  supports_mixed_formats?: boolean;
}

// Define the message interface
export interface Message {
  id: string;
  content: string;
  timestamp: Date;
  isUser: boolean;
  responseType?: 'reply' | 'ask' | 'async' | 'fallback' | 'intent_clarification';
  responseData?: any;
  status?: 'sending' | 'sent' | 'error';
  error?: string;
  crfsMetadata?: CRFSMetadata;
  rawResponse?: any;
}

// Define the chat state interface
interface ChatState {
  messages: Message[];
  conversationId: string | null;
  isLoading: boolean;
  activeWorkflows: Record<string, WorkflowStatus>;
  error: string | null;
  activeField?: string; // For ask responses
}

// Define the chat action types
type ChatAction = 
  | { type: 'SEND_MESSAGE', payload: { message: string, isUser: boolean } }
  | { type: 'RECEIVE_RESPONSE', payload: ResolveResponse }
  | { type: 'SET_LOADING', payload: boolean }
  | { type: 'SET_ERROR', payload: string | null }
  | { type: 'UPDATE_WORKFLOW', payload: { id: string, status: WorkflowStatus } }
  | { type: 'UPDATE_MESSAGE', payload: { id: string, updates: Partial<Message> } }
  | { type: 'RESET_CONVERSATION' };

// Create the chat reducer
const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'SEND_MESSAGE':
      return {
        ...state,
        messages: [
          ...state.messages,
          {
            id: uuidv4(),
            content: action.payload.message,
            timestamp: new Date(),
            isUser: action.payload.isUser,
            status: 'sending',
          },
        ],
      };
    
    case 'RECEIVE_RESPONSE': {
      const response = action.payload;
      console.log('Received response:', response);
      // Extract content based on response type
      let content = '';
      if (response.kind === 'reply') {
        content = response.text || '';
      } else if (response.kind === 'ask') {
        content = response.question || '';
      } else if (response.kind === 'fallback') {
        content = response.text || 'I couldn\'t process that information. Could you try again?';
      } else if (response.kind === 'intent_clarification') {
        content = response.question || 'Could you clarify what you mean?';
        console.log('Intent clarification response:', response);
      } else if (response.kind === 'async') {
        content = 'Processing your request...';
      }
      
      const newMessage: Message = {
        id: uuidv4(),
        content,
        timestamp: new Date(),
        isUser: false,
        responseType: response.kind,
        responseData: response,
        status: 'sent',
      };
      
      // Handle active field for ask responses
      const newActiveField = response.kind === 'ask' ? response.field : undefined;
      
      // Handle tracking async workflows
      const newActiveWorkflows = { ...state.activeWorkflows };
      if (response.kind === 'async' && response.workflow_id) {
        newActiveWorkflows[response.workflow_id] = {
          workflow_id: response.workflow_id,
          status: 'running',
          progress: 0,
          current_step: 'Starting...',
        };
      }
      
      return {
        ...state,
        messages: [...state.messages, newMessage],
        activeField: newActiveField,
        activeWorkflows: newActiveWorkflows,
        conversationId: response.conversation_id || state.conversationId,
      };
    }
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    
    case 'UPDATE_WORKFLOW': {
      const { id, status } = action.payload;
      const newActiveWorkflows = { ...state.activeWorkflows };
      newActiveWorkflows[id] = status;
      
      // Create a completion message if the workflow is finished
      let updatedMessages = [...state.messages];
      if (status.status === 'completed' && status.result) {
        updatedMessages.push({
          id: uuidv4(),
          content: typeof status.result === 'string' 
            ? status.result 
            : status.result.text || JSON.stringify(status.result),
          timestamp: new Date(),
          isUser: false,
          responseType: 'reply',
          responseData: status.result,
          status: 'sent',
        });
        
        // Remove from active workflows if completed
        delete newActiveWorkflows[id];
      }
      
      return {
        ...state,
        messages: updatedMessages,
        activeWorkflows: newActiveWorkflows,
      };
    }
    
    case 'UPDATE_MESSAGE': {
      const { id, updates } = action.payload;
      const updatedMessages = state.messages.map(message => 
        message.id === id ? { ...message, ...updates } : message
      );
      
      return {
        ...state,
        messages: updatedMessages,
      };
    }
    
    case 'RESET_CONVERSATION':
      return {
        messages: [],
        conversationId: null,
        isLoading: false,
        activeWorkflows: {},
        error: null,
        activeField: undefined,
      };
    
    default:
      return state;
  }
};

// Define the return type of the hook
interface UseChatResult {
  state: ChatState;
  sendMessage: (message: string) => Promise<void>;
  resetConversation: () => void;
  handleFieldResponse: (field: string, value: any) => Promise<void>;
  handleIntentSelection: (intentId: string) => Promise<void>;
}

// Polling configuration for async workflows
const POLLING_INTERVALS = [1000, 2000, 3000, 5000, 8000, 13000];
const MAX_POLLING_INTERVAL = 15000;

/**
 * Custom hook for managing chat state and API interactions
 */
export function useChat(userId: string): UseChatResult {
  // Get session data for authentication
  const { token, organization } = useAdminSession();
  
  // Initialize state with useReducer
  const [state, dispatch] = useReducer(chatReducer, {
    messages: [],
    conversationId: null,
    isLoading: false,
    activeWorkflows: {},
    error: null,
  });
  
  // Keep track of polling intervals for async workflows
  const [pollingAttempts, setPollingAttempts] = useState<Record<string, number>>({});
  
  // Helper function to categorize and format error messages
  const formatErrorMessage = useCallback((error: unknown): string => {
    console.error('Chat error:', error);
    
    if (error instanceof Error) {
      // Check for authentication errors
      if (error.message.includes('401') || 
          error.message.includes('403') || 
          error.message.toLowerCase().includes('unauthorized') ||
          error.message.toLowerCase().includes('forbidden')) {
        // Return more specific authentication error messages
        if (error.message.includes('API key')) {
          return 'Authentication failed: Missing or invalid API key. Please ensure your organization has a valid API key.';
        }
        if (error.message.includes('token')) {
          return 'Authentication failed: Invalid or expired token. Please try refreshing the page.';
        }
        if (error.message.includes('permission')) {
          return 'Authorization failed: Insufficient permissions to perform this action.';
        }
        
        // Generic auth error
        return `Authentication failed: ${error.message}`;
      }
      
      // General error with message
      return error.message;
    }
    
    // Fallback for unknown error types
    return 'An unexpected error occurred. Please try again.';
  }, []);

  // Handle sending a new message
  const sendMessage = useCallback(async (message: string) => {
    try {
      // Clear any previous errors
      if (state.error) {
        dispatch({ type: 'SET_ERROR', payload: null });
      }
      
      // Add user message to state
      dispatch({ type: 'SEND_MESSAGE', payload: { message, isUser: true } });
      
      // Set loading state
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Verify we have authentication data
      if (!token) {
        throw new Error('Authentication token missing. Please sign in again.');
      }
      
      if (!organization?.id) {
        throw new Error('Organization context missing. Please select an organization.');
      }
      
      // Prepare request options with authentication
      const options = {
        token,
        orgId: organization.id
      };
      
      // Create context with tenant ID for RLS to work correctly
      const requestContext = {
        tenant_id: organization.id,
        ...state.context
      };
      
      // Send message to API
      const response = state.conversationId
        ? await chatClient.continueConversation(message, state.conversationId, userId, undefined, options)
        : await chatClient.sendMessage(message, userId, undefined, requestContext, options);
      
      // Add response to state
      dispatch({ type: 'RECEIVE_RESPONSE', payload: response });
      
      // Clear loading state
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      // Format and set the error message
      const errorMessage = formatErrorMessage(error);
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      // Log detailed error for debugging
      console.error('Chat error details:', error);
      
      // For certain errors, update the UI to indicate the message failed
      if (state.messages.length > 0) {
        const lastMessage = state.messages[state.messages.length - 1];
        if (lastMessage.isUser && lastMessage.status === 'sending') {
          dispatch({ 
            type: 'UPDATE_MESSAGE', 
            payload: { 
              id: lastMessage.id, 
              updates: { 
                status: 'error',
                error: 'Failed to send'
              } 
            } 
          });
        }
      }
    }
  }, [state.conversationId, state.messages, state.error, state.context, userId, token, organization, formatErrorMessage]);
  
  // Handle field responses for ask type messages
  const handleFieldResponse = useCallback(async (field: string, value: any) => {
    try {
      // Clear any previous errors
      if (state.error) {
        dispatch({ type: 'SET_ERROR', payload: null });
      }
      
      // Format the value as a user-friendly string for display
      const displayValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
      
      // Add user response to state
      dispatch({ type: 'SEND_MESSAGE', payload: { message: displayValue, isUser: true } });
      
      // Set loading state
      dispatch({ type: 'SET_LOADING', payload: true });
      
      if (!state.conversationId) {
        throw new Error('No active conversation. Please start a new chat.');
      }
      
      // Verify we have authentication data
      if (!token) {
        throw new Error('Authentication token missing. Please sign in again.');
      }
      
      if (!organization?.id) {
        throw new Error('Organization context missing. Please select an organization.');
      }
      
      // Prepare request options with authentication
      const options = {
        token,
        orgId: organization.id
      };
      
      // Log field response details for debugging
      console.log(`Sending field response for field ${field}:`, { 
        conversationId: state.conversationId,
        userId,
        displayValue
      });
      
      // Send field response to API
      const response = await chatClient.continueConversation(
        displayValue, 
        state.conversationId, 
        userId,
        field,
        options
      );
      
      // Add response to state
      dispatch({ type: 'RECEIVE_RESPONSE', payload: response });
      
      // Clear loading state
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      // Format and set the error message
      const errorMessage = formatErrorMessage(error);
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      // Log detailed error for debugging
      console.error('Field response error details:', error);
      
      // Update the UI to indicate the message failed
      if (state.messages.length > 0) {
        const lastMessage = state.messages[state.messages.length - 1];
        if (lastMessage.isUser && lastMessage.status === 'sending') {
          dispatch({ 
            type: 'UPDATE_MESSAGE', 
            payload: { 
              id: lastMessage.id, 
              updates: { 
                status: 'error',
                error: 'Failed to process'
              } 
            } 
          });
        }
      }
    }
  }, [state.conversationId, state.messages, state.error, userId, token, organization, formatErrorMessage]);
  
  // Handle intent selection for clarification responses
  const handleIntentSelection = useCallback(async (intentId: string) => {
    try {
      // Clear any previous errors
      if (state.error) {
        dispatch({ type: 'SET_ERROR', payload: null });
      }
      
      // Find the options for the selected intent from the last message
      const lastMessage = state.messages[state.messages.length - 1];
      const intentOptions = lastMessage?.responseData?.options || [];
      const selectedOption = intentOptions.find((option: any) => option.id === intentId);
      
      if (!selectedOption) {
        throw new Error(`Intent option was not found. Please try selecting a different option.`);
      }
      
      // Add user selection to state
      dispatch({ 
        type: 'SEND_MESSAGE', 
        payload: { 
          message: `Selected: ${selectedOption.text}`, 
          isUser: true 
        } 
      });
      
      // Set loading state
      dispatch({ type: 'SET_LOADING', payload: true });
      
      if (!state.conversationId) {
        throw new Error('No active conversation. Please start a new chat.');
      }
      
      // Verify we have authentication data
      if (!token) {
        throw new Error('Authentication token missing. Please sign in again.');
      }
      
      if (!organization?.id) {
        throw new Error('Organization context missing. Please select an organization.');
      }
      
      // Prepare request options with authentication
      const authOptions = {
        token,
        orgId: organization.id
      };
      
      // Log intent selection details for debugging
      console.log(`Sending intent selection:`, { 
        intentId,
        conversationId: state.conversationId,
        userId,
        optionText: selectedOption.text
      });
      
      // Send intent selection to API
      const response = await chatClient.continueConversation(
        intentId, 
        state.conversationId, 
        userId,
        'intent_selection',
        authOptions
      );
      
      // Add response to state
      dispatch({ type: 'RECEIVE_RESPONSE', payload: response });
      
      // Clear loading state
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      // Format and set the error message
      const errorMessage = formatErrorMessage(error);
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      // Log detailed error for debugging
      console.error('Intent selection error details:', error);
      
      // Update the UI to indicate the selection failed
      if (state.messages.length > 0) {
        const lastMessage = state.messages[state.messages.length - 1];
        if (lastMessage.isUser && lastMessage.status === 'sending') {
          dispatch({ 
            type: 'UPDATE_MESSAGE', 
            payload: { 
              id: lastMessage.id, 
              updates: { 
                status: 'error',
                error: 'Failed to process selection'
              } 
            } 
          });
        }
      }
    }
  }, [state.messages, state.conversationId, state.error, userId, token, organization, formatErrorMessage]);
  
  // Reset the conversation
  const resetConversation = useCallback(() => {
    dispatch({ type: 'RESET_CONVERSATION' });
  }, []);
  
  // Poll for workflow status updates
  useEffect(() => {
    const workflowIds = Object.keys(state.activeWorkflows);
    
    if (workflowIds.length === 0) {
      return;
    }
    
    // Track active polling intervals
    const intervals: Record<string, NodeJS.Timeout> = {};
    
    // Setup polling for each active workflow
    workflowIds.forEach(workflowId => {
      const pollWorkflow = async () => {
        try {
          // Prepare request options with authentication
          const options = {
            token,
            orgId: organization?.id
          };
          
          // Get current status
          const status = await chatClient.checkWorkflowStatus(workflowId, options);
          
          // Update state with new status
          dispatch({ 
            type: 'UPDATE_WORKFLOW', 
            payload: { id: workflowId, status } 
          });
          
          // If workflow is still running, continue polling with backoff
          if (status.status === 'running') {
            const currentAttempt = pollingAttempts[workflowId] || 0;
            const nextAttempt = currentAttempt + 1;
            
            setPollingAttempts(prev => ({
              ...prev,
              [workflowId]: nextAttempt
            }));
            
            // Calculate next polling interval with exponential backoff
            const intervalIndex = Math.min(currentAttempt, POLLING_INTERVALS.length - 1);
            const nextInterval = POLLING_INTERVALS[intervalIndex] || MAX_POLLING_INTERVAL;
            
            // Setup next polling interval
            intervals[workflowId] = setTimeout(pollWorkflow, nextInterval);
          } else {
            // If workflow is complete or failed, remove it from polling
            setPollingAttempts(prev => {
              const newAttempts = { ...prev };
              delete newAttempts[workflowId];
              return newAttempts;
            });
          }
        } catch (error) {
          console.error(`Error polling workflow ${workflowId}:`, error);
          
          // Retry with backoff on error
          const currentAttempt = pollingAttempts[workflowId] || 0;
          const nextAttempt = currentAttempt + 1;
          
          setPollingAttempts(prev => ({
            ...prev,
            [workflowId]: nextAttempt
          }));
          
          // Calculate next polling interval with exponential backoff
          const intervalIndex = Math.min(currentAttempt, POLLING_INTERVALS.length - 1);
          const nextInterval = POLLING_INTERVALS[intervalIndex] || MAX_POLLING_INTERVAL;
          
          // Setup next polling interval
          intervals[workflowId] = setTimeout(pollWorkflow, nextInterval);
        }
      };
      
      // Start polling immediately
      pollWorkflow();
    });
    
    // Cleanup function to clear all intervals
    return () => {
      Object.values(intervals).forEach(interval => clearTimeout(interval));
    };
  }, [state.activeWorkflows, pollingAttempts, token, organization?.id]);
  
  return {
    state,
    sendMessage,
    resetConversation,
    handleFieldResponse,
    handleIntentSelection,
  };
}