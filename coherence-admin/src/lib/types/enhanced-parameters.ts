/**
 * Enhanced parameter schemas with full OpenAPI constraint support
 */

export interface EnhancedParameterSchema {
  name: string;
  type: string;
  in_location: string; // "path" | "query" | "header" | "body"
  required: boolean;
  description?: string;
  
  // Enhanced schema fields from OpenAPI specification
  default?: any;
  example?: any;
  enum?: any[];
  format?: string;
  pattern?: string;
  
  // Validation constraints
  minimum?: number;
  maximum?: number;
  exclusive_minimum?: number;
  exclusive_maximum?: number;
  multiple_of?: number;
  
  // String constraints
  min_length?: number;
  max_length?: number;
  
  // Array constraints
  min_items?: number;
  max_items?: number;
  unique_items?: boolean;
  
  // Object constraints
  min_properties?: number;
  max_properties?: number;
  
  // Additional metadata
  deprecated?: boolean;
  read_only?: boolean;
  write_only?: boolean;
  nullable?: boolean;
  
  // Media type for request body parameters
  media_type?: string;
  
  // Raw schema for complex types
  raw_schema?: Record<string, any>;
}

export interface EnhancedAPIEndpoint {
  endpoint_id: string;
  path: string;
  method: string;
  operation_id?: string;
  summary?: string;
  description?: string;
  tags: string[];
  deprecated: boolean;
  enabled: boolean;
  enhanced_parameters: EnhancedParameterSchema[];
  response_schemas: Record<string, any>;
  operation_security: Array<Record<string, any>>;
  openapi_snippet?: Record<string, any>; // For backwards compatibility
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ParameterConstraints {
  minimum?: number;
  maximum?: number;
  exclusive_minimum?: number;
  exclusive_maximum?: number;
  multiple_of?: number;
  min_length?: number;
  max_length?: number;
  pattern?: string;
  min_items?: number;
  max_items?: number;
  unique_items?: boolean;
  enum?: any[];
}