/**
 * Client-side schema validation utilities for OpenAPI parameters
 */

import { EnhancedParameterSchema, ValidationResult } from '@/lib/types/enhanced-parameters';

export class SchemaValidator {
  /**
   * Validate a parameter value against its enhanced schema
   */
  static validateParameter(value: any, schema: EnhancedParameterSchema): ValidationResult {
    const result: ValidationResult = { valid: true, errors: [], warnings: [] };
    
    // Check required
    if (schema.required && (value === undefined || value === null || value === '')) {
      result.valid = false;
      result.errors.push(`${schema.name} is required`);
      return result;
    }

    // Skip validation for empty optional values
    if (!schema.required && (value === undefined || value === null || value === '')) {
      return result;
    }

    // Type-specific validation
    try {
      this.validateByType(value, schema, result);
      this.validateConstraints(value, schema, result);
      this.validateEnum(value, schema, result);
      this.validateFormat(value, schema, result);
    } catch (error) {
      result.valid = false;
      result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Validate multiple parameters at once
   */
  static validateParameters(values: Record<string, any>, schemas: EnhancedParameterSchema[]): Record<string, ValidationResult> {
    const results: Record<string, ValidationResult> = {};
    
    for (const schema of schemas) {
      const value = values[schema.name];
      results[schema.name] = this.validateParameter(value, schema);
    }
    
    return results;
  }

  /**
   * Get all validation errors from multiple parameter validations
   */
  static getAllErrors(validationResults: Record<string, ValidationResult>): string[] {
    const allErrors: string[] = [];
    
    for (const [paramName, result] of Object.entries(validationResults)) {
      if (!result.valid) {
        allErrors.push(...result.errors);
      }
    }
    
    return allErrors;
  }

  /**
   * Check if all validations passed
   */
  static allValid(validationResults: Record<string, ValidationResult>): boolean {
    return Object.values(validationResults).every(result => result.valid);
  }

  /**
   * Get appropriate HTML input type for a parameter schema
   */
  static getInputType(schema: EnhancedParameterSchema): string {
    if (schema.type === 'boolean') return 'checkbox';
    if (schema.format === 'password') return 'password';
    if (schema.format === 'email') return 'email';
    if (schema.format === 'uri') return 'url';
    if (schema.format === 'date') return 'date';
    if (schema.format === 'date-time') return 'datetime-local';
    if (schema.type === 'number' || schema.type === 'integer') return 'number';
    if (schema.format === 'binary' || schema.format === 'byte') return 'file';
    return 'text';
  }

  /**
   * Get user-friendly type description
   */
  static getTypeDescription(schema: EnhancedParameterSchema): string {
    if (schema.enum) return 'Select from options';
    if (schema.format === 'email') return 'Email address';
    if (schema.format === 'uri') return 'URL/URI';
    if (schema.format === 'date') return 'Date (YYYY-MM-DD)';
    if (schema.format === 'date-time') return 'Date & Time (ISO 8601)';
    if (schema.format === 'password') return 'Password';
    if (schema.format === 'binary') return 'File upload';
    if (schema.type === 'boolean') return 'True/False';
    if (schema.type === 'integer') return 'Integer';
    if (schema.type === 'number') return 'Number';
    if (schema.type === 'array') return 'Array (JSON format)';
    if (schema.type === 'object') return 'Object (JSON format)';
    return 'Text';
  }

  /**
   * Generate helpful constraint description
   */
  static getConstraintDescription(schema: EnhancedParameterSchema): string[] {
    const constraints: string[] = [];

    if (schema.min_length !== undefined) {
      constraints.push(`Minimum ${schema.min_length} characters`);
    }
    if (schema.max_length !== undefined) {
      constraints.push(`Maximum ${schema.max_length} characters`);
    }
    if (schema.minimum !== undefined) {
      constraints.push(`Minimum value: ${schema.minimum}`);
    }
    if (schema.maximum !== undefined) {
      constraints.push(`Maximum value: ${schema.maximum}`);
    }
    if (schema.pattern) {
      constraints.push(`Must match pattern: ${schema.pattern}`);
    }
    if (schema.multiple_of !== undefined) {
      constraints.push(`Must be multiple of ${schema.multiple_of}`);
    }

    return constraints;
  }

  private static validateByType(value: any, schema: EnhancedParameterSchema, result: ValidationResult): void {
    switch (schema.type) {
      case 'string':
        if (typeof value !== 'string') {
          result.valid = false;
          result.errors.push(`Expected string, got ${typeof value}`);
        }
        break;
      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          result.valid = false;
          result.errors.push(`Expected number, got ${typeof value}`);
        }
        break;
      case 'integer':
        if (typeof value !== 'number' || !Number.isInteger(value)) {
          result.valid = false;
          result.errors.push(`Expected integer, got ${typeof value}`);
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          result.valid = false;
          result.errors.push(`Expected boolean, got ${typeof value}`);
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          result.valid = false;
          result.errors.push(`Expected array, got ${typeof value}`);
        }
        break;
      case 'object':
        if (typeof value !== 'object' || Array.isArray(value) || value === null) {
          result.valid = false;
          result.errors.push(`Expected object, got ${typeof value}`);
        }
        break;
    }
  }

  private static validateConstraints(value: any, schema: EnhancedParameterSchema, result: ValidationResult): void {
    // String constraints
    if (schema.type === 'string' && typeof value === 'string') {
      if (schema.min_length !== undefined && value.length < schema.min_length) {
        result.valid = false;
        result.errors.push(`String length ${value.length} is less than minimum ${schema.min_length}`);
      }
      if (schema.max_length !== undefined && value.length > schema.max_length) {
        result.valid = false;
        result.errors.push(`String length ${value.length} exceeds maximum ${schema.max_length}`);
      }
    }

    // Numeric constraints
    if ((schema.type === 'number' || schema.type === 'integer') && typeof value === 'number') {
      if (schema.minimum !== undefined && value < schema.minimum) {
        result.valid = false;
        result.errors.push(`Value ${value} is less than minimum ${schema.minimum}`);
      }
      if (schema.maximum !== undefined && value > schema.maximum) {
        result.valid = false;
        result.errors.push(`Value ${value} exceeds maximum ${schema.maximum}`);
      }
      if (schema.exclusive_minimum !== undefined && value <= schema.exclusive_minimum) {
        result.valid = false;
        result.errors.push(`Value ${value} must be greater than ${schema.exclusive_minimum}`);
      }
      if (schema.exclusive_maximum !== undefined && value >= schema.exclusive_maximum) {
        result.valid = false;
        result.errors.push(`Value ${value} must be less than ${schema.exclusive_maximum}`);
      }
      if (schema.multiple_of !== undefined && value % schema.multiple_of !== 0) {
        result.valid = false;
        result.errors.push(`Value ${value} is not a multiple of ${schema.multiple_of}`);
      }
    }

    // Array constraints
    if (schema.type === 'array' && Array.isArray(value)) {
      if (schema.min_items !== undefined && value.length < schema.min_items) {
        result.valid = false;
        result.errors.push(`Array has ${value.length} items, minimum is ${schema.min_items}`);
      }
      if (schema.max_items !== undefined && value.length > schema.max_items) {
        result.valid = false;
        result.errors.push(`Array has ${value.length} items, maximum is ${schema.max_items}`);
      }
      if (schema.unique_items && new Set(value).size !== value.length) {
        result.valid = false;
        result.errors.push('Array items must be unique');
      }
    }
  }

  private static validateEnum(value: any, schema: EnhancedParameterSchema, result: ValidationResult): void {
    if (schema.enum && !schema.enum.includes(value)) {
      result.valid = false;
      result.errors.push(`Value '${value}' is not in allowed values: ${schema.enum.join(', ')}`);
    }
  }

  private static validateFormat(value: any, schema: EnhancedParameterSchema, result: ValidationResult): void {
    if (schema.type !== 'string' || typeof value !== 'string') {
      return;
    }

    switch (schema.format) {
      case 'email':
        if (!this.isValidEmail(value)) {
          result.valid = false;
          result.errors.push('Invalid email format');
        }
        break;
      case 'uri':
        if (!this.isValidUri(value)) {
          result.valid = false;
          result.errors.push('Invalid URI format');
        }
        break;
      case 'date':
        if (!this.isValidDate(value)) {
          result.valid = false;
          result.errors.push('Invalid date format (expected YYYY-MM-DD)');
        }
        break;
      case 'date-time':
        if (!this.isValidDateTime(value)) {
          result.valid = false;
          result.errors.push('Invalid date-time format (expected ISO 8601)');
        }
        break;
      case 'uuid':
        if (!this.isValidUuid(value)) {
          result.valid = false;
          result.errors.push('Invalid UUID format');
        }
        break;
    }

    // Pattern validation
    if (schema.pattern) {
      try {
        const regex = new RegExp(schema.pattern);
        if (!regex.test(value)) {
          result.valid = false;
          result.errors.push(`Value does not match pattern: ${schema.pattern}`);
        }
      } catch (error) {
        result.warnings.push(`Invalid regex pattern in schema: ${schema.pattern}`);
      }
    }
  }

  private static isValidEmail(value: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  }

  private static isValidUri(value: string): boolean {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  }

  private static isValidDate(value: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(value)) return false;
    const date = new Date(value);
    return date.toISOString().slice(0, 10) === value;
  }

  private static isValidDateTime(value: string): boolean {
    try {
      const date = new Date(value);
      return !isNaN(date.getTime()) && date.toISOString() === value;
    } catch {
      return false;
    }
  }

  private static isValidUuid(value: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(value);
  }
}