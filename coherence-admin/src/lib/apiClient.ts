// For client-side calls, you'd typically get the token from useAuth()

export interface ApiClientOptions extends RequestInit {
  orgId?: string | null;
  token?: string | null; // Add token support
  timeout?: number; // Add support for request timeout
}

async function apiClient<T = unknown>( // Changed T = any to T = unknown
  endpoint: string,
  options: ApiClientOptions = {}
): Promise<T> {
  const { orgId, token, timeout, ...fetchOptions } = options;
  const headers = new Headers(fetchOptions.headers || {});
  
  // Log the request size if body is provided
  if (fetchOptions.body) {
    const bodySize = typeof fetchOptions.body === 'string' ? fetchOptions.body.length : 'unknown';
    console.log(`Request body size: ${bodySize} characters`);
  }

  // Add Authorization header if token is provided
  if (token) {
    headers.append('Authorization', `Bearer ${token}`);
  }

  if (orgId) {
    headers.append('X-Tenant-ID', orgId);
    console.log(`[apiClient] Setting X-Tenant-ID header: ${orgId}`);
  } else {
    console.log(`[apiClient] No orgId provided, X-Tenant-ID header not set`);
  }
  headers.append('Content-Type', 'application/json');

  // Check if this is a Next.js API route (starts with /api/) or a backend endpoint
  let fullUrl: string;
  if (endpoint.startsWith('/api/')) {
    // This is a Next.js API route - don't prepend the backend URL
    fullUrl = endpoint;
  } else if (endpoint.startsWith('http')) {
    // This is already a full URL
    fullUrl = endpoint;
  } else {
    // This is a backend endpoint - prepend the API base URL
    let apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_COHERENCE_API_URL || 'http://localhost:8001/v1';
    
    // Handle Docker vs browser environments
    if (typeof window !== 'undefined') {
      // Browser environment - use public URLs
      // Replace Docker hostnames with localhost for browser requests
      apiBaseUrl = apiBaseUrl
        .replace('http://coherence-api:8000', 'http://localhost:8001')
        .replace('http://coherence-api:8001', 'http://localhost:8001');
      console.log(`[apiClient] Browser environment - adjusted API URL: ${apiBaseUrl}`);
    } else {
      // Server environment - keep Docker inter-container URLs if NODE_ENV is production
      if (process.env.NODE_ENV === 'production' && process.env.API_URL) {
        // Override with the internal API_URL for server-side API calls
        apiBaseUrl = process.env.API_URL;
        console.log(`[apiClient] Server environment in production - using API_URL: ${apiBaseUrl}`);
      }
    }
    
    // Check for duplicate /v1 prefixes (if API base URL already has /v1 and endpoint also starts with /v1)
    if (apiBaseUrl.endsWith('/v1') && endpoint.startsWith('/v1')) {
      // Remove duplicate /v1 prefix from endpoint
      endpoint = endpoint.replace(/^\/v1/, '');
      console.log(`[apiClient] Detected duplicate /v1 prefix - fixed to: ${apiBaseUrl}${endpoint}`);
    }
    
    fullUrl = `${apiBaseUrl}${endpoint}`;
  }
  
  console.log(`Making API call to: ${fullUrl}`);
  console.log(`With Authorization header: ${token ? 'Yes' : 'No'}`);
  
  // Log all headers for debugging
  const headerEntries = [...headers.entries()];
  console.log(`Request headers:`, Object.fromEntries(headerEntries.map(([key, value]) => 
    [key, key === 'Authorization' ? 'Bearer [REDACTED]' : value]
  )));
  
  // Get the method, defaulting to 'GET'
  const method = fetchOptions.method || 'GET';
  
  // Log the HTTP method for debugging
  console.log(`[apiClient] HTTP Method: ${method}`);
  
  // For DELETE requests, ensure we don't send a body if none is specified
  // This avoids errors in some browsers/APIs that don't like empty bodies with DELETE
  const requestOptions = {
    ...fetchOptions,
    headers,
    signal: timeout ? AbortSignal.timeout(timeout) : undefined,
  };
  
  // Log the final URL and request details for debugging
  console.log(`[apiClient] Final URL: ${fullUrl} (Method: ${method}, Timeout: ${timeout || 'default'})`);
  
  // If it's a DELETE request with no body, ensure body is undefined rather than null
  if (method === 'DELETE' && !fetchOptions.body) {
    delete requestOptions.body;
  }
  
  const response = await fetch(fullUrl, requestOptions);

  if (!response.ok) {
    let errorData;
    let responseBody = '';
    try {
      // Try to get response as text first
      responseBody = await response.text();
      
      // Then try to parse it as JSON
      try {
        errorData = JSON.parse(responseBody);
      } catch (jsonError) {
        // If it's not valid JSON, use the text response
        console.warn("Response is not valid JSON, using text content", jsonError);
        errorData = { 
          message: responseBody || response.statusText || 'An unknown error occurred',
          parseError: true
        };
      }
    } catch (readError) {
      console.error("Failed to read response body", readError);
      errorData = { message: response.statusText || 'An unknown error occurred' };
    }
    
    // Include more details from the errorData if available
    const errorMessage = errorData?.detail || errorData?.message || errorData?.error || `HTTP error ${response.status}`;
    
    // Create a more informative error
    const error = new Error(errorMessage);
    
    // Add additional properties to the error object
    Object.assign(error, {
      status: response.status,
      statusText: response.statusText,
      url: response.url,
      responseBody: responseBody,
      errorData: errorData
    });
    
    // Log detailed error information
    const errorDetails = {
      message: errorMessage,
      endpoint: endpoint,
      response: {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries([...response.headers.entries()]),
        data: errorData,
        body: responseBody.length > 1000 ? responseBody.substring(0, 1000) + '...' : responseBody
      },
      requestInfo: {
        url: fullUrl,
        method,
        bodySize: fetchOptions.body ? (typeof fetchOptions.body === 'string' ? fetchOptions.body.length : 'unknown') : 'none'
      }
    };
    
    console.error(`API error (${response.status}):`, errorDetails);
    
    // Set detailed error message
    let detailedMessage = `HTTP error ${response.status}`;
    if (errorMessage && errorMessage !== `HTTP error ${response.status}`) {
      detailedMessage += `: ${errorMessage}`;
    }
    if (responseBody && responseBody.length < 200 && !detailedMessage.includes(responseBody)) {
      detailedMessage += ` - Response: ${responseBody}`;
    }
    
    // Create a new error with the detailed message
    const detailedError = new Error(detailedMessage);
    
    // Copy the properties from the original error to maintain compatibility
    Object.assign(detailedError, {
      status: response.status,
      statusText: response.statusText,
      url: response.url,
      responseBody: responseBody,
      errorData: errorData
    });
    
    throw detailedError;
  }

  // Handle cases where response might be empty (e.g., 204 No Content)
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    const jsonData = await response.json();
    console.log(`API response data (${endpoint}):`, jsonData);
    return jsonData as T;
  } else {
    // For non-JSON responses or empty responses, resolve with null or response text
    // This part might need adjustment based on expected non-JSON responses
    const text = await response.text();
    console.log(`API response text (${endpoint}):`, text.substring(0, 100) + (text.length > 100 ? '...' : ''));
    return text ? (JSON.parse(text) as T) : (null as unknown as T); // Attempt to parse if text is not empty, else null
  }
}

// Specialized method for DELETE operations
async function deleteResource(
  endpoint: string,
  options: ApiClientOptions = {}
): Promise<void> {
  // Use the main apiClient's URL construction logic to ensure consistency
  // We'll reuse all the URL construction logic from the main apiClient function
  const { orgId, token, ...fetchOptions } = options;
  const headers = new Headers(fetchOptions.headers || {});

  if (token) {
    headers.append('Authorization', `Bearer ${token}`);
  }

  if (orgId) {
    headers.append('X-Tenant-ID', orgId);
    console.log(`[apiClient-delete] Setting X-Tenant-ID header: ${orgId}`);
  } else {
    console.log(`[apiClient-delete] No orgId provided, X-Tenant-ID header not set`);
  }
  headers.append('Content-Type', 'application/json');

  // Check if this is a Next.js API route (starts with /api/) or a backend endpoint
  let fullUrl: string;
  if (endpoint.startsWith('/api/')) {
    fullUrl = endpoint;
  } else if (endpoint.startsWith('http')) {
    fullUrl = endpoint;
  } else {
    let apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_COHERENCE_API_URL || 'http://localhost:8001/v1';
    
    if (typeof window !== 'undefined') {
      apiBaseUrl = apiBaseUrl
        .replace('http://coherence-api:8000', 'http://localhost:8001')
        .replace('http://coherence-api:8001', 'http://localhost:8001');
    } else if (process.env.NODE_ENV === 'production' && process.env.API_URL) {
      apiBaseUrl = process.env.API_URL;
    }
    
    // Handle both admin and v1 endpoints properly
    // First, ensure apiBaseUrl doesn't have duplicate /v1 for admin routes
    let adjustedEndpoint = endpoint;
    
    // For admin endpoints, make sure to preserve the full path
    if (endpoint.startsWith('/admin/')) {
      // If apiBaseUrl ends with /v1, use the base URL without /v1 suffix
      if (apiBaseUrl.endsWith('/v1')) {
        // Remove the /v1 suffix from the apiBaseUrl
        apiBaseUrl = apiBaseUrl.substring(0, apiBaseUrl.length - 3);
      }
    } 
    // For other endpoints (like /v1/...), fix duplicate /v1 prefixes
    else if (apiBaseUrl.endsWith('/v1') && endpoint.startsWith('/v1')) {
      adjustedEndpoint = endpoint.replace(/^\/v1/, '');
    }
    
    fullUrl = `${apiBaseUrl}${adjustedEndpoint}`;
  }
  
  console.log(`[apiClient-delete] Making DELETE request to: ${fullUrl}`);
  console.log(`[apiClient-delete] With Authorization header: ${token ? 'Yes' : 'No'}`);
  
  // Log all headers for debugging
  const headerEntries = [...headers.entries()];
  console.log(`[apiClient-delete] Request headers:`, Object.fromEntries(headerEntries.map(([key, value]) => 
    [key, key === 'Authorization' ? 'Bearer [REDACTED]' : value]
  )));
  
  const response = await fetch(fullUrl, {
    method: 'DELETE',
    headers,
  });

  if (!response.ok) {
    let errorData;
    let errorText;
    try {
      // Try to get the response as text first
      errorText = await response.text();
      console.log(`[apiClient-delete] Error response text: ${errorText}`);
      
      // Then try to parse it as JSON
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { message: response.statusText || 'An unknown error occurred' };
      }
    } catch (e) {
      console.error(`[apiClient-delete] Error reading response: ${e}`);
      errorData = { message: response.statusText || 'An unknown error occurred' };
    }
    
    console.error(`[apiClient-delete] Error status: ${response.status}`, errorData);
    const errorMessage = errorData?.detail || errorData?.message || `HTTP error ${response.status}`;
    throw new Error(errorMessage);
  }
  
  // For successful DELETE operations, no content is expected (204)
  return;
}

// Define utility methods that wrap the main apiClient function
const api = {
  get: <T>(url: string, options?: ApiClientOptions) => 
    apiClient<T>(url, { ...options, method: 'GET' }),
  
  post: <T>(url: string, data: any, options?: ApiClientOptions) => 
    apiClient<T>(url, { 
      ...options, 
      method: 'POST',
      body: JSON.stringify(data)
    }),
  
  put: <T>(url: string, data: any, options?: ApiClientOptions) => 
    apiClient<T>(url, { 
      ...options, 
      method: 'PUT',
      body: JSON.stringify(data)
    }),
  
  patch: <T>(url: string, data: any, options?: ApiClientOptions) => 
    apiClient<T>(url, { 
      ...options, 
      method: 'PATCH',
      body: JSON.stringify(data)
    }),
  
  delete: (url: string, options?: ApiClientOptions) => 
    deleteResource(url, options)
};

// Export both the main apiClient and the specialized DELETE method
export { deleteResource, apiClient };
export default apiClient;
export { api };