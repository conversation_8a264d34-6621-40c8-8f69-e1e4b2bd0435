import { type Metadata } from 'next'
import {
  <PERSON><PERSON>rovider,
} from '@clerk/nextjs'
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import { JetBrains_Mono, Orbitron } from 'next/font/google'
import { AdminSessionProvider } from '@/context/AdminSessionContext';
import { ThemeProvider } from '@/context/ThemeProvider';
import './globals.css'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

const jetbrainsMono = JetBrains_Mono({
  variable: '--font-jetbrains-mono',
  subsets: ['latin'],
})

const orbitron = Orbitron({
  variable: '--font-orbitron',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Coherence Admin Dashboard',
  description: 'AI-powered workflow management system',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en" className="h-full">
        <body className={`${geistSans.variable} ${geistMono.variable} ${jetbrainsMono.variable} ${orbitron.variable} font-mono antialiased h-full bg-cw-bg-bottom text-cw-foreground`}>
          <AdminSessionProvider>
            <ThemeProvider>
              <div className="min-h-screen bg-gradient-to-b from-cw-bg-top to-cw-bg-bottom">
                {children}
              </div>
            </ThemeProvider>
          </AdminSessionProvider>
        </body>
      </html>
    </ClerkProvider>
  )
}
