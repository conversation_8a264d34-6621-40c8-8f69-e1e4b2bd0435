'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
// import ProtectedRoute from '@/components/auth/ProtectedRoute'; // Corrected import - REMOVED
import { useAdminSession } from '@/context/AdminSessionContext';
import { useAuth } from '@clerk/nextjs'; // Added for getToken
import apiClient from '@/lib/apiClient';
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path
import { useHasPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path

// Mirroring the TenantRead schema from backend for type safety
interface TenantData {
  id: string; // This is the internal UUID, but the page is accessed by clerk_org_id
  name: string;
  clerk_org_id: string;
  industry_pack?: string | null;
  compliance_tier?: string | null;
  settings?: Record<string, string> | null;
  created_at: string;
  updated_at: string;
}

export default function TenantDashboardPage() {
  const params = useParams();
  const tenantIdFromPath = params.id as string; // This will be the clerk_org_id
  const { isLoading: isSessionLoading, isAuthenticated } = useAdminSession(); // Corrected destructuring - removed orgId
  const { getToken } = useAuth(); // For API client
  const [tenantData, setTenantData] = useState<TenantData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const hasPermission = useHasPermission("organization:view_own_dashboard" as Permission);

  useEffect(() => {
    if (isSessionLoading) {
      // Wait for session and permission check to complete
      setLoading(true); // Keep loading true while session is loading
      return;
    }

    if (!isAuthenticated) {
      setError("User session not found or not authenticated. Cannot fetch tenant data.");
      setLoading(false);
      return;
    }

    if (!hasPermission) {
      setError("You do not have permission to view this page.");
      setLoading(false);
      return;
    }

    // At this point, user is authenticated and has permission
    if (tenantIdFromPath && getToken) {
      setLoading(true);
      const fetchData = async () => {
        try {
          const token = await getToken();
          if (!token) {
            setError("Authentication token not available.");
            setLoading(false);
            return;
          }
          const response = await apiClient<TenantData>(
            `/admin/tenants/${tenantIdFromPath}`,
            { token, orgId: tenantIdFromPath } // Assuming tenantIdFromPath is the orgId for this context
          );
          setTenantData(response);
          setError(null);
        } catch (err: unknown) {
          console.error('Failed to fetch tenant data:', err);
          let displayError = 'Failed to load tenant information. You may not have permission to view this tenant or it may not exist.';
          if (err instanceof Error) {
            displayError = err.message; // Default to standard error message
            if (typeof err === 'object' && err !== null && 'responseBody' in err) {
              const responseBody = (err as { responseBody?: unknown }).responseBody;
              if (typeof responseBody === 'object' && responseBody !== null && 'detail' in responseBody) {
                const detail = (responseBody as { detail?: unknown }).detail;
                if (typeof detail === 'string') {
                  displayError = detail;
                }
              }
            }
          } else if (typeof err === 'string') {
            displayError = err;
          }
          setError(displayError);
          setTenantData(null);
        } finally {
          setLoading(false);
        }
      };
      fetchData();
    } else {
      // Should not happen if tenantIdFromPath is always present from router
      setError("Tenant ID not found in path.");
      setLoading(false);
    }
  }, [tenantIdFromPath, isAuthenticated, isSessionLoading, getToken, hasPermission]);

  if (isSessionLoading || loading) {
    return <p>Loading tenant information...</p>;
  }

  if (!hasPermission) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Access Denied: </strong>
          <span className="block sm:inline">You do not have permission to view this page.</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }
  
  if (!tenantData) {
    return (
      <div className="container mx-auto p-4">
        <p>No tenant data available or tenant not found.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">
        Tenant Dashboard {tenantData ? `- ${tenantData.name}` : ''}
      </h1>
      <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <div className="mb-4">
          <p className="text-gray-700 text-sm font-bold mb-1">Tenant Name:</p>
          <p className="text-gray-900 text-lg">{tenantData.name}</p>
        </div>
        <div className="mb-4">
          <p className="text-gray-700 text-sm font-bold mb-1">Clerk Organization ID:</p>
          <p className="text-gray-900">{tenantData.clerk_org_id}</p>
        </div>
          <div className="mb-4">
          <p className="text-gray-700 text-sm font-bold mb-1">Internal ID:</p>
          <p className="text-gray-900">{tenantData.id}</p>
        </div>
        <div className="mb-4">
          <p className="text-gray-700 text-sm font-bold mb-1">Creation Date:</p>
          <p className="text-gray-900">
            {new Date(tenantData.created_at).toLocaleString()}
          </p>
        </div>
        {tenantData.industry_pack && (
          <div className="mb-4">
            <p className="text-gray-700 text-sm font-bold mb-1">Industry Pack:</p>
            <p className="text-gray-900">{tenantData.industry_pack}</p>
          </div>
        )}
        {tenantData.compliance_tier && (
          <div className="mb-4">
            <p className="text-gray-700 text-sm font-bold mb-1">Compliance Tier:</p>
            <p className="text-gray-900">{tenantData.compliance_tier}</p>
          </div>
        )}
          <div className="mb-4">
          <p className="text-gray-700 text-sm font-bold mb-1">Last Updated:</p>
          <p className="text-gray-900">
            {new Date(tenantData.updated_at).toLocaleString()}
          </p>
        </div>
        {/* Add more fields as necessary, e.g., status if it becomes available */}
      </div>
    </div>
  );
}