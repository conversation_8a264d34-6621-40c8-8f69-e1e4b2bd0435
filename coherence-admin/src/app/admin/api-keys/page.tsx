'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useHasAnyPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path
import apiClient, { api } from '@/lib/apiClient';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

// Define interfaces for API key data
interface ApiKey {
  id: string;
  name: string;
  key_prefix: string;
  created_at: string;
  revoked: boolean;
  last_used_at?: string;
  expires_at?: string;
}

interface ApiKeyCreateResponse extends ApiKey {
  key: string; // Full key value (only returned once)
}

export default function ApiKeysPage() {
  const adminSession = useAdminSession();
  const router = useRouter();
  const orgId = adminSession.organization?.id;
  const token = adminSession.token;
  
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newKeyName, setNewKeyName] = useState('');
  const [showDialog, setShowDialog] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string | null>(null);
  const [createKeyLoading, setCreateKeyLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  // const [hasPermission, setHasPermission] = useState<boolean | undefined>(undefined); // Replaced by hook

  const apiKeyPermissions: Permission[] = [
    'api-key:read' as Permission,
    'api-key:*' as Permission,
    'api-keys:read' as Permission,
    'api-keys:*' as Permission,
    'system:*' as Permission
  ];
  const canViewApiKeys = useHasAnyPermission(apiKeyPermissions);

  // Effect for permission checks and authentication state
  useEffect(() => {
    if (adminSession.isLoading) {
      setLoading(true); // Keep loading true while session is loading
      return;
    }

    if (!adminSession.isAuthenticated) {
      setLoading(false);
      // setHasPermission(false); // Not needed
      setError("User not authenticated.");
      return;
    }

    // const isSystemAdmin = (
    //   adminSession.actor &&
    //   (adminSession.actor as any)?.publicMetadata?.isSystemAdmin === true
    // );
    // const hasApiKeyPermission = Array.isArray(adminSession.permissions) && (
    //   adminSession.permissions.includes('api-key:read') ||
    //   adminSession.permissions.includes('api-key:*') ||
    //   adminSession.permissions.includes('api-keys:read') ||
    //   adminSession.permissions.includes('api-keys:*') ||
    //   adminSession.permissions.includes('system:*')
    // );
    // const canViewApiKeysCurrent = isSystemAdmin || hasApiKeyPermission;
    // setHasPermission(canViewApiKeysCurrent); // Not needed

    if (!canViewApiKeys) {
      setLoading(false);
      setError("You do not have permission to view API keys.");
      return;
    }

    // Only fetch if authenticated, has proper permissions, and hasn't loaded data yet
    if (!dataLoaded && adminSession.token && adminSession.organization?.id) {
      loadApiKeysData();
    }
  }, [
    adminSession.isAuthenticated,
    adminSession.isLoading,
    adminSession.token,
    adminSession.organization?.id, // Ensure orgId is stable or correctly part of deps
    dataLoaded,
    canViewApiKeys // Dependency on the hook's result
  ]);

  // Load API keys data
  const loadApiKeysData = async () => {
    if (!adminSession.token || !adminSession.organization?.id) {
      setError('Session information is missing.');
      setLoading(false);
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Get API keys using apiClient with JWT authentication
      const url = `/admin/api-keys/${adminSession.organization.id}/api-keys`;
      
      // Use the reusable apiClient which will handle tokens correctly
      const responseData = await apiClient<ApiKey[]>(url, {
        token: adminSession.token,
        orgId: adminSession.organization.id
      });
      
      setApiKeys(Array.isArray(responseData) ? responseData : []);
      setDataLoaded(true);
    } catch (apiError: unknown) {
      console.error('Error fetching API keys:', apiError);
      
      // Extract error message
      if (apiError instanceof Error) {
        setError(`Failed to load API keys: ${apiError.message}`);
      } else if (typeof apiError === 'string') {
        setError(`Failed to load API keys: ${apiError}`);
      } else {
        setError('Failed to load API keys. An unknown error occurred.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Create a new API key
  async function createApiKey(e: React.FormEvent) {
    e.preventDefault();
    if (!newKeyName.trim() || !adminSession.token || !adminSession.organization?.id) return;
    
    setCreateKeyLoading(true);
    try {
      // Use api.post which handles token and content type
      const url = `/admin/api-keys/${adminSession.organization.id}/api-keys`;
      const data = await api.post<ApiKeyCreateResponse>(url, {
        name: newKeyName.trim()
      }, {
        token: adminSession.token,
        orgId: adminSession.organization.id
      });
      
      if (data && data.key) {
        // Show the full key value to the user
        setNewApiKey(data.key);
        
        // Add the new key to the list (without the full key value)
        setApiKeys(prev => [data, ...prev]);
        
        // Clear the form
        setNewKeyName('');
      } else {
        setError('API key created but the key value was not returned');
      }
    } catch (err: any) {
      console.error('Error creating API key:', err);
      setError(err.message || 'Failed to create API key');
    } finally {
      setCreateKeyLoading(false);
    }
  }

  // Revoke an API key
  async function revokeApiKey(keyId: string) {
    if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.') || 
        !adminSession.token || !adminSession.organization?.id) {
      return;
    }
    
    try {
      // Use api.delete which handles token and method
      const url = `/admin/api-keys/${adminSession.organization.id}/api-keys/${keyId}`;
      await api.delete(url, {
        token: adminSession.token,
        orgId: adminSession.organization.id
      });
      
      // Update the UI to show the key as revoked
      setApiKeys(prev => prev.map(key => 
        key.id === keyId ? { ...key, revoked: true } : key
      ));
    } catch (err: any) {
      console.error('Error revoking API key:', err);
      setError(err.message || 'Failed to revoke API key');
    }
  }

  // Format date strings
  function formatDate(dateString: string | undefined) {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  }

  // Show different UI states based on auth and loading status
  if (adminSession.isLoading) {
    return (
      <div className="p-6">
        <div className="py-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading authentication data...</p>
        </div>
      </div>
    );
  }

  if (!adminSession.isAuthenticated) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
          <p>You need to be authenticated to access this page.</p>
          {adminSession.error && (
            <div className="mt-2 p-2 bg-red-100 rounded">
              <p><strong>Error:</strong> {adminSession.error.message}</p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Combined loading and initial auth/permission error display
  if (adminSession.isLoading || (loading && !error && canViewApiKeys)) {
     return (
      <div className="p-6">
        <div className="py-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading API keys...</p>
        </div>
      </div>
    );
  }
  
  if (error) { // Catches auth, permission, or data fetch errors set in useEffect
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <h2 className="text-xl font-semibold mb-2">Error</h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!canViewApiKeys) { // This should ideally be caught by the error state if useEffect runs correctly
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p>You do not have permission to view API keys. Required: {apiKeyPermissions.join(' OR ')}</p>
        </div>
      </div>
    );
  }

  // Main content when authenticated and authorized
  return (
    <div className="p-6">
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold">Organization API Keys</h1>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => loadApiKeysData()}
            disabled={loading}
          >
            <span className={`inline-block w-4 h-4 mr-2 ${loading ? 'animate-spin rounded-full border-2 border-t-transparent border-blue-500' : ''}`}>↻</span>
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
          
          <Dialog open={showDialog} onOpenChange={setShowDialog}>
            <DialogTrigger asChild>
              <Button>Create New API Key</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New API Key</DialogTitle>
                <DialogDescription>
                  {newApiKey ? (
                    <div className="my-4">
                      <div className="text-amber-600 font-medium mb-2">Copy your API key now. You won't be able to see it again!</div>
                      <div className="bg-gray-100 p-3 rounded-md break-all font-mono text-sm">
                        {newApiKey}
                      </div>
                      <Button 
                        className="mt-3 w-full"
                        onClick={() => {
                          navigator.clipboard.writeText(newApiKey);
                          alert('API key copied to clipboard');
                        }}
                      >
                        Copy to Clipboard
                      </Button>
                    </div>
                  ) : (
                    <form onSubmit={createApiKey} className="space-y-4 mt-4">
                      <div>
                        <Label htmlFor="keyName">Name for this API key</Label>
                        <Input 
                          id="keyName" 
                          value={newKeyName}
                          onChange={(e) => setNewKeyName(e.target.value)}
                          placeholder="e.g., Production API Key"
                          required
                          minLength={3}
                        />
                      </div>
                      <DialogFooter>
                        <Button type="button" variant="outline" onClick={() => setShowDialog(false)}>
                          Cancel
                        </Button>
                        <Button type="submit" disabled={createKeyLoading || !newKeyName.trim()}>
                          {createKeyLoading ? 'Creating...' : 'Create API Key'}
                        </Button>
                      </DialogFooter>
                    </form>
                  )}
                </DialogDescription>
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 text-red-700 p-4 rounded-md">
          <p>{error}</p>
        </div>
      )}
      
      {loading ? (
        <div className="py-8 text-center glass-morphism-subtle rounded-lg">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cw-accent-left mx-auto"></div>
          <p className="mt-4 text-cw-foreground/70 font-mono tracking-wider">LOADING API KEYS...</p>
        </div>
      ) : apiKeys.length === 0 ? (
        <div className="text-center py-12 glass-morphism-subtle rounded-lg">
          <h3 className="text-lg font-display font-medium text-cw-foreground tracking-wider">No API Keys Found</h3>
          <p className="text-cw-foreground/70 mt-2 font-mono">Create an API key to use the chat interface and other features.</p>
          <Button 
            className="mt-4" 
            onClick={() => setShowDialog(true)}
          >
            Create Your First API Key
          </Button>
        </div>
      ) : (
        <div className="glass-morphism rounded-lg overflow-hidden border border-cw-b-cyan/50">
          <table className="min-w-full divide-y divide-cw-b-cyan/30">
            <thead className="bg-cw-bg-top/95">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">
                  Prefix
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">
                  Created
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">
                  Last Used
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-cw-bg-top/70 divide-y divide-cw-b-cyan/30">
              {apiKeys.map((key) => (
                <tr key={key.id} className="hover:bg-cw-accent-left/10 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-mono font-medium text-cw-foreground">{key.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-mono text-cw-accent-left">{key.key_prefix}...</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-mono text-cw-foreground/80">{formatDate(key.created_at)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-mono text-cw-foreground/80">{formatDate(key.last_used_at)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {key.revoked ? (
                      <span className="px-2 inline-flex text-xs leading-5 font-mono font-semibold rounded-full bg-cw-n-red/20 text-cw-n-red border border-cw-n-red/40">
                        REVOKED
                      </span>
                    ) : (
                      <span className="px-2 inline-flex text-xs leading-5 font-mono font-semibold rounded-full bg-cw-n-green/20 text-cw-n-green border border-cw-n-green/40">
                        ACTIVE
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {!key.revoked && (
                      <button
                        onClick={() => revokeApiKey(key.id)}
                        className="text-cw-n-red hover:text-cw-b-red font-mono tracking-wider transition-colors"
                      >
                        REVOKE
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="mt-6 glass-morphism-subtle p-6 rounded-lg border border-cw-accent-left/30">
        <h3 className="text-lg font-display font-medium mb-3 text-cw-foreground tracking-wider">API KEY SECURITY</h3>
        <p className="text-sm font-mono text-cw-foreground/80 leading-relaxed">
          API keys authenticate your organization's requests to the Coherence AI system. 
          Chat interface and autonomous workflows require valid organization API keys.
        </p>
        <ul className="list-disc ml-5 mt-3 text-sm font-mono text-cw-foreground/70 space-y-1">
          <li>Store API keys securely; shown only once during creation</li>
          <li>Revoked keys cannot be restored - new key generation required</li>
          <li>Each key inherits organizational permissions and access levels</li>
        </ul>
      </div>
    </div>
  );
}