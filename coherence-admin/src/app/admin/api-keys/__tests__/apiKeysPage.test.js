// API Keys Page Tests
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ApiKeysPage from '../page';
import { useAdminSession } from '@/context/AdminSessionContext';
import apiClient from '@/lib/apiClient';

// Mock the AdminSessionContext hook
jest.mock('@/context/AdminSessionContext', () => ({
  useAdminSession: jest.fn(),
}));

// Mock the apiClient
jest.mock('@/lib/apiClient', () => ({
  __esModule: true,
  default: jest.fn(),
  api: {
    get: jest.fn(),
    post: jest.fn(),
    delete: jest.fn(),
  },
}));

// Mock the Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

describe('API Keys Page', () => {
  const mockAdminSession = {
    isLoading: false,
    isAuthenticated: true,
    token: 'mock-jwt-token',
    permissions: ['system:*'],
    organization: {
      id: 'org_123456',
      name: 'Test Organization',
      role: 'admin',
    },
    tenant: {
      id: 'tenant_123456',
      name: 'Test Tenant',
    },
    userId: 'user_123456',
    error: null,
    actor: {
      publicMetadata: {
        isSystemAdmin: true,
      },
    },
  };

  const mockApiKeys = [
    {
      id: 'key_123',
      name: 'Test API Key 1',
      key_prefix: 'coh_org_abc',
      created_at: new Date().toISOString(),
      revoked: false,
      last_used_at: new Date().toISOString(),
    },
    {
      id: 'key_456',
      name: 'Test API Key 2',
      key_prefix: 'coh_org_def',
      created_at: new Date().toISOString(),
      revoked: true,
      last_used_at: null,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    useAdminSession.mockReturnValue(mockAdminSession);
    apiClient.mockResolvedValue(mockApiKeys);
  });

  test('renders loading state while admin session is loading', () => {
    useAdminSession.mockReturnValue({ ...mockAdminSession, isLoading: true });
    
    render(<ApiKeysPage />);
    
    expect(screen.getByText('Loading authentication data...')).toBeInTheDocument();
  });

  test('renders authentication required message when user is not authenticated', () => {
    useAdminSession.mockReturnValue({ 
      ...mockAdminSession, 
      isLoading: false, 
      isAuthenticated: false 
    });
    
    render(<ApiKeysPage />);
    
    expect(screen.getByText('Authentication Required')).toBeInTheDocument();
  });

  test('renders API keys table when data is loaded', async () => {
    render(<ApiKeysPage />);
    
    // Should start by making an API call
    await waitFor(() => {
      expect(apiClient).toHaveBeenCalledWith(
        '/admin/api-keys/org_123456/api-keys',
        expect.objectContaining({
          token: 'mock-jwt-token',
          orgId: 'org_123456',
        })
      );
    });
    
    // Should show API keys in table
    await waitFor(() => {
      expect(screen.getByText('Test API Key 1')).toBeInTheDocument();
      expect(screen.getByText('Test API Key 2')).toBeInTheDocument();
      
      // Check status indicators
      const activeStatus = screen.getByText('Active');
      const revokedStatus = screen.getByText('Revoked');
      expect(activeStatus).toBeInTheDocument();
      expect(revokedStatus).toBeInTheDocument();
    });
  });

  test('handles API error gracefully', async () => {
    apiClient.mockRejectedValueOnce(new Error('Failed to load API keys'));
    
    render(<ApiKeysPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to load API keys')).toBeInTheDocument();
    });
  });
  
  test('shows access denied message when user lacks permissions', async () => {
    useAdminSession.mockReturnValue({
      ...mockAdminSession,
      permissions: ['some:other:permission'],
      actor: {
        publicMetadata: {
          isSystemAdmin: false
        }
      }
    });
    
    render(<ApiKeysPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/Access Denied/)).toBeInTheDocument();
      expect(screen.getByText(/You do not have permission to view API keys/)).toBeInTheDocument();
    });
  });

  test('creates a new API key successfully', async () => {
    const newKeyResponse = {
      id: 'key_new',
      name: 'New Test Key',
      key_prefix: 'coh_org_new',
      created_at: new Date().toISOString(),
      revoked: false,
      key: 'coh_org_full_key_value_123456789',
    };
    
    apiClient.api.post.mockResolvedValueOnce(newKeyResponse);
    
    render(<ApiKeysPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Create New API Key')).toBeInTheDocument();
    });
    
    // Open the dialog
    const user = userEvent.setup();
    await act(async () => {
      await user.click(screen.getByText('Create New API Key'));
    });
    
    // Fill in the form
    await act(async () => {
      await user.type(screen.getByLabelText('Name for this API key'), 'New Test Key');
      await user.click(screen.getByRole('button', { name: 'Create API Key' }));
    });
    
    // Verify API call
    await waitFor(() => {
      expect(apiClient.api.post).toHaveBeenCalledWith(
        '/admin/api-keys/org_123456/api-keys',
        { name: 'New Test Key' },
        expect.objectContaining({
          token: 'mock-jwt-token',
          orgId: 'org_123456',
        })
      );
    });
    
    // Verify UI shows the created key
    await waitFor(() => {
      expect(screen.getByText('coh_org_full_key_value_123456789')).toBeInTheDocument();
    });
  });

  test('revokes an API key successfully', async () => {
    // Mock confirm to return true
    window.confirm = jest.fn().mockReturnValue(true);
    
    render(<ApiKeysPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Test API Key 1')).toBeInTheDocument();
    });
    
    // Click the revoke button
    const user = userEvent.setup();
    await act(async () => {
      await user.click(screen.getByText('Revoke'));
    });
    
    // Verify API call
    await waitFor(() => {
      expect(apiClient.api.delete).toHaveBeenCalledWith(
        '/admin/api-keys/org_123456/api-keys/key_123',
        expect.objectContaining({
          token: 'mock-jwt-token',
          orgId: 'org_123456',
        })
      );
    });
  });
});