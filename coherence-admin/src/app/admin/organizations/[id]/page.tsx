'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useHasPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path
import apiClient from '@/lib/apiClient'; // Renamed import to apiClient directly

// Updated interface to better match TenantRead schema
interface OrganizationData {
  id: string; // Internal DB UUID 
  name: string;
  clerk_org_id: string | null;
  industry_pack?: string | null;
  compliance_tier?: string | null;
  settings?: Record<string, string> | null;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

const OrganizationDashboardPage = () => {
  const params = useParams();
  // organizationIdFromUrl is the Clerk Org ID from the URL path
  const organizationIdFromUrl = params.id as string;
  const adminSession = useAdminSession();

  const [organizationData, setOrganizationData] = React.useState<OrganizationData | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  // const [hasPermission, setHasPermission] = React.useState<boolean | null>(null); // Replaced by hook

  const canViewDashboard = useHasPermission('organization:view_own_dashboard' as Permission);

  React.useEffect(() => {
    if (adminSession.isLoading) {
      setLoading(true);
      return;
    }

    if (!adminSession.isAuthenticated || !adminSession.token) { // Removed adminSession.permissions check
      setError('User session not available or token missing. Please log in.');
      setLoading(false);
      // setHasPermission(false); // Not needed
      return;
    }

    // const canViewDashboardEffect = Array.isArray(adminSession.permissions) &&
    //                          adminSession.permissions.includes('organization:view_own_dashboard');
    // setHasPermission(canViewDashboardEffect); // Not needed, using hook directly

    if (!canViewDashboard) {
      setLoading(false);
      setError("You do not have permission to view this organization's dashboard.");
      return;
    }

    // At this point, user is authenticated and has permission
    if (organizationIdFromUrl) { // Token check is implicitly part of isAuthenticated
      setLoading(true);
      apiClient<OrganizationData>(`/admin/organizations/${organizationIdFromUrl}`, {
        token: adminSession.token! // Token is checked via isAuthenticated
      })
      .then((responseData: OrganizationData) => {
        setOrganizationData(responseData);
        setError(null);
      })
      .catch((apiError: unknown) => {
        console.error('API Error fetching organization data:', apiError);
        if (apiError instanceof Error) {
          setError(`Failed to load organization data: ${apiError.message}`);
        } else if (typeof apiError === 'string') {
          setError(`Failed to load organization data: ${apiError}`);
        } else {
          setError('Failed to load organization data. An unknown error occurred.');
        }
      })
      .finally(() => {
        setLoading(false);
      });
    } else {
      setError('Organization ID from URL is missing.');
      setLoading(false);
    }
  }, [organizationIdFromUrl, adminSession.isAuthenticated, adminSession.isLoading, adminSession.token, canViewDashboard]);

  if (adminSession.isLoading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[calc(100vh-theme(spacing.24))]">
        <p className="text-lg text-gray-600">Loading session...</p>
      </div>
    );
  }

  if (adminSession.error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Session Error: </strong>
          <span className="block sm:inline">{adminSession.error.message}</span>
        </div>
      </div>
    );
  }
  
  if (!adminSession.isAuthenticated) {
     return (
      <div className="p-6">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Authentication Required: </strong>
          <span className="block sm:inline">Please log in to access this page.</span>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[calc(100vh-theme(spacing.24))]">
        <p className="text-lg text-gray-600">Loading organization details...</p>
      </div>
    );
  }

  // Use canViewDashboard from hook directly
  if (!adminSession.isLoading && !canViewDashboard && adminSession.isAuthenticated) {
    return (
        <div className="p-6">
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
                <strong className="font-bold">Access Denied: </strong>
                <span className="block sm:inline">You do not have permission to view this organization dashboard. (organization:view_own_dashboard)</span>
            </div>
        </div>
    );
  }

  if (error) { // This will also catch permission errors set in useEffect
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Page Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  if (!organizationData) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[calc(100vh-theme(spacing.24))]">
         <p className="text-lg text-gray-600">No organization data available or data fetch is pending. If this persists, check console for errors.</p>
      </div>
    );
  }

  // Display Organization Data
  return (
    <div className="p-6">
      <header className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Organization Dashboard</h1>
      </header>
      
      <div className="bg-white shadow-lg rounded-xl p-6 md:p-8">
        <div className="mb-6 pb-4 border-b border-gray-200">
            <h2 className="text-2xl font-semibold text-gray-700">{organizationData.name}</h2>
            <p className="text-sm text-gray-500">Clerk Org ID: {organizationData.clerk_org_id || 'N/A'}</p>
            <p className="text-xs text-gray-400 mt-1">Internal DB ID: {organizationData.id}</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
            <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Industry Pack</p>
                <p className="text-lg text-gray-800">{organizationData.industry_pack || 'Not set'}</p>
            </div>
            <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Compliance Tier</p>
                <p className="text-lg text-gray-800">{organizationData.compliance_tier || 'Not set'}</p>
            </div>
            <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Created At</p>
                <p className="text-lg text-gray-800">{new Date(organizationData.created_at).toLocaleString()}</p>
            </div>
            <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Last Updated At</p>
                <p className="text-lg text-gray-800">{new Date(organizationData.updated_at).toLocaleString()}</p>
            </div>
        </div>
        
        {organizationData.settings && Object.keys(organizationData.settings).length > 0 && (
            <div className="mt-6 pt-4 border-t border-gray-200">
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Additional Settings</h3>
                <ul className="list-disc pl-5 text-gray-600">
                    {Object.entries(organizationData.settings).map(([key, value]) => (
                        <li key={key}><span className="font-medium">{key}:</span> {String(value)}</li>
                    ))}
                </ul>
            </div>
        )}

        <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-xs text-gray-400 text-center">
                This dashboard provides a read-only view of your organization&apos;s information.
            </p>
        </div>
      </div>
    </div>
  );
};

export default OrganizationDashboardPage; 