'use client';

import React, { useState, useEffect } from 'react';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useHasPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path
import apiClient from '@/lib/apiClient';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface Organization {
  id: string;
  name: string;
  clerk_org_id: string | null;
  industry_pack?: string | null;
  compliance_tier?: string | null;
  created_at: string;
  updated_at: string;
}

const OrganizationsPage = () => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const adminSession = useAdminSession();

  const canViewOrganizations = useHasPermission('organization:read' as Permission);
  const isSystemAdmin = useHasPermission('system_admin:access' as Permission);

  useEffect(() => {
    if (adminSession.isLoading) {
      setIsLoading(true); // Ensure loading is true while session is loading
      return;
    }

    if (!adminSession.isAuthenticated || !adminSession.token) {
      setIsLoading(false);
      setError("User not authenticated or token missing.");
      return;
    }

    // const canViewOrganizationsEffect = Array.isArray(adminSession.permissions) &&
    //                          adminSession.permissions.includes('organization:read');
    // Replaced by hook: canViewOrganizations

    if (!canViewOrganizations) {
      setIsLoading(false);
      setError("You do not have permission to view organizations.");
      return;
    }

    // At this point, user is authenticated and has permission
    setIsLoading(true); // Set loading before fetch
    const fetchOrganizations = async () => {
      try {
        if (isSystemAdmin) {
          // System admins can see all organizations
          const data = await apiClient<Organization[]>('/admin/tenants', {
            token: adminSession.token,
          });
          setOrganizations(data);
        } else if (adminSession.organization && adminSession.tenant) {
          // Regular users only see their own organization
          // Create an organization object from the session data
          const currentOrg: Organization = {
            id: adminSession.tenant.id,
            name: adminSession.organization.name || adminSession.tenant.name,
            clerk_org_id: adminSession.organization.id,
            industry_pack: adminSession.tenant.industry_pack || null,
            compliance_tier: adminSession.tenant.compliance_tier || null,
            created_at: adminSession.tenant.created_at || new Date().toISOString(),
            updated_at: adminSession.tenant.updated_at || new Date().toISOString(),
          };
          setOrganizations([currentOrg]);
        } else {
          setError("No organization found for the current user.");
        }
        setError(null);
      } catch (err) {
        console.error('API Error fetching organizations:', err);
        if (err instanceof Error) {
          setError(`Failed to load organizations: ${err.message}`);
        } else {
          setError('Failed to load organizations. An unknown error occurred.');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizations();
  }, [adminSession.isLoading, adminSession.isAuthenticated, adminSession.token, canViewOrganizations, isSystemAdmin, adminSession.organization, adminSession.tenant]); // Added deps

  // Loading and error states
  if (adminSession.isLoading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[calc(100vh-theme(spacing.24))]">
        <p className="text-lg text-gray-600">Loading session...</p>
      </div>
    );
  }

  if (adminSession.error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Session Error: </strong>
          <span className="block sm:inline">{adminSession.error.message}</span>
        </div>
      </div>
    );
  }

  if (!adminSession.isAuthenticated) {
    return (
      <div className="p-6">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Authentication Required: </strong>
          <span className="block sm:inline">Please log in to access this page.</span>
        </div>
      </div>
    );
  }

  // const canViewOrganizations = Array.isArray(adminSession.permissions) &&
  //                           adminSession.permissions.includes('organization:read');
  // Replaced by hook call at the top

  if (!isLoading && !canViewOrganizations) { // Check isLoading to prevent premature denial
    return (
      <div className="p-6">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Access Denied: </strong>
          <span className="block sm:inline">You do not have permission to view organizations. (organization:read)</span>
        </div>
      </div>
    );
  }
  
  // This handles data loading state after permission is confirmed (or if error is already set)
  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[calc(100vh-theme(spacing.24))]">
        <p className="text-lg text-gray-600">Loading organizations...</p>
      </div>
    );
  }

  if (error) { // This will now also catch permission errors set in useEffect
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <header className="mb-6">
        <h1 className="text-3xl font-display font-bold text-cw-foreground tracking-wider">ORGANIZATIONS</h1>
        <p className="text-cw-foreground/70 mt-2 font-mono">Manage organization settings and access control</p>
      </header>

      <div className="glass-morphism rounded-xl p-6 md:p-8 border border-cw-b-cyan/50">
        {organizations.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-cw-b-cyan/30">
              <thead className="bg-cw-bg-top/95">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">Industry Pack</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">Compliance Tier</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">Created</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-display font-medium text-cw-foreground/90 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-cw-bg-top/70 divide-y divide-cw-b-cyan/30">
                {organizations.map((org) => (
                  <tr 
                    key={org.id} 
                    className="hover:bg-cw-accent-left/10 transition-colors cursor-pointer"
                    onClick={() => window.location.href = `/admin/organizations/${org.clerk_org_id || org.id}`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono font-medium text-cw-foreground">{org.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-cw-foreground/80">{org.industry_pack || 'N/A'}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-cw-foreground/80">{org.compliance_tier || 'N/A'}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-cw-foreground/80">{new Date(org.created_at).toLocaleDateString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button variant="outline" size="sm">View Details</Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No organizations found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrganizationsPage; 