'use client';

import React from 'react';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useUser } from '@clerk/nextjs';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { debugClerkToken } from '@/utils/debug-token';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { AIStatus } from '@/components/ui/ai-status';
import { AIProgress } from '@/components/ui/ai-progress';

// Enhanced icon components with better design
const UserIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4"><path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" /></svg>;
const BuildingOfficeIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4"><path strokeLinecap="round" strokeLinejoin="round" d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h6.75M9 12h6.75m-6.75 5.25h6.75M5.25 21v-2.25a2.25 2.25 0 012.25-2.25h1.5a2.25 2.25 0 012.25 2.25V21m-4.5 0H21" /></svg>;
const ShieldCheckIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4"><path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" /></svg>;
const TemplateIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4"><path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" /></svg>;
const WorkflowIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4"><path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" /></svg>;
const SettingsIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4"><path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" /><path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>;
const ChartIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4"><path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" /></svg>;
const ActivityIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4"><path strokeLinecap="round" strokeLinejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" /></svg>;
const RocketIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4"><path strokeLinecap="round" strokeLinejoin="round" d="M15.59 14.37a6 6 0 01-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 006.16-12.12A14.98 14.98 0 009.631 8.41m5.96 5.96a14.926 14.926 0 01-5.841 2.58m-.119-8.54a6 6 0 00-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 00-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 01-2.448-2.448 14.9 14.9 0 01.06-.312m-2.24 2.39a4.493 4.493 0 00-1.757 4.306 4.493 4.493 0 004.306-1.757M16.5 9a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" /></svg>;

export default function AdminDashboardPage() {
  const { permissions, isLoading, isAuthenticated, error, organization, tenant } = useAdminSession();
  const { user } = useUser();
  const router = useRouter();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-48">
        <p className="text-muted-foreground">Loading dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Session Error: </strong>
          <span className="block sm:inline">{error.message}</span>
        </div>
        <div className="mt-4">
          <Button onClick={() => router.refresh()} variant="outline">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto max-w-7xl p-6">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Authentication Required: </strong>
          <span className="block sm:inline">Please log in to access this page.</span>
        </div>
        <div className="mt-4">
          <Button onClick={() => router.push('/sign-in')}>Sign In</Button>
        </div>
      </div>
    );
  }

  // Enhanced AI-First Card component
  const EnhancedCard = ({ title, icon, children, className = "", headerAction = null, variant = "glass" as any }: { 
    title: string; 
    icon?: React.ReactNode; 
    children: React.ReactNode; 
    className?: string;
    headerAction?: React.ReactNode;
    variant?: "glass" | "neural" | "ai-processing" | "ai-active" | "data-flow" | "default";
  }) => (
    <Card variant={variant} className={className}>
      <div className="px-6 py-4 border-b border-cw-b-cyan/30 flex items-center justify-between">
        <div className="flex items-center">
          {icon && <span className="text-cw-foreground/80 mr-3">{icon}</span>}
          <h2 className="text-lg font-display font-semibold text-cw-foreground tracking-wider">{title}</h2>
        </div>
        {headerAction}
      </div>
      <div className="p-6">{children}</div>
    </Card>
  );

  // Enhanced AI-First Quick Action component
  const QuickAction = ({ href, title, description, icon, status = "idle" }: { 
    href: string; 
    title: string; 
    description: string; 
    icon: React.ReactNode;
    status?: "idle" | "active" | "processing";
  }) => {
    const getVariant = () => {
      switch (status) {
        case 'processing': return 'ai-processing';
        case 'active': return 'ai-active';
        default: return 'glass';
      }
    };

    return (
      <Link href={href} className="group">
        <Card variant={getVariant()} className="p-6 hover:glass-morphism-strong transition-all duration-300 h-full accent-glow">
          <div className="flex items-start justify-between mb-4">
            <div className="p-3 rounded-lg glass-morphism-subtle text-cw-accent-left">{icon}</div>
            <div className="flex items-center gap-2">
              <AIStatus status={status} size="sm" />
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-cw-foreground/60 group-hover:text-cw-accent-left group-hover:translate-x-1 transition-all duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </div>
          </div>
          <h3 className="font-display font-semibold text-lg mb-2 text-cw-foreground tracking-wider">{title}</h3>
          <p className="text-cw-foreground/70 text-sm font-mono">{description}</p>
        </Card>
      </Link>
    );
  };

  // Enhanced AI-First Stat Card component
  const StatCard = ({ title, value, icon, trend = null, progress = null }: { 
    title: string, 
    value: string, 
    icon: React.ReactNode, 
    trend?: { value: string, up: boolean } | null,
    progress?: { value: number, label?: string } | null
  }) => {
    return (
      <Card variant="neural" className="p-6 accent-glow">
        <div className="flex items-center justify-between mb-4">
          <span className="text-cw-foreground/70 text-sm font-mono font-medium tracking-wider">{title}</span>
          <span className="p-3 rounded-lg glass-morphism-subtle text-cw-accent-left">{icon}</span>
        </div>
        <div className="flex items-end justify-between mb-4">
          <div>
            <p className="text-3xl font-display font-bold text-cw-foreground mb-2 tracking-wider">{value}</p>
            {trend && (
              <p className={`flex items-center text-sm font-mono ${trend.up ? 'text-cw-n-green' : 'text-cw-n-red'}`}>
                <span className="mr-1">
                  {trend.up ? (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4">
                      <path fillRule="evenodd" d="M12.577 4.878a.75.75 0 01.919-.53l4.78 1.281a.75.75 0 01.531.919l-1.281 4.78a.75.75 0 01-1.449-.387l.81-3.022a19.407 19.407 0 00-5.594 *********** 0 01-1.139.093L7 10.06l-4.72 4.72a.75.75 0 01-1.06-1.061l5.25-5.25a.75.75 0 011.06 0l3.074 3.073a20.923 20.923 0 015.545-4.931l-3.042-.815a.75.75 0 01-.53-.919z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4">
                      <path fillRule="evenodd" d="M1.22 5.222a.75.75 0 011.06 0L7 9.942l3.768-3.769a.75.75 0 011.113.058 20.908 20.908 0 013.813 7.254l1.574-2.727a.75.75 0 011.3.75l-2.475 4.286a.75.75 0 01-1.025.275l-4.287-2.475a.75.75 0 01.75-1.3l2.71 1.565a19.422 19.422 0 00-3.013-6.024L7.53 11.533a.75.75 0 01-1.06 0l-5.25-5.25a.75.75 0 010-1.06z" clipRule="evenodd" />
                    </svg>
                  )}
                </span>
                {trend.value}
              </p>
            )}
          </div>
        </div>
        {progress && (
          <AIProgress 
            variant="neural" 
            value={progress.value} 
            label={progress.label}
            size="sm"
          />
        )}
      </Card>
    );
  };

  return (
    <div>
      {/* Enhanced AI-First Header section */}
      <div className="mb-8 relative">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-4xl font-display font-bold text-cw-foreground tracking-wider bg-gradient-to-r from-cw-foreground via-cw-accent-left to-cw-accent-right bg-clip-text">
              COHERENCE ADMIN
            </h1>
            <div className="flex items-center gap-4 mt-2">
              <p className="text-cw-foreground/70 font-mono text-sm">
                Welcome back, <span className="text-cw-accent-left font-semibold">{user?.firstName || 'ADMIN'}</span>
              </p>
              <AIStatus status="active" size="sm">SYSTEM ONLINE</AIStatus>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="glass-morphism-strong rounded-lg p-4 min-w-[200px]">
              <div className="text-xs font-mono text-cw-foreground/60 mb-1">AI PIPELINE STATUS</div>
              <AIProgress 
                variant="neural" 
                value={87} 
                showValue={true}
                size="sm"
              />
            </div>
          </div>
        </div>
        <p className="text-cw-foreground/60 font-mono text-sm max-w-2xl">
          Command center for AI workflows, template management, and real-time system monitoring. 
          All systems operational.
        </p>
        
        {/* Real-time indicators */}
        <div className="absolute top-0 right-0 opacity-30">
          <div className="flex gap-2">
            <div className="w-2 h-2 bg-cw-accent-left rounded-full animate-pulse-ai"></div>
            <div className="w-2 h-2 bg-cw-accent-right rounded-full animate-pulse-ai" style={{ animationDelay: '500ms' }}></div>
            <div className="w-2 h-2 bg-cw-n-cyan rounded-full animate-pulse-ai" style={{ animationDelay: '1000ms' }}></div>
          </div>
        </div>
      </div>

      <div className="space-y-8">
        {/* Enhanced Session and user info cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <EnhancedCard
            title="SESSION DETAILS"
            icon={<UserIcon />}
            variant="ai-active"
            headerAction={<AIStatus status="active" size="sm" />}
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-mono text-cw-foreground/70 tracking-wider">USER:</span>
                <span className="text-sm font-mono text-cw-accent-left">{user?.firstName} {user?.lastName || ''}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-mono text-cw-foreground/70 tracking-wider">EMAIL:</span>
                <span className="text-sm font-mono text-cw-foreground truncate max-w-[180px]">
                  {user?.emailAddresses[0]?.emailAddress || 'N/A'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-mono text-cw-foreground/70 tracking-wider">STATUS:</span>
                <AIStatus status="active" size="sm">AUTHENTICATED</AIStatus>
              </div>
              <AIProgress 
                variant="gradient" 
                value={100} 
                label="Session Health"
                size="sm"
              />
            </div>
          </EnhancedCard>

          <EnhancedCard
            title="ORGANIZATION"
            icon={<BuildingOfficeIcon />}
            variant="data-flow"
            headerAction={
              organization?.role && (
                <AIStatus status="idle" size="sm">{organization.role.toUpperCase()}</AIStatus>
              )
            }
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-mono text-cw-foreground/70 tracking-wider">NAME:</span>
                <span className="text-sm font-mono text-cw-accent-right">{organization?.name || 'N/A'}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-mono text-cw-foreground/70 tracking-wider">ID:</span>
                <span className="text-xs font-mono text-cw-foreground bg-cw-bg-top/50 px-2 py-1 rounded border border-cw-b-cyan/30">
                  {organization?.id ? `...${organization.id.slice(-8)}` : 'N/A'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-mono text-cw-foreground/70 tracking-wider">TYPE:</span>
                <span className="text-sm font-mono text-cw-n-cyan">ENTERPRISE</span>
              </div>
            </div>
          </EnhancedCard>

          <EnhancedCard
            title="PERMISSIONS"
            icon={<ShieldCheckIcon />}
            variant="neural"
            headerAction={
              permissions && (
                <AIStatus status="active" size="sm">{permissions.length} PERMS</AIStatus>
              )
            }
          >
            {permissions ? (
              <div className="space-y-3">
                <div className="max-h-24 overflow-y-auto custom-scrollbar">
                  <div className="space-y-1">
                    {permissions.slice(0, 4).map((permission) => (
                      <div key={permission} className="text-xs font-mono text-cw-foreground/80 truncate bg-cw-bg-top/30 px-2 py-1 rounded border border-cw-b-cyan/20">
                        {permission}
                      </div>
                    ))}
                    {permissions.length > 4 && (
                      <div className="text-xs font-mono text-cw-accent-left italic">
                        +{permissions.length - 4} more permissions
                      </div>
                    )}
                  </div>
                </div>
                <AIProgress 
                  variant="glow" 
                  value={Math.min((permissions.length / 10) * 100, 100)} 
                  label="Permission Coverage"
                  size="sm"
                />
              </div>
            ) : (
              <div className="text-sm font-mono text-cw-foreground/50 italic">
                No permissions available
              </div>
            )}
          </EnhancedCard>
        </div>

        {/* Enhanced AI-First Quick Actions */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-display font-bold text-cw-foreground tracking-wider">QUICK ACTIONS</h2>
            <div className="flex items-center gap-2">
              <AIStatus status="active" size="sm">4 MODULES</AIStatus>
              <div className="w-px h-6 bg-gradient-to-b from-cw-accent-left to-cw-accent-right"></div>
              <span className="text-xs font-mono text-cw-foreground/60">ALL SYSTEMS OPERATIONAL</span>
            </div>
          </div>
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <QuickAction
              href="/admin/templates"
              title="TEMPLATES"
              description="AI-powered template generation and management system"
              icon={<TemplateIcon />}
              status="active"
            />
            <QuickAction
              href="/admin/workflows"
              title="WORKFLOWS"
              description="Autonomous workflow orchestration and monitoring"
              icon={<WorkflowIcon />}
              status="processing"
            />
            <QuickAction
              href="/admin/integrations"
              title="INTEGRATIONS"
              description="External API connections and service mesh"
              icon={<RocketIcon />}
              status="active"
            />
            <QuickAction
              href="/admin/api-keys"
              title="API KEYS"
              description="Security token management and authentication"
              icon={<SettingsIcon />}
              status="idle"
            />
          </div>
        </div>

        {/* Enhanced AI-First System overview */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-display font-bold text-cw-foreground tracking-wider">SYSTEM METRICS</h2>
            <div className="flex items-center gap-4">
              <AIStatus status="processing" size="sm">ANALYZING</AIStatus>
              <select className="text-sm border border-cw-b-cyan/30 rounded-md px-3 py-2 glass-morphism-subtle font-mono bg-transparent text-cw-foreground focus:border-cw-accent-left outline-none">
                <option className="bg-cw-bg-top text-cw-foreground">LAST 7 DAYS</option>
                <option className="bg-cw-bg-top text-cw-foreground">LAST 30 DAYS</option>
                <option className="bg-cw-bg-top text-cw-foreground">LAST 90 DAYS</option>
              </select>
            </div>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <StatCard
              title="AI TEMPLATES"
              value="24"
              icon={<TemplateIcon />}
              trend={{ value: "12% this week", up: true }}
              progress={{ value: 78, label: "Optimization" }}
            />
            <StatCard
              title="ACTIVE WORKFLOWS"
              value="9"
              icon={<WorkflowIcon />}
              progress={{ value: 92, label: "Performance" }}
            />
            <StatCard
              title="API REQUESTS"
              value="1.4k"
              icon={<ChartIcon />}
              trend={{ value: "5% today", up: true }}
              progress={{ value: 65, label: "Load" }}
            />
            <StatCard
              title="AVG RESPONSE"
              value="280ms"
              icon={<ActivityIcon />}
              trend={{ value: "10ms faster", up: true }}
              progress={{ value: 85, label: "Efficiency" }}
            />
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <EnhancedCard title="RECENT ACTIVITY" icon={<ActivityIcon />} variant="data-flow">
              <div className="space-y-4">
                <div className="flex items-center justify-between py-3 border-b border-cw-b-cyan/30">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-cw-n-green rounded-full animate-pulse-ai"></div>
                    <span className="text-sm font-mono text-cw-foreground">Template "Welcome Message" updated</span>
                  </div>
                  <span className="text-xs font-mono text-cw-foreground/60 bg-cw-bg-top/50 px-2 py-1 rounded">2 MIN AGO</span>
                </div>
                <div className="flex items-center justify-between py-3 border-b border-cw-b-cyan/30">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-cw-accent-left rounded-full animate-pulse-ai"></div>
                    <span className="text-sm font-mono text-cw-foreground">Workflow "User Onboarding" executed</span>
                  </div>
                  <span className="text-xs font-mono text-cw-foreground/60 bg-cw-bg-top/50 px-2 py-1 rounded">5 MIN AGO</span>
                </div>
                <div className="flex items-center justify-between py-3">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-cw-accent-right rounded-full animate-pulse-ai"></div>
                    <span className="text-sm font-mono text-cw-foreground">Integration "Slack" connected</span>
                  </div>
                  <span className="text-xs font-mono text-cw-foreground/60 bg-cw-bg-top/50 px-2 py-1 rounded">12 MIN AGO</span>
                </div>
                <AIProgress 
                  variant="gradient" 
                  value={73} 
                  label="Activity Index"
                  size="sm"
                />
              </div>
            </EnhancedCard>

            <EnhancedCard title="AI PERFORMANCE" icon={<ChartIcon />} variant="ai-processing">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="glass-morphism-subtle rounded-lg p-4">
                    <div className="text-xs font-mono text-cw-foreground/60 mb-1">INFERENCE TIME</div>
                    <div className="text-lg font-display font-bold text-cw-accent-left">127ms</div>
                    <AIProgress variant="neural" value={82} size="sm" />
                  </div>
                  <div className="glass-morphism-subtle rounded-lg p-4">
                    <div className="text-xs font-mono text-cw-foreground/60 mb-1">TOKEN RATE</div>
                    <div className="text-lg font-display font-bold text-cw-accent-right">2.4k/s</div>
                    <AIProgress variant="glow" value={91} size="sm" />
                  </div>
                </div>
                
                <div className="glass-morphism-subtle rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-xs font-mono text-cw-foreground/60">NEURAL PIPELINE STATUS</span>
                    <AIStatus status="processing" size="sm">OPTIMIZING</AIStatus>
                  </div>
                  <AIProgress 
                    variant="neural" 
                    indeterminate={true}
                    label="Deep Learning Optimization"
                    size="default"
                  />
                </div>
                
                <div className="flex justify-center">
                  <div className="text-xs font-mono text-cw-n-cyan">
                    Next optimization cycle: 47s
                  </div>
                </div>
              </div>
            </EnhancedCard>
          </div>
        </div>
      </div>
    </div>
  );
}
