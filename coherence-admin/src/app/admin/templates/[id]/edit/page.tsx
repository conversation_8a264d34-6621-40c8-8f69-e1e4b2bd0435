'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useHasPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path
import apiClient from '@/lib/apiClient';
import TemplateEditor from '@/components/TemplateEditor';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save, RefreshCw, AlertTriangle } from 'lucide-react';

interface TemplateDetails {
  id: string;
  key: string;
  description?: string | null;
  category: string;
  body: string;
  actions?: Record<string, any> | null;
  parameters?: Record<string, any> | null;
  test_data?: Record<string, any> | null;
  language: string;
  version: number;
  updated_at: string;
}

const TemplateEditPage = () => {
  const router = useRouter();
  const params = useParams();
  const templateId = params.id as string;
  const adminSession = useAdminSession();
  
  const [template, setTemplate] = useState<TemplateDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'body' | 'actions' | 'parameters' | 'test_data'>('body');
  
  // Form state
  const [templateBody, setTemplateBody] = useState('');
  const [templateActions, setTemplateActions] = useState('{}');
  const [templateParameters, setTemplateParameters] = useState('{}');
  const [templateTestData, setTemplateTestData] = useState('{}');
  const [description, setDescription] = useState('');
  const [validationErrors, setValidationErrors] = useState<{
    body?: string;
    actions?: string;
    parameters?: string;
    test_data?: string;
  }>({});

  const canUpdateTemplates = useHasPermission('template:update' as Permission);

  useEffect(() => {
    if (!adminSession.isAuthenticated || adminSession.isLoading) return;
    if (!canUpdateTemplates) {
      setError('You do not have permission to edit templates');
      return;
    }

    const fetchTemplate = async () => {
      try {
        setIsLoading(true);
        const data = await apiClient<TemplateDetails>(`/admin/templates/${templateId}`, {
          token: adminSession.token,
          headers: {
            'X-Tenant-ID': adminSession.tenant?.id || ''
          }
        });
        setTemplate(data);
        setTemplateBody(data.body);
        setTemplateActions(data.actions ? JSON.stringify(data.actions, null, 2) : '{}');
        setTemplateParameters(data.parameters ? JSON.stringify(data.parameters, null, 2) : '{}');
        setTemplateTestData(data.test_data ? JSON.stringify(data.test_data, null, 2) : '{}');
        setDescription(data.description || '');
        setError(null);
      } catch (err) {
        console.error('Error fetching template:', err);
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('Failed to load template details');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchTemplate();
  }, [templateId, adminSession, canUpdateTemplates]);

  const validateForm = () => {
    const errors: {
      body?: string;
      actions?: string;
      parameters?: string;
      test_data?: string;
    } = {};
    let isValid = true;

    // Validate body
    if (!templateBody.trim()) {
      errors.body = 'Template body cannot be empty';
      isValid = false;
    }

    // Validate actions JSON if provided
    if (activeTab === 'actions' && templateActions) {
      try {
        JSON.parse(templateActions);
      } catch (e) {
        errors.actions = 'Invalid JSON format';
        isValid = false;
      }
    }

    // Validate parameters JSON if provided
    if (activeTab === 'parameters' && templateParameters) {
      try {
        JSON.parse(templateParameters);
      } catch (e) {
        errors.parameters = 'Invalid JSON format';
        isValid = false;
      }
    }

    // Validate test_data JSON if provided
    if (activeTab === 'test_data' && templateTestData) {
      try {
        JSON.parse(templateTestData);
      } catch (e) {
        errors.test_data = 'Invalid JSON format';
        isValid = false;
      }
    }

    setValidationErrors(errors);
    return isValid;
  };

  const handleSave = async () => {
    if (!canUpdateTemplates || !template) return;
    
    if (!validateForm()) {
      return;
    }

    try {
      setIsSaving(true);
      
      // Parse JSON strings back to objects
      const actionsObj = templateActions ? JSON.parse(templateActions) : null;
      const parametersObj = templateParameters ? JSON.parse(templateParameters) : null;
      const testDataObj = templateTestData ? JSON.parse(templateTestData) : null;
      
      await apiClient(`/admin/templates/${templateId}`, {
        method: 'PUT',
        token: adminSession.token,
        headers: {
          'X-Tenant-ID': adminSession.tenant?.id || ''
        },
        body: JSON.stringify({
          body: templateBody,
          description,
          actions: actionsObj,
          parameters: parametersObj,
          test_data: testDataObj,
        }),
      });
      
      // Redirect back to the detail view
      router.push(`/admin/templates/${templateId}`);
    } catch (err) {
      console.error('Error updating template:', err);
      if (err instanceof Error) {
        setError(`Failed to save template: ${err.message}`);
      } else {
        setError('Failed to save template');
      }
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <RefreshCw className="animate-spin h-6 w-6 text-gray-500" />
        <span className="ml-2">Loading template...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="p-6">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative">
          <strong className="font-bold">Not Found: </strong>
          <span className="block sm:inline">The requested template could not be found.</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <a href={`/admin/templates/${templateId}`}>
          <Button 
            variant="ghost" 
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Template
          </Button>
        </a>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Edit Template: {template.key}</h1>
          <p className="text-gray-600 mt-1">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize
              ${template.category === 'action' ? 'bg-purple-100 text-purple-800' : ''}
              ${template.category === 'response_gen' ? 'bg-green-100 text-green-800' : ''}
              ${template.category === 'error_handler' ? 'bg-red-100 text-red-800' : ''}
              ${template.category === 'system' ? 'bg-blue-100 text-blue-800' : ''}
              ${!['action', 'response_gen', 'error_handler', 'system'].includes(template.category) ? 'bg-gray-100 text-gray-800' : ''}
            `}>
              {template.category}
            </span>
            <span className="mx-2">•</span>
            <span>Current Version: {template.version}</span>
          </p>
        </div>
        <Button 
          onClick={handleSave}
          disabled={isSaving}
          className="bg-indigo-600 hover:bg-indigo-700 text-white"
        >
          {isSaving ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" /> Save Template
            </>
          )}
        </Button>
      </div>

      <div className="bg-white shadow-sm rounded-lg p-6 mb-6">
        <div className="mb-4">
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter a description for this template"
            rows={3}
            className="block w-full mt-1"
          />
        </div>
      </div>

      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('body')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'body'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Template Body
            </button>
            <button
              onClick={() => setActiveTab('actions')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'actions'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Actions Configuration
            </button>
            <button
              onClick={() => setActiveTab('parameters')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'parameters'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Parameters
            </button>
            <button
              onClick={() => setActiveTab('test_data')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'test_data'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Test Data
            </button>
          </nav>
        </div>

        <div className="mt-6">
          {activeTab === 'body' && (
            <div>
              <TemplateEditor
                initialValue={templateBody}
                onChange={setTemplateBody}
                language={template.language === 'en' ? 'plaintext' : template.language}
                theme="light"
              />
              {validationErrors.body && (
                <div className="mt-2 text-red-500 text-sm flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" /> {validationErrors.body}
                </div>
              )}
            </div>
          )}
          {activeTab === 'actions' && (
            <div>
              <TemplateEditor
                initialValue={templateActions}
                onChange={setTemplateActions}
                language="json"
                theme="light"
              />
              {validationErrors.actions && (
                <div className="mt-2 text-red-500 text-sm flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" /> {validationErrors.actions}
                </div>
              )}
            </div>
          )}
          {activeTab === 'parameters' && (
            <div>
              <TemplateEditor
                initialValue={templateParameters}
                onChange={setTemplateParameters}
                language="json"
                theme="light"
              />
              {validationErrors.parameters && (
                <div className="mt-2 text-red-500 text-sm flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" /> {validationErrors.parameters}
                </div>
              )}
            </div>
          )}
          {activeTab === 'test_data' && (
            <div>
              <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Test Data Format</h3>
                <p className="text-sm text-blue-700">
                  Test data allows you to define mock responses and sample parameters for testing templates without making actual API calls.
                </p>
                <pre className="mt-2 text-xs bg-white p-2 rounded border border-blue-200">
{`{
  "mock_responses": {
    "success": { "status": 200, "data": {...} },
    "error": { "status": 404, "error": "Not found" }
  },
  "sample_parameters": {
    "param1": "value1",
    "param2": 123
  }
}`}
                </pre>
              </div>
              <TemplateEditor
                initialValue={templateTestData}
                onChange={setTemplateTestData}
                language="json"
                theme="light"
              />
              {validationErrors.test_data && (
                <div className="mt-2 text-red-500 text-sm flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" /> {validationErrors.test_data}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TemplateEditPage;