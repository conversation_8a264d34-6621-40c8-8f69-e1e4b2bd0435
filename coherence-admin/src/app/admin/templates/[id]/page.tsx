'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useHasPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path
import apiClient, { deleteResource } from '@/lib/apiClient';
import TemplateEditor from '@/components/TemplateEditor';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit, RefreshCw, Trash, Zap } from 'lucide-react';

interface TemplateDetails {
  id: string;
  key: string;
  description?: string | null;
  category: string;
  body: string;
  actions?: Record<string, any> | null;
  parameters?: Record<string, any> | null;
  test_data?: Record<string, any> | null;
  language: string;
  version: number;
  updated_at: string;
  protected?: boolean;
  // Unified template fields
  endpoint_id?: string | null;
  intent_config?: Record<string, any> | null;
  action_config?: Record<string, any> | null;
  ui_fields?: Record<string, any> | null;
  prompts?: Record<string, any> | null;
  docs_config?: Record<string, any> | null;
  response_format?: Record<string, any> | null;
}

const TemplateDetailPage = () => {
  const router = useRouter();
  const params = useParams();
  const templateId = params.id as string;
  const adminSession = useAdminSession();
  
  const [template, setTemplate] = useState<TemplateDetails | null>(null);
  const [intentTemplate, setIntentTemplate] = useState<TemplateDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingIntent, setIsLoadingIntent] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [intentError, setIntentError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'body' | 'intent' | 'action' | 'parameters' | 'prompts' | 'response' | 'docs' | 'test' | 'actions'>('overview');
  const [isTestExecuting, setIsTestExecuting] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);

  const canUpdateTemplates = useHasPermission('template:update' as Permission);

  const executeTest = async (scenario?: string) => {
    if (!template || !adminSession.token) return;
    
    try {
      setIsTestExecuting(true);
      const response = await apiClient(`/templates/${templateId}/test`, {
        method: 'POST',
        token: adminSession.token,
        headers: {
          'X-Tenant-ID': adminSession.tenant?.id || '',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          scenario: scenario || 'success'
        })
      });
      
      setTestResults(response);
    } catch (err) {
      console.error('Error executing test:', err);
      setError(err instanceof Error ? err.message : 'Failed to execute test');
    } finally {
      setIsTestExecuting(false);
    }
  };

  const handleDeleteTemplate = async () => {
    if (!window.confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
      return;
    }
    
    try {
      setIsDeleting(true);
      console.log("Deleting template:", templateId);
      
      // Use apiClient directly instead of deleteResource to ensure consistency
      await apiClient(`/admin/templates/${templateId}`, {
        method: 'DELETE',
        token: adminSession.token,
        orgId: adminSession.tenant?.id || ''
      });
      
      console.log("Template deleted successfully");
      
      // Redirect back to templates list on success
      router.push('/admin/templates');
      
    } catch (err) {
      console.error('Error deleting template:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to delete template. Please try refreshing the page and logging in again.');
      }
      setIsDeleting(false);
    }
  };

  // Helper function to find associated intent template
  const findIntentTemplate = async () => {
    if (!template || !template.key || !adminSession.tenant?.id) return;
    
    // Only search for intent template if we're viewing a response_gen template
    if (template.category !== 'response_gen') return;
    
    // Construct the intent template key by adding 'intent_' prefix
    const intentKey = `intent_${template.key}`;
    
    try {
      setIsLoadingIntent(true);
      setIntentError(null);
      
      // Fetch all templates and filter for the intent template
      const allTemplates = await apiClient<TemplateDetails[]>('/admin/templates', {
        token: adminSession.token,
        headers: {
          'X-Tenant-ID': adminSession.tenant?.id || ''
        }
      });
      
      // Find the intent template with the matching key
      const matchingTemplate = allTemplates.find(t => t.key === intentKey);
      
      if (matchingTemplate) {
        // Now fetch the full template details
        const intentData = await apiClient<TemplateDetails>(`/admin/templates/${matchingTemplate.id}`, {
          token: adminSession.token,
          headers: {
            'X-Tenant-ID': adminSession.tenant?.id || ''
          }
        });
        setIntentTemplate(intentData);
      } else {
        setIntentTemplate(null);
        console.log(`No intent template found with key: ${intentKey}`);
      }
    } catch (err) {
      console.error('Error fetching intent template:', err);
      if (err instanceof Error) {
        setIntentError(err.message);
      } else {
        setIntentError('Failed to load intent template');
      }
    } finally {
      setIsLoadingIntent(false);
    }
  };

  useEffect(() => {
    if (!adminSession.isAuthenticated || adminSession.isLoading) return;

    const fetchTemplate = async () => {
      try {
        setIsLoading(true);
        const data = await apiClient<TemplateDetails>(`/admin/templates/${templateId}`, {
          token: adminSession.token,
          headers: {
            'X-Tenant-ID': adminSession.tenant?.id || ''
          }
        });
        setTemplate(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching template:', err);
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('Failed to load template details');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchTemplate();
  }, [templateId, adminSession]);
  
  // Effect to find the associated intent template when the main template loads
  useEffect(() => {
    if (template) {
      findIntentTemplate();
    }
  }, [template, adminSession.tenant?.id]);

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <RefreshCw className="animate-spin h-6 w-6 text-cw-n-cyan" />
        <span className="ml-2 text-cw-n-blue">Loading template...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-cw-n-red/10 border border-cw-n-red/30 text-cw-n-red px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="p-6">
        <div className="bg-cw-n-yellow/10 border border-cw-n-yellow/30 text-cw-n-yellow px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
          <strong className="font-bold">Not Found: </strong>
          <span className="block sm:inline">The requested template could not be found.</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <a href="/admin/templates">
          <Button 
            variant="ghost" 
            className="text-cw-n-blue hover:text-cw-n-cyan hover:bg-cw-n-blue/10 transition-colors"
          >
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Templates
          </Button>
        </a>
      </div>

      <div className="flex justify-between items-center mb-8 bg-gradient-to-r from-cw-bg-top/70 to-cw-bg-bottom/70 p-6 rounded-xl backdrop-blur-sm border border-cw-n-blue/30 shadow-lg">
        <div>
          <h1 className="text-2xl font-semibold bg-gradient-to-r from-cw-n-cyan to-cw-n-blue bg-clip-text text-transparent">{template.key}</h1>
          <p className="text-cw-n-blue mt-1">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize shadow-cw-glow-xs border
              ${template.category === 'action' ? 'bg-cw-n-purple/10 text-cw-n-purple border-cw-n-purple/30' : ''}
              ${template.category === 'response_gen' ? 'bg-cw-n-green/10 text-cw-n-green border-cw-n-green/30' : ''}
              ${template.category === 'error_handler' ? 'bg-cw-n-red/10 text-cw-n-red border-cw-n-red/30' : ''}
              ${template.category === 'system' ? 'bg-cw-n-blue/10 text-cw-n-blue border-cw-n-blue/30' : ''}
              ${!['action', 'response_gen', 'error_handler', 'system'].includes(template.category) ? 'bg-cw-n-blue/10 text-cw-n-blue border-cw-n-blue/20' : ''}
            `}>
              {template.category}
            </span>
            <span className="mx-2">•</span>
            <span>Version {template.version}</span>
            <span className="mx-2">•</span>
            <span>Last updated {new Date(template.updated_at).toLocaleDateString()}</span>
            {template.protected && (
              <>
                <span className="mx-2">•</span>
                <span className="inline-flex items-center rounded-full bg-cw-n-yellow/10 border border-cw-n-yellow/30 px-2 py-0.5 text-xs font-medium text-cw-n-yellow shadow-cw-glow-xs" title="System template - cannot be modified by regular admins">
                  Protected
                </span>
              </>
            )}
          </p>
        </div>
        <div className="flex gap-2">
          {canUpdateTemplates && (
            <>
              {template.protected ? (
                <Button className="bg-cw-bg-top/60 border border-cw-n-blue/20 text-cw-n-blue/50 shadow-cw-glow-xs" disabled title="System templates cannot be edited by regular admins">
                  <Edit className="mr-2 h-4 w-4" /> Edit Template
                </Button>
              ) : (
                <Link href={`/admin/templates/${templateId}/edit`}>
                  <Button className="bg-cw-n-blue hover:bg-cw-n-cyan text-white shadow-cw-glow-sm transition-colors">
                    <Edit className="mr-2 h-4 w-4" /> Edit Template
                  </Button>
                </Link>
              )}
              
              <Button 
                onClick={handleDeleteTemplate}
                className={template.protected ? 
                  "bg-cw-bg-top/60 border border-cw-n-blue/20 text-cw-n-blue/50 shadow-cw-glow-xs" : 
                  "bg-cw-n-red hover:bg-cw-n-red/80 text-white shadow-cw-glow-sm"}
                disabled={isDeleting || template.protected}
                title={template.protected ? "System templates cannot be deleted by regular admins" : ""}
              >
                {isDeleting ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Trash className="mr-2 h-4 w-4" />
                )}
                Delete
              </Button>
            </>
          )}
        </div>
      </div>

      {template.description && (
        <div className="bg-cw-bg-top/50 p-4 rounded-lg mb-6 border border-cw-n-blue/20 shadow-cw-glow-sm">
          <h2 className="text-md font-medium text-cw-n-cyan mb-2">Description</h2>
          <p className="text-cw-n-blue">{template.description}</p>
        </div>
      )}

      <div className="mb-6">
        <div className="border-b border-cw-n-blue/20 bg-gradient-to-r from-cw-bg-top/30 to-cw-bg-bottom/30 rounded-t-lg">
          <nav className="-mb-px flex flex-wrap space-x-2 p-1 overflow-x-auto">
            {/* Overview tab */}
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-colors whitespace-nowrap ${
                activeTab === 'overview'
                  ? 'border-cw-n-cyan text-cw-n-cyan bg-cw-n-blue/10'
                  : 'border-transparent text-cw-n-blue hover:text-cw-n-cyan hover:border-cw-n-blue/30 hover:bg-cw-n-blue/5'
              }`}
            >
              Overview
            </button>

            {/* Template Body tab */}
            <button
              onClick={() => setActiveTab('body')}
              className={`py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-colors whitespace-nowrap ${
                activeTab === 'body'
                  ? 'border-cw-n-cyan text-cw-n-cyan bg-cw-n-blue/10'
                  : 'border-transparent text-cw-n-blue hover:text-cw-n-cyan hover:border-cw-n-blue/30 hover:bg-cw-n-blue/5'
              }`}
            >
              Template Body
            </button>

            {/* Intent Configuration tab - for unified templates or legacy intent templates */}
            {(template.category === 'unified' || template.intent_config || template.category === 'response_gen' || template.category === 'intent_router') && (
              <button
                onClick={() => setActiveTab('intent')}
                className={`py-3 px-4 border-b-2 font-medium text-sm flex items-center rounded-t-lg transition-colors whitespace-nowrap ${
                  activeTab === 'intent'
                    ? 'border-cw-n-cyan text-cw-n-cyan bg-cw-n-blue/10'
                    : 'border-transparent text-cw-n-blue hover:text-cw-n-cyan hover:border-cw-n-blue/30 hover:bg-cw-n-blue/5'
                }`}
              >
                <Zap className="mr-1 h-4 w-4" /> Intent Config
                {template.intent_config && <span className="ml-1 w-2 h-2 bg-cw-n-green rounded-full"></span>}
              </button>
            )}

            {/* Action Configuration tab - for unified templates */}
            {(template.category === 'unified' || template.action_config) && (
              <button
                onClick={() => setActiveTab('action')}
                className={`py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-colors whitespace-nowrap ${
                  activeTab === 'action'
                    ? 'border-cw-n-cyan text-cw-n-cyan bg-cw-n-blue/10'
                    : 'border-transparent text-cw-n-blue hover:text-cw-n-cyan hover:border-cw-n-blue/30 hover:bg-cw-n-blue/5'
                }`}
              >
                Action Config
                {template.action_config && <span className="ml-1 w-2 h-2 bg-cw-n-green rounded-full"></span>}
              </button>
            )}

            {/* Parameters tab */}
            {(template.parameters || template.ui_fields || template.category === 'unified') && (
              <button
                onClick={() => setActiveTab('parameters')}
                className={`py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-colors whitespace-nowrap ${
                  activeTab === 'parameters'
                    ? 'border-cw-n-cyan text-cw-n-cyan bg-cw-n-blue/10'
                    : 'border-transparent text-cw-n-blue hover:text-cw-n-cyan hover:border-cw-n-blue/30 hover:bg-cw-n-blue/5'
                }`}
              >
                Parameters
                {(template.parameters || template.ui_fields) && <span className="ml-1 w-2 h-2 bg-cw-n-green rounded-full"></span>}
              </button>
            )}

            {/* Prompts tab - for unified templates */}
            {(template.category === 'unified' || template.prompts) && (
              <button
                onClick={() => setActiveTab('prompts')}
                className={`py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-colors whitespace-nowrap ${
                  activeTab === 'prompts'
                    ? 'border-cw-n-cyan text-cw-n-cyan bg-cw-n-blue/10'
                    : 'border-transparent text-cw-n-blue hover:text-cw-n-cyan hover:border-cw-n-blue/30 hover:bg-cw-n-blue/5'
                }`}
              >
                Prompts
                {template.prompts && <span className="ml-1 w-2 h-2 bg-cw-n-green rounded-full"></span>}
              </button>
            )}

            {/* Response tab - for unified templates or legacy response format */}
            {(template.category === 'unified' || template.response_format) && (
              <button
                onClick={() => setActiveTab('response')}
                className={`py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-colors whitespace-nowrap ${
                  activeTab === 'response'
                    ? 'border-cw-n-cyan text-cw-n-cyan bg-cw-n-blue/10'
                    : 'border-transparent text-cw-n-blue hover:text-cw-n-cyan hover:border-cw-n-blue/30 hover:bg-cw-n-blue/5'
                }`}
              >
                Response
                {template.response_format && <span className="ml-1 w-2 h-2 bg-cw-n-green rounded-full"></span>}
              </button>
            )}

            {/* Documentation tab - for unified templates */}
            {(template.category === 'unified' || template.docs_config) && (
              <button
                onClick={() => setActiveTab('docs')}
                className={`py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-colors whitespace-nowrap ${
                  activeTab === 'docs'
                    ? 'border-cw-n-cyan text-cw-n-cyan bg-cw-n-blue/10'
                    : 'border-transparent text-cw-n-blue hover:text-cw-n-cyan hover:border-cw-n-blue/30 hover:bg-cw-n-blue/5'
                }`}
              >
                Documentation
                {template.docs_config && <span className="ml-1 w-2 h-2 bg-cw-n-green rounded-full"></span>}
              </button>
            )}

            {/* Legacy Actions tab - for non-unified templates */}
            {template.actions && template.category !== 'unified' && (
              <button
                onClick={() => setActiveTab('actions')}
                className={`py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-colors whitespace-nowrap ${
                  activeTab === 'actions'
                    ? 'border-cw-n-cyan text-cw-n-cyan bg-cw-n-blue/10'
                    : 'border-transparent text-cw-n-blue hover:text-cw-n-cyan hover:border-cw-n-blue/30 hover:bg-cw-n-blue/5'
                }`}
              >
                Legacy Actions
                <span className="ml-1 w-2 h-2 bg-cw-n-green rounded-full"></span>
              </button>
            )}

            {/* Test tab */}
            {(template.test_data || template.category === 'unified') && (
              <button
                onClick={() => setActiveTab('test')}
                className={`py-3 px-4 border-b-2 font-medium text-sm flex items-center rounded-t-lg transition-colors whitespace-nowrap ${
                  activeTab === 'test'
                    ? 'border-cw-n-cyan text-cw-n-cyan bg-cw-n-blue/10'
                    : 'border-transparent text-cw-n-blue hover:text-cw-n-cyan hover:border-cw-n-blue/30 hover:bg-cw-n-blue/5'
                }`}
              >
                Test
                {template.test_data && <span className="ml-1 w-2 h-2 bg-cw-n-green rounded-full"></span>}
              </button>
            )}
          </nav>
        </div>

        <div className="mt-6 bg-cw-bg-top/60 p-5 rounded-b-lg border border-cw-n-blue/20 border-t-0 shadow-cw-glow-sm">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Basic Information */}
                <div className="bg-cw-bg-top/60 rounded-lg border border-cw-n-blue/30 p-4 shadow-cw-glow-sm">
                  <h3 className="text-lg font-semibold mb-3 text-cw-n-cyan">Basic Information</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-cw-n-blue/70">Key:</span> <span className="text-cw-n-blue font-mono">{template.key}</span></div>
                    <div><span className="text-cw-n-blue/70">Category:</span> <span className="text-cw-n-blue">{template.category}</span></div>
                    <div><span className="text-cw-n-blue/70">Language:</span> <span className="text-cw-n-blue">{template.language}</span></div>
                    <div><span className="text-cw-n-blue/70">Version:</span> <span className="text-cw-n-blue">{template.version}</span></div>
                    {template.endpoint_id && (
                      <div><span className="text-cw-n-blue/70">Endpoint:</span> <span className="text-cw-n-blue font-mono">{template.endpoint_id}</span></div>
                    )}
                  </div>
                </div>

                {/* Configuration Status */}
                <div className="bg-cw-bg-top/60 rounded-lg border border-cw-n-blue/30 p-4 shadow-cw-glow-sm">
                  <h3 className="text-lg font-semibold mb-3 text-cw-n-cyan">Configuration Status</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="text-cw-n-blue/70">Intent Config:</span>
                      <span className={`w-3 h-3 rounded-full ${template.intent_config ? 'bg-cw-n-green' : 'bg-cw-n-red/50'}`}></span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-cw-n-blue/70">Action Config:</span>
                      <span className={`w-3 h-3 rounded-full ${template.action_config ? 'bg-cw-n-green' : 'bg-cw-n-red/50'}`}></span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-cw-n-blue/70">Parameters:</span>
                      <span className={`w-3 h-3 rounded-full ${(template.parameters || template.ui_fields) ? 'bg-cw-n-green' : 'bg-cw-n-red/50'}`}></span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-cw-n-blue/70">Prompts:</span>
                      <span className={`w-3 h-3 rounded-full ${template.prompts ? 'bg-cw-n-green' : 'bg-cw-n-red/50'}`}></span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-cw-n-blue/70">Response:</span>
                      <span className={`w-3 h-3 rounded-full ${template.response_format ? 'bg-cw-n-green' : 'bg-cw-n-red/50'}`}></span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-cw-n-blue/70">Documentation:</span>
                      <span className={`w-3 h-3 rounded-full ${template.docs_config ? 'bg-cw-n-green' : 'bg-cw-n-red/50'}`}></span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-cw-n-blue/70">Test Data:</span>
                      <span className={`w-3 h-3 rounded-full ${template.test_data ? 'bg-cw-n-green' : 'bg-cw-n-red/50'}`}></span>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-cw-bg-top/60 rounded-lg border border-cw-n-blue/30 p-4 shadow-cw-glow-sm">
                  <h3 className="text-lg font-semibold mb-3 text-cw-n-cyan">Quick Actions</h3>
                  <div className="space-y-2">
                    <button
                      onClick={() => setActiveTab('body')}
                      className="w-full text-left p-2 rounded bg-cw-n-blue/10 hover:bg-cw-n-blue/20 text-cw-n-blue text-sm transition-colors"
                    >
                      View Template Body
                    </button>
                    {template.intent_config && (
                      <button
                        onClick={() => setActiveTab('intent')}
                        className="w-full text-left p-2 rounded bg-cw-n-blue/10 hover:bg-cw-n-blue/20 text-cw-n-blue text-sm transition-colors"
                      >
                        View Intent Configuration
                      </button>
                    )}
                    {template.test_data && (
                      <button
                        onClick={() => setActiveTab('test')}
                        className="w-full text-left p-2 rounded bg-cw-n-blue/10 hover:bg-cw-n-blue/20 text-cw-n-blue text-sm transition-colors"
                      >
                        Run Tests
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Template Description */}
              {template.description && (
                <div className="bg-cw-bg-top/60 rounded-lg border border-cw-n-blue/30 p-4 shadow-cw-glow-sm">
                  <h3 className="text-lg font-semibold mb-3 text-cw-n-cyan">Description</h3>
                  <p className="text-cw-n-blue">{template.description}</p>
                </div>
              )}
            </div>
          )}

          {/* Template Body Tab */}
          {activeTab === 'body' && (
            <div className="space-y-4">
              <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 rounded-lg p-4 shadow-cw-glow-sm">
                <h3 className="text-sm font-medium text-cw-n-cyan mb-2">Template Body Content</h3>
                <p className="text-sm text-cw-n-blue">
                  This is the main template content that defines the behavior and structure.
                </p>
              </div>
              <TemplateEditor
                initialValue={template.body}
                language={template.language === 'en' ? 'plaintext' : template.language}
                readOnly={true}
                theme="vs-dark"
                className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
              />
            </div>
          )}

          {/* Intent Configuration Tab */}
          {activeTab === 'intent' && (
            <div className="space-y-6">
              <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 rounded-lg p-4 shadow-cw-glow-sm">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Zap className="h-5 w-5 text-cw-n-cyan" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-cw-n-cyan mb-1">Intent Configuration</h3>
                    <p className="text-sm text-cw-n-blue">
                      Defines how the system recognizes user intents and routes to this template.
                    </p>
                  </div>
                </div>
              </div>

              {template.intent_config ? (
                <TemplateEditor
                  initialValue={JSON.stringify(template.intent_config, null, 2)}
                  language="json"
                  readOnly={true}
                  theme="vs-dark"
                  className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                />
              ) : (
                <div>
                  {template.category === 'response_gen' ? (
                    // For response_gen templates, show the associated intent template
                    <>
                      {isLoadingIntent ? (
                        <div className="flex items-center justify-center p-8">
                          <RefreshCw className="animate-spin h-6 w-6 text-cw-n-cyan" />
                          <span className="ml-2 text-cw-n-blue">Loading intent template...</span>
                        </div>
                      ) : intentError ? (
                        <div className="bg-cw-n-red/10 border border-cw-n-red/30 text-cw-n-red px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
                          <strong className="font-bold">Error: </strong>
                          <span className="block sm:inline">{intentError}</span>
                        </div>
                      ) : intentTemplate ? (
                        <div>
                          <div className="bg-cw-n-yellow/10 border border-cw-n-yellow/30 rounded-lg p-4 mb-4 shadow-cw-glow-sm">
                            <div className="flex items-center">
                              <div className="flex-shrink-0">
                                <Zap className="h-5 w-5 text-cw-n-yellow" />
                              </div>
                              <div className="ml-3">
                                <p className="text-sm text-cw-n-yellow">
                                  This template is used for vector matching to identify user intents. The patterns and examples below help the system recognize when users are asking about this topic.
                                </p>
                              </div>
                            </div>
                          </div>
                          <div className="mb-2">
                            <Link href={`/admin/templates/${intentTemplate.id}`}>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="text-xs border-cw-n-blue/30 bg-cw-n-blue/10 text-cw-n-blue hover:bg-cw-n-blue/20 hover:text-cw-n-cyan shadow-cw-glow-xs"
                              >
                                View full intent template
                              </Button>
                            </Link>
                          </div>
                          <TemplateEditor
                            initialValue={intentTemplate.body}
                            language={intentTemplate.language === 'en' ? 'plaintext' : intentTemplate.language}
                            readOnly={true}
                            theme="vs-dark"
                            className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                          />
                        </div>
                      ) : (
                        <div className="bg-cw-n-yellow/10 border border-cw-n-yellow/30 text-cw-n-yellow px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
                          <strong className="font-bold">No Intent Template Found: </strong>
                          <span className="block sm:inline">
                            There is no associated intent template for this API action. Intent templates help the AI system recognize when users are asking about this topic.
                          </span>
                        </div>
                      )}
                    </>
                  ) : template.category === 'intent_router' ? (
                    // For intent_router templates, extract and display the patterns
                    <div>
                      <div className="mb-6 grid grid-cols-1 gap-4">
                        <div className="bg-cw-bg-top/60 rounded-lg border border-cw-n-blue/30 p-4 shadow-cw-glow-sm">
                          <h3 className="text-lg font-semibold mb-2 text-cw-n-cyan">Intent Recognition Patterns</h3>
                          <div className="mb-4">
                            {(() => {
                              const lines = template.body.split('\n');
                              let inPatternsSection = false;
                              const patterns = [];
                              
                              for (const line of lines) {
                                if (line.includes('{% block intent_patterns %}')) {
                                  inPatternsSection = true;
                                  continue;
                                }
                                
                                if (inPatternsSection) {
                                  if (line.includes('{% endblock %}')) {
                                    break;
                                  }
                                  
                                  if (line.trim().startsWith('- ')) {
                                    patterns.push(line.trim().substring(2));
                                  }
                                }
                              }
                              
                              if (patterns.length === 0) {
                                return (
                                  <p className="text-cw-n-blue/60 italic">No patterns found in template.</p>
                                );
                              }
                              
                              return (
                                <ul className="list-disc pl-5 space-y-1">
                                  {patterns.map((pattern, index) => (
                                    <li key={index} className="text-cw-n-blue">
                                      {pattern}
                                    </li>
                                  ))}
                                </ul>
                              );
                            })()}
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 text-cw-n-blue px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
                      <strong className="font-bold">No Intent Configuration: </strong>
                      <span className="block sm:inline">
                        This template does not have intent configuration data.
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Action Configuration Tab */}
          {activeTab === 'action' && (
            <div className="space-y-4">
              <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 rounded-lg p-4 shadow-cw-glow-sm">
                <h3 className="text-sm font-medium text-cw-n-cyan mb-2">Action Configuration</h3>
                <p className="text-sm text-cw-n-blue">
                  Defines the API action configuration including method, path, authentication, and retry settings.
                </p>
              </div>
              
              {template.action_config ? (
                <TemplateEditor
                  initialValue={JSON.stringify(template.action_config, null, 2)}
                  language="json"
                  readOnly={true}
                  theme="dark"
                  className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                />
              ) : (
                <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 text-cw-n-blue px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
                  <strong className="font-bold">No Action Configuration: </strong>
                  <span className="block sm:inline">
                    This template does not have action configuration data.
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Parameters Tab */}
          {activeTab === 'parameters' && (
            <div className="space-y-6">
              <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 rounded-lg p-4 shadow-cw-glow-sm">
                <h3 className="text-sm font-medium text-cw-n-cyan mb-2">Parameters Configuration</h3>
                <p className="text-sm text-cw-n-blue">
                  Parameter schemas and UI field definitions for form generation and validation.
                </p>
              </div>

              {template.parameters && (
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-cw-n-cyan">Parameter Schema</h4>
                  <TemplateEditor
                    initialValue={JSON.stringify(template.parameters, null, 2)}
                    language="json"
                    readOnly={true}
                    theme="dark"
                    className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                  />
                </div>
              )}

              {template.ui_fields && (
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-cw-n-cyan">UI Field Definitions</h4>
                  <TemplateEditor
                    initialValue={JSON.stringify(template.ui_fields, null, 2)}
                    language="json"
                    readOnly={true}
                    theme="dark"
                    className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                  />
                </div>
              )}

              {!template.parameters && !template.ui_fields && (
                <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 text-cw-n-blue px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
                  <strong className="font-bold">No Parameters Configuration: </strong>
                  <span className="block sm:inline">
                    This template does not have parameter definitions.
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Prompts Tab */}
          {activeTab === 'prompts' && (
            <div className="space-y-4">
              <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 rounded-lg p-4 shadow-cw-glow-sm">
                <h3 className="text-sm font-medium text-cw-n-cyan mb-2">Prompts Configuration</h3>
                <p className="text-sm text-cw-n-blue">
                  System prompts, parameter extraction prompts, and completion prompts for AI interactions.
                </p>
              </div>
              
              {template.prompts ? (
                <TemplateEditor
                  initialValue={JSON.stringify(template.prompts, null, 2)}
                  language="json"
                  readOnly={true}
                  theme="dark"
                  className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                />
              ) : (
                <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 text-cw-n-blue px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
                  <strong className="font-bold">No Prompts Configuration: </strong>
                  <span className="block sm:inline">
                    This template does not have prompts configuration data.
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Response Tab */}
          {activeTab === 'response' && (
            <div className="space-y-4">
              <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 rounded-lg p-4 shadow-cw-glow-sm">
                <h3 className="text-sm font-medium text-cw-n-cyan mb-2">Response Configuration</h3>
                <p className="text-sm text-cw-n-blue">
                  CRFS (Coherence Response Format Specification) configuration and error handling mappings.
                </p>
              </div>
              
              {template.response_format ? (
                <div className="space-y-4">
                  {/* CRFS Configuration Details */}
                  <div className="bg-cw-bg-top/60 rounded-lg border border-cw-n-blue/30 p-4 shadow-cw-glow-sm">
                    <h4 className="text-md font-medium text-cw-n-cyan mb-3">CRFS Configuration</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-cw-n-blue/70">Kind:</span>
                        <span className="text-cw-n-blue ml-2 font-mono">{template.response_format.kind || 'Not specified'}</span>
                      </div>
                      <div>
                        <span className="text-cw-n-blue/70">Type:</span>
                        <span className="text-cw-n-blue ml-2 font-mono">{template.response_format.type || 'Not specified'}</span>
                      </div>
                      <div>
                        <span className="text-cw-n-blue/70">Auto Select:</span>
                        <span className="text-cw-n-blue ml-2">{template.response_format.auto_select ? 'Yes' : 'No'}</span>
                      </div>
                      <div>
                        <span className="text-cw-n-blue/70">Sections:</span>
                        <span className="text-cw-n-blue ml-2">{template.response_format.sections?.length || 0} defined</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Raw JSON View */}
                  <div>
                    <h4 className="text-md font-medium text-cw-n-cyan mb-2">Raw Configuration</h4>
                    <TemplateEditor
                      initialValue={JSON.stringify(template.response_format, null, 2)}
                      language="json"
                      readOnly={true}
                      theme="vs-dark"
                      height="300px"
                      className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                    />
                  </div>
                </div>
              ) : (
                <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 text-cw-n-blue px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
                  <strong className="font-bold">No Response Configuration: </strong>
                  <span className="block sm:inline">
                    This template does not have response format configuration.
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Documentation Tab */}
          {activeTab === 'docs' && (
            <div className="space-y-4">
              <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 rounded-lg p-4 shadow-cw-glow-sm">
                <h3 className="text-sm font-medium text-cw-n-cyan mb-2">Documentation Configuration</h3>
                <p className="text-sm text-cw-n-blue">
                  Template documentation including descriptions, examples, and usage notes.
                </p>
              </div>
              
              {template.docs_config ? (
                <TemplateEditor
                  initialValue={JSON.stringify(template.docs_config, null, 2)}
                  language="json"
                  readOnly={true}
                  theme="dark"
                  className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                />
              ) : (
                <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 text-cw-n-blue px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
                  <strong className="font-bold">No Documentation Configuration: </strong>
                  <span className="block sm:inline">
                    This template does not have documentation configuration.
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Legacy Actions Tab */}
          {activeTab === 'actions' && template.actions && (
            <div className="space-y-4">
              <div className="bg-cw-n-yellow/10 border border-cw-n-yellow/30 rounded-lg p-4 shadow-cw-glow-sm">
                <h3 className="text-sm font-medium text-cw-n-yellow mb-2">Legacy Actions Configuration</h3>
                <p className="text-sm text-cw-n-yellow">
                  This is legacy actions configuration. For unified templates, use the Action Config tab instead.
                </p>
              </div>
              <TemplateEditor
                initialValue={JSON.stringify(template.actions, null, 2)}
                language="json"
                readOnly={true}
                theme="dark"
                className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
              />
            </div>
          )}

          {/* Test Tab */}
          {activeTab === 'test' && (
            <div className="space-y-4">
              <div className="bg-cw-n-blue/10 border border-cw-n-blue/30 rounded-lg p-4 shadow-cw-glow-sm">
                <h3 className="text-sm font-medium text-cw-n-cyan mb-2">Template Testing</h3>
                <p className="text-sm text-cw-n-blue">
                  Test your template with mock data without making actual API calls.
                </p>
              </div>

              {template.test_data && (Object.keys(template.test_data).length > 0) ? (
                <div>
                  {/* Test scenarios */}
                  {template.test_data.mock_responses && (
                    <div className="mb-6">
                      <h4 className="text-md font-medium text-cw-n-cyan mb-3">Available Test Scenarios</h4>
                      <div className="flex gap-2 flex-wrap">
                        {Object.keys(template.test_data.mock_responses).map((scenario) => (
                          <Button
                            key={scenario}
                            onClick={() => executeTest(scenario)}
                            disabled={isTestExecuting}
                            className="bg-cw-n-blue hover:bg-cw-n-cyan text-white shadow-cw-glow-sm transition-colors"
                          >
                            {isTestExecuting ? (
                              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            ) : null}
                            Test: {scenario}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* Sample parameters */}
                  {template.test_data.sample_parameters && (
                    <div className="mb-6">
                      <h4 className="text-md font-medium text-cw-n-cyan mb-3">Sample Parameters</h4>
                      <TemplateEditor
                        initialValue={JSON.stringify(template.test_data.sample_parameters, null, 2)}
                        language="json"
                        readOnly={true}
                        theme="dark"
                        height="200px"
                        className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                      />
                    </div>
                  )}
                  
                  {/* Test results */}
                  {testResults && (
                    <div className="mb-6">
                      <h4 className="text-md font-medium text-cw-n-cyan mb-3">Test Results</h4>
                      
                      {/* Validation errors */}
                      {testResults.validation_errors && testResults.validation_errors.length > 0 && (
                        <div className="mb-4 bg-cw-n-red/10 border border-cw-n-red/30 rounded-lg p-4 shadow-cw-glow-sm">
                          <h5 className="text-sm font-medium text-cw-n-red mb-2">Validation Errors</h5>
                          <ul className="list-disc pl-5 space-y-1">
                            {testResults.validation_errors.map((error: string, index: number) => (
                              <li key={index} className="text-cw-n-red text-sm">{error}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {/* Mock response */}
                      <div className="mb-4">
                        <h5 className="text-sm font-medium text-cw-n-blue mb-2">Mock Response</h5>
                        <TemplateEditor
                          initialValue={JSON.stringify(testResults.mock_response, null, 2)}
                          language="json"
                          readOnly={true}
                          theme="dark"
                          height="300px"
                          className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                        />
                      </div>
                      
                      {/* Formatted response */}
                      {testResults.formatted_response && (
                        <div className="mb-4">
                          <h5 className="text-sm font-medium text-cw-n-blue mb-2">Formatted Response (CRFS)</h5>
                          <div className="bg-cw-bg-top/60 rounded-lg border border-cw-n-blue/30 p-4 shadow-cw-glow-sm">
                            <pre className="text-sm text-cw-n-blue whitespace-pre-wrap">
                              {JSON.stringify(testResults.formatted_response, null, 2)}
                            </pre>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="bg-cw-n-yellow/10 border border-cw-n-yellow/30 text-cw-n-yellow px-4 py-3 rounded-lg shadow-cw-glow-sm relative">
                    <strong className="font-bold">No Test Data Configured: </strong>
                    <span className="block sm:inline">
                      This template does not have test data configuration. Test data allows you to validate template behavior without making actual API calls.
                    </span>
                  </div>
                  
                  {template.category === 'unified' && (
                    <div className="bg-cw-bg-top/60 rounded-lg border border-cw-n-blue/30 p-4 shadow-cw-glow-sm">
                      <h4 className="text-md font-medium text-cw-n-cyan mb-3">How to Add Test Data</h4>
                      <p className="text-sm text-cw-n-blue mb-3">
                        Test data should include mock responses and sample parameters. Here's an example structure:
                      </p>
                      <TemplateEditor
                        initialValue={JSON.stringify({
                          sample_parameters: {
                            param1: "value1",
                            param2: "value2"
                          },
                          mock_responses: {
                            success: {
                              status: 200,
                              data: { /* mock response data */ }
                            },
                            error: {
                              status: 400,
                              error: "Bad Request"
                            }
                          }
                        }, null, 2)}
                        language="json"
                        readOnly={true}
                        theme="vs-dark"
                        height="250px"
                        className="rounded border border-cw-n-blue/30 shadow-cw-glow-xs overflow-hidden"
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TemplateDetailPage;