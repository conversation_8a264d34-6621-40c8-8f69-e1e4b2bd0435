'use client';

import React, { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation'; // Corrected import for App Router
// Assuming these are available from Phase 1 and project setup
import { useAdminSession } from '../../../context/AdminSessionContext'; // Corrected path
import apiClient from '../../../lib/apiClient'; // Using the correct import name
// Import Button component from our UI library
import { Button } from '../../../components/ui/button';
// Wrap with custom styling to support Cyber Wave theme
const CyberButton = ({ onClick, children, className, disabled = false }: { onClick?: () => void; children: React.ReactNode; className?: string, disabled?: boolean }) => 
    <Button 
        onClick={onClick} 
        disabled={disabled}
        variant={className?.includes('red') ? 'destructive' : className?.includes('blue') ? 'default' : className?.includes('indigo') ? 'cyber' : 'outline'} 
        className={`${className} transition-all duration-300`}
    >
        {children}
    </Button>;

// Helper function to format template keys for better display
const formatTemplateKey = (template: AdminTemplateType): { displayName: string, fullKey: string } => {
    const key = template.key;
    const metadata = template.metadata || {};
    
    // If there's a display_name in metadata, use it
    if (metadata.display_name) {
        return {
            displayName: metadata.display_name,
            fullKey: key
        };
    }
    
    // Handle API templates (format: api_<method>_<operation>_<id_hash>)
    const apiMatch = key.match(/^api_([a-z]+)_(.+)_([0-9a-f]+)$/i);
    if (apiMatch) {
        const method = apiMatch[1].toUpperCase();
        const operation = apiMatch[2].replace(/_/g, ' ');
        return {
            displayName: `${method} ${operation}`,
            fullKey: key
        };
    }
    
    // Handle intent templates (format: intent_api_<method>_<operation>_<id_hash>)
    const intentMatch = key.match(/^intent_api_([a-z]+)_(.+)_([0-9a-f]+)$/i);
    if (intentMatch) {
        const method = intentMatch[1].toUpperCase();
        const operation = intentMatch[2].replace(/_/g, ' ');
        return {
            displayName: `Intent: ${method} ${operation}`,
            fullKey: key
        };
    }
    
    // For fallback templates (format: api_<method>_<operation>_<id_hash>_fallback)
    const fallbackMatch = key.match(/^(api_[a-z]+_.+)_fallback$/i);
    if (fallbackMatch) {
        // Check if there's an operation part
        const apiMatch = fallbackMatch[1].match(/^api_([a-z]+)_(.+)_([0-9a-f]+)$/i);
        if (apiMatch) {
            const method = apiMatch[1].toUpperCase();
            const operation = apiMatch[2].replace(/_/g, ' ');
            return {
                displayName: `Fallback: ${method} ${operation}`,
                fullKey: key
            };
        }
    }
    
    // Legacy format: Handle endpoint-specific templates (format: endpoint_<uuid>_<operation>)
    const endpointMatch = key.match(/^endpoint_([0-9a-f-]+)_(.+)$/i);
    if (endpointMatch) {
        return {
            displayName: endpointMatch[2].replace(/_/g, ' '),
            fullKey: key
        };
    }
    
    // Legacy format: Handle intent templates (format: intent_endpoint_<uuid>_<operation>)
    const legacyIntentMatch = key.match(/^intent_endpoint_([0-9a-f-]+)_(.+)$/i);
    if (legacyIntentMatch) {
        return {
            displayName: `Intent: ${legacyIntentMatch[2].replace(/_/g, ' ')}`,
            fullKey: key
        };
    }
    
    // For other templates, truncate if too long
    if (key.length > 30) {
        return {
            displayName: key.substring(0, 27) + '...',
            fullKey: key
        };
    }
    
    // Default case: return the key as is
    return {
        displayName: key,
        fullKey: key
    };
};

// Define TypeScript types based on Pydantic schemas
// Ideally, these would be in a shared types file, e.g., coherence-admin/src/types/templates.ts

export interface AdminTemplateVersionBase {
    version: number;
    body: string;
    actions?: Record<string, unknown> | null;
    parameters?: Record<string, unknown> | null;
    editor_id?: string | null; // UUID
    edited_at: string; // ISO datetime string
    change_reason?: string | null;
}

export interface AdminTemplateType {
    id: string; // UUID
    tenant_id: string; // UUID
    key: string;
    description?: string | null;
    category: string; // AdminTemplateCategory enum as string
    language: string;
    body: string;
    actions?: Record<string, unknown> | null;
    parameters?: Record<string, unknown> | null;
    scope: string; // AdminTemplateScope enum as string
    version: number;
    created_at: string; // ISO datetime string
    updated_at: string; // ISO datetime string
    created_by?: string | null; // UUID
    protected?: boolean; // If true, template cannot be modified by regular admins
    latest_version_details?: AdminTemplateVersionBase | null;
    metadata?: {
        display_name?: string;
        source?: string;
        integration_id?: string;
        endpoint_id?: string;
        api_name?: string;
        generated?: boolean;
        vector_indexable?: boolean;
        response_template_key?: string;
        tags?: string[];
        [key: string]: any;
    };
}

const TemplatesPage = () => {
    const router = useRouter();
    const adminSession = useAdminSession(); // Correct usage
    const { permissions, isLoading: isSessionLoading, isAuthenticated } = adminSession; // Removed orgId

    const [templates, setTemplates] = useState<AdminTemplateType[]>([]);
    const [filteredTemplates, setFilteredTemplates] = useState<AdminTemplateType[]>([]);
    const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
    const [isLoadingData, setIsLoadingData] = useState(true); // Separate loading state for data fetching
    const [error, setError] = useState<string | null>(null);

    // Group templates by endpoint identification in key
    const getEndpointFromKey = (key: string): string => {
        // Extract endpoint information from template key if it follows the pattern
        // endpoint_<endpoint_id>_<operation>
        const matches = key.match(/^endpoint_([^_]+)_/);
        return matches ? matches[1] : 'Other';
    };

    const canReadTemplates = Array.isArray(permissions) && permissions.includes('template:read');
    const canCreateTemplates = Array.isArray(permissions) && permissions.includes('template:create');
    const canDeleteTemplates = Array.isArray(permissions) && permissions.includes('template:delete');
    const canUpdateTemplates = Array.isArray(permissions) && permissions.includes('template:update');

    const fetchTemplates = useCallback(async () => {
        if (!isAuthenticated || !canReadTemplates) {
            if (isAuthenticated && !canReadTemplates) {
                setError("You do not have permission to view templates.");
            }
            setIsLoadingData(false);
            return;
        }
        try {
            setIsLoadingData(true);
            
            // Detailed logging to help debug auth issues
            console.log('Admin Session:', {
                isAuthenticated,
                hasToken: !!adminSession.token,
                tokenLength: adminSession.token ? adminSession.token.length : 0
            });
            
            // Log the endpoint (removing the /v1 prefix since apiClient will add it)
            console.log('Making API call to:', process.env.NEXT_PUBLIC_API_URL + '/admin/templates');
            console.log('With Authorization header:', adminSession.token ? 'Yes' : 'No');
            console.log('Tenant ID:', adminSession.tenant?.id || 'NOT SET');
            
            // Make sure to include tenant_id which is required for admin routes
            // Using orgId parameter instead of direct headers to ensure apiClient sets X-Tenant-ID properly
            const response = await apiClient<AdminTemplateType[]>('/admin/templates', { 
                method: 'GET',
                token: adminSession.token,
                orgId: adminSession.tenant?.id || ''
            });
            setTemplates(response); // Response is directly AdminTemplateType[]
            setError(null);
        } catch (err: unknown) { // Typed as unknown
            console.error("Failed to fetch templates:", err);
            let message = "Failed to fetch templates. Please try again.";
            if (typeof err === 'object' && err !== null && 'response' in err) {
                const httpError = err as { response?: { data?: { detail?: string } } };
                if (httpError.response?.data?.detail) {
                    message = httpError.response.data.detail;
                }
            } else if (err instanceof Error) {
                message = err.message;
            }
            setError(message);
        } finally {
            setIsLoadingData(false);
        }
    }, [isAuthenticated, canReadTemplates]);

    useEffect(() => {
        if (!isSessionLoading) { // Only fetch once session is loaded (isAuthenticated reflects this)
            fetchTemplates();
        }
    }, [isSessionLoading, fetchTemplates]);
    
    // Filter templates when selection changes or templates update
    useEffect(() => {
        if (selectedCategory) {
            setFilteredTemplates(templates.filter(t => t.category === selectedCategory));
        } else {
            setFilteredTemplates(templates);
        }
    }, [templates, selectedCategory]);

    const handleDelete = async (templateId: string) => {
        if (!canDeleteTemplates) {
            alert("You do not have permission to delete templates.");
            return;
        }
        if (confirm("Are you sure you want to delete this template?")) {
            try {
                console.log("Deleting template:", templateId);
                
                // Make sure to include token and tenant ID which are required
                await apiClient(`/admin/templates/${templateId}`, { 
                    method: 'DELETE',
                    token: adminSession.token,
                    orgId: adminSession.tenant?.id || ''
                });
                
                console.log("Template deleted successfully on server");
                
                // Update the local state to remove the deleted template
                setTemplates(prevTemplates => {
                    const filtered = prevTemplates.filter(t => t.id !== templateId);
                    console.log("Templates after deletion:", filtered.length, "of", prevTemplates.length);
                    return filtered;
                });
                
                // Also update filtered templates to ensure UI is updated
                setFilteredTemplates(prevFiltered => {
                    return prevFiltered.filter(t => t.id !== templateId);
                });
                
                alert("Template deleted successfully.");
                
                // Force a fresh fetch to ensure everything is in sync
                setTimeout(() => {
                    fetchTemplates();
                }, 500);
                
            } catch (err: unknown) { // Typed as unknown
                console.error("Failed to delete template:", err);
                let message = "Failed to delete template.";
                if (typeof err === 'object' && err !== null && 'response' in err) {
                    const httpError = err as { response?: { data?: { detail?: string } } };
                    if (httpError.response?.data?.detail) {
                        message = httpError.response.data.detail;
                    }
                } else if (err instanceof Error) {
                    message = err.message;
                }
                alert(message);
            }
        }
    };

    if (isSessionLoading || (isLoadingData && !templates.length)) { 
        return (
            <div>
                <div className="flex justify-center items-center h-48">
                    <p className="text-muted-foreground">Loading templates...</p>
                </div>
            </div>
        );
    }

    if (!isAuthenticated && !isSessionLoading) { // Check isAuthenticated after loading is complete
        router.push('/login'); 
        return (
            <div>
                <div className="flex justify-center items-center h-48">
                    <p className="text-muted-foreground">Redirecting to login...</p>
                </div>
            </div>
        );
    }

    if (!canReadTemplates && error) { 
        return (
            <div>
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            </div>
        );
    }

    if (!canReadTemplates && !error && !isSessionLoading) { 
        return (
            <div>
                <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
                    <strong className="font-bold">Access Denied: </strong>
                    <span className="block sm:inline">You do not have permission to view this page.</span>
                </div>
            </div>
        );
    }
    
    if (error && !templates.length && !isLoadingData) { 
        return (
            <div>
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            </div>
        );
    }

    return (
        <div>
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold">Templates</h1>
                {canCreateTemplates && (
                    <Link href="/admin/templates/create" passHref>
                        <Button>
                            Create New Template
                        </Button>
                    </Link>
                )}
            </div>
            
            {/* Category Filter */}
            <div className="mb-6 bg-card shadow-lg rounded-xl p-4 border border-border">
                <h2 className="text-sm font-medium text-muted-foreground mb-3">Filter by Category</h2>
                <div className="flex flex-wrap gap-2">
                    <button 
                        className={`px-3 py-1 rounded-md text-sm ${!selectedCategory ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground hover:bg-muted/80'}`}
                        onClick={() => setSelectedCategory(null)}
                    >
                        All
                    </button>
                    <button 
                        className={`px-3 py-1 rounded-md text-sm ${selectedCategory === 'action' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground hover:bg-muted/80'}`}
                        onClick={() => setSelectedCategory('action')}
                    >
                        Action
                    </button>
                    <button 
                        className={`px-3 py-1 rounded-md text-sm ${selectedCategory === 'response_gen' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground hover:bg-muted/80'}`}
                        onClick={() => setSelectedCategory('response_gen')}
                    >
                        Response Gen
                    </button>
                    <button 
                        className={`px-3 py-1 rounded-md text-sm ${selectedCategory === 'error_handler' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground hover:bg-muted/80'}`}
                        onClick={() => setSelectedCategory('error_handler')}
                    >
                        Error Handler
                    </button>
                    <button 
                        className={`px-3 py-1 rounded-md text-sm ${selectedCategory === 'system' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground hover:bg-muted/80'}`}
                        onClick={() => setSelectedCategory('system')}
                    >
                        System
                    </button>
                </div>
            </div>

            {isLoadingData && (
                <div className="bg-card shadow-lg rounded-xl p-6 text-center border border-border">
                    <p className="text-muted-foreground">Loading templates...</p>
                </div>
            )}
            
            {!isLoadingData && !error && templates.length === 0 && (
                <div className="bg-card shadow-lg rounded-xl p-6 text-center border border-border">
                    <p className="text-muted-foreground">No templates found. Get started by creating one!</p>
                </div>
            )}
            
            {!isLoadingData && error && templates.length === 0 && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                    <p>Could not load templates: {error}</p>
                </div>
            )}

            {filteredTemplates.length > 0 && (
                <div className="flex-1 overflow-y-auto">
                    {/* Group templates by endpoint */}
                    {(() => {
                        // Get unique endpoints
                        const endpoints = Array.from(new Set(
                            filteredTemplates.map(t => getEndpointFromKey(t.key))
                        )).sort();
                        
                        return endpoints.map(endpoint => {
                            // Get templates for this endpoint
                            const endpointTemplates = filteredTemplates.filter(
                                t => getEndpointFromKey(t.key) === endpoint
                            );
                            
                            if (endpointTemplates.length === 0) return null;
                            
                            return (
                                <div key={endpoint} className="mb-6 bg-card shadow-lg rounded-xl overflow-hidden border border-border">
                                    <div className="px-6 py-3 bg-muted border-b border-border">
                                        <h3 className="text-lg font-semibold">
                                            Endpoint: {endpoint}
                                        </h3>
                                    </div>
                                    <div className="overflow-hidden">
                                        <table className="min-w-full divide-y divide-border table-fixed">
                                            <thead className="bg-muted/50">
                                                <tr>
                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider w-1/5">Key</th>
                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider w-1/12">Category</th>
                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider w-1/3">Description</th>
                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider w-1/12">Version</th>
                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider w-1/10">Last Updated</th>
                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider w-1/6">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody className="bg-background divide-y divide-border">
                                                {endpointTemplates.map((template) => (
                                                    <tr 
                                                        key={template.id} 
                                                        className="hover:bg-muted/50 transition-colors cursor-pointer"
                                                        onClick={() => router.push(`/admin/templates/${template.id}`)}
                                                    >
                                                        <td className="px-6 py-4 text-sm font-medium">
                                                            <div className="flex items-center">
                                                                <span 
                                                                    className="text-cw-foreground truncate max-w-[180px] inline-block"
                                                                    title={template.key} // Show full key on hover
                                                                >
                                                                    {formatTemplateKey(template).displayName}
                                                                </span>
                                                                {template.protected && (
                                                                    <span className="ml-2 inline-flex flex-shrink-0 items-center rounded-full bg-cw-n-yellow/20 text-cw-n-yellow border border-cw-n-yellow/40 px-2 py-0.5 text-xs font-mono font-medium tracking-wider" title="System template - cannot be modified by regular admins">
                                                                        PROTECTED
                                                                    </span>
                                                                )}
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4 text-sm">
                                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-mono font-medium uppercase tracking-wider border
                                                                ${template.category === 'action' ? 'bg-cw-accent-right/20 text-cw-accent-right border-cw-accent-right/40' : ''}
                                                                ${template.category === 'response_gen' ? 'bg-cw-n-green/20 text-cw-n-green border-cw-n-green/40' : ''}
                                                                ${template.category === 'error_handler' ? 'bg-cw-n-red/20 text-cw-n-red border-cw-n-red/40' : ''}
                                                                ${template.category === 'system' ? 'bg-cw-n-blue/20 text-cw-n-blue border-cw-n-blue/40' : ''}
                                                                ${template.category === 'intent_router' ? 'bg-cw-accent-left/20 text-cw-accent-left border-cw-accent-left/40' : ''}
                                                                ${template.category === 'documentation' ? 'bg-cw-n-cyan/20 text-cw-n-cyan border-cw-n-cyan/40' : ''}
                                                                ${!['action', 'response_gen', 'error_handler', 'system', 'intent_router', 'documentation'].includes(template.category) ? 'bg-cw-n-white/20 text-cw-n-white border-cw-n-white/40' : ''}
                                                            `}>
                                                                {template.category}
                                                            </span>
                                                        </td>
                                                        <td className="px-6 py-4 text-sm text-muted-foreground overflow-hidden">
                                                            <div className="truncate" title={template.description || ''}>
                                                                {template.description || '-'}
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4 text-sm text-muted-foreground text-center">{template.version}</td>
                                                        <td className="px-6 py-4 text-sm text-muted-foreground">{new Date(template.updated_at).toLocaleDateString()}</td>
                                                        <td className="px-6 py-4 text-sm font-medium">
                                                            <div className="flex justify-end space-x-1">
                                                                {canUpdateTemplates && (
                                                                    template.protected ? (
                                                                        <CyberButton 
                                                                            disabled 
                                                                            className="text-foreground/40 text-xs !px-1.5 !py-0.5 bg-transparent cursor-not-allowed" 
                                                                            title="System templates cannot be edited by regular admins"
                                                                        >
                                                                            Edit
                                                                        </CyberButton>
                                                                    ) : (
                                                                        <CyberButton 
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                router.push(`/admin/templates/${template.id}/edit`);
                                                                            }}
                                                                            className="text-indigo-600 hover:text-indigo-900 text-xs !px-1.5 !py-0.5 bg-transparent hover:bg-accent/10"
                                                                        >
                                                                            Edit
                                                                        </CyberButton>
                                                                    )
                                                                )}
                                                                <CyberButton 
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        router.push(`/admin/templates/${template.id}`);
                                                                    }}
                                                                    className="text-blue-600 hover:text-blue-900 text-xs !px-1.5 !py-0.5 bg-transparent hover:bg-primary/10"
                                                                >
                                                                    View
                                                                </CyberButton>
                                                                {canDeleteTemplates && (
                                                                    template.protected ? (
                                                                        <CyberButton 
                                                                            disabled 
                                                                            className="text-foreground/40 text-xs !px-1.5 !py-0.5 bg-transparent cursor-not-allowed"
                                                                            title="System templates cannot be deleted by regular admins"
                                                                        >
                                                                            Delete
                                                                        </CyberButton>
                                                                    ) : (
                                                                        <CyberButton 
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                handleDelete(template.id);
                                                                            }}
                                                                            className="text-red-600 hover:text-red-900 text-xs !px-1.5 !py-0.5 bg-transparent hover:bg-cw-n-red/10"
                                                                        >
                                                                            Delete
                                                                        </CyberButton>
                                                                    )
                                                                )}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            );
                        });
                    })()}
                </div>
            )}
        </div>
    );
};

export default TemplatesPage; 