'use client';

import React, { useRef, useEffect } from 'react';
import { useChat } from '@/lib/hooks/useChat';
import ChatContainer from '@/components/chat/ChatContainer';
import ChatMessage from '@/components/chat/ChatMessage';
import ChatInput from '@/components/chat/ChatInput';
import { useAdminSession } from '@/context/AdminSessionContext';

interface ChatClientProps {
  userId: string;
}

/**
 * Client component for the chat interface
 * 
 * This component manages the chat state and renders the chat UI components
 */
const ChatClient: React.FC<ChatClientProps> = ({ userId }) => {
  // Use the chat hook to manage state and API interactions
  const { 
    state, 
    sendMessage, 
    resetConversation, 
    handleFieldResponse,
    handleIntentSelection
  } = useChat(userId);
  
  // Ref for auto-scrolling to the latest message
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Auto-scroll to the bottom when new messages are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [state.messages]);
  
  // Function to determine if an error is auth-related
  const isAuthError = (error: string | null): boolean => {
    if (!error) return false;
    
    const authErrorPatterns = [
      'unauthorized', 
      'auth', 
      'permission', 
      'forbidden', 
      '401', 
      '403',
      'not authenticated', 
      'authentication failed',
      'token',
      'api key',
      'credential'
    ];
    
    return authErrorPatterns.some(pattern => 
      error.toLowerCase().includes(pattern.toLowerCase())
    );
  };

  // Check if user is a system admin
  const { permissions } = useAdminSession();
  const isSystemAdmin = React.useMemo(() => {
    return permissions?.includes('system:*') || false;
  }, [permissions]);

  // Function to render appropriate error message UI
  const renderErrorMessage = () => {
    if (!state.error) return null;
    
    if (isAuthError(state.error)) {
      // If it's an API key error and the user is a system admin, don't show the error
      if (isSystemAdmin && state.error.toLowerCase().includes('api key')) {
        return null;
      }
      
      return (
        <div className="mx-4 p-4 bg-yellow-100 border border-yellow-400 rounded-md mb-4">
          <div className="flex items-start">
            <div className="p-2 rounded-full bg-yellow-200 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-yellow-800 mb-1">Authentication Error</h4>
              <p className="text-sm mb-3 text-yellow-700">{state.error}</p>
              
              <div className="text-xs text-yellow-700">
                <p className="font-medium">This might be due to:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Missing or expired API key for your organization</li>
                  <li>Insufficient permissions for this operation</li>
                  <li>Session timeout</li>
                </ul>
              </div>
              
              <div className="flex flex-wrap mt-4 gap-2">
                <button 
                  onClick={() => window.location.reload()} 
                  className="text-xs px-3 py-1.5 rounded bg-white border border-gray-300 hover:bg-gray-50 transition-colors"
                >
                  ↻ Refresh Page
                </button>
                
                {(state.error && state.error.toLowerCase().includes('api key')) && (
                  <a 
                    href="/admin/api-keys" 
                    className="text-xs px-3 py-1.5 rounded bg-yellow-600 text-white hover:bg-yellow-700 transition-colors inline-flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                    </svg>
                    Create API Key
                  </a>
                )}
              </div>
              
              {(state.error && state.error.toLowerCase().includes('api key')) && (
                <div className="mt-4 text-xs border border-yellow-200 bg-yellow-50 p-3 rounded">
                  <p className="font-medium text-yellow-800">Note about API keys:</p>
                  <ol className="list-decimal pl-5 mt-2 space-y-1 text-yellow-700">
                    <li>Create a new API key for your organization</li>
                    <li><strong>IMPORTANT:</strong> Copy the full key value shown after creation</li>
                    <li>Set this value as <code className="bg-gray-100 px-1 py-0.5 rounded">COHERENCE_API_KEY</code> in your environment</li>
                    <li>Restart your application</li>
                  </ol>
                  
                  {state.responseData?.development_key && (
                    <div className="mt-3 p-3 bg-gray-100 rounded">
                      <p className="font-medium text-gray-700">Development API Key:</p>
                      <code className="block mt-1 p-2 bg-white rounded text-xs break-all font-mono">{state.responseData.development_key}</code>
                      <p className="mt-1 text-gray-600">Set this as COHERENCE_API_KEY in your environment (development only)</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }
    
    return (
      <div className="mx-4 p-4 bg-red-100 border border-red-400 rounded-md mb-4">
        <div className="flex items-start">
          <div className="p-2 rounded-full bg-red-200 mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-red-800 mb-1">Error</h4>
            <p className="text-sm text-red-700">{state.error}</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Header with controls */}
      <div className="flex-shrink-0 flex justify-between items-center border-b border-border px-6 py-4 bg-card">
        <div>
          <h2 className="text-xl font-semibold">Coherence Chat</h2>
          <p className="text-sm text-muted-foreground mt-1">Interact with the intent pipeline using natural language</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={resetConversation}
            className="px-4 py-2 text-sm border border-border rounded-md hover:bg-muted transition-colors"
          >
            ↻ New Conversation
          </button>
        </div>
      </div>
      
      {/* Chat messages container - flex-1 to take remaining space */}
      <div className="flex-1 overflow-hidden">
        <ChatContainer>
        {state.messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-20 px-4 text-center">
            <div className="w-16 h-16 mb-6 rounded-full bg-muted flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-8 h-8 text-muted-foreground">
                <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Start a conversation</h3>
            <p className="max-w-md text-muted-foreground text-sm">
              Ask a question or enter a command to interact with the Coherence intent pipeline.
            </p>
            <div className="mt-6 p-4 bg-muted/50 rounded-md text-sm">
              <p className="font-medium mb-2">Try asking something like:</p>
              <ul className="text-muted-foreground space-y-1">
                <li>• "What APIs do I have available?"</li>
                <li>• "Show me the weather for New York"</li>
                <li>• "Search for recent articles about AI"</li>
              </ul>
            </div>
          </div>
        ) : (
          state.messages.map((message) => (
            <ChatMessage
              key={message.id}
              message={message}
              onFieldResponse={handleFieldResponse}
              onIntentSelection={handleIntentSelection}
            />
          ))
        )}
        
        {/* Enhanced error message */}
        {renderErrorMessage()}
        
        {/* Active workflows */}
        {Object.values(state.activeWorkflows).map((workflow) => (
          <div key={workflow.workflow_id} className="mx-4 p-4 bg-muted/50 border border-border rounded-md mb-4">
            <div className="flex items-center">
              <div className="animate-spin mr-3 h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
              <p className="text-sm">{workflow.current_step || 'Processing...'}</p>
            </div>
            <div className="w-full bg-muted rounded-full h-2 mt-3">
              <div 
                className="bg-primary h-2 rounded-full transition-all" 
                style={{ width: `${workflow.progress * 100}%` }}
              ></div>
            </div>
          </div>
        ))}
        
        {/* Auto-scroll anchor */}
        <div ref={messagesEndRef} />
      </ChatContainer>
      </div>
      
      {/* Chat input - flex-shrink-0 to keep it at bottom */}
      <div className="flex-shrink-0 border-t border-border px-4 py-4 bg-background">
        <ChatInput 
          onSendMessage={sendMessage} 
          isLoading={state.isLoading}
          disabled={!!state.activeField || (isAuthError(state.error) && !(isSystemAdmin && state.error.toLowerCase().includes('api key')))}
          placeholder={state.activeField 
            ? `Please provide a value for ${state.activeField}...` 
            : (isAuthError(state.error) && !(isSystemAdmin && state.error.toLowerCase().includes('api key')))
              ? "Please resolve authentication issues before continuing..."
              : "Type a message..."
          }
        />
      </div>
    </div>
  );
};

export default ChatClient;