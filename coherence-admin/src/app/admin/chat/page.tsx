'use client';

import React from 'react';
import { useAdminSession } from '@/context/AdminSessionContext';
// import ProtectedRoute from '@/components/auth/ProtectedRoute'; // Removed
import ChatClient from './ChatClient';
// import { CoherencePermission } from '@/lib/permissions'; // Removed
import { useHasPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path

/**
 * Chat page in the admin portal
 * 
 * This page renders the chat interface, which allows users to interact
 * with the Coherence intent pipeline directly.
 * 
 * Uses useHasPermission to enforce the chat:access permission.
 */
const ChatPage: React.FC = () => {
  const { userId, isLoading, isAuthenticated } = useAdminSession();
  const hasChatAccess = useHasPermission('chat:access' as Permission);
  
  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto max-w-4xl p-6">
        <div className="flex justify-center items-center h-48">
          <p className="text-muted-foreground">Loading chat interface...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto max-w-4xl p-6">
        <div className="bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-400 dark:border-yellow-600 text-yellow-700 dark:text-yellow-400 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Authentication Required: </strong>
          <span className="block sm:inline">Please log in to access the chat interface.</span>
        </div>
      </div>
    );
  }

  if (!hasChatAccess) {
    return (
      <div className="container mx-auto max-w-4xl p-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-600 text-red-700 dark:text-red-400 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Access Denied: </strong>
          <span className="block sm:inline">You don't have permission to access the chat interface. (chat:access)</span>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col overflow-hidden">
      <div className="flex-shrink-0 p-6">
        <h1 className="text-3xl font-bold text-foreground">Chat Interface</h1>
      </div>
      
      <div className="flex-1 px-6 pb-6 overflow-hidden">
        <div className="h-full max-w-7xl mx-auto">
          <div className="bg-card shadow-lg rounded-xl h-full flex flex-col overflow-hidden border border-border">
            {userId && <ChatClient userId={userId} />}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatPage;