'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useClerk, useUser } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { useAdminSession } from '@/context/AdminSessionContext';

const ProfilePage = () => {
  const { user, isLoaded: userIsLoaded } = useUser();
  const { signOut } = useClerk();
  const router = useRouter();
  const { isLoading, isAuthenticated, userId, organization, tenant } = useAdminSession();

  const handleSignOut = async () => {
    await signOut();
    router.push('/sign-in');
  };

  if (isLoading || !userIsLoaded) {
    return (
      <div>
        <div className="flex justify-center items-center h-48">
          <p className="text-muted-foreground">Loading profile information...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return (
      <div>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Authentication Required: </strong>
          <span className="block sm:inline">Please log in to view your profile.</span>
        </div>
        <div className="mt-4">
          <Button onClick={() => router.push('/sign-in')}>Sign In</Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Your Profile</h1>
      
      <div className="bg-card shadow-lg rounded-xl p-6 md:p-8 border border-border">
        <div className="flex items-center gap-6 mb-8">
          {user.imageUrl && (
            <img 
              src={user.imageUrl} 
              alt={user.firstName || 'User'}
              className="w-24 h-24 rounded-full object-cover border-2 border-border"
            />
          )}
          <div>
            <h2 className="text-2xl font-bold text-foreground">{user.fullName || 'User'}</h2>
            <p className="text-muted-foreground">{user.primaryEmailAddress?.emailAddress}</p>
            <p className="text-muted-foreground text-sm mt-1">User ID: {userId}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div>
            <h3 className="font-semibold text-foreground mb-2">Account Information</h3>
            <div className="bg-muted/50 p-4 rounded-md space-y-2">
              <p><span className="font-medium">Email:</span> {user.primaryEmailAddress?.emailAddress}</p>
              <p><span className="font-medium">Created:</span> {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</p>
              <p><span className="font-medium">Last Sign In:</span> {user.lastSignInAt ? new Date(user.lastSignInAt).toLocaleString() : 'N/A'}</p>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-foreground mb-2">Organization</h3>
            <div className="bg-muted/50 p-4 rounded-md space-y-2">
              {organization ? (
                <>
                  <p><span className="font-medium">Name:</span> {organization.name}</p>
                  <p><span className="font-medium">Role:</span> {organization.role || 'Member'}</p>
                  <p><span className="font-medium">Organization ID:</span> {organization.id}</p>
                </>
              ) : (
                <p className="text-muted-foreground italic">No organization information available.</p>
              )}
            </div>
          </div>
        </div>
        
        {tenant && (
          <div className="mb-8">
            <h3 className="font-semibold text-foreground mb-2">Tenant Information</h3>
            <div className="bg-muted/50 p-4 rounded-md space-y-2">
              <p><span className="font-medium">Tenant Name:</span> {tenant.name}</p>
              <p><span className="font-medium">Tenant ID:</span> {tenant.id}</p>
              <p><span className="font-medium">Industry:</span> {tenant.industry || 'Not specified'}</p>
              {tenant.created_at && <p><span className="font-medium">Created:</span> {new Date(tenant.created_at).toLocaleDateString()}</p>}
            </div>
          </div>
        )}
        
        <div className="space-y-4">
          <h3 className="font-semibold text-foreground mb-2">Account Actions</h3>
          <div className="flex gap-4">
            <Button variant="outline" onClick={() => window.open('https://accounts.clerk.dev/user/account', '_blank')}>
              Manage Account
            </Button>
            <Button variant="outline" onClick={() => router.push('/org-selection')}>
              Change Organization
            </Button>
            <Button variant="destructive" onClick={handleSignOut}>
              Sign Out
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;