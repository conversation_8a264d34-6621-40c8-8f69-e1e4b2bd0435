'use client';

import React, { useState } from 'react';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useHasPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path
import apiClient from '@/lib/apiClient';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Upload, Globe, FileText, Link2 } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import jsYaml from 'js-yaml';

// Cyber Wave styled button component
const CyberButton = ({ onClick, children, className, disabled = false, variant = 'default' }: { 
  onClick?: () => void; 
  children: React.ReactNode; 
  className?: string; 
  disabled?: boolean;
  variant?: 'default' | 'destructive' | 'outline';
}) => (
  <Button 
    onClick={onClick} 
    disabled={disabled}
    variant={variant}
    className={`${className} transition-all duration-300`}
  >
    {children}
  </Button>
);

const ImportApiSpecPage = () => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [specContent, setSpecContent] = useState('');
  const [openApiUrl, setOpenApiUrl] = useState('');
  const [isLoadingFromUrl, setIsLoadingFromUrl] = useState(false);
  // Using setFile below in handleFileChange, but not using file directly
  const [, setFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('url');
  const adminSession = useAdminSession();
  const router = useRouter();

  // Handle loading OpenAPI spec from URL
  const handleLoadFromUrl = async () => {
    if (!openApiUrl.trim()) {
      setError('Please provide a valid URL');
      return;
    }

    setIsLoadingFromUrl(true);
    setError(null);

    try {
      // Validate URL format
      const url = new URL(openApiUrl);
      
      // Use the proxy API to fetch the OpenAPI spec (to avoid CORS issues)
      const proxyUrl = `/api/proxy/openapi?url=${encodeURIComponent(url.toString())}`;
      const response = await fetch(proxyUrl);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: response.statusText }));
        throw new Error(errorData.error || `Failed to fetch OpenAPI spec: ${response.status} ${response.statusText}`);
      }
      
      const contentType = response.headers.get('content-type') || '';
      let content: string;
      
      // First get the raw text response
      content = await response.text();
      
      // Check if it's HTML by looking for typical HTML tags
      if (content.trim().startsWith('<!DOCTYPE') || content.trim().startsWith('<html')) {
        throw new Error('The URL returned an HTML page instead of an API specification. Please verify the URL points directly to a JSON or YAML specification file.');
      }
      
      // Try to parse as JSON first
      try {
        // If it's already valid JSON, parse and re-stringify it for formatting
        const json = JSON.parse(content);
        content = JSON.stringify(json, null, 2);
      } catch (jsonError) {
        // If it's not valid JSON, try YAML
        try {
          console.log("URL content isn't valid JSON, attempting YAML parsing");
          const yamlData = jsYaml.load(content);
          
          // Check that it parsed to a valid object
          if (!yamlData || typeof yamlData !== 'object') {
            console.warn('YAML parsing did not produce a valid object');
            // Leave content as-is for further handling
          } else {
            // Convert YAML to JSON for consistent handling
            console.log("Successfully parsed URL content as YAML");
            content = JSON.stringify(yamlData, null, 2);
          }
        } catch (yamlError) {
          // If it's not valid YAML either, leave it as plain text
          console.warn('Content is neither valid JSON nor YAML, using as raw text:', yamlError);
        }
      }
      
      setSpecContent(content);
      
      // Try to extract a name from the URL if name is empty
      if (!name.trim()) {
        const pathParts = url.pathname.split('/');
        const filename = pathParts[pathParts.length - 1];
        if (filename && filename !== 'openapi.json') {
          const nameWithoutExt = filename.replace(/\.(json|yaml|yml)$/, '');
          setName(nameWithoutExt.charAt(0).toUpperCase() + nameWithoutExt.slice(1));
        }
      }
      
      setSuccessMessage('API specification loaded successfully from URL');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error('Error loading OpenAPI spec from URL:', err);
      if (err instanceof Error) {
        setError(`Failed to load OpenAPI spec: ${err.message}`);
      } else {
        setError('Failed to load OpenAPI specification from URL. Please check the URL and try again.');
      }
    } finally {
      setIsLoadingFromUrl(false);
    }
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setError(null); // Clear any previous errors
      
      // Validate file type
      const validExtensions = ['.json', '.yaml', '.yml', '.txt'];
      const fileExt = selectedFile.name.substring(selectedFile.name.lastIndexOf('.')).toLowerCase();
      
      if (!validExtensions.includes(fileExt)) {
        setError(`Unsupported file type: ${fileExt}. Please upload a JSON or YAML file.`);
        return;
      }
      
      // Read file content
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          const content = event.target.result as string;
          setSpecContent(content);
          
          // Try to extract a name from the filename if name is empty
          if (!name.trim()) {
            const filename = selectedFile.name;
            const nameWithoutExt = filename.replace(/\.(json|yaml|yml)$/, '');
            setName(nameWithoutExt.charAt(0).toUpperCase() + nameWithoutExt.slice(1));
          }
          
          // Auto-convert YAML to JSON if needed
          if (fileExt === '.yaml' || fileExt === '.yml') {
            try {
              // Try to parse YAML and convert to JSON
              const yamlData = jsYaml.load(content);
              if (yamlData && typeof yamlData === 'object') {
                // If successful, update the content to be JSON
                setSpecContent(JSON.stringify(yamlData, null, 2));
                console.log('Successfully converted YAML file to JSON');
              } else {
                setError('The YAML file could not be parsed. Please check the format.');
              }
            } catch (yamlError) {
              console.error('Error parsing YAML file:', yamlError);
              if (yamlError instanceof Error) {
                setError(`Error parsing YAML file: ${yamlError.message}`);
              } else {
                setError('Error parsing YAML file: An unknown error occurred.');
              }
            }
          }
        }
      };
      reader.onerror = () => {
        setError(`Failed to read file: ${selectedFile.name}. Please try again or use URL/manual entry instead.`);
      };
      reader.readAsText(selectedFile);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    if (!name.trim()) {
      setError('Please provide a name for the integration');
      setIsLoading(false);
      return;
    }

    if (!specContent.trim()) {
      setError('Please provide OpenAPI specification content');
      setIsLoading(false);
      return;
    }

    try {
      // Parse the spec content as JSON or YAML (the backend expects it as a JSON object)
      let specJson;
      try {
        // First try to parse as JSON
        try {
          specJson = JSON.parse(specContent);
        } catch (jsonError) {
          // If JSON parsing fails, try YAML parsing
          try {
            console.log("Attempting to parse content as YAML instead of JSON");
            specJson = jsYaml.load(specContent);
            
            if (!specJson || typeof specJson !== 'object') {
              throw new Error("YAML parsing resulted in invalid object");
            }
            
            console.log("Successfully parsed content as YAML", specJson);
          } catch (yamlError) {
            console.error('YAML parse error:', yamlError);
            
            // Check if the content appears to be HTML
            if (specContent.trim().startsWith('<!DOCTYPE') || specContent.trim().startsWith('<html')) {
              throw new Error('The specification appears to be HTML, not a valid API specification. Please provide a proper JSON or YAML document.');
            }
            
            // If both JSON and YAML parsing fail, throw a combined error
            const previewContent = specContent.substring(0, 100).replace(/\n/g, ' ');
            const jsonErrorMessage = jsonError instanceof Error ? jsonError.message : String(jsonError);
            const yamlErrorMessage = yamlError instanceof Error ? yamlError.message : String(yamlError);
            throw new Error(`API specification must be in valid JSON or YAML format. JSON error: ${jsonErrorMessage}. YAML error: ${yamlErrorMessage}. Content starts with: "${previewContent}..."`);
          }
        }
      } catch (parseError) {
        console.error('Failed to parse content as either JSON or YAML:', parseError);
        throw parseError;
      }

      // Check if the spec is a supported format (OpenAPI, FAPI, or BAPI)
      if (
        // OpenAPI format
        (!specJson.openapi && !specJson.swagger) && 
        // FAPI format
        (!specJson.financial_api && !specJson.fapi_version) && 
        // BAPI format
        (!specJson.business_api && !specJson.bapi_version)
      ) {
        throw new Error('Specification must be in a supported format (OpenAPI, FAPI, or BAPI).');
      }
      
      // All formats should at least have an info section
      if (!specJson.info) {
        throw new Error('Specification must include an "info" section.');
      }
      
      // For OpenAPI, verify paths section exists
      if ((specJson.openapi || specJson.swagger) && !specJson.paths) {
        throw new Error('OpenAPI specification must include a "paths" section.');
      }
      
      // For FAPI, verify endpoints or some structure exists
      if ((specJson.financial_api || specJson.fapi_version) && 
          !specJson.endpoints && !specJson.security_profiles) {
        throw new Error('FAPI specification must include either "endpoints" or "security_profiles" sections.');
      }
      
      // For BAPI, verify services section exists
      if ((specJson.business_api || specJson.bapi_version) && !specJson.services) {
        throw new Error('BAPI specification must include a "services" section.');
      }

      // Ensure we have valid data before proceeding
      if (!name.trim()) {
        setError('Please provide a name for the integration');
        setIsLoading(false);
        return;
      }

      if (!specJson || typeof specJson !== 'object') {
        setError('Invalid specification format. Please provide a valid JSON or YAML specification.');
        setIsLoading(false);
        return;
      }

      const payload = {
        name,
        description: description.trim() || null,
        spec: specJson, // Send as JSON object, not string
      };

      // Debug payload information
      console.log('Preparing to submit payload:', {
        payloadName: payload.name,
        payloadDescriptionLength: payload.description ? payload.description.length : 0,
        specJsonType: typeof specJson,
        specJsonSize: JSON.stringify(specJson).length,
        specHasInfo: specJson.info ? true : false,
      });

      if (!adminSession.token) {
        throw new Error('Session token is missing.');
      }

      interface ImportResponse {
        integration_id: string;
        name: string;
        spec_format: string;
        // Add other properties as needed
      }

      // Ensure we have the organization ID from the session
      if (!adminSession.organization?.id) {
        throw new Error('Organization not selected. Please select an organization first.');
      }

      // Log the actual request payload for debugging
      console.log('Full API request payload:', JSON.stringify(payload).substring(0, 500) + '...');

      // Use the appropriate endpoint based on detected format
      const endpoint = '/v1/openapi/import';
      
      console.log(`Sending API import request to ${endpoint}`);
      
      try {
        // Add a timeout to allow for potentially large payloads
        const response = await apiClient<ImportResponse>(endpoint, {
          method: 'POST',
          token: adminSession.token,
          orgId: adminSession.organization.id, // Use organization ID from Clerk
          body: JSON.stringify(payload),
          // Add a longer timeout for large specs
          timeout: 30000,
        });
        
        // Success - handle the response
        console.log('API import successful:', response);
        
        setSuccessMessage(`Successfully imported ${name} API (${response.spec_format.toUpperCase()} format)!`);
        
        // Redirect to the integration details after a brief delay
        setTimeout(() => {
          router.push(`/admin/integrations/${response.integration_id}`);
        }, 1500);
      } catch (err) {
        // Re-throw the error to be caught by the outer catch block
        throw err;
      }
    } catch (err) {
      console.error('Error importing API spec:', err);
      
      // Log important debug information
      console.log('Import debug info:', {
        endpointUsed: '/v1/openapi/import',
        hasToken: !!adminSession.token,
        tokenLength: adminSession.token ? adminSession.token.length : 0,
        tenantId: adminSession.tenant?.id,
        organizationName: adminSession.tenant?.name || 'Not selected',
        specContentSize: specContent ? specContent.length : 0,
        nameProvided: name ? name.length : 0,
        apiBaseUrl: typeof window !== 'undefined' 
          ? (process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_COHERENCE_API_URL || 'http://localhost:8001/v1')
          : 'Server-side rendering',
        permissions: adminSession.permissions ? adminSession.permissions.join(', ') : 'None'
      });
      
      if (err instanceof Error) {
        // Check for common error patterns
        if (err.message.includes('401') || err.message.includes('Unauthorized')) {
          setError(`Authentication error: ${err.message}. Please check that you are logged in, have selected an organization, and have the right permissions.`);
        } else if (err.message.includes('404') || err.message.includes('Not Found')) {
          setError(`API endpoint not found: ${err.message}. This may indicate a configuration issue with the API URL.`);
        } else if (err.message.includes('400') || err.message.includes('Bad Request') || err.message.startsWith('HTTP error 400')) {
          // Add more detailed error messages for 400 errors
          if (err.message.includes('YAML')) {
            setError(`YAML parsing error: ${err.message}. Please check that your YAML is properly formatted and valid.`);
          } else if (err.message.includes('JSON')) {
            setError(`JSON parsing error: ${err.message}. Please check that your JSON is properly formatted and valid.`);
          } else if (err.message.includes('HTML')) {
            setError(`The URL returned HTML content instead of a valid API specification. Please check that the URL points directly to a JSON or YAML file.`);
          } else {
            setError(`Bad request error: ${err.message}. This may be due to an invalid API specification format or content.`);
          }
        } else {
          // General error message with debugging tips
          setError(`Failed to import API specification: ${err.message}. Please check that you have selected an organization and have the right permissions.`);
        }
      } else {
        setError('Failed to import API specification. An unknown error occurred. Please check the browser console for more details.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (adminSession.isLoading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[calc(100vh-theme(spacing.24))]">
        <p className="text-lg text-cw-foreground/70">Loading session...</p>
      </div>
    );
  }

  if (adminSession.error) {
    return (
      <div className="p-6">
        <div className="bg-cw-n-red/20 border border-cw-n-red text-cw-n-red px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Session Error: </strong>
          <span className="block sm:inline">{adminSession.error.message}</span>
        </div>
      </div>
    );
  }

  if (!adminSession.isAuthenticated) {
    return (
      <div className="p-6">
        <div className="bg-cw-n-yellow/20 border border-cw-n-yellow text-cw-n-yellow px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Authentication Required: </strong>
          <span className="block sm:inline">Please log in to access this page.</span>
        </div>
      </div>
    );
  }

  // const canCreateIntegration = Array.isArray(adminSession.permissions) &&
  //                             adminSession.permissions.includes('integration:create');
  // Replaced by hook call below

  const canCreateIntegration = useHasPermission('integration:create' as Permission);

  if (!adminSession.isLoading && !canCreateIntegration) { // Ensure session is loaded before denying
    return (
      <div className="p-6">
        <div className="bg-cw-n-yellow/20 border border-cw-n-yellow text-cw-n-yellow px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Access Denied: </strong>
          <span className="block sm:inline">You do not have permission to import API integrations. (integration:create)</span>
        </div>
      </div>
    );
  }
  
  // Check if the user has selected an organization
  if (!adminSession.organization?.id) {
    return (
      <div className="p-6">
        <div className="bg-cw-n-yellow/20 border border-cw-n-yellow text-cw-n-yellow px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Organization Required: </strong>
          <span className="block sm:inline">Please select an organization before importing an API integration. <a href="/org-selection" className="underline">Select Organization</a></span>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <CyberButton 
          variant="outline" 
          onClick={() => router.push('/admin/integrations')}
          className="bg-gradient-to-r from-cw-bg-top/70 to-cw-bg-bottom/70 backdrop-blur-sm border-cw-n-blue/30 text-cw-foreground hover:shadow-cw-glow-xs"
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Integrations
        </CyberButton>
      </div>

      <header className="mb-6 bg-gradient-to-r from-cw-bg-top/70 to-cw-bg-bottom/70 backdrop-blur-sm rounded-xl p-6 border border-cw-n-blue/30 shadow-cw-glow-sm">
        <h1 className="text-3xl font-bold mb-2 text-cw-foreground">Import API Specification</h1>
        <p className="text-cw-foreground/80">Import an API specification (OpenAPI, FAPI, or BAPI) to create a new API integration</p>
      </header>

      {error && (
        <div className="mb-6 bg-cw-n-red/20 border border-cw-n-red text-cw-n-red px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {successMessage && (
        <div className="mb-6 bg-cw-n-green/20 border border-cw-n-green text-cw-n-green px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Success: </strong>
          <span className="block sm:inline">{successMessage}</span>
        </div>
      )}

      <div className="bg-gradient-to-r from-cw-bg-top/70 to-cw-bg-bottom/70 backdrop-blur-sm border border-cw-n-blue/30 shadow-cw-glow-sm rounded-xl p-6 md:p-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label htmlFor="name" className="block text-sm font-medium text-cw-foreground">
              Integration Name *
            </label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="My API Integration"
              required
              className="bg-cw-bg-top/30 border-cw-n-blue/30 text-cw-foreground placeholder:text-cw-foreground/50"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="description" className="block text-sm font-medium text-cw-foreground">
              Description
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="A brief description of this API integration"
              rows={3}
              className="bg-cw-bg-top/30 border-cw-n-blue/30 text-cw-foreground placeholder:text-cw-foreground/50"
            />
          </div>

          <div className="space-y-4">
            <label className="block text-sm font-medium text-cw-foreground">
              API Specification Source * (OpenAPI, FAPI, or BAPI)
            </label>
            
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-gradient-to-r from-cw-bg-top/50 to-cw-bg-bottom/50 border border-cw-n-blue/30">
                <TabsTrigger value="url" className="flex items-center gap-2 data-[state=active]:bg-cw-accent-left/20 data-[state=active]:text-cw-accent-left">
                  <Globe className="h-4 w-4" />
                  From URL
                </TabsTrigger>
                <TabsTrigger value="file" className="flex items-center gap-2 data-[state=active]:bg-cw-accent-left/20 data-[state=active]:text-cw-accent-left">
                  <Upload className="h-4 w-4" />
                  File Upload
                </TabsTrigger>
                <TabsTrigger value="manual" className="flex items-center gap-2 data-[state=active]:bg-cw-accent-left/20 data-[state=active]:text-cw-accent-left">
                  <FileText className="h-4 w-4" />
                  Manual Entry
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="url" className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="url" className="block text-sm font-medium text-cw-foreground">
                    API Specification URL
                  </label>
                  <div className="flex space-x-2">
                    <Input
                      id="url"
                      value={openApiUrl}
                      onChange={(e) => setOpenApiUrl(e.target.value)}
                      placeholder="https://api.example.com/openapi.json"
                      className="flex-1"
                      type="url"
                    />
                    <Button
                      type="button"
                      onClick={handleLoadFromUrl}
                      disabled={isLoadingFromUrl || !openApiUrl.trim()}
                      variant="outline"
                    >
                      {isLoadingFromUrl ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Loading...
                        </span>
                      ) : (
                        <span className="flex items-center">
                          <Link2 className="mr-2 h-4 w-4" /> Load
                        </span>
                      )}
                    </Button>
                  </div>
                  <p className="text-sm text-cw-foreground/60">
                    Enter the URL to your API specification (OpenAPI, FAPI, or BAPI in JSON or YAML format)
                  </p>
                </div>
              </TabsContent>
              
              <TabsContent value="file" className="space-y-2">
                <label htmlFor="file" className="block text-sm font-medium text-cw-foreground">
                  API Specification File
                </label>
                <Input
                  id="file"
                  type="file"
                  accept=".json,.yaml,.yml,.txt"
                  onChange={handleFileChange}
                  className="cursor-pointer"
                />
                <p className="text-sm text-cw-foreground/60">
                  Upload a JSON or YAML API specification file (OpenAPI, FAPI, or BAPI)
                </p>
              </TabsContent>
              
              <TabsContent value="manual" className="space-y-2">
                <label htmlFor="spec" className="block text-sm font-medium text-cw-foreground">
                  API Specification Content
                </label>
                <Textarea
                  id="spec"
                  value={specContent}
                  onChange={(e) => setSpecContent(e.target.value)}
                  placeholder="Paste your API specification (OpenAPI, FAPI, or BAPI) in JSON or YAML format here"
                  rows={10}
                  className="font-mono text-sm"
                />
                <p className="text-sm text-cw-foreground/60">
                  Paste your API specification content (OpenAPI, FAPI, or BAPI) manually
                </p>
              </TabsContent>
            </Tabs>
          </div>

          {specContent && (
            <div className="space-y-2">
              <label className="block text-sm font-medium text-cw-foreground">
                Preview
              </label>
              <Textarea
                value={specContent}
                onChange={(e) => setSpecContent(e.target.value)}
                rows={6}
                className="font-mono text-sm"
                placeholder="OpenAPI specification will appear here"
              />
            </div>
          )}

          <div className="flex justify-end">
            <Button 
              type="submit" 
              disabled={isLoading || !specContent.trim() || !name.trim()}
              className="ml-3"
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </span>
              ) : (
                <span className="flex items-center">
                  <Upload className="mr-2 h-4 w-4" /> Import Integration
                </span>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ImportApiSpecPage;
