'use client'; 

import React, { useEffect, useState } from 'react';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useHasPermission } from '@/lib/hooks/useHasPermission';
import type { Permission } from '@/lib/generated/permissions';
import apiClient, { deleteResource } from '@/lib/apiClient';
import { Button } from '@/components/ui/button';
import { Plus, Trash, RefreshCw } from "lucide-react";
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Interface for a single integration
interface Integration {
  id: string; // UUID
  tenant_id: string; // UUID
  name: string;
  description?: string | null;
  summary?: string | null; // Summary from OpenAPI spec (often API title)
  api_type: string; // "rest", "graphql", etc.
  tags?: string[]; // Tags from OpenAPI spec
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
  has_credentials: boolean; // Whether credentials are configured
  spec_format?: string; // Format of the API specification (openapi, fapi, bapi)
}

// Cyber Wave styled button component
const CyberButton = ({ onClick, children, className, disabled = false, variant = 'default' }: { 
  onClick?: (e: React.MouseEvent) => void; 
  children: React.ReactNode; 
  className?: string; 
  disabled?: boolean;
  variant?: 'default' | 'destructive' | 'outline';
}) => (
  <Button 
    onClick={onClick} 
    disabled={disabled}
    variant={variant}
    className={`${className} transition-all duration-300`}
  >
    {children}
  </Button>
);

// Helper function to group integrations by spec format/api type
const getGroupFromIntegration = (integration: Integration): string => {
  if (integration.spec_format) {
    return integration.spec_format.toUpperCase();
  }
  return integration.api_type ? integration.api_type.toUpperCase() : 'Other';
};

const IntegrationsListPage = () => {
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [filteredIntegrations, setFilteredIntegrations] = useState<Integration[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loadingData, setLoadingData] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [integrationToDelete, setIntegrationToDelete] = useState<Integration | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const adminSession = useAdminSession();
  const router = useRouter();

  const hasReadIntegrationsPermission = useHasPermission('integration:read' as Permission);
  const canCreateIntegrationPermission = useHasPermission('integration:create' as Permission);
  const canDeleteIntegration = useHasPermission('integration:delete' as Permission);

  // Filter integrations when selection changes or integrations update
  useEffect(() => {
    if (selectedGroup) {
      setFilteredIntegrations(integrations.filter(i => getGroupFromIntegration(i) === selectedGroup));
    } else {
      setFilteredIntegrations(integrations);
    }
  }, [integrations, selectedGroup]);

  // Delete handler
  const handleDelete = async (integration: Integration) => {
    setIntegrationToDelete(integration);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!integrationToDelete) return;
    
    try {
      setIsDeleting(true);
      await deleteResource(`/openapi/integrations/${integrationToDelete.id}`, { 
        token: adminSession.token, 
        orgId: adminSession.organization?.id
      });
      // After successful deletion, refresh the data
      setIntegrations(integrations.filter(item => item.id !== integrationToDelete.id));
    } catch (error) {
      console.error("Failed to delete integration:", error);
      alert(`Failed to delete integration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setIntegrationToDelete(null);
    }
  };

  // Separate the token refresh from the data loading
  // Track if a data load has occurred
  const [dataLoaded, setDataLoaded] = useState(false);
  
  // Effect for permission checks and authentication state
  useEffect(() => {
    console.log('AdminSession state:', {
      isLoading: adminSession.isLoading,
      isAuthenticated: adminSession.isAuthenticated,
      permissions: adminSession.permissions,
      token: adminSession.token ? 'present' : 'missing',
      organization: adminSession.organization
    });

    if (adminSession.isLoading) {
      setLoadingData(true); // Keep loading while session is resolving
      return;
    }

    if (!adminSession.isAuthenticated) {
      setLoadingData(false);
      // setHasPermission(false); // Not needed, hasReadIntegrationsPermission handles this
      setError("User not authenticated."); // More specific error
      return;
    }

    // const canReadIntegrations = Array.isArray(adminSession.permissions) &&
    //                          adminSession.permissions.includes('integration:read');
    // console.log('Permission check:', {
    //   permissions: adminSession.permissions,
    //   canReadIntegrations: hasReadIntegrationsPermission,
    //   hasIntegrationRead: adminSession.permissions?.includes('integration:read')
    // });
    // setHasPermission(hasReadIntegrationsPermission); // No longer using state for this

    if (!hasReadIntegrationsPermission) {
      setLoadingData(false);
      setError("You do not have permission to view integrations."); // More specific error
      return;
    }

    // Only trigger loading if we haven't loaded data yet and user has permission
    if (!dataLoaded && adminSession.token) {
      loadIntegrationsData(adminSession.token, adminSession.organization?.id);
    }
  }, [adminSession.isAuthenticated, adminSession.isLoading, adminSession.token, adminSession.organization, dataLoaded, hasReadIntegrationsPermission]);
  
  // Separate function to load data
  const loadIntegrationsData = async (token: string | null, orgId: string | undefined) => {
    // Permission check is now done before calling this function
    if (!token) {
      setError('Session token is missing.');
      setLoadingData(false);
      // setHasPermission(false); // Not needed
      return;
    }
    
    setLoadingData(true);
    console.log('Making API call to /openapi');
    
    try {
      const responseData = await apiClient<Integration[]>('/openapi', { token });
      console.log('API response:', responseData);
      setIntegrations(responseData);
      setError(null);
      setDataLoaded(true); // Mark data as loaded successfully
    } catch (apiError: unknown) {
      console.error('API Error fetching integrations:', apiError);
      if (apiError instanceof Error) {
        setError(`Failed to load integrations: ${apiError.message}`);
      } else if (typeof apiError === 'string') {
        setError(`Failed to load integrations: ${apiError}`);
      } else {
        setError('Failed to load integrations. An unknown error occurred.');
      }
    } finally {
      setLoadingData(false);
    }
  };

  if (adminSession.isLoading) {
    return (
      <div className="flex justify-center items-center h-48">
        <p className="text-muted-foreground">Loading session...</p>
      </div>
    );
  }
  
  if (adminSession.error) {
    return (
      <div className="container mx-auto max-w-7xl p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Session Error: </strong>
          <span className="block sm:inline">{adminSession.error.message}</span>
        </div>
      </div>
    );
  }

  if (!adminSession.isAuthenticated) {
    return (
     <div className="container mx-auto max-w-7xl p-6">
       <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
         <strong className="font-bold">Authentication Required: </strong>
         <span className="block sm:inline">Please log in to access this page.</span>
       </div>
     </div>
   );
 }

 // Combined loading state check
 if (adminSession.isLoading || (loadingData && hasReadIntegrationsPermission)) {
   return (
     <div className="container mx-auto max-w-7xl p-6">
       <div className="flex justify-center items-center h-48">
         <p className="text-muted-foreground">Loading integrations...</p>
       </div>
     </div>
   );
 }

 // Error display should come after loading checks and auth checks
  if (error) { // This will catch auth errors, permission errors, or data fetching errors
   return (
     <div className="container mx-auto max-w-7xl p-6">
       <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
         <strong className="font-bold">Error: </strong>
         <span className="block sm:inline">{error}</span>
       </div>
     </div>
   );
 }

 // This specific permission denied message can be shown if no other error occurred
 if (!hasReadIntegrationsPermission) {
   return (
     <div className="container mx-auto max-w-7xl p-6">
       <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
         <strong className="font-bold">Access Denied: </strong>
         <span className="block sm:inline">You do not have permission to view integrations. (integration:read)</span>
       </div>
     </div>
   );
 }
 
 // const canCreateIntegration = Array.isArray(adminSession.permissions) &&
 //                             adminSession.permissions.includes('integration:create');
 // Use the hook result for canCreateIntegrationPermission defined at the top

 return (
   <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-cw-foreground">API Integrations</h1>
        <div className="flex gap-2">
          <CyberButton 
            variant="outline" 
            onClick={() => adminSession.token && loadIntegrationsData(adminSession.token, adminSession.organization?.id)}
            disabled={loadingData}
            className="bg-gradient-to-r from-cw-bg-top/70 to-cw-bg-bottom/70 backdrop-blur-sm border-cw-n-blue/30"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loadingData ? 'animate-spin' : ''}`} />
            {loadingData ? 'Refreshing...' : 'Refresh'}
          </CyberButton>
          {canCreateIntegrationPermission && (
            <CyberButton 
              onClick={() => router.push('/admin/integrations/import')}
              className="bg-gradient-to-r from-cw-accent-left to-cw-accent-right shadow-cw-glow-sm"
            >
              <Plus className="mr-2 h-4 w-4" /> Import API
            </CyberButton>
          )}
        </div>
      </div>

      {/* Group Filter */}
      <div className="mb-6 bg-gradient-to-r from-cw-bg-top/70 to-cw-bg-bottom/70 backdrop-blur-sm rounded-xl p-4 border border-cw-n-blue/30 shadow-cw-glow-xs">
        <h2 className="text-sm font-medium text-cw-foreground/80 mb-3">Filter by Type</h2>
        <div className="flex flex-wrap gap-2">
          <button 
            className={`px-3 py-1 rounded-md text-sm transition-all duration-300 ${
              !selectedGroup 
                ? 'bg-cw-accent-left/20 text-cw-accent-left border border-cw-accent-left/40 shadow-cw-glow-xs' 
                : 'bg-cw-n-white/10 text-cw-n-white/70 border border-cw-n-white/20 hover:bg-cw-n-white/20'
            }`}
            onClick={() => setSelectedGroup(null)}
          >
            All
          </button>
          {Array.from(new Set(integrations.map(i => getGroupFromIntegration(i)))).sort().map(group => (
            <button 
              key={group}
              className={`px-3 py-1 rounded-md text-sm transition-all duration-300 ${
                selectedGroup === group
                  ? 'bg-cw-accent-left/20 text-cw-accent-left border border-cw-accent-left/40 shadow-cw-glow-xs'
                  : 'bg-cw-n-white/10 text-cw-n-white/70 border border-cw-n-white/20 hover:bg-cw-n-white/20'
              }`}
              onClick={() => setSelectedGroup(group)}
            >
              {group}
            </button>
          ))}
        </div>
      </div>

      {loadingData && (
        <div className="bg-gradient-to-r from-cw-bg-top/70 to-cw-bg-bottom/70 backdrop-blur-sm rounded-xl p-6 text-center border border-cw-n-blue/30 shadow-cw-glow-xs">
          <p className="text-cw-foreground/80">Loading integrations...</p>
        </div>
      )}
      
      {!loadingData && !error && integrations.length === 0 && (
        <div className="bg-gradient-to-r from-cw-bg-top/70 to-cw-bg-bottom/70 backdrop-blur-sm rounded-xl p-6 text-center border border-cw-n-blue/30 shadow-cw-glow-xs">
          <p className="text-cw-foreground/80">No integrations found. Get started by importing an API!</p>
        </div>
      )}

      {filteredIntegrations.length > 0 && (
        <div className="flex-1 overflow-y-auto">
          {/* Group integrations by type */}
          {(() => {
            // Get unique groups
            const groups = Array.from(new Set(
              filteredIntegrations.map(i => getGroupFromIntegration(i))
            )).sort();
            
            return groups.map(group => {
              // Get integrations for this group
              const groupIntegrations = filteredIntegrations.filter(
                i => getGroupFromIntegration(i) === group
              );
              
              if (groupIntegrations.length === 0) return null;
              
              return (
                <div key={group} className="mb-6 bg-gradient-to-r from-cw-bg-top/70 to-cw-bg-bottom/70 backdrop-blur-sm rounded-xl overflow-hidden border border-cw-n-blue/30 shadow-cw-glow-sm">
                  <div className="px-6 py-3 bg-gradient-to-r from-cw-accent-left/10 to-cw-accent-right/10 border-b border-cw-n-blue/30">
                    <h3 className="text-lg font-semibold text-cw-foreground">
                      {group} APIs ({groupIntegrations.length})
                    </h3>
                  </div>
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-cw-n-blue/20 table-fixed">
                      <thead className="bg-cw-bg-top/30">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-cw-foreground/80 uppercase tracking-wider w-1/4">Name</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-cw-foreground/80 uppercase tracking-wider w-1/4">Summary</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-cw-foreground/80 uppercase tracking-wider w-1/6">Tags</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-cw-foreground/80 uppercase tracking-wider w-1/12">Credentials</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-cw-foreground/80 uppercase tracking-wider w-1/10">Last Updated</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-cw-foreground/80 uppercase tracking-wider w-1/6">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-transparent divide-y divide-cw-n-blue/10">
                        {groupIntegrations.map((integration) => (
                          <tr 
                            key={integration.id} 
                            className="hover:bg-cw-accent-left/5 transition-colors cursor-pointer group"
                            onClick={() => router.push(`/admin/integrations/${integration.id}`)}
                          >
                            <td className="px-6 py-4 text-sm font-medium">
                              <div className="text-cw-foreground truncate max-w-[200px]" title={integration.name}>
                                {integration.name}
                              </div>
                              {integration.spec_format && (
                                <div className="mt-1">
                                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-mono bg-cw-n-blue/20 text-cw-n-blue border border-cw-n-blue/40">
                                    {integration.spec_format.toUpperCase()}
                                  </span>
                                </div>
                              )}
                            </td>
                            <td className="px-6 py-4 text-sm text-cw-foreground/80 overflow-hidden">
                              <div className="truncate max-w-[250px]" title={integration.summary || integration.description || ''}>
                                {integration.summary || integration.description || '-'}
                              </div>
                            </td>
                            <td className="px-6 py-4 text-sm">
                              {integration.tags && integration.tags.length > 0 ? (
                                <div className="flex flex-wrap gap-1">
                                  {integration.tags.slice(0, 2).map((tag, index) => (
                                    <span
                                      key={index}
                                      className="px-2 py-0.5 bg-cw-accent-right/20 text-cw-accent-right border border-cw-accent-right/40 text-xs font-mono rounded-full"
                                    >
                                      {tag}
                                    </span>
                                  ))}
                                  {integration.tags.length > 2 && (
                                    <span className="text-cw-foreground/60 text-xs">+{integration.tags.length - 2}</span>
                                  )}
                                </div>
                              ) : (
                                <span className="text-cw-foreground/60">-</span>
                              )}
                            </td>
                            <td className="px-6 py-4 text-sm">
                              <span 
                                className={`px-2.5 py-0.5 text-xs font-medium rounded-full border ${
                                  integration.has_credentials 
                                    ? 'bg-cw-n-green/20 text-cw-n-green border-cw-n-green/40' 
                                    : 'bg-cw-n-white/20 text-cw-n-white border-cw-n-white/40'
                                }`}
                              >
                                {integration.has_credentials ? 'Configured' : 'Not Set'}
                              </span>
                            </td>
                            <td className="px-6 py-4 text-sm text-cw-foreground/70">
                              {new Date(integration.updated_at).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 text-sm font-medium">
                              <div className="flex justify-end space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <CyberButton 
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    router.push(`/admin/integrations/${integration.id}`);
                                  }}
                                  className="text-cw-n-blue hover:text-cw-n-blue/80 text-xs !px-1.5 !py-0.5 bg-transparent hover:bg-cw-n-blue/10 border-none"
                                >
                                  View
                                </CyberButton>
                                {canDeleteIntegration && (
                                  <CyberButton 
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDelete(integration);
                                    }}
                                    className="text-cw-n-red hover:text-cw-n-red/80 text-xs !px-1.5 !py-0.5 bg-transparent hover:bg-cw-n-red/10 border-none"
                                  >
                                    Delete
                                  </CyberButton>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              );
            });
          })()}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="bg-gradient-to-r from-cw-bg-top to-cw-bg-bottom border border-cw-n-blue/30">
          <DialogHeader>
            <DialogTitle className="text-cw-foreground">Delete Integration</DialogTitle>
            <DialogDescription className="text-cw-foreground/80">
              Are you sure you want to delete the integration <span className="font-semibold text-cw-accent-left">{integrationToDelete?.name}</span>? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <CyberButton 
              variant="outline" 
              onClick={() => setDeleteDialogOpen(false)} 
              disabled={isDeleting}
              className="border-cw-n-white/30 text-cw-n-white"
            >
              Cancel
            </CyberButton>
            <CyberButton 
              variant="destructive" 
              onClick={confirmDelete} 
              disabled={isDeleting}
              className="bg-gradient-to-r from-cw-n-red/80 to-cw-n-red text-white"
            >
              {isDeleting ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash className="mr-2 h-4 w-4" />
                  Delete
                </>
              )}
            </CyberButton>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default IntegrationsListPage;
