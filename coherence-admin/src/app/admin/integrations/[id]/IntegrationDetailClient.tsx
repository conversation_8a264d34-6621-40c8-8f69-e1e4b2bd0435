'use client';

import React, { useState, useRef } from 'react';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useHasPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path
import apiClient, { deleteResource } from '@/lib/apiClient';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeft, RefreshCcw, Save, PlayCircle, Trash, Edit, X } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import dynamic from 'next/dynamic';
import { DataContainer } from '@/components/auth/DataContainer';
import { SchemaViewer } from '@/components/integrations/SchemaViewer';
import { EndpointCard } from '@/components/integrations/EndpointCard';
import { ParameterDetails } from '@/components/integrations/ParameterDetails';
import { CredentialsForm } from '@/components/integrations/CredentialsForm';
import { EndpointSelector } from '@/components/integrations/EndpointSelector';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Use dynamic import with no SSR to prevent hydration issues
const ActionTestConsole = dynamic(
  () => import('@/components/actions/ActionTestConsole'),
  { ssr: false }
);

// Based on APIAuthConfig (from backend models)
interface ApiAuthConfig {
  integration_id: string;
  auth_type: 'api_key' | 'oauth2' | 'bearer' | 'basic' | 'custom';
  credentials?: Record<string, any> | null; // Sensitive, handle with care
  scopes?: string[] | null;
  expires_at?: string | null; // datetime
  // refresh_token is intentionally omitted as it's sensitive
}

// Based on APIRateLimit (from backend models)
interface ApiRateLimit {
  id: string;
  endpoint_id: string;
  requests_per_min: number;
  burst_size: number;
  cooldown_sec: number;
}

// Based on APIEndpoint (from backend models)
interface ApiEndpointExtended {
  id: string;
  integration_id: string;
  path: string;
  method: string;
  operation_id?: string | null;
  action_class_name?: string | null;
  intent_id?: string | null;
  enabled: boolean;
  rate_limits: ApiRateLimit[];
  summary?: string; // Keeping summary from original spec_summary for convenience
  description?: string; // Full description of the endpoint from the OpenAPI spec
  tags?: string[]; // Tags associated with this endpoint from the OpenAPI spec
  deprecated?: boolean; // Whether this endpoint is marked as deprecated
  openapi_snippet?: Record<string, any>; // Full OpenAPI operation object
}

// Main interface based on APIIntegration, incorporating nested structures
interface ApiIntegrationExtended {
  id: string;
  tenant_id: string;
  name: string;
  version?: string | null;
  openapi_spec: Record<string, any>; // Full OpenAPI spec
  base_url?: string | null;
  created_at: string;
  updated_at: string;
  status: 'draft' | 'active' | 'disabled';
  auth_config?: ApiAuthConfig | null;
  endpoints: ApiEndpointExtended[];
  has_credentials?: boolean; // Added to match the integration list endpoint
  spec_format: string; // Format of the API specification (openapi, fapi, bapi)
  original_format?: string | null; // Original format if converted from another format

  // Fields from original IntegrationDetails that might still be relevant or part of a summary
  description?: string | null;
  api_type: string; // This might be derived or a separate field from backend.
}


const IntegrationDetailClient = () => {
  const params = useParams();
  const id = params.id as string;
  const [activeTab, setActiveTab] = useState('details');
  const [generatingTemplates, setGeneratingTemplates] = useState(false);
  const [generatingIntents, setGeneratingIntents] = useState(false);
  const [selectedEndpoints, setSelectedEndpoints] = useState<string[]>([]);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedEndpointId, setSelectedEndpointId] = useState<string>('');
  const [isLoadingParameters, setIsLoadingParameters] = useState(false);
  
  // Edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [editBaseUrl, setEditBaseUrl] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  
  // Action Test Console state
  const [selectedActionConfig, setSelectedActionConfig] = useState<any>({
    method: "GET",
    endpoint: "/api/example",
    integration_id: "",
    api_key: "",
    parameter_mapping: {
      "query": "{{parameters.query}}",
      "limit": "{{parameters.limit|default(10)}}"
    },
    authentication: {
      type: "api_key",
      name: "Authorization",
      location: "header",
      value: "{{credentials.api_key}}"
    }
  });
  const [selectedParameters, setSelectedParameters] = useState<any[]>([]);
  // Define typings for API responses
  interface TemplateResponse {
    id: string;
    key: string;
    category: string;
    body: string;
    created_at: string;
  }
  
  interface IntentResponse {
    id: string;
    name: string;
    description: string;
    examples: string[];
    created_at: string;
  }
  
  const [generationResults, setGenerationResults] = useState<{
    templates?: TemplateResponse[],
    intents?: IntentResponse[],
    error?: string
  } | null>(null);
  const adminSession = useAdminSession();
  const router = useRouter();
  
  // Function to fetch integration data
  const fetchIntegrationData = async (token: string, orgId?: string) => {
    const data = await apiClient<ApiIntegrationExtended>(`/v1/admin/integrations/${id}`, {
      token: token,
      orgId: orgId,
    });
    
    // Initialize edit form fields with current values
    setEditName(data.name || '');
    setEditDescription(data.description || '');
    setEditBaseUrl(data.base_url || '');
    
    // Initialize the action config with current integration data
    setSelectedActionConfig((prev: any) => ({
      ...prev,
      integration_id: data.id,
      api_key: data.name,
      authentication: {
        ...prev.authentication,
        type: data.auth_config?.auth_type || 'api_key'
      }
    }));
    
    return data;
  };
  
  // Function to handle selection of all endpoints
  const handleSelectAll = (isSelected: boolean, integration: ApiIntegrationExtended) => {
    if (isSelected) {
      setSelectedEndpoints(integration?.endpoints?.map(ep => ep.id) || []);
    } else {
      setSelectedEndpoints([]);
    }
  };
  
  // Function to handle individual endpoint selection
  const handleEndpointSelection = (endpointId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedEndpoints(prev => [...prev, endpointId]);
    } else {
      setSelectedEndpoints(prev => prev.filter(id => id !== endpointId));
    }
  };

  // Function to handle toggling endpoint enabled state
  const handleEndpointToggle = async (endpointId: string, enabled: boolean) => {
    if (!adminSession.token) return;
    
    try {
      await apiClient(`/v1/openapi/endpoints/${endpointId}/toggle`, {
        method: 'PUT',
        token: adminSession.token,
        orgId: adminSession.tenant?.id,
        body: JSON.stringify({ enabled }),
      });
      
      // DataContainer will handle refreshing the data
      alert(`Endpoint ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('Error toggling endpoint:', error);
      alert(`Failed to ${enabled ? 'enable' : 'disable'} endpoint`);
    }
  };

  // Handle integration deletion
  const handleDeleteIntegration = async (integration: ApiIntegrationExtended) => {
    if (!adminSession.token) return;
    
    try {
      setIsDeleting(true);
      await deleteResource(`/v1/admin/integrations/${id}`, { 
        token: adminSession.token,
        orgId: adminSession.tenant?.id
      });
      
      // After successful deletion, navigate back to integrations page
      alert(`Integration "${integration.name}" has been successfully deleted`);
      router.push('/admin/integrations');
    } catch (error) {
      console.error("Failed to delete integration:", error);
      setIsDeleting(false);
      alert(`Failed to delete integration: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setDeleteConfirmOpen(false);
    }
  };

  // Handle template generation
  const handleGenerateTemplates = async (integration: ApiIntegrationExtended) => {
    if (!adminSession.token) return;

    // Check if any endpoints are selected
    if (selectedEndpoints.length === 0) {
      alert('Please select at least one endpoint to generate templates for.');
      return;
    }

    try {
      setGeneratingTemplates(true);
      setGenerationResults(null);
      
      const templates: TemplateResponse[] = [];
      const errors: string[] = [];
      
      // Generate unified templates for each selected endpoint
      for (const endpointId of selectedEndpoints) {
        try {
          // Find the endpoint details
          const endpoint = integration.endpoints?.find(ep => ep.id === endpointId);
          if (!endpoint) {
            errors.push(`Endpoint ${endpointId} not found`);
            continue;
          }
          
          // Prepare the request for unified template generation
          const response = await apiClient<any>(`/v1/unified-templates/generate`, {
            method: 'POST',
            token: adminSession.token,
            orgId: adminSession.tenant?.id,
            body: JSON.stringify({
              operation: endpoint.openapi_snippet || {},
              path: endpoint.path,
              method: endpoint.method.toUpperCase(),
              base_url: integration.base_url || '',
              spec_info: {
                title: integration.openapi_spec?.info?.title || integration.name,
                version: integration.openapi_spec?.info?.version || '1.0.0',
                description: integration.openapi_spec?.info?.description || integration.description
              }
            }),
          });
          
          // Convert unified template response to match expected format
          if (response.data) {
            templates.push({
              id: response.data.id,
              key: response.data.key,
              category: response.data.category,
              created_at: response.data.created_at
            });
          }
        } catch (err) {
          console.error(`Error generating template for endpoint ${endpointId}:`, err);
          errors.push(`Failed to generate template for ${endpointId}: ${err instanceof Error ? err.message : 'Unknown error'}`);
        }
      }
      
      // Update state with the response data
      setGenerationResults({
        templates: templates,
        error: errors.length > 0 ? errors.join('\n') : undefined
      });
      
      // Show success message
      if (templates.length > 0) {
        alert(`Successfully generated ${templates.length} unified template(s)!`);
      } else if (errors.length > 0) {
        alert(`Failed to generate templates:\n${errors.join('\n')}`);
      }
    } catch (err) {
      console.error('Error generating templates:', err);
      let errorMessage = 'Failed to generate templates';
      
      if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      // Log detailed debugging information
      console.log('Template generation debug info:', {
        endpoint: '/v1/unified-templates/generate',
        hasToken: !!adminSession.token,
        tokenLength: adminSession.token ? adminSession.token.length : 0,
        tenantId: adminSession.tenant?.id,
        organizationName: adminSession.tenant?.name || 'Not selected',
        selectedEndpointsCount: selectedEndpoints.length,
        errorDetails: err instanceof Error ? err.stack : String(err)
      });
      
      setGenerationResults(prev => ({
        ...prev,
        error: errorMessage
      }));
      
      alert(`Error generating templates: ${errorMessage}`);
    } finally {
      setGeneratingTemplates(false);
    }
  };

  // Handle intent generation
  const handleGenerateIntents = async (integration: ApiIntegrationExtended) => {
    if (!adminSession.token) return;
    
    // Check if any endpoints are selected
    if (selectedEndpoints.length === 0) {
      alert('Please select at least one endpoint to generate intents for.');
      return;
    }
    
    try {
      setGeneratingIntents(true);
      setGenerationResults(prev => ({
        ...prev,
        error: undefined
      }));
      
      // Call the selective intent generation endpoint
      const response = await apiClient<{intents: IntentResponse[]}>(`/v1/openapi/intents/generate-selected`, {
        method: 'POST',
        token: adminSession.token,
        orgId: adminSession.tenant?.id,
        body: JSON.stringify({
          integration_id: id,
          endpoint_ids: selectedEndpoints,
        }),
      });
      
      // Update state with the response data
      setGenerationResults(prev => ({
        ...prev,
        intents: response.intents,
      }));
      
      // Show success message
      alert(`Successfully generated ${response.intents.length} intents for ${selectedEndpoints.length} endpoint(s)!`);
    } catch (err) {
      console.error('Error generating intents:', err);
      let errorMessage = 'Failed to generate intents';
      
      if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      // Log detailed debugging information
      console.log('Intent generation debug info:', {
        endpoint: '/v1/openapi/intents/generate-selected',
        hasToken: !!adminSession.token,
        tokenLength: adminSession.token ? adminSession.token.length : 0,
        tenantId: adminSession.tenant?.id,
        organizationName: adminSession.tenant?.name || 'Not selected',
        payloadSize: JSON.stringify({
          integration_id: id,
          endpoint_ids: selectedEndpoints,
        }).length,
        selectedEndpoints: selectedEndpoints.length,
        errorDetails: err instanceof Error ? err.stack : String(err)
      });
      
      setGenerationResults(prev => ({
        ...prev,
        error: errorMessage
      }));
      
      alert(`Error generating intents: ${errorMessage}`);
    } finally {
      setGeneratingIntents(false);
    }
  };


  const canReadIntegrations = useHasPermission('integration:read' as Permission);
  const canDeleteIntegration = useHasPermission('integration:delete' as Permission);
  const canEditIntegration = useHasPermission('integration:update' as Permission);
  const canManageCredentials = useHasPermission('integration:manage_credentials' as Permission);

  // Initial loading/auth check before attempting to use permissions
  if (adminSession.isLoading) {
    return (
      <div className="flex justify-center items-center h-48">
        <p className="text-muted-foreground">Loading session...</p>
      </div>
    );
  }
  if (!adminSession.isAuthenticated) {
    return (
      <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Authentication Required: </strong>
        <span className="block sm:inline">Please log in to access this page.</span>
      </div>
    );
  }

  if (!canReadIntegrations) {
    return (
      <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Access Denied: </strong>
        <span className="block sm:inline">You do not have permission to view integration details. (integration:read)</span>
      </div>
    );
  }

  // const canManageCredentials = Array.isArray(adminSession.permissions) &&
  //                             adminSession.permissions.includes('integration:manage_credentials');
  // Replaced by hook call above

  return (
    <div className="admin-page">
      <DataContainer
        fetchData={fetchIntegrationData}
        dependencies={[id]}
        showRefreshButton={false}
      >
      {(integration) => {
        if (!integration) {
          return (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">Integration not found</span>
            </div>
          );
        }
        
        return (
          <div>
            <div className="mb-6 flex justify-between items-center">
              <div className="flex space-x-2">
                <Button 
                  variant="ghost" 
                  onClick={() => router.push('/admin/integrations')}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" /> Back to Integrations
                </Button>
              </div>
              
              {canDeleteIntegration && (
                <Button 
                  variant="destructive" 
                  onClick={() => setDeleteConfirmOpen(true)}
                  className="admin-button-primary bg-red-600 hover:bg-red-700"
                >
                  <Trash className="mr-2 h-4 w-4" /> Delete Integration
                </Button>
              )}
            </div>

      <header className="admin-header">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-cw-foreground">{integration.name}</h1>
            {integration.description && (
              <p className="text-cw-foreground/80 mt-2">{integration.description}</p>
            )}
            <div className="flex gap-2 mt-3">
              {integration.spec_format && (
                <span className="admin-badge admin-badge-blue text-sm font-mono">
                  {integration.spec_format.toUpperCase()}
                </span>
              )}
              <span className={`admin-badge text-sm font-medium ${
                integration.status === 'active' 
                  ? 'admin-badge-green' 
                  : integration.status === 'disabled'
                  ? 'admin-badge-red'
                  : 'admin-badge-yellow'
              }`}>
                {integration.status.toUpperCase()}
              </span>
              {integration.has_credentials && (
                <span className="admin-badge admin-badge-purple text-sm font-medium">
                  Credentials Configured
                </span>
              )}
            </div>
          </div>
        </div>
      </header>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Integration</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the integration <span className="font-semibold">{integration.name}</span>? This action cannot be undone and will remove all associated endpoints, templates, and connections.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setDeleteConfirmOpen(false)} 
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => handleDeleteIntegration(integration)} 
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <RefreshCcw className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash className="mr-2 h-4 w-4" />
                  Delete Integration
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="px-6 md:px-8">
        <Tabs 
          defaultValue="details" 
          className="w-full" 
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="admin-tabs-list">
            <TabsTrigger value="details">
              Details
            </TabsTrigger>
            {canManageCredentials && (
              <TabsTrigger value="credentials">
                Credentials
              </TabsTrigger>
            )}
            <TabsTrigger value="endpoints">
              Endpoints
            </TabsTrigger>
            <TabsTrigger value="generate">
              Generate
            </TabsTrigger>
            {canManageCredentials && (
              <TabsTrigger value="test">
                <PlayCircle className="mr-1 h-4 w-4" />
                Test
              </TabsTrigger>
            )}
          </TabsList>
        
        <TabsContent value="details">
          <div className="admin-card">
          {/* Edit mode toggle button */}
          {canEditIntegration && (
            <div className="flex justify-end mb-4">
              <Button 
                variant={isEditMode ? "destructive" : "outline"}
                onClick={() => {
                  if (isEditMode) {
                    // Reset form values if cancelling
                    setEditName(integration.name || '');
                    setEditDescription(integration.description || '');
                    setEditBaseUrl(integration.base_url || '');
                  }
                  setIsEditMode(!isEditMode);
                }}
                className="text-sm"
              >
                {isEditMode ? (
                  <>
                    <X className="mr-2 h-4 w-4" /> Cancel Editing
                  </>
                ) : (
                  <>
                    <Edit className="mr-2 h-4 w-4" /> Edit Integration
                  </>
                )}
              </Button>
            </div>
          )}
          
          {isEditMode ? (
            <div className="space-y-4 mb-6">
              <div>
                <Label htmlFor="integration-name">Integration Name</Label>
                <Input
                  id="integration-name"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  placeholder="Integration Name"
                  className="w-full mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="integration-description">Description</Label>
                <Textarea
                  id="integration-description"
                  value={editDescription}
                  onChange={(e) => setEditDescription(e.target.value)}
                  placeholder="Integration Description"
                  className="w-full mt-1"
                  rows={3}
                />
              </div>
              
              <div>
                <Label htmlFor="integration-base-url">Base URL</Label>
                <Input
                  id="integration-base-url"
                  value={editBaseUrl}
                  onChange={(e) => setEditBaseUrl(e.target.value)}
                  placeholder="https://api.example.com"
                  className="w-full mt-1"
                />
              </div>
              
              <Button
                onClick={async () => {
                  if (!integration || !adminSession.token) return;
                  
                  try {
                    setIsSaving(true);
                    
                    await apiClient(`/v1/openapi/integrations/${id}`, {
                      method: 'PUT',
                      token: adminSession.token,
                      orgId: adminSession.tenant?.id,
                      body: JSON.stringify({
                        name: editName,
                        description: editDescription,
                        base_url: editBaseUrl
                      }),
                    });
                    
                    // Update the local state
                    // Refetch integration data to ensure all fields are updated correctly
                    try {
                      // Fetch updated integration data, but we don't manually update state
                      // since we're using DataContainer which handles refreshing data
                      await apiClient<ApiIntegrationExtended>(`/v1/admin/integrations/${id}`, {
                        token: adminSession.token,
                      });
                      // Let DataContainer handle refreshing the data
                    } catch (refreshError) {
                      console.error('Error refreshing integration after update:', refreshError);
                      // No manual update needed, DataContainer will fetch the latest data
                    }
                    
                    setIsEditMode(false);
                    alert('Integration updated successfully');
                  } catch (err) {
                    console.error('Error updating integration:', err);
                    alert(`Failed to update integration: ${err instanceof Error ? err.message : 'Unknown error'}`);
                  } finally {
                    setIsSaving(false);
                  }
                }}
                disabled={isSaving}
                className="mt-2"
              >
                {isSaving ? (
                  <>
                    <RefreshCcw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-foreground mb-1">Integration ID</h3>
                  <p className="text-sm font-mono text-muted-foreground break-all">{integration.id}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-foreground mb-1">API Type</h3>
                  <p className="capitalize text-muted-foreground">{integration.api_type || 'Not specified'}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-foreground mb-1">Created</h3>
                  <p className="text-muted-foreground">{new Date(integration.created_at).toLocaleString()}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-foreground mb-1">Last Updated</h3>
                  <p className="text-muted-foreground">{new Date(integration.updated_at).toLocaleString()}</p>
                </div>
                {integration.version && (
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Integration Version</h3>
                    <p className="text-muted-foreground">{integration.version}</p>
                  </div>
                )}
                {integration.openapi_spec?.info?.title && (
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">API Title (from Spec)</h3>
                    <p className="text-muted-foreground">{integration.openapi_spec.info.title}</p>
                  </div>
                )}
                {integration.status && (
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Status</h3>
                    <p className="capitalize text-muted-foreground">{integration.status}</p>
                  </div>
                )}
                {integration.spec_format && (
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Specification Format</h3>
                    <div className="flex items-center">
                      <span className="admin-badge admin-badge-blue mr-2">
                        {integration.spec_format.toUpperCase()}
                      </span>
                      {integration.original_format && integration.original_format !== integration.spec_format && (
                        <span className="text-sm text-muted-foreground">
                          (Converted from {integration.original_format.toUpperCase()})
                        </span>
                      )}
                    </div>
                  </div>
                )}
                {integration.base_url && (
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">Base URL</h3>
                    <p className="font-mono text-muted-foreground break-all">{integration.base_url}</p>
                  </div>
                )}
                {integration.openapi_spec && (
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">OpenAPI Specification</h3>
                    <div className="flex gap-2">
                      <SchemaViewer 
                        openApiSpec={integration.openapi_spec}
                        integrationName={integration.name}
                      />
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => {
                          const blob = new Blob([JSON.stringify(integration.openapi_spec, null, 2)], { type: 'application/json' });
                          const url = URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = `${integration.name}-openapi-spec.json`;
                          a.click();
                          URL.revokeObjectURL(url);
                        }}
                      >
                        Download Spec
                      </Button>
                    </div>
                  </div>
                )}
              </div>
              
              {integration.openapi_spec?.info?.description && (
                <div className="mt-6 p-4 rounded-lg bg-muted border border-border">
                  <h3 className="font-semibold text-foreground mb-2">API Description (from Spec)</h3>
                  <p className="text-sm text-muted-foreground">{integration.openapi_spec.info.description}</p>
                </div>
              )}
            </>
          )}
          </div>
        </TabsContent>

        {canManageCredentials && (
          <TabsContent value="credentials">
            <div className="admin-card">
              <h2 className="text-xl font-bold mb-6">Authentication Configuration</h2>
              
              <CredentialsForm
                integrationId={integration.id}
                integrationName={integration.name}
                authConfig={integration.auth_config}
                openApiSpec={integration.openapi_spec}
                token={adminSession.token || ''}
                orgId={adminSession.tenant?.id}
                onSaveSuccess={() => {
                  // Refresh the integration data to show updated credentials
                  window.location.reload();
                }}
              />
            </div>
          </TabsContent>
        )}

        <TabsContent value="endpoints">
          <div className="admin-card">
            <h2 className="text-xl font-bold mb-4">API Endpoints</h2>
          
          {integration.endpoints && integration.endpoints.length > 0 ? (
            <div className="space-y-4">
              {integration.endpoints.map((endpoint) => (
                <EndpointCard
                  key={endpoint.id}
                  endpoint={endpoint}
                  integrationName={integration.name}
                  apiType={integration.api_type}
                  onToggle={handleEndpointToggle}
                  showEnhancedDetails={true}
                  token={adminSession.token}
                  orgId={adminSession.tenant?.id}
                />
              ))}
            </div>
          ) : (
            <p className="text-gray-500 italic">No endpoints available for this integration.</p>
          )}
          </div>
        </TabsContent>
        
        <TabsContent value="generate">
          <div className="admin-card">
            <h2 className="text-xl font-bold mb-4">Generate Templates & Intents</h2>
          
          {/* Endpoint selection using EndpointSelector component */}
          {integration.endpoints && integration.endpoints.length > 0 ? (
            <div className="mb-6">
              <EndpointSelector
                endpoints={integration.endpoints}
                selectedEndpoints={selectedEndpoints}
                onSelectionChange={handleEndpointSelection}
                onSelectAll={(isSelected) => handleSelectAll(isSelected, integration)}
                token={adminSession.token}
                orgId={adminSession.tenant?.id}
              />
            </div>
          ) : (
            <p className="text-gray-500 italic">No endpoints available for this integration.</p>
          )}
          
          {integration.endpoints && integration.endpoints.length > 0 && (
            <div className="space-y-6">
              <div className="admin-card-secondary">
                <h3 className="text-lg font-semibold mb-3">Generate for Selected Endpoints</h3>
                <div className="flex flex-col md:flex-row gap-3 mt-4">
                  <Button 
                    onClick={() => handleGenerateTemplates(integration)}
                    disabled={generatingTemplates || selectedEndpoints.length === 0}
                    className="flex-1 bg-primary text-primary-foreground hover:bg-primary/90"
                  >
                    {generatingTemplates ? (
                      <span className="flex items-center">
                        <RefreshCcw className="mr-2 h-4 w-4 animate-spin" />
                        Generating Templates...
                      </span>
                    ) : (
                      <span>Generate Templates</span>
                    )}
                  </Button>
                  <Button 
                    onClick={() => handleGenerateIntents(integration)}
                    disabled={generatingIntents || selectedEndpoints.length === 0}
                    className="flex-1"
                    variant="outline"
                  >
                    {generatingIntents ? (
                      <span className="flex items-center">
                        <RefreshCcw className="mr-2 h-4 w-4 animate-spin" />
                        Generating Intents...
                      </span>
                    ) : (
                      <span>Generate Intents</span>
                    )}
                  </Button>
                </div>
              </div>
              
              {/* Results display */}
              {generationResults && (
                <div className="admin-card-secondary">
                  <h3 className="text-lg font-semibold mb-4">Generation Results</h3>
                  
                  {generationResults.error && (
                    <div className="admin-alert-error">
                      <p className="admin-alert-error-text">{generationResults.error}</p>
                    </div>
                  )}
                  
                  {generationResults.templates && generationResults.templates.length > 0 && (
                    <div>
                      <h4 className="text-md font-medium mb-2">Templates Generated <span className="admin-badge admin-badge-blue">{generationResults.templates.length}</span></h4>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Key</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Created</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {generationResults.templates.map((template) => (
                            <TableRow key={template.id}>
                              <TableCell className="font-medium">{template.key}</TableCell>
                              <TableCell>
                                <span className={`admin-badge ${
                                  template.category === 'unified' ? 'admin-badge-green' :
                                  template.category === 'action' ? 'admin-badge-purple' :
                                  template.category === 'response_gen' ? 'admin-badge-blue' :
                                  template.category === 'error_handler' ? 'admin-badge-red' :
                                  'admin-badge-gray'
                                }`}>
                                  {template.category}
                                </span>
                              </TableCell>
                              <TableCell className="text-muted-foreground">{new Date(template.created_at).toLocaleString()}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                  
                  {generationResults.intents && generationResults.intents.length > 0 && (
                    <div className="mt-6">
                      <h4 className="text-md font-medium mb-2">Intents Generated <span className="admin-badge admin-badge-blue">{generationResults.intents.length}</span></h4>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead>Created</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {generationResults.intents.map((intent) => (
                            <TableRow key={intent.id}>
                              <TableCell className="font-medium">{intent.name}</TableCell>
                              <TableCell className="truncate max-w-md">{intent.description}</TableCell>
                              <TableCell className="text-muted-foreground">{new Date(intent.created_at).toLocaleString()}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
          </div>
        </TabsContent>

        {canManageCredentials && (
          <TabsContent value="test">
            <div className="admin-card">
              <h2 className="text-xl font-bold mb-4">API Action Testing</h2>

            {integration.endpoints && integration.endpoints.length > 0 ? (
              <div className="space-y-6">
                <div className="mb-4">
                  <p className="text-muted-foreground p-3 border border-border rounded-md bg-muted">
                    This console allows you to test API actions against real endpoints. Select an endpoint to test,
                    configure parameters, and execute the request to see the response.
                  </p>
                </div>

                {/* Endpoint Selection dropdown */}
                <div className="mb-6">
                  <label className="block text-sm font-medium mb-2">
                    Select Endpoint to Test
                  </label>
                  <select 
                    className="w-full p-2 border border-border rounded-md bg-background"
                    value={selectedEndpointId}
                    onChange={async (e) => {
                      const endpointId = e.target.value;
                      setSelectedEndpointId(endpointId);
                      
                      if (!endpointId) {
                        // Reset if no endpoint selected
                        setSelectedActionConfig(null);
                        setSelectedParameters([]);
                        setIsLoadingParameters(false);
                        return;
                      }
                      
                      const selectedEndpoint = integration.endpoints.find(ep => ep.id === endpointId);
                      if (selectedEndpoint) {
                        setIsLoadingParameters(true);
                        // Create a default action config based on the selected endpoint
                        const actionConfig = {
                          method: selectedEndpoint.method,
                          endpoint: selectedEndpoint.path,
                          integration_id: integration.id,
                          api_key: integration.name,
                          parameter_mapping: {},
                          authentication: {
                            type: integration.auth_config?.auth_type || 'api_key',
                            name: 'Authorization',
                            location: 'header',
                            value: '{{credentials.api_key}}'
                          }
                        };
                        
                        // For test panel, we need to set up parameter mappings with Jinja2-style templates
                        // These will be processed by DynamicActionExecutor._map_parameters
                        const parameterMapping: Record<string, string> = {};
                        
                        // Add parameters from the OpenAPI spec
                        if (selectedEndpoint.openapi_snippet && selectedEndpoint.openapi_snippet.parameters) {
                          selectedEndpoint.openapi_snippet.parameters.forEach((param: any) => {
                            if (param.name) {
                              // Create a template that gets the value from the parameters object
                              parameterMapping[param.name as string] = `{{parameters.${param.name}}}`;
                            }
                          });
                        }
                        
                        // Add parameters from request body if it's a POST/PUT/PATCH
                        if (selectedEndpoint.method.toUpperCase() !== 'GET' && 
                            selectedEndpoint.openapi_snippet && 
                            selectedEndpoint.openapi_snippet.requestBody && 
                            selectedEndpoint.openapi_snippet.requestBody.content && 
                            selectedEndpoint.openapi_snippet.requestBody.content['application/json'] && 
                            selectedEndpoint.openapi_snippet.requestBody.content['application/json'].schema &&
                            selectedEndpoint.openapi_snippet.requestBody.content['application/json'].schema.properties) {
                          
                          const bodyProperties = selectedEndpoint.openapi_snippet.requestBody.content['application/json'].schema.properties;
                          Object.keys(bodyProperties).forEach(propName => {
                            // Create a template that gets the value from the parameters object
                            parameterMapping[propName as string] = `{{parameters.${propName}}}`;
                          });
                        }
                        
                        // Apply the parameter mappings to the action config
                        actionConfig.parameter_mapping = parameterMapping;
                        
                        // Fetch enhanced parameters from the new endpoint
                        const fetchEnhancedParameters = async () => {
                          try {
                            const enhancedData = await apiClient(`/v1/openapi/endpoints/${endpointId}/enhanced-parameters`, {
                              token: adminSession.token,
                              orgId: adminSession.tenant?.id
                            });
                            
                            // Convert enhanced parameters to the format expected by ActionTestConsole
                            const parameters = enhancedData.enhanced_parameters.map((param: any) => ({
                              name: param.name,
                              type: param.type,
                              description: param.description,
                              required: param.required,
                              schema: {
                                type: param.type,
                                enum: param.enum,
                                format: param.format,
                                pattern: param.pattern,
                                minimum: param.minimum,
                                maximum: param.maximum,
                                minLength: param.min_length,
                                maxLength: param.max_length,
                                default: param.default,
                                example: param.example,
                                nullable: param.nullable
                              }
                            }));
                            
                            console.log('Enhanced parameters loaded:', parameters.length, 'parameters with constraints');
                            return parameters;
                          } catch (error) {
                            console.warn('Failed to load enhanced parameters, falling back to basic extraction:', error);
                            console.error('Enhanced parameters API error details:', {
                              endpoint: `/v1/openapi/endpoints/${endpointId}/enhanced-parameters`,
                              error: error instanceof Error ? error.message : error
                            });
                            
                            // Fallback to basic parameter extraction
                            const parameters: Array<{
                              name: string;
                              type: string;
                              description?: string;
                              required: boolean;
                              schema?: any;
                            }> = [];
                            
                            // Extract path/query/header parameters
                            if (selectedEndpoint.openapi_snippet?.parameters) {
                              selectedEndpoint.openapi_snippet.parameters.forEach((param: any) => {
                                const schema = param.schema || {};
                                parameters.push({
                                  name: param.name,
                                  type: schema.type || 'string',
                                  description: param.description,
                                  required: param.required || (param.in === 'path'),
                                  schema: schema
                                });
                              });
                            }
                            
                            // Extract request body parameters for POST/PUT/PATCH
                            if (selectedEndpoint.method.toUpperCase() !== 'GET' && 
                                selectedEndpoint.openapi_snippet?.requestBody?.content?.['application/json']?.schema?.properties) {
                              
                              const bodySchema = selectedEndpoint.openapi_snippet.requestBody.content['application/json'].schema;
                              const bodyProperties = bodySchema.properties;
                              const requiredProps = bodySchema.required || [];
                              
                              Object.entries(bodyProperties).forEach(([propName, propSchema]: [string, any]) => {
                                parameters.push({
                                  name: propName,
                                  type: propSchema.type || 'string',
                                  description: propSchema.description || `Request body property: ${propName}`,
                                  required: requiredProps.includes(propName),
                                  schema: propSchema
                                });
                              });
                            }
                            
                            console.log('Fallback extraction found', parameters.length, 'parameters');
                            return parameters;
                          }
                        };
                        
                        // Load enhanced parameters
                        const parameters = await fetchEnhancedParameters();
                        
                        console.log('Selected endpoint:', selectedEndpoint.method, selectedEndpoint.path, 'with', parameters.length, 'parameters');
                        
                        // Update the state to pass to ActionTestConsole
                        setSelectedActionConfig(actionConfig);
                        setSelectedParameters(parameters);
                        setIsLoadingParameters(false);
                        
                        // Switch to test tab
                        setActiveTab('test');
                      }
                    }}
                  >
                    <option value="">-- Select an endpoint --</option>
                    {integration.endpoints.map(endpoint => (
                      <option key={endpoint.id} value={endpoint.id}>
                        {endpoint.method} {endpoint.path} {endpoint.description ? `- ${endpoint.description}` : ''}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Action Test Console */}
                {isLoadingParameters ? (
                  <div className="p-8 text-center">
                    <RefreshCcw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                    <p className="text-muted-foreground">Loading endpoint parameters...</p>
                  </div>
                ) : selectedActionConfig && selectedEndpointId ? (
                <ActionTestConsole
                  actionConfig={selectedActionConfig || {
                    method: "GET",
                    endpoint: "/api/example",
                    integration_id: integration.id,
                    api_key: integration.name,
                    parameter_mapping: {},
                    authentication: {
                      type: integration.auth_config?.auth_type || 'api_key',
                      name: 'Authorization',
                      location: 'header',
                      value: '{{credentials.api_key}}'
                    }
                  }}
                  integrationId={integration.id}
                  token={adminSession.token || ''}
                  orgId={adminSession.tenant?.id || ''}
                  parameters={selectedParameters}
                  key={selectedActionConfig ? `${selectedActionConfig.method}-${selectedActionConfig.endpoint}` : 'default-console'}
                />
                ) : (
                  <div className="p-4 border border-dashed border-border rounded-md">
                    <p className="text-muted-foreground text-center">
                      Please select an endpoint above to start testing
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500 italic">No endpoints available for testing.</p>
            )}
            </div>
          </TabsContent>
        )}
      </Tabs>
      </div>
            </div>
        )
      }}
    </DataContainer>
    </div>
  );
};

export default IntegrationDetailClient;