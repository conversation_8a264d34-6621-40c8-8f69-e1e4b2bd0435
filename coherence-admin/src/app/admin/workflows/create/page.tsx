'use client';

import React from 'react';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useHasPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path
import WorkflowForm, { WorkflowFormValues } from '@/components/admin/workflows/WorkflowForm';

const CreateWorkflowPage = () => {
  const adminSession = useAdminSession();
  // const [hasPermission, setHasPermission] = React.useState<boolean | null>(null); // Replaced by hook
  const [isSubmitting, setIsSubmitting] = React.useState<boolean>(false);
  const [submitError, setSubmitError] = React.useState<string | null>(null);

  const canCreateWorkflow = useHasPermission('workflow:create' as Permission);

  // React.useEffect(() => { // This useEffect is no longer needed for permission setting
  //   if (adminSession.isLoading) {
  //     return; // Wait for session to load
  //   }
  //   if (!adminSession.isAuthenticated || !adminSession.permissions) {
  //     setHasPermission(false);
  //     return;
  //   }
  //   const canCreate = Array.isArray(adminSession.permissions) &&
  //                     adminSession.permissions.includes('workflow:create');
  //   setHasPermission(canCreate);
  // }, [adminSession.isLoading, adminSession.isAuthenticated, adminSession.permissions]);

  const handleFormSubmit = async (values: WorkflowFormValues) => {
    if (!adminSession.token) {
      setSubmitError("Authentication token not found. Please log in again.");
      return;
    }
    setIsSubmitting(true);
    setSubmitError(null);
    console.log("Form submitted values:", values);

    try {
      // TODO: Implement actual API call to POST /admin/workflows
      // const response = await apiClient.post('/admin/workflows', values, { token: adminSession.token });
      // console.log("API Response:", response);
      // Handle success (e.g., redirect to workflow list or show success message)
      alert("Workflow submitted (simulated). Check console for values.");
      // Example: router.push('/admin/workflows');
    } catch (error: unknown) {
      console.error("Failed to submit workflow:", error);
      if (error instanceof Error) {
        setSubmitError(error.message);
      } else if (typeof error === 'string') {
        setSubmitError(error);
      } else {
        setSubmitError("An unknown error occurred while submitting the workflow.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (adminSession.isLoading) { // Simplified loading check
    return (
      <div className="p-6 flex justify-center items-center min-h-[calc(100vh-theme(spacing.24))]">
        <p className="text-lg text-gray-600">Loading...</p>
      </div>
    );
  }

  if (!adminSession.isAuthenticated) {
    return (
     <div className="p-6">
       <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
         <strong className="font-bold">Authentication Required: </strong>
         <span className="block sm:inline">Please log in to create workflows.</span>
       </div>
     </div>
   );
 }

  if (!canCreateWorkflow) { // Use the hook result directly
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Access Denied: </strong>
          <span className="block sm:inline">You do not have permission to create workflows. (workflow:create)</span>
        </div>
      </div>
    );
  }
  
  if (adminSession.error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Session Error: </strong>
          <span className="block sm:inline">{adminSession.error.message}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <header className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Create New Workflow</h1>
      </header>
      <div className="bg-white shadow-lg rounded-xl p-6 md:p-8">
        {submitError && (
          <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Submission Error: </strong>
            <span className="block sm:inline">{submitError}</span>
          </div>
        )}
        <WorkflowForm onSubmit={handleFormSubmit} isLoading={isSubmitting} />
      </div>
    </div>
  );
};

export default CreateWorkflowPage; 