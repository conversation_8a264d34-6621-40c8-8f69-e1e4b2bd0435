'use client'; 

import React from 'react';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useHasPermission } from '@/lib/hooks/useHasPermission'; // Fixed import path
import type { Permission } from '@/lib/generated/permissions'; // Fixed import path
import apiClient from '@/lib/apiClient';
import { DataTable } from '@/components/ui/DataTable';
import type { ColumnDef, HeaderContext, CellContext } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Interface for a single workflow step
interface WorkflowStep {
  id: string; // UUID
  name: string;
  step_type: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  config: Record<string, any>;
  order: number;
}

// Interface for a single workflow
interface Workflow {
  id: string; // UUID
  tenant_id: string; // UUID
  name: string;
  description?: string | null;
  steps: WorkflowStep[];
  is_enabled: boolean;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

const columns: ColumnDef<Workflow>[] = [
  {
    accessorKey: "name",
    header: ({ column }: HeaderContext<Workflow, unknown>) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }: CellContext<Workflow, unknown>) => <div className="font-medium">{row.getValue("name")}</div>,
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }: CellContext<Workflow, unknown>) => {
      const description = row.getValue("description") as string;
      return <div className="truncate max-w-xs">{description || '-'}</div>;
    }
  },
  {
    accessorKey: "is_enabled",
    header: ({ column }: HeaderContext<Workflow, unknown>) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }: CellContext<Workflow, unknown>) => {
      const isEnabled = row.getValue("is_enabled");
      return (
        <span 
          className={`px-2 py-1 text-xs font-semibold rounded-full border ${isEnabled ? 'bg-cw-n-green/20 text-cw-n-green border-cw-n-green/40' : 'bg-cw-n-red/20 text-cw-n-red border-cw-n-red/40'}`}
        >
          {isEnabled ? 'Enabled' : 'Disabled'}
        </span>
      );
    }
  },
  {
    accessorKey: "updated_at",
    header: ({ column }: HeaderContext<Workflow, unknown>) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Last Updated
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }: CellContext<Workflow, unknown>) => {
      const date = new Date(row.getValue("updated_at"));
      return <div>{date.toLocaleString()}</div>;
    }
  },
  {
    id: "actions",
    cell: ({ row }: CellContext<Workflow, unknown>) => {
      const workflow = row.original;
      // Placeholder for actual actions
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(workflow.id)}
            >
              Copy Workflow ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View Details (NYI)</DropdownMenuItem>
            <DropdownMenuItem>Edit (NYI)</DropdownMenuItem>
            <DropdownMenuItem className="text-red-600 hover:!text-red-600 hover:!bg-red-50">Delete (NYI)</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }
  }
];

const WorkflowListPage = () => {
  const adminSession = useAdminSession();
  const [workflows, setWorkflows] = React.useState<Workflow[]>([]);
  const [loadingData, setLoadingData] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  // const [hasPermission, setHasPermission] = React.useState<boolean | null>(null); // Replaced by hook

  const canReadWorkflows = useHasPermission('workflow:read' as Permission);

  React.useEffect(() => {
    if (adminSession.isLoading) {
      setLoadingData(true);
      return;
    }

    if (!adminSession.isAuthenticated || !adminSession.token) { // Removed adminSession.permissions check here as hook handles it
      setError('User session not available or token missing. Please log in.');
      setLoadingData(false);
      // setHasPermission(false); // Not needed
      return;
    }

    // const canReadWorkflowsLocal = Array.isArray(adminSession.permissions) &&
    //                          adminSession.permissions.includes('workflow:read');
    // setHasPermission(canReadWorkflowsLocal); // Not needed, using hook directly

    if (!canReadWorkflows) {
      setLoadingData(false);
      setError('You do not have permission to view workflows.'); // Set error if no permission
      return;
    }

    // At this point, user is authenticated and has permission
    setLoadingData(true);
    apiClient<Workflow[]>('/admin/workflows', { token: adminSession.token })
      .then((responseData: Workflow[]) => {
        setWorkflows(responseData);
        setError(null);
      })
      .catch((apiError: unknown) => {
        console.error('API Error fetching workflows:', apiError);
        if (apiError instanceof Error) {
          setError(`Failed to load workflows: ${apiError.message}`);
        } else if (typeof apiError === 'string') {
          setError(`Failed to load workflows: ${apiError}`);
        } else {
          setError('Failed to load workflows. An unknown error occurred.');
        }
      })
      .finally(() => {
        setLoadingData(false);
      });
  }, [adminSession.isAuthenticated, adminSession.isLoading, adminSession.token, canReadWorkflows]);

  if (adminSession.isLoading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[calc(100vh-theme(spacing.24))]">
        <p className="text-lg text-gray-600">Loading session...</p>
      </div>
    );
  }
  
  if (adminSession.error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Session Error: </strong>
          <span className="block sm:inline">{adminSession.error.message}</span>
        </div>
      </div>
    );
  }

  if (!adminSession.isAuthenticated) {
    return (
     <div className="p-6">
       <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
         <strong className="font-bold">Authentication Required: </strong>
         <span className="block sm:inline">Please log in to access this page.</span>
       </div>
     </div>
   );
 }

 // Combined loading check
 if (adminSession.isLoading || (loadingData && canReadWorkflows && !error)) {
   return (
     <div className="p-6 flex justify-center items-center min-h-[calc(100vh-theme(spacing.24))]">
       <p className="text-lg text-gray-600">Loading workflows...</p>
     </div>
   );
 }
 
 // Error display (catches auth, permission, or data fetch errors)
 if (error) {
   return (
     <div className="p-6">
       <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
         <strong className="font-bold">Error: </strong>
         <span className="block sm:inline">{error}</span>
       </div>
     </div>
   );
 }

 // Explicit permission denied message if no other error and not loading
 if (!canReadWorkflows) {
   return (
       <div className="p-6">
           <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
               <strong className="font-bold">Access Denied: </strong>
               <span className="block sm:inline">You do not have permission to view workflows. (workflow:read)</span>
           </div>
       </div>
   );
 }

 // This specific error case might be redundant if useEffect sets error correctly
 // if (error && !adminSession.isLoading) {
 //   return (
 //     <div className="p-6">
 //       <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
 //         <strong className="font-bold">Page Error: </strong>
 //         <span className="block sm:inline">{error}</span>
 //       </div>
 //     </div>
 //   );
 // }

 return (
    <div className="p-6">
      <header className="mb-6 flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-800">Workflows</h1>
        {/* Placeholder for Create Workflow button - to be added based on 'workflow:create' permission */}
        {/* <Button>Create Workflow</Button> */}
      </header>
      <div className="bg-white shadow-lg rounded-xl p-6 md:p-8">
        <DataTable columns={columns} data={workflows} loading={loadingData} />
      </div>
    </div>
  );
};

export default WorkflowListPage; 