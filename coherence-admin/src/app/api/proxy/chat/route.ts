import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { cookies } from 'next/headers';

/**
 * Helper function to check if a user is a system admin
 */
async function isSystemAdmin(token: string, backendUrl: string): Promise<boolean> {
  try {
    // Fetch session info to check if the user is a system admin
    const sessionResponse = await fetch(`${backendUrl}/v1/auth/session-info`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
    });
    
    if (sessionResponse.ok) {
      const sessionData = await sessionResponse.json();
      
      // Check for is_system_admin flag or system:* permission
      const isSystemAdmin = 
        sessionData.is_system_admin === true || 
        (Array.isArray(sessionData.permissions) && sessionData.permissions.includes("system:*"));
      
      console.log("System admin check:", isSystemAdmin ? "User is a system admin" : "User is not a system admin");
      return isSystemAdmin;
    }
    
    console.error("Failed to retrieve session info for system admin check");
    return false;
  } catch (error) {
    console.error("Error checking system admin status:", error);
    return false;
  }
}

/**
 * Helper function to generate a consistent API key missing error response
 */
function getApiKeyMissingResponse(orgId?: string) {
  // For development only: Generate a key that can be used directly
  const devKey = orgId ? 
    "coh_dev_" + Buffer.from(`org_${orgId}_development_key_do_not_use_in_production`).toString('base64') : 
    "coh_dev_fallback_key";
  
  return NextResponse.json({
    kind: "fallback",
    text: "Chat requires an organization API key.",
    error: "Please create a new API key for your organization in the API Keys section and note the full key value. Then configure it in your environment.",
    error_code: "missing_api_key",
    details: `For development only: Set COHERENCE_API_KEY=${devKey} in your environment variables. Do not use this key in production.`,
    development_key: devKey,
  }, { status: 200 });
}

/**
 * Helper function to fetch an organization API key
 * This will try to get an active API key for the organization from the backend
 * If none is found, it will fall back to environment variables
 */
async function getOrganizationApiKey(token: string, orgId: string, backendUrl: string): Promise<string | null> {
  console.log("Fetching organization API key for orgId:", orgId);
  
  try {
    console.log(`Attempting to fetch API keys from: ${backendUrl}/v1/system/org-api-keys/${orgId}`);
    
    // Try to fetch the organization's active API keys
    const apiKeysResponse = await fetch(`${backendUrl}/v1/system/org-api-keys/${orgId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
    });
    
    if (apiKeysResponse.ok) {
      const apiKeys = await apiKeysResponse.json();
      console.log(`Found ${apiKeys.length} API keys for the organization`);
      
      // Look for an active API key (non-revoked)
      const activeKey = apiKeys.find((key: any) => !key.revoked);
      if (activeKey) {
        console.log("Found active API key to use:", {
          id: activeKey.id,
          name: activeKey.name,
          prefix: activeKey.key_prefix,
          revoked: activeKey.revoked
        });
        
        // Check if we have the actual key value (only available on creation)
        if (activeKey.key) {
          console.log("Using newly created key with complete value");
          return activeKey.key;
        } else {
          console.error(
            "IMPORTANT: Found existing API key in the database, but the complete key value is not available. " +
            "This is normal - for security reasons, the full key value is only shown once when created."
          );
          console.error(
            "SOLUTION: You need to create a new API key at /admin/api-keys and either:" +
            "\n1. Save the full value shown after creation in your environment as COHERENCE_API_KEY" +
            "\n2. OR pass the key directly to the chat component"
          );
          return null;
        }
      } else {
        console.error("No active (non-revoked) API keys found for this organization");
      }
    } else {
      const errorText = await apiKeysResponse.text();
      console.error(`Failed to fetch organization API keys. Status: ${apiKeysResponse.status}, Response:`, errorText);
      console.error(`Check if endpoint /v1/system/org-api-keys/${orgId} exists and is accessible`);
    }
  } catch (error) {
    console.error("Error fetching organization API keys:", error);
  }
  
  // Fallback to environment variables if no org API key found
  const envApiKey = process.env.COHERENCE_API_KEY || process.env.COHERENCE_MASTER_API_KEY;
  console.log("Using environment API key as fallback:", envApiKey ? "Found key" : "No key available");
  
  // If no key in environment, create a hardcoded fallback for development
  if (!envApiKey) {
    console.log("No API key in environment, using hardcoded development fallback");
    // This is a development-only fallback - in production, use environment variables or org API keys
    // Generate a stable key based on the organization ID so it's consistent across requests
    const fallbackKey = "coh_dev_" + Buffer.from(`org_${orgId}_development_key_do_not_use_in_production`).toString('base64');
    console.log("Created fallback API key:", fallbackKey);
    return fallbackKey;
  }
  
  return envApiKey;
}

/**
 * Proxy endpoint for chat-related API requests
 * This serves as a middleman between the frontend and the backend API
 * It forwards requests to the appropriate backend endpoint with the proper authentication
 */
export async function POST(request: NextRequest) {
  try {
    // Get authentication information from Clerk
    const { userId, getToken, orgId } = getAuth({ request });
    
    if (!userId || !orgId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the JSON body from the request
    const body = await request.json();
    
    // Ensure we have all required fields for the API
    if (!body.user_id) {
      body.user_id = userId;
    }
    
    if (!body.role) {
      body.role = 'user';
    }
    
    // Add tenant_id to the body
    body.tenant_id = orgId;
    
    console.log("Request body:", JSON.stringify(body, null, 2));
    
    // Determine which endpoint to call based on the presence of a conversation_id
    const endpoint = body.conversation_id ? "/v1/continue" : "/v1/resolve";
    
    // Get the token for authentication
    const token = await getToken();
    
    if (!token) {
      return NextResponse.json({ error: "Authentication token missing" }, { status: 401 });
    }

    // Get the backend URL from environment variables
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    
    // Check if user is a system admin
    const userIsSystemAdmin = await isSystemAdmin(token, backendUrl);
    
    // Only fetch and require API key if user is not a system admin
    let apiKey = null;
    if (!userIsSystemAdmin) {
      apiKey = await getOrganizationApiKey(token, orgId, backendUrl);
      
      if (!apiKey) {
        return getApiKeyMissingResponse(orgId);
      }
    }
    
    console.log("Making chat API request to:", `${backendUrl}${endpoint}`);
    console.log("Using JWT Token for authorization" + (userIsSystemAdmin ? " (system admin bypass for API key)" : " and API Key for authentication"));
    console.log("Organization ID:", orgId);
    
    // Prepare headers
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${token}`,
      "X-Tenant-ID": orgId,
    };
    
    // Only add API key if user is not a system admin
    if (!userIsSystemAdmin) {
      headers["X-API-Key"] = apiKey!;
    }
    
    // Make the API request to the backend
    const response = await fetch(`${backendUrl}${endpoint}`, {
      method: "POST",
      headers,
      body: JSON.stringify(body),
    });

    try {
      // Get the response data
      const data = await response.json();

      // Check if we have a 401 or other error response
      if (!response.ok) {
        console.error("Backend API error:", response.status, data);
        
        // Enhanced error handling for authentication/authorization issues
        if (response.status === 401 || response.status === 403) {
          // API key issues
          if (data.detail?.includes("API key") || data.message?.includes("API key")) {
            return NextResponse.json({
              kind: "fallback",
              text: "The chat interface requires an organization API key.",
              error: "Please have a system administrator create an organization API key for your organization.",
              error_code: "missing_api_key",
              status: response.status,
            }, { status: 200 });
          }
          
          // JWT token issues
          if (data.detail?.includes("token") || data.message?.includes("token") || 
              data.detail?.includes("JWT") || data.message?.includes("JWT")) {
            return NextResponse.json({
              kind: "fallback",
              text: "Your authentication token has expired or is invalid.",
              error: "Please try refreshing the page or signing out and back in.",
              error_code: "invalid_token",
              status: response.status,
            }, { status: 200 });
          }
          
          // Permission issues
          if (data.detail?.includes("permission") || data.message?.includes("permission")) {
            return NextResponse.json({
              kind: "fallback",
              text: "You don't have permission to use the chat interface.",
              error: "Please contact your administrator to request access to this feature.",
              error_code: "insufficient_permissions",
              status: response.status,
            }, { status: 200 });
          }
          
          // Missing or invalid tenant (org) context
          if (data.detail?.includes("tenant") || data.message?.includes("tenant") ||
              data.detail?.includes("organization") || data.message?.includes("organization")) {
            return NextResponse.json({
              kind: "fallback",
              text: "Your organization isn't properly configured or selected.",
              error: "Please try selecting a different organization or contact support.",
              error_code: "invalid_tenant",
              status: response.status,
            }, { status: 200 });
          }
          
          // Generic auth error
          return NextResponse.json({
            kind: "fallback",
            text: "Authentication or authorization error.",
            error: data.detail || data.message || "Please verify your account permissions and try again.",
            error_code: "auth_error",
            status: response.status,
          }, { status: 200 });
        }
        
        // Server errors (500+)
        if (response.status >= 500) {
          return NextResponse.json({
            kind: "fallback",
            text: "The server encountered an error processing your request.",
            error: "This might be a temporary issue. Please try again in a few moments.",
            error_code: "server_error",
            status: response.status,
          }, { status: 200 });
        }
        
        // Rate limiting
        if (response.status === 429) {
          return NextResponse.json({
            kind: "fallback",
            text: "Too many requests.",
            error: "Please wait a moment before sending more messages.",
            error_code: "rate_limited",
            status: response.status,
          }, { status: 200 });
        }
        
        // Return a more user-friendly error message for other errors
        return NextResponse.json({
          kind: "fallback",
          text: "Sorry, there was an error processing your request.",
          error: data.detail || data.message || "Please try again or contact support.",
          error_code: "general_error",
          status: response.status,
        }, { status: 200 }); // Return 200 with error message in response body instead of error status
      }

      // Return the successful response from the backend
      return NextResponse.json(data, { status: 200 });
    } catch (error) {
      console.error("Error parsing response:", error);
      
      // Return a fallback response if we can't parse the JSON
      return NextResponse.json({
        kind: "fallback",
        text: "Sorry, there was an error processing your request.",
        error: "Could not parse the server response.",
      }, { status: 200 });
    }
  } catch (error) {
    console.error("Error in chat proxy:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Handle GET requests for checking workflow status
 */
export async function GET(request: NextRequest) {
  try {
    // Get authentication information from Clerk
    const { userId, getToken, orgId } = getAuth({ request });
    
    if (!userId || !orgId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the workflow_id from the URL
    const url = new URL(request.url);
    const workflowId = url.searchParams.get('workflow_id');
    
    if (!workflowId) {
      return NextResponse.json({ error: "Missing workflow_id parameter" }, { status: 400 });
    }

    // Get the token for authentication
    const token = await getToken();
    
    if (!token) {
      return NextResponse.json({ error: "Authentication token missing" }, { status: 401 });
    }

    // Get the backend URL from environment variables
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    
    // Check if user is a system admin
    const userIsSystemAdmin = await isSystemAdmin(token, backendUrl);
    
    // Only fetch and require API key if user is not a system admin
    let apiKey = null;
    if (!userIsSystemAdmin) {
      apiKey = await getOrganizationApiKey(token, orgId, backendUrl);
      
      if (!apiKey) {
        return getApiKeyMissingResponse(orgId);
      }
    }
    
    console.log("Making workflow status request to:", `${backendUrl}/v1/status/${workflowId}`);
    console.log("Using JWT Token for authorization" + (userIsSystemAdmin ? " (system admin bypass for API key)" : " and API Key for authentication"));
    console.log("Organization ID:", orgId);
    
    // Prepare headers
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${token}`,
      "X-Tenant-ID": orgId,
    };
    
    // Only add API key if user is not a system admin
    if (!userIsSystemAdmin) {
      headers["X-API-Key"] = apiKey!;
    }
    
    // Make the API request to the backend
    const response = await fetch(`${backendUrl}/v1/status/${workflowId}?tenant_id=${orgId}`, {
      method: "GET",
      headers,
    });

    try {
      // Get the response data
      const data = await response.json();

      // Check if we have a 401 or other error response
      if (!response.ok) {
        console.error("Backend API error:", response.status, data);
        
        // Enhanced error handling for authentication/authorization issues
        if (response.status === 401 || response.status === 403) {
          // API key issues
          if (data.detail?.includes("API key") || data.message?.includes("API key")) {
            return NextResponse.json({
              kind: "fallback",
              text: "The chat interface requires an organization API key.",
              error: "Please have a system administrator create an organization API key for your organization.",
              error_code: "missing_api_key",
              status: response.status,
            }, { status: 200 });
          }
          
          // JWT token issues
          if (data.detail?.includes("token") || data.message?.includes("token") || 
              data.detail?.includes("JWT") || data.message?.includes("JWT")) {
            return NextResponse.json({
              kind: "fallback",
              text: "Your authentication token has expired or is invalid.",
              error: "Please try refreshing the page or signing out and back in.",
              error_code: "invalid_token",
              status: response.status,
            }, { status: 200 });
          }
          
          // Permission issues
          if (data.detail?.includes("permission") || data.message?.includes("permission")) {
            return NextResponse.json({
              kind: "fallback",
              text: "You don't have permission to use the chat interface.",
              error: "Please contact your administrator to request access to this feature.",
              error_code: "insufficient_permissions",
              status: response.status,
            }, { status: 200 });
          }
          
          // Missing or invalid tenant (org) context
          if (data.detail?.includes("tenant") || data.message?.includes("tenant") ||
              data.detail?.includes("organization") || data.message?.includes("organization")) {
            return NextResponse.json({
              kind: "fallback",
              text: "Your organization isn't properly configured or selected.",
              error: "Please try selecting a different organization or contact support.",
              error_code: "invalid_tenant",
              status: response.status,
            }, { status: 200 });
          }
          
          // Generic auth error
          return NextResponse.json({
            kind: "fallback",
            text: "Authentication or authorization error.",
            error: data.detail || data.message || "Please verify your account permissions and try again.",
            error_code: "auth_error",
            status: response.status,
          }, { status: 200 });
        }
        
        // Server errors (500+)
        if (response.status >= 500) {
          return NextResponse.json({
            kind: "fallback",
            text: "The server encountered an error processing your request.",
            error: "This might be a temporary issue. Please try again in a few moments.",
            error_code: "server_error",
            status: response.status,
          }, { status: 200 });
        }
        
        // Rate limiting
        if (response.status === 429) {
          return NextResponse.json({
            kind: "fallback",
            text: "Too many requests.",
            error: "Please wait a moment before sending more messages.",
            error_code: "rate_limited",
            status: response.status,
          }, { status: 200 });
        }
        
        // Return a more user-friendly error message for other errors
        return NextResponse.json({
          kind: "fallback",
          text: "Sorry, there was an error processing your request.",
          error: data.detail || data.message || "Please try again or contact support.",
          error_code: "general_error",
          status: response.status,
        }, { status: 200 }); // Return 200 with error message in response body instead of error status
      }

      // Return the successful response from the backend
      return NextResponse.json(data, { status: 200 });
    } catch (error) {
      console.error("Error parsing response:", error);
      
      // Return a fallback response if we can't parse the JSON
      return NextResponse.json({
        kind: "fallback",
        text: "Sorry, there was an error processing your request.",
        error: "Could not parse the server response.",
      }, { status: 200 });
    }
  } catch (error) {
    console.error("Error in workflow status check:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}