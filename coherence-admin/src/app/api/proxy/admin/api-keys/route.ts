import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';

/**
 * API Keys management endpoint proxy
 * This serves as middleware between the frontend and backend API for Organization API Key management
 */

// List organization API keys
export async function GET(request: NextRequest) {
  try {
    console.log("[API-KEYS] Starting GET request processing");
    
    // Get auth info from Clerk
    let authInfo;
    try {
      authInfo = getAuth({ request });
      console.log("[API-KEYS] Auth info obtained:", { 
        userId: !!authInfo.userId, 
        hasGetToken: !!authInfo.getToken, 
        orgId: authInfo.orgId 
      });
    } catch (authError) {
      console.error("[API-KEYS] Error getting auth info from Clerk:", authError);
      return NextResponse.json({ error: "Failed to authenticate with <PERSON>", details: String(authError) }, { status: 500 });
    }
    
    const { userId, getToken, orgId } = authInfo;
    
    if (!userId || !orgId) {
      console.error("[API-KEYS] Missing userId or orgId:", { userId, orgId });
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    // Get the JWT token
    let token;
    try {
      token = await getToken();
      console.log("[API-KEYS] JWT token retrieved:", token ? "Token obtained" : "No token");
    } catch (tokenError) {
      console.error("[API-KEYS] Error getting token:", tokenError);
      return NextResponse.json({ error: "Failed to retrieve authentication token", details: String(tokenError) }, { status: 500 });
    }
    
    if (!token) {
      console.error("[API-KEYS] Authentication token missing");
      return NextResponse.json({ error: "Authentication token missing" }, { status: 401 });
    }

    // Get the backend URL
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    const apiEndpoint = `${backendUrl}/v1/system/org-api-keys/${orgId}`;
    
    console.log(`[API-KEYS] Making API request to: ${apiEndpoint}`);
    console.log(`[API-KEYS] Environment variables:`, {
      COHERENCE_BACKEND_URL: process.env.COHERENCE_BACKEND_URL,
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
      COHERENCE_API_KEY: !!process.env.COHERENCE_API_KEY,
      COHERENCE_MASTER_API_KEY: !!process.env.COHERENCE_MASTER_API_KEY,
    });
    
    console.log("[API-KEYS] Sending request headers:", {
      Authorization: "Bearer [REDACTED]",
      "X-Tenant-ID": orgId,
    });
    
    const response = await fetch(apiEndpoint, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
        "X-Tenant-ID": orgId,
      }
    });

    // Handle API response
    let data;
    try {
      const responseText = await response.text();
      console.log(`[API-KEYS] API response status: ${response.status}, body:`, responseText.substring(0, 500));
      
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("[API-KEYS] Error parsing API response:", parseError);
        return NextResponse.json({ 
          error: "Failed to parse API response",
          details: responseText
        }, { status: 500 });
      }
    } catch (readError) {
      console.error("[API-KEYS] Failed to read response body:", readError);
      return NextResponse.json({ 
        error: "Failed to read API response"
      }, { status: 500 });
    }
    
    if (!response.ok) {
      console.error("[API-KEYS] API error:", response.status, data);
      return NextResponse.json({ 
        error: data.detail || data.message || "Failed to load API keys"
      }, { status: response.status });
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error("[API-KEYS] Error in API keys proxy:", error);
    return NextResponse.json(
      { error: "Internal server error", details: String(error) },
      { status: 500 }
    );
  }
}

// Create a new organization API key
export async function POST(request: NextRequest) {
  try {
    console.log("[API-KEYS] Starting POST request processing");
    
    // Get auth info from Clerk
    let authInfo;
    try {
      authInfo = getAuth({ request });
      console.log("[API-KEYS] Auth info obtained:", { 
        userId: !!authInfo.userId, 
        hasGetToken: !!authInfo.getToken, 
        orgId: authInfo.orgId 
      });
    } catch (authError) {
      console.error("[API-KEYS] Error getting auth info from Clerk:", authError);
      return NextResponse.json({ error: "Failed to authenticate with Clerk", details: String(authError) }, { status: 500 });
    }
    
    const { userId, getToken, orgId } = authInfo;
    
    if (!userId || !orgId) {
      console.error("[API-KEYS] Missing userId or orgId:", { userId, orgId });
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    // Get the JWT token
    let token;
    try {
      token = await getToken();
      console.log("[API-KEYS] JWT token retrieved:", token ? "Token obtained" : "No token");
    } catch (tokenError) {
      console.error("[API-KEYS] Error getting token:", tokenError);
      return NextResponse.json({ error: "Failed to retrieve authentication token", details: String(tokenError) }, { status: 500 });
    }
    
    if (!token) {
      console.error("[API-KEYS] Authentication token missing");
      return NextResponse.json({ error: "Authentication token missing" }, { status: 401 });
    }

    // Get the request body
    let body;
    try {
      body = await request.json();
      console.log("[API-KEYS] Request body:", body);
    } catch (bodyError) {
      console.error("[API-KEYS] Error parsing request body:", bodyError);
      return NextResponse.json({ error: "Invalid request body" }, { status: 400 });
    }
    
    // Ensure required fields are present
    if (!body.name) {
      return NextResponse.json({ error: "API key name is required" }, { status: 400 });
    }

    // Get the backend URL
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    const apiEndpoint = `${backendUrl}/v1/system/org-api-keys/${orgId}`;
    
    console.log(`[API-KEYS] Creating API key at: ${apiEndpoint}`);
    
    // Make the request to the backend API
    const response = await fetch(apiEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
        "X-Tenant-ID": orgId,
      },
      body: JSON.stringify({
        name: body.name,
        permissions: body.permissions || null,
        expires_at: body.expires_at || null,
      }),
    });

    // Handle API response
    let data;
    try {
      const responseText = await response.text();
      console.log(`[API-KEYS] API response status: ${response.status}, body:`, responseText.substring(0, 500));
      
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("[API-KEYS] Error parsing API response:", parseError);
        return NextResponse.json({ 
          error: "Failed to parse API response",
          details: responseText
        }, { status: 500 });
      }
    } catch (readError) {
      console.error("[API-KEYS] Failed to read response body:", readError);
      return NextResponse.json({ 
        error: "Failed to read API response"
      }, { status: 500 });
    }
    
    if (!response.ok) {
      console.error("[API-KEYS] API error:", response.status, data);
      return NextResponse.json({ 
        error: data.detail || data.message || "Failed to create API key"
      }, { status: response.status });
    }
    
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error("[API-KEYS] Error in API keys proxy:", error);
    return NextResponse.json(
      { error: "Internal server error", details: String(error) },
      { status: 500 }
    );
  }
}