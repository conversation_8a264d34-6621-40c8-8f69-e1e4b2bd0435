import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';

/**
 * API Keys management endpoint proxy for specific key IDs
 * This serves as middleware between the frontend and backend API for operations on specific API keys
 */

// Revoke an API key
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const keyId = params.id;
    console.log(`[API-KEYS] Starting DELETE request processing for key ${keyId}`);
    
    // Get auth info from Clerk
    let authInfo;
    try {
      authInfo = getAuth({ request });
      console.log("[API-KEYS] Auth info obtained:", { 
        userId: !!authInfo.userId, 
        hasGetToken: !!authInfo.getToken, 
        orgId: authInfo.orgId 
      });
    } catch (authError) {
      console.error("[API-KEYS] Error getting auth info from Clerk:", authError);
      return NextResponse.json({ error: "Failed to authenticate with <PERSON>", details: String(authError) }, { status: 500 });
    }
    
    const { userId, getToken, orgId } = authInfo;
    
    if (!userId || !orgId) {
      console.error("[API-KEYS] Missing userId or orgId:", { userId, orgId });
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    // Get the JWT token
    let token;
    try {
      token = await getToken();
      console.log("[API-KEYS] JWT token retrieved:", token ? "Token obtained" : "No token");
    } catch (tokenError) {
      console.error("[API-KEYS] Error getting token:", tokenError);
      return NextResponse.json({ error: "Failed to retrieve authentication token", details: String(tokenError) }, { status: 500 });
    }
    
    if (!token) {
      console.error("[API-KEYS] Authentication token missing");
      return NextResponse.json({ error: "Authentication token missing" }, { status: 401 });
    }

    // Get the backend URL
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    const apiEndpoint = `${backendUrl}/v1/system/org-api-keys/${orgId}/${keyId}`;
    
    console.log(`[API-KEYS] Revoking API key at: ${apiEndpoint}`);
    console.log(`[API-KEYS] Environment variables:`, {
      COHERENCE_BACKEND_URL: process.env.COHERENCE_BACKEND_URL,
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    });
    
    // Make the request to the backend API
    const response = await fetch(apiEndpoint, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
        "X-Tenant-ID": orgId,
      }
    });

    // For successful DELETE operations, no content is expected (204)
    if (response.status === 204) {
      console.log(`[API-KEYS] Successfully revoked key ${keyId}`);
      return new NextResponse(null, { status: 204 });
    }

    // If there was an error, try to parse the response
    let errorResponse;
    try {
      const responseText = await response.text();
      console.log(`[API-KEYS] API response status: ${response.status}, body:`, responseText);
      
      try {
        errorResponse = JSON.parse(responseText);
      } catch (e) {
        // If we can't parse as JSON, use the text directly
        errorResponse = { message: responseText };
      }
    } catch (e) {
      // If we can't read the response at all, use a generic message
      errorResponse = { message: "Failed to read error response" };
    }
    
    console.error(`[API-KEYS] Failed to revoke key ${keyId}:`, errorResponse);
    
    return NextResponse.json({ 
      error: errorResponse.detail || errorResponse.message || "Failed to revoke API key"
    }, { status: response.status });
  } catch (error) {
    console.error("[API-KEYS] Error in API key revocation proxy:", error);
    return NextResponse.json(
      { error: "Internal server error", details: String(error) },
      { status: 500 }
    );
  }
}