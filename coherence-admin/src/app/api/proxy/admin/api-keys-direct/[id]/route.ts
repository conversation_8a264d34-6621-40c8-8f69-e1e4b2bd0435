import { NextRequest, NextResponse } from 'next/server';

/**
 * Direct API key management endpoint proxy for specific key IDs
 * This version reads auth data from headers, not URL parameters
 */

// Revoke an API key
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const keyId = params.id;
    
    // Get authorization from headers
    const authHeader = request.headers.get('Authorization');
    const orgId = request.headers.get('X-Tenant-ID');
    const token = authHeader ? authHeader.replace('Bearer ', '') : null;
    
    console.log("Headers received:", {
      Authorization: authHeader ? "Bearer [REDACTED]" : "Not provided",
      "X-Tenant-ID": orgId || "Not provided"
    });
    
    // Validate required headers
    if (!orgId || !token) {
      const error = !orgId ? "Missing X-Tenant-ID header" : "Missing Authorization header";
      console.error(`[API-KEYS-DIRECT] Error:`, error);
      return NextResponse.json({ error }, { status: 400 });
    }
    
    console.log(`[API-KEYS-DIRECT] Processing deletion of key ${keyId} for orgId: ${orgId}`);

    // Get the backend URL without any confusion
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    
    // Use the correct endpoint path
    const apiEndpoint = `${backendUrl}/v1/system/org-api-keys/${orgId}/${keyId}`;
    
    console.log(`[DEBUG] Revoking API key at: ${apiEndpoint}`);
    
    // Make the request with minimal complexity
    const response = await fetch(apiEndpoint, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
        "X-Tenant-ID": orgId,
      }
    });

    // Handle API response with minimal error handling
    if (!response.ok) {
      let errorText;
      try {
        errorText = await response.text();
      } catch (e) {
        errorText = "Could not read error response";
      }
      
      console.error(`[DEBUG] API error (${response.status}):`, errorText);
      return NextResponse.json({ 
        error: `API error: ${response.status} ${response.statusText}`,
        details: errorText 
      }, { status: response.status });
    }
    
    // For successful DELETE operations, return 204 No Content
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("[DEBUG] Error in API key direct revocation proxy:", error);
    return NextResponse.json(
      { error: `Internal server error: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
}