import { NextRequest, NextResponse } from 'next/server';

/**
 * Direct API key management endpoint proxy
 * This version reads auth data from headers, not URL parameters
 */

// List API keys
export async function GET(request: NextRequest) {
  try {
    // Get authorization from headers
    const authHeader = request.headers.get('Authorization');
    const orgId = request.headers.get('X-Tenant-ID');
    const token = authHeader ? authHeader.replace('Bearer ', '') : null;
    
    console.log("Headers received:", {
      Authorization: authHeader ? "Bearer [REDACTED]" : "Not provided",
      "X-Tenant-ID": orgId || "Not provided"
    });
    
    // Validate required headers
    if (!orgId || !token) {
      const error = !orgId ? "Missing X-Tenant-ID header" : "Missing Authorization header";
      console.error(`[API-KEYS-DIRECT] Error:`, error);
      return NextResponse.json({ error }, { status: 400 });
    }
    
    console.log(`[API-KEYS-DIRECT] Processing request with orgId: ${orgId}`);

    // Get the backend URL without any confusion
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    
    // Use the correct endpoint path
    const apiEndpoint = `${backendUrl}/v1/system/org-api-keys/${orgId}`;
    
    console.log(`[DEBUG] Making direct API request to: ${apiEndpoint}`);
    
    // Make the request with minimal complexity
    const response = await fetch(apiEndpoint, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
        "X-Tenant-ID": orgId,
      }
    });

    // Handle API response with minimal error handling
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DEBUG] API error (${response.status}):`, errorText);
      return NextResponse.json({ 
        error: `API error: ${response.status} ${response.statusText}`,
        details: errorText 
      }, { status: response.status });
    }
    
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("[DEBUG] Error in API keys direct proxy:", error);
    return NextResponse.json(
      { error: `Internal server error: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
}

// Create API key
export async function POST(request: NextRequest) {
  try {
    // Get authorization from headers
    const authHeader = request.headers.get('Authorization');
    const orgId = request.headers.get('X-Tenant-ID');
    const token = authHeader ? authHeader.replace('Bearer ', '') : null;
    
    console.log("Headers received:", {
      Authorization: authHeader ? "Bearer [REDACTED]" : "Not provided",
      "X-Tenant-ID": orgId || "Not provided"
    });
    
    // Validate required headers
    if (!orgId || !token) {
      const error = !orgId ? "Missing X-Tenant-ID header" : "Missing Authorization header";
      console.error(`[API-KEYS-DIRECT] Error:`, error);
      return NextResponse.json({ error }, { status: 400 });
    }
    
    // Get the request body
    const body = await request.json();
    
    // Ensure required fields are present
    if (!body.name) {
      return NextResponse.json({ error: "API key name is required" }, { status: 400 });
    }

    // Get the backend URL without any confusion
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    
    // Use the correct endpoint path
    const apiEndpoint = `${backendUrl}/v1/system/org-api-keys/${orgId}`;
    
    console.log(`[DEBUG] Creating API key at: ${apiEndpoint}`);
    
    // Make the request with minimal complexity
    const response = await fetch(apiEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
        "X-Tenant-ID": orgId,
      },
      body: JSON.stringify({
        name: body.name,
        permissions: body.permissions || null,
        expires_at: body.expires_at || null,
      }),
    });

    // Handle API response with minimal error handling
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DEBUG] API error (${response.status}):`, errorText);
      return NextResponse.json({ 
        error: `API error: ${response.status} ${response.statusText}`,
        details: errorText 
      }, { status: response.status });
    }
    
    const data = await response.json();
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error("[DEBUG] Error in API keys direct proxy:", error);
    return NextResponse.json(
      { error: `Internal server error: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
}