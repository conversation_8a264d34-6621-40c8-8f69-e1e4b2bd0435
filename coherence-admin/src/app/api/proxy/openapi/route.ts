import { NextRequest, NextResponse } from 'next/server';
import * as jsYaml from 'js-yaml';

// Add debug logging to track token refresh impact
const DEBUG = process.env.NODE_ENV !== 'production';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const url = searchParams.get('url');

  console.log(`Proxy request received for URL: ${url}`);

  if (!url) {
    return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 });
  }

  try {
    // Validate that the URL is a valid HTTP/HTTPS URL
    const targetUrl = new URL(url);
    if (!['http:', 'https:'].includes(targetUrl.protocol)) {
      return NextResponse.json({ error: 'Only HTTP and HTTPS URLs are allowed' }, { status: 400 });
    }

    // Transform localhost URLs to work within Docker network
    let finalUrl = targetUrl.toString();
    
    // Check if we're running in Docker and the URL is targeting localhost
    if (targetUrl.hostname === 'localhost' || targetUrl.hostname === '127.0.0.1') {
      // Check if it's targeting the coherence API
      if (targetUrl.port === '8001' || targetUrl.port === '8000') {
        // Replace with Docker internal hostname
        targetUrl.hostname = 'coherence-api';
        targetUrl.port = '8000';
        finalUrl = targetUrl.toString();
        console.log(`Transformed URL for Docker: ${url} -> ${finalUrl}`);
      }
    }

    // Fetch the API spec from the target URL
    const response = await fetch(finalUrl, {
      headers: {
        'Accept': 'application/json, application/yaml, text/yaml, text/plain, */*',
        'User-Agent': 'Coherence-Admin/1.0',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch from ${url}: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    // Get the content type and body
    const contentType = response.headers.get('content-type') || '';
    let bodyContent = await response.text();

    // Check if the content appears to be HTML
    if (bodyContent.trim().startsWith('<!DOCTYPE') || bodyContent.trim().startsWith('<html')) {
      console.error('API proxy received HTML content instead of API specification');
      return NextResponse.json(
        { error: `The URL returned an HTML page instead of an API specification. Please verify the URL points directly to a JSON or YAML specification file.` },
        { status: 400 }
      );
    }

    // Attempt to parse the content based on its type
    let parsedContent: any;
    let contentIsJson = false;

    // Try to validate as JSON first
    try {
      parsedContent = JSON.parse(bodyContent);
      contentIsJson = true;
      console.log('Successfully parsed content as JSON');
    } catch (jsonError) {
      // If not JSON, try YAML
      if (contentType.includes('yaml') || contentType.includes('text/plain') || 
          bodyContent.trim().startsWith('openapi:') || bodyContent.trim().startsWith('swagger:') ||
          bodyContent.trim().startsWith('financial_api:') || bodyContent.trim().startsWith('business_api:')) {
        try {
          parsedContent = jsYaml.load(bodyContent);
          if (parsedContent && typeof parsedContent === 'object') {
            // Convert YAML to JSON for consistent handling
            bodyContent = JSON.stringify(parsedContent);
            contentIsJson = true;
            console.log('Successfully parsed content as YAML and converted to JSON');
          } else {
            console.error('YAML content did not parse to a valid object');
            return NextResponse.json(
              { error: `The content from ${url} could not be parsed as a valid YAML object.` },
              { status: 400 }
            );
          }
        } catch (yamlError) {
          console.error('Invalid YAML content received from API proxy:', yamlError);
          return NextResponse.json(
            { error: `The content from ${url} is not valid YAML. Error: ${yamlError instanceof Error ? yamlError.message : 'Unknown error'}` },
            { status: 400 }
          );
        }
      } else if (contentType.includes('application/json')) {
        // It claims to be JSON but failed to parse
        console.error('Invalid JSON content received from API proxy:', jsonError);
        return NextResponse.json(
          { error: `The content from ${url} is not valid JSON. Error: ${jsonError instanceof Error ? jsonError.message : 'Unknown error'}` },
          { status: 400 }
        );
      }
    }

    // Set appropriate headers for the response
    return new NextResponse(bodyContent, {
      status: 200,
      headers: {
        // Always return as application/json if we've successfully converted to JSON
        'Content-Type': contentIsJson ? 'application/json' : contentType,
        'Cache-Control': 'no-cache', // Don't cache to ensure fresh data
      },
    });
  } catch (error) {
    console.error('Error fetching API specification:', error);
    return NextResponse.json(
      { error: `Failed to fetch API specification: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
