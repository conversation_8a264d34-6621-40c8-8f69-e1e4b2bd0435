import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

export async function GET(_request: Request) {
  try {
    const { getToken, userId, actor } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized: No user ID found' }, { status: 401 });
    }

    // Get token with the specified JWT template from environment variable
    const templateName = process.env.CLERK_SESSION_TOKEN_TEMPLATE || 'coherence_session';
    console.log(`[debug-token] Attempting to get token with template "${templateName}"...`);
    let token = await getToken({ template: templateName });
    
    // If that fails, try without a template
    if (!token) {
      console.log('[debug-token] Failed with template, trying without...');
      token = await getToken();
    }
    
    console.log('[debug-token] Token obtained');
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized: Could not retrieve token' }, { status: 401 });
    }

    // Decode token (JWT is in format header.payload.signature)
    const parts = token.split('.');
    if (parts.length !== 3) {
      return NextResponse.json({ error: 'Invalid token format' }, { status: 400 });
    }

    let decodedHeader, decodedPayload;
    try {
      decodedHeader = JSON.parse(Buffer.from(parts[0], 'base64').toString());
      // Some base64 implementations might require padding
      const payloadBase64 = parts[1].replace(/-/g, '+').replace(/_/g, '/');
      const padding = payloadBase64.length % 4;
      const paddedPayload = padding ? 
        payloadBase64 + '='.repeat(4 - padding) : 
        payloadBase64;
      
      decodedPayload = JSON.parse(Buffer.from(paddedPayload, 'base64').toString());
    } catch (e) {
      console.error('[debug-token] Error decoding token:', e);
      return NextResponse.json({ 
        error: 'Error decoding token', 
        details: e instanceof Error ? e.message : 'Unknown error'
      }, { status: 400 });
    }

    // Extract and log session claims
    const sessionClaims = decodedPayload.__session || {};
    
    console.log('[debug-token] Session claims:', sessionClaims);
    
    // Check for system admin status in various locations
    let isSystemAdmin = false;
    let isSystemAdminSource = 'none';
    
    // 1. Check direct claim in JWT payload
    if (decodedPayload.is_system_admin !== undefined) {
      isSystemAdmin = Boolean(decodedPayload.is_system_admin);
      isSystemAdminSource = 'direct_claim';
    }
    // 2. Check in session claims
    else if (sessionClaims.is_system_admin !== undefined) {
      isSystemAdmin = Boolean(sessionClaims.is_system_admin);
      isSystemAdminSource = 'session_claim';
    }
    // 3. Check in JWT permissions array
    else if (Array.isArray(decodedPayload.permissions) && decodedPayload.permissions.includes('system:*')) {
      isSystemAdmin = true;
      isSystemAdminSource = 'permissions_array';
    }
    // 4. Check in actor's public_metadata
    else if (decodedPayload.actor?.public_metadata?.isSystemAdmin !== undefined) {
      isSystemAdmin = Boolean(decodedPayload.actor.public_metadata.isSystemAdmin);
      isSystemAdminSource = 'actor_public_metadata_camelCase';
    }
    else if (decodedPayload.actor?.public_metadata?.is_system_admin !== undefined) {
      isSystemAdmin = Boolean(decodedPayload.actor.public_metadata.is_system_admin);
      isSystemAdminSource = 'actor_public_metadata_snake_case';
    }
    
    // Check public metadata from actor directly (if available)
    const actorPublicMetadata = actor?.publicMetadata || {};
    
    console.log('[debug-token] System admin status check:', { 
      isSystemAdmin, 
      isSystemAdminSource,
      actorPublicMetadata
    });
    
    return NextResponse.json({
      userId,
      tokenFirstChars: token.substring(0, 50) + '...',
      tokenLength: token.length,
      tokenExpiresAt: decodedPayload.exp ? new Date(decodedPayload.exp * 1000).toISOString() : null,
      header: decodedHeader,
      claims: decodedPayload,
      sessionClaims,
      sessionClaimTypes: {
        org_id: typeof sessionClaims.org_id,
        org_name: typeof sessionClaims.org_name,
        org_role: typeof sessionClaims.org_role,
        org_slug: typeof sessionClaims.org_slug,
        org_metadata: typeof sessionClaims.org_metadata,
      },
      // System admin information
      systemAdminInfo: {
        isSystemAdmin,
        isSystemAdminSource,
        actorPublicMetadata,
        hasSystemPermissionInJWT: Array.isArray(decodedPayload.permissions) && decodedPayload.permissions.includes('system:*'),
        // Environment configuration for system admin
        envSystemAdminConfig: {
          CLERK_SESSION_TOKEN_TEMPLATE: process.env.CLERK_SESSION_TOKEN_TEMPLATE || 'coherence_session (default)'
        }
      }
    });

  } catch (error) {
    console.error('[debug-token] Internal error:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Internal server error', details: errorMessage }, { status: 500 });
  }
}