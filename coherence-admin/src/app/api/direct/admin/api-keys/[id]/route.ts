import { NextRequest, NextResponse } from 'next/server';

/**
 * Direct API for managing individual API keys
 * Completely bypasses Clerk authentication to avoid the x-clerk-debug error
 */

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("[DIRECT-API-KEYS] Processing DELETE request for key ID:", params.id);
    
    // Get cookies from the request
    const cookieHeader = request.headers.get('cookie');
    console.log("[DIRECT-API-KEYS] Cookie header exists:", !!cookieHeader);
    
    if (!params.id) {
      return NextResponse.json({ error: "API key ID is required" }, { status: 400 });
    }
    
    // Get backend URL
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    
    // Check if this is running inside Docker container
    const apiBaseUrl = process.env.NODE_ENV === 'production'
      ? (process.env.API_URL || 'http://coherence-api:8000/v1')
      : (process.env.NEXT_PUBLIC_API_URL || backendUrl + '/v1');
    
    // First retrieve session info to get organization ID
    console.log("[DIRECT-API-KEYS] Fetching session info from:", `${apiBaseUrl}/auth/session-info`);
    
    const sessionResponse = await fetch(`${apiBaseUrl}/auth/session-info`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Cookie": cookieHeader || '',
      }
    });
    
    if (!sessionResponse.ok) {
      console.error("[DIRECT-API-KEYS] Failed to get session info:", sessionResponse.status);
      return NextResponse.json({ 
        error: "Failed to authenticate with backend", 
        status: sessionResponse.status 
      }, { status: 401 });
    }
    
    const sessionData = await sessionResponse.json();
    console.log("[DIRECT-API-KEYS] Session data retrieved for key deletion:", {
      orgId: sessionData.org?.id,
      isSystemAdmin: sessionData.is_system_admin
    });
    
    const orgId = sessionData.org?.id;
    if (!orgId) {
      return NextResponse.json({ error: "No organization found in session" }, { status: 400 });
    }
    
    // Now delete the API key
    const apiEndpoint = `${apiBaseUrl}/system/org-api-keys/${orgId}/${params.id}`;
    console.log(`[DIRECT-API-KEYS] Revoking API key at: ${apiEndpoint}`);
    
    const response = await fetch(apiEndpoint, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        "Cookie": cookieHeader || '',
      }
    });
    
    // Process the response
    if (!response.ok) {
      let errorMessage = "Failed to revoke API key";
      try {
        const errorText = await response.text();
        console.error(`[DIRECT-API-KEYS] API error response: ${response.status}`, errorText);
        
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.detail || errorData.message || errorMessage;
        } catch {
          // Not JSON, use the text directly
          if (errorText) errorMessage = errorText;
        }
      } catch (error) {
        console.error("[DIRECT-API-KEYS] Error reading error response:", error);
      }
      
      return NextResponse.json({ error: errorMessage }, { status: response.status });
    }
    
    // Successfully revoked
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("[DIRECT-API-KEYS] Error in API key deletion:", error);
    return NextResponse.json(
      { error: "Internal server error", details: String(error) },
      { status: 500 }
    );
  }
}