import { NextRequest, NextResponse } from 'next/server';

/**
 * Direct API Keys endpoint that bypasses Clerk authentication
 * This endpoint directly forwards the request to the backend using cookies
 */

export async function GET(request: NextRequest) {
  try {
    console.log("[DIRECT-API-KEYS] Starting GET request processing");
    
    // Get cookies from the request
    const cookieHeader = request.headers.get('cookie');
    console.log("[DIRECT-API-KEYS] Cookie header exists:", !!cookieHeader);
    
    // Get backend URL
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    
    // Check if this is running inside Docker container
    const apiBaseUrl = process.env.NODE_ENV === 'production'
      ? (process.env.API_URL || 'http://coherence-api:8000/v1')
      : (process.env.NEXT_PUBLIC_API_URL || backendUrl + '/v1');
    
    // First retrieve session info to get organization ID
    console.log("[DIRECT-API-KEYS] Fetching session info from:", `${apiBaseUrl}/auth/session-info`);
    
    const sessionResponse = await fetch(`${apiBaseUrl}/auth/session-info`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Cookie": cookieHeader || '',
      }
    });
    
    if (!sessionResponse.ok) {
      console.error("[DIRECT-API-KEYS] Failed to get session info:", sessionResponse.status);
      return NextResponse.json({ 
        error: "Failed to authenticate with backend", 
        status: sessionResponse.status 
      }, { status: 401 });
    }
    
    const sessionData = await sessionResponse.json();
    console.log("[DIRECT-API-KEYS] Session data retrieved:", {
      orgId: sessionData.org?.id,
      userId: sessionData.user?.id,
      isSystemAdmin: sessionData.is_system_admin
    });
    
    const orgId = sessionData.org?.id;
    if (!orgId) {
      return NextResponse.json({ error: "No organization found in session" }, { status: 400 });
    }
    
    // Now use the organization ID to fetch API keys
    const apiEndpoint = `${apiBaseUrl}/system/org-api-keys/${orgId}`;
    console.log(`[DIRECT-API-KEYS] Fetching API keys from: ${apiEndpoint}`);
    
    const response = await fetch(apiEndpoint, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Cookie": cookieHeader || '',
      }
    });
    
    // Handle API response
    let data;
    try {
      const responseText = await response.text();
      console.log(`[DIRECT-API-KEYS] API response status: ${response.status}`);
      
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("[DIRECT-API-KEYS] Error parsing API response:", parseError);
        return NextResponse.json({ 
          error: "Failed to parse API response",
          details: responseText.substring(0, 500)
        }, { status: 500 });
      }
    } catch (readError) {
      console.error("[DIRECT-API-KEYS] Failed to read response body:", readError);
      return NextResponse.json({ 
        error: "Failed to read API response"
      }, { status: 500 });
    }
    
    if (!response.ok) {
      console.error("[DIRECT-API-KEYS] API error:", response.status, data);
      return NextResponse.json({ 
        error: data.detail || data.message || "Failed to load API keys"
      }, { status: response.status });
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error("[DIRECT-API-KEYS] Error in direct API keys endpoint:", error);
    return NextResponse.json(
      { error: "Internal server error", details: String(error) },
      { status: 500 }
    );
  }
}

// Create a new organization API key
export async function POST(request: NextRequest) {
  try {
    console.log("[DIRECT-API-KEYS] Starting POST request processing");
    
    // Get cookies from the request
    const cookieHeader = request.headers.get('cookie');
    console.log("[DIRECT-API-KEYS] Cookie header exists:", !!cookieHeader);
    
    // Get the request body
    let body;
    try {
      body = await request.json();
      console.log("[DIRECT-API-KEYS] Request body:", body);
    } catch (bodyError) {
      console.error("[DIRECT-API-KEYS] Error parsing request body:", bodyError);
      return NextResponse.json({ error: "Invalid request body" }, { status: 400 });
    }
    
    // Ensure required fields are present
    if (!body.name) {
      return NextResponse.json({ error: "API key name is required" }, { status: 400 });
    }
    
    // Get backend URL
    const backendUrl = process.env.COHERENCE_BACKEND_URL || 'http://localhost:8001';
    
    // Check if this is running inside Docker container
    const apiBaseUrl = process.env.NODE_ENV === 'production'
      ? (process.env.API_URL || 'http://coherence-api:8000/v1')
      : (process.env.NEXT_PUBLIC_API_URL || backendUrl + '/v1');
    
    // First retrieve session info to get organization ID
    console.log("[DIRECT-API-KEYS] Fetching session info from:", `${apiBaseUrl}/auth/session-info`);
    
    const sessionResponse = await fetch(`${apiBaseUrl}/auth/session-info`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Cookie": cookieHeader || '',
      }
    });
    
    if (!sessionResponse.ok) {
      console.error("[DIRECT-API-KEYS] Failed to get session info:", sessionResponse.status);
      return NextResponse.json({ 
        error: "Failed to authenticate with backend", 
        status: sessionResponse.status 
      }, { status: 401 });
    }
    
    const sessionData = await sessionResponse.json();
    console.log("[DIRECT-API-KEYS] Session data retrieved:", {
      orgId: sessionData.org?.id,
      userId: sessionData.user?.id,
      isSystemAdmin: sessionData.is_system_admin
    });
    
    const orgId = sessionData.org?.id;
    if (!orgId) {
      return NextResponse.json({ error: "No organization found in session" }, { status: 400 });
    }
    
    // Now create the API key
    const apiEndpoint = `${apiBaseUrl}/system/org-api-keys/${orgId}`;
    console.log(`[DIRECT-API-KEYS] Creating API key at: ${apiEndpoint}`);
    
    const response = await fetch(apiEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Cookie": cookieHeader || '',
      },
      body: JSON.stringify({
        name: body.name,
        permissions: body.permissions || null,
        expires_at: body.expires_at || null,
      }),
    });
    
    // Handle API response
    let data;
    try {
      const responseText = await response.text();
      console.log(`[DIRECT-API-KEYS] API response status: ${response.status}`);
      
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("[DIRECT-API-KEYS] Error parsing API response:", parseError);
        return NextResponse.json({ 
          error: "Failed to parse API response",
          details: responseText.substring(0, 500)
        }, { status: 500 });
      }
    } catch (readError) {
      console.error("[DIRECT-API-KEYS] Failed to read response body:", readError);
      return NextResponse.json({ 
        error: "Failed to read API response"
      }, { status: 500 });
    }
    
    if (!response.ok) {
      console.error("[DIRECT-API-KEYS] API error:", response.status, data);
      return NextResponse.json({ 
        error: data.detail || data.message || "Failed to create API key"
      }, { status: response.status });
    }
    
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error("[DIRECT-API-KEYS] Error in direct API keys endpoint:", error);
    return NextResponse.json(
      { error: "Internal server error", details: String(error) },
      { status: 500 }
    );
  }
}