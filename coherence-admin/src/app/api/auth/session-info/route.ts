import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(_request: Request) {
  try {
    const { getToken, userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized: No user ID found' }, { status: 401 });
    }

    // Get token with the specified JWT template
    console.log('[session-info] Attempting to get token with template...');
    let token = await getToken({ template: 'coherence_session' });
    
    // If that fails, try without a template
    if (!token) {
      console.log('[session-info] Failed with template, trying without...');
      token = await getToken();
    }
    
    console.log('[session-info] Token obtained (first 50 chars):', token?.substring(0, 50));
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized: Could not retrieve token' }, { status: 401 });
    }

    // URL for your actual backend (FastAPI) endpoint
    // Using the environment variable defined in docker-compose.yml for coherence-admin
    const backendApiUrl = process.env.API_URL || process.env.NEXT_PUBLIC_API_URL;
    if (!backendApiUrl) {
        return NextResponse.json({ error: 'Server configuration error: API_URL not set' }, { status: 500 });
    }
    
    // Docker networking - special handling for container communication
    let sessionInfoUrl;

    // Check if this is running inside Docker container
    if (process.env.NODE_ENV === 'production') {
      // In Docker, use the API_URL environment variable which should be set correctly for inter-container communication
      const internalApiUrl = process.env.API_URL || 'http://coherence-api:8000/v1';
      sessionInfoUrl = `${internalApiUrl.endsWith('/v1') ? internalApiUrl : internalApiUrl + '/v1'}/auth/session-info`;
      console.log(`[API Route session-info] Using Docker inter-container URL: ${sessionInfoUrl}`);
    } else {
      // For local development
      sessionInfoUrl = `${backendApiUrl.endsWith('/v1') ? backendApiUrl : backendApiUrl + '/v1'}/auth/session-info`;
    }

    console.log(`[API Route session-info] Fetching from backend: ${sessionInfoUrl} for user ${userId}`);

    const backendResponse = await fetch(sessionInfoUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!backendResponse.ok) {
      const errorData = await backendResponse.text();
      console.error(`[API Route session-info] Error from backend: ${backendResponse.status} ${backendResponse.statusText}`, errorData);
      return NextResponse.json({ error: `Backend error: ${backendResponse.statusText}`, details: errorData }, { status: backendResponse.status });
    }

    const data = await backendResponse.json();
    console.log('[API Route session-info] Successfully fetched data from backend.');
    console.log('[API Route session-info] Organization data received:', {
      org_id: data.org_id,
      org_name: data.org_name,
      org_role: data.org_role
    });

    return NextResponse.json(data);

  } catch (error) {
    console.error('[API Route session-info] Internal error:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Internal server error', details: errorMessage }, { status: 500 });
  }
} 