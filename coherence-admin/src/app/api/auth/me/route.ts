import { auth } from '@clerk/nextjs/server';
import { NextResponse, NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('[me] Starting authentication check');
    
    // Check if Authorization header is present
    const authHeader = request.headers.get('authorization');
    console.log('[me] Authorization header:', authHeader ? `Bearer ...${authHeader.slice(-10)}` : 'MISSING');
    
    // Try to get auth context
    const { getToken, userId } = await auth();
    console.log('[me] User ID from auth():', userId);

    if (!userId) {
      console.log('[me] No user ID found in auth context');
      return NextResponse.json({ error: 'Unauthorized: No user ID found' }, { status: 401 });
    }

    // If we have an Authorization header, extract the token
    let token = null;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      console.log('[me] Token extracted from header:', { hasToken: !!token, length: token?.length });
    } else {
      // Fallback to trying getToken
      console.log('[me] No Authorization header, trying getToken...');
      
      // Get token with the specified JWT template from environment
      const templateName = process.env.CLERK_SESSION_TOKEN_TEMPLATE || 'coherence_session';
      console.log(`[me] Attempting to get token with template "${templateName}"...`);
      token = await getToken({ template: templateName });
      console.log('[me] Template token result:', token ? 'SUCCESS' : 'FAILED');
      
      // If that fails, try without a template
      if (!token) {
        console.log('[me] Failed with template, trying without...');
        token = await getToken();
        console.log('[me] Default token result:', token ? 'SUCCESS' : 'FAILED');
      }
    }
    
    console.log('[me] Final token status:', {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      tokenPreview: token ? token.substring(0, 50) + '...' : null
    });
    
    if (!token) {
      console.log('[me] ERROR: Could not retrieve any token');
      return NextResponse.json({ error: 'Unauthorized: Could not retrieve token' }, { status: 401 });
    }

    // URL for your actual backend (FastAPI) endpoint
    // Using the environment variable defined in docker-compose.yml for coherence-admin
    const backendApiUrl = process.env.API_URL || process.env.NEXT_PUBLIC_API_URL;
    if (!backendApiUrl) {
        return NextResponse.json({ error: 'Server configuration error: API_URL not set' }, { status: 500 });
    }
    
    // Docker networking - special handling for container communication
    let sessionInfoUrl;

    // Check if this is running inside Docker container
    if (process.env.NODE_ENV === 'production') {
      // In Docker, use the API_URL environment variable which should be set correctly for inter-container communication
      const internalApiUrl = process.env.API_URL || 'http://coherence-api:8000/v1';
      sessionInfoUrl = `${internalApiUrl.endsWith('/v1') ? internalApiUrl : internalApiUrl + '/v1'}/auth/session-info`;
      console.log(`[API Route me] Using Docker inter-container URL: ${sessionInfoUrl}`);
    } else {
      // For local development
      sessionInfoUrl = `${backendApiUrl.endsWith('/v1') ? backendApiUrl : backendApiUrl + '/v1'}/auth/session-info`;
    }

    console.log(`[API Route me] Fetching from backend: ${sessionInfoUrl} for user ${userId}`);

    const backendResponse = await fetch(sessionInfoUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!backendResponse.ok) {
      const errorData = await backendResponse.text();
      console.error(`[API Route me] Error from backend: ${backendResponse.status} ${backendResponse.statusText}`, errorData);
      return NextResponse.json({ error: `Backend error: ${backendResponse.statusText}`, details: errorData }, { status: backendResponse.status });
    }

    const data = await backendResponse.json();
    console.log('[API Route me] Successfully fetched data from backend.');
    console.log('[API Route me] Organization data received:', {
      org_id: data.org_id,
      org_name: data.org_name,
      org_role: data.org_role
    });

    // Check if org_id contains template variable (indicating template rendering failure)
    const hasUnrenderedTemplate = (value: string | string[]) =>
      typeof value === 'string' && (value.includes('{{') || value.includes('}}'));

    // Function to extract Clerk user ID from token
    const extractUserIdFromToken = (token: string) => {
      try {
        // Basic JWT parsing - split by dots and decode the payload (second part)
        const parts = token.split('.');
        if (parts.length !== 3) return null;

        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        return payload.sub || null;
      } catch (e) {
        console.error('[API Route me] Error extracting user ID from token:', e);
        return null;
      }
    };

    // Check if we have the JWT with system admin status in the token
    const tokenUserId = extractUserIdFromToken(token);
    console.log('[API Route me] Token contains user ID:', tokenUserId);

    // Detect unrendered template variables
    const orgIdHasTemplate = hasUnrenderedTemplate(data.org_id);
    const orgNameHasTemplate = hasUnrenderedTemplate(data.org_name);
    const orgRoleHasTemplate = hasUnrenderedTemplate(data.org_role);

    console.log('[API Route me] Template detection:', {
      orgIdHasTemplate,
      orgNameHasTemplate,
      orgRoleHasTemplate,
      orgId: data.org_id,
      orgName: data.org_name,
      orgRole: data.org_role,
      is_system_admin: data.is_system_admin,
      tokenUserId
    });

    // If system_admin and tokens have unrendered templates, use fallback values
    let orgId = data.org_id;
    let orgName = data.org_name;
    let orgRole = data.org_role;

    if (data.is_system_admin) {
      // For system admins with template issues, use fallback values
      if (orgIdHasTemplate || !orgId) {
        orgId = 'org_fallback_for_system_admin';
        console.log('[API Route me] Using fallback org_id for system admin');
      }

      if (orgNameHasTemplate || !orgName) {
        orgName = 'System Admin Organization';
        console.log('[API Route me] Using fallback org_name for system admin');
      }

      if (orgRoleHasTemplate || !orgRole) {
        orgRole = 'admin';
        console.log('[API Route me] Using fallback org_role for system admin');
      }
    }

    // Return structured data expected by AdminSessionContext
    return NextResponse.json({
      org: {
        id: orgId,
        name: orgName,
        role: orgRole,
        slug: data.org_slug || null,
        public_metadata: data.org_metadata || {}
      },
      tenant: {
        id: data.tenant_id,
        name: data.tenant_name
      },
      permissions: data.permissions || []
    });

  } catch (error) {
    console.error('[API Route me] Internal error:', error);

    // Enhanced error details
    let errorDetails = {
      message: error instanceof Error ? error.message : 'An unknown error occurred',
      cause: error instanceof Error && 'cause' in error ? (error as any).cause : undefined,
      stack: error instanceof Error ? error.stack : undefined,
      env: {
        NODE_ENV: process.env.NODE_ENV,
        API_URL: process.env.API_URL,
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL
      }
    };

    console.error('[API Route me] Detailed error info:', JSON.stringify(errorDetails, null, 2));

    return NextResponse.json({
      error: 'Internal server error',
      details: errorDetails.message,
      diagnostics: errorDetails
    }, { status: 500 });
  }
}
