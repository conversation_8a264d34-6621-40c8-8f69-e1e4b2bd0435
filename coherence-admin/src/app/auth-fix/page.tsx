'use client';

import { useAuth, useOrganization, useUser, SignOutButton } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function AuthFixPage() {
  const { isLoaded, isSignedIn, getToken, userId, sessionId, orgId, orgRole } = useAuth();
  const { organization, isLoaded: orgLoaded } = useOrganization();
  const { user } = useUser();
  const router = useRouter();
  const [debugInfo, setDebugInfo] = useState<any>(null);

  const runDiagnostics = async () => {
    console.log('Running auth diagnostics...');
    
    const diagnostics = {
      clerk: {
        isLoaded,
        isSignedIn,
        userId,
        sessionId,
        orgId,
        orgRole,
        hasOrganization: !!organization,
        organizationName: organization?.name,
        userEmail: user?.emailAddresses?.[0]?.emailAddress,
      },
      tokens: {},
      api: {}
    };

    // Test token retrieval
    try {
      const tokenWithTemplate = await getToken({ template: 'coherence_session' });
      const tokenDefault = await getToken();
      
      diagnostics.tokens = {
        templateToken: tokenWithTemplate ? 'Available' : 'Not available',
        defaultToken: tokenDefault ? 'Available' : 'Not available',
        templateTokenPreview: tokenWithTemplate ? tokenWithTemplate.substring(0, 50) + '...' : null,
        defaultTokenPreview: tokenDefault ? tokenDefault.substring(0, 50) + '...' : null,
      };
    } catch (error) {
      diagnostics.tokens = { error: error.message };
    }

    // Test API call
    try {
      const response = await fetch('/api/auth/me');
      const data = await response.json();
      
      diagnostics.api = {
        status: response.status,
        ok: response.ok,
        hasOrg: !!data.org,
        hasTenant: !!data.tenant,
        hasPermissions: !!data.permissions,
        error: !response.ok ? data : null
      };
    } catch (error) {
      diagnostics.api = { error: error.message };
    }

    setDebugInfo(diagnostics);
    console.log('Diagnostics complete:', diagnostics);
  };

  const clearSession = () => {
    // Clear any local storage or session storage that might be interfering
    if (typeof window !== 'undefined') {
      localStorage.clear();
      sessionStorage.clear();
      console.log('Cleared local and session storage');
    }
  };

  if (!isLoaded) {
    return <div className="p-8">Loading Clerk...</div>;
  }

  return (
    <div className="max-w-4xl mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Authentication Fix Tool</h1>
      
      <div className="space-y-6">
        {/* Current Status */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Current Status</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="font-medium">Clerk Status:</span>
              <span className={`ml-2 ${isSignedIn ? 'text-green-600' : 'text-red-600'}`}>
                {isSignedIn ? 'Signed In' : 'Not Signed In'}
              </span>
            </div>
            <div>
              <span className="font-medium">User ID:</span>
              <span className="ml-2 font-mono">{userId || 'None'}</span>
            </div>
            <div>
              <span className="font-medium">Organization:</span>
              <span className="ml-2">{organization?.name || 'None'}</span>
            </div>
            <div>
              <span className="font-medium">Org Role:</span>
              <span className="ml-2">{orgRole || 'None'}</span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={runDiagnostics}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Run Diagnostics
            </button>
            
            <button
              onClick={clearSession}
              className="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700"
            >
              Clear Local Storage
            </button>
            
            <button
              onClick={() => router.push('/org-selection')}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Go to Org Selection
            </button>
            
            <button
              onClick={() => router.push('/debug/auth')}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
            >
              Full Debug Page
            </button>
            
            <SignOutButton>
              <button className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                Sign Out & Retry
              </button>
            </SignOutButton>
          </div>
        </div>

        {/* Diagnostics Results */}
        {debugInfo && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Diagnostics Results</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        )}

        {/* Common Solutions */}
        <div className="bg-blue-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Common Solutions</h2>
          <ol className="space-y-3 list-decimal list-inside">
            <li>
              <strong>No Organization Selected:</strong> If you see "No organization" above, 
              click "Go to Org Selection" to select or create an organization.
            </li>
            <li>
              <strong>JWT Template Issues:</strong> If tokens are showing "Not available" with template,
              the Clerk JWT template might not be configured. This can be ignored if the default token works.
            </li>
            <li>
              <strong>API Connection Issues:</strong> If the API test fails, make sure the backend
              is running at {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001/v1'}.
            </li>
            <li>
              <strong>Cache Issues:</strong> Try "Clear Local Storage" and refresh the page,
              or use incognito/private browsing mode.
            </li>
            <li>
              <strong>Complete Reset:</strong> Sign out completely, clear storage, and sign in again.
            </li>
          </ol>
        </div>

        {/* Environment Info */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Environment Configuration</h2>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">API URL:</span>
              <span className="ml-2 font-mono">{process.env.NEXT_PUBLIC_API_URL || 'Not set'}</span>
            </div>
            <div>
              <span className="font-medium">Clerk Publishable Key:</span>
              <span className="ml-2 font-mono">
                {process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY ? 
                  process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY.substring(0, 20) + '...' : 
                  'Not set'}
              </span>
            </div>
            <div>
              <span className="font-medium">After Sign In URL:</span>
              <span className="ml-2 font-mono">{process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL || 'Not set'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
