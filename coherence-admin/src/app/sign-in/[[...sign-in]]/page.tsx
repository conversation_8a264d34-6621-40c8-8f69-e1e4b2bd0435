import { SignIn } from "@clerk/nextjs";

export default function SignInPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Sign in to Coherence Admin</h1>
          <p className="text-gray-600 mt-2">Welcome back! Please sign in to continue.</p>
        </div>
        <SignIn 
          path="/sign-in" 
          routing="path" 
          fallbackRedirectUrl="/admin"
          afterSignInUrl="/admin"
        />
      </div>
    </div>
  );
} 
