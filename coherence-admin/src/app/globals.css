@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Orbitron and JetBrains Mono fonts */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

/* CRFS Component Styles */
.crfs-renderer {
  @apply space-y-4;
}

/* CRFS Message Content */
.crfs-message-content {
  @apply mt-2;
}

/* CRFS Header Controls */
.crfs-header-controls {
  @apply flex justify-between items-center mb-4;
}

.crfs-metadata {
  @apply flex items-center space-x-4;
}

.crfs-view-controls button {
  @apply text-xs px-3 py-1 rounded border transition-all duration-200;
  @apply hover:bg-gray-800/50 focus:outline-none focus:ring-2 focus:ring-cyan-500/50;
}

/* CRFS Section Styles */
.crfs-section {
  @apply mb-6;
}

.crfs-header {
  @apply text-cyan-400 hover:text-cyan-300 transition-colors duration-200;
  @apply flex items-center justify-between cursor-pointer mb-3;
}

.crfs-header h3 {
  @apply text-lg font-semibold;
}

/* CRFS Content Wrappers */
.crfs-content-wrapper {
  @apply relative;
}

/* CRFS JSON Section */
.crfs-json-section pre {
  @apply bg-gray-900/80 border border-purple-500/30 rounded-lg p-4;
  @apply text-green-400 font-mono text-sm overflow-x-auto;
  @apply hover:border-purple-400/50 transition-all duration-300;
  @apply relative;
}

.crfs-json-section pre::before {
  @apply content-['JSON'] absolute top-2 right-2 text-xs;
  @apply text-purple-400 bg-purple-900/30 px-2 py-1 rounded;
}

/* CRFS List Section */
.crfs-list-section ul {
  @apply space-y-2;
}

.crfs-list-section li {
  @apply flex items-start space-x-2;
}

.crfs-list-section li span:first-child {
  @apply text-cyan-400 mt-1;
}

.crfs-list-section li span:last-child {
  @apply text-gray-300;
}

/* CRFS Object Section */
.crfs-object-section {
  @apply space-y-2;
}

.crfs-object-section > div {
  @apply flex items-start space-x-3;
}

.crfs-object-section > div > span:first-child {
  @apply text-purple-400 font-semibold min-w-0 flex-shrink-0;
}

.crfs-object-section > div > span:last-child {
  @apply text-gray-300;
}

/* CRFS Raw View */
.crfs-raw-view pre {
  @apply bg-gray-900/80 border border-purple-500/30 rounded-lg p-4;
  @apply text-green-400 font-mono text-sm overflow-x-auto whitespace-pre-wrap;
  @apply hover:border-purple-400/50 transition-all duration-300;
}

/* CRFS Formatted View */
.crfs-formatted-view {
  @apply space-y-4;
}

.crfs-formatted-fallback .prose {
  @apply prose-invert max-w-none;
}

.crfs-formatted-fallback .prose div {
  @apply text-gray-300 whitespace-pre-wrap;
}

/* CRFS Style Classes */
.crfs-style-success {
  @apply text-green-400 bg-green-900/20 border-l-4 border-green-500 pl-4 rounded-r-lg;
}

.crfs-style-error {
  @apply text-red-400 bg-red-900/20 border-l-4 border-red-500 pl-4 rounded-r-lg;
}

.crfs-style-info {
  @apply text-blue-400 bg-blue-900/20 border-l-4 border-blue-500 pl-4 rounded-r-lg;
}

.crfs-style-warning {
  @apply text-yellow-400 bg-yellow-900/20 border-l-4 border-yellow-500 pl-4 rounded-r-lg;
}

.crfs-style-heading {
  @apply text-2xl font-bold text-cyan-400;
  @apply bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent;
}

/* CRFS Animations */
@keyframes crfs-pulse {
  0%, 100% { 
    opacity: 0.5; 
  }
  50% { 
    opacity: 1; 
  }
}

.crfs-loading {
  @apply animate-pulse;
  animation: crfs-pulse 2s ease-in-out infinite;
}

/* CRFS Code Highlights */
.crfs-highlight {
  @apply bg-yellow-500/20 text-yellow-300 px-1 rounded;
}

/* CRFS Interactive Elements */
.crfs-section.expandable .crfs-header:hover {
  @apply bg-gray-800/30 rounded-lg px-2 py-1 -mx-2 -my-1;
}

/* CRFS Metadata Badges */
.crfs-metadata span {
  @apply text-xs font-mono;
}

.crfs-metadata span.version {
  @apply text-cyan-400;
}

.crfs-metadata span.feature {
  @apply px-2 py-1 rounded;
}

.crfs-metadata span.auto-select {
  @apply text-purple-400 bg-purple-900/20;
}

.crfs-metadata span.streaming {
  @apply text-blue-400 bg-blue-900/20;
}

/* CRFS Content Transitions */
.crfs-content {
  @apply transition-all duration-300;
}

.crfs-section .crfs-content.hidden {
  @apply opacity-0 max-h-0 overflow-hidden;
}

.crfs-section .crfs-content.block {
  @apply opacity-100 max-h-none;
}

/* CRFS Responsive Design */
@media (max-width: 640px) {
  .crfs-header-controls {
    @apply flex-col space-y-2;
  }
  
  .crfs-metadata {
    @apply flex-wrap space-x-2 space-y-1;
  }
  
  .crfs-json-section pre {
    @apply text-xs p-3;
  }
}

/* Base theme variables */
:root {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-jetbrains-mono); 
  --font-display: var(--font-orbitron);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Standardized Card Styles for Admin Pages */
.admin-page {
  @apply min-h-screen bg-background text-foreground;
}

/* Primary content cards - used for main tab content */
.admin-card {
  @apply bg-white dark:bg-gray-900 shadow-lg rounded-xl border border-gray-200 dark:border-gray-800 p-6 md:p-8;
}

/* Card with reduced padding for list views */
.admin-card-compact {
  @apply bg-white dark:bg-gray-900 shadow-lg rounded-xl border border-gray-200 dark:border-gray-800 p-4;
}

/* Secondary cards - used within primary cards */
.admin-card-secondary {
  @apply bg-gray-50 dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 p-5;
}

/* Header card style */
.admin-header {
  @apply bg-white dark:bg-gray-900 rounded-xl p-6 border border-gray-200 dark:border-gray-800 shadow-lg mb-8;
}

/* Tab list styling */
.admin-tabs-list {
  @apply mb-6 bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-1;
}

/* Button styles */
.admin-button-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600;
}

.admin-button-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600;
}

/* Status badges */
.admin-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.admin-badge-blue {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200 dark:border-blue-800;
}

.admin-badge-green {
  @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border border-green-200 dark:border-green-800;
}

.admin-badge-yellow {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800;
}

.admin-badge-red {
  @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200 dark:border-red-800;
}

.admin-badge-purple {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 border border-purple-200 dark:border-purple-800;
}

/* Error/Alert boxes */
.admin-alert-error {
  @apply mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4 rounded-lg;
}

.admin-alert-error-text {
  @apply text-red-700 dark:text-red-400;
}

:root {
  --radius: 0.625rem;
  
  /* Cyber Wave Theme Color Variables */
  --cw-bg-top: #002633;
  --cw-bg-bottom: #000000;
  --cw-accent-left: #007972;
  --cw-accent-right: #7b008f;
  --cw-foreground: #ffffff;
  --cw-n-black: #616161;
  --cw-n-red: #ff8272;
  --cw-n-green: #b4fa72;
  --cw-n-yellow: #fefdc2;
  --cw-n-blue: #a5d5fe;
  --cw-n-magenta: #ff8ffd;
  --cw-n-cyan: #d0d1fe;
  --cw-n-white: #f1f1f1;
  --cw-b-black: #8e8e8e;
  --cw-b-red: #ffc4bd;
  --cw-b-green: #d6fcb9;
  --cw-b-yellow: #fefdd5;
  --cw-b-blue: #c1e3fe;
  --cw-b-magenta: #ffb1fe;
  --cw-b-cyan: #e5e6fe;
  --cw-b-white: #feffff;
  
  /* Light Theme UI Mapping */
  --background: var(--cw-n-white);
  --foreground: var(--cw-bg-top);
  --card: hsl(0 0% 100%);
  --card-foreground: var(--cw-bg-top);
  --popover: hsl(0 0% 100%);
  --popover-foreground: var(--cw-bg-top);
  --primary: var(--cw-accent-left);
  --primary-foreground: var(--cw-foreground);
  --secondary: var(--cw-n-black);
  --secondary-foreground: var(--cw-foreground);
  --muted: var(--cw-n-black);
  --muted-foreground: var(--cw-n-white);
  --accent: var(--cw-accent-right);
  --accent-foreground: var(--cw-foreground);
  --destructive: var(--cw-n-red);
  --border: var(--cw-n-cyan);
  --input: var(--cw-n-white);
  --ring: var(--cw-n-blue);

  --chart-1: var(--cw-n-red);
  --chart-2: var(--cw-n-green);
  --chart-3: var(--cw-n-blue);
  --chart-4: var(--cw-n-magenta);
  --chart-5: var(--cw-n-yellow);

  --sidebar: var(--cw-b-white);
  --sidebar-foreground: var(--cw-bg-top);
  --sidebar-primary: var(--cw-accent-left);
  --sidebar-primary-foreground: var(--cw-foreground);
  --sidebar-accent: var(--cw-accent-right);
  --sidebar-accent-foreground: var(--cw-foreground);
  --sidebar-border: var(--cw-n-cyan);
  --sidebar-ring: var(--cw-n-blue);
  
  /* Font variables */
  --font-jetbrains-mono: 'JetBrains Mono', ui-monospace, monospace;
  --font-orbitron: 'Orbitron', sans-serif;
}

.dark {
  /* Dark Theme UI Mapping */
  --background: var(--cw-bg-bottom);
  --foreground: var(--cw-foreground);
  --card: hsl(240 10% 3.9%);
  --card-foreground: var(--cw-foreground);
  --popover: hsl(240 10% 3.9%);
  --popover-foreground: var(--cw-foreground);
  --primary: var(--cw-accent-left);
  --primary-foreground: var(--cw-foreground);
  --secondary: var(--cw-n-black);
  --secondary-foreground: var(--cw-foreground);
  --muted: var(--cw-n-black);
  --muted-foreground: var(--cw-n-white);
  --accent: var(--cw-accent-right);
  --accent-foreground: var(--cw-foreground);
  --destructive: var(--cw-b-red);
  --border: var(--cw-b-cyan);
  --input: var(--cw-n-black);
  --ring: var(--cw-b-blue);

  /* Table-specific variables */
  --table-bg: var(--cw-bg-top);
  --table-border: var(--cw-b-cyan);
  --table-row-hover: var(--cw-accent-left);
  --table-header-bg: var(--cw-bg-top);
  --table-header-color: var(--cw-foreground);
  
  /* Dialog-specific variables */
  --dialog-bg: var(--cw-bg-top);
  --dialog-border: var(--cw-b-cyan);
  --dialog-shadow: rgba(0, 0, 0, 0.5);
  
  /* Form element variables */
  --form-input-bg: var(--cw-bg-top);
  --form-input-border: var(--cw-b-cyan);
  --form-input-text: var(--cw-foreground);
  --form-input-placeholder: var(--cw-n-black);
  --form-input-focus-ring: var(--cw-accent-left);
  
  /* Button variables */
  --button-primary-bg: var(--cw-accent-left);
  --button-primary-text: var(--cw-foreground);
  --button-secondary-bg: var(--cw-accent-right);
  --button-secondary-text: var(--cw-foreground);
  --button-disabled-bg: var(--cw-n-black);
  --button-disabled-text: var(--cw-n-white);

  /* Chart colors */
  --chart-1: var(--cw-b-red);
  --chart-2: var(--cw-b-green);
  --chart-3: var(--cw-b-blue);
  --chart-4: var(--cw-b-magenta);
  --chart-5: var(--cw-b-yellow);

  /* Sidebar variables */
  --sidebar: var(--cw-bg-top);
  --sidebar-foreground: var(--cw-foreground);
  --sidebar-primary: var(--cw-accent-left);
  --sidebar-primary-foreground: var(--cw-foreground);
  --sidebar-accent: var(--cw-accent-right);
  --sidebar-accent-foreground: var(--cw-foreground);
  --sidebar-border: var(--cw-b-cyan);
  --sidebar-ring: var(--cw-b-blue);
}

@layer base {
  * {
    @apply border-cw-b-cyan;
  }

  body {
    @apply bg-cw-bg-bottom text-cw-foreground font-mono;
  }
}

@layer base {
  h1, h2, h3, h4, h5, h6 {
    @apply font-display tracking-wider;
  }
}

@layer components {
  /* Enhanced Glassmorphism Effects */
  .glass-morphism {
    @apply backdrop-blur-xl shadow-2xl;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.03)
    );
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-morphism-strong {
    @apply backdrop-blur-2xl shadow-2xl;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.15),
      rgba(255, 255, 255, 0.05)
    );
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-morphism-subtle {
    @apply backdrop-blur-lg shadow-xl;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.08),
      rgba(255, 255, 255, 0.02)
    );
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  /* AI-First Visual Elements */
  .ai-processing {
    @apply relative overflow-hidden;
  }

  .ai-processing::before {
    content: '';
    @apply absolute inset-0 opacity-30;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 121, 114, 0.3),
      transparent
    );
    animation: ai-scan 2s linear infinite;
  }

  .ai-status-active {
    @apply relative;
  }

  .ai-status-active::after {
    content: '';
    @apply absolute -top-1 -right-1 w-3 h-3 rounded-full;
    background: radial-gradient(circle, #b4fa72, #007972);
    animation: pulse-ai 1.5s ease-in-out infinite;
  }

  .neural-grid {
    @apply relative;
    background-image: 
      linear-gradient(rgba(0, 121, 114, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 121, 114, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .neural-grid::before {
    content: '';
    @apply absolute inset-0 opacity-20;
    background: radial-gradient(
      circle at 50% 50%,
      rgba(0, 121, 114, 0.1) 0%,
      transparent 70%
    );
  }

  .data-flow {
    @apply relative overflow-hidden;
  }

  .data-flow::after {
    content: '';
    @apply absolute inset-0 opacity-20;
    background: linear-gradient(
      45deg,
      transparent 40%,
      rgba(123, 0, 143, 0.1) 50%,
      transparent 60%
    );
    background-size: 200% 200%;
    animation: data-stream 3s linear infinite;
  }

  /* Enhanced Accent Glow */
  .accent-glow {
    @apply relative;
  }

  .accent-glow::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-tr from-cw-accent-left to-cw-accent-right opacity-0 transition-all duration-700 rounded-md -z-10;
    filter: blur(8px);
    transform: scale(1.05);
  }

  .accent-glow:hover::after {
    @apply opacity-25;
  }

  .accent-glow-strong {
    @apply relative;
  }

  .accent-glow-strong::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-tr from-cw-accent-left to-cw-accent-right opacity-0 transition-all duration-500 rounded-md -z-10;
    filter: blur(12px);
    transform: scale(1.1);
  }

  .accent-glow-strong:hover::after {
    @apply opacity-40;
  }

  /* Legacy Components */
  .card-cyberpunk {
    @apply rounded-md border border-cw-b-cyan bg-cw-bg-top p-4 shadow-md transition-all duration-300;
  }

  .button-cyberpunk {
    @apply rounded-md bg-cw-accent-left text-cw-foreground hover:bg-cw-accent-right border border-cw-accent-left/50 shadow-md;
  }
}

@layer components {
  /* Custom scrollbar styles - CyberWave themed */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #007972 transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #007972, #7b008f);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    opacity: 0.8;
  }
}

@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-ai-scan {
    animation: ai-scan 2s linear infinite;
  }
  
  .animate-pulse-ai {
    animation: pulse-ai 1.5s ease-in-out infinite;
  }
  
  .animate-data-stream {
    animation: data-stream 3s linear infinite;
  }
  
  .animate-neural-pulse {
    animation: neural-pulse 4s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes ai-scan {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse-ai {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes data-stream {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}

@keyframes neural-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

/* Custom prose styles for chat messages */
@layer components {
  .prose.prose-sm h1,
  .prose.prose-sm h2,
  .prose.prose-sm h3,
  .prose.prose-sm h4,
  .prose.prose-sm h5,
  .prose.prose-sm h6 {
    @apply text-cw-b-cyan;
  }
  
  .prose.prose-sm p {
    @apply text-inherit leading-relaxed;
  }
  
  .prose.prose-sm a {
    @apply text-cw-n-blue hover:text-cw-b-blue transition-colors underline;
  }
  
  .prose.prose-sm code {
    @apply bg-black/50 text-cw-n-green px-1 py-0.5 rounded text-xs;
  }
  
  .prose.prose-sm pre {
    @apply bg-black/60 border border-cw-b-cyan/30 rounded-lg p-4 overflow-x-auto;
  }
  
  .prose.prose-sm pre code {
    @apply bg-transparent p-0;
  }
  
  .prose.prose-sm ul,
  .prose.prose-sm ol {
    @apply space-y-1;
  }
  
  .prose.prose-sm li {
    @apply text-inherit;
  }
  
  .prose.prose-sm blockquote {
    @apply border-l-4 border-cw-accent-left pl-4 italic text-cw-b-white/80;
  }
  
  .prose.prose-sm table {
    @apply border-collapse w-full;
  }
  
  .prose.prose-sm th {
    @apply bg-cw-bg-top/50 text-cw-n-cyan border border-cw-b-cyan/30 p-2;
  }
  
  .prose.prose-sm td {
    @apply border border-cw-b-cyan/30 p-2 text-inherit;
  }
  
  .prose.prose-sm strong {
    @apply text-cw-n-yellow font-semibold;
  }
  
  .prose.prose-sm em {
    @apply text-cw-n-magenta italic;
  }
}

/* Chat message animations and effects */
@layer components {
  .chat-message-user {
    animation: slideInFromRight 0.3s ease-out;
    position: relative;
  }
  
  .chat-message-assistant {
    animation: slideInFromLeft 0.3s ease-out;
    position: relative;
  }
  
  .message-bubble-user {
    background-image: none !important; /* Remove any gradient, override utilities */
    background-color: var(--cw-accent-right) !important; /* Solid accent color (purple), override utilities */
    color: var(--cw-foreground);       /* White text */
    position: relative; /* For the ::after pseudo-element tail */
    /* Assuming padding and border-radius are handled by utility classes or existing styles */
  }

  .message-bubble-user::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -6px;
    width: 12px;
    height: 20px;
    background: var(--cw-accent-right); /* Match bubble background (purple) */
    clip-path: path('M 6 0 Q 12 0 12 6 L 12 20 L 0 6 Q 0 0 6 0');
  }

  .message-bubble-user .prose p {
    color: var(--cw-foreground) !important; /* Ensure white text for p tags within user bubble prose */
  }
  
  .message-bubble-assistant::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -6px;
    width: 12px;
    height: 20px;
    background: rgba(0, 0, 0, 0.6);
    clip-path: path('M 6 0 Q 0 0 0 6 L 0 20 L 12 6 Q 12 0 6 0');
    border-left: 1px solid var(--cw-b-cyan);
  }
  
  /* Hover effects for messages */
  .chat-message-user:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease;
  }
  
  .chat-message-assistant:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease;
  }
  
  /* Message spacing */
  .message-group-user {
    margin-bottom: 0.75rem;
  }
  
  .message-group-assistant {
    margin-bottom: 0.75rem;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(1rem);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-1rem);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
