import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '../../context/ThemeProvider';
import { ThemeToggle } from '../../components/ui/ThemeToggle';

jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
    };
  },
}));

describe('Theme System', () => {
  it('renders in dark mode by default', () => {
    render(
      <ThemeProvider>
        <div data-testid="theme-test">Theme Test</div>
        <ThemeToggle />
      </ThemeProvider>
    );
    
    // Verify the toggle button is rendered
    expect(screen.getByRole('button', { name: 'Toggle theme' })).toBeInTheDocument();
    
    // In our implementation, theme is applied via CSS variables which aren't
    // easily testable in Jest, so we're just checking the component renders
    expect(screen.getByTestId('theme-test')).toBeInTheDocument();
  });
});