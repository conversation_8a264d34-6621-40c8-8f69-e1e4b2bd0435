'use client';

import { useState } from 'react';

export default function BackendDebugPage() {
  const [results, setResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testBackendConnection = async () => {
    setIsLoading(true);
    setResults(null);

    const tests = [];

    // Test 1: Check if backend is reachable
    try {
      const response = await fetch(`http://localhost:8001/v1/health`, {
        method: 'GET',
        mode: 'no-cors', // This will help bypass CORS issues for testing
      });
      tests.push({
        name: 'Backend Health Check (8001)',
        status: 'success',
        message: 'Backend appears to be reachable',
      });
    } catch (error) {
      tests.push({
        name: 'Backend Health Check (8001)',
        status: 'error',
        message: `Cannot reach backend: ${error.message}`,
      });
    }

    // Test 2: Check if backend is running on port 8000
    try {
      const response = await fetch(`http://localhost:8000/v1/health`, {
        method: 'GET',
        mode: 'no-cors',
      });
      tests.push({
        name: 'Backend Health Check (8000)',
        status: 'success',
        message: 'Backend appears to be reachable on port 8000',
      });
    } catch (error) {
      tests.push({
        name: 'Backend Health Check (8000)',
        status: 'error',
        message: `Cannot reach backend: ${error.message}`,
      });
    }

    // Test 3: Check API route directly
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        tests.push({
          name: 'Frontend API Route',
          status: 'success',
          message: 'API route responded successfully',
          data: await response.json(),
        });
      } else {
        const errorData = await response.json();
        tests.push({
          name: 'Frontend API Route',
          status: 'warning',
          message: `API route returned ${response.status}`,
          data: errorData,
        });
      }
    } catch (error) {
      tests.push({
        name: 'Frontend API Route',
        status: 'error',
        message: `API route error: ${error.message}`,
      });
    }

    setResults({
      timestamp: new Date().toISOString(),
      tests,
      environment: {
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
        // Add other relevant env vars
      },
    });

    setIsLoading(false);
  };

  const checkDockerServices = async () => {
    setIsLoading(true);
    const dockerTests = [];

    // This would need to be implemented with actual Docker API calls
    // For now, just show instructions
    dockerTests.push({
      name: 'Docker Services Check',
      status: 'info',
      message: 'Please run: docker-compose ps',
      instructions: [
        'Run "docker-compose ps" in your terminal',
        'Check if coherence-api service is running',
        'Ensure ports 8000 or 8001 are mapped correctly',
      ],
    });

    setResults({
      timestamp: new Date().toISOString(),
      tests: dockerTests,
    });
    setIsLoading(false);
  };

  return (
    <div className="max-w-4xl mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Backend Connection Diagnostics</h1>
      
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Quick Tests</h2>
          <div className="flex gap-4">
            <button
              onClick={testBackendConnection}
              disabled={isLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Testing...' : 'Test Backend Connection'}
            </button>
            
            <button
              onClick={checkDockerServices}
              disabled={isLoading}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
            >
              Check Docker Services
            </button>
          </div>
        </div>

        {results && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <p className="text-sm text-gray-600 mb-4">Executed at: {results.timestamp}</p>
            
            <div className="space-y-4">
              {results.tests.map((test, index) => (
                <div key={index} className="border rounded p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className={`w-3 h-3 rounded-full ${
                      test.status === 'success' ? 'bg-green-500' :
                      test.status === 'warning' ? 'bg-yellow-500' :
                      test.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                    }`}></span>
                    <h3 className="font-medium">{test.name}</h3>
                  </div>
                  <p className="text-sm text-gray-700 mb-2">{test.message}</p>
                  
                  {test.instructions && (
                    <div className="bg-blue-50 p-3 rounded">
                      <p className="font-medium text-blue-800 mb-2">Instructions:</p>
                      <ul className="text-blue-700 text-sm space-y-1">
                        {test.instructions.map((instruction, i) => (
                          <li key={i}>• {instruction}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {test.data && (
                    <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto">
                      {JSON.stringify(test.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))}
            </div>

            {results.environment && (
              <div className="mt-6 p-4 bg-gray-50 rounded">
                <h3 className="font-medium mb-2">Environment Variables</h3>
                <pre className="text-xs">{JSON.stringify(results.environment, null, 2)}</pre>
              </div>
            )}
          </div>
        )}

        <div className="bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-400">
          <h2 className="text-xl font-semibold mb-4">Common Issues & Solutions</h2>
          <div className="space-y-3 text-sm">
            <div>
              <h3 className="font-medium">1. Backend Not Running</h3>
              <p>The coherence backend API needs to be running. Check if it's running with:</p>
              <code className="bg-gray-200 px-2 py-1 rounded">docker-compose ps</code>
            </div>
            
            <div>
              <h3 className="font-medium">2. Port Mismatch</h3>
              <p>The environment variable shows port 8001, but the backend might be on 8000.</p>
              <p>Check your docker-compose.yml file for the correct port mapping.</p>
            </div>
            
            <div>
              <h3 className="font-medium">3. JWT Template Issues</h3>
              <p>The backend expects a specific JWT format. Make sure Clerk is configured with the right template.</p>
            </div>

            <div>
              <h3 className="font-medium">4. Quick Fix</h3>
              <p>Try restarting the services:</p>
              <code className="bg-gray-200 px-2 py-1 rounded block mt-1">docker-compose down && docker-compose up -d</code>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-400">
          <h2 className="text-xl font-semibold mb-4">Manual Checks</h2>
          <div className="space-y-2 text-sm">
            <p>1. Run in terminal: <code className="bg-white px-2 py-1 rounded">curl http://localhost:8001/v1/health</code></p>
            <p>2. Also try: <code className="bg-white px-2 py-1 rounded">curl http://localhost:8000/v1/health</code></p>
            <p>3. Check docker services: <code className="bg-white px-2 py-1 rounded">docker-compose ps</code></p>
            <p>4. Check logs: <code className="bg-white px-2 py-1 rounded">docker-compose logs coherence-api</code></p>
          </div>
        </div>
      </div>
    </div>
  );
}
