'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useState } from 'react';

export default function TokenDebugPage() {
  const { getToken, isLoaded, isSignedIn, userId, sessionId, orgId, orgRole } = useAuth();
  const { user } = useUser();
  const [tokenInfo, setTokenInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const analyzeToken = async () => {
    setLoading(true);
    
    try {
      // Get tokens
      const templateToken = await getToken({ template: 'coherence_session' });
      const defaultToken = await getToken();
      
      const analysis = {
        clerk_state: {
          isLoaded,
          isSignedIn,
          userId,
          sessionId,
          orgId,
          orgRole,
          user_email: user?.emailAddresses?.[0]?.emailAddress,
        },
        tokens: {
          template_token: templateToken ? {
            exists: true,
            length: templateToken.length,
            preview: templateToken.substring(0, 50) + '...',
          } : { exists: false },
          default_token: defaultToken ? {
            exists: true,
            length: defaultToken.length,
            preview: defaultToken.substring(0, 50) + '...',
          } : { exists: false },
        },
        decoded_tokens: {},
      };

      // Decode tokens if they exist
      if (templateToken) {
        try {
          const parts = templateToken.split('.');
          if (parts.length === 3) {
            const payload = JSON.parse(atob(parts[1]));
            analysis.decoded_tokens.template = {
              iss: payload.iss,
              sub: payload.sub,
              aud: payload.aud,
              exp: payload.exp,
              iat: payload.iat,
              org_id: payload.org_id,
              org_name: payload.org_name,
              org_role: payload.org_role,
              org_slug: payload.org_slug,
              permissions: payload.permissions,
              // Check for template variables
              has_templates: {
                org_id: payload.org_id?.includes?.('{{') || false,
                org_name: payload.org_name?.includes?.('{{') || false,
                org_role: payload.org_role?.includes?.('{{') || false,
              }
            };
          }
        } catch (e) {
          analysis.decoded_tokens.template_error = e.message;
        }
      }

      if (defaultToken) {
        try {
          const parts = defaultToken.split('.');
          if (parts.length === 3) {
            const payload = JSON.parse(atob(parts[1]));
            analysis.decoded_tokens.default = {
              iss: payload.iss,
              sub: payload.sub,
              aud: payload.aud,
              exp: payload.exp,
              iat: payload.iat,
              // Add other relevant fields if present
            };
          }
        } catch (e) {
          analysis.decoded_tokens.default_error = e.message;
        }
      }

      setTokenInfo(analysis);
    } catch (error) {
      setTokenInfo({ error: error.message });
    }
    
    setLoading(false);
  };

  const testApiCall = async () => {
    try {
      const response = await fetch('/api/auth/me');
      const data = await response.json();
      
      console.log('API Response:', {
        status: response.status,
        ok: response.ok,
        data
      });
      
      alert(`API Response: ${response.status} - ${response.ok ? 'Success' : 'Error'}`);
    } catch (error) {
      console.error('API Error:', error);
      alert(`API Error: ${error.message}`);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Token Analysis Tool</h1>
      
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="flex gap-4">
            <button
              onClick={analyzeToken}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Analyzing...' : 'Analyze JWT Tokens'}
            </button>
            
            <button
              onClick={testApiCall}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Test API Call
            </button>
          </div>
        </div>

        {tokenInfo && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Token Analysis Results</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-[500px]">
              {JSON.stringify(tokenInfo, null, 2)}
            </pre>
          </div>
        )}

        <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-400">
          <h2 className="text-xl font-semibold mb-4">What to Look For</h2>
          <ul className="space-y-2 text-sm">
            <li>• Check if both template and default tokens exist</li>
            <li>• Verify that org_id, org_name, and org_role are not template variables like {`{{org_id}}`}</li>
            <li>• Ensure the sub (subject) field contains your user ID</li>
            <li>• Check that the organization fields match what you selected</li>
            <li>• Look for any errors in token generation or decoding</li>
          </ul>
        </div>

        <div className="bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-400">
          <h2 className="text-xl font-semibold mb-4">Common Issues</h2>
          <div className="space-y-3 text-sm">
            <div>
              <h3 className="font-medium">Template Variables Still Present</h3>
              <p>If you see {`{{org_id}}`} or similar in the token, it means the Clerk JWT template isn't properly configured.</p>
            </div>
            
            <div>
              <h3 className="font-medium">Missing Organization Data</h3>
              <p>If org_id is null or undefined, make sure you've selected an organization in Clerk.</p>
            </div>
            
            <div>
              <h3 className="font-medium">Token Generation Fails</h3>
              <p>If no tokens are generated, there might be a Clerk configuration issue.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
