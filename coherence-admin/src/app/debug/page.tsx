'use client';

import { useAuth, useOrganization } from '@clerk/nextjs';

export default function DebugPage() {
  const { isSignedIn, userId, orgId, sessionId, getToken, actor } = useAuth();
  const { organization, membership } = useOrganization();

  const handleDebugToken = async () => {
    console.log('=== AUTH DEBUG INFO ===');
    console.log('isSignedIn:', isSignedIn);
    console.log('userId:', userId);
    console.log('orgId:', orgId);
    console.log('sessionId:', sessionId);
    console.log('actor:', actor);
    console.log('organization:', organization);
    console.log('membership:', membership);
    
    // Try getting token with template
    try {
      console.log('Attempting to get token with template...');
      const tokenWithTemplate = await getToken({ template: 'default' });
      console.log('Token with template (first 50 chars):', tokenWithTemplate?.substring(0, 50));
      
      if (tokenWithTemplate) {
        // Decode and log claims
        const parts = tokenWithTemplate.split('.');
        if (parts.length === 3) {
          const payload = JSON.parse(atob(parts[1]));
          console.log('JWT claims:', payload);
          console.log('org_id:', payload.org_id);
          console.log('org_name:', payload.org_name);
          console.log('org_role:', payload.org_role);
          console.log('org_slug:', payload.org_slug);
        }
      }
    } catch (error) {
      console.error('Error getting token with template:', error);
    }
    
    // Try getting token without template
    try {
      console.log('Attempting to get token without template...');
      const tokenWithoutTemplate = await getToken();
      console.log('Token without template (first 50 chars):', tokenWithoutTemplate?.substring(0, 50));
      
      if (tokenWithoutTemplate) {
        // Decode and log claims
        const parts = tokenWithoutTemplate.split('.');
        if (parts.length === 3) {
          const payload = JSON.parse(atob(parts[1]));
          console.log('JWT claims (no template):', payload);
        }
      }
    } catch (error) {
      console.error('Error getting token without template:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6">Auth Debug Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-gray-50 p-4 rounded">
            <h2 className="font-semibold mb-2">Auth State</h2>
            <p><strong>Signed In:</strong> {isSignedIn ? 'Yes' : 'No'}</p>
            <p><strong>User ID:</strong> {userId || 'N/A'}</p>
            <p><strong>Org ID:</strong> {orgId || 'N/A'}</p>
            <p><strong>Session ID:</strong> {sessionId || 'N/A'}</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded">
            <h2 className="font-semibold mb-2">Organization</h2>
            <p><strong>Name:</strong> {organization?.name || 'N/A'}</p>
            <p><strong>ID:</strong> {organization?.id || 'N/A'}</p>
            <p><strong>Slug:</strong> {organization?.slug || 'N/A'}</p>
            <p><strong>Role:</strong> {membership?.role || 'N/A'}</p>
          </div>
        </div>
        
        <button 
          onClick={handleDebugToken}
          className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 transition-colors"
        >
          Debug Token (Check Console)
        </button>
        
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-sm text-yellow-800">
            Click the button above and check the browser console (F12) for detailed debug information.
          </p>
        </div>
      </div>
    </div>
  );
}
