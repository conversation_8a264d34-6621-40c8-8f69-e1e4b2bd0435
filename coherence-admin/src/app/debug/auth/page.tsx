'use client';

import { useAuth, useUser, useClerk } from '@clerk/nextjs';
import { useAdminSession } from '@/context/AdminSessionContext';
import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function AuthDebugPage() {
  const clerkAuth = useAuth();
  const adminSession = useAdminSession();
  const { user } = useUser();
  const { session } = useClerk();
  
  const [tokenInfo, setTokenInfo] = useState<any>(null);
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [decodedToken, setDecodedToken] = useState<any>(null);
  const [debugApiResponse, setDebugApiResponse] = useState<any>(null);

  const checkToken = async () => {
    try {
      if (clerkAuth.getToken) {
        // Try with template from environment variable
        const templateName = process.env.NEXT_PUBLIC_CLERK_SESSION_TOKEN_TEMPLATE || 'coherence_session';
        console.log(`Trying to get token with template: ${templateName}`);
        const tokenWithTemplate = await clerkAuth.getToken({ template: templateName });
        const tokenDefault = await clerkAuth.getToken();
        
        // Try to decode the template token if available
        let decodedData = null;
        if (tokenWithTemplate) {
          try {
            // Parse JWT token
            const parts = tokenWithTemplate.split('.');
            if (parts.length === 3) {
              const payload = JSON.parse(atob(parts[1]));
              decodedData = payload;
              setDecodedToken(payload);
            }
          } catch (e) {
            console.error('Error decoding token:', e);
          }
        }
        
        setTokenInfo({
          templateName,
          templateToken: tokenWithTemplate ? 'Available' : 'Not available',
          defaultToken: tokenDefault ? 'Available' : 'Not available',
          templateTokenLength: tokenWithTemplate?.length || 0,
          defaultTokenLength: tokenDefault?.length || 0,
          tokenPreview: tokenWithTemplate ? `${tokenWithTemplate.substring(0, 20)}...` : 'N/A',
          decodingSuccessful: !!decodedData,
          hasSystemAdminFlag: decodedData?.is_system_admin || decodedData?.__session?.is_system_admin,
        });
      }
    } catch (error) {
      setTokenInfo({ error: (error as Error).message });
    }
  };

  const testApiCall = async () => {
    try {
      const response = await fetch('/api/auth/me');
      const data = await response.json();
      setApiResponse({
        status: response.status,
        data,
        ok: response.ok
      });
    } catch (error) {
      setApiResponse({ error: (error as Error).message });
    }
  };
  
  const fetchDebugToken = async () => {
    try {
      const response = await fetch('/api/debug-token');
      if (response.ok) {
        const data = await response.json();
        setDebugApiResponse(data);
      } else {
        setDebugApiResponse({ error: response.statusText });
      }
    } catch (error) {
      setDebugApiResponse({ error: (error as Error).message });
    }
  };
  
  const showAllPermissions = () => {
    if (!adminSession.permissions || adminSession.permissions.length === 0) {
      return <p className="text-red-500">No permissions available</p>;
    }
    
    return (
      <ul className="text-sm max-h-40 overflow-auto">
        {adminSession.permissions.map((perm, i) => (
          <li key={i} className={perm === 'system:*' ? 'text-green-600 font-bold' : ''}>
            {perm}
          </li>
        ))}
      </ul>
    );
  };

  // Check if user is a system admin based on permissions
  const isSystemAdmin = adminSession.permissions?.includes('system:*') || false;

  return (
    <div className="max-w-4xl mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Authentication Debug Page</h1>
      
      {/* System Admin Status Panel */}
      <div className="mb-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">System Admin Status (Canonical)</h2>
        <p>
          Canonical System Admin Status (from permissions): {' '}
          <span className={isSystemAdmin ? 'text-green-600 font-bold' : 'text-red-600'}>
            {isSystemAdmin ? 'TRUE' : 'FALSE'}
          </span>
        </p>
        <p className="text-sm mt-2">
          This value is determined by checking for the <code>system:*</code> permission in the permissions array from the <code>/api/auth/me</code> response.
        </p>
      </div>
      
      <div className="grid gap-6">
        {/* Clerk Auth State */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Clerk Authentication State</h2>
          <div className="space-y-2">
            <div>Is Loaded: <span className={clerkAuth.isLoaded ? 'text-green-600' : 'text-red-600'}>{clerkAuth.isLoaded ? 'Yes' : 'No'}</span></div>
            <div>Is Signed In: <span className={clerkAuth.isSignedIn ? 'text-green-600' : 'text-red-600'}>{clerkAuth.isSignedIn ? 'Yes' : 'No'}</span></div>
            <div>User ID: <span className="font-mono">{clerkAuth.userId || 'None'}</span></div>
            <div>Session ID: <span className="font-mono">{clerkAuth.sessionId || 'None'}</span></div>
            <div>Organization ID: <span className="font-mono">{clerkAuth.orgId || 'None'}</span></div>
            <div>Organization Role: <span className="font-mono">{clerkAuth.orgRole || 'None'}</span></div>
            
            {user && (
              <div className="mt-4">
                <h3 className="font-medium">Public Metadata:</h3>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto max-h-20">
                  {JSON.stringify(user.publicMetadata, null, 2) || 'None'}
                </pre>
              </div>
            )}
          </div>
          
          <button 
            onClick={checkToken}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded"
          >
            Check Token
          </button>
          
          {tokenInfo && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-semibold">Token Information:</h3>
              <pre className="text-sm mt-2 overflow-auto max-h-40">{JSON.stringify(tokenInfo, null, 2)}</pre>
            </div>
          )}
        </div>

        {/* Admin Session State */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Admin Session State</h2>
          <div className="space-y-2">
            <div>Is Loading: <span className={adminSession.isLoading ? 'text-yellow-600' : 'text-green-600'}>{adminSession.isLoading ? 'Yes' : 'No'}</span></div>
            <div>Is Authenticated: <span className={adminSession.isAuthenticated ? 'text-green-600' : 'text-red-600'}>{adminSession.isAuthenticated ? 'Yes' : 'No'}</span></div>
            <div>Error: <span className={adminSession.error ? 'text-red-600' : 'text-green-600'}>{adminSession.error?.message || 'None'}</span></div>
            <div>Organization: <span className="font-mono">{adminSession.organization?.name || 'None'}</span></div>
            <div>Tenant: <span className="font-mono">{adminSession.tenant?.name || 'None'}</span></div>
            
            <div className="mt-2">
              <h3 className="font-medium mb-1">Permissions: ({adminSession.permissions?.length || 0})</h3>
              {showAllPermissions()}
            </div>
            
            {adminSession.organization && (
              <div className="mt-2">
                <h3 className="font-medium mb-1">Organization Object:</h3>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-20">
                  {JSON.stringify(adminSession.organization, null, 2)}
                </pre>
              </div>
            )}
            
            {adminSession.actor && (
              <div className="mt-2">
                <h3 className="font-medium mb-1">Actor Object:</h3>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-20">
                  {JSON.stringify(adminSession.actor, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* Debug Tools */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Debug Tools</h2>
          
          <div className="flex flex-wrap gap-2 mb-4">
            <button 
              onClick={testApiCall}
              className="bg-green-600 text-white px-4 py-2 rounded"
            >
              Test /api/auth/me
            </button>
            
            <button 
              onClick={fetchDebugToken}
              className="bg-purple-600 text-white px-4 py-2 rounded"
            >
              Fetch Debug Token
            </button>
            
            <Link href="/org-selection" className="bg-yellow-600 text-white px-4 py-2 rounded">
              Go to Org Selection
            </Link>
          </div>
          
          {apiResponse && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-semibold">/api/auth/me Response:</h3>
              <pre className="text-sm mt-2 overflow-auto max-h-40">{JSON.stringify(apiResponse, null, 2)}</pre>
            </div>
          )}
          
          {debugApiResponse && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-semibold">/api/debug-token Response:</h3>
              <pre className="text-sm mt-2 overflow-auto max-h-40">{JSON.stringify(debugApiResponse, null, 2)}</pre>
            </div>
          )}
        </div>

        {/* Environment Variables */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Environment Configuration</h2>
          <div className="space-y-2 text-sm">
            <div>API URL: <span className="font-mono">{process.env.NEXT_PUBLIC_API_URL || 'Not set'}</span></div>
            <div>Clerk Publishable Key: <span className="font-mono">{process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY ? 'Set' : 'Not set'}</span></div>
            <div>JWT Template: <span className="font-mono">{process.env.NEXT_PUBLIC_CLERK_SESSION_TOKEN_TEMPLATE || 'coherence_session (default)'}</span></div>
            <div>App URL: <span className="font-mono">{process.env.NEXT_PUBLIC_APP_URL || 'Not set'}</span></div>
            <div>After Sign In URL: <span className="font-mono">{process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL || 'Not set'}</span></div>
          </div>
        </div>
      </div>
    </div>
  );
}
