import { OrganizationList } from "@clerk/nextjs";

export default function OrgSelectionPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8 bg-gradient-to-br from-cw-bg-top to-cw-bg-bottom">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-display font-bold text-cw-foreground mb-4">
            Select Organization
          </h1>
          <p className="text-muted-foreground">
            Choose an organization to continue to the admin dashboard.
          </p>
        </div>

        <div className="bg-card/90 backdrop-blur-sm border border-cw-b-cyan rounded-lg p-6 shadow-xl">
          <h2 className="text-lg font-semibold mb-4 text-cw-foreground">
            Your Organizations
          </h2>
          <OrganizationList
            hidePersonal={true}
            afterSelectOrganizationUrl="/"
            appearance={{
              elements: {
                organizationPreview: "border border-border rounded-md p-3 mb-3 hover:bg-accent/10 transition-colors",
                organizationPreviewAvatarBox: "mr-3",
                organizationPreviewMainIdentifier: "text-foreground font-medium",
                organizationPreviewSecondaryIdentifier: "text-muted-foreground text-sm",
              }
            }}
          />
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-muted-foreground">
            Need to create a new organization?{' '}
            <a 
              href="https://dashboard.clerk.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-primary hover:text-accent transition-colors underline"
            >
              Visit Clerk Dashboard
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}