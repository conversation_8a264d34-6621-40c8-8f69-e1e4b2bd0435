# Cyber Wave Design System PR

## Description
Implements the Cyber Wave design system across the Admin site, providing a consistent, futuristic UI theme based on Warp Terminal's Cyber Wave theme.

## Changes
- [ ] Added Cyber Wave color palette to `tailwind.config.js`
- [ ] Added CSS variables and font imports to `globals.css`
- [ ] Updated layout components with Cyber Wave styling
- [ ] Enhanced UI components with theme-aware styling
- [ ] Updated pages to use the new design system
- [ ] Added visual testing for the theme
- [ ] Updated documentation

## Testing
- [ ] Verified theme works in both light and dark modes
- [ ] Confirmed accessibility standards are maintained
- [ ] Ran visual tests with <PERSON>wright
- [ ] Tested across Chrome, Firefox, and Safari

## Screenshots
[Add before/after screenshots showing the theme implementation]

## Notes
The Cyber Wave theme provides a distinctive, futuristic aesthetic while maintaining full functionality of the admin interface.