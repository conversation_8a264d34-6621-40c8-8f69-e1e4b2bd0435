name: Cyber Wave Theme Check

on:
  push:
    branches: [ main, feature/* ]
  pull_request:
    branches: [ main ]

jobs:
  theme-check:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
      
    - name: Verify Tailwind plugins
      run: |
        if ! grep -q "@tailwindcss/typography" package.json; then
          echo "Required plugin @tailwindcss/typography is missing"
          exit 1
        fi
        if ! grep -q "tailwindcss-animate" package.json; then
          echo "Required plugin tailwindcss-animate is missing"
          exit 1
        fi
    
    - name: Run PurgeCSS check
      run: npm run purge:check
      
    - name: Check unused classes percentage
      run: |
        ORIGINAL_SIZE=$(wc -c < ./src/app/globals.css)
        PURGED_SIZE=$(wc -c < ./dist/build.css)
        UNUSED_PERCENT=$(echo "scale=2; 100 - ($PURGED_SIZE * 100 / $ORIGINAL_SIZE)" | bc)
        echo "Unused CSS: $UNUSED_PERCENT%"
        
        if (( $(echo "$UNUSED_PERCENT > 5" | bc -l) )); then
          echo "Warning: More than 5% of CSS classes are unused"
          exit 1
        fi
    
    - name: Run tests
      run: npm run test
      
    - name: Install Playwright browsers
      run: npx playwright install --with-deps
      
    - name: Run Playwright tests
      run: npx playwright test