# API Keys Proxy Cleanup

This document outlines the cleanup steps needed after fixing the API keys management page by switching from the direct proxy routes to the standard Clerk-authenticated proxy routes.

## Immediate Tasks (Done)

- [x] Update `ApiKeysPage` to use standard proxy routes instead of direct routes
- [x] Mark direct proxy routes as deprecated with 410 Gone responses

## Short-term Tasks (Next Sprint)

- [ ] Delete the deprecated direct proxy route files:
  - Delete `/src/app/api/proxy/admin/api-keys-direct/route.ts`
  - Delete `/src/app/api/proxy/admin/api-keys-direct/[id]/route.ts`
  - Delete the `/src/app/api/proxy/admin/api-keys-direct/` directory if empty

## Long-term Tasks (Backlog)

- [ ] Add unit tests for API keys management:
  - "GET /admin/api-keys returns 200 when Clerk auth cookie present"
  - "POST /admin/api-keys creates a new API key and returns the full key value once"
  - "DELETE /admin/api-keys/{id} revokes an API key"

- [ ] Enforce `X-Tenant-ID` middleware on every admin proxy route for consistent multitenancy boundary

- [ ] Document the supported headers for internal proxies in `docs/architecture.md` for on-boarding & future maintenance

- [ ] Consolidate proxy patterns into a shared middleware that handles:
  - Authentication via Clerk
  - Tenant ID extraction and validation
  - Error handling and formatting
  - Consistent logging

## Security Considerations

- Direct proxy routes posed a security risk by exposing JWT tokens in URL query parameters
- Standard proxy routes use Clerk's `getAuth()` which extracts authentication information from HTTP headers and cookies securely
- This change improves security by preventing JWT token leakage in server logs and browser history

## Migration Impact

- Zero impact for end users
- Improved security and maintainability
- Simplified code with less duplication
- Consistent authentication pattern across the admin interface