/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: "class",
  content: [
    "./src/app/**/*.{js,ts,jsx,tsx}",
    "./src/components/**/*.{js,ts,jsx,tsx}",
    "./src/lib/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Direct color definitions for easier access
        "cw-bg-top": "#002633",
        "cw-bg-bottom": "#000000",
        "cw-accent-left": "#007972",
        "cw-accent-right": "#7b008f",
        "cw-foreground": "#ffffff",
        
        // Normal colors
        "cw-n-black": "#616161",
        "cw-n-red": "#ff8272",
        "cw-n-green": "#b4fa72",
        "cw-n-yellow": "#fefdc2",
        "cw-n-blue": "#a5d5fe",
        "cw-n-magenta": "#ff8ffd",
        "cw-n-cyan": "#d0d1fe",
        "cw-n-white": "#f1f1f1",
        
        // Bright colors
        "cw-b-black": "#8e8e8e",
        "cw-b-red": "#ffc4bd",
        "cw-b-green": "#d6fcb9",
        "cw-b-yellow": "#fefdd5",
        "cw-b-blue": "#c1e3fe",
        "cw-b-magenta": "#ffb1fe",
        "cw-b-cyan": "#e5e6fe",
        "cw-b-white": "#feffff",
        
        // Standard theme mappings
        border: "#e5e6fe", // Using cw-b-cyan
        background: "#000000", // Using cw-bg-bottom
        foreground: "#ffffff", // Using cw-foreground
        primary: "#007972", // Using cw-accent-left
        "primary-foreground": "#ffffff",
        secondary: "#7b008f", // Using cw-accent-right
        "secondary-foreground": "#ffffff",
      },
      fontFamily: {
        mono: ["JetBrains Mono", "ui-monospace", "monospace"],
        display: ["Orbitron", "sans-serif"],
      },
      backgroundImage: {
        "gradient-cw": "linear-gradient(180deg, #002633, #000000)",
        "gradient-cw-accent": "linear-gradient(135deg, #007972, #7b008f)",
      },
      animation: {
        "pulse-accent": "pulse-accent 4s ease-in-out infinite",
        "wave-slide": "wave-slide 8s linear infinite",
      },
      keyframes: {
        "pulse-accent": {
          "0%, 100%": { opacity: 1 },
          "50%": { opacity: 0.7 },
        },
        "wave-slide": {
          "0%": { transform: "translateX(0%)" },
          "100%": { transform: "translateX(50%)" },
        },
      },
      // Fix for graphics being too large
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
        '5xl': '3rem',
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/typography"),
  ],
};