import { clerkMiddleware, ClerkMiddlewareAuth, createRouteMatcher } from '@clerk/nextjs/server';
import { NextFetchEvent, NextRequest, NextResponse } from 'next/server';

// Define routes that are publicly accessible (no authentication required)
const publicRoutesArray = [
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/org-selection', // Org selection page needs to be public for users to land on it
  '/api/auth/(.*)', // API routes for authentication (e.g., Clerk webhooks)
  '/auth-fix', // Auth debugging page
  '/debug/(.*)', // Debug routes
  // Add any other truly public routes here if they exist
];

// Create a route matcher function based on the publicRoutesArray
const isPublicRoute = createRouteMatcher(publicRoutesArray);

export default clerkMiddleware(async (auth: ClerkMiddlewareAuth, req: NextRequest, evt: NextFetchEvent) => {
  // Call auth() to get the authentication state object.
  const authState = await auth();
  const requestHeaders = new Headers(req.headers);
  const { pathname } = req.nextUrl;

  console.log(`[Middleware] Processing ${pathname}, Authenticated: ${!!authState.userId}`);

  // If a user is authenticated, get the custom session token and set it in headers
  if (authState.userId && authState.getToken) {
    try {
      // Try template first, fallback to default
      let jwt: string | null = null;
      try {
        jwt = await authState.getToken({ template: "coherence_session" });
        console.log(`[Middleware] Template token: ${jwt ? 'SUCCESS' : 'FAILED'}`);
      } catch (templateErr) {
        console.log('[Middleware] Template token failed, trying default');
        jwt = await authState.getToken();
        console.log(`[Middleware] Default token: ${jwt ? 'SUCCESS' : 'FAILED'}`);
      }
      
      if (jwt) {
        requestHeaders.set("Authorization", `Bearer ${jwt}`);
        console.log(`[Middleware] Set Authorization header (length: ${jwt.length})`);
      } else {
        console.log('[Middleware] No token could be obtained');
      }
    } catch (err: any) {
      console.error("[Middleware] Error fetching token:", err);
    }
  }
  
  // Use our own isPublicRoute checker function
  const currentPathIsActuallyPublic = isPublicRoute(req);

  // Debug routes should be accessible even without full auth
  if (pathname.startsWith('/debug') || pathname.startsWith('/auth-fix')) {
    console.log('[Middleware] Allowing debug/auth-fix route');
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }

  // If the user is not authenticated and not on a public page, redirect to sign-in
  if (!authState.userId && !currentPathIsActuallyPublic) {
    console.log('[Middleware] Redirecting unauthenticated user to sign-in');
    if (authState.redirectToSignIn) {
      return authState.redirectToSignIn({ returnBackUrl: req.url });
    } else {
      const signInUrl = new URL('/sign-in', req.url);
      signInUrl.searchParams.set('redirect_url', req.url);
      return NextResponse.redirect(signInUrl);
    }
  }

  // Don't redirect if already on /org-selection
  if (pathname === '/org-selection') {
    console.log('[Middleware] On org-selection page, allowing');
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }

  // If user is authenticated but not on a public route and not already on org-selection,
  // check if they need organization selection
  if (authState.userId && !currentPathIsActuallyPublic && pathname !== '/org-selection') {
    // Only redirect to org-selection if user doesn't have an organization
    if (!authState.orgId) {
      console.log('[Middleware] User has no organization, redirecting to org-selection');
      const orgSelectionUrl = new URL('/org-selection', req.url);
      return NextResponse.redirect(orgSelectionUrl);
    }
  }
  
  // Allow requests to continue
  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
  return response;
});

export const config = {
  // Match all routes except static files and Next.js internals.
  matcher: ["/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"],
};