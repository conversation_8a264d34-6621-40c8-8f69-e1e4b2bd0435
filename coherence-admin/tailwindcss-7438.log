2025-05-12T13:41:35.466640Z  INFO tailwindcss_oxide::scanner: Provided sources:
2025-05-12T13:41:35.466744Z  INFO tailwindcss_oxide::scanner: Source: PublicSourceEntry { base: "/Users/<USER>/Documents/projects/coherence/coherence-admin", pattern: "**/*", negated: false }
2025-05-12T13:41:35.467220Z  INFO tailwindcss_oxide::scanner: Optimized sources:
2025-05-12T13:41:35.467227Z  INFO tailwindcss_oxide::scanner: Source: Auto { base: "/Users/<USER>/Documents/projects/coherence/coherence-admin" }
2025-05-12T13:41:35.469594Z  INFO scan_sources: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.470380Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/postcss.config.mjs"
2025-05-12T13:41:35.470400Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/Dockerfile"
2025-05-12T13:41:35.470591Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/README.md"
2025-05-12T13:41:35.470608Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/.dockerignore"
2025-05-12T13:41:35.470626Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/public"
2025-05-12T13:41:35.470650Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/public/file.svg"
2025-05-12T13:41:35.470662Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/public/vercel.svg"
2025-05-12T13:41:35.470673Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/public/next.svg"
2025-05-12T13:41:35.470682Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/public/globe.svg"
2025-05-12T13:41:35.470691Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/public/window.svg"
2025-05-12T13:41:35.470710Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/package.json"
2025-05-12T13:41:35.470721Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/components.json"
2025-05-12T13:41:35.470732Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/tsconfig.json"
2025-05-12T13:41:35.470742Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/eslint.config.mjs"
2025-05-12T13:41:35.470752Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/next.config.ts"
2025-05-12T13:41:35.470771Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src"
2025-05-12T13:41:35.470793Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/middleware.ts"
2025-05-12T13:41:35.470809Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/context"
2025-05-12T13:41:35.470830Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/context/AdminSessionContext.tsx"
2025-05-12T13:41:35.470846Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app"
2025-05-12T13:41:35.470871Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-up"
2025-05-12T13:41:35.470902Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-up/[[...sign-up]]"
2025-05-12T13:41:35.470921Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-up/[[...sign-up]]/page.tsx"
2025-05-12T13:41:35.470941Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin"
2025-05-12T13:41:35.470965Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/organizations"
2025-05-12T13:41:35.470987Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/organizations/[id]"
2025-05-12T13:41:35.471006Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/organizations/[id]/page.tsx"
2025-05-12T13:41:35.471023Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/workflows"
2025-05-12T13:41:35.471040Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/workflows/page.tsx"
2025-05-12T13:41:35.471056Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/workflows/create"
2025-05-12T13:41:35.471072Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/workflows/create/page.tsx"
2025-05-12T13:41:35.471088Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations"
2025-05-12T13:41:35.471111Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/[id]"
2025-05-12T13:41:35.471128Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/[id]/page.tsx"
2025-05-12T13:41:35.471145Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/import"
2025-05-12T13:41:35.471161Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/import/page.tsx"
2025-05-12T13:41:35.471174Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/page.tsx"
2025-05-12T13:41:35.471188Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/layout.tsx"
2025-05-12T13:41:35.471201Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/templates"
2025-05-12T13:41:35.471216Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/templates/page.tsx"
2025-05-12T13:41:35.471226Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/page.tsx"
2025-05-12T13:41:35.471240Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/tenants"
2025-05-12T13:41:35.471262Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/tenants/[id]"
2025-05-12T13:41:35.471281Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/tenants/[id]/page.tsx"
2025-05-12T13:41:35.471297Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/profile"
2025-05-12T13:41:35.471317Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/profile/page.tsx"
2025-05-12T13:41:35.471332Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/layout.tsx"
2025-05-12T13:41:35.471346Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api"
2025-05-12T13:41:35.471366Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api/auth"
2025-05-12T13:41:35.471391Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api/auth/session-info"
2025-05-12T13:41:35.471409Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api/auth/session-info/route.ts"
2025-05-12T13:41:35.471425Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-in"
2025-05-12T13:41:35.471446Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-in/[[...sign-in]]"
2025-05-12T13:41:35.471462Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-in/[[...sign-in]]/page.tsx"
2025-05-12T13:41:35.471476Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/page.tsx"
2025-05-12T13:41:35.471486Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/globals.css"
2025-05-12T13:41:35.471505Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components"
2025-05-12T13:41:35.471528Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui"
2025-05-12T13:41:35.471555Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/tabs.tsx"
2025-05-12T13:41:35.471565Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/label.tsx"
2025-05-12T13:41:35.471573Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/switch.tsx"
2025-05-12T13:41:35.471582Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/DataTable.tsx"
2025-05-12T13:41:35.471590Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/table.tsx"
2025-05-12T13:41:35.471599Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/button.tsx"
2025-05-12T13:41:35.471607Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/checkbox.tsx"
2025-05-12T13:41:35.471616Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/dropdown-menu.tsx"
2025-05-12T13:41:35.471629Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/textarea.tsx"
2025-05-12T13:41:35.471638Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/input.tsx"
2025-05-12T13:41:35.471645Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/form.tsx"
2025-05-12T13:41:35.471659Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/auth"
2025-05-12T13:41:35.471680Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/auth/ProtectedRoute.tsx"
2025-05-12T13:41:35.471695Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout"
2025-05-12T13:41:35.471713Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout/Footer.tsx"
2025-05-12T13:41:35.471724Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout/Layout.tsx"
2025-05-12T13:41:35.471734Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout/Header.tsx"
2025-05-12T13:41:35.471743Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout/Sidebar.tsx"
2025-05-12T13:41:35.471756Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/admin"
2025-05-12T13:41:35.471773Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/admin/workflows"
2025-05-12T13:41:35.471787Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/admin/workflows/WorkflowForm.tsx"
2025-05-12T13:41:35.471801Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib"
2025-05-12T13:41:35.471817Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib/permissions.ts"
2025-05-12T13:41:35.471826Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib/utils.ts"
2025-05-12T13:41:35.471837Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib/types.ts"
2025-05-12T13:41:35.471847Z  INFO scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib/apiClient.ts"
2025-05-12T13:41:35.471918Z  INFO scan_sources: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.471922Z  INFO extract_candidates: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.471924Z  INFO extract_candidates:read_all_files: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.471926Z  INFO extract_candidates:read_all_files: tailwindcss_oxide::scanner: Reading 53 file(s)
2025-05-12T13:41:35.472154Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/next.config.ts"
2025-05-12T13:41:35.472200Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/context/AdminSessionContext.tsx"
2025-05-12T13:41:35.472216Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/tenants/[id]/page.tsx"
2025-05-12T13:41:35.472206Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api/auth/session-info/route.ts"
2025-05-12T13:41:35.472212Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/Dockerfile"
2025-05-12T13:41:35.472262Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/profile/page.tsx"
2025-05-12T13:41:35.472272Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-in/[[...sign-in]]/page.tsx"
2025-05-12T13:41:35.472280Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/DataTable.tsx"
2025-05-12T13:41:35.472215Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-up/[[...sign-up]]/page.tsx"
2025-05-12T13:41:35.472215Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/dropdown-menu.tsx"
2025-05-12T13:41:35.472205Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/tsconfig.json"
2025-05-12T13:41:35.472309Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/organizations/[id]/page.tsx"
2025-05-12T13:41:35.472229Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/postcss.config.mjs"
2025-05-12T13:41:35.472255Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/page.tsx"
2025-05-12T13:41:35.472260Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/workflows/create/page.tsx"
2025-05-12T13:41:35.472335Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/templates/page.tsx"
2025-05-12T13:41:35.472201Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/tabs.tsx"
2025-05-12T13:41:35.472347Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/[id]/page.tsx"
2025-05-12T13:41:35.472353Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/page.tsx"
2025-05-12T13:41:35.472357Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/public/next.svg"
2025-05-12T13:41:35.472290Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/layout.tsx"
2025-05-12T13:41:35.472300Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/table.tsx"
2025-05-12T13:41:35.472226Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/README.md"
2025-05-12T13:41:35.472317Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/textarea.tsx"
2025-05-12T13:41:35.472394Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/components.json"
2025-05-12T13:41:35.472260Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/eslint.config.mjs"
2025-05-12T13:41:35.472344Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/layout.tsx"
2025-05-12T13:41:35.472200Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/middleware.ts"
2025-05-12T13:41:35.472206Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/package.json"
2025-05-12T13:41:35.472433Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/checkbox.tsx"
2025-05-12T13:41:35.472359Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/label.tsx"
2025-05-12T13:41:35.472297Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/page.tsx"
2025-05-12T13:41:35.472386Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/import/page.tsx"
2025-05-12T13:41:35.472327Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/workflows/page.tsx"
2025-05-12T13:41:35.472399Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/button.tsx"
2025-05-12T13:41:35.472478Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout/Sidebar.tsx"
2025-05-12T13:41:35.472424Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/public/file.svg"
2025-05-12T13:41:35.472488Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout/Footer.tsx"
2025-05-12T13:41:35.472495Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/auth/ProtectedRoute.tsx"
2025-05-12T13:41:35.472501Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib/utils.ts"
2025-05-12T13:41:35.472440Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/form.tsx"
2025-05-12T13:41:35.472449Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib/permissions.ts"
2025-05-12T13:41:35.472472Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib/types.ts"
2025-05-12T13:41:35.472473Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib/apiClient.ts"
2025-05-12T13:41:35.472409Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/input.tsx"
2025-05-12T13:41:35.472478Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/admin/workflows/WorkflowForm.tsx"
2025-05-12T13:41:35.472355Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/.dockerignore"
2025-05-12T13:41:35.472498Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout/Layout.tsx"
2025-05-12T13:41:35.472439Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui/switch.tsx"
2025-05-12T13:41:35.472503Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/public/vercel.svg"
2025-05-12T13:41:35.472439Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout/Header.tsx"
2025-05-12T13:41:35.472850Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/public/globe.svg"
2025-05-12T13:41:35.472851Z  INFO tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/public/window.svg"
2025-05-12T13:41:35.472885Z  INFO extract_candidates:read_all_files: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.472891Z  INFO extract_candidates:parse_all_blobs: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.472895Z  INFO extract_candidates:parse_all_blobs:extract: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.473584Z  INFO extract_candidates:parse_all_blobs:extract: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.473599Z  INFO extract_candidates:parse_all_blobs: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.473603Z  INFO extract_candidates:read_all_files: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.473605Z  INFO extract_candidates:read_all_files: tailwindcss_oxide::scanner: Reading 1 file(s)
2025-05-12T13:41:35.473623Z  INFO extract_candidates:read_all_files: tailwindcss_oxide::scanner: Reading "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/globals.css"
2025-05-12T13:41:35.473627Z  INFO extract_candidates:read_all_files: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.473630Z  INFO extract_candidates:extract_css_variables: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.473632Z  INFO extract_candidates:extract_css_variables:extract: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.473784Z  INFO extract_candidates:extract_css_variables:extract: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.473792Z  INFO extract_candidates:extract_css_variables: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.474316Z  INFO extract_candidates: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.474663Z  INFO get_files: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.474667Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.475196Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/public"
2025-05-12T13:41:35.475270Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src"
2025-05-12T13:41:35.475293Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/context"
2025-05-12T13:41:35.475318Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app"
2025-05-12T13:41:35.475337Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-up"
2025-05-12T13:41:35.475356Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-up/[[...sign-up]]"
2025-05-12T13:41:35.475382Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin"
2025-05-12T13:41:35.475405Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/organizations"
2025-05-12T13:41:35.475423Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/organizations/[id]"
2025-05-12T13:41:35.475449Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/workflows"
2025-05-12T13:41:35.475476Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/workflows/create"
2025-05-12T13:41:35.475506Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations"
2025-05-12T13:41:35.475529Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/[id]"
2025-05-12T13:41:35.475553Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/import"
2025-05-12T13:41:35.475589Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/templates"
2025-05-12T13:41:35.475619Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/tenants"
2025-05-12T13:41:35.475637Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/tenants/[id]"
2025-05-12T13:41:35.475669Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/profile"
2025-05-12T13:41:35.475698Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api"
2025-05-12T13:41:35.475715Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api/auth"
2025-05-12T13:41:35.475735Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api/auth/session-info"
2025-05-12T13:41:35.475762Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-in"
2025-05-12T13:41:35.475783Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-in/[[...sign-in]]"
2025-05-12T13:41:35.475815Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components"
2025-05-12T13:41:35.475837Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui"
2025-05-12T13:41:35.475912Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/auth"
2025-05-12T13:41:35.475939Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout"
2025-05-12T13:41:35.475980Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/admin"
2025-05-12T13:41:35.475998Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/admin/workflows"
2025-05-12T13:41:35.476024Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib"
2025-05-12T13:41:35.476090Z  INFO get_files:scan_sources: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.476252Z  INFO get_files: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.476327Z  INFO get_globs: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.476332Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: enter
2025-05-12T13:41:35.476924Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/public"
2025-05-12T13:41:35.477002Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src"
2025-05-12T13:41:35.477028Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/context"
2025-05-12T13:41:35.477065Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app"
2025-05-12T13:41:35.477090Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-up"
2025-05-12T13:41:35.477110Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-up/[[...sign-up]]"
2025-05-12T13:41:35.477162Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin"
2025-05-12T13:41:35.477186Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/organizations"
2025-05-12T13:41:35.477214Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/organizations/[id]"
2025-05-12T13:41:35.477257Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/workflows"
2025-05-12T13:41:35.477287Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/workflows/create"
2025-05-12T13:41:35.477320Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations"
2025-05-12T13:41:35.477344Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/[id]"
2025-05-12T13:41:35.477373Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/integrations/import"
2025-05-12T13:41:35.477421Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/admin/templates"
2025-05-12T13:41:35.477485Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/tenants"
2025-05-12T13:41:35.477504Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/tenants/[id]"
2025-05-12T13:41:35.477530Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/profile"
2025-05-12T13:41:35.477582Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api"
2025-05-12T13:41:35.477605Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api/auth"
2025-05-12T13:41:35.477645Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/api/auth/session-info"
2025-05-12T13:41:35.477698Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-in"
2025-05-12T13:41:35.477725Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/app/sign-in/[[...sign-in]]"
2025-05-12T13:41:35.477785Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components"
2025-05-12T13:41:35.477815Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/ui"
2025-05-12T13:41:35.477909Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/auth"
2025-05-12T13:41:35.477938Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/layout"
2025-05-12T13:41:35.477978Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/admin"
2025-05-12T13:41:35.478015Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/components/admin/workflows"
2025-05-12T13:41:35.478044Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: Discovering "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/lib"
2025-05-12T13:41:35.478112Z  INFO get_globs:scan_sources: tailwindcss_oxide::scanner: exit
2025-05-12T13:41:35.478954Z  INFO get_globs: tailwindcss_oxide::scanner: exit
