#!/usr/bin/env python3
"""
Coherence Codebase Knowledge Graph Sync
Comprehensive analysis of all Python and TypeScript files for ISAAC Neo4j integration
"""

import os
import ast
import re
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Set, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class FunctionInfo:
    name: str
    signature: str
    parameters: List[Dict[str, Any]]
    return_type: Optional[str]
    is_async: bool
    is_exported: bool
    decorators: List[str]
    line_number: int
    docstring: Optional[str]


@dataclass
class ClassInfo:
    name: str
    bases: List[str]
    methods: List[FunctionInfo]
    properties: List[str]
    is_exported: bool
    decorators: List[str]
    line_number: int
    docstring: Optional[str]


@dataclass
class ImportInfo:
    module: str
    names: List[str]
    alias: Optional[str]
    is_relative: bool
    line_number: int


@dataclass
class ComponentInfo:
    name: str
    path: str
    type: str  # 'module', 'component', 'class', 'endpoint', 'model'
    functions: List[FunctionInfo]
    classes: List[ClassInfo]
    imports: List[ImportInfo]
    exports: List[str]
    dependencies: Set[str]
    api_endpoints: List[Dict[str, Any]]
    props: List[Dict[str, Any]]  # For React components
    hooks_used: List[str]  # For React components
    db_relationships: List[Dict[str, Any]]  # For models
    last_modified: str
    content_hash: str


class CoherenceCodebaseAnalyzer:
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.components: Dict[str, ComponentInfo] = {}
        self.relationships: List[Dict[str, Any]] = []
        self.patterns: List[Dict[str, Any]] = []
        
    def analyze_codebase(self) -> Dict[str, Any]:
        """Main entry point for codebase analysis"""
        print("🔍 Starting comprehensive Coherence codebase analysis...")
        
        # Analyze Python backend
        print("\n📊 Analyzing Python backend files...")
        self._analyze_python_files()
        
        # Analyze TypeScript frontend
        print("\n⚛️ Analyzing TypeScript frontend files...")
        self._analyze_typescript_files()
        
        # Extract relationships
        print("\n🔗 Mapping component relationships...")
        self._extract_relationships()
        
        # Detect patterns
        print("\n🧩 Detecting code patterns...")
        self._detect_patterns()
        
        # Generate final structure
        return self._generate_knowledge_graph_structure()
    
    def _analyze_python_files(self):
        """Analyze all Python files in src/coherence/"""
        python_root = self.root_path / "src" / "coherence"
        
        for py_file in python_root.rglob("*.py"):
            if "__pycache__" in str(py_file):
                continue
                
            try:
                relative_path = str(py_file.relative_to(self.root_path))
                print(f"  🐍 Analyzing: {relative_path}")
                
                component = self._analyze_python_file(py_file)
                if component:
                    self.components[relative_path] = component
                    
            except Exception as e:
                print(f"  ⚠️ Error analyzing {py_file}: {e}")
    
    def _analyze_typescript_files(self):
        """Analyze all TypeScript files in coherence-admin/src/"""
        ts_root = self.root_path / "coherence-admin" / "src"
        
        for ts_file in ts_root.rglob("*.ts*"):
            if "node_modules" in str(ts_file) or ".d.ts" in str(ts_file):
                continue
                
            try:
                relative_path = str(ts_file.relative_to(self.root_path))
                print(f"  ⚛️ Analyzing: {relative_path}")
                
                component = self._analyze_typescript_file(ts_file)
                if component:
                    self.components[relative_path] = component
                    
            except Exception as e:
                print(f"  ⚠️ Error analyzing {ts_file}: {e}")
    
    def _analyze_python_file(self, file_path: Path) -> Optional[ComponentInfo]:
        """Analyze a single Python file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST
            tree = ast.parse(content)
            
            # Extract file metadata
            relative_path = str(file_path.relative_to(self.root_path))
            stat = file_path.stat()
            
            component = ComponentInfo(
                name=file_path.stem,
                path=relative_path,
                type=self._determine_python_component_type(file_path, tree),
                functions=[],
                classes=[],
                imports=[],
                exports=[],
                dependencies=set(),
                api_endpoints=[],
                props=[],
                hooks_used=[],
                db_relationships=[],
                last_modified=datetime.fromtimestamp(stat.st_mtime).isoformat(),
                content_hash=str(hash(content))
            )
            
            # Extract AST information
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    func_info = self._extract_function_info(node, content)
                    component.functions.append(func_info)
                    
                    # Check for API endpoints
                    if self._is_api_endpoint(node):
                        endpoint_info = self._extract_endpoint_info(node, content)
                        if endpoint_info:
                            component.api_endpoints.append(endpoint_info)
                
                elif isinstance(node, ast.ClassDef):
                    class_info = self._extract_class_info(node, content)
                    component.classes.append(class_info)
                    
                    # Check for database models
                    if self._is_db_model(node):
                        db_info = self._extract_db_relationships(node, content)
                        component.db_relationships.extend(db_info)
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    import_info = self._extract_import_info(node)
                    if import_info:
                        component.imports.append(import_info)
                        component.dependencies.add(import_info.module)
            
            # Extract exports (functions/classes defined at module level)
            component.exports = self._extract_python_exports(tree)
            
            return component
            
        except Exception as e:
            print(f"    ⚠️ Failed to parse {file_path}: {e}")
            return None
    
    def _analyze_typescript_file(self, file_path: Path) -> Optional[ComponentInfo]:
        """Analyze a single TypeScript file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract file metadata
            relative_path = str(file_path.relative_to(self.root_path))
            stat = file_path.stat()
            
            component = ComponentInfo(
                name=file_path.stem,
                path=relative_path,
                type=self._determine_typescript_component_type(file_path, content),
                functions=[],
                classes=[],
                imports=[],
                exports=[],
                dependencies=set(),
                api_endpoints=[],
                props=[],
                hooks_used=[],
                db_relationships=[],
                last_modified=datetime.fromtimestamp(stat.st_mtime).isoformat(),
                content_hash=str(hash(content))
            )
            
            # Extract TypeScript/React information using regex patterns
            # (For a complete implementation, you'd use a proper TS parser like typescript-ast)
            
            # Extract imports
            component.imports = self._extract_typescript_imports(content)
            for imp in component.imports:
                component.dependencies.add(imp.module)
            
            # Extract exports
            component.exports = self._extract_typescript_exports(content)
            
            # Extract functions
            component.functions = self._extract_typescript_functions(content)
            
            # Extract React component props and hooks
            if self._is_react_component(content):
                component.props = self._extract_react_props(content)
                component.hooks_used = self._extract_react_hooks(content)
            
            # Extract API routes
            if self._is_api_route(file_path):
                component.api_endpoints = self._extract_nextjs_api_routes(content)
            
            return component
            
        except Exception as e:
            print(f"    ⚠️ Failed to parse {file_path}: {e}")
            return None
    
    def _extract_function_info(self, node: ast.FunctionDef | ast.AsyncFunctionDef, content: str) -> FunctionInfo:
        """Extract detailed function information from AST node"""
        parameters = []
        for arg in node.args.args:
            param_info = {
                "name": arg.arg,
                "type": ast.unparse(arg.annotation) if arg.annotation else None,
                "default": None
            }
            parameters.append(param_info)
        
        # Add defaults
        defaults = node.args.defaults
        if defaults:
            for i, default in enumerate(defaults):
                param_idx = len(parameters) - len(defaults) + i
                if param_idx >= 0:
                    parameters[param_idx]["default"] = ast.unparse(default)
        
        return FunctionInfo(
            name=node.name,
            signature=self._get_function_signature(node),
            parameters=parameters,
            return_type=ast.unparse(node.returns) if node.returns else None,
            is_async=isinstance(node, ast.AsyncFunctionDef),
            is_exported=not node.name.startswith('_'),
            decorators=[ast.unparse(d) for d in node.decorator_list],
            line_number=node.lineno,
            docstring=ast.get_docstring(node)
        )
    
    def _extract_class_info(self, node: ast.ClassDef, content: str) -> ClassInfo:
        """Extract detailed class information from AST node"""
        methods = []
        properties = []
        
        for item in node.body:
            if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                methods.append(self._extract_function_info(item, content))
            elif isinstance(item, ast.AnnAssign) and isinstance(item.target, ast.Name):
                properties.append(item.target.id)
        
        return ClassInfo(
            name=node.name,
            bases=[ast.unparse(base) for base in node.bases],
            methods=methods,
            properties=properties,
            is_exported=not node.name.startswith('_'),
            decorators=[ast.unparse(d) for d in node.decorator_list],
            line_number=node.lineno,
            docstring=ast.get_docstring(node)
        )
    
    def _extract_import_info(self, node: ast.Import | ast.ImportFrom) -> Optional[ImportInfo]:
        """Extract import information from AST node"""
        if isinstance(node, ast.Import):
            names = [alias.name for alias in node.names]
            return ImportInfo(
                module=names[0] if names else "",
                names=names,
                alias=node.names[0].asname if node.names and node.names[0].asname else None,
                is_relative=False,
                line_number=node.lineno
            )
        elif isinstance(node, ast.ImportFrom):
            if node.module:
                names = [alias.name for alias in node.names]
                return ImportInfo(
                    module=node.module,
                    names=names,
                    alias=None,
                    is_relative=node.level > 0,
                    line_number=node.lineno
                )
        return None
    
    def _extract_typescript_imports(self, content: str) -> List[ImportInfo]:
        """Extract TypeScript import statements using regex"""
        imports = []
        
        # Match various import patterns
        import_patterns = [
            r"import\s+{([^}]+)}\s+from\s+['\"]([^'\"]+)['\"]",
            r"import\s+(\w+)\s+from\s+['\"]([^'\"]+)['\"]",
            r"import\s+\*\s+as\s+(\w+)\s+from\s+['\"]([^'\"]+)['\"]",
        ]
        
        for i, line in enumerate(content.split('\n'), 1):
            for pattern in import_patterns:
                match = re.search(pattern, line)
                if match:
                    if '{' in pattern:  # Named imports
                        names = [name.strip() for name in match.group(1).split(',')]
                        module = match.group(2)
                    else:  # Default or namespace imports
                        names = [match.group(1)]
                        module = match.group(2)
                    
                    imports.append(ImportInfo(
                        module=module,
                        names=names,
                        alias=None,
                        is_relative=module.startswith('.'),
                        line_number=i
                    ))
        
        return imports
    
    def _extract_typescript_exports(self, content: str) -> List[str]:
        """Extract TypeScript exports using regex"""
        exports = []
        
        export_patterns = [
            r"export\s+(?:const|let|var|function|class|interface|type)\s+(\w+)",
            r"export\s+default\s+(?:function\s+)?(\w+)",
            r"export\s+{([^}]+)}",
        ]
        
        for pattern in export_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                if '{' in pattern:  # Named exports
                    names = [name.strip() for name in match.group(1).split(',')]
                    exports.extend(names)
                else:
                    exports.append(match.group(1))
        
        return exports
    
    def _extract_typescript_functions(self, content: str) -> List[FunctionInfo]:
        """Extract TypeScript function information using regex"""
        functions = []
        
        # Pattern for function declarations
        func_pattern = r"(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\(([^)]*)\)(?:\s*:\s*([^{]+))?"
        
        for match in re.finditer(func_pattern, content):
            name = match.group(1)
            params_str = match.group(2) if match.group(2) else ""
            return_type = match.group(3).strip() if match.group(3) else None
            
            # Parse parameters
            parameters = []
            if params_str.strip():
                for param in params_str.split(','):
                    param = param.strip()
                    if ':' in param:
                        param_name, param_type = param.split(':', 1)
                        parameters.append({
                            "name": param_name.strip(),
                            "type": param_type.strip(),
                            "default": None
                        })
            
            functions.append(FunctionInfo(
                name=name,
                signature=f"function {name}({params_str})",
                parameters=parameters,
                return_type=return_type,
                is_async="async" in match.group(0),
                is_exported="export" in match.group(0),
                decorators=[],
                line_number=content[:match.start()].count('\n') + 1,
                docstring=None
            ))
        
        return functions
    
    def _extract_react_props(self, content: str) -> List[Dict[str, Any]]:
        """Extract React component props using regex"""
        props = []
        
        # Look for interface/type definitions for props
        props_pattern = r"(?:interface|type)\s+(\w*Props)\s*{([^}]+)}"
        
        for match in re.finditer(props_pattern, content):
            props_name = match.group(1)
            props_body = match.group(2)
            
            for line in props_body.split('\n'):
                line = line.strip()
                if ':' in line and not line.startswith('//'):
                    prop_match = re.match(r"(\w+)(\?)?\s*:\s*([^;]+)", line)
                    if prop_match:
                        props.append({
                            "name": prop_match.group(1),
                            "type": prop_match.group(3).strip(),
                            "optional": prop_match.group(2) == '?',
                            "interface": props_name
                        })
        
        return props
    
    def _extract_react_hooks(self, content: str) -> List[str]:
        """Extract React hooks used in component"""
        hooks = []
        
        # Common React hooks pattern
        hook_pattern = r"use[A-Z]\w*"
        
        for match in re.finditer(hook_pattern, content):
            hook_name = match.group(0)
            if hook_name not in hooks:
                hooks.append(hook_name)
        
        return hooks
    
    def _extract_nextjs_api_routes(self, content: str) -> List[Dict[str, Any]]:
        """Extract Next.js API route information"""
        endpoints = []
        
        # Look for HTTP method exports
        method_pattern = r"export\s+(?:async\s+)?function\s+(GET|POST|PUT|DELETE|PATCH)\s*\("
        
        for match in re.finditer(method_pattern, content):
            method = match.group(1)
            endpoints.append({
                "method": method,
                "function_name": method,
                "type": "nextjs_api_route"
            })
        
        return endpoints
    
    def _determine_python_component_type(self, file_path: Path, tree: ast.AST) -> str:
        """Determine the type of Python component"""
        path_str = str(file_path)
        
        if "endpoints" in path_str:
            return "api_endpoint"
        elif "models" in path_str:
            return "database_model"
        elif "crud" in path_str:
            return "crud_service"
        elif "schemas" in path_str:
            return "pydantic_schema"
        elif "services" in path_str:
            return "business_service"
        elif "middleware" in path_str:
            return "middleware"
        elif file_path.name == "main.py":
            return "application_entry"
        else:
            return "python_module"
    
    def _determine_typescript_component_type(self, file_path: Path, content: str) -> str:
        """Determine the type of TypeScript component"""
        path_str = str(file_path)
        
        if "page.tsx" in path_str:
            return "nextjs_page"
        elif "route.ts" in path_str:
            return "nextjs_api_route"
        elif "layout.tsx" in path_str:
            return "nextjs_layout"
        elif ".tsx" in path_str and ("function" in content or "const" in content):
            return "react_component"
        elif "Context" in file_path.name:
            return "react_context"
        elif "hook" in path_str.lower() or content.startswith("use"):
            return "react_hook"
        elif "middleware.ts" in path_str:
            return "nextjs_middleware"
        else:
            return "typescript_module"
    
    def _is_api_endpoint(self, node: ast.FunctionDef | ast.AsyncFunctionDef) -> bool:
        """Check if function is an API endpoint"""
        decorators = [ast.unparse(d) for d in node.decorator_list]
        return any("app." in d or "router." in d for d in decorators)
    
    def _is_db_model(self, node: ast.ClassDef) -> bool:
        """Check if class is a database model"""
        bases = [ast.unparse(base) for base in node.bases]
        return any("Base" in base or "Model" in base for base in bases)
    
    def _is_react_component(self, content: str) -> bool:
        """Check if file contains a React component"""
        return ("React" in content or "jsx" in content.lower() or 
                "return (" in content or "export default function" in content)
    
    def _is_api_route(self, file_path: Path) -> bool:
        """Check if file is a Next.js API route"""
        return "route.ts" in str(file_path) or "/api/" in str(file_path)
    
    def _extract_endpoint_info(self, node: ast.FunctionDef | ast.AsyncFunctionDef, content: str) -> Optional[Dict[str, Any]]:
        """Extract API endpoint information"""
        decorators = [ast.unparse(d) for d in node.decorator_list]
        
        for decorator in decorators:
            if any(method in decorator.lower() for method in ['get', 'post', 'put', 'delete', 'patch']):
                # Extract HTTP method and path
                method_match = re.search(r'\.(get|post|put|delete|patch)\s*\(\s*["\']([^"\']*)["\']', decorator)
                if method_match:
                    return {
                        "method": method_match.group(1).upper(),
                        "path": method_match.group(2),
                        "function_name": node.name,
                        "type": "fastapi_endpoint"
                    }
        
        return None
    
    def _extract_db_relationships(self, node: ast.ClassDef, content: str) -> List[Dict[str, Any]]:
        """Extract database model relationships"""
        relationships = []
        
        for item in node.body:
            if isinstance(item, ast.AnnAssign) and isinstance(item.target, ast.Name):
                annotation = ast.unparse(item.annotation) if item.annotation else ""
                
                # Look for SQLAlchemy relationships
                if "relationship" in annotation.lower() or "foreignkey" in annotation.lower():
                    relationships.append({
                        "field": item.target.id,
                        "type": annotation,
                        "model": node.name
                    })
        
        return relationships
    
    def _extract_python_exports(self, tree: ast.AST) -> List[str]:
        """Extract Python module exports"""
        exports = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                if not node.name.startswith('_'):
                    exports.append(node.name)
        
        return exports
    
    def _get_function_signature(self, node: ast.FunctionDef | ast.AsyncFunctionDef) -> str:
        """Generate function signature string"""
        args = []
        for arg in node.args.args:
            arg_str = arg.arg
            if arg.annotation:
                arg_str += f": {ast.unparse(arg.annotation)}"
            args.append(arg_str)
        
        signature = f"{'async ' if isinstance(node, ast.AsyncFunctionDef) else ''}def {node.name}({', '.join(args)})"
        
        if node.returns:
            signature += f" -> {ast.unparse(node.returns)}"
        
        return signature
    
    def _extract_relationships(self):
        """Extract relationships between components"""
        print("  🔗 Analyzing import/export relationships...")
        
        for component_path, component in self.components.items():
            for import_info in component.imports:
                # Map import to actual component
                target_component = self._resolve_import_path(import_info.module, component_path)
                
                if target_component and target_component in self.components:
                    relationship = {
                        "type": "IMPORTS",
                        "source": component_path,
                        "target": target_component,
                        "details": {
                            "imported_names": import_info.names,
                            "is_relative": import_info.is_relative,
                            "line_number": import_info.line_number
                        }
                    }
                    self.relationships.append(relationship)
            
            # Add API endpoint relationships
            for endpoint in component.api_endpoints:
                # Handle endpoints that might not have 'path' key
                endpoint_path = endpoint.get('path', endpoint.get('function_name', 'unknown'))
                endpoint_method = endpoint.get('method', 'UNKNOWN')
                
                relationship = {
                    "type": "DEFINES_ENDPOINT",
                    "source": component_path,
                    "target": f"{endpoint_method} {endpoint_path}",
                    "details": endpoint
                }
                self.relationships.append(relationship)
            
            # Add database model relationships
            for db_rel in component.db_relationships:
                relationship = {
                    "type": "DB_RELATIONSHIP",
                    "source": component_path,
                    "target": db_rel["field"],
                    "details": db_rel
                }
                self.relationships.append(relationship)
    
    def _resolve_import_path(self, import_module: str, current_file: str) -> Optional[str]:
        """Resolve import path to actual file path"""
        # This is a simplified version - in practice, you'd need more sophisticated resolution
        
        if import_module.startswith('.'):
            # Relative import
            current_dir = os.path.dirname(current_file)
            # Handle relative imports based on dots
            levels = len(import_module) - len(import_module.lstrip('.'))
            for _ in range(levels - 1):
                current_dir = os.path.dirname(current_dir)
            
            resolved_path = os.path.join(current_dir, import_module.lstrip('.').replace('.', '/'))
        else:
            # Absolute import
            if import_module.startswith('coherence.'):
                # Internal import
                module_path = import_module.replace('coherence.', 'src/coherence/').replace('.', '/')
                resolved_path = module_path
            else:
                # External import - skip
                return None
        
        # Try to find matching component
        for component_path in self.components.keys():
            if resolved_path in component_path:
                return component_path
        
        return None
    
    def _detect_patterns(self):
        """Detect common code patterns"""
        print("  🧩 Detecting architectural patterns...")
        
        # Detect API patterns
        api_endpoints = [c for c in self.components.values() if c.api_endpoints]
        if api_endpoints:
            self.patterns.append({
                "name": "FastAPI REST Endpoints",
                "type": "api_pattern",
                "description": "FastAPI-based REST API endpoints",
                "components": [c.path for c in api_endpoints],
                "count": len(api_endpoints)
            })
        
        # Detect React component patterns
        react_components = [c for c in self.components.values() if c.type == "react_component"]
        if react_components:
            self.patterns.append({
                "name": "React Components",
                "type": "frontend_pattern",
                "description": "React functional components",
                "components": [c.path for c in react_components],
                "count": len(react_components)
            })
        
        # Detect database model patterns
        db_models = [c for c in self.components.values() if c.type == "database_model"]
        if db_models:
            self.patterns.append({
                "name": "SQLAlchemy Models",
                "type": "database_pattern",
                "description": "SQLAlchemy database models",
                "components": [c.path for c in db_models],
                "count": len(db_models)
            })
        
        # Detect middleware patterns
        middleware_components = [c for c in self.components.values() if c.type == "middleware"]
        if middleware_components:
            self.patterns.append({
                "name": "FastAPI Middleware",
                "type": "middleware_pattern",
                "description": "FastAPI middleware components",
                "components": [c.path for c in middleware_components],
                "count": len(middleware_components)
            })
    
    def _generate_knowledge_graph_structure(self) -> Dict[str, Any]:
        """Generate the final knowledge graph structure"""
        print("\n📋 Generating knowledge graph structure...")
        
        # Convert components to serializable format
        components_data = {}
        for path, component in self.components.items():
            components_data[path] = {
                "name": component.name,
                "path": component.path,
                "type": component.type,
                "functions": [asdict(f) for f in component.functions],
                "classes": [asdict(c) for c in component.classes],
                "imports": [asdict(i) for i in component.imports],
                "exports": component.exports,
                "dependencies": list(component.dependencies),
                "api_endpoints": component.api_endpoints,
                "props": component.props,
                "hooks_used": component.hooks_used,
                "db_relationships": component.db_relationships,
                "last_modified": component.last_modified,
                "content_hash": component.content_hash
            }
        
        return {
            "project_info": {
                "name": "Coherence",
                "type": "AI Middleware System",
                "description": "AI-powered middleware system for natural language to API operations",
                "tech_stack": {
                    "backend": "FastAPI, SQLAlchemy, PostgreSQL, Redis, Qdrant",
                    "frontend": "Next.js 15, React 19, TypeScript, Tailwind CSS",
                    "ai_ml": "OpenAI API, RAG system, Intent recognition",
                    "devops": "Docker Compose"
                },
                "analysis_timestamp": datetime.now().isoformat(),
                "total_files_analyzed": len(self.components),
                "python_files": len([c for c in self.components.values() if "src/coherence" in c.path]),
                "typescript_files": len([c for c in self.components.values() if "coherence-admin" in c.path])
            },
            "components": components_data,
            "relationships": self.relationships,
            "patterns": self.patterns,
            "statistics": {
                "total_functions": sum(len(c.functions) for c in self.components.values()),
                "total_classes": sum(len(c.classes) for c in self.components.values()),
                "total_api_endpoints": sum(len(c.api_endpoints) for c in self.components.values()),
                "total_relationships": len(self.relationships),
                "component_types": self._get_component_type_stats()
            }
        }
    
    def _get_component_type_stats(self) -> Dict[str, int]:
        """Get statistics by component type"""
        stats = {}
        for component in self.components.values():
            component_type = component.type
            stats[component_type] = stats.get(component_type, 0) + 1
        return stats


def main():
    """Main execution function"""
    root_path = "/Users/<USER>/Documents/projects/coherence"
    
    print("🚀 Starting Coherence Knowledge Graph Sync")
    print(f"📂 Root path: {root_path}")
    
    analyzer = CoherenceCodebaseAnalyzer(root_path)
    
    try:
        # Perform comprehensive analysis
        knowledge_graph_data = analyzer.analyze_codebase()
        
        # Save results
        output_file = os.path.join(root_path, "knowledge_graph_output", "coherence_knowledge_graph.json")
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(knowledge_graph_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Analysis complete!")
        print(f"📊 Results saved to: {output_file}")
        print(f"📈 Statistics:")
        print(f"   • Total files analyzed: {knowledge_graph_data['project_info']['total_files_analyzed']}")
        print(f"   • Python files: {knowledge_graph_data['project_info']['python_files']}")
        print(f"   • TypeScript files: {knowledge_graph_data['project_info']['typescript_files']}")
        print(f"   • Total functions: {knowledge_graph_data['statistics']['total_functions']}")
        print(f"   • Total classes: {knowledge_graph_data['statistics']['total_classes']}")
        print(f"   • API endpoints: {knowledge_graph_data['statistics']['total_api_endpoints']}")
        print(f"   • Relationships: {knowledge_graph_data['statistics']['total_relationships']}")
        print(f"   • Patterns detected: {len(knowledge_graph_data['patterns'])}")
        
        print(f"\n🎯 Component type breakdown:")
        for comp_type, count in knowledge_graph_data['statistics']['component_types'].items():
            print(f"   • {comp_type}: {count}")
        
        return knowledge_graph_data
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        raise


if __name__ == "__main__":
    main()