"""
Helper functions for safe enum value additions in migrations.

PostgreSQL doesn't support IF NOT EXISTS for enum values in all versions,
so we need to handle this manually.
"""

from alembic import op
from sqlalchemy import text
from sqlalchemy.exc import ProgrammingError
import logging

logger = logging.getLogger(__name__)


def add_enum_value_safe(enum_name: str, value: str, before: str = None, after: str = None):
    """
    Safely add a value to an enum type, handling the case where it already exists.
    
    Args:
        enum_name: Name of the enum type
        value: Value to add
        before: Optional value to insert before
        after: Optional value to insert after
    """
    conn = op.get_bind()
    
    # First check if the value already exists
    result = conn.execute(
        text("""
            SELECT EXISTS (
                SELECT 1 
                FROM pg_enum 
                WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = :enum_name)
                AND enumlabel = :value
            )
        """),
        {"enum_name": enum_name, "value": value}
    )
    
    if result.scalar():
        logger.info(f"Enum value '{value}' already exists in {enum_name}, skipping")
        return
    
    # Build the ALTER TYPE statement
    sql = f"ALTER TYPE {enum_name} ADD VALUE '{value}'"
    if before:
        sql += f" BEFORE '{before}'"
    elif after:
        sql += f" AFTER '{after}'"
    
    try:
        conn.execute(text(sql))
        logger.info(f"Added enum value '{value}' to {enum_name}")
    except ProgrammingError as e:
        if "already exists" in str(e):
            logger.info(f"Enum value '{value}' already exists in {enum_name}, skipping")
        else:
            raise