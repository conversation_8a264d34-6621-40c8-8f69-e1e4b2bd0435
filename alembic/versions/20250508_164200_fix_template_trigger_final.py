"""fix_template_trigger_final

Revision ID: 20250508_164200
Revises: b63fdcc5062c
Create Date: 2025-05-08 16:42:00.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250508_164200'
down_revision: Union[str, None] = 'b63fdcc5062c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    Final fix for the template trigger to correctly handle version history.
    This combines the best parts of both previous fixes.
    """
    # Drop existing trigger and function
    op.execute("DROP TRIGGER IF EXISTS version_template ON templates;")
    op.execute("DROP FUNCTION IF EXISTS save_template_version CASCADE;")
    
    # Create the fixed function that handles both content and body correctly
    op.execute("""
    CREATE OR REPLACE FUNCTION save_template_version()
    RETURNS TRIGGER AS $$
    BEGIN
        -- Only create a version if the body has changed
        IF OLD.body IS DISTINCT FROM NEW.body THEN
            INSERT INTO template_versions (
                id,
                template_id,
                version,
                body,
                actions,
                parameters,
                editor_id,
                change_reason,
                edited_at
            )
            VALUES (
                gen_random_uuid(),
                NEW.id,
                NEW.version,
                NEW.body,
                NEW.actions,
                NEW.parameters,
                NULL, -- editor_id set by application
                'Update via database trigger',
                CURRENT_TIMESTAMP
            );
        END IF;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """)
    
    # Create trigger that runs AFTER update
    # This ensures we have access to both OLD and NEW values
    op.execute("""
    CREATE TRIGGER version_template
    AFTER UPDATE ON templates
    FOR EACH ROW
    WHEN (OLD.body IS DISTINCT FROM NEW.body)
    EXECUTE FUNCTION save_template_version();
    """)


def downgrade() -> None:
    """
    Restore to the state before this final fix.
    """
    # Drop the fixed trigger and function
    op.execute("DROP TRIGGER IF EXISTS version_template ON templates;")
    op.execute("DROP FUNCTION IF EXISTS save_template_version CASCADE;")
