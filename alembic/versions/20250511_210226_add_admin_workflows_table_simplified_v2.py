""""add_admin_workflows_table_simplified_v2"

Revision ID: d35684c5d828
Revises: 20250510_203523
Create Date: 2025-05-11 21:02:26.724357+00:00

"""
from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = 'd35684c5d828'
down_revision: Union[str, None] = '20250510_203523'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'admin_workflows',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('is_enabled', sa.<PERSON>(), nullable=False, server_default=sa.true()),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_admin_workflows_id'), 'admin_workflows', ['id'], unique=False)
    op.create_index(op.f('ix_admin_workflows_name'), 'admin_workflows', ['name'], unique=False) # Model had index=True


def downgrade() -> None:
    op.drop_index(op.f('ix_admin_workflows_name'), table_name='admin_workflows')
    op.drop_index(op.f('ix_admin_workflows_id'), table_name='admin_workflows')
    op.drop_table('admin_workflows')