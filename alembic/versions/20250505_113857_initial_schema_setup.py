"""Initial schema setup

Revision ID: 1fe15b4cbf90
Revises: 
Create Date: 2025-05-05 11:38:57.660309+00:00

"""
from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1fe15b4cbf90"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "tenants",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("industry_pack", sa.String(), nullable=True),
        sa.Column("compliance_tier", sa.String(), nullable=True),
        sa.Column("settings", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "api_integrations",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("tenant_id", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("version", sa.String(), nullable=True),
        sa.Column(
            "openapi_spec", postgresql.JSONB(astext_type=sa.Text()), nullable=False
        ),
        sa.Column("base_url", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "status",
            postgresql.ENUM("draft", "active", "disabled", name="integration_status"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["tenant_id"], ["tenants.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("api_integrations", schema=None) as batch_op:
        batch_op.create_index(
            "ix_api_integrations_tenant_id_name", ["tenant_id", "name"], unique=True
        )

    op.create_table(
        "api_keys",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("tenant_id", sa.UUID(), nullable=False),
        sa.Column("label", sa.String(), nullable=True),
        sa.Column("key_hash", sa.String(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("last_used_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("revoked", sa.Boolean(), nullable=True),
        sa.Column("expires_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["tenant_id"], ["tenants.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("api_keys", schema=None) as batch_op:
        batch_op.create_index("ix_api_keys_key_hash", ["key_hash"], unique=True)
        batch_op.create_index("ix_api_keys_tenant_id", ["tenant_id"], unique=False)

    op.create_table(
        "templates",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "scope",
            postgresql.ENUM("global", "pack", "tenant", name="template_scope"),
            nullable=False,
        ),
        sa.Column("scope_id", sa.UUID(), nullable=True),
        sa.Column("tenant_id", sa.UUID(), nullable=True),
        sa.Column("key", sa.String(), nullable=False),
        sa.Column(
            "category",
            postgresql.ENUM(
                "intent_router",
                "param_complete",
                "retrieval",
                "response_gen",
                "error_handler",
                name="template_category",
            ),
            nullable=False,
        ),
        sa.Column("version", sa.Integer(), nullable=False),
        sa.Column("language", sa.String(length=5), nullable=False),
        sa.Column("body", sa.TEXT(), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column("actions", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("parameters", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("created_by", sa.UUID(), nullable=True),
        sa.ForeignKeyConstraint(["tenant_id"], ["tenants.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("templates", schema=None) as batch_op:
        batch_op.create_index(
            "ix_templates_scope_scope_id_key_version",
            ["scope", "scope_id", "key", "version"],
            unique=True,
        )
        batch_op.create_index("ix_templates_tenant_id", ["tenant_id"], unique=False)

    op.create_table(
        "tenant_settings",
        sa.Column("tenant_id", sa.UUID(), nullable=False),
        sa.Column("tier1_threshold", sa.String(), nullable=True),
        sa.Column("tier2_threshold", sa.String(), nullable=True),
        sa.Column("llm_model", sa.String(), nullable=True),
        sa.Column("embedding_model", sa.String(), nullable=True),
        sa.Column("max_requests_per_min", sa.String(), nullable=True),
        sa.Column("max_tokens_per_month", sa.String(), nullable=True),
        sa.Column("settings", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(["tenant_id"], ["tenants.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("tenant_id"),
    )
    op.create_table(
        "api_auth_configs",
        sa.Column("integration_id", sa.UUID(), nullable=False),
        sa.Column(
            "auth_type",
            postgresql.ENUM(
                "api_key", "oauth2", "bearer", "basic", "custom", name="auth_type"
            ),
            nullable=False,
        ),
        sa.Column(
            "credentials", postgresql.JSONB(astext_type=sa.Text()), nullable=True
        ),
        sa.Column("scopes", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("refresh_token", sa.String(), nullable=True),
        sa.Column("expires_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["integration_id"], ["api_integrations.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("integration_id"),
    )
    op.create_table(
        "api_endpoints",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("integration_id", sa.UUID(), nullable=False),
        sa.Column("path", sa.String(), nullable=False),
        sa.Column("method", sa.String(), nullable=False),
        sa.Column("operation_id", sa.String(), nullable=True),
        sa.Column("action_class_name", sa.String(), nullable=True),
        sa.Column("intent_id", sa.String(), nullable=True),
        sa.Column("enabled", sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(
            ["integration_id"], ["api_integrations.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("api_endpoints", schema=None) as batch_op:
        batch_op.create_index(
            "ix_api_endpoints_integration_id_path_method",
            ["integration_id", "path", "method"],
            unique=True,
        )

    op.create_table(
        "template_dependencies",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("template_id", sa.UUID(), nullable=False),
        sa.Column("depends_on_template_id", sa.UUID(), nullable=False),
        sa.Column(
            "dependency_type",
            postgresql.ENUM("extends", "includes", name="dependency_type"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["depends_on_template_id"], ["templates.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["template_id"], ["templates.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "template_tests",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("template_id", sa.UUID(), nullable=False),
        sa.Column("test_name", sa.String(), nullable=False),
        sa.Column(
            "test_input", postgresql.JSONB(astext_type=sa.Text()), nullable=False
        ),
        sa.Column(
            "expected_output", postgresql.JSONB(astext_type=sa.Text()), nullable=False
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("last_run_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("last_result", sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(["template_id"], ["templates.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "template_versions",
        sa.Column("template_id", sa.UUID(), nullable=False),
        sa.Column("version", sa.Integer(), nullable=False),
        sa.Column("body", sa.TEXT(), nullable=False),
        sa.Column("actions", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("parameters", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("editor_id", sa.UUID(), nullable=True),
        sa.Column(
            "edited_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("change_reason", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(["template_id"], ["templates.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("template_id", "version"),
    )
    op.create_table(
        "api_rate_limits",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("endpoint_id", sa.UUID(), nullable=False),
        sa.Column("requests_per_min", sa.Integer(), nullable=False),
        sa.Column("burst_size", sa.Integer(), nullable=False),
        sa.Column("cooldown_sec", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["endpoint_id"], ["api_endpoints.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("api_rate_limits")
    op.drop_table("template_versions")
    op.drop_table("template_tests")
    op.drop_table("template_dependencies")
    with op.batch_alter_table("api_endpoints", schema=None) as batch_op:
        batch_op.drop_index("ix_api_endpoints_integration_id_path_method")

    op.drop_table("api_endpoints")
    op.drop_table("api_auth_configs")
    op.drop_table("tenant_settings")
    with op.batch_alter_table("templates", schema=None) as batch_op:
        batch_op.drop_index("ix_templates_tenant_id")
        batch_op.drop_index("ix_templates_scope_scope_id_key_version")

    op.drop_table("templates")
    with op.batch_alter_table("api_keys", schema=None) as batch_op:
        batch_op.drop_index("ix_api_keys_tenant_id")
        batch_op.drop_index("ix_api_keys_key_hash")

    op.drop_table("api_keys")
    with op.batch_alter_table("api_integrations", schema=None) as batch_op:
        batch_op.drop_index("ix_api_integrations_tenant_id_name")

    op.drop_table("api_integrations")
    op.drop_table("tenants")
    # ### end Alembic commands ###
