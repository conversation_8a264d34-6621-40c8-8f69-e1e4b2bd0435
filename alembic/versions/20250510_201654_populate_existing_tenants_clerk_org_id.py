"""Populate existing tenants clerk_org_id

Revision ID: 20250510_201654
Revises: 20250510_201505
Create Date: 2025-05-10 20:16:54.000000+00:00

"""
from typing import Any, Dict, Sequence, Union

import sqlalchemy as sa
from sqlalchemy.orm import Session
from sqlalchemy.sql import column, table

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250510_201654'
down_revision: Union[str, None] = '20250510_201505'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# Define a simple table representation for the tenants table
# This is useful for data migrations where the full ORM model might not be available
# or could have changed.
tenants_table = table('tenants',
    column('id', sa.dialects.postgresql.UUID(as_uuid=True)),
    column('name', sa.String),
    column('clerk_org_id', sa.String)
    # Add other columns that might be needed for deriving the clerk_org_id
    # e.g., column('admin_email', sa.String)
)

def get_clerk_org_id_for_tenant(tenant_data: Dict[str, Any]) -> Union[str, None]:
    """
    Placeholder function to determine the Clerk Organization ID for a given tenant.
    Implement the actual mapping logic here.

    Args:
        tenant_data: A dictionary representing a row from the tenants table.
                     It will contain 'id', 'name', and any other columns defined
                     in tenants_table used for the query.

    Returns:
        The Clerk Organization ID as a string, or None if no mapping found.
    """
    tenant_id = tenant_data['id']
    tenant_name = tenant_data['name']
    # Example:
    # if tenant_name == "Acme Corp Old":
    #     return "org_acme_clerk_id"
    # elif tenant_id == uuid.UUID("...some specific uuid..."):
    #     return "org_specific_clerk_id"

    # --- IMPLEMENT YOUR MAPPING LOGIC HERE ---
    # This could involve:
    # 1. Querying an external system (Clerk API) using tenant_name or another identifier.
    # 2. Looking up in a pre-prepared CSV or mapping dictionary.
    # 3. Deriving it based on some convention.
    print(f"INFO: No mapping logic implemented for tenant_id: {tenant_id}, name: {tenant_name}. Skipping.")
    return None


def upgrade() -> None:
    """
    Populates the clerk_org_id for existing tenants.
    For a new system, this will likely not update any rows if no tenants exist,
    or if the mapping logic doesn't find a match.
    """
    bind = op.get_bind()
    session = Session(bind=bind)

    # Fetch all existing tenants
    tenants_to_update = session.execute(sa.select(tenants_table)).mappings().all()

    if not tenants_to_update:
        print("INFO: No existing tenants found to populate clerk_org_id.")
        session.close()
        return

    updated_count = 0
    for tenant_data in tenants_to_update:
        # Ensure tenant_data is a dictionary to pass to get_clerk_org_id_for_tenant
        tenant_dict = dict(tenant_data)
        clerk_org_id = get_clerk_org_id_for_tenant(tenant_dict)

        if clerk_org_id:
            session.execute(
                tenants_table.update().
                where(tenants_table.c.id == tenant_dict['id']).
                values(clerk_org_id=clerk_org_id)
            )
            updated_count += 1
            print(f"INFO: Updated tenant_id {tenant_dict['id']} with clerk_org_id: {clerk_org_id}")
        else:
            print(f"INFO: No clerk_org_id mapping found for tenant_id: {tenant_dict['id']}. Skipping.")

    if updated_count > 0:
        session.commit()
        print(f"INFO: Successfully populated clerk_org_id for {updated_count} tenant(s).")
    else:
        session.rollback() # or session.commit() if no updates is also a success
        print("INFO: No tenants were updated with a clerk_org_id.")
    
    session.close()


def downgrade() -> None:
    """
    The downgrade path for deleting all tenants is not straightforward
    and typically not desired. This function does nothing.
    """
    pass