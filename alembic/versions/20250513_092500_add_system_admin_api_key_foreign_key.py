"""add_system_admin_api_key_foreign_key

Revision ID: a58f91c0a456
Revises: 9dc12e5f43f3
Create Date: 2025-05-13 09:25:00.000000+00:00

"""
from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = 'a58f91c0a456'
down_revision: Union[str, None] = '9dc12e5f43f3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add system_admin_id column to system_admin_api_keys table
    op.add_column('system_admin_api_keys',
                  sa.Column('system_admin_id', postgresql.UUID(as_uuid=True), nullable=True))
    
    # Create the foreign key relationship
    op.create_foreign_key(
        'fk_system_admin_api_key_system_admin_id',
        'system_admin_api_keys', 'system_admins',
        ['system_admin_id'], ['id'],
        ondelete='CASCADE'
    )
    
    # Update existing records (if any) to have a valid system_admin_id
    # This is a placeholder - in a real migration, we would need to populate this field
    # with appropriate data before making it non-nullable
    
    # Make the column non-nullable after all data is migrated
    op.alter_column('system_admin_api_keys', 'system_admin_id', nullable=False)


def downgrade() -> None:
    # Remove the foreign key constraint first
    op.drop_constraint('fk_system_admin_api_key_system_admin_id', 'system_admin_api_keys', type_='foreignkey')
    
    # Then remove the column
    op.drop_column('system_admin_api_keys', 'system_admin_id')