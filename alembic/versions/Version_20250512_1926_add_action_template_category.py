"""Add ACTION category to template_category enum

Revision ID: Version_20250512_1926
Revises: 
Create Date: 2025-05-12 19:26
"""

from alembic import op
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from utils.enum_helpers import add_enum_value_safe

# revision identifiers, used by Alembic.
revision = 'Version_20250512_1926'
down_revision = 'a58f91c0a456'  # Pointing to the existing migration head
branch_labels = None
depends_on = None


def upgrade():
    # Add 'action' to the template_category enum
    add_enum_value_safe('template_category', 'action')


def downgrade():
    # Cannot easily remove enum values in PostgreSQL
    pass
