"""add_rls_to_integration_tables

Revision ID: 20250510_203523
Revises: 20250510_203433
Create Date: 2025-05-10 20:35:23.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250510_203523'
down_revision: Union[str, None] = '20250510_203433'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# Assumes helper functions like is_system_admin(), current_session_clerk_org_id(),
# current_tenant_id(), and current_setting('app.rls.lookup_clerk_org_id', true)
# are defined and available from previous migrations.

# This is the core logic for checking access to a parent tenant.
# It mirrors the USING clause of the 'select_for_tenants' policy.
PARENT_TENANT_ACCESS_CONDITION = """
    is_system_admin() OR
    (pt.clerk_org_id IS NOT NULL AND pt.clerk_org_id = current_session_clerk_org_id()) OR
    (pt.id = current_tenant_id()) OR
    ( 
        pt.clerk_org_id IS NOT NULL AND
        pt.clerk_org_id = current_setting('app.rls.lookup_clerk_org_id', true) AND
        current_tenant_id() IS NULL AND 
        is_system_admin() = false
    )
"""

# Define tables and their link to tenants table or intermediate tables
TABLE_CONFIGS = [
    {
        "name": "api_integrations",
        "policy_name": "rls_api_integrations_access",
        "fk_to_tenants_col": "tenant_id", # Direct FK to tenants.id
        "join_condition": f"EXISTS (SELECT 1 FROM tenants pt WHERE pt.id = api_integrations.tenant_id AND ({PARENT_TENANT_ACCESS_CONDITION}))"
    },
    {
        "name": "api_auth_configs",
        "policy_name": "rls_api_auth_configs_access",
        # Links to api_integrations.id, then api_integrations.tenant_id to tenants.id
        "join_condition": f"""
            EXISTS (
                SELECT 1 FROM api_integrations ai
                JOIN tenants pt ON ai.tenant_id = pt.id
                WHERE ai.id = api_auth_configs.integration_id AND ({PARENT_TENANT_ACCESS_CONDITION})
            )
        """
    },
    {
        "name": "api_endpoints",
        "policy_name": "rls_api_endpoints_access",
        # Links to api_integrations.id, then api_integrations.tenant_id to tenants.id
        "join_condition": f"""
            EXISTS (
                SELECT 1 FROM api_integrations ai
                JOIN tenants pt ON ai.tenant_id = pt.id
                WHERE ai.id = api_endpoints.integration_id AND ({PARENT_TENANT_ACCESS_CONDITION})
            )
        """
    },
    {
        "name": "api_rate_limits",
        "policy_name": "rls_api_rate_limits_access",
        # Links to api_endpoints.id -> api_integrations.id -> tenants.id
        "join_condition": f"""
            EXISTS (
                SELECT 1 FROM api_endpoints ae
                JOIN api_integrations ai ON ae.integration_id = ai.id
                JOIN tenants pt ON ai.tenant_id = pt.id
                WHERE ae.id = api_rate_limits.endpoint_id AND ({PARENT_TENANT_ACCESS_CONDITION})
            )
        """
    }
]

def upgrade() -> None:
    for config in TABLE_CONFIGS:
        table_name = config["name"]
        policy_name = config["policy_name"]
        join_condition = config["join_condition"]

        op.execute(f"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY;")
        op.execute(f"ALTER TABLE {table_name} FORCE ROW LEVEL SECURITY;")
        op.execute(f"DROP POLICY IF EXISTS {policy_name} ON {table_name};")

        policy_sql = f"""
        CREATE POLICY {policy_name} ON {table_name}
        FOR ALL
        USING (
            is_system_admin() OR
            {join_condition}
        );
        """
        op.execute(policy_sql)

def downgrade() -> None:
    for config in TABLE_CONFIGS:
        table_name = config["name"]
        policy_name = config["policy_name"]
        op.execute(f"DROP POLICY IF EXISTS {policy_name} ON {table_name};")
        op.execute(f"-- Policy {policy_name} on {table_name} dropped. RLS on table remains enabled.")
        # Optionally, disable RLS if it was not enabled before this migration for these tables.
        # op.execute(f"ALTER TABLE {table_name} DISABLE ROW LEVEL SECURITY;")