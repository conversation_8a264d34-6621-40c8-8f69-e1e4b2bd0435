"""Add DOCUMENTATION category to template_category enum

Revision ID: 20250101_000001
Revises: Version_20250512_1926
Create Date: 2025-01-01 00:00:01
"""

from alembic import op
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from utils.enum_helpers import add_enum_value_safe

# revision identifiers, used by Alembic.
revision = '20250518_000001'
down_revision = '20250516_133000'
branch_labels = None
depends_on = None


def upgrade():
    # Add 'documentation' to the template_category enum
    add_enum_value_safe('template_category', 'documentation')


def downgrade():
    # Cannot easily remove enum values in PostgreSQL
    # This would require recreating the enum type which is complex
    pass