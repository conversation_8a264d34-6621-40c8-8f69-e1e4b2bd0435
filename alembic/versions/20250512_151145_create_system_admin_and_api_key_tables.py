"""create_system_admin_and_api_key_tables

Revision ID: 9dc12e5f43f3
Revises: 63344857d38d  # Adjust if the previous revision is different
Create Date: 2025-05-12 15:11:45.939211+00:00

"""
from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '9dc12e5f43f3'
down_revision: Union[str, None] = '63344857d38d' # Make sure this points to the correct previous migration
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands manually created based on models ###
    op.create_table('system_admins',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('clerk_user_id', sa.String(), unique=True, nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.Column('created_by', sa.String(), nullable=False)
    )
    op.create_table('system_admin_api_keys',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('key_hash', sa.String(), unique=True, nullable=False, index=True),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('permissions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_by', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.Column('last_used_at', sa.DateTime(), nullable=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('revoked', sa.Boolean(), server_default=sa.false(), nullable=False)
    )
    # ### end commands ###


def downgrade() -> None:
    # ### commands manually created based on models ###
    op.drop_table('system_admin_api_keys')
    op.drop_table('system_admins')
    # ### end commands ###