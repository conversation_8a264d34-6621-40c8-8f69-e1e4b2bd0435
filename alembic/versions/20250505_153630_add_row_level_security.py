"""Add Row-Level Security policies

Revision ID: 2fe15b4cbf91
Revises: 1fe15b4cbf90
Create Date: 2025-05-05 15:36:30.123456+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "2fe15b4cbf91"
down_revision: Union[str, None] = "1fe15b4cbf90"  # Points to initial schema setup
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create a function to get the current tenant ID from session context
    op.execute(
        """
    CREATE OR REPLACE FUNCTION current_tenant_id()
    RETURNS UUID AS $$
    DECLARE
        tenant_id TEXT;
    BEGIN
        tenant_id := current_setting('app.current_tenant_id', TRUE);
        -- Handle the case where the setting is 'NULL' or NULL
        IF tenant_id IS NULL OR tenant_id = 'NULL' OR tenant_id = '' THEN
            RETURN NULL;
        END IF;
        RETURN tenant_id::UUID;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN NULL;
    END;
    $$ LANGUAGE plpgsql;
    """
    )

    # Create a function to check if user is a system admin
    op.execute(
        """
    CREATE OR REPLACE FUNCTION is_system_admin()
    RETURNS BOOLEAN AS $$
    DECLARE
        is_admin TEXT;
    BEGIN
        is_admin := current_setting('app.is_system_admin', TRUE);
        -- Handle case where the setting is 'true', 'false', NULL, or ''
        IF is_admin IS NULL OR is_admin = '' THEN
            RETURN FALSE;
        ELSIF is_admin = 'true' THEN
            RETURN TRUE;
        ELSIF is_admin = 'false' THEN
            RETURN FALSE;
        ELSE
            -- Try to cast to boolean if it's not one of the expected values
            RETURN is_admin::BOOLEAN;
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN FALSE;
    END;
    $$ LANGUAGE plpgsql;
    """
    )

    # --------------------------------
    # Enable Row-Level Security on all tenant-specific tables
    # --------------------------------

    # API Keys table
    op.execute("ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;")

    # RLS policy for API Keys table - restrict access to tenant's own records or system admins
    op.execute(
        """
    CREATE POLICY tenant_isolation_api_keys ON api_keys
    USING (
        tenant_id = current_tenant_id() OR
        is_system_admin() OR
        current_tenant_id() IS NULL
    );
    """
    )

    # API Integrations table
    op.execute("ALTER TABLE api_integrations ENABLE ROW LEVEL SECURITY;")

    # RLS policy for API Integrations table
    op.execute(
        """
    CREATE POLICY tenant_isolation_api_integrations ON api_integrations
    USING (
        tenant_id = current_tenant_id() OR
        is_system_admin() OR
        current_tenant_id() IS NULL
    );
    """
    )

    # API Auth Configs table
    op.execute("ALTER TABLE api_auth_configs ENABLE ROW LEVEL SECURITY;")

    # RLS policy for API Auth Configs table - using join to api_integrations
    op.execute(
        """
    CREATE POLICY tenant_isolation_api_auth_configs ON api_auth_configs
    USING (
        EXISTS (
            SELECT 1 FROM api_integrations
            WHERE api_integrations.id = api_auth_configs.integration_id
            AND (api_integrations.tenant_id = current_tenant_id() OR is_system_admin() OR current_tenant_id() IS NULL)
        )
    );
    """
    )

    # API Endpoints table
    op.execute("ALTER TABLE api_endpoints ENABLE ROW LEVEL SECURITY;")

    # RLS policy for API Endpoints table - using join to api_integrations
    op.execute(
        """
    CREATE POLICY tenant_isolation_api_endpoints ON api_endpoints
    USING (
        EXISTS (
            SELECT 1 FROM api_integrations
            WHERE api_integrations.id = api_endpoints.integration_id
            AND (api_integrations.tenant_id = current_tenant_id() OR is_system_admin() OR current_tenant_id() IS NULL)
        )
    );
    """
    )

    # API Rate Limits table
    op.execute("ALTER TABLE api_rate_limits ENABLE ROW LEVEL SECURITY;")

    # RLS policy for API Rate Limits table - nested joins
    op.execute(
        """
    CREATE POLICY tenant_isolation_api_rate_limits ON api_rate_limits
    USING (
        EXISTS (
            SELECT 1 FROM api_endpoints
            JOIN api_integrations ON api_integrations.id = api_endpoints.integration_id
            WHERE api_endpoints.id = api_rate_limits.endpoint_id
            AND (api_integrations.tenant_id = current_tenant_id() OR is_system_admin() OR current_tenant_id() IS NULL)
        )
    );
    """
    )

    # Templates table (more complex - handles scopes)
    op.execute("ALTER TABLE templates ENABLE ROW LEVEL SECURITY;")

    # RLS policy for Templates table that respects global/pack/tenant scope
    op.execute(
        """
    CREATE POLICY tenant_isolation_templates ON templates
    USING (
        -- System admin can see all templates
        is_system_admin() OR
        -- Context not set - allow access (internal operation)
        current_tenant_id() IS NULL OR 
        -- Tenant can see their own templates
        (tenant_id = current_tenant_id()) OR
        -- Everyone can see global templates
        (scope = 'global') OR
        -- Tenant can see templates from their industry pack
        (scope = 'pack' AND EXISTS (
            SELECT 1 FROM tenants
            WHERE tenants.id = current_tenant_id()
            AND tenants.industry_pack::TEXT = templates.scope_id::TEXT
        ))
    );
    """
    )

    # Template Versions table
    op.execute("ALTER TABLE template_versions ENABLE ROW LEVEL SECURITY;")

    # RLS policy for Template Versions - linked to templates
    op.execute(
        """
    CREATE POLICY tenant_isolation_template_versions ON template_versions
    USING (
        EXISTS (
            SELECT 1 FROM templates
            WHERE templates.id = template_versions.template_id
            AND (
                is_system_admin() OR
                current_tenant_id() IS NULL OR
                tenant_id = current_tenant_id() OR
                scope = 'global' OR
                (scope = 'pack' AND EXISTS (
                    SELECT 1 FROM tenants
                    WHERE tenants.id = current_tenant_id()
                    AND tenants.industry_pack::TEXT = templates.scope_id::TEXT
                ))
            )
        )
    );
    """
    )

    # Template Tests table
    op.execute("ALTER TABLE template_tests ENABLE ROW LEVEL SECURITY;")

    # RLS policy for Template Tests
    op.execute(
        """
    CREATE POLICY tenant_isolation_template_tests ON template_tests
    USING (
        EXISTS (
            SELECT 1 FROM templates
            WHERE templates.id = template_tests.template_id
            AND (
                is_system_admin() OR
                current_tenant_id() IS NULL OR
                tenant_id = current_tenant_id() OR
                scope = 'global' OR
                (scope = 'pack' AND EXISTS (
                    SELECT 1 FROM tenants
                    WHERE tenants.id = current_tenant_id()
                    AND tenants.industry_pack::TEXT = templates.scope_id::TEXT
                ))
            )
        )
    );
    """
    )

    # Template Dependencies table
    op.execute("ALTER TABLE template_dependencies ENABLE ROW LEVEL SECURITY;")

    # RLS policy for Template Dependencies
    op.execute(
        """
    CREATE POLICY tenant_isolation_template_dependencies ON template_dependencies
    USING (
        EXISTS (
            SELECT 1 FROM templates
            WHERE templates.id = template_dependencies.template_id
            AND (
                is_system_admin() OR
                current_tenant_id() IS NULL OR
                tenant_id = current_tenant_id() OR
                scope = 'global' OR
                (scope = 'pack' AND EXISTS (
                    SELECT 1 FROM tenants
                    WHERE tenants.id = current_tenant_id()
                    AND tenants.industry_pack::TEXT = templates.scope_id::TEXT
                ))
            )
        )
    );
    """
    )

    # Tenant Settings table
    op.execute("ALTER TABLE tenant_settings ENABLE ROW LEVEL SECURITY;")

    # RLS policy for Tenant Settings
    op.execute(
        """
    CREATE POLICY tenant_isolation_tenant_settings ON tenant_settings
    USING (
        tenant_id = current_tenant_id() OR
        is_system_admin() OR
        current_tenant_id() IS NULL
    );
    """
    )

    # --------------------------------
    # Create trigger functions for data validation
    # --------------------------------

    # Enforce tenant constraints on insert/update
    op.execute(
        """
    CREATE OR REPLACE FUNCTION check_tenant_constraint()
    RETURNS TRIGGER AS $$
    BEGIN
        -- Skip if system admin or no tenant context set
        IF is_system_admin() OR current_tenant_id() IS NULL THEN
            RETURN NEW;
        END IF;
        
        -- For tables with direct tenant_id column
        IF TG_TABLE_NAME IN ('api_keys', 'api_integrations', 'templates', 'tenant_settings') THEN
            IF NEW.tenant_id IS DISTINCT FROM current_tenant_id() THEN
                RAISE EXCEPTION 'Cannot insert or update records for different tenant';
            END IF;
        END IF;
        
        -- For tables with indirect tenant relationship through api_integrations
        IF TG_TABLE_NAME IN ('api_auth_configs', 'api_endpoints') THEN
            IF EXISTS (
                SELECT 1 FROM api_integrations
                WHERE api_integrations.id = NEW.integration_id
                AND api_integrations.tenant_id IS DISTINCT FROM current_tenant_id()
            ) THEN
                RAISE EXCEPTION 'Cannot insert or update records for different tenant';
            END IF;
        END IF;
        
        -- For api_rate_limits (deeper relationship)
        IF TG_TABLE_NAME = 'api_rate_limits' THEN
            IF EXISTS (
                SELECT 1 FROM api_endpoints
                JOIN api_integrations ON api_integrations.id = api_endpoints.integration_id
                WHERE api_endpoints.id = NEW.endpoint_id
                AND api_integrations.tenant_id IS DISTINCT FROM current_tenant_id()
            ) THEN
                RAISE EXCEPTION 'Cannot insert or update records for different tenant';
            END IF;
        END IF;
        
        -- For template relationships
        IF TG_TABLE_NAME IN ('template_versions', 'template_tests', 'template_dependencies') THEN
            -- Validate based on template_id
            IF EXISTS (
                SELECT 1 FROM templates
                WHERE templates.id = 
                    CASE 
                        WHEN TG_TABLE_NAME = 'template_dependencies' THEN NEW.template_id 
                        ELSE NEW.template_id 
                    END
                AND templates.tenant_id IS DISTINCT FROM current_tenant_id()
                AND templates.scope <> 'global'
                AND (templates.scope <> 'pack' OR NOT EXISTS (
                    SELECT 1 FROM tenants
                    WHERE tenants.id = current_tenant_id()
                    AND tenants.industry_pack::TEXT = templates.scope_id::TEXT
                ))
            ) THEN
                RAISE EXCEPTION 'Cannot insert or update records for templates from different tenant';
            END IF;
        END IF;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """
    )

    # Create triggers for all tables
    op.execute(
        """
    CREATE TRIGGER enforce_tenant_constraint_api_keys
    BEFORE INSERT OR UPDATE ON api_keys
    FOR EACH ROW EXECUTE FUNCTION check_tenant_constraint();
    """
    )

    op.execute(
        """
    CREATE TRIGGER enforce_tenant_constraint_api_integrations
    BEFORE INSERT OR UPDATE ON api_integrations
    FOR EACH ROW EXECUTE FUNCTION check_tenant_constraint();
    """
    )

    op.execute(
        """
    CREATE TRIGGER enforce_tenant_constraint_api_auth_configs
    BEFORE INSERT OR UPDATE ON api_auth_configs
    FOR EACH ROW EXECUTE FUNCTION check_tenant_constraint();
    """
    )

    op.execute(
        """
    CREATE TRIGGER enforce_tenant_constraint_api_endpoints
    BEFORE INSERT OR UPDATE ON api_endpoints
    FOR EACH ROW EXECUTE FUNCTION check_tenant_constraint();
    """
    )

    op.execute(
        """
    CREATE TRIGGER enforce_tenant_constraint_api_rate_limits
    BEFORE INSERT OR UPDATE ON api_rate_limits
    FOR EACH ROW EXECUTE FUNCTION check_tenant_constraint();
    """
    )

    op.execute(
        """
    CREATE TRIGGER enforce_tenant_constraint_templates
    BEFORE INSERT OR UPDATE ON templates
    FOR EACH ROW EXECUTE FUNCTION check_tenant_constraint();
    """
    )

    op.execute(
        """
    CREATE TRIGGER enforce_tenant_constraint_template_versions
    BEFORE INSERT OR UPDATE ON template_versions
    FOR EACH ROW EXECUTE FUNCTION check_tenant_constraint();
    """
    )

    op.execute(
        """
    CREATE TRIGGER enforce_tenant_constraint_template_tests
    BEFORE INSERT OR UPDATE ON template_tests
    FOR EACH ROW EXECUTE FUNCTION check_tenant_constraint();
    """
    )

    op.execute(
        """
    CREATE TRIGGER enforce_tenant_constraint_template_dependencies
    BEFORE INSERT OR UPDATE ON template_dependencies
    FOR EACH ROW EXECUTE FUNCTION check_tenant_constraint();
    """
    )

    op.execute(
        """
    CREATE TRIGGER enforce_tenant_constraint_tenant_settings
    BEFORE INSERT OR UPDATE ON tenant_settings
    FOR EACH ROW EXECUTE FUNCTION check_tenant_constraint();
    """
    )


def downgrade() -> None:
    # Drop all the triggers
    op.execute("DROP TRIGGER IF EXISTS enforce_tenant_constraint_api_keys ON api_keys;")
    op.execute(
        "DROP TRIGGER IF EXISTS enforce_tenant_constraint_api_integrations ON api_integrations;"
    )
    op.execute(
        "DROP TRIGGER IF EXISTS enforce_tenant_constraint_api_auth_configs ON api_auth_configs;"
    )
    op.execute(
        "DROP TRIGGER IF EXISTS enforce_tenant_constraint_api_endpoints ON api_endpoints;"
    )
    op.execute(
        "DROP TRIGGER IF EXISTS enforce_tenant_constraint_api_rate_limits ON api_rate_limits;"
    )
    op.execute(
        "DROP TRIGGER IF EXISTS enforce_tenant_constraint_templates ON templates;"
    )
    op.execute(
        "DROP TRIGGER IF EXISTS enforce_tenant_constraint_template_versions ON template_versions;"
    )
    op.execute(
        "DROP TRIGGER IF EXISTS enforce_tenant_constraint_template_tests ON template_tests;"
    )
    op.execute(
        "DROP TRIGGER IF EXISTS enforce_tenant_constraint_template_dependencies ON template_dependencies;"
    )
    op.execute(
        "DROP TRIGGER IF EXISTS enforce_tenant_constraint_tenant_settings ON tenant_settings;"
    )

    # Drop the trigger function
    op.execute("DROP FUNCTION IF EXISTS check_tenant_constraint();")

    # Drop all policies
    op.execute("DROP POLICY IF EXISTS tenant_isolation_api_keys ON api_keys;")
    op.execute(
        "DROP POLICY IF EXISTS tenant_isolation_api_integrations ON api_integrations;"
    )
    op.execute(
        "DROP POLICY IF EXISTS tenant_isolation_api_auth_configs ON api_auth_configs;"
    )
    op.execute("DROP POLICY IF EXISTS tenant_isolation_api_endpoints ON api_endpoints;")
    op.execute(
        "DROP POLICY IF EXISTS tenant_isolation_api_rate_limits ON api_rate_limits;"
    )
    op.execute("DROP POLICY IF EXISTS tenant_isolation_templates ON templates;")
    op.execute(
        "DROP POLICY IF EXISTS tenant_isolation_template_versions ON template_versions;"
    )
    op.execute(
        "DROP POLICY IF EXISTS tenant_isolation_template_tests ON template_tests;"
    )
    op.execute(
        "DROP POLICY IF EXISTS tenant_isolation_template_dependencies ON template_dependencies;"
    )
    op.execute(
        "DROP POLICY IF EXISTS tenant_isolation_tenant_settings ON tenant_settings;"
    )

    # Disable RLS on all tables
    op.execute("ALTER TABLE api_keys DISABLE ROW LEVEL SECURITY;")
    op.execute("ALTER TABLE api_integrations DISABLE ROW LEVEL SECURITY;")
    op.execute("ALTER TABLE api_auth_configs DISABLE ROW LEVEL SECURITY;")
    op.execute("ALTER TABLE api_endpoints DISABLE ROW LEVEL SECURITY;")
    op.execute("ALTER TABLE api_rate_limits DISABLE ROW LEVEL SECURITY;")
    op.execute("ALTER TABLE templates DISABLE ROW LEVEL SECURITY;")
    op.execute("ALTER TABLE template_versions DISABLE ROW LEVEL SECURITY;")
    op.execute("ALTER TABLE template_tests DISABLE ROW LEVEL SECURITY;")
    op.execute("ALTER TABLE template_dependencies DISABLE ROW LEVEL SECURITY;")
    op.execute("ALTER TABLE tenant_settings DISABLE ROW LEVEL SECURITY;")

    # Drop helper functions
    op.execute("DROP FUNCTION IF EXISTS current_tenant_id();")
    op.execute("DROP FUNCTION IF EXISTS is_system_admin();")
