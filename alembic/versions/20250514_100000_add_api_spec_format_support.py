"""add_api_spec_format_support

Revision ID: 20250514_100000
Revises: 20250513_110000
Create Date: 2025-05-14 10:00:00.000000+00:00

"""
from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>, UUID

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250514_100000'
down_revision: Union[str, None] = '20250513_110000'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

PARENT_TENANT_ACCESS_CONDITION = """
    is_system_admin() OR
    (pt.clerk_org_id IS NOT NULL AND pt.clerk_org_id = current_session_clerk_org_id()) OR
    (pt.id = current_tenant_id()) OR
    ( 
        pt.clerk_org_id IS NOT NULL AND
        pt.clerk_org_id = current_setting('app.rls.lookup_clerk_org_id', true) AND
        current_tenant_id() IS NULL AND 
        is_system_admin() = false
    )
"""

def upgrade() -> None:
    # Create enum type for API specification format if it doesn't exist
    op.execute("""
    DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'spec_format') THEN
            CREATE TYPE spec_format AS ENUM ('openapi', 'fapi', 'bapi');
        END IF;
    END
    $$;
    """)
    
    # Add spec_format column to api_integrations table if it doesn't exist
    connection = op.get_bind()
    result = connection.execute(sa.text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'api_integrations' AND column_name = 'spec_format';
    """))
    if not result.fetchone():
        op.add_column(
            'api_integrations',
            sa.Column('spec_format', sa.Enum('openapi', 'fapi', 'bapi', name='spec_format'), 
                    nullable=False, server_default='openapi')
        )
    
    # Check if api_original_specs table exists
    connection = op.get_bind()
    result = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'api_original_specs'
        );
    """))
    table_exists = result.scalar()
    
    if not table_exists:
        # Create api_original_specs table
        op.create_table(
            'api_original_specs',
            sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()')),
            sa.Column('integration_id', UUID(as_uuid=True), sa.ForeignKey('api_integrations.id', ondelete='CASCADE'), 
                    nullable=False, unique=True),
            sa.Column('format', sa.String(50), nullable=False),
            sa.Column('content', JSONB, nullable=False),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'))
        )
        
        # Add index on integration_id
        op.create_index(
            'ix_api_original_specs_integration_id',
            'api_original_specs',
            ['integration_id']
        )
    
    # Check if the table exists before applying RLS
    if table_exists:
        # Enable row level security on api_original_specs
        op.execute("ALTER TABLE api_original_specs ENABLE ROW LEVEL SECURITY;")
        op.execute("ALTER TABLE api_original_specs FORCE ROW LEVEL SECURITY;")
        
        # Check if policy already exists
        check_policy_sql = sa.text("""
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'api_original_specs' AND policyname = 'rls_api_original_specs_access';
        """)
        result = connection.execute(check_policy_sql)
        policy_exists = result.scalar() is not None
        
        if not policy_exists:
            # Create policy for tenant isolation
            policy_sql = f"""
            CREATE POLICY rls_api_original_specs_access ON api_original_specs
            FOR ALL
            USING (
                is_system_admin() OR
                EXISTS (
                    SELECT 1 FROM api_integrations ai
                    JOIN tenants pt ON ai.tenant_id = pt.id
                    WHERE ai.id = api_original_specs.integration_id AND ({PARENT_TENANT_ACCESS_CONDITION})
                )
            );
            """
            op.execute(policy_sql)

def downgrade() -> None:
    # Drop policy
    op.execute("DROP POLICY IF EXISTS rls_api_original_specs_access ON api_original_specs;")
    
    # Drop table
    op.drop_table('api_original_specs')
    
    # Remove spec_format column from api_integrations
    op.drop_column('api_integrations', 'spec_format')
    
    # Drop enum type
    op.execute("DROP TYPE spec_format;")