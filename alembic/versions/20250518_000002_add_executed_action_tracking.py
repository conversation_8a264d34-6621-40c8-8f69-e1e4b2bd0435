"""Add executed_action tracking

Revision ID: 20250101_000002
Revises: 20250101_000001
Create Date: 2025-01-01 00:00:02.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250518_000002'
down_revision = '20250518_000001'
branch_labels = None
depends_on = None


def upgrade():
    # Create the action_type enum if it doesn't exist
    op.execute("""
        DO $$ BEGIN
            CREATE TYPE action_type AS ENUM ('api_call', 'documentation', 'system');
        EXCEPTION
            WHEN duplicate_object THEN NULL;
        END $$;
    """)
    
    # Create the parameter_collection_state enum if it doesn't exist
    op.execute("""
        DO $$ BEGIN
            CREATE TYPE parameter_collection_state AS ENUM ('not_started', 'in_progress', 'completed', 'failed');
        EXCEPTION
            WHEN duplicate_object THEN NULL;
        END $$;
    """)
    
    # Create executed_actions table
    op.create_table('executed_actions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, server_default=sa.text('gen_random_uuid()')),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('template_id', postgresql.UUID(as_uuid=True), nullable=True),
        
        sa.Column('conversation_id', sa.String(), nullable=False),
        sa.Column('intent_name', sa.String(), nullable=False),
        sa.Column('action_type', postgresql.ENUM('api_call', 'documentation', 'system', name='action_type', create_type=False), nullable=False),
        
        sa.Column('parameter_state', postgresql.ENUM('not_started', 'in_progress', 'completed', 'failed', name='parameter_collection_state', create_type=False), nullable=False, server_default='not_started'),
        sa.Column('parameters_collected', sa.JSON(), nullable=True),
        sa.Column('parameters_missing', sa.JSON(), nullable=True),
        sa.Column('parameter_collection_rounds', sa.Integer(), nullable=False, server_default='0'),
        
        sa.Column('is_documentation_request', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('documentation_endpoint', sa.String(), nullable=True),
        sa.Column('documentation_method', sa.String(), nullable=True),
        sa.Column('documentation_response_format', sa.String(), nullable=True),
        
        sa.Column('api_responses', sa.JSON(), nullable=True),
        sa.Column('final_response', sa.Text(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('execution_time_ms', sa.Integer(), nullable=True),
        
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['template_id'], ['templates.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index('ix_executed_actions_conversation_id', 'executed_actions', ['conversation_id'])
    op.create_index('ix_executed_actions_tenant_id', 'executed_actions', ['tenant_id'])
    op.create_index('ix_executed_actions_user_id', 'executed_actions', ['user_id'])
    op.create_index('ix_executed_actions_created_at', 'executed_actions', ['created_at'])
    
    # Add RLS policy for tenant isolation
    op.execute("""
        -- Create superuser role if it doesn't exist
        DO $$ 
        BEGIN
            IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'coherence_superuser') THEN
                CREATE ROLE coherence_superuser;
            END IF;
        END $$;
        
        ALTER TABLE executed_actions ENABLE ROW LEVEL SECURITY;
        
        CREATE POLICY tenant_isolation_policy ON executed_actions
            FOR ALL 
            USING (tenant_id::text = current_setting('app.tenant_id', true));
            
        CREATE POLICY superuser_bypass_policy ON executed_actions
            FOR ALL
            TO coherence_superuser
            USING (true);
    """)


def downgrade():
    # Drop RLS policies
    op.execute("""
        DROP POLICY IF EXISTS superuser_bypass_policy ON executed_actions;
        DROP POLICY IF EXISTS tenant_isolation_policy ON executed_actions;
        ALTER TABLE executed_actions DISABLE ROW LEVEL SECURITY;
    """)
    
    # Drop indexes
    op.drop_index('ix_executed_actions_created_at', table_name='executed_actions')
    op.drop_index('ix_executed_actions_user_id', table_name='executed_actions')
    op.drop_index('ix_executed_actions_tenant_id', table_name='executed_actions')
    op.drop_index('ix_executed_actions_conversation_id', table_name='executed_actions')
    
    # Drop table
    op.drop_table('executed_actions')
    
    # Drop enums (only if we can confirm they're not used elsewhere)
    op.execute("""
        DO $$ 
        BEGIN
            -- Check if action_type is used by other tables
            IF NOT EXISTS (
                SELECT 1
                FROM pg_type t
                JOIN pg_attribute a ON t.oid = a.atttypid
                JOIN pg_class c ON a.attrelid = c.oid
                WHERE t.typname = 'action_type'
                AND c.relname != 'executed_actions'
            ) THEN
                DROP TYPE IF EXISTS action_type;
            END IF;
            
            -- Check if parameter_collection_state is used by other tables
            IF NOT EXISTS (
                SELECT 1
                FROM pg_type t
                JOIN pg_attribute a ON t.oid = a.atttypid
                JOIN pg_class c ON a.attrelid = c.oid
                WHERE t.typname = 'parameter_collection_state'
                AND c.relname != 'executed_actions'
            ) THEN
                DROP TYPE IF EXISTS parameter_collection_state;
            END IF;
        END $$;
    """)