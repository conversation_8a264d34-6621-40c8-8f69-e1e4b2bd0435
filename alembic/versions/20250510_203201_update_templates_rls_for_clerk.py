"""update_templates_rls_for_clerk

Revision ID: 20250510_203201
Revises: 20250510_203113
Create Date: 2025-05-10 20:32:01.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250510_203201'
down_revision: Union[str, None] = '20250510_203113'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

TABLE_NAME = "templates"
POLICY_NAME = "templates_isolation_policy"

# Assumes helper functions like is_system_admin(), current_session_clerk_org_id(),
# and current_tenant_id() are defined and available from previous migrations.

def get_previous_templates_policy_if_any() -> str:
    # If there was a known previous policy, define its creation SQL here.
    # For this example, we'll assume there might not have been one, or it was very basic.
    # If a specific policy needs to be restored, its definition should be returned.
    # This is a placeholder; actual downgrade might be more complex or simply remove the new policy.
    return f"-- Previous policy for {TABLE_NAME} (if any) would be restored here. For now, just dropping the new one."


def upgrade() -> None:
    # Ensure RLS is enabled on the templates table
    op.execute(f"ALTER TABLE {TABLE_NAME} ENABLE ROW LEVEL SECURITY;")
    op.execute(f"ALTER TABLE {TABLE_NAME} FORCE ROW LEVEL SECURITY;")

    # Drop any existing policy with the same name to avoid errors if re-running
    op.execute(f"DROP POLICY IF EXISTS {POLICY_NAME} ON {TABLE_NAME};")

    # Create the RLS policy for templates
    # Access is allowed if:
    # 1. User is a system admin.
    # 2. Template scope is 'global'.
    # 3. Template scope is 'pack' (assuming packs are generally readable by authenticated users,
    #    or this needs further refinement based on pack entitlements).
    # 4. For 'tenant' scoped templates:
    #    a. The template's tenant_id maps to a tenant whose clerk_org_id matches the session's clerk_org_id.
    #    b. (Transitional) The template's tenant_id matches the session's old current_tenant_id().
    op.execute(f"""
    CREATE POLICY {POLICY_NAME} ON {TABLE_NAME}
    FOR ALL  -- Applies to SELECT, INSERT, UPDATE, DELETE
    USING (
        is_system_admin() OR
        scope = 'global' OR
        scope = 'pack' OR  -- Review: Access control for 'pack' scope might need more specific rules.
        (
            scope = 'tenant' AND (
                EXISTS (
                    SELECT 1 FROM tenants t
                    WHERE t.id = {TABLE_NAME}.tenant_id
                      AND t.clerk_org_id IS NOT NULL
                      AND t.clerk_org_id = current_session_clerk_org_id()
                ) OR
                ({TABLE_NAME}.tenant_id = current_tenant_id()) 
            )
        )
    );
    """)

def downgrade() -> None:
    op.execute(f"DROP POLICY IF EXISTS {POLICY_NAME} ON {TABLE_NAME};")
    
    # Restore previous policy if one was defined and known
    # op.execute(get_previous_templates_policy_if_any()) 
    # For now, simply dropping the policy. If RLS was not enabled before,
    # one might consider disabling it, but that depends on prior state.
    # op.execute(f"ALTER TABLE {TABLE_NAME} DISABLE ROW LEVEL SECURITY;")
    # Keeping RLS enabled but without this specific policy is safer if other policies might exist
    # or if RLS was intended to be on.
    op.execute(f"-- Policy {POLICY_NAME} dropped. RLS on {TABLE_NAME} remains enabled unless other downgrade steps disable it.")