"""add clerk org id

Revision ID: 20250509_180645
Revises: 20250508_164200
Create Date: 2025-05-09 18:06:45.000000+00:00

"""
from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250509_180645'
down_revision: Union[str, None] = '20250508_164200'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add clerk_org_id column to tenants table
    op.add_column('tenants', sa.Column('clerk_org_id', sa.String(), nullable=True, unique=True))
    op.create_unique_constraint('uq_tenants_clerk_org_id', 'tenants', ['clerk_org_id'])


def downgrade() -> None:
    # Remove clerk_org_id column from tenants table
    op.drop_constraint('uq_tenants_clerk_org_id', 'tenants', type_='unique')
    op.drop_column('tenants', 'clerk_org_id')
