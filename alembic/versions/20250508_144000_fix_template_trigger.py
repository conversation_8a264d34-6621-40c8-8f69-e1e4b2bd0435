"""fix_template_trigger

Revision ID: 20250508_144000
Revises: 20250507_135841
Create Date: 2025-05-08 14:40:00.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250508_144000'
down_revision: Union[str, None] = '20250507_135841'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    Fix the template trigger to correctly use 'body' field instead of 'content'.
    """
    # Drop the existing trigger
    op.execute("DROP TRIGGER IF EXISTS version_template ON templates;")
    
    # Drop the existing function
    op.execute("DROP FUNCTION IF EXISTS save_template_version CASCADE;")
    
    # Create a new function that correctly maps the fields
    op.execute("""
    CREATE OR REPLACE FUNCTION save_template_version()
    RETURNS TRIGGER AS $$
    BEGIN
        INSERT INTO template_versions (
            id, 
            template_id, 
            tenant_id, 
            version, 
            content,
            body,
            actions,
            parameters
        )
        VALUES (
            gen_random_uuid(),
            OLD.id,
            OLD.tenant_id,
            (SELECT COALESCE(MAX(version), 0) + 1 FROM template_versions WHERE template_id = OLD.id),
            to_jsonb(OLD),
            OLD.body,
            OLD.actions,
            OLD.parameters
        );
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """)
    
    # Re-create the trigger
    op.execute("""
    CREATE TRIGGER version_template
    BEFORE UPDATE ON templates
    FOR EACH ROW EXECUTE FUNCTION save_template_version();
    """)


def downgrade() -> None:
    """
    Restore the original trigger (which has the issue with 'content' vs 'body').
    """
    # Drop the fixed trigger
    op.execute("DROP TRIGGER IF EXISTS version_template ON templates;")
    
    # Drop the fixed function
    op.execute("DROP FUNCTION IF EXISTS save_template_version CASCADE;")
    
    # Restore the original function
    op.execute("""
    CREATE OR REPLACE FUNCTION save_template_version()
    RETURNS TRIGGER AS $$
    BEGIN
        INSERT INTO template_versions (id, template_id, tenant_id, version, content, body)
        VALUES (
            gen_random_uuid(),
            OLD.id,
            OLD.tenant_id,
            (SELECT COALESCE(MAX(version), 0) + 1 FROM template_versions WHERE template_id = OLD.id),
            to_jsonb(OLD),
            coalesce(OLD.content, '')
        );
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """)
    
    # Restore the original trigger
    op.execute("""
    CREATE TRIGGER version_template
    BEFORE UPDATE ON templates
    FOR EACH ROW EXECUTE FUNCTION save_template_version();
    """)