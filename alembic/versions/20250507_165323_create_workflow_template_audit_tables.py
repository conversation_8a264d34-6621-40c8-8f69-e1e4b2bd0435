"""create_workflow_template_audit_tables

Revision ID: 7f921b25f700
Revises: 20250505_175000
Create Date: 2025-05-07 16:53:23.447404+00:00

"""
from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, UUID

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250507_165323'
down_revision: Union[str, None] = '20250505_175000'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'workflow_status',
        sa.Column('id', UUID(), nullable=False),
        sa.Column('tenant_id', UUID(), nullable=False),
        sa.Column('status', sa.Text(), nullable=False),
        sa.Column('progress', sa.Float(), nullable=True),
        sa.Column('current_step', sa.Text(), nullable=True),
        sa.Column('result', JSONB(), nullable=True),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.execute("ALTER TABLE workflow_status ENABLE ROW LEVEL SECURITY;")
    op.execute("CREATE POLICY tenant_isolation_policy ON workflow_status USING (tenant_id = current_setting('app.tenant_id')::uuid);")

    op.create_table(
        'template_versions',
        sa.Column('id', UUID(), nullable=False),
        sa.Column('template_id', UUID(), nullable=False),
        sa.Column('tenant_id', UUID(), nullable=False),
        sa.Column('version', sa.Integer(), nullable=False),
        sa.Column('content', JSONB(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['template_id'], ['templates.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.execute("ALTER TABLE template_versions ENABLE ROW LEVEL SECURITY;")
    op.execute("CREATE POLICY tenant_isolation_policy ON template_versions USING (tenant_id = current_setting('app.tenant_id')::uuid);")

    op.create_table(
        'audit_log',
        sa.Column('id', UUID(), nullable=False),
        sa.Column('tenant_id', UUID(), nullable=False),
        sa.Column('user_id', UUID(), nullable=True),
        sa.Column('action', sa.Text(), nullable=False),
        sa.Column('timestamp', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('details', JSONB(), nullable=True),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.execute("ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;")
    op.execute("CREATE POLICY tenant_isolation_policy ON audit_log USING (tenant_id = current_setting('app.tenant_id')::uuid);")

    # Add the save_template_version function and trigger
    op.execute("""
        CREATE OR REPLACE FUNCTION save_template_version()
        RETURNS TRIGGER AS $$
        BEGIN
            INSERT INTO template_versions (id, template_id, tenant_id, version, content)
            VALUES (gen_random_uuid(), OLD.id, OLD.tenant_id, (SELECT COALESCE(MAX(version), 0) + 1 FROM template_versions WHERE template_id = OLD.id), to_jsonb(OLD));
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """)
    op.execute("""
        CREATE TRIGGER version_template
        BEFORE UPDATE ON templates
        FOR EACH ROW EXECUTE FUNCTION save_template_version();
    """)


def downgrade() -> None:
    op.execute("DROP TRIGGER IF EXISTS version_template ON templates;")
    op.execute("DROP FUNCTION IF EXISTS save_template_version();")
    op.drop_table('audit_log')
    op.drop_table('template_versions')
    op.drop_table('workflow_status')