"""add_rls_to_template_children_tables

Revision ID: 20250510_203256
Revises: 20250510_203201
Create Date: 2025-05-10 20:32:56.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250510_203256'
down_revision: Union[str, None] = '20250510_203201'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# Assumes helper functions like is_system_admin(), current_session_clerk_org_id(),
# and current_tenant_id() are defined and available from previous migrations.

# This is the core logic for checking access to a parent template.
# It mirrors the USING clause of the 'templates_isolation_policy' from the parent migration.
PARENT_TEMPLATE_ACCESS_CONDITION = """
    is_system_admin() OR
    parent_template.scope = 'global' OR
    parent_template.scope = 'pack' OR
    (
        parent_template.scope = 'tenant' AND (
            EXISTS (
                SELECT 1 FROM tenants t
                WHERE t.id = parent_template.tenant_id
                  AND t.clerk_org_id IS NOT NULL
                  AND t.clerk_org_id = current_session_clerk_org_id()
            ) OR
            (parent_template.tenant_id = current_tenant_id())
        )
    )
"""

TABLES_AND_POLICIES = {
    "template_versions": {
        "fk_column": "template_id",
        "policy_name": "rls_template_versions_access"
    },
    "template_tests": {
        "fk_column": "template_id",
        "policy_name": "rls_template_tests_access"
    },
    "template_dependencies": { # This one is trickier as it has two FKs to templates
        "fk_column_template": "template_id", # The template that has a dependency
        "fk_column_depends_on": "depends_on_template_id", # The template it depends on
        "policy_name": "rls_template_dependencies_access"
    }
}

def upgrade() -> None:
    for table_name, details in TABLES_AND_POLICIES.items():
        op.execute(f"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY;")
        op.execute(f"ALTER TABLE {table_name} FORCE ROW LEVEL SECURITY;")
        op.execute(f"DROP POLICY IF EXISTS {details['policy_name']} ON {table_name};")

        if table_name == "template_dependencies":
            # For template_dependencies, access is granted if the user can see *either*
            # the template_id OR the depends_on_template_id.
            # This means they can see dependencies TO their templates, and dependencies FROM their templates.
            policy_sql = f"""
            CREATE POLICY {details['policy_name']} ON {table_name}
            FOR ALL
            USING (
                is_system_admin() OR
                EXISTS (
                    SELECT 1 FROM templates parent_template
                    WHERE parent_template.id = {table_name}.{details['fk_column_template']}
                      AND ({PARENT_TEMPLATE_ACCESS_CONDITION})
                ) OR
                EXISTS (
                    SELECT 1 FROM templates parent_template
                    WHERE parent_template.id = {table_name}.{details['fk_column_depends_on']}
                      AND ({PARENT_TEMPLATE_ACCESS_CONDITION})
                )
            );
            """
        else:
            fk_column = details['fk_column']
            policy_sql = f"""
            CREATE POLICY {details['policy_name']} ON {table_name}
            FOR ALL
            USING (
                is_system_admin() OR
                EXISTS (
                    SELECT 1 FROM templates parent_template
                    WHERE parent_template.id = {table_name}.{fk_column}
                      AND ({PARENT_TEMPLATE_ACCESS_CONDITION})
                )
            );
            """
        op.execute(policy_sql)


def downgrade() -> None:
    for table_name, details in TABLES_AND_POLICIES.items():
        op.execute(f"DROP POLICY IF EXISTS {details['policy_name']} ON {table_name};")
        # op.execute(f"ALTER TABLE {table_name} DISABLE ROW LEVEL SECURITY;") # Or NO FORCE
        # Keeping RLS enabled but without this specific policy is generally safer.
        op.execute(f"-- Policy {details['policy_name']} on {table_name} dropped. RLS on table remains enabled.")