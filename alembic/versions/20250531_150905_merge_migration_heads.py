"""Merge migration heads

Revision ID: 698a7fb17d8a
Revises: 20250514_100000, 20250518_000002
Create Date: 2025-05-31 15:09:05.022090+00:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '698a7fb17d8a'
down_revision: Union[str, None] = ('20250514_100000', '20250518_000002')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass