"""Add generated actions table

Revision ID: 20250507_135841
Revises: 20250507_165323
Create Date: 2025-05-07 13:58:41.123456

"""
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = '20250507_135841'
down_revision = '20250507_165323'
branch_labels = None
depends_on = None


def upgrade():
    # Create the generated_actions table
    op.create_table('generated_actions',
        sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('endpoint_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('class_name', sa.String(), nullable=False),
        sa.Column('version', sa.String(), nullable=False),
        sa.Column('generated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('validation_status', postgresql.ENUM('pending', 'valid', 'invalid', 'outdated', name='validation_status', create_type=True), default='pending', nullable=False),
        sa.Column('validation_message', sa.Text(), nullable=True),
        sa.Column('last_validated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('code', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), onupdate=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['endpoint_id'], ['api_endpoints.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index('ix_generated_actions_endpoint_id_version', 'generated_actions', ['endpoint_id', 'version'], unique=True)
    op.create_index('ix_generated_actions_tenant_id', 'generated_actions', ['tenant_id'], unique=False)
    
    # Enable Row Level Security
    op.execute("""
    ALTER TABLE generated_actions ENABLE ROW LEVEL SECURITY;
    
    CREATE POLICY generated_actions_tenant_isolation ON generated_actions
        USING (tenant_id = current_setting('app.tenant_id')::uuid);
        
    CREATE POLICY generated_actions_system_admin ON generated_actions
        USING (current_setting('app.is_system_admin', 'false')::boolean = true);
    """)


def downgrade():
    # Drop the table
    op.drop_table('generated_actions')
    
    # Drop the enum type if it exists
    op.execute("DROP TYPE IF EXISTS validation_status")