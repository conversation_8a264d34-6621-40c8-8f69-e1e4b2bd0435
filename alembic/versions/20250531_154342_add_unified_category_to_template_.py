"""Add unified category to template_category enum

Revision ID: 4266d8649de7
Revises: 5f3f4e44bfff
Create Date: 2025-05-31 15:43:42.509493+00:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4266d8649de7'
down_revision: Union[str, None] = '5f3f4e44bfff'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Import the helper function
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from utils.enum_helpers import add_enum_value_safe
    
    # Add 'unified' to the template_category enum
    add_enum_value_safe('template_category', 'unified')


def downgrade() -> None:
    # Cannot easily remove enum values in PostgreSQL
    pass