"""Add unified template schema support

Revision ID: 5f3f4e44bfff
Revises: 698a7fb17d8a
Create Date: 2025-05-31 15:09:40.338600+00:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5f3f4e44bfff'
down_revision: Union[str, None] = '698a7fb17d8a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_preferences')
    op.drop_table('oauth_states')
    op.drop_column('api_auth_configs', 'encryption_nonce')
    op.drop_column('api_auth_configs', 'encryption_key_id')
    op.alter_column('api_endpoints', 'intent_generated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('api_endpoints', 'enabled',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('api_integrations', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_integrations', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_keys', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_keys', 'revoked',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('template_tests', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.add_column('template_versions', sa.Column('endpoint_id', sa.String(), nullable=True))
    op.add_column('template_versions', sa.Column('intent_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('template_versions', sa.Column('action_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('template_versions', sa.Column('ui_fields', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('template_versions', sa.Column('prompts', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('template_versions', sa.Column('docs_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('template_versions', 'edited_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.drop_constraint('template_version_unique_constraint', 'template_versions', type_='unique')
    op.drop_constraint('template_versions_tenant_id_fkey', 'template_versions', type_='foreignkey')
    op.drop_column('template_versions', 'id')
    op.drop_column('template_versions', 'tenant_id')
    op.drop_column('template_versions', 'content')
    op.add_column('templates', sa.Column('endpoint_id', sa.String(), nullable=True, comment="Endpoint identifier for unified templates (e.g., 'GET_/api/weather')"))
    op.add_column('templates', sa.Column('intent_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Intent patterns and configuration for unified templates'))
    op.add_column('templates', sa.Column('action_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='API action configuration (method, path, auth, retries)'))
    op.add_column('templates', sa.Column('ui_fields', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='UI field definitions for parameter forms'))
    op.add_column('templates', sa.Column('prompts', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='System, extraction, and completion prompts'))
    op.add_column('templates', sa.Column('docs_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Documentation configuration and examples'))
    op.alter_column('templates', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('templates', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenant_settings', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenant_settings', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenants', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenants', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('tenants', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenants', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenant_settings', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenant_settings', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('templates', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('templates', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_column('templates', 'docs_config')
    op.drop_column('templates', 'prompts')
    op.drop_column('templates', 'ui_fields')
    op.drop_column('templates', 'action_config')
    op.drop_column('templates', 'intent_config')
    op.drop_column('templates', 'endpoint_id')
    op.add_column('template_versions', sa.Column('content', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('template_versions', sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('template_versions', sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), autoincrement=False, nullable=False))
    op.create_foreign_key('template_versions_tenant_id_fkey', 'template_versions', 'tenants', ['tenant_id'], ['id'])
    op.create_unique_constraint('template_version_unique_constraint', 'template_versions', ['template_id', 'version'])
    op.alter_column('template_versions', 'edited_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_column('template_versions', 'docs_config')
    op.drop_column('template_versions', 'prompts')
    op.drop_column('template_versions', 'ui_fields')
    op.drop_column('template_versions', 'action_config')
    op.drop_column('template_versions', 'intent_config')
    op.drop_column('template_versions', 'endpoint_id')
    op.alter_column('template_tests', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_keys', 'revoked',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('api_keys', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_integrations', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_integrations', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_endpoints', 'enabled',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('api_endpoints', 'intent_generated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.add_column('api_auth_configs', sa.Column('encryption_key_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('api_auth_configs', sa.Column('encryption_nonce', postgresql.BYTEA(), autoincrement=False, nullable=True))
    op.create_table('oauth_states',
    sa.Column('state', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('state', name='oauth_states_pkey')
    )
    op.create_table('user_preferences',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('preferences', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], name='user_preferences_tenant_id_fkey', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name='user_preferences_pkey'),
    sa.UniqueConstraint('user_id', name='user_preferences_user_id_key')
    )
    # ### end Alembic commands ###