"""Protect core templates

Revision ID: 20250513_110000
Revises: a58f91c0a456
Create Date: 2025-05-13 11:00:00.000000

"""
from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250513_110000'
down_revision: Union[str, None] = 'a58f91c0a456'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # First add the protected column if it doesn't exist
    op.add_column('templates', 
                 sa.Column('protected', sa.<PERSON>an(), 
                           nullable=False, 
                           server_default=sa.text('false'),
                           comment='When true, template cannot be edited or deleted by regular admins'))
    
    # Mark core templates as protected
    op.execute("""
    UPDATE templates 
    SET protected = TRUE 
    WHERE key IN (
        'ERROR_HANDLER_V1', 
        'INTENT_ROUTER_V1', 
        'PARAM_COMPLETE_V1', 
        'RESPONSE_GEN_V1', 
        'RETRIEVAL_V1',
        'WEATHER_QUERY'
    )
    """)


def downgrade() -> None:
    # Unprotect these templates
    op.execute("""
    UPDATE templates 
    SET protected = FALSE 
    WHERE key IN (
        'ERROR_HANDLER_V1', 
        'INTENT_ROUTER_V1', 
        'PARAM_COMPLETE_V1', 
        'RESPONSE_GEN_V1', 
        'RETRIEVAL_V1',
        'WEATHER_QUERY'
    )
    """)
    
    # Drop the protected column
    op.drop_column('templates', 'protected')