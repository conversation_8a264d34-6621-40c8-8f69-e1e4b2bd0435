"""Add OAuth states table

Revision ID: 20250505_175000
Revises: 2fe15b4cbf91
Create Date: 2025-05-05 17:50:00.000000

"""
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = '20250505_175000'
down_revision = '2fe15b4cbf91'
branch_labels = None
depends_on = None


def upgrade():
    # Create oauth_states table for storing OAuth flow state
    op.create_table(
        'oauth_states',
        sa.Column('state', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('state')
    )

    # Add encryption_key_id and encryption_nonce columns to api_auth_configs
    op.add_column('api_auth_configs', sa.Column('encryption_key_id', sa.String(), nullable=True))
    op.add_column('api_auth_configs', sa.Column('encryption_nonce', postgresql.BYTEA(), nullable=True))


def downgrade():
    # Remove columns from api_auth_configs
    op.drop_column('api_auth_configs', 'encryption_nonce')
    op.drop_column('api_auth_configs', 'encryption_key_id')

    # Drop oauth_states table
    op.drop_table('oauth_states')