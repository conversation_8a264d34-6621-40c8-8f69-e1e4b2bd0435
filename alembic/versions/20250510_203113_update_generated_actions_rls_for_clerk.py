"""update_generated_actions_rls_for_clerk

Revision ID: 20250510_203113
Revises: 20250510_203008
Create Date: 2025-05-10 20:31:13.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250510_203113'
down_revision: Union[str, None] = '20250510_203008'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

TABLE_NAME = "generated_actions"
POLICY_NAME = "tenant_isolation_generated_actions" # Keeping the same policy name

# Assumes helper functions like is_system_admin(), current_session_clerk_org_id(),
# and current_tenant_id() are defined and available from previous migrations.

def get_previous_generated_actions_policy() -> str:
    # This is the definition from migration 20250509_225636
    return f"""
    CREATE POLICY {POLICY_NAME} ON {TABLE_NAME}
    FOR ALL
    USING (
        tenant_id = current_tenant_id() OR
        is_system_admin() OR
        current_tenant_id() IS NULL 
    );
    """

def upgrade() -> None:
    # Drop the existing policy
    op.execute(f"DROP POLICY IF EXISTS {POLICY_NAME} ON {TABLE_NAME};")

    # Create the updated RLS policy for generated_actions
    # This policy restricts access based on:
    # 1. System admin status.
    # 2. The generated_action's tenant_id matching a tenant whose clerk_org_id
    #    matches the session's current_clerk_org_id.
    # 3. (Transitional) The generated_action's tenant_id matching the session's current_tenant_id (old API key system).
    # The overly permissive 'current_tenant_id() IS NULL' is removed.
    op.execute(f"""
    CREATE POLICY {POLICY_NAME} ON {TABLE_NAME}
    FOR ALL
    USING (
        is_system_admin() OR
        EXISTS (
            SELECT 1 FROM tenants t
            WHERE t.id = {TABLE_NAME}.tenant_id 
              AND t.clerk_org_id IS NOT NULL
              AND t.clerk_org_id = current_session_clerk_org_id()
        ) OR
        ({TABLE_NAME}.tenant_id = current_tenant_id()) 
    );
    """)
    # Ensure RLS is enabled (it should be from previous migration)
    op.execute(f"ALTER TABLE {TABLE_NAME} ENABLE ROW LEVEL SECURITY;")
    op.execute(f"ALTER TABLE {TABLE_NAME} FORCE ROW LEVEL SECURITY;")


def downgrade() -> None:
    # Drop the updated policy
    op.execute(f"DROP POLICY IF EXISTS {POLICY_NAME} ON {TABLE_NAME};")
    # Re-create the previous version of the policy
    op.execute(get_previous_generated_actions_policy())
    # RLS remains enabled as it was before this specific policy update.
    op.execute(f"ALTER TABLE {TABLE_NAME} ENABLE ROW LEVEL SECURITY;")
    op.execute(f"ALTER TABLE {TABLE_NAME} FORCE ROW LEVEL SECURITY;")