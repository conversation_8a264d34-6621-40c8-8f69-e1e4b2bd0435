"""add_endpoint_descriptions

Revision ID: 20250512_200000
Revises: Version_20250512_1926_add_action_template_category
Create Date: 2025-05-12 20:00:00.000000+00:00

"""
from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250512_200000'
down_revision: Union[str, None] = 'Version_20250512_1926'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add OpenAPI endpoint description fields."""
    # Add new columns to api_endpoints table
    op.add_column('api_endpoints', sa.Column('summary', sa.String(), nullable=True))
    op.add_column('api_endpoints', sa.Column('description', sa.String(), nullable=True))
    op.add_column('api_endpoints', sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('api_endpoints', sa.Column('deprecated', sa.<PERSON>(), server_default='false', nullable=False))
    op.add_column('api_endpoints', sa.Column('openapi_snippet', postgresql.JSONB(astext_type=sa.Text()), nullable=True))


def downgrade() -> None:
    """Remove OpenAPI endpoint description fields."""
    # Remove added columns
    op.drop_column('api_endpoints', 'openapi_snippet')
    op.drop_column('api_endpoints', 'deprecated')
    op.drop_column('api_endpoints', 'tags')
    op.drop_column('api_endpoints', 'description')
    op.drop_column('api_endpoints', 'summary')
