"""add_rls_to_audit_log

Revision ID: 20250510_203433
Revises: 20250510_203348
Create Date: 2025-05-10 20:34:33.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250510_203433'
down_revision: Union[str, None] = '20250510_203348'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

TABLE_NAME = "audit_log"
POLICY_NAME = "rls_audit_log_access"

# Assumes helper functions like is_system_admin(), current_session_clerk_org_id(),
# current_tenant_id(), and current_setting('app.rls.lookup_clerk_org_id', true)
# are defined and available from previous migrations.

# This is the core logic for checking access to a parent tenant.
# It mirrors the USING clause of the 'select_for_tenants' policy.
PARENT_TENANT_ACCESS_CONDITION = """
    is_system_admin() OR
    (parent_tenant.clerk_org_id IS NOT NULL AND parent_tenant.clerk_org_id = current_session_clerk_org_id()) OR
    (parent_tenant.id = current_tenant_id()) OR
    ( 
        parent_tenant.clerk_org_id IS NOT NULL AND
        parent_tenant.clerk_org_id = current_setting('app.rls.lookup_clerk_org_id', true) AND
        current_tenant_id() IS NULL AND 
        is_system_admin() = false
    )
"""

def upgrade() -> None:
    op.execute(f"ALTER TABLE {TABLE_NAME} ENABLE ROW LEVEL SECURITY;")
    op.execute(f"ALTER TABLE {TABLE_NAME} FORCE ROW LEVEL SECURITY;")

    op.execute(f"DROP POLICY IF EXISTS {POLICY_NAME} ON {TABLE_NAME};")

    # RLS policy for audit_log
    # Access is allowed if the user can access the parent tenant record associated with the audit log entry.
    # System admins can access all audit logs.
    # For INSERT, it's assumed the application logic correctly sets the tenant_id.
    # The WITH CHECK clause for INSERT would be similar to USING if direct inserts by users were allowed
    # and needed to be restricted to their own tenant. However, audit logs are often inserted by the system.
    # A simple USING clause for SELECT/UPDATE/DELETE is common for audit logs visibility.
    # Using FOR ALL for consistency, assuming application controls inserts.
    policy_sql = f"""
    CREATE POLICY {POLICY_NAME} ON {TABLE_NAME}
    FOR ALL 
    USING (
        is_system_admin() OR 
        EXISTS (
            SELECT 1 FROM tenants parent_tenant
            WHERE parent_tenant.id = {TABLE_NAME}.tenant_id -- audit_log.tenant_id links to tenants.id
              AND ({PARENT_TENANT_ACCESS_CONDITION})
        )
    );
    """
    op.execute(policy_sql)


def downgrade() -> None:
    op.execute(f"DROP POLICY IF EXISTS {POLICY_NAME} ON {TABLE_NAME};")
    # op.execute(f"ALTER TABLE {TABLE_NAME} DISABLE ROW LEVEL SECURITY;") # Or NO FORCE
    op.execute(f"-- Policy {POLICY_NAME} on {TABLE_NAME} dropped. RLS on table remains enabled.")