"""Add intent fields to api_endpoints table

Revision ID: 20250516_070900
Revises: 20250512_200000
Create Date: 2025-05-16 07:09:00.000000+00:00

"""
from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250516_070900'
down_revision: Union[str, None] = '20250512_200000'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add intent fields to api_endpoints table."""
    # Check if intent_name column exists before adding it
    conn = op.get_bind()
    insp = sa.inspect(conn)
    columns = insp.get_columns('api_endpoints')
    existing_columns = [c['name'] for c in columns]
    
    # Only add columns if they don't already exist
    if 'intent_name' not in existing_columns:
        op.add_column('api_endpoints', sa.Column('intent_name', sa.String(), nullable=True))
    
    if 'intent_description' not in existing_columns:
        op.add_column('api_endpoints', sa.Column('intent_description', sa.String(), nullable=True))
    
    if 'intent_examples' not in existing_columns:
        op.add_column('api_endpoints', sa.Column('intent_examples', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    
    if 'intent_parameters' not in existing_columns:
        op.add_column('api_endpoints', sa.Column('intent_parameters', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    
    if 'intent_required_fields' not in existing_columns:
        op.add_column('api_endpoints', sa.Column('intent_required_fields', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    
    if 'intent_generated_at' not in existing_columns:
        op.add_column('api_endpoints', sa.Column('intent_generated_at', sa.DateTime(timezone=True), nullable=True))
    
    if 'intent_generation_status' not in existing_columns:
        op.add_column('api_endpoints', sa.Column('intent_generation_status', sa.String(), nullable=True))


def downgrade() -> None:
    """Remove intent fields from api_endpoints table."""
    op.drop_column('api_endpoints', 'intent_generation_status')
    op.drop_column('api_endpoints', 'intent_generated_at')
    op.drop_column('api_endpoints', 'intent_required_fields')
    op.drop_column('api_endpoints', 'intent_parameters')
    op.drop_column('api_endpoints', 'intent_examples')
    op.drop_column('api_endpoints', 'intent_description')
    op.drop_column('api_endpoints', 'intent_name')

