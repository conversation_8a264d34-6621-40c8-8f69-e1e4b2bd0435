"""add_rls_to_organization_api_keys

Revision ID: 20250510_202911
Revises: 20250510_201654
Create Date: 2025-05-10 20:29:11.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250510_202911'
down_revision: Union[str, None] = '20250510_201654'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

TABLE_NAME = "organization_api_keys"

# Helper function to create SQL for RLS policies, assuming session variables are set.
# It's assumed that 'app.current_clerk_org_id' and 'app.is_system_admin' are set by the application.

def create_rls_helpers_if_not_exist():
    # These functions might already exist from other RLS migrations (e.g., for tenants table).
    # Creating them as `OR REPLACE` or checking existence can be an option.
    # For simplicity, we assume they might need to be created or are compatible if they exist.
    # It's crucial these session variables are set by the application layer (e.g., middleware).

    op.execute("""
    CREATE OR REPLACE FUNCTION is_system_admin() RETURNS boolean AS $$
    BEGIN
        RETURN COALESCE(current_setting('app.is_system_admin', true), 'false')::boolean;
    EXCEPTION WHEN OTHERS THEN
        RETURN false;
    END;
    $$ LANGUAGE plpgsql;
    """)

    op.execute("""
    CREATE OR REPLACE FUNCTION current_session_clerk_org_id() RETURNS TEXT AS $$
    BEGIN
        RETURN current_setting('app.current_clerk_org_id', true);
    EXCEPTION WHEN OTHERS THEN
        RETURN NULL;
    END;
    $$ LANGUAGE plpgsql;
    """)

def drop_rls_helpers():
    # Dropping these might affect other tables if they are shared.
    # Typically, helper functions are not dropped unless they are specific to this table's RLS
    # and no longer needed. For now, we'll leave them in downgrade.
    op.execute("DROP FUNCTION IF EXISTS is_system_admin();")
    op.execute("DROP FUNCTION IF EXISTS current_session_clerk_org_id();")


def upgrade() -> None:
    create_rls_helpers_if_not_exist()

    op.execute(f"ALTER TABLE {TABLE_NAME} ENABLE ROW LEVEL SECURITY;")
    # Apply to table owners as well, ensuring RLS is not bypassed by them.
    op.execute(f"ALTER TABLE {TABLE_NAME} FORCE ROW LEVEL SECURITY;")

    # Policy for SELECT
    # Users can select keys for their own organization or if they are a system admin.
    op.execute(f"""
        CREATE POLICY select_organization_api_keys ON {TABLE_NAME}
        FOR SELECT
        USING (
            (clerk_org_id = current_session_clerk_org_id()) OR
            (is_system_admin())
        );
    """)

    # Policy for INSERT
    # Users can insert keys for their own organization (application logic should verify org admin role)
    # or if they are a system admin.
    op.execute(f"""
        CREATE POLICY insert_organization_api_keys ON {TABLE_NAME}
        FOR INSERT
        WITH CHECK (
            (clerk_org_id = current_session_clerk_org_id()) OR
            (is_system_admin())
        );
    """)

    # Policy for UPDATE
    # Users can update keys for their own organization (application logic for org admin role)
    # or if they are a system admin.
    op.execute(f"""
        CREATE POLICY update_organization_api_keys ON {TABLE_NAME}
        FOR UPDATE
        USING (
            (clerk_org_id = current_session_clerk_org_id()) OR
            (is_system_admin())
        )
        WITH CHECK ( -- Ensures updated row still meets the criteria if clerk_org_id could be changed
            (clerk_org_id = current_session_clerk_org_id()) OR
            (is_system_admin())
        );
    """)

    # Policy for DELETE
    # Users can delete keys for their own organization (application logic for org admin role)
    # or if they are a system admin.
    op.execute(f"""
        CREATE POLICY delete_organization_api_keys ON {TABLE_NAME}
        FOR DELETE
        USING (
            (clerk_org_id = current_session_clerk_org_id()) OR
            (is_system_admin())
        );
    """)


def downgrade() -> None:
    op.execute(f"DROP POLICY IF EXISTS delete_organization_api_keys ON {TABLE_NAME};")
    op.execute(f"DROP POLICY IF EXISTS update_organization_api_keys ON {TABLE_NAME};")
    op.execute(f"DROP POLICY IF EXISTS insert_organization_api_keys ON {TABLE_NAME};")
    op.execute(f"DROP POLICY IF EXISTS select_organization_api_keys ON {TABLE_NAME};")
    
    # op.execute(f"ALTER TABLE {TABLE_NAME} NO FORCE ROW LEVEL SECURITY;") # Revert FORCE RLS
    op.execute(f"ALTER TABLE {TABLE_NAME} DISABLE ROW LEVEL SECURITY;")

    # Consider whether to drop helper functions. If shared, do not drop.
    # drop_rls_helpers()
    # For safety, let's assume they might be shared and not drop them by default in downgrade.
    # If they were created SOLELY for this table, then dropping is fine.
    op.execute("-- Downgrade did not drop helper functions is_system_admin() or current_session_clerk_org_id() as they might be shared.")