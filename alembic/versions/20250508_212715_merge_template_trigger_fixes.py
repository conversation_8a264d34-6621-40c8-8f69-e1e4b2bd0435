"""merge_template_trigger_fixes

Revision ID: b63fdcc5062c
Revises: 20250508_143000, 20250508_144000
Create Date: 2025-05-08 21:27:15.280948+00:00

"""
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = 'b63fdcc5062c'
down_revision: Union[str, None] = ('20250508_143000', '20250508_144000')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass