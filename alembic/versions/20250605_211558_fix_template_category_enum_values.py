"""fix_template_category_enum_values

Revision ID: c8cea4374b44
Revises: 60ece0d26f6b
Create Date: 2025-06-05 21:15:58.476116+00:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c8cea4374b44'
down_revision: Union[str, None] = '60ece0d26f6b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Import the helper function
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from utils.enum_helpers import add_enum_value_safe
    
    # Ensure all template_category enum values are present
    # This handles cases where previous migrations failed silently
    add_enum_value_safe('template_category', 'intent_router')
    add_enum_value_safe('template_category', 'param_complete')
    add_enum_value_safe('template_category', 'retrieval')
    add_enum_value_safe('template_category', 'response_gen')
    add_enum_value_safe('template_category', 'error_handler')
    add_enum_value_safe('template_category', 'action')
    add_enum_value_safe('template_category', 'documentation')
    add_enum_value_safe('template_category', 'unified')


def downgrade() -> None:
    # Cannot easily remove enum values in PostgreSQL
    pass