"""add_test_data_field_to_templates

Revision ID: 29222d2086ef
Revises: 44376f41c210
Create Date: 2025-06-04 21:11:15.894936+00:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '29222d2086ef'
down_revision: Union[str, None] = '44376f41c210'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add test_data field to templates table
    op.add_column('templates', 
        sa.Column(
            'test_data', 
            postgresql.JSONB(astext_type=sa.Text()), 
            nullable=True,
            comment='Test data and mock responses for development'
        )
    )
    
    # Add test_data field to template_versions table
    op.add_column('template_versions', 
        sa.Column(
            'test_data', 
            postgresql.JSONB(astext_type=sa.Text()), 
            nullable=True,
            comment='Test data and mock responses for development'
        )
    )


def downgrade() -> None:
    # Remove test_data field from template_versions table
    op.drop_column('template_versions', 'test_data')
    
    # Remove test_data field from templates table
    op.drop_column('templates', 'test_data')