"""fix_template_trigger

Revision ID: 20250508_143000
Revises: 20250507_135841
Create Date: 2025-05-08 14:30:00.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250508_143000'
down_revision: Union[str, None] = '20250507_135841'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Fix the template trigger to properly handle the 'body' field instead of 'content'."""
    
    # Drop the existing trigger
    op.execute("DROP TRIGGER IF EXISTS version_template ON templates;")
    
    # Drop the existing function
    op.execute("DROP FUNCTION IF EXISTS save_template_version();")
    
    # Create a new function that correctly references the 'body' field instead of 'content'
    # This function creates a JSON object that contains all the important fields
    op.execute("""
        CREATE OR REPLACE FUNCTION save_template_version()
        RETURNS TRIGGER AS $$
        BEGIN
            -- Create a new version record using a JSON structure with the correct field names
            INSERT INTO template_versions (
                template_id, 
                version, 
                body,
                actions,
                parameters,
                editor_id,
                change_reason
            )
            VALUES (
                NEW.id, 
                NEW.version,
                NEW.body,
                NEW.actions,
                NEW.parameters,
                NULL, -- editor_id not available in trigger context
                'Update via database trigger'
            );
            
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # Create a new trigger that fires AFTER UPDATE, not BEFORE
    # This ensures that the NEW record is already updated with the correct values
    op.execute("""
        CREATE TRIGGER version_template
        AFTER UPDATE ON templates
        FOR EACH ROW EXECUTE FUNCTION save_template_version();
    """)


def downgrade() -> None:
    """Restore the original trigger functionality."""
    
    # Drop the fixed trigger
    op.execute("DROP TRIGGER IF EXISTS version_template ON templates;")
    
    # Drop the fixed function
    op.execute("DROP FUNCTION IF EXISTS save_template_version();")
    
    # Recreate the original function (attempts to use to_jsonb on the entire record)
    op.execute("""
        CREATE OR REPLACE FUNCTION save_template_version()
        RETURNS TRIGGER AS $$
        BEGIN
            INSERT INTO template_versions (id, template_id, tenant_id, version, content)
            VALUES (gen_random_uuid(), OLD.id, OLD.tenant_id, (SELECT COALESCE(MAX(version), 0) + 1 FROM template_versions WHERE template_id = OLD.id), to_jsonb(OLD));
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # Recreate the original trigger
    op.execute("""
        CREATE TRIGGER version_template
        BEFORE UPDATE ON templates
        FOR EACH ROW EXECUTE FUNCTION save_template_version();
    """)