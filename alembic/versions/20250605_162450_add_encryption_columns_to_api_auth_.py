"""add_encryption_columns_to_api_auth_configs

Revision ID: 60ece0d26f6b
Revises: 29222d2086ef
Create Date: 2025-06-05 16:24:50.483702+00:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '60ece0d26f6b'
down_revision: Union[str, None] = '29222d2086ef'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add encryption_key_id column
    op.add_column('api_auth_configs', 
        sa.Column('encryption_key_id', sa.String(), nullable=True)
    )
    
    # Add encryption_nonce column
    op.add_column('api_auth_configs',
        sa.Column('encryption_nonce', sa.LargeBinary(), nullable=True)
    )


def downgrade() -> None:
    # Remove encryption_nonce column
    op.drop_column('api_auth_configs', 'encryption_nonce')
    
    # Remove encryption_key_id column
    op.drop_column('api_auth_configs', 'encryption_key_id')