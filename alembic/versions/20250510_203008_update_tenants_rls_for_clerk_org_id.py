"""update_tenants_rls_for_clerk_org_id

Revision ID: 20250510_203008
Revises: 20250510_202911
Create Date: 2025-05-10 20:30:08.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250510_203008'
down_revision: Union[str, None] = '20250510_202911'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

POLICY_NAME = "select_for_tenants"
TABLE_NAME = "tenants"

# It's assumed that helper functions like is_system_admin() and 
# current_session_clerk_org_id() are defined from previous migrations
# (e.g., 20250510_202911_add_rls_to_organization_api_keys.py).
# Also, current_tenant_id() is assumed to be an existing function for backward compatibility.
# If current_tenant_id() is not defined or should be deprecated, its usage should be removed.

# Function to get the old policy definition for downgrade, if needed.
# This is a simplified example; in reality, you might fetch this from DB or have it versioned.
def get_previous_policy_definition() -> str:
    # This definition is from migration 20250510_191045_add_rls_policy_for_tenant_clerk_org_.py
    # It includes the specific 'app.rls.lookup_clerk_org_id' for the public /by-clerk-org endpoint.
    # And current_tenant_id() for old API key based access.
    return f"""
        CREATE POLICY {POLICY_NAME} ON {TABLE_NAME}
        FOR SELECT
        USING (
            (id = current_tenant_id()) OR 
            (is_system_admin()) OR
            (
                clerk_org_id = current_setting('app.rls.lookup_clerk_org_id', true) AND
                current_tenant_id() IS NULL AND 
                is_system_admin() = false
            )
        );
    """

def upgrade() -> None:
    # Ensure RLS helper functions are available (idempotently created in previous migration)
    # op.execute("SELECT 1 FROM pg_proc WHERE proname = 'is_system_admin';") # Could check
    # op.execute("SELECT 1 FROM pg_proc WHERE proname = 'current_session_clerk_org_id';")

    # Drop the existing policy to redefine it
    op.execute(f"DROP POLICY IF EXISTS {POLICY_NAME} ON {TABLE_NAME};")

    # Create the updated RLS policy for tenants
    # This policy allows SELECT if:
    # 1. The user is a system_admin.
    # 2. The tenant's clerk_org_id matches the session's current_clerk_org_id (for Clerk JWT users).
    # 3. The old mechanism: tenant's id matches current_tenant_id() (for old API key transition).
    # 4. The special lookup for /by-clerk-org endpoint.
    # The order of OR conditions can matter for performance but is usually optimized by PG.
    # More common conditions first might be slightly better.
    op.execute(f"""
        CREATE POLICY {POLICY_NAME} ON {TABLE_NAME}
        FOR SELECT
        USING (
            is_system_admin() OR
            (clerk_org_id IS NOT NULL AND clerk_org_id = current_session_clerk_org_id()) OR
            (id = current_tenant_id()) OR 
            ( 
                clerk_org_id IS NOT NULL AND
                clerk_org_id = current_setting('app.rls.lookup_clerk_org_id', true) AND
                current_tenant_id() IS NULL AND 
                is_system_admin() = false
            )
        );
    """)
    # Ensure RLS is enabled (it should be from previous migration, but good to be sure)
    op.execute(f"ALTER TABLE {TABLE_NAME} ENABLE ROW LEVEL SECURITY;")
    op.execute(f"ALTER TABLE {TABLE_NAME} FORCE ROW LEVEL SECURITY;")


def downgrade() -> None:
    # Drop the updated policy
    op.execute(f"DROP POLICY IF EXISTS {POLICY_NAME} ON {TABLE_NAME};")
    # Re-create the previous version of the policy
    op.execute(get_previous_policy_definition())
    # RLS remains enabled as it was before this specific policy update.
    op.execute(f"ALTER TABLE {TABLE_NAME} ENABLE ROW LEVEL SECURITY;")
    op.execute(f"ALTER TABLE {TABLE_NAME} FORCE ROW LEVEL SECURITY;")