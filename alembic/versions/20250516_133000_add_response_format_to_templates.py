"""Add response_format to templates table

Revision ID: 20250516_133000
Revises: 20250516_070900_add_intent_fields_to_api_endpoints
Create Date: 2025-05-16 13:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250516_133000'
down_revision = '20250516_070900'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add response_format column to templates and template_versions tables."""
    op.add_column('templates', sa.Column('response_format', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('template_versions', sa.Column('response_format', postgresql.JSONB(astext_type=sa.Text()), nullable=True))


def downgrade() -> None:
    """Remove response_format column from templates and template_versions tables."""
    op.drop_column('templates', 'response_format')
    op.drop_column('template_versions', 'response_format')