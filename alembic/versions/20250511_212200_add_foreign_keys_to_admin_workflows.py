""""add_foreign_keys_to_admin_workflows"

Revision ID: 63344857d38d
Revises: 9455a0812686
Create Date: 2025-05-11 21:22:00.006159+00:00

"""
from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '63344857d38d'
down_revision: Union[str, None] = '9455a0812686'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('admin_workflows', sa.Column('created_by_id', sa.Integer(), nullable=True))
    op.add_column('admin_workflows', sa.Column('updated_by_id', sa.Integer(), nullable=True))
    op.add_column('admin_workflows', sa.<PERSON>umn('organization_id', sa.Integer(), nullable=True))

    op.create_index(op.f('ix_admin_workflows_organization_id'), 'admin_workflows', ['organization_id'], unique=False)
    
    op.create_foreign_key(
        op.f('fk_admin_workflows_created_by_id_users'),
        'admin_workflows',
        'users',
        ['created_by_id'],
        ['id']
    )
    op.create_foreign_key(
        op.f('fk_admin_workflows_updated_by_id_users'),
        'admin_workflows',
        'users',
        ['updated_by_id'],
        ['id']
    )
    op.create_foreign_key(
        op.f('fk_admin_workflows_organization_id_organizations'),
        'admin_workflows',
        'organizations',
        ['organization_id'],
        ['id']
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_admin_workflows_organization_id_organizations'), 'admin_workflows', type_='foreignkey')
    op.drop_constraint(op.f('fk_admin_workflows_updated_by_id_users'), 'admin_workflows', type_='foreignkey')
    op.drop_constraint(op.f('fk_admin_workflows_created_by_id_users'), 'admin_workflows', type_='foreignkey')
    op.drop_index(op.f('ix_admin_workflows_organization_id'), table_name='admin_workflows')
    
    op.drop_column('admin_workflows', 'organization_id')
    op.drop_column('admin_workflows', 'updated_by_id')
    op.drop_column('admin_workflows', 'created_by_id')
    # ### end Alembic commands ###