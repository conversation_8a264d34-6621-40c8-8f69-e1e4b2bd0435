"""sync_database_with_models

Revision ID: 44376f41c210
Revises: 4266d8649de7
Create Date: 2025-06-04 15:27:14.213407+00:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '44376f41c210'
down_revision: Union[str, None] = '4266d8649de7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('oauth_states')
    op.drop_table('user_preferences')
    op.drop_column('api_auth_configs', 'encryption_nonce')
    op.drop_column('api_auth_configs', 'encryption_key_id')
    op.add_column('api_endpoints', sa.Column('intent_name', sa.String(), nullable=True))
    op.add_column('api_endpoints', sa.Column('intent_description', sa.String(), nullable=True))
    op.add_column('api_endpoints', sa.Column('intent_examples', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('api_endpoints', sa.Column('intent_parameters', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('api_endpoints', sa.Column('intent_required_fields', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('api_endpoints', sa.Column('intent_generated_at', sa.DateTime(), nullable=True))
    op.add_column('api_endpoints', sa.Column('intent_generation_status', sa.String(), nullable=True))
    op.add_column('api_endpoints', sa.Column('summary', sa.String(), nullable=True))
    op.add_column('api_endpoints', sa.Column('description', sa.String(), nullable=True))
    op.add_column('api_endpoints', sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('api_endpoints', sa.Column('deprecated', sa.Boolean(), server_default='false', nullable=False))
    op.add_column('api_endpoints', sa.Column('openapi_snippet', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('api_endpoints', 'enabled',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    # Check if spec_format enum already exists
    op.execute("DO $$ BEGIN CREATE TYPE spec_format AS ENUM ('openapi', 'fapi', 'bapi'); EXCEPTION WHEN duplicate_object THEN null; END $$;")
    op.add_column('api_integrations', sa.Column('spec_format', postgresql.ENUM('openapi', 'fapi', 'bapi', name='spec_format', create_type=False), server_default='openapi', nullable=False))
    op.alter_column('api_integrations', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_integrations', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_keys', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_keys', 'revoked',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('template_tests', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.add_column('templates', sa.Column('protected', sa.Boolean(), server_default='false', nullable=False, comment='When true, template cannot be edited or deleted by regular admins'))
    op.add_column('templates', sa.Column('endpoint_id', sa.String(), nullable=True, comment="Endpoint identifier for unified templates (e.g., 'GET_/api/weather')"))
    op.add_column('templates', sa.Column('intent_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Intent patterns and configuration for unified templates'))
    op.add_column('templates', sa.Column('action_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='API action configuration (method, path, auth, retries)'))
    op.add_column('templates', sa.Column('ui_fields', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='UI field definitions for parameter forms'))
    op.add_column('templates', sa.Column('prompts', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='System, extraction, and completion prompts'))
    op.add_column('templates', sa.Column('docs_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Documentation configuration and examples'))
    op.add_column('templates', sa.Column('response_format', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('templates', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('templates', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenant_settings', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenant_settings', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.add_column('tenants', sa.Column('clerk_org_id', sa.String(), nullable=True))
    op.alter_column('tenants', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenants', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.create_unique_constraint('tenants_clerk_org_id_key', 'tenants', ['clerk_org_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('tenants_clerk_org_id_key', 'tenants', type_='unique')
    op.alter_column('tenants', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenants', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_column('tenants', 'clerk_org_id')
    op.alter_column('tenant_settings', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('tenant_settings', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('templates', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('templates', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_column('templates', 'response_format')
    op.drop_column('templates', 'docs_config')
    op.drop_column('templates', 'prompts')
    op.drop_column('templates', 'ui_fields')
    op.drop_column('templates', 'action_config')
    op.drop_column('templates', 'intent_config')
    op.drop_column('templates', 'endpoint_id')
    op.drop_column('templates', 'protected')
    op.alter_column('template_tests', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_keys', 'revoked',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('api_keys', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_integrations', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_integrations', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_column('api_integrations', 'spec_format')
    op.alter_column('api_endpoints', 'enabled',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.drop_column('api_endpoints', 'openapi_snippet')
    op.drop_column('api_endpoints', 'deprecated')
    op.drop_column('api_endpoints', 'tags')
    op.drop_column('api_endpoints', 'description')
    op.drop_column('api_endpoints', 'summary')
    op.drop_column('api_endpoints', 'intent_generation_status')
    op.drop_column('api_endpoints', 'intent_generated_at')
    op.drop_column('api_endpoints', 'intent_required_fields')
    op.drop_column('api_endpoints', 'intent_parameters')
    op.drop_column('api_endpoints', 'intent_examples')
    op.drop_column('api_endpoints', 'intent_description')
    op.drop_column('api_endpoints', 'intent_name')
    op.add_column('api_auth_configs', sa.Column('encryption_key_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('api_auth_configs', sa.Column('encryption_nonce', postgresql.BYTEA(), autoincrement=False, nullable=True))
    op.create_table('user_preferences',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('preferences', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], name='user_preferences_tenant_id_fkey', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name='user_preferences_pkey'),
    sa.UniqueConstraint('user_id', name='user_preferences_user_id_key')
    )
    op.create_table('oauth_states',
    sa.Column('state', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('state', name='oauth_states_pkey')
    )
    # ### end Alembic commands ###