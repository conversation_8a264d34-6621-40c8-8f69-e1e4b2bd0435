"""Create organization_api_keys table

Revision ID: 20250510_201505
Revises: 20250510_191045
Create Date: 2025-05-10 20:15:05.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250510_201505'
down_revision: Union[str, None] = '20250510_191045'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # op.create_table(
    #     'organization_api_keys',
    #     sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
    #     sa.Column('clerk_org_id', sa.String(), nullable=False, index=True),
    #     sa.Column('name', sa.String(), nullable=False),
    #     sa.Column('key_hash', sa.String(), nullable=False, unique=True),
    #     sa.Column('key_prefix', sa.String(8), nullable=False, index=True), # Assuming prefix is 8 chars
    #     sa.Column('permissions', JSONB, nullable=True), # Or sa.ARRAY(sa.String) if preferred
    #     sa.Column('created_by', sa.String(), nullable=False), # Clerk User ID
    #     sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
    #     sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True),
    #     sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    #     sa.Column('revoked', sa.Boolean(), default=False, nullable=False),
    #     # Consider a ForeignKeyConstraint if tenants.clerk_org_id is guaranteed to exist first
    #     # and is the definitive source of clerk_org_id.
    #     # sa.ForeignKeyConstraint(['clerk_org_id'], ['tenants.clerk_org_id'], name='fk_org_api_keys_clerk_org_id'),
    # )
    # op.create_index(op.f('ix_organization_api_keys_clerk_org_id_name'), 'organization_api_keys', ['clerk_org_id', 'name'], unique=False)
    pass # Table and index already exist


def downgrade() -> None:
    op.drop_index(op.f('ix_organization_api_keys_clerk_org_id_name'), table_name='organization_api_keys')
    op.drop_table('organization_api_keys')