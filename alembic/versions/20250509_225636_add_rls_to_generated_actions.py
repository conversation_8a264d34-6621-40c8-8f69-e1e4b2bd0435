"""add_rls_to_generated_actions

Revision ID: 20250509_225636
Revises: 20250509_180645
Create Date: 2025-05-09 22:56:36.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# import sqlalchemy as sa # Not strictly needed for this SQL-only migration

# revision identifiers, used by Alembic.
revision: str = '20250509_225636'
down_revision: Union[str, None] = '20250509_180645'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Enable Row-Level Security on the generated_actions table
    op.execute("ALTER TABLE generated_actions ENABLE ROW LEVEL SECURITY;")

    # Create the RLS policy for generated_actions
    # This policy restricts access based on the current tenant or system admin status.
    # It allows access if:
    # 1. The record's tenant_id matches the current session's tenant_id (obtained via current_tenant_id()).
    # 2. The current session belongs to a system admin (checked via is_system_admin()).
    # 3. The tenant_id context is not set (current_tenant_id() IS NULL), e.g., for internal system operations.
    # The policy applies to ALL operations (SELECT, INSERT, UPDATE, DELETE).
    op.execute(
        """
    CREATE POLICY tenant_isolation_generated_actions ON generated_actions
    FOR ALL
    USING (
        tenant_id = current_tenant_id() OR
        is_system_admin() OR
        current_tenant_id() IS NULL
    );
    """
    )


def downgrade() -> None:
    # Drop the RLS policy from the generated_actions table
    op.execute("DROP POLICY IF EXISTS tenant_isolation_generated_actions ON generated_actions;")

    # Disable Row-Level Security on the generated_actions table
    op.execute("ALTER TABLE generated_actions DISABLE ROW LEVEL SECURITY;")