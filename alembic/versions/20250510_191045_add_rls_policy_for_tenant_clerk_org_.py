"""add_rls_policy_for_tenant_clerk_org_lookup

Revision ID: 20250510_191045
Revises: 20250509_225636
Create Date: 2025-05-10 19:10:45.000000+00:00

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '20250510_191045'
down_revision: Union[str, None] = '20250509_225636' # Point to the last RLS-related or general migration
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Ensure RLS is enabled on the tenants table
    op.execute("ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;")
    op.execute("ALTER TABLE tenants FORCE ROW LEVEL SECURITY;") # Ensure it applies to table owners too

    # Drop the policy if it already exists to make this migration idempotent
    op.execute("DROP POLICY IF EXISTS select_for_tenants ON tenants;")

    # Create the RLS policy for tenants
    # This policy allows access if:
    # 1. The current_tenant_id matches the tenant's ID.
    # 2. The user is a system_admin.
    # 3. A specific clerk_org_id is being looked up (via app.rls.lookup_clerk_org_id)
    #    AND the context is public (no tenant_id, not system_admin).
    op.execute(
        """
        CREATE POLICY select_for_tenants ON tenants
        FOR SELECT
        USING (
            (id = current_tenant_id()) OR
            (is_system_admin()) OR
            (
                clerk_org_id = current_setting('app.rls.lookup_clerk_org_id', true) AND
                current_tenant_id() IS NULL AND
                is_system_admin() = false
            )
        );
        """
    )


def downgrade() -> None:
    # Drop the specific policy
    op.execute("DROP POLICY IF EXISTS select_for_tenants ON tenants;")

    # Re-create a more restrictive default policy (or the one assumed to be in place)
    # This assumes a common default where only the tenant or a system admin can see the record.
    # If there was a different specific policy before this one, that should be restored instead.
    op.execute(
        """
        CREATE POLICY select_for_tenants ON tenants
        FOR SELECT
        USING (
            (id = current_tenant_id()) OR
            (is_system_admin())
        );
        """
    )
    # Optionally, one might choose to disable RLS if it wasn't enabled before this migration.
    # However, given the context, it's safer to leave RLS enabled with a default policy.
    # op.execute("ALTER TABLE tenants DISABLE ROW LEVEL SECURITY;")