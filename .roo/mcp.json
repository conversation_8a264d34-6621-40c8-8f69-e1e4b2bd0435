{"mcpServers": {"postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql+psycopg2://postgres:postgres@localhost:5433/coherence"]}, "qdrant": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-qdrant"], "env": {"QDRANT_URL": "http://localhost:6335", "COLLECTION_NAME": "template_idx_global", "EMBEDDING_MODEL": "sentence-transformers/all-MiniLM-L6-v2"}}}}