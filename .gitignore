###############################################################################
# 📦  Dependencies & package managers
###############################################################################
/node_modules/          # npm / pnpm / yarn installs
node_modules/
/.pnp/                  # Yarn 2+ Plug-n-Play
.pnp.*
.pnpm-store/            # pnpm offline cache
coherence-admin/.pnpm-store/
coherence-admin/node_modules/

# Yarn v2+ virtual files – keep only what's useful
.yarn/*
!.yarn/patches/
!.yarn/plugins/
!.yarn/releases/
!.yarn/versions/

###############################################################################
# 🛠️  Build & coverage artefacts
###############################################################################
/dist/                  # Generic production bundles
/build/                 # Next.js or other build output
/.next/                 # Next.js serverless build
/out/                   # Next.js static export
/coverage/              # Test coverage reports
.vercel/                # Vercel deployment artefacts
/test-results/          # Playwright test results

###############################################################################
# 📝  Logs & debugging
###############################################################################
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
*.log                   # Catch-all (e.g., custom scripts)
*.tmp
tailwindcss-*.log       # Tailwind logs

###############################################################################
# 🔐  Secrets & environment files
###############################################################################
.env*                   # All env variants (.env, .env.local, etc.)
*.pem                   # PEM keys / certs
*.key
*.cer
*.crt

###############################################################################
# 🤖  AI / tooling outputs
###############################################################################
repomix-output.*        # Repomix packs or zips
coherence-admin/repomix-output.* # Repomix output in coherence-admin
.claude/               # Claude AI assistant files
CLAUDE.local.md        # Local Claude instructions
.roo/                  # roo command tool files

###############################################################################
# 🖋  TypeScript helpers
###############################################################################
*.tsbuildinfo
next-env.d.ts

###############################################################################
# 💻  OS & editor clutter
###############################################################################
.DS_Store
Thumbs.db

###############################################################################
# 🐍  Python specific
###############################################################################
__pycache__/
**/__pycache__/
*/__pycache__/*
*.py[cod]
*$py.class
*.so
.Python
.pytest_cache/
poetry.lock

###############################################################################
# 🧠  Project specific backups and temp files
###############################################################################
*.bak
src/coherence/api/v1/endpoints/admin_templates.py.bak
src/coherence/intent_pipeline/orchestrator.py.bak
src/coherence/core/qdrant_client.py.new
docs/weather.json