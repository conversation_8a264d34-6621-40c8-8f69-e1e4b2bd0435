name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    name: Lint & Type Check
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.5.1
          virtualenvs-create: true
          virtualenvs-in-project: true
      
      - name: Install dependencies
        run: |
          poetry install
      
      - name: Run Black formatter check
        run: |
          poetry run black --check src/ tests/
      
      - name: Run Ruff linter
        run: |
          poetry run ruff check src/ tests/
      
      - name: Run MyPy type checker
        run: |
          poetry run mypy src/
  
  test:
    name: Test with Python ${{ matrix.python-version }}
    runs-on: ubuntu-latest
    needs: lint
    strategy:
      matrix:
        python-version: ['3.10', '3.11']
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: coherence_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      qdrant:
        image: qdrant/qdrant:v1.6.0
        ports:
          - 6333:6333
        options: >-
          --health-cmd "curl -f http://localhost:6333/healthz"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.5.1
          virtualenvs-create: true
          virtualenvs-in-project: true
      
      - name: Install dependencies
        run: |
          poetry install
      
      - name: Run unit tests
        run: |
          poetry run pytest tests/unit/ --cov=src --cov-report=xml
        env:
          POSTGRES_HOST: localhost
          POSTGRES_PORT: 5432
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: coherence_test
          REDIS_HOST: localhost
          REDIS_PORT: 6379
          QDRANT_HOST: localhost
          QDRANT_PORT: 6333
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          fail_ci_if_error: false
  
  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Start Docker Compose services
        run: docker-compose up -d
      
      - name: Set up Python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.5.1
          virtualenvs-create: true
          virtualenvs-in-project: true
      
      - name: Install dependencies
        run: |
          poetry install
      
      - name: Wait for services
        run: |
          sleep 20  # Allow services time to start
          docker-compose ps
      
      - name: Run integration tests
        run: |
          poetry run pytest tests/integration/ --cov=src --cov-report=xml --cov-append
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          fail_ci_if_error: false
      
      - name: Stop Docker Compose services
        run: docker-compose down
  
  sdk-test:
    name: SDK Tests
    runs-on: ubuntu-latest
    needs: integration-test
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.5.1
          virtualenvs-create: true
          virtualenvs-in-project: true
      
      - name: Install dependencies
        run: |
          cd sdk
          poetry install
      
      - name: Run SDK tests
        run: |
          cd sdk
          poetry run pytest tests/ --cov=coherence_sdk --cov-report=xml
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./sdk/coverage.xml
          fail_ci_if_error: false
  
  openapi-validation:
    name: Validate OpenAPI Spec
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install OpenAPI validator
        run: npm install -g @stoplight/spectral-cli
      
      - name: Start API server
        run: |
          docker-compose up -d coherence-api
          sleep 20  # Allow API service to start
      
      - name: Fetch OpenAPI spec
        run: |
          curl -s http://localhost:8001/openapi.json > openapi.json
      
      - name: Validate OpenAPI spec
        run: |
          spectral lint openapi.json -f json
  
  coverage-check:
    name: Check Coverage Threshold
    runs-on: ubuntu-latest
    needs: [test, integration-test, sdk-test]
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Install dependencies
        run: |
          pip install coverage
      
      - name: Download coverage data
        uses: actions/download-artifact@v3
        with:
          name: coverage-data
      
      - name: Check coverage
        run: |
          coverage report --fail-under=90
  
  build-artifacts:
    name: Build Artifacts
    runs-on: ubuntu-latest
    needs: [coverage-check, openapi-validation]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.5.1
          virtualenvs-create: true
          virtualenvs-in-project: true
      
      - name: Build Python SDK
        run: |
          cd sdk
          poetry build
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Build TypeScript SDK
        run: |
          cd sdk/typescript
          npm install
          npm run build
      
      - name: Upload Python artifacts
        uses: actions/upload-artifact@v3
        with:
          name: python-sdk
          path: sdk/dist/
      
      - name: Upload TypeScript artifacts
        uses: actions/upload-artifact@v3
        with:
          name: typescript-sdk
          path: sdk/typescript/dist/