global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'coherence-api'
    scrape_interval: 300s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['coherence-api:8000']  # Application runs on port 8000 inside the container
    scrape_timeout: 5s
    honor_labels: true  # Preserve tenant_id labels
    scheme: http  # Explicitly set scheme

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']