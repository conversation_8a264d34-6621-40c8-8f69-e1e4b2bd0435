"""
Optional Prometheus instrumentator module.

This module provides a standardized way to instrument FastAPI with Prometheus metrics.
It's designed to be imported conditionally to avoid dependency issues.
"""

import logging

from fastapi import FastAPI

logger = logging.getLogger(__name__)

# Global variables to indicate availability of the instrumentator
instrumentator_available = False
instrumentator = None
metrics = None

try:
    # Conditional import for prometheus-fastapi-instrumentator
    from prometheus_fastapi_instrumentator import Instrumentator
    from prometheus_fastapi_instrumentator import metrics as pfi_metrics
    instrumentator_available = True
    instrumentator = Instrumentator
    metrics = pfi_metrics
    logger.info("Prometheus FastAPI Instrumentator is available")
except ImportError:
    logger.warning("Prometheus FastAPI Instrumentator is not available. Basic metrics will be used instead.")
    
def add_tenant_labels(metric_name, metric_doc, metric_namespace, metric_subsystem, metric_type):
    """Generate a function to add tenant labels to metrics.
    
    Args:
        metric_name: Name of the metric
        metric_doc: Documentation for the metric
        metric_namespace: Namespace for the metric
        metric_subsystem: Subsystem for the metric
        metric_type: Type of metric
        
    Returns:
        Function that adds tenant labels to metrics
    """
    if not instrumentator_available:
        return None
        
    def instrumentation(handler, request, response):
        tenant_id = request.headers.get("X-Tenant-ID", "unknown")
        user_id = request.headers.get("X-User-ID", "unknown")
        
        labels = {
            "tenant_id": tenant_id, 
            "user_id": user_id, 
            "handler": handler, 
            "method": request.method, 
            "status": response.status_code
        }
        
        # Return the labels to add to the metric
        return labels
    
    return instrumentation

def instrument_app(app: FastAPI) -> None:
    """Instrument a FastAPI application with Prometheus metrics.
    
    This function will use prometheus-fastapi-instrumentator if available,
    otherwise it falls back to basic metrics.
    
    Args:
        app: The FastAPI application to instrument
    """
    logger.info("instrument_app function called.")
    if not instrumentator_available:
        logger.warning("Skipping advanced instrumentation in instrument_app due to instrumentator_available=False.")
        return
    
    logger.info("instrumentator_available is True, proceeding with advanced instrumentation.")
    try:
        # Create instrumentator with minimal configuration
        inst = instrumentator()
        
        # Instrument the app with default settings
        logger.info("Attempting to call inst.instrument(app).expose(app)")
        inst.instrument(app).expose(app)
        logger.info("Successfully called inst.instrument(app).expose(app) and instrumented app with Prometheus FastAPI Instrumentator")
        
    except Exception as e:
        logger.error(f"Failed to instrument app with Prometheus FastAPI Instrumentator from within instrument_app: {e}", exc_info=True)