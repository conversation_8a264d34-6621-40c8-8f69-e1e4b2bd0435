# Coherence API Testing

This document provides a quick reference for testing the Coherence API using various methods.

## Available Testing Methods

1. **Script-based Testing**: Run the test script to automate a full flow of API operations
2. **Curl Commands**: Test individual endpoints from the command line
3. **Swagger UI**: Test endpoints interactively using the built-in web interface
4. **Postman Collection**: Import and use the provided Postman collection for structured testing

## 1. Script-based Testing

The `test_api_flow.sh` script automates a complete testing flow from tenant creation to intent resolution:

```bash
# Set your system admin key
export SYSTEM_ADMIN_KEY=your_system_admin_key

# Run the test script
./scripts/test_api_flow.sh
```

The script will:
- Create a test tenant
- Create API keys
- Update tenant settings
- Test intent resolution
- Test conversation continuation
- Create a template
- Test OpenAPI integration

## 2. Curl Commands

Refer to the [API Testing Guide](./api_testing_guide.md) for detailed curl commands to test each endpoint.

Quick examples:

```bash
# Set up environment variables
export API_URL="http://localhost:8001/v1"
export ADMIN_URL="${API_URL}/admin"
export SYSTEM_ADMIN_KEY="your_system_admin_key"

# Create a tenant
curl -X POST "${ADMIN_URL}/tenants" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${SYSTEM_ADMIN_KEY}" \
  -d '{
    "name": "Test Tenant",
    "industry_pack": "General",
    "admin_email": "<EMAIL>"
  }'

# Resolve an intent
curl -X POST "${API_URL}/resolve" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_tenant_api_key" \
  -d '{
    "user_id": "00000000-0000-0000-0000-000000000001",
    "role": "user",
    "message": "What's the weather like in Chicago?",
    "context": {
      "location": "US"
    }
  }'
```

## 3. Swagger UI

The Swagger UI is available at:

- **Development**: http://localhost:8001/docs
- **Production**: https://your-api-domain.com/docs

Steps to use Swagger UI:
1. Click "Authorize" and enter your API key
2. Expand an endpoint to see its details
3. Click "Try it out" and fill in the required parameters
4. Click "Execute" to send the request
5. View the response below

## 4. Postman Collection

A Postman collection is available at `docs/postman_collection.json`.

To use it:
1. In Postman, select "Import" > "File" and choose the collection file
2. Set the collection variables (click the collection name > Variables):
   - `baseUrl`: URL of your API (default: http://localhost:8001/v1)
   - `systemAdminKey`: Your system admin API key
3. Run the "Create Tenant" request first to set up a tenant and API key
4. Run subsequent requests as needed

## Common Use Cases to Test

1. **Weather Information Flow**:
   - Resolve intent: "What's the weather like in Chicago?"
   - Continue with: "How about tomorrow?"

## Testing CRFS Response Formats

When testing endpoints that return CRFS-formatted responses, you can verify the formatting by:

### 1. Testing with Different Accept Headers

```bash
# Get markdown response (default)
curl -X POST "${API_URL}/resolve" \
  -H "Content-Type: application/json" \
  -H "Accept: */*" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{"message": "What's the weather?"}'

# Get JSON response
curl -X POST "${API_URL}/resolve" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{"message": "What's the weather?"}'

# Get plain text response
curl -X POST "${API_URL}/resolve" \
  -H "Content-Type: application/json" \
  -H "Accept: text/plain" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{"message": "What's the weather?"}'
```

### 2. Validating CRFS Structure

Check that responses include proper CRFS sections:

```bash
# Test action endpoint to verify CRFS formatting
curl -X POST "${ADMIN_URL}/actions/test" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${ADMIN_KEY}" \
  -d '{
    "template_key": "weather_intent",
    "test_params": {
      "location": "Chicago"
    }
  }'
```

Expected response should contain:
- Hierarchical sections with headers and content
- Properly rendered template variables
- Pretty-formatted JSON when using `pretty_json` filter

### 3. Testing Error Formatting

Verify error responses follow CRFS fallback patterns:

```bash
# Trigger an error to test fallback formatting
curl -X POST "${API_URL}/resolve" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{"message": "invalid request"}'
```

### 4. Testing Response Mappings

When creating templates, test the response mapping functionality:

```bash
# Create a template with CRFS response format
curl -X POST "${ADMIN_URL}/templates" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${ADMIN_KEY}" \
  -d '{
    "key": "test_crfs",
    "name": "Test CRFS Template",
    "description": "Testing CRFS formatting",
    "category": "action",
    "scope": "tenant",
    "response_format": {
      "crfs_version": "2.0",
      "format": {
        "sections": [{
          "id": "result",
          "content": {
            "template": "Result: {{ response_mapping.data | pretty_json }}"
          }
        }]
      }
    },
    "actions": [{
      "response_mapping": {
        "data": "{{ response.json() }}"
      }
    }]
  }'
```

### 5. Pretty JSON Filter Testing

Test the `pretty_json` filter with complex JSON:

```bash
# Test with nested JSON response
curl -X POST "${API_URL}/resolve" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{
    "message": "Show me detailed weather data",
    "context": {
      "include_forecast": true,
      "include_alerts": true
    }
  }'
```

### Tips for CRFS Testing

1. Always check the `response_type` field to understand the response structure
2. Verify that template variables are properly rendered
3. Test with various data types to ensure filters work correctly
4. Validate that null/undefined values are handled with `default` filter
5. Check that content negotiation works with different Accept headers

2. **Meeting Scheduling Flow**:
   - Resolve intent: "Schedule a meeting with John tomorrow at 2pm"
   - Continue with: "Make it a video call"

3. **OpenAPI Integration Flow**:
   - Import an OpenAPI spec
   - Resolve intent: "Find all customers in the CRM"

## Troubleshooting

- **401 Unauthorized**: Check your API key
- **404 Not Found**: Verify endpoint path and resource IDs
- **400 Bad Request**: Check request payload format
- **500 Internal Server Error**: Check server logs for details

For more detailed troubleshooting, refer to the [API Testing Guide](./api_testing_guide.md).

## API Documentation

Full API documentation is available at:
- OpenAPI JSON: http://localhost:8001/openapi.json
- ReDoc UI: http://localhost:8001/redoc