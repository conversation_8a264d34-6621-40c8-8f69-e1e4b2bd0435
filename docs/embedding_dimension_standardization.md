# Embedding Dimension Standardization Plan

This document outlines a comprehensive step-by-step plan to standardize embedding dimensions across the Coherence platform. We're addressing several issues with the current implementation and ensuring consistent vector dimensions throughout the system.

## 0. Current Implementation (Validated in Code)

| Stage                        | Live implementation                                                                                                                                             | Risk it introduces                                                 |
| ---------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------ |
| **Embedding**                | `VectorIndexer._generate_embedding()` creates vectors with `fail_fast=False`, and if dimension ≠ `self.embedding_dimensions` it silently returns a zero-vector  | Bad vectors pollute the index & hide upstream errors               |
| **Prepare collection**       | `_ensure_template_collection_exists()` *creates* the collection but never inspects its `vectors.size`                                                           | Dimension drift stays undetected                                   |
| **Upsert**                   | `QdrantClient.upsert_intent_vectors/…template_vectors` push points straight to Qdrant; again no dimension check                                                 | Upserts blow up when a collection was created with 768-dim vectors |
| **Trigger path**             | Admin API (`update_template`) adds a background task → `VectorIndexer.upsert_template()` whenever an **intent\_router** template is changed                     | Correct trigger, but task fails for the two reasons above          |
| **Scripts**                  | Some rebuild scripts still hard-code 1 536 dims (ex. `rebuild_weather_index.py`)                                                                                | Re-introduce wrong-size collections after we fix prod              |
| **Collections live in prod** | Screenshot shows `intent_idx_<tenant>_user` + `template_idx_global`; that matches the code path so naming is *not* the bug                                      |                                                                    |

## 1. Code-level fixes (PR → staging)

### 1.1  Enforce collection dimensions at every write

```diff
@@ class QdrantClient
+    async def _verify_dimensions(self, collection: str, expected: int) -> None:
+        info = await self.get_collection(collection)
+        size = info.config.params.vectors.size
+        if size != expected:
+            raise ValueError(
+                f"{collection} expects {size}-d vectors, "
+                f"but we are configured for {expected}"
+            )
```

*Call it* at the top of both `upsert_intent_vectors` and `upsert_template_vectors` just after we compute `collection_name`.

### 1.2  Fail fast on bad embeddings

*In* `VectorIndexer._generate_embedding()`:

```diff
- if actual_dimension != self.embedding_dimensions:
-     logger.error("Embedding dimension mismatch …")
-     return [0.0] * self.embedding_dimensions
+ if actual_dimension != self.embedding_dimensions:
+     raise RuntimeError(
+         f"Embedding dimension mismatch: {actual_dimension} vs "
+         f"{self.embedding_dimensions}"
+     )
```

And switch the provider call to:

```diff
- llm_provider = self.llm_factory.get_default_provider(fail_fast=False)
+ llm_provider = self.llm_factory.get_default_provider(fail_fast=True)
```

### 1.3  Tighten collection creation helpers

Enhance `_ensure_template_collection_exists()` so that **when the collection already exists** we still read its config and validate the dimension (reuse `_verify_dimensions`). No change required when it is newly created with `vector_size=self.embedding_dimensions`.

### 1.4  Standardize vector size constant across all scripts

Every script still using a literal `1536` (see `rebuild_weather_index.py`) must instead import `settings.EMBEDDING_DIMENSION`. Add a CI-guard that greps for `vector_size=*1536` to prevent regression.

## 2. Operational migration (one-off)

1. **Deploy the code above** (it will start rejecting bad upserts rather than hiding them).

2. **Run `scripts/check_vector_dimensions.py`** (new utility) which enumerates every `*_idx_*` collection and prints size; fail the run if not = 384.

3. For each offending collection:

   ```bash
   python scripts/rebuild_template_index.py   --tenant <id>   # templates
   python scripts/rebuild_intent_index.py     --tenant <id>   # example–based intents
   ```

   Those scripts already recreate the collection with `vector_size=384`.

4. Confirm that `/collections` UI shows only 384-dim collections.

5. Re-run the check script → green.

## 3. Workflow changes for *new* API-generated intents

| Old                                                 | New                                                                                                                                                                                                 |
| --------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| API importer generates a template row → returns 201 | **Same**                                                                                                                                                                                            |
| Validation passes                                   | **Same**                                                                                                                                                                                            |
| Background task embeds **template body only**       | **Extend:** also embed each *example utterance* and call `upsert_intent_vectors` so Tier-1 can hit immediately. Leverage helper already used by rebuild scripts (`extract_examples_from_template`) |
| Upsert                                              | Now guarded by dimension check (1.1)                                                                                                                                                                |

*No user-visible latency increase*: embedding happens in the existing FastAPI `BackgroundTasks` queue.

## 4. Quality gates & monitoring

* **Unit tests**
  * `tests/unit/services/test_vector_indexer.py` already mocks embeddings; add a new case `test_upsert_dimension_mismatch_raises` that asserts we raise `ValueError` when `_verify_dimensions` reports a mismatch.
* **CI**
  * Add a workflow step `scripts/test_embedding_dimensions.py` (already present) that fails if OpenAI returns the wrong size.
* **Runtime metrics**
  * Expose counter `VECTOR_UPSERT_DIMENSION_ERROR` in `QdrantClient` when we hit the guard.
* **Alert**
  * Alert on 1% error rate for that counter over 5 min.

## 5. Timeline & owners

| Day     | Action                                                      | Owner    |
| ------- | ----------------------------------------------------------- | -------- |
| **T-0** | Merge PR with sections **1.1–1.4**                          | Platform |
| **T-1** | Deploy, run dimension audit, schedule rebuilds              | Dev-Ops  |
| **T-2** | Clean-up legacy scripts (delete `rebuild_weather_index.py`) | Platform |
| **T-3** | Add CI grep & new unit tests                                | QA       |
| **T-4** | Post-mortem review, turn alerts to **page**                 | SRE      |

## Benefits

After these steps:

* Every upsert **fails loud** if a collection's dimension drifts.
* No more zero-vectors land in Qdrant, protecting recall.
* API-generated intents are searchable the moment they're created.
* Legacy 1 536-dim collections are purged, aligning all tenants on the 384-dim standard.