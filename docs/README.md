# Coherence Documentation

This directory contains the comprehensive documentation for the Coherence middleware platform.

## Structure

- **PRDs (Product Requirements Documents)**: Detailed specifications of the product features and requirements
  - `master_prd.md`: The comprehensive vision for Coherence
  
- **Implementation Plans**: Strategies for implementing the features specified in the PRDs
  - Various implementation plans for different features and phases
  
- **Architecture**: Technical architecture documentation
  - `system_architecture.md`: Overall system design
  - `admin_site_architecture.md`: Admin dashboard architecture
  
- **Guides**: End-user and developer guides
  - `template_system.md`: Template system and CRFS v2 documentation
  - `authentication.md`: Authentication and authorization
  - `api_keys.md`: API key management
  - `getting_started.md`: Quick start guide
  
- **Design System**: UI/UX documentation
  - `cyber-wave.md`: Cyberpunk theme design system
  
- **Progress**: Development progress updates
  - Regular progress reports documenting completed features
  - Latest: `20250518_crfs_implementation_and_ui_updates.md`

## Key Documents

### Master PRD

The `master_prd.md` document outlines the comprehensive vision for Coherence as a language-agnostic middleware that transforms natural conversation into deterministic actions. It covers:

- Three-tier intent recognition architecture
- Template management system with inheritance
- Multi-tenant isolation with enterprise-grade security
- OpenAPI integration for auto-generating actions
- And many more features

### Implementation Plans

1. **Initial Implementation Plan**: Focuses on delivering PulseTrack API integration as an MVP while laying groundwork for the broader vision.

2. **Enhanced Implementation Plan**: Builds upon the initial plan with stronger foundations for:
   - Template inheritance
   - Error handling framework
   - Tenant isolation
   - Clear extension path to the full vision

3. **Implementation With Existing Code**: Provides a strategy for leveraging the existing PulseTrack codebase to accelerate development, including:
   - Reusable components from PulseTrack
   - Migration path from PulseTrack to Coherence
   - Implementation phases
   - Technical architecture using existing patterns

## Updating Documentation

When adding new documentation:

1. Place it in the appropriate directory based on document type
2. Link to it from relevant documents
3. Update this README if adding a major document

For questions about the documentation structure, please contact the project lead.

## Recent Updates (May 2025)

### CRFS v2 Implementation
- Implemented Coherence Response Format Standard v2
- Added dynamic response mapping generation
- Introduced pretty JSON filter for enhanced formatting
- Enabled content negotiation for multiple output formats

### UI Enhancements
- Launched cyberpunk-themed UI design system
- Updated all admin components with neon aesthetics
- Added animation effects and hover states
- Created comprehensive design documentation

### Documentation Improvements
- Updated template system guide with CRFS v2
- Added API testing guidelines for CRFS
- Created design system documentation
- Enhanced CLAUDE.md with latest features

For the latest updates, see the [progress directory](./progress/).