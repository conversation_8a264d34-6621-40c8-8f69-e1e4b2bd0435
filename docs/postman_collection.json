{"info": {"name": "Coherence API", "description": "A collection to test the Coherence API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8001/v1", "type": "string"}, {"key": "systemAdminKey", "value": "your_system_admin_key", "type": "string"}, {"key": "tenantApiKey", "value": "your_tenant_api_key", "type": "string"}, {"key": "tenantId", "value": "your_tenant_id", "type": "string"}, {"key": "userId", "value": "00000000-0000-0000-0000-000000000001", "type": "string"}, {"key": "conversationId", "value": "", "type": "string"}, {"key": "workflowId", "value": "", "type": "string"}], "item": [{"name": "1. Tenant Management", "item": [{"name": "List All Tenants", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{system<PERSON>d<PERSON><PERSON>ey}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/tenants", "host": ["{{baseUrl}}"], "path": ["admin", "tenants"]}}}, {"name": "Create Tenant", "event": [{"listen": "test", "script": {"exec": ["var jsonResponse = pm.response.json();", "pm.collectionVariables.set('tenantId', jsonResponse.id);", "pm.collectionVariables.set('tenantApi<PERSON>ey', jsonResponse.api_keys[0].key);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{system<PERSON>d<PERSON><PERSON>ey}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Postman Test Tenant\",\n    \"industry_pack\": \"General\",\n    \"admin_email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/tenants", "host": ["{{baseUrl}}"], "path": ["admin", "tenants"]}, "description": "Creates a new tenant and captures the tenant ID and API key in variables"}}, {"name": "Get Tenant", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/tenants/{{tenantId}}", "host": ["{{baseUrl}}"], "path": ["admin", "tenants", "{{tenantId}}"]}}}, {"name": "Update Tenant", "request": {"method": "PUT", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Tenant Name\",\n    \"industry_pack\": \"Financial\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/tenants/{{tenantId}}", "host": ["{{baseUrl}}"], "path": ["admin", "tenants", "{{tenantId}}"]}}}, {"name": "Create API Key", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenant_id\": \"{{tenantId}}\",\n    \"label\": \"Postman Testing Key\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/tenants/{{tenantId}}/api-keys", "host": ["{{baseUrl}}"], "path": ["admin", "tenants", "{{tenantId}}", "api-keys"]}}}, {"name": "List API Keys", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/tenants/{{tenantId}}/api-keys", "host": ["{{baseUrl}}"], "path": ["admin", "tenants", "{{tenantId}}", "api-keys"]}}}], "description": "Tenant and API key management endpoints"}, {"name": "2. <PERSON><PERSON>", "item": [{"name": "Get Tenant Settings", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/tenants/{{tenantId}}/settings", "host": ["{{baseUrl}}"], "path": ["admin", "tenants", "{{tenantId}}", "settings"]}}}, {"name": "Update Tenant Settings", "request": {"method": "PUT", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tier1_threshold\": \"0.9\",\n    \"tier2_threshold\": \"0.75\",\n    \"llm_model\": \"gpt-4\",\n    \"embedding_model\": \"text-embedding-ada-002\",\n    \"settings\": {\n        \"debug_mode\": \"true\",\n        \"log_level\": \"debug\"\n    }\n}"}, "url": {"raw": "{{baseUrl}}/admin/tenants/{{tenantId}}/settings", "host": ["{{baseUrl}}"], "path": ["admin", "tenants", "{{tenantId}}", "settings"]}}}]}, {"name": "3. Intent Resolution", "item": [{"name": "Resolve Weather Intent", "event": [{"listen": "test", "script": {"exec": ["var jsonResponse = pm.response.json();", "if (jsonResponse.conversation_id) {", "    pm.collectionVariables.set('conversationId', jsonResponse.conversation_id);", "}", "if (jsonResponse.kind === 'async' && jsonResponse.workflow_id) {", "    pm.collectionVariables.set('workflowId', jsonResponse.workflow_id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": \"{{userId}}\",\n    \"role\": \"user\",\n    \"message\": \"What's the weather like in Chicago?\",\n    \"context\": {\n        \"location\": \"US\",\n        \"timezone\": \"America/Chicago\"\n    }\n}"}, "url": {"raw": "{{baseUrl}}/resolve", "host": ["{{baseUrl}}"], "path": ["resolve"]}, "description": "Tests resolving a weather intent and saves the conversation ID for follow-up"}}, {"name": "Resolve Meeting Intent", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": \"{{userId}}\",\n    \"role\": \"user\",\n    \"message\": \"Schedule a meeting with <PERSON> tomorrow at 2pm\",\n    \"context\": {\n        \"timezone\": \"America/New_York\"\n    }\n}"}, "url": {"raw": "{{baseUrl}}/resolve", "host": ["{{baseUrl}}"], "path": ["resolve"]}, "description": "Tests resolving a meeting scheduling intent"}}, {"name": "Continue Conversation", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"conversation_id\": \"{{conversationId}}\",\n    \"user_id\": \"{{userId}}\",\n    \"message\": \"How about tomorrow afternoon?\"\n}"}, "url": {"raw": "{{baseUrl}}/continue", "host": ["{{baseUrl}}"], "path": ["continue"]}, "description": "Tests continuing a conversation with additional information"}}, {"name": "Check Workflow Status", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/status/{{workflowId}}", "host": ["{{baseUrl}}"], "path": ["status", "{{workflowId}}"]}, "description": "Tests checking the status of an async workflow"}}], "description": "Intent resolution and conversation endpoints"}, {"name": "4. Template Management", "item": [{"name": "List Templates", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/templates", "host": ["{{baseUrl}}"], "path": ["admin", "templates"]}}}, {"name": "Create Template", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"key\": \"weather_template\",\n    \"category\": \"intent_router\",\n    \"body\": \"How is the weather in {location} on {date}?\",\n    \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/templates", "host": ["{{baseUrl}}"], "path": ["admin", "templates"]}}}], "description": "Template management endpoints"}, {"name": "5. OpenAPI Integration", "item": [{"name": "List Integrations", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/integrations", "host": ["{{baseUrl}}"], "path": ["admin", "integrations"]}}}, {"name": "Import OpenAPI Spec", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{tenant<PERSON><PERSON><PERSON>ey}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Weather API\",\n    \"spec_url\": \"https://api.example.com/openapi.json\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/integrations", "host": ["{{baseUrl}}"], "path": ["admin", "integrations"]}}}], "description": "OpenAPI integration endpoints"}, {"name": "6. Cleanup (Optional)", "item": [{"name": "Delete Tenant", "request": {"method": "DELETE", "header": [{"key": "X-API-Key", "value": "{{system<PERSON>d<PERSON><PERSON>ey}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/tenants/{{tenantId}}", "host": ["{{baseUrl}}"], "path": ["admin", "tenants", "{{tenantId}}"]}, "description": "Deletes the test tenant"}}], "description": "Cleanup operations to remove test data"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}