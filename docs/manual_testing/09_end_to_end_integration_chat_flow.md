# End-to-End Integration to Chat Flow Testing Guide

## Overview
This guide walks through the complete process from importing an API integration to executing chat-based actions with the enhanced unified template system.

## Test Flow Stages

1. **Integration Import** → 2. **Template Generation** → 3. **Template Enhancement** → 4. **Intent Resolution** → 5. **Parameter Extraction** → 6. **Action Execution** → 7. **Chat Interface**

## Prerequisites

```bash
# Start all services
docker-compose up -d

# Verify services are running
docker-compose ps

# Get auth token (via Clerk or your auth system)
export TOKEN="your-auth-token"
export TENANT_ID="your-tenant-id"
export CLERK_ORG_ID="your-clerk-org-id"
```

## Stage 1: Integration Import

### 1.1 Prepare OpenAPI Spec
Create a test OpenAPI spec or use an existing one:

```yaml
# test-weather-api.yaml
openapi: 3.0.0
info:
  title: Weather API
  version: 1.0.0
servers:
  - url: https://api.weather-test.com
paths:
  /weather/current:
    get:
      operationId: getCurrentWeather
      summary: Get current weather for a location
      parameters:
        - name: location
          in: query
          required: true
          schema:
            type: string
            minLength: 1
            maxLength: 100
        - name: units
          in: query
          required: false
          schema:
            type: string
            enum: [metric, imperial]
            default: metric
      responses:
        200:
          description: Current weather data
          content:
            application/json:
              schema:
                type: object
                properties:
                  location:
                    type: string
                  temperature:
                    type: number
                  conditions:
                    type: string
                  humidity:
                    type: number
        404:
          description: Location not found
```

### 1.2 Import Integration
```bash
# Import via API
curl -X POST http://localhost:8001/v1/openapi/import \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "spec_url": "file:///path/to/test-weather-api.yaml",
    "integration_name": "Weather Test API",
    "credential_config": {
      "type": "api_key",
      "name": "weather_api_key"
    }
  }'
```

**Expected Response:**
```json
{
  "integration_id": "uuid",
  "templates_created": 1,
  "endpoints_processed": 1
}
```

## Stage 2: Template Generation Verification

### 2.1 List Generated Templates
```bash
curl -X GET http://localhost:8001/v1/templates?category=unified \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID"
```

### 2.2 Inspect Generated Template
```bash
# Get specific template
curl -X GET http://localhost:8001/v1/templates/{template_id} \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" | jq '.'
```

**Verify Template Contains:**
- ✓ Integration configuration with base_url
- ✓ Validation rules for parameters
- ✓ CRFS response formats
- ✓ Test data section

## Stage 3: Template Enhancement (if needed)

### 3.1 Run Enhancement Script
```bash
docker-compose exec coherence-api python scripts/enhance_unified_templates.py
```

### 3.2 Validate Enhanced Template
```bash
docker-compose exec coherence-api python scripts/validate_template_structure.py \
  --template-key getcurrentweather
```

## Stage 4: Intent Resolution Testing

### 4.1 Test Natural Language Query
```bash
curl -X POST http://localhost:8001/v1/resolve \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is the weather in London?"
  }'
```

**Expected Response:**
```json
{
  "intent": "getCurrentWeather",
  "confidence": 0.92,
  "parameters": {
    "location": "London"
  },
  "template_key": "getcurrentweather"
}
```

### 4.2 Test Multiple Variations
```bash
# Variation 1
curl -X POST http://localhost:8001/v1/resolve \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Show me the temperature in Paris"
  }'

# Variation 2
curl -X POST http://localhost:8001/v1/resolve \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "How hot is it in Tokyo?"
  }'

# Variation 3 (with units)
curl -X POST http://localhost:8001/v1/resolve \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Weather in New York in fahrenheit"
  }'
```

## Stage 5: Parameter Extraction Testing

### 5.1 Test Multi-Parameter Extraction
```bash
curl -X POST http://localhost:8001/v1/resolve \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Get weather for San Francisco in imperial units"
  }'
```

**Verify Extracted:**
- location: "San Francisco"
- units: "imperial"

### 5.2 Test Parameter Validation
```bash
# Invalid location (too long)
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "location": "This is an extremely long location name that definitely exceeds the maximum allowed length of 100 characters"
    }
  }'
```

## Stage 6: Action Execution

### 6.1 Test Mode Execution
```bash
# Execute with test data
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "location": "London",
      "units": "metric"
    },
    "scenario": "success"
  }'
```

### 6.2 Test CRFS Formatting
```bash
# JSON format
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {"location": "London"}
  }'

# Plain text format
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Accept: text/plain" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {"location": "London"}
  }'
```

## Stage 7: Chat Interface Testing

### 7.1 Complete Chat Flow
```bash
# Start chat session
curl -X POST http://localhost:8001/v1/chat \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What is the weather like in London?",
    "session_id": "test-session-001"
  }'
```

**Expected Flow:**
1. Intent resolved to weather template
2. Parameters extracted (location: "London")
3. Action executed (test mode or real API)
4. Response formatted with CRFS
5. Returned to user

### 7.2 Multi-Turn Conversation
```bash
# First turn
curl -X POST http://localhost:8001/v1/chat \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What is the weather in Paris?",
    "session_id": "test-session-002"
  }'

# Second turn (context maintained)
curl -X POST http://localhost:8001/v1/chat \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What about in fahrenheit?",
    "session_id": "test-session-002"
  }'
```

### 7.3 Error Handling in Chat
```bash
# Invalid location
curl -X POST http://localhost:8001/v1/chat \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Weather in Zzzzzzz123456",
    "session_id": "test-session-003"
  }'
```

## Stage 8: Admin UI Testing (Optional)

### 8.1 Access Admin UI
1. Navigate to http://localhost:3003
2. Login with Clerk credentials
3. Go to Templates section

### 8.2 Verify Template Display
- Check template list shows your imported template
- Click to view details
- Verify all sections are properly displayed

### 8.3 Test Template Editor
- Try editing intent patterns
- Update CRFS formatting
- Save and verify changes

## Validation Checklist

### Integration & Import
- [ ] OpenAPI spec imports successfully
- [ ] Integration created with correct base URL
- [ ] Templates generated for all endpoints

### Template Quality
- [ ] Integration config includes base_url and credentials
- [ ] Validation rules match OpenAPI schema
- [ ] CRFS formats include multiple options
- [ ] Test data is comprehensive

### Intent Resolution
- [ ] Natural language queries resolve correctly
- [ ] Confidence scores are appropriate (>0.8)
- [ ] Parameters extracted accurately
- [ ] Multiple phrasings work

### Parameter Handling
- [ ] Required parameters validated
- [ ] Type validation works
- [ ] Transformations apply (trim, lowercase)
- [ ] Error messages are helpful

### Action Execution
- [ ] Test mode returns mock data
- [ ] CRFS formatting works for all formats
- [ ] Error scenarios handled gracefully
- [ ] Performance is acceptable (<200ms)

### Chat Interface
- [ ] Complete flow works end-to-end
- [ ] Multi-turn conversations maintain context
- [ ] Errors are user-friendly
- [ ] Response formatting is correct

## Troubleshooting

### Common Issues

1. **Template Not Found**
   - Check template was created: `GET /v1/templates`
   - Verify tenant_id matches

2. **Intent Not Resolving**
   - Check intent patterns in template
   - Verify embedding index is updated
   - Try exact match first

3. **Parameter Validation Failing**
   - Check validation rules in template
   - Verify parameter names match
   - Check transformation order

4. **CRFS Formatting Issues**
   - Verify response structure matches template
   - Check Jinja2 syntax in templates
   - Test with simple format first

## Test Results Summary

| Stage | Component | Status | Notes |
|-------|-----------|---------|-------|
| 1 | Integration Import | | |
| 2 | Template Generation | | |
| 3 | Template Enhancement | | |
| 4 | Intent Resolution | | |
| 5 | Parameter Extraction | | |
| 6 | Action Execution | | |
| 7 | Chat Interface | | |
| 8 | Admin UI | | |

## Final Notes

- Document any issues or unexpected behavior
- Save request/response examples for documentation
- Note performance metrics
- Suggest improvements based on testing

## Sign-off

- Tester: _________________
- Date: _________________
- Environment: Development
- Overall Result: PASS / FAIL