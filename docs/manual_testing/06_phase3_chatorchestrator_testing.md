# Phase 3 - ChatOrchestrator Enhancement Testing Guide

## Feature Overview
Phase 3 enhances the ChatOrchestrator with:
1. Parameter validation using template-defined rules
2. Parameter transformations (trim, lowercase, etc.)
3. Integration config for base URL priority
4. CRFS auto-selection based on Accept headers

## Prerequisites
- Admin access to create/modify templates
- Test API integrations configured
- Various test templates with validation rules

## Test Scenarios

### 1. Parameter Validation Testing

#### Test 1.1: Required Parameter Validation
**Setup:**
1. Create a template with required parameters:
```json
{
  "parameters": {
    "schema": {
      "email": {"type": "string", "required": true},
      "age": {"type": "integer", "required": true}
    },
    "validation_rules": {
      "email": {"type": "string", "required": true, "format": "email"},
      "age": {"type": "integer", "required": true, "min": 18, "max": 120}
    }
  }
}
```

**Test Steps:**
1. Send request without email parameter
2. Send request with invalid email format
3. Send request with age < 18
4. Send request with age > 120
5. Send request with valid parameters

**Expected Results:**
- Missing email: "Required parameter 'email' is missing"
- Invalid email: "Parameter 'email' must be a valid email address"
- Age < 18: "Parameter 'age' must be >= 18"
- Age > 120: "Parameter 'age' must be <= 120"
- Valid request: Successful execution

#### Test 1.2: String Validation Rules
**Test Cases:**
- `min_length`: "abc" with min_length=5 → Error
- `max_length`: "abcdefghij" with max_length=5 → Error
- `pattern`: "ABC123" with pattern="^[a-z]+$" → Error
- `enum`: "purple" with enum=["red", "green", "blue"] → Error

#### Test 1.3: Number Validation Rules
**Test Cases:**
- `minimum`: 5 with min=10 → Error
- `maximum`: 20 with max=15 → Error
- `exclusive_min`: 10 with exclusive_min=10 → Error
- `exclusive_max`: 10 with exclusive_max=10 → Error
- `multiple_of`: 7 with multiple_of=3 → Error

#### Test 1.4: Array Validation Rules
**Test Cases:**
- `min_items`: ["a"] with min_items=3 → Error
- `max_items`: ["a", "b", "c"] with max_items=2 → Error
- `unique_items`: ["a", "b", "a"] with unique_items=true → Error

### 2. Parameter Transformation Testing

#### Test 2.1: String Transformations
**Setup:** Create template with transformations:
```json
{
  "parameters": {
    "transformations": {
      "username": ["trim", "lowercase"],
      "email": ["trim", "lowercase"],
      "phone": ["remove_non_numeric"]
    }
  }
}
```

**Test Cases:**
1. Username: "  JohnDoe  " → "johndoe"
2. Email: " <EMAIL> " → "<EMAIL>"
3. Phone: "(*************" → "5551234567"

#### Test 2.2: Array Transformations
**Test Case:**
- Input: ["apple", "banana", "apple", "cherry"]
- Transformation: ["deduplicate"]
- Expected: ["apple", "banana", "cherry"]

### 3. Integration Config Base URL Testing

#### Test 3.1: Base URL Resolution
**Setup:** Create template with integration config:
```json
{
  "action_config": {
    "integration": {
      "base_url": "https://api.example.com",
      "api_version": "v2",
      "credential_ref": "example_api_key"
    }
  }
}
```

**Test Steps:**
1. Execute action and verify base URL used
2. Check logs for "Using integration config for base URL"
3. Verify API version appended correctly

**Expected Results:**
- Base URL: "https://api.example.com/v2"
- API calls use the resolved base URL
- Credential reference resolved correctly

#### Test 3.2: Base URL Priority
**Test Scenarios:**
1. Template has integration.base_url → Use it
2. No integration config → Use action_config.base_url
3. Neither present → Use default from integration

### 4. CRFS Auto-Selection Testing

#### Test 4.1: Accept Header Based Selection
**Setup:** Template with multiple CRFS formats:
```json
{
  "response_format": {
    "crfs": {
      "auto_select": true,
      "default_format": "structured",
      "formats": {
        "application/json": {...},
        "text/plain": {...},
        "structured": {...}
      }
    }
  }
}
```

**Test Cases:**
1. Accept: "application/json" → JSON format
2. Accept: "text/plain" → Plain text format
3. Accept: "*/*" → Default structured format
4. No Accept header → Default structured format

#### Test 4.2: Content-Type Context
**Test:** Verify content_type from API response is available in template context
**Steps:**
1. Make API call that returns JSON
2. Check template context has content_type="application/json"
3. Verify CRFS formatter can access this for format decisions

### 5. Error Handling

#### Test 5.1: Validation Error Response
**Test:** Multiple validation errors
**Expected Format:**
```
I found some issues with your request:
• Parameter 'email' must be a valid email address
• Parameter 'age' must be >= 18
• Required parameter 'location' is missing

Please correct these and try again.
```

#### Test 5.2: Transformation Robustness
**Test Cases:**
- Apply "lowercase" to number → No error, value unchanged
- Apply "trim" to array → No error, value unchanged
- Apply "deduplicate" to string → No error, value unchanged

### 6. Integration Testing

#### Test 6.1: Full Flow Test
**Steps:**
1. Create unified template with all Phase 3 features:
   - Validation rules
   - Transformations
   - Integration config
   - CRFS auto-selection
2. Send request with mixed valid/invalid params
3. Verify validation occurs first
4. Verify transformations applied after validation
5. Verify base URL resolved from integration
6. Verify response format auto-selected

**Success Criteria:**
- [ ] Validation prevents invalid requests
- [ ] Transformations clean data appropriately
- [ ] API calls use correct base URL
- [ ] Response format matches Accept header

### 7. Performance Testing

#### Test 7.1: Validation Performance
**Test:** Time validation for complex rules
- 10 parameters with multiple rules each
- Measure validation time
- Should be < 10ms

#### Test 7.2: Transformation Performance
**Test:** Time transformations
- Apply 5 transformations to 20 parameters
- Measure transformation time
- Should be < 5ms

## Common Issues

### Issue: Parameters not validated
- Check template has `parameters.validation_rules`
- Verify template loaded correctly
- Check logs for validation skip reasons

### Issue: Transformations not applied
- Check template has `parameters.transformations`
- Verify parameter names match exactly
- Check transformation names are valid

### Issue: Base URL not used
- Check template has `action_config.integration`
- Verify integration config structure
- Check action executor supports base_url param

### Issue: CRFS format not auto-selected
- Check template has `response_format.crfs.auto_select = true`
- Verify Accept header passed in context
- Check CRFS formatter supports auto-selection

## Success Criteria

- [ ] All validation rules enforced correctly
- [ ] Parameter transformations applied as expected
- [ ] Integration config base URLs resolved
- [ ] CRFS auto-selection works with Accept headers
- [ ] Error messages are user-friendly
- [ ] Performance within acceptable limits
- [ ] No regressions in existing functionality