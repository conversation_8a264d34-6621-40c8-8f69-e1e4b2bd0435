# Phase 8: Enhanced Unified Templates - Manual Testing Guide

## Overview
This guide provides comprehensive manual testing procedures for the enhanced unified template features implemented in Phases 1-8.

## Prerequisites
- Docker environment running
- API running on port 8001
- Valid authentication token
- Access to template IDs

## Testing Checklist

### 1. Template Test Mode ✓

#### 1.1 Basic Test Mode
```bash
# Get a template ID first
curl -X GET http://localhost:8001/v1/templates \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID"

# Test with mock data
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "location": "London"
    },
    "scenario": "success"
  }'
```

**Expected**: Mock response formatted with CRFS

#### 1.2 Error Scenario Testing
```bash
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "location": "InvalidCity123"
    },
    "scenario": "not_found"
  }'
```

**Expected**: 404 error with interpolated message

### 2. CRFS Format Auto-Selection ✓

#### 2.1 JSON Format
```bash
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {"location": "Paris"}
  }'
```

**Expected**: Raw JSON response

#### 2.2 Plain Text Format
```bash
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Accept: text/plain" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {"location": "Paris"}
  }'
```

**Expected**: Plain text formatted response

#### 2.3 Structured Format (Default)
```bash
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Accept: text/html" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {"location": "Paris"}
  }'
```

**Expected**: Structured CRFS sections

### 3. Parameter Validation ✓

#### 3.1 Missing Required Parameter
```bash
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {}
  }'
```

**Expected**: Validation error for missing "location"

#### 3.2 Invalid Parameter Format
```bash
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "email": "not-an-email",
      "location": "London"
    }
  }'
```

**Expected**: Email format validation error

#### 3.3 Parameter Length Validation
```bash
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "location": "A very long location name that exceeds the maximum allowed length of 100 characters in the validation rules"
    }
  }'
```

**Expected**: Max length validation error

### 4. Parameter Transformations ✓

#### 4.1 Test Trim and Lowercase
```bash
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "location": "  LONDON  ",
      "email": "  <EMAIL>  "
    }
  }'
```

**Expected**: Parameters trimmed and lowercased in response

### 5. Integration Configuration ✓

#### 5.1 Environment Variable Substitution
Check template configuration:
```bash
curl -X GET http://localhost:8001/v1/templates/{template_id} \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID"
```

Look for: `"base_url": "${API_URL:https://default.api.com}"`

#### 5.2 Credential Reference
Verify credential references in action config:
- `credential_ref` field should reference stored credentials
- Authentication headers should use `{{credentials.key_name}}`

### 6. Complex Template Flows ✓

#### 6.1 Payment Processing Flow
```bash
# Create payment intent
curl -X POST http://localhost:8001/v1/resolve \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "process payment of 50 USD"
  }'

# Execute with test mode
curl -X POST http://localhost:8001/v1/templates/{payment_template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "amount": 50.00,
      "currency": "USD",
      "payment_method_id": "pm_test_123"
    }
  }'
```

#### 6.2 Authentication Flow
```bash
# Test login
curl -X POST http://localhost:8001/v1/templates/{auth_template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "email": "<EMAIL>",
      "password": "SecurePass123!"
    }
  }'
```

#### 6.3 Data Retrieval with Pagination
```bash
# Test data listing
curl -X POST http://localhost:8001/v1/templates/{data_template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "page": 1,
      "limit": 20,
      "status": "active"
    }
  }'
```

### 7. Error Handling ✓

#### 7.1 Authentication Error
```bash
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer invalid_token" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {"location": "London"}
  }'
```

**Expected**: 401 Unauthorized

#### 7.2 Template Not Found
```bash
curl -X POST http://localhost:8001/v1/templates/00000000-0000-0000-0000-000000000000/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {"location": "London"}
  }'
```

**Expected**: 404 Template not found

### 8. Performance Testing ✓

#### 8.1 Response Time Check
```bash
time curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {"location": "London"}
  }'
```

**Expected**: < 200ms for test mode

#### 8.2 Concurrent Requests
```bash
# Run 10 concurrent requests
for i in {1..10}; do
  curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
    -H "Authorization: Bearer $TOKEN" \
    -H "X-Tenant-ID: $TENANT_ID" \
    -H "Content-Type: application/json" \
    -d '{"parameters": {"location": "London"}}' &
done
wait
```

**Expected**: All requests succeed

## Validation Criteria

### Pass Criteria
- [ ] All test mode scenarios return expected mock data
- [ ] CRFS auto-selection works for all Accept headers
- [ ] Parameter validation catches all invalid inputs
- [ ] Transformations apply correctly
- [ ] Error messages include interpolated parameters
- [ ] Performance meets requirements (< 200ms)

### Known Issues
- Document any issues discovered during testing
- Note workarounds if applicable

## Test Results

| Test Case | Status | Notes |
|-----------|---------|-------|
| Test Mode - Success | | |
| Test Mode - Error | | |
| CRFS JSON Format | | |
| CRFS Plain Text | | |
| CRFS Structured | | |
| Missing Parameters | | |
| Invalid Format | | |
| Transformations | | |
| Payment Flow | | |
| Auth Flow | | |
| Data Retrieval | | |
| Performance | | |

## Sign-off

- Tester: _________________
- Date: _________________
- Version: Phase 8 Implementation
- Result: PASS / FAIL