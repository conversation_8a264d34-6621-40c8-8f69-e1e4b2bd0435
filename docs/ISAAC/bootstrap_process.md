# ISAAC Bootstrap Process

## How CLAUDE.md Bootstraps from KG

Instead of storing all ISAAC instructions in CLAUDE.md, the system now:
1. Stores minimal bootstrap code in CLAUDE.md
2. Queries the Knowledge Graph for full instructions
3. Executes patterns dynamically based on context

## New CLAUDE.md Structure

```markdown
# CLAUDE.md (Ultra-Compressed with K<PERSON> Bootstrap)

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## 🧠 CORE RULES
- Production-ready code only. No fabrication.
- Query Neo4j KG before any reference/import
- All Python runs in Docker: `docker-compose exec coherence-api`
- Branch workflow only, never touch main

## 🤖 ISAAC BOOTSTRAP

### Initialize ISAAC
When user types `/isaac-init`:
```python
# Get initialization pattern
init_pattern = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {name: 'isaac_init'})
  RETURN p.code as response
""")
print(init_pattern[0]['response'])

# Get and execute dashboard pattern
dashboard_pattern = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {name: 'isaac_dashboard_init'})
  RETURN p.code
""")
exec(dashboard_pattern[0]['code'])
```

### Execute ISAAC Commands
For any `/isaac-*` command:
```python
# Extract command name
command = user_input.split()[0].replace('/', '')

# Get command pattern
cmd_pattern = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {name: $cmd_name})
  WHERE p.category = 'isaac_command'
  RETURN p.description, p.requires_parameter, p.code
""", params={"cmd_name": command})

# Get workflow steps
workflow = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {category: 'isaac_workflow', command: $cmd})
  RETURN p.sequence, p.name, p.code
  ORDER BY p.sequence
""", params={"cmd": user_input})

# Execute workflow steps in order
for step in workflow:
    exec(step['code'])
```

### Get Validation Patterns
```python
# Get validation pattern by name
validation = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {name: $pattern_name, category: 'isaac_validation'})
  RETURN p.code
""", params={"pattern_name": "isaac_validation_component_lookup"})
```

### Get Rules
```python
# Get critical rules
rules = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {name: 'isaac_critical_rules'})
  RETURN p.code
""")
```

## MCP Tools
- `mcp__isaac__read_neo4j_cypher` - Read patterns
- `mcp__isaac__write_neo4j_cypher` - Update patterns
- `mcp__isaac__get_neo4j_schema` - Schema info

## Architecture & Commands
[Keep existing architecture section but remove verbose ISAAC instructions]
```

## Benefits

1. **Token Efficiency**: 90%+ reduction in CLAUDE.md size
2. **Dynamic Updates**: Patterns improve without changing CLAUDE.md
3. **Context-Aware**: Load only needed patterns
4. **Self-Documenting**: Patterns document themselves
5. **Version Control**: All changes tracked in KG

## Pattern Loading Strategy

### On /isaac-init
1. Load command definitions
2. Load dashboard pattern
3. Display available commands

### On /isaac-begin-session
1. Load command pattern
2. Load workflow steps (1-7)
3. Load validation patterns
4. Execute in sequence

### During Session
1. Load validation patterns on demand
2. Update progress patterns
3. Track pattern usage

### On /isaac-end-session
1. Load finalization workflow
2. Load git patterns
3. Load PR template
4. Execute completion

## Error Handling

If pattern not found:
```python
if not pattern_result:
    print(f"Pattern '{pattern_name}' not found in KG.")
    similar = mcp__isaac__read_neo4j_cypher(query="""
      MATCH (p:Pattern)
      WHERE p.category STARTS WITH 'isaac_'
        AND p.name CONTAINS $partial
      RETURN p.name, p.category
      LIMIT 5
    """, params={"partial": pattern_name.split('_')[-1]})
    print(f"Similar patterns: {similar}")
```

## Pattern Evolution

Each successful use:
- Increments usage_count
- Updates last_used_at
- Slightly increases confidence

Failed uses:
- Decrease confidence
- Log failure reason
- Suggest alternatives