# ISAAC Cross-Project Usage Guide

## Overview

ISAAC is now a standalone system in the Knowledge Graph, independent of any specific project. This guide explains how to use ISAAC in your other projects like Pulse, Track, or personal projects.

## Prerequisites

1. Neo4j Knowledge Graph with ISAAC System installed
2. MCP tools configured (`mcp__isaac__*` commands)
3. GitHub repository for your project
4. CLAUDE.md with ISAAC bootstrap code

## Setting Up ISAAC in a New Project

### Step 1: Register Your Project with ISAAC

When starting to use ISAAC in a new project, first register it:

```cypher
// Register your project with ISAAC
MATCH (isaac:System {name: 'ISAAC'})
MATCH (p:Project {name: 'YourProjectName'})
CREATE (p)-[:USES]->(isaac)
```

If your project doesn't exist in the KG yet:

```cypher
// Create project and register with ISAAC
MATCH (isaac:System {name: 'ISAAC'})
CREATE (p:Project {
  name: 'YourProjectName',
  description: 'Your project description',
  created_at: datetime(),
  status: 'active'
})
CREATE (p)-[:USES]->(isaac)
```

### Step 2: Add ISAAC Bootstrap to CLAUDE.md

Copy this minimal bootstrap code to your project's CLAUDE.md:

```markdown
## 🤖 ISAAC BOOTSTRAP

When user types any `/isaac-*` command:

```python
# Extract command name
cmd_name = user_input.split()[0].replace('/', '').replace('-', '_')

# Get command pattern from KG
cmd_pattern = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {name: $cmd_name, category: 'isaac_command'})
  RETURN p.code, p.description, p.requires_parameter
""", params={"cmd_name": cmd_name})

if not cmd_pattern:
    print(f"Unknown ISAAC command: {user_input}")
    return

# Execute command-specific logic
if cmd_name == 'isaac_init':
    print(cmd_pattern[0]['code'])
    # Get project-specific dashboard
    dashboard = mcp__isaac__read_neo4j_cypher(query="""
      MATCH (p:Project {name: 'YourProjectName'})
      OPTIONAL MATCH (p)-[:HAS_SESSION]->(s:Session)
      RETURN {
        project: p.name,
        total_sessions: count(s),
        active_sessions: size([s IN collect(s) WHERE s.status = 'active'])
      } as metrics
    """)
    print(f"Project: {dashboard[0]['metrics']}")
```
```

### Step 3: Configure GitHub Repository

ISAAC needs to know your GitHub repository details. Update the patterns or create project-specific configuration:

```cypher
// Add GitHub config to your project
MATCH (p:Project {name: 'YourProjectName'})
SET p.github_owner = 'YourGitHubUsername',
    p.github_repo = 'your-repo-name'
```

### Step 4: First ISAAC Session

Start your first ISAAC session in the new project:

```
/isaac-init
/isaac-begin-session "Initial setup and configuration"
```

ISAAC will:
1. Create a feature branch
2. Create a GitHub issue
3. Track the session in KG
4. Link session to both your project and ISAAC system

## Querying Cross-Project ISAAC Data

### Find All Projects Using ISAAC

```cypher
MATCH (p:Project)-[:USES]->(isaac:System {name: 'ISAAC'})
RETURN p.name, p.description
ORDER BY p.name
```

### Get ISAAC Sessions Across All Projects

```cypher
MATCH (s:Session)-[:MANAGED_BY]->(isaac:System {name: 'ISAAC'})
MATCH (s)-[:BELONGS_TO]->(p:Project)
RETURN p.name as project, 
       count(s) as session_count,
       count(CASE WHEN s.status = 'active' THEN 1 END) as active_sessions
ORDER BY session_count DESC
```

### Find Patterns Used in Specific Project

```cypher
MATCH (p:Project {name: 'YourProjectName'})-[:HAS_SESSION]->(s:Session)
MATCH (s)-[:USED_PATTERN]->(pattern:Pattern)-[:BELONGS_TO]->(isaac:System {name: 'ISAAC'})
RETURN DISTINCT pattern.name, pattern.category, count(s) as usage_count
ORDER BY usage_count DESC
```

## Best Practices

### 1. Project Initialization

Always run `/isaac-init` when starting work on a project to:
- Verify ISAAC connection
- Check project registration
- Display current project state

### 2. Pattern Evolution

Patterns improve across all projects. When a pattern succeeds:

```cypher
MATCH (pattern:Pattern {name: $pattern_name})
SET pattern.usage_count = pattern.usage_count + 1,
    pattern.last_used_at = datetime(),
    pattern.confidence = CASE 
      WHEN pattern.confidence < 1.0 THEN pattern.confidence + 0.01 
      ELSE 1.0 
    END
```

### 3. Project-Specific Patterns

While ISAAC patterns are global, you can create project-specific patterns:

```cypher
MATCH (p:Project {name: 'YourProjectName'})
CREATE (pattern:Pattern {
  name: 'your_project_pattern',
  category: 'project_specific',
  code: 'Your pattern code',
  confidence: 0.9
})
CREATE (pattern)-[:BELONGS_TO]->(p)
```

### 4. Session Management

Sessions belong to projects but are managed by ISAAC:

```
Project <-- BELONGS_TO -- Session -- MANAGED_BY --> ISAAC System
```

This allows project-specific context while maintaining ISAAC's management capabilities.

## Troubleshooting

### ISAAC Commands Not Working

1. Verify ISAAC system exists:
```cypher
MATCH (isaac:System {name: 'ISAAC'})
RETURN isaac
```

2. Check project registration:
```cypher
MATCH (p:Project {name: 'YourProjectName'})-[:USES]->(isaac:System {name: 'ISAAC'})
RETURN p, isaac
```

3. Verify patterns are loaded:
```cypher
MATCH (pattern:Pattern)-[:BELONGS_TO]->(isaac:System {name: 'ISAAC'})
RETURN pattern.category, count(*) as count
```

### Session Not Linking to ISAAC

Ensure your session has identifiable ISAAC markers:
- Branch name starts with `isaac/`
- Has a GitHub issue number
- Contains "ISAAC" in task description

## Advanced Usage

### Custom ISAAC Workflows

Create project-specific workflow overrides:

```cypher
// Create custom workflow for your project
MATCH (p:Project {name: 'YourProjectName'})
CREATE (workflow:Pattern {
  name: 'custom_begin_session',
  category: 'project_workflow',
  project_override: true,
  code: 'Your custom workflow code'
})
CREATE (workflow)-[:OVERRIDES]->(:Pattern {name: 'isaac_begin_session'})
CREATE (workflow)-[:BELONGS_TO]->(p)
```

### Metrics and Reporting

Track ISAAC usage across projects:

```cypher
MATCH (isaac:System {name: 'ISAAC'})
MATCH (p:Project)-[:USES]->(isaac)
OPTIONAL MATCH (s:Session)-[:BELONGS_TO]->(p)
WHERE s.created_at > datetime() - duration('P30D')
RETURN p.name as project,
       count(s) as recent_sessions,
       avg(s.completion_time - s.created_at) as avg_session_duration
ORDER BY recent_sessions DESC
```

## Conclusion

ISAAC is designed to be your consistent development companion across all projects. By maintaining patterns in a central system while keeping project data separate, you get the best of both worlds: consistent workflows and project-specific context.