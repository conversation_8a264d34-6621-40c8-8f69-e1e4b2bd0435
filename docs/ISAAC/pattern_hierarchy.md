# ISAAC Pattern Hierarchy

## Pattern Categories

### 1. `isaac_command` - Top-level commands
- `isaac_init` - Initialize system
- `isaac_begin_session` - Start development
- `isaac_end_session` - Finalize and PR
- `isaac_sync` - Deep KG sync

### 2. `isaac_workflow` - Step-by-step workflows
Organized by command and sequence:
- `isaac_workflow_branch_creation` (begin-session, seq: 1)
- `isaac_workflow_issue_parsing` (begin-session, seq: 2)
- `isaac_workflow_github_issue` (begin-session, seq: 3)
- `isaac_workflow_session_creation` (begin-session, seq: 4)

### 3. `isaac_validation` - Core validation patterns
- `isaac_validation_component_lookup`
- `isaac_validation_function_signature`
- `isaac_validation_pattern_check`
- `isaac_validation_update_component`

### 4. `isaac_dashboard` - Dashboard generation
- `isaac_dashboard_init` - After /isaac-init
- `isaac_dashboard_progress` - During session
- `isaac_dashboard_completion` - After /isaac-end-session

### 5. `isaac_git` - Git workflow patterns
- `isaac_git_during_session` - No commits rule
- `isaac_git_commit_pattern` - Structured commits
- `isaac_git_pr_body` - PR template

### 6. `isaac_rules` - Critical rules
- `isaac_critical_rules` - The 9 commandments

## Pattern Relationships

```
Project: Coherence
    |
    +--> HAS_PATTERN --> Pattern (category: isaac_command)
    |                       |
    |                       +--> isaac_init
    |                       +--> isaac_begin_session
    |                       +--> isaac_end_session
    |                       +--> isaac_sync
    |
    +--> HAS_PATTERN --> Pattern (category: isaac_workflow)
    |                       |
    |                       +--> Workflow steps (with sequence)
    |
    +--> HAS_PATTERN --> Pattern (category: isaac_validation)
    |                       |
    |                       +--> Validation patterns
    |
    +--> HAS_PATTERN --> Pattern (category: isaac_dashboard)
    |                       |
    |                       +--> Dashboard generators
    |
    +--> HAS_PATTERN --> Pattern (category: isaac_git)
                            |
                            +--> Git workflow patterns
```

## Pattern Evolution

Patterns improve over time through:
1. **confidence** - Success rate (0.0-1.0)
2. **usage_count** - Times used successfully
3. **last_used_at** - Recent usage tracking

Failed patterns can be marked with lower confidence and alternative patterns suggested.