# ISAAC Knowledge Graph Integration

This directory documents the ISAAC system's integration with the Neo4j Knowledge Graph.

## Overview

ISAAC (Intelligent System for Autonomous AI-assisted Coding) instructions are now stored in the Knowledge Graph itself, making the system:
- **Self-documenting**: Instructions are queryable entities
- **Self-improving**: Patterns evolve based on successful sessions
- **Token-efficient**: Minimal CLAUDE.md bootstraps from KG
- **Version-controlled**: All changes tracked in the graph

## Structure

- `migration_log.md` - Records of migrating instructions to KG
- `pattern_hierarchy.md` - How ISAAC patterns are organized
- `query_examples.md` - Common queries for ISAAC patterns
- `bootstrap_process.md` - How CLAUDE.md bootstraps from KG

## Key Concepts

### Pattern Categories
- `isaac_command` - Command definitions (/isaac-init, etc.)
- `isaac_workflow` - Workflow steps and sequences
- `isaac_validation` - KG validation patterns
- `isaac_git` - Git workflow patterns
- `isaac_dashboard` - Dashboard generation patterns

### Pattern Attributes
- `name` - Pattern identifier
- `code` - Executable code/instructions
- `context` - When/how to use
- `confidence` - Success rate (0.0-1.0)
- `usage_count` - Times used
- `example` - Usage examples

## Quick Start

To retrieve ISAAC instructions from KG:

```python
# Get all ISAAC commands
commands = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {category: 'isaac_command'})
  RETURN p.name, p.code, p.context
  ORDER BY p.name
""")

# Get workflow for specific command
workflow = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {category: 'isaac_workflow', command: '/isaac-begin-session'})
  RETURN p.name, p.code, p.context
  ORDER BY p.sequence
""")
```