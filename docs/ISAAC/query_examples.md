# ISAAC Pattern Query Examples

## Basic Queries

### Get all ISAAC commands
```cypher
MATCH (p:<PERSON><PERSON> {category: 'isaac_command'})
RETURN p.name, p.description, p.context
ORDER BY p.name
```

### Get specific command details
```cypher
MATCH (p:Pattern {name: 'isaac_begin_session'})
RETURN p.description, p.code, p.requires_parameter
```

### Get workflow steps for a command
```cypher
MATCH (p:Pattern {category: 'isaac_workflow', command: '/isaac-begin-session'})
RETURN p.name, p.sequence, p.description, p.code
ORDER BY p.sequence
```

## Advanced Queries

### Get all validation patterns
```cypher
MATCH (p:Pattern {category: 'isaac_validation'})
RETURN p.name, p.description, p.code
ORDER BY p.usage_count DESC
```

### Get most successful patterns
```cypher
MATCH (p:Pattern)
WHERE p.category STARTS WITH 'isaac_' 
  AND p.confidence > 0.9
RETURN p.category, p.name, p.confidence, p.usage_count
ORDER BY p.confidence DESC, p.usage_count DESC
```

### Get patterns used in last 7 days
```cypher
MATCH (p:Pattern)
WHERE p.category STARTS WITH 'isaac_'
  AND p.last_used_at > datetime() - duration('P7D')
RETURN p.name, p.category, p.last_used_at
ORDER BY p.last_used_at DESC
```

## Pattern Usage Tracking

### Update pattern after successful use
```cypher
MATCH (p:Pattern {name: $pattern_name})
SET p.usage_count = p.usage_count + 1,
    p.last_used_at = datetime(),
    p.confidence = CASE 
      WHEN p.confidence < 1.0 
      THEN p.confidence + 0.01 
      ELSE 1.0 
    END
RETURN p
```

### Mark pattern as failed
```cypher
MATCH (p:Pattern {name: $pattern_name})
SET p.confidence = p.confidence * 0.9,
    p.last_failed_at = datetime()
RETURN p
```

## Bootstrap Queries for CLAUDE.md

### Get ISAAC initialization
```cypher
// Get init command and dashboard pattern
MATCH (cmd:Pattern {name: 'isaac_init'})
MATCH (dash:Pattern {name: 'isaac_dashboard_init'})
RETURN {
  command: cmd.code,
  dashboard: dash.code
} as init_instructions
```

### Get complete workflow for a command
```cypher
MATCH (cmd:Pattern {name: 'isaac_begin_session'})
MATCH (steps:Pattern {category: 'isaac_workflow', command: '/isaac-begin-session'})
RETURN {
  command: cmd.description,
  steps: collect({
    sequence: steps.sequence,
    name: steps.name,
    code: steps.code
  })
} as workflow
```

### Get all patterns for a category
```cypher
MATCH (p:Pattern {category: $category})
RETURN collect({
  name: p.name,
  code: p.code,
  context: p.context
}) as patterns
```