# Coherence SDK Development Guide

This guide outlines the process for developing client SDKs for the Coherence API, using our locked-in API specifications as a foundation.

> **Note:** The Python and TypeScript SDKs have been fully implemented. See the [/sdk](/sdk) directory for the production-ready implementations and [SDK Release Notes](/docs/SDK_RELEASE_NOTES.md) for details about the current release.

## Overview

The Coherence API is designed with client SDK development in mind. Our approach to SDK development involves:

1. <PERSON><PERSON><PERSON> testing and documenting the API
2. Creating a locked-in OpenAPI specification
3. Using code generation to create consistent SDKs across languages
4. Adding language-specific enhancements where appropriate

## Prerequisites

Before developing an SDK, ensure you have:

1. Access to the Coherence API server (running or staged)
2. Familiarity with the API endpoints and behaviors
3. Testing resources provided in this repository
4. OpenAPI specification (either from the API or the summary version)

## Recommended SDK Development Process

### 1. Understand the API Design

Start by reviewing:
- API documentation
- OpenAPI specification
- Test flows and example requests/responses

Key API concepts to understand:
- Multi-tenant architecture
- Authentication via API keys
- Intent resolution flow (resolve → ask → continue)
- Asynchronous operations
- Admin operations

### 2. Choose an SDK Generation Approach

#### Option A: OpenAPI Generator (Recommended)

Use [OpenAPI Generator](https://openapi-generator.tech/) to create a base SDK:

```bash
# Generate a TypeScript client
openapi-generator-cli generate -i docs/openapi-summary.yaml -g typescript-fetch -o ./sdk/typescript

# Generate a Python client
openapi-generator-cli generate -i docs/openapi-summary.yaml -g python -o ./sdk/python
```

#### Option B: Manual SDK Development

For more control, develop the SDK manually:
- Create client classes for each endpoint group
- Implement authentication handling
- Add request/response models
- Implement error handling

### 3. Enhance the Generated SDK

Generated SDKs typically need enhancements:

1. **Authentication Improvements**:
   - Add API key management
   - Support for token refresh if needed

2. **Error Handling**:
   - Add specific error classes for different error types
   - Implement retry logic for transient errors

3. **Conversation Management**:
   - Helper methods for managing conversation state
   - Utilities for handling multi-turn interactions

4. **Type Safety** (language-specific):
   - Add more robust type definitions
   - Implement validation where appropriate

5. **Convenience Methods**:
   - Shortcuts for common operations
   - Fluent interfaces for chaining operations

### 4. SDK Testing

Create comprehensive tests for your SDK:

1. **Unit Tests**:
   - Test each method in isolation
   - Mock API responses for predictable testing

2. **Integration Tests**:
   - Test against the actual API
   - Follow the flows in the API testing guide

3. **Scenario Tests**:
   - Implement the common use cases from the API testing guide
   - Create more complex, realistic scenarios

### 5. Documentation

Document your SDK thoroughly:

1. **Installation Instructions**:
   - Package manager commands
   - Dependencies and requirements

2. **Quick Start Examples**:
   - Simple examples for common operations
   - Authentication setup

3. **API Reference**:
   - Document all classes, methods, and properties
   - Include parameter and return type information

4. **Tutorials**:
   - Step-by-step guides for common tasks
   - Complete examples with context

## SDK Feature Checklist

A complete SDK should include:

- [x] Authentication handling
- [x] Complete API coverage
- [x] Error handling and meaningful error messages
- [x] Conversation state management
- [x] Asynchronous operation support
- [x] Logging
- [x] Configurable timeouts and retries
- [x] Promise/async support (for applicable languages)
- [x] Type safety (for applicable languages)
- [x] Comprehensive documentation

## Example SDK Code Structure

Here's a recommended structure for a TypeScript SDK:

```typescript
// Client setup
const coherence = new CoherenceClient({
  baseUrl: 'https://api.example.com/v1',
  apiKey: 'your_api_key'
});

// Simple intent resolution
const response = await coherence.resolve({
  userId: '00000000-0000-0000-0000-000000000001',
  role: 'user',
  message: 'What's the weather in Chicago?'
});

// Handling different response types
switch (response.kind) {
  case 'reply':
    console.log(`Answer: ${response.text}`);
    break;
  case 'ask':
    console.log(`Question: ${response.question}`);
    // Continue the conversation
    const continueResponse = await coherence.continue({
      conversationId: response.conversationId,
      userId: '00000000-0000-0000-0000-000000000001',
      message: 'Tomorrow afternoon'
    });
    break;
  case 'async':
    // Poll for the result
    const statusResponse = await coherence.checkStatus(response.workflowId);
    break;
}

// Conversation management helper
const conversation = coherence.createConversation({
  userId: '00000000-0000-0000-0000-000000000001',
  role: 'user'
});

// Send multiple messages in the same conversation
await conversation.send('Hello');
await conversation.send('I need weather information');
```

## Language-Specific Considerations

### TypeScript/JavaScript
- Use promises and async/await
- Implement strong typings with TypeScript
- Consider supporting both Node.js and browser environments

### Python
- Use async/await with asyncio
- Implement context managers for resource management
- Follow PEP 8 style guidelines

### Java
- Use builder pattern for request construction
- Implement fluent interfaces
- Support both synchronous and asynchronous operations

### Go
- Use context for cancellation and timeouts
- Follow standard Go conventions and idioms
- Implement clear error handling

## Resources

- [OpenAPI Generator](https://openapi-generator.tech/)
- [API Testing Guide](/docs/api_testing_guide.md)
- [OpenAPI Specification](/docs/openapi-summary.yaml)
- [Postman Collection](/docs/postman_collection.json)