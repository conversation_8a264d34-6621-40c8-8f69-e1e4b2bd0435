# Docker Container Migration System

## Overview

The Coherence API container automatically runs database migrations on startup to ensure the database schema is always up-to-date with the application code.

## How It Works

### 1. Container Startup Process

When the API container starts (via `docker-compose up`), it executes the following sequence:

```bash
# From docker-compose.yml
command: >
  bash -c "
    # Wait for services
    sleep 10
    
    # Apply system fixes (including migrations)
    if [ "$APPLY_FIXES" = 'true' ]; then
      python -m scripts.apply_fixes
    fi
    
    # Start API server
    uvicorn src.coherence.main:app --host 0.0.0.0 --port 8000 --reload
  "
```

### 2. Migration Runner (`scripts/apply_fixes.py`)

The `apply_alembic_migration()` function handles migrations with the following steps:

1. **Ensure Enum Types**: Creates all PostgreSQL enum types if they don't exist
   - `validation_status`
   - `spec_format`
   - `auth_type`
   - `integration_status`
   - `template_category`

2. **Check Migration State**: Determines if database needs initialization

3. **Run Migrations**: Executes `alembic upgrade head`

4. **Error Handling**: 
   - Handles "table already exists" errors gracefully
   - Detects multiple migration heads
   - Allows API startup even if non-critical migration errors occur

### 3. Environment Variables

- `APPLY_FIXES=true`: Enables migration runner (set in docker-compose.yml)
- Database connection settings are passed via environment variables

## Monitoring Migrations

### Check Migration Status

```bash
# From host machine
docker-compose exec coherence-api alembic current

# Check migration history
docker-compose exec coherence-api alembic history
```

### View Migration Logs

```bash
# During startup
docker-compose logs coherence-api | grep -E "(migration|alembic)"

# Specific migration messages
docker-compose logs coherence-api | grep "Alembic migrations applied successfully"
```

## Troubleshooting

### Common Issues

1. **Migration Already Applied**
   - The system detects this and continues normally
   - Tables are stamped as current version

2. **Multiple Migration Heads**
   - Requires manual intervention
   - Run: `docker-compose exec coherence-api alembic merge -m "merge heads"`

3. **Failed Enum Creation**
   - Enum types use CREATE IF NOT EXISTS pattern
   - Duplicate errors are caught and ignored

### Manual Migration Commands

If you need to run migrations manually:

```bash
# Apply all pending migrations
docker-compose exec coherence-api alembic upgrade head

# Rollback one migration
docker-compose exec coherence-api alembic downgrade -1

# Create new migration
docker-compose exec coherence-api alembic revision --autogenerate -m "description"
```

## Best Practices

1. **Always Test Locally**: Run `docker-compose up` locally before deploying
2. **Check Migration Status**: Verify migrations applied correctly in logs
3. **Keep Models in Sync**: Ensure SQLAlchemy models match migration files
4. **Use Autogenerate**: Let Alembic detect schema changes automatically

## Production Deployment

For production environments:

1. Set `APPLY_FIXES=true` in production docker-compose
2. Monitor startup logs for migration success
3. Have rollback plan ready (database backups)
4. Test migrations in staging environment first

The migration system ensures zero-downtime deployments by applying schema changes automatically during container startup.