# Auth & Permission Standardization Migration Guide

This document outlines the changes made to standardize authentication and permission handling across the Coherence platform.

## Summary of Changes

1. **Canonical System Admin Flag**: JWT now includes a standardized `is_system_admin` boolean flag at the root level
2. **Permission Code Generator**: Python permissions are now automatically exported to TypeScript
3. **Wildcard Permission**: System admins now receive a `system:*` permission for simplified checks
4. **Frontend Permission Hook**: New `useHasPermission` hook provides centralized permission checking
5. **Dashboard Permission Renamed**: Changed from `tenant:view_own_dashboard` to `organization:view_own_dashboard`

## Breaking Changes

### JWT Claims Structure

The JWT claims structure has been standardized to consistently use:

```json
{
  "is_system_admin": boolean,
  "org_id": "org_xxx",
  "org_name": "Organization Name",
  "org_role": "org:admin",
  "org_slug": "org-slug"
}
```

**Migration Action**: Update your Clerk JWT template to include `is_system_admin` as a boolean based on the user's public metadata:

```json
{
  "is_system_admin": "{{user.public_metadata.isSystemAdmin}}"
}
```

### Permission String Changes

The permission for viewing organization dashboards has been renamed from `tenant:view_own_dashboard` to `organization:view_own_dashboard`.

**Migration Action**: Update any frontend code that directly references the old permission string:

```javascript
// Old code
if (permissions.includes('tenant:view_own_dashboard')) {
  // ...
}

// New code
if (permissions.includes('organization:view_own_dashboard')) {
  // ...
}
```

### Permission Import Location

Frontend components should now import permissions from the generated file instead of manually declaring them:

```typescript
// Old approach
import { CoherencePermission } from '@/lib/permissions';

// New approach
import { PERMISSIONS } from '@/lib/generated/permissions';
```

## New Features

### useHasPermission Hook

A new permission hook provides standardized permission checking across the application:

```typescript
import { useHasPermission } from '@/hooks/useHasPermission';

function MyComponent() {
  // Check a specific permission
  const canCreateTemplate = useHasPermission('template:create');
  
  // Also available for multiple permission checks
  const canManageTemplates = useHasAllPermissions([
    'template:create',
    'template:update',
    'template:delete'
  ]);
  
  // Or check if user has any of a set of permissions
  const canViewAnyReports = useHasAnyPermission([
    'report:view_usage',
    'report:view_analytics'
  ]);
  
  // ...
}
```

This hook automatically handles:
- System admin status granting all permissions
- The wildcard `system:*` permission granting all permissions
- Normal permission string matching

### Permission Code Generator

Permissions are now automatically generated from the backend enum to ensure consistency:

```bash
# Run this command to update the frontend permission file
python scripts/generate_permissions_ts.py
```

This generates:
- `apps/admin/lib/generated/permissions.ts` for use in TypeScript code
- `apps/admin/lib/generated/permissions.json` for unit testing

## Migration Checklist

1. **JWT Template**: Update Clerk JWT template to include `is_system_admin` boolean
2. **Permission References**: Replace any hardcoded permission strings
3. **Permission Checks**: Migrate direct `permissions.includes()` calls to `useHasPermission()`
4. **Test Protection**: Update tests to use the new permission structures
5. **Dashboard Permission**: Rename any references from `tenant:view_own_dashboard` to `organization:view_own_dashboard`

## CI Validation

The new system includes automated tests:
- Backend tests ensure system admins get the wildcard permission
- Frontend tests validate permission hook behavior
- E2E tests verify correct routing based on permissions
- A permissions sync test ensures frontend and backend permissions match