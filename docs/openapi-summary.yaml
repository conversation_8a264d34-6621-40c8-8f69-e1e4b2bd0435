openapi: 3.0.3
info:
  title: Coherence API
  description: |
    The Coherence API transforms natural language into structured actions through a multi-tiered intent recognition pipeline.
    This document provides a simplified overview of the main endpoints.
  version: 0.1.0
servers:
  - url: http://localhost:8001/v1
    description: Local development server
  - url: https://api.coherence.example/v1
    description: Example production server
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
  schemas:
    ResolveRequest:
      type: object
      required:
        - user_id
        - role
        - message
      properties:
        conversation_id:
          type: string
          format: uuid
          description: Unique identifier for the conversation session
        user_id:
          type: string
          format: uuid
          description: Identifier for the user sending the message
        role:
          type: string
          description: Role of the user (e.g., 'admin', 'user')
        message:
          type: string
          description: Natural language message from the user
        context:
          type: object
          additionalProperties: true
          description: Additional context for the conversation
    ContinueRequest:
      type: object
      required:
        - conversation_id
        - user_id
        - message
      properties:
        conversation_id:
          type: string
          format: uuid
          description: Identifier for the existing conversation to continue
        user_id:
          type: string
          format: uuid
          description: Identifier for the user sending the message
        message:
          type: string
          description: User's response to a previous question
        field:
          type: string
          description: The field this response is providing information for
    ResolveResponse:
      oneOf:
        - $ref: '#/components/schemas/ReplyResponse'
        - $ref: '#/components/schemas/AskResponse'
        - $ref: '#/components/schemas/AsyncResponse'
        - $ref: '#/components/schemas/FallbackResponse'
    ReplyResponse:
      type: object
      required:
        - kind
        - text
      properties:
        kind:
          type: string
          enum: ['reply']
        text:
          type: string
          description: Human-readable response to display to the user
        intent:
          type: string
          description: The resolved intent (if available)
        data:
          type: object
          additionalProperties: true
          description: Additional structured data related to the intent execution
    AskResponse:
      type: object
      required:
        - kind
        - field
        - question
      properties:
        kind:
          type: string
          enum: ['ask']
        field:
          type: string
          description: The name of the field that needs to be filled
        question:
          type: string
          description: Human-readable question to ask the user
        options:
          type: array
          items:
            type: object
            additionalProperties: true
          description: Optional list of possible values for the field
    AsyncResponse:
      type: object
      required:
        - kind
        - workflow_id
        - status_url
      properties:
        kind:
          type: string
          enum: ['async']
        workflow_id:
          type: string
          format: uuid
          description: Identifier for the async workflow
        status_url:
          type: string
          description: URL to check workflow status
    FallbackResponse:
      type: object
      required:
        - kind
      properties:
        kind:
          type: string
          enum: ['fallback']
        text:
          type: string
          default: "I'm not sure I understand. Could you rephrase that?"
          description: Human-readable fallback message
        reason:
          type: string
          description: Technical reason for the fallback (for debugging)
    TenantCreate:
      type: object
      required:
        - name
        - admin_email
      properties:
        name:
          type: string
          description: Name of the tenant organization
        industry_pack:
          type: string
          description: Industry-specific template pack
        admin_email:
          type: string
          format: email
          description: Email of the tenant admin user
    APIKeyCreate:
      type: object
      required:
        - tenant_id
      properties:
        tenant_id:
          type: string
          format: uuid
          description: Tenant this API key belongs to
        label:
          type: string
          description: Optional label for the API key
        expires_at:
          type: string
          format: date-time
          description: When this API key expires
paths:
  /resolve:
    post:
      summary: Resolve intent from natural language
      description: |
        Processes a natural language message to identify the user's intent and take appropriate action.
      operationId: resolveIntent
      security:
        - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResolveRequest'
      responses:
        '200':
          description: Successfully processed the message
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResolveResponse'
        '401':
          description: Invalid or missing API key
  /continue:
    post:
      summary: Continue a conversation with additional information
      description: |
        Continues a conversation by providing additional information requested in a previous 'ask' response.
      operationId: continueConversation
      security:
        - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContinueRequest'
      responses:
        '200':
          description: Successfully processed the continuation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResolveResponse'
        '401':
          description: Invalid or missing API key
        '404':
          description: Conversation not found
  /status/{workflow_id}:
    get:
      summary: Check status of an async workflow
      description: |
        Checks the status of an asynchronous workflow that was initiated by a previous request.
      operationId: checkWorkflowStatus
      security:
        - ApiKeyAuth: []
      parameters:
        - name: workflow_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Workflow status information
          content:
            application/json:
              schema:
                type: object
                properties:
                  workflow_id:
                    type: string
                    format: uuid
                  status:
                    type: string
                    enum: [running, completed, failed]
                  progress:
                    type: number
                    format: float
                    minimum: 0
                    maximum: 1
                  current_step:
                    type: string
        '401':
          description: Invalid or missing API key
        '404':
          description: Workflow not found
  /admin/tenants:
    get:
      summary: List all tenants
      description: |
        Lists all tenants in the system. Only accessible to system administrators.
      operationId: listTenants
      security:
        - ApiKeyAuth: []
      responses:
        '200':
          description: List of tenants
        '401':
          description: Invalid or missing API key
        '403':
          description: Insufficient permissions
    post:
      summary: Create a new tenant
      description: |
        Creates a new tenant with an initial API key. Only accessible to system administrators.
      operationId: createTenant
      security:
        - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TenantCreate'
      responses:
        '201':
          description: Tenant created successfully
        '401':
          description: Invalid or missing API key
        '403':
          description: Insufficient permissions
        '409':
          description: Tenant with this name already exists
  /admin/tenants/{tenant_id}/api-keys:
    post:
      summary: Create a new API key for a tenant
      description: |
        Creates a new API key for the specified tenant.
      operationId: createApiKey
      security:
        - ApiKeyAuth: []
      parameters:
        - name: tenant_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIKeyCreate'
      responses:
        '201':
          description: API key created successfully
        '401':
          description: Invalid or missing API key
        '403':
          description: Insufficient permissions
        '404':
          description: Tenant not found
security:
  - ApiKeyAuth: []
tags:
  - name: resolve
    description: Intent resolution endpoints
  - name: admin
    description: Administrative endpoints
  - name: templates
    description: Template management endpoints
  - name: integrations
    description: OpenAPI integration endpoints