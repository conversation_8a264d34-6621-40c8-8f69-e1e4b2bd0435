# Coherence SDK Release Notes

## v1.0.0-rc1 (May 2025)

### First Release Candidate

This is the first release candidate of the Coherence SDK, providing a comprehensive client for interacting with the Coherence AI middleware. The SDK is available for both Python and TypeScript, offering type-safe access to all Coherence API functionalities.

### Features

- **Complete API Coverage**: Full support for all Coherence API endpoints
- **Type Safety**: Comprehensive typing for better developer experience
- **Async Support**: Native async/await patterns for efficient I/O
- **Multi-Tenant Isolation**: Built-in tenant context handling
- **Error Handling**: Structured error handling with detailed error information
- **Authentication**: Simple API key authentication
- **Documentation**: Comprehensive inline documentation and examples
- **Resilience Patterns**: Built-in retry mechanisms and circuit breakers
- **Fail-Fast Behavior**: Improved error handling with proper HTTP status codes
- **OpenAI-Optimized Retry**: Exponential backoff with jitter for optimal API performance

### Python SDK

```python
pip install coherence-sdk==2025.05
```

#### Example Usage

```python
import asyncio
from coherence_sdk import CoherenceClient

async def main():
    # Initialize the client with your API key and tenant ID
    client = CoherenceClient(
        api_key="your_api_key",
        tenant_id="your_tenant_id",
        base_url="https://api.coherence.ai"
    )
    
    # Resolve natural language to an intent
    response = await client.resolve(
        message="What's the weather like in San Francisco?"
    )
    
    print(f"Intent: {response.intent}")
    print(f"Parameters: {response.parameters}")

if __name__ == "__main__":
    asyncio.run(main())
```

### TypeScript SDK

```bash
npm install @coherence/sdk
# or
yarn add @coherence/sdk
```

#### Example Usage

```typescript
import { CoherenceClient, resolveResolve } from '@coherence/sdk';

// Initialize the client with your API key and tenant ID
const client = new CoherenceClient({
  apiKey: 'your_api_key',
  tenantId: 'your_tenant_id',
  baseURL: 'https://api.coherence.ai'
});

// Resolve natural language to an intent
async function main() {
  try {
    const response = await resolveResolve(
      {
        message: "What's the weather like in San Francisco?"
      }, 
      client.getInstance()
    );
    
    console.log(`Intent: ${response.intent}`);
    console.log(`Parameters: ${JSON.stringify(response.parameters)}`);
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
```

### Requirements

- Python 3.10 or later
- Node.js 16 or later (for TypeScript SDK)

### Known Limitations

- This is a release candidate and may contain bugs
- Some advanced features may require additional configuration
- Performance optimizations are ongoing

### Upcoming Features

- Additional authentication methods
- Improved caching mechanisms
- Streaming response support
- CLI tools for common operations

### Feedback and Contributions

We welcome feedback and contributions to the SDK. Please submit issues and pull requests to the [GitHub repository](https://github.com/coherence-ai/coherence).