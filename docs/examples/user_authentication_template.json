{"meta": {"key": "user_auth_enhanced", "endpoint_id": "POST_/v1/auth/login", "version": 1, "tags": ["authentication", "security", "user", "enhanced"], "created_at": "2025-01-02T18:00:00Z", "updated_at": "2025-01-02T18:00:00Z"}, "intent": {"patterns": ["login with {email} and {password}", "authenticate user {email}", "sign in as {email}", "log me in with {email}", "access account {email}", "user login {email}", "authenticate {email} {password}"], "confidence_threshold": 0.85, "parameter_hints": {"email": ["email", "username", "user", "account"], "password": ["password", "pass", "secret", "pwd"]}, "security_level": "high", "fallback_templates": ["forgot_password", "create_account"]}, "action": {"method": "POST", "path": "/v1/auth/login", "integration": {"base_url": "${AUTH_API_URL:https://api.auth.example.com}", "api_version": "v1", "credential_ref": "auth_api_key", "health_check": {"endpoint": "/v1/health", "interval": 60, "timeout": 5, "expected_status": 200}}, "headers": {"Content-Type": "application/json", "Accept": "application/json", "X-API-Key": "{{credentials.auth_api_key}}", "X-Request-ID": "{{generate_uuid()}}", "X-Client-Version": "1.0.0"}, "body": {"email": "{{parameters.email}}", "password": "{{parameters.password}}", "remember_me": "{{parameters.remember_me|default(false)}}", "device_info": {"user_agent": "{{headers.user_agent}}", "ip_address": "{{request.ip}}", "device_id": "{{parameters.device_id}}"}}, "validation_rules": {"email": {"type": "string", "required": true, "format": "email", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "max_length": 255}, "password": {"type": "string", "required": true, "min_length": 8, "max_length": 128, "sensitive": true}, "remember_me": {"type": "boolean", "required": false, "default": false}, "device_id": {"type": "string", "required": false, "pattern": "^[a-zA-Z0-9-]{36}$"}}, "transformations": {"email": ["trim", "lowercase"], "password": ["no_transform"], "device_id": ["trim"]}, "security": {"rate_limit": {"attempts": 5, "window": "15m", "lockout_duration": "30m"}, "encryption": {"in_transit": "TLS 1.3", "at_rest": "AES-256-GCM"}, "audit": {"log_success": true, "log_failure": true, "exclude_fields": ["password"]}}, "retries": {"max_attempts": 1, "timeout": 10, "retry_on": []}}, "response": {"crfs": {"type": "structured", "auto_select": true, "default_format": "structured", "formats": {"structured": {"sections": [{"type": "text", "style": "heading", "content": "🔐 Login Successful"}, {"type": "text", "style": "success", "content": "Welcome back, {{result.user.name}}!"}, {"type": "keyvalue", "style": "info", "data": {"User ID": "{{result.user.id}}", "Email": "{{result.user.email}}", "Last Login": "{{result.user.last_login|timestamp_to_relative}}", "Session Expires": "{{result.expires_at|timestamp_to_duration}}"}}, {"type": "divider"}, {"type": "action", "actions": [{"label": "Go to Dashboard", "url": "{{result.redirect_url|default('/dashboard')}}", "style": "primary"}, {"label": "Update Profile", "action": "user_profile_update", "style": "secondary"}, {"label": "Security Settings", "action": "security_settings", "style": "link"}]}, {"type": "alert", "condition": "{{result.warnings}}", "style": "warning", "content": "{{result.warnings[0].message}}"}]}, "text/plain": {"template": "Login successful. Welcome back, {{result.user.name}}!\nSession token: {{result.token}}\nExpires: {{result.expires_at}}"}, "application/json": {"raw": false, "transform": {"success": true, "user_id": "{{result.user.id}}", "token": "{{result.token}}", "expires_at": "{{result.expires_at}}", "redirect_url": "{{result.redirect_url}}"}}}}, "error_mapping": {"400": "Invalid request. Please check your input.", "401": "Invalid email or password.", "403": "Account locked due to too many failed attempts. Please try again in {{result.lockout_minutes}} minutes.", "404": "User account not found.", "409": "Account already logged in from another device.", "422": "{{result.errors[0].field}}: {{result.errors[0].message}}", "429": "Too many login attempts. Please try again later.", "500": "Authentication service error. Please try again.", "503": "Authentication service temporarily unavailable."}, "success_indicator": {"status_codes": [200], "required_fields": ["token", "user", "expires_at"], "forbidden_fields": ["password"]}}, "test_data": {"mock_responses": {"success": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "refresh_token": "rf_1234567890abcdef", "user": {"id": "usr_123456789", "email": "<EMAIL>", "name": "<PERSON>", "role": "user", "last_login": "2025-01-02T17:00:00Z", "created_at": "2024-01-01T00:00:00Z"}, "expires_at": "2025-01-02T19:00:00Z", "redirect_url": "/dashboard", "session_id": "sess_abc123def456"}, "success_with_warning": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "user": {"id": "usr_987654321", "email": "<EMAIL>", "name": "<PERSON>"}, "expires_at": "2025-01-02T19:00:00Z", "warnings": [{"code": "WEAK_PASSWORD", "message": "Your password is weak. Consider updating it for better security."}]}, "invalid_credentials": {"error": {"code": "INVALID_CREDENTIALS", "message": "Invalid email or password"}}, "account_locked": {"error": {"code": "ACCOUNT_LOCKED", "message": "Account locked due to too many failed attempts", "lockout_minutes": 30, "attempts_remaining": 0}}, "mfa_required": {"requires_mfa": true, "mfa_token": "mfa_temp_token_123", "mfa_methods": ["totp", "sms"], "expires_at": "2025-01-02T18:05:00Z"}}, "sample_parameters": [{"name": "standard", "values": {"email": "<EMAIL>", "password": "SecurePassword123!", "remember_me": false}}, {"name": "with_device", "values": {"email": "<EMAIL>", "password": "AnotherSecure123!", "remember_me": true, "device_id": "550e8400-e29b-41d4-a716-************"}}, {"name": "minimal", "values": {"email": "<EMAIL>", "password": "Password123!"}}], "error_scenarios": [{"name": "missing_email", "parameters": {"password": "Password123!"}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Missing required parameter: email"}}, {"name": "invalid_email_format", "parameters": {"email": "not-an-email", "password": "Password123!"}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Invalid email format"}}, {"name": "short_password", "parameters": {"email": "<EMAIL>", "password": "short"}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Password must be at least 8 characters"}}], "test_accounts": {"valid_user": {"email": "<EMAIL>", "password": "TestPassword123!"}, "locked_user": {"email": "<EMAIL>", "password": "any"}, "mfa_user": {"email": "<EMAIL>", "password": "MfaPassword123!"}, "admin_user": {"email": "<EMAIL>", "password": "AdminPassword123!"}}, "performance_data": {"response_times": {"p50": 200, "p90": 400, "p95": 600, "p99": 1000}, "throughput": {"requests_per_second": 1000, "concurrent_users": 5000}}}, "documentation": {"description": "Authenticates a user with email and password, returning a JWT token for API access.", "examples": [{"description": "Standard login", "parameters": {"email": "<EMAIL>", "password": "SecurePassword123!"}}, {"description": "Login with remember me", "parameters": {"email": "<EMAIL>", "password": "SecurePassword123!", "remember_me": true}}], "notes": ["Passwords are never stored or logged", "Account locks after 5 failed attempts within 15 minutes", "Token expires after 2 hours by default", "Remember me extends token lifetime to 30 days", "MFA may be required for certain accounts"], "security_considerations": ["Always use HTTPS in production", "Implement CSRF protection", "Store tokens securely (httpOnly cookies recommended)", "Monitor for suspicious login patterns", "Implement device fingerprinting for enhanced security"]}}