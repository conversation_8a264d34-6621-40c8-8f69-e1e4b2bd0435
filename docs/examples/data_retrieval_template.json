{"meta": {"key": "user_list_enhanced", "endpoint_id": "GET_/v1/users", "version": 1, "tags": ["users", "data", "retrieval", "enhanced", "pagination"], "created_at": "2025-01-02T18:00:00Z", "updated_at": "2025-01-02T18:00:00Z"}, "intent": {"patterns": ["list all users", "show users", "get users with {filter}", "find users where {field} is {value}", "search users by {search_term}", "show {limit} users", "get users page {page}", "list active users", "show users sorted by {sort_field}", "find users created {date_range}"], "confidence_threshold": 0.8, "parameter_hints": {"filter": ["active", "inactive", "verified", "unverified"], "field": ["name", "email", "role", "status"], "sort_field": ["name", "created_at", "updated_at", "email"], "limit": ["10", "20", "50", "100"], "page": ["1", "2", "3", "next", "previous"]}, "fallback_templates": ["user_search_help"]}, "action": {"method": "GET", "path": "/v1/users", "integration": {"base_url": "${API_BASE_URL:https://api.example.com}", "api_version": "v1", "credential_ref": "api_key", "health_check": {"endpoint": "/v1/health", "interval": 300, "timeout": 10, "expected_status": 200}, "cache": {"enabled": true, "ttl": 60, "vary_by": ["filter", "sort", "page", "limit"]}}, "authentication": {"type": "bearer", "header": "Authorization", "value": "Bearer {{credentials.api_key}}"}, "headers": {"Content-Type": "application/json", "Accept": "application/json", "X-API-Version": "1.0"}, "query_parameters": {"page": "{{parameters.page|default(1)}}", "limit": "{{parameters.limit|default(20)}}", "sort": "{{parameters.sort_field|default('created_at')}}", "order": "{{parameters.sort_order|default('desc')}}", "filter[status]": "{{parameters.status}}", "filter[role]": "{{parameters.role}}", "search": "{{parameters.search_term}}", "created_after": "{{parameters.created_after}}", "created_before": "{{parameters.created_before}}", "fields": "{{parameters.fields|join(',')}}"}, "validation_rules": {"page": {"type": "integer", "minimum": 1, "maximum": 10000, "required": false}, "limit": {"type": "integer", "minimum": 1, "maximum": 100, "required": false}, "sort_field": {"type": "string", "enum": ["id", "name", "email", "created_at", "updated_at", "last_login"], "required": false}, "sort_order": {"type": "string", "enum": ["asc", "desc"], "required": false}, "status": {"type": "string", "enum": ["active", "inactive", "pending", "suspended"], "required": false}, "role": {"type": "string", "enum": ["admin", "user", "moderator", "guest"], "required": false}, "search_term": {"type": "string", "min_length": 2, "max_length": 100, "required": false}, "created_after": {"type": "string", "format": "date-time", "required": false}, "created_before": {"type": "string", "format": "date-time", "required": false}, "fields": {"type": "array", "items": {"type": "string", "enum": ["id", "name", "email", "role", "status", "created_at", "updated_at", "avatar", "last_login"]}, "required": false}}, "transformations": {"search_term": ["trim", "lowercase"], "sort_field": ["trim", "lowercase"], "created_after": ["to_iso8601"], "created_before": ["to_iso8601"]}, "retries": {"max_attempts": 3, "backoff": "exponential", "timeout": 30, "retry_on": [502, 503, 504]}}, "response": {"crfs": {"type": "structured", "auto_select": true, "default_format": "structured", "formats": {"structured": {"sections": [{"type": "text", "style": "heading", "content": "👥 Users ({{result.meta.total}})"}, {"type": "text", "style": "subheading", "content": "Showing {{result.meta.from}}-{{result.meta.to}} of {{result.meta.total}} users"}, {"type": "table", "columns": [{"key": "id", "label": "ID", "width": "10%"}, {"key": "name", "label": "Name", "width": "25%", "sortable": true}, {"key": "email", "label": "Email", "width": "25%", "sortable": true}, {"key": "role", "label": "Role", "width": "15%", "format": "badge"}, {"key": "status", "label": "Status", "width": "15%", "format": "status"}, {"key": "created_at", "label": "Created", "width": "10%", "format": "relative_time"}], "data": "{{result.data}}", "actions": [{"label": "View", "action": "user_view", "parameters": {"user_id": "{{row.id}}"}}, {"label": "Edit", "action": "user_edit", "parameters": {"user_id": "{{row.id}}"}}]}, {"type": "divider"}, {"type": "pagination", "current_page": "{{result.meta.current_page}}", "total_pages": "{{result.meta.last_page}}", "per_page": "{{result.meta.per_page}}", "actions": {"first": "user_list", "previous": "user_list", "next": "user_list", "last": "user_list"}}, {"type": "action", "actions": [{"label": "Export CSV", "action": "user_export", "parameters": {"format": "csv", "filters": "{{parameters}}"}, "style": "secondary"}, {"label": "Add New User", "action": "user_create", "style": "primary"}]}]}, "text/plain": {"template": "Users List ({{result.meta.total}} total):\n\n{{#each result.data}}{{@index+1}}. {{this.name}} ({{this.email}}) - {{this.role}} - {{this.status}}\n{{/each}}\n\nPage {{result.meta.current_page}} of {{result.meta.last_page}}"}, "text/csv": {"template": "ID,Name,Email,Role,Status,Created At\n{{#each result.data}}{{this.id}},\"{{this.name}}\",{{this.email}},{{this.role}},{{this.status}},{{this.created_at}}\n{{/each}}"}, "application/json": {"raw": true}}}, "error_mapping": {"400": "Invalid request parameters: {{result.errors|join(', ')}}", "401": "Authentication required. Please login.", "403": "You don't have permission to view users.", "422": "Invalid filter values: {{result.errors[0].message}}", "429": "Rate limit exceeded. Please try again in {{result.retry_after}} seconds.", "500": "Server error while retrieving users.", "503": "User service is temporarily unavailable."}, "success_indicator": {"status_codes": [200], "required_fields": ["data", "meta"], "array_fields": ["data"]}}, "test_data": {"mock_responses": {"success": {"data": [{"id": "usr_001", "name": "<PERSON>", "email": "<EMAIL>", "role": "admin", "status": "active", "created_at": "2024-01-15T10:30:00Z", "updated_at": "2025-01-02T15:45:00Z", "last_login": "2025-01-02T17:00:00Z"}, {"id": "usr_002", "name": "<PERSON>", "email": "<EMAIL>", "role": "user", "status": "active", "created_at": "2024-02-20T14:20:00Z", "updated_at": "2025-01-01T09:30:00Z", "last_login": "2025-01-01T12:00:00Z"}, {"id": "usr_003", "name": "<PERSON>", "email": "<EMAIL>", "role": "moderator", "status": "inactive", "created_at": "2024-03-10T08:15:00Z", "updated_at": "2024-12-20T16:00:00Z", "last_login": "2024-12-15T10:00:00Z"}], "meta": {"current_page": 1, "from": 1, "to": 3, "per_page": 20, "last_page": 5, "total": 87, "path": "/v1/users"}, "links": {"first": "/v1/users?page=1", "last": "/v1/users?page=5", "prev": null, "next": "/v1/users?page=2"}}, "empty": {"data": [], "meta": {"current_page": 1, "from": null, "to": null, "per_page": 20, "last_page": 1, "total": 0}}, "filtered": {"data": [{"id": "usr_010", "name": "Admin User", "email": "<EMAIL>", "role": "admin", "status": "active", "created_at": "2024-01-01T00:00:00Z"}], "meta": {"current_page": 1, "total": 1, "filters_applied": ["role:admin", "status:active"]}}}, "sample_parameters": [{"name": "standard", "values": {"page": 1, "limit": 20}}, {"name": "filtered", "values": {"page": 1, "limit": 10, "status": "active", "role": "admin"}}, {"name": "search", "values": {"search_term": "john", "limit": 50}}, {"name": "sorted", "values": {"sort_field": "created_at", "sort_order": "desc", "limit": 20}}, {"name": "date_range", "values": {"created_after": "2024-01-01T00:00:00Z", "created_before": "2024-12-31T23:59:59Z"}}, {"name": "partial_fields", "values": {"fields": ["id", "name", "email"], "limit": 100}}], "error_scenarios": [{"name": "invalid_page", "parameters": {"page": -1}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Page must be greater than 0"}}, {"name": "invalid_sort", "parameters": {"sort_field": "invalid_field"}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Invalid sort field"}}, {"name": "excessive_limit", "parameters": {"limit": 1000}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Limit cannot exceed 100"}}], "performance_data": {"response_times": {"p50": 100, "p90": 250, "p95": 400, "p99": 800}, "throughput": {"requests_per_second": 500, "concurrent_users": 1000}, "cache": {"hit_rate": 0.85, "ttl": 60}}}, "documentation": {"description": "Retrieves a paginated list of users with support for filtering, sorting, and field selection.", "examples": [{"description": "Get first page of users", "parameters": {}}, {"description": "Get active admin users", "parameters": {"status": "active", "role": "admin"}}, {"description": "Search users by name", "parameters": {"search_term": "john", "limit": 10}}, {"description": "Get users created in 2024", "parameters": {"created_after": "2024-01-01T00:00:00Z", "created_before": "2024-12-31T23:59:59Z", "sort_field": "created_at", "sort_order": "asc"}}], "notes": ["Default page size is 20 items", "Maximum page size is 100 items", "Search is performed on name and email fields", "Results are cached for 60 seconds", "Date filters use ISO 8601 format", "Field selection can reduce response size"], "api_reference": "https://api.example.com/docs/users#list"}}