{"meta": {"key": "payment_process_enhanced", "endpoint_id": "POST_/v1/payments", "version": 1, "tags": ["payment", "transaction", "enhanced", "pci"], "created_at": "2025-01-02T18:00:00Z", "updated_at": "2025-01-02T18:00:00Z"}, "intent": {"patterns": ["process payment of {amount} {currency}", "charge {amount} to {card_type}", "make a payment for {amount}", "pay {amount} using {payment_method}", "complete transaction of {amount} {currency}", "charge customer {amount}", "process {amount} payment", "debit {amount} from card"], "confidence_threshold": 0.9, "parameter_hints": {"amount": ["amount", "price", "total", "sum", "charge"], "currency": ["USD", "EUR", "GBP", "currency"], "card_type": ["visa", "mastercard", "amex", "card"], "payment_method": ["card", "credit", "debit"]}, "fallback_templates": ["payment_help", "transaction_status"]}, "action": {"method": "POST", "path": "/v1/payments", "integration": {"base_url": "${PAYMENT_API_URL:https://api.stripe.com}", "api_version": "v1", "credential_ref": "stripe_secret_key", "health_check": {"endpoint": "/v1/health", "interval": 60, "timeout": 5, "expected_status": 200}, "pci_compliance": {"level": "PCI-DSS-1", "tokenization": true, "encryption": "AES-256"}}, "authentication": {"type": "bearer", "header": "Authorization", "value": "Bearer {{credentials.stripe_secret_key}}"}, "headers": {"Content-Type": "application/json", "Accept": "application/json", "Stripe-Version": "2023-10-16", "Idempotency-Key": "{{generate_uuid()}}"}, "body": {"amount": "{{parameters.amount_cents}}", "currency": "{{parameters.currency|default('usd')|lowercase}}", "payment_method": "{{parameters.payment_method_id}}", "description": "{{parameters.description|default('Payment')}}", "metadata": {"order_id": "{{parameters.order_id}}", "customer_id": "{{parameters.customer_id}}"}, "confirm": true, "return_url": "{{parameters.return_url|default('https://example.com/return')}}"}, "validation_rules": {"amount": {"type": "number", "required": true, "minimum": 0.01, "maximum": 999999.99, "description": "Amount in major currency units"}, "currency": {"type": "string", "required": false, "enum": ["USD", "EUR", "GBP", "CAD", "AUD", "JPY"], "pattern": "^[A-Z]{3}$"}, "payment_method_id": {"type": "string", "required": true, "pattern": "^pm_[a-zA-Z0-9]{24}$"}, "customer_id": {"type": "string", "required": false, "pattern": "^cus_[a-zA-Z0-9]{14,}$"}, "order_id": {"type": "string", "required": false, "max_length": 100}}, "transformations": {"amount": ["to_cents"], "currency": ["trim", "uppercase"], "description": ["trim", "sanitize_html"]}, "retries": {"max_attempts": 2, "backoff": "exponential", "timeout": 30, "retry_on": [502, 503], "idempotent": true}}, "response": {"crfs": {"type": "structured", "auto_select": true, "default_format": "structured", "formats": {"structured": {"sections": [{"type": "text", "style": "heading", "content": "✅ Payment Processed Successfully"}, {"type": "keyvalue", "style": "highlight", "data": {"Transaction ID": "{{result.id}}", "Amount": "{{parameters.currency|default('USD')}} {{parameters.amount|format_currency}}", "Status": "{{result.status|capitalize}}"}}, {"type": "divider"}, {"type": "keyvalue", "data": {"Payment Method": "{{result.payment_method_types[0]|humanize}}", "Processing Time": "{{result.created|timestamp_to_relative}}", "Receipt": "{{result.receipt_url}}"}}, {"type": "action", "actions": [{"label": "View Receipt", "url": "{{result.receipt_url}}", "style": "primary"}, {"label": "Refund Payment", "action": "payment_refund", "parameters": {"payment_id": "{{result.id}}"}}]}]}, "text/plain": {"template": "Payment of {{parameters.currency|default('USD')}} {{parameters.amount}} processed successfully.\nTransaction ID: {{result.id}}\nStatus: {{result.status}}\nReceipt: {{result.receipt_url}}"}, "application/json": {"raw": false, "transform": {"transaction_id": "{{result.id}}", "amount": "{{parameters.amount}}", "currency": "{{parameters.currency}}", "status": "{{result.status}}", "receipt_url": "{{result.receipt_url}}"}}}}, "error_mapping": {"400": "Invalid payment details. Please check your input.", "401": "Authentication failed. Invalid API credentials.", "402": "Payment failed: {{result.error.message}}", "403": "Payment method not allowed for this merchant.", "404": "Payment method '{{parameters.payment_method_id}}' not found.", "409": "Duplicate payment detected. Transaction may have already been processed.", "422": "{{result.error.param}}: {{result.error.message}}", "429": "Too many requests. Please try again later.", "500": "Payment processor error. Please try again."}, "success_indicator": {"status_codes": [200, 201], "required_fields": ["id", "status", "amount"], "success_values": {"status": ["succeeded", "processing"]}}}, "test_data": {"mock_responses": {"success": {"id": "pi_3MQvyqLkdIwHu7ix0h4Y9eO3", "object": "payment_intent", "amount": 2000, "currency": "usd", "status": "succeeded", "charges": {"object": "list", "data": [{"id": "ch_3MQvyqLkdIwHu7ix0N5zPWSj", "amount": 2000, "currency": "usd", "paid": true}]}, "created": 1679589766, "payment_method_types": ["card"], "receipt_url": "https://pay.stripe.com/receipts/payment/test_receipt", "metadata": {"order_id": "ORD-12345", "customer_id": "cus_NffrFeUfNV2Hib"}}, "processing": {"id": "pi_3MQvyqLkdIwHu7ix0h4Y9eO4", "status": "processing", "amount": 5000, "currency": "eur"}, "declined": {"error": {"code": "card_declined", "decline_code": "generic_decline", "message": "Your card was declined.", "param": "payment_method", "type": "card_error"}}, "insufficient_funds": {"error": {"code": "card_declined", "decline_code": "insufficient_funds", "message": "Your card has insufficient funds.", "type": "card_error"}}}, "sample_parameters": [{"name": "standard", "values": {"amount": 20.0, "currency": "USD", "payment_method_id": "pm_1MQvyqLkdIwHu7ixgEyTEST1", "description": "Order #12345", "order_id": "ORD-12345", "customer_id": "cus_NffrFeUfNV2Hib"}}, {"name": "minimal", "values": {"amount": 10.5, "payment_method_id": "pm_1MQvyqLkdIwHu7ixgEyTEST2"}}, {"name": "international", "values": {"amount": 100.0, "currency": "EUR", "payment_method_id": "pm_1MQvyqLkdIwHu7ixgEyTEST3", "description": "International payment"}}], "error_scenarios": [{"name": "missing_amount", "parameters": {"payment_method_id": "pm_test"}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Missing required parameter: amount"}}, {"name": "invalid_currency", "parameters": {"amount": 10, "currency": "XXX", "payment_method_id": "pm_test"}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Invalid currency code"}}, {"name": "card_declined", "parameters": {"amount": 10, "payment_method_id": "pm_card_declined"}, "expected_error": {"status": 402, "code": "card_declined", "message": "Your card was declined"}}], "test_cards": {"visa": "pm_card_visa", "visa_debit": "pm_card_visa_debit", "mastercard": "pm_card_mastercard", "amex": "pm_card_amex", "declined": "pm_card_declined", "insufficient_funds": "pm_card_insufficient_funds", "expired": "pm_card_expired"}, "performance_data": {"response_times": {"p50": 800, "p90": 1500, "p95": 2000, "p99": 3000}, "throughput": {"requests_per_second": 50, "concurrent_transactions": 100}, "rate_limits": {"requests_per_second": 100, "burst_limit": 200}}}, "compliance": {"pci_dss": {"level": 1, "scope": "payment_processing", "requirements": ["Never store card details", "Use tokenization for card data", "Encrypt all transmissions", "Audit all payment operations"]}, "regulations": ["PSD2", "GDPR"], "data_retention": {"transaction_data": "7 years", "card_tokens": "until_expiry", "audit_logs": "3 years"}}, "documentation": {"description": "Process secure payments using tokenized card information. Supports multiple currencies and payment methods.", "examples": [{"description": "Process a $20 USD payment", "parameters": {"amount": 20.0, "currency": "USD", "payment_method_id": "pm_1234567890abcdef"}}, {"description": "Process a €50 EUR payment with metadata", "parameters": {"amount": 50.0, "currency": "EUR", "payment_method_id": "pm_abcdef1234567890", "order_id": "ORD-98765", "description": "Premium subscription"}}], "notes": ["Amount should be in major currency units (dollars, euros, etc.)", "Amount is automatically converted to cents for processing", "Use test payment method IDs in development", "Idempotency key prevents duplicate charges", "All payments are confirmed automatically"], "api_reference": "https://stripe.com/docs/api/payment_intents"}}