{"meta": {"key": "weather_forecast_enhanced", "endpoint_id": "GET_/v1/forecast", "version": 2, "tags": ["weather", "forecast", "enhanced"], "created_at": "2025-01-04T20:00:00Z", "updated_at": "2025-01-04T21:00:00Z"}, "intent": {"patterns": ["weather forecast for {location}", "what's the weather in {city}", "forecast in {place}", "show me weather for {location}", "get forecast {city}", "weather {location} forecast", "temperature in {place}", "will it rain in {location}", "weather conditions {city}", "forecast for tomorrow in {location}"], "confidence_threshold": 0.85, "parameter_hints": {"location": ["city", "place", "location", "area", "region"], "units": ["metric", "imperial", "celsius", "fahrenheit"]}, "fallback_templates": ["general_weather_help"]}, "action": {"method": "GET", "path": "/v1/forecast", "integration": {"base_url": "${WEATHER_API_URL:https://api.openweathermap.org}", "api_version": "v1", "credential_ref": "openweather_api_key", "health_check": {"endpoint": "/health", "interval": 300, "timeout": 10, "expected_status": 200}}, "authentication": {"type": "api_key", "header": "X-API-Key", "value": "{{credentials.openweather_api_key}}"}, "headers": {"Content-Type": "application/json", "Accept": "application/json", "User-Agent": "Coherence/1.0"}, "parameter_mapping": {"q": "{{parameters.location}}", "units": "{{parameters.units|default('metric')}}", "lang": "{{parameters.language|default('en')}}", "cnt": "{{parameters.days|default(5)}}"}, "validation_rules": {"location": {"type": "string", "required": true, "min_length": 1, "max_length": 100, "pattern": "^[A-Za-z\\s,\\-]+$"}, "units": {"type": "string", "enum": ["metric", "imperial", "standard"], "required": false}, "days": {"type": "integer", "minimum": 1, "maximum": 16, "required": false}, "language": {"type": "string", "pattern": "^[a-z]{2}$", "required": false}}, "transformations": {"location": ["trim", "lowercase"], "units": ["trim", "lowercase"], "language": ["trim", "lowercase"]}, "retries": {"max_attempts": 3, "backoff": "exponential", "timeout": 30, "retry_on": [502, 503, 504]}}, "response": {"crfs": {"type": "structured", "auto_select": true, "default_format": "structured", "formats": {"structured": {"sections": [{"type": "text", "style": "heading", "content": "🌤️ Weather Forecast for {{parameters.location|capitalize}}"}, {"type": "text", "style": "subheading", "content": "{{result.cnt}}-Day Forecast"}, {"type": "list", "style": "bullet", "items": "{{result.list}}", "template": "{{item.dt_txt}}: {{item.main.temp}}°{{#if parameters.units == 'imperial'}}F{{else}}C{{/if}}, {{item.weather[0].description}} ({{item.main.humidity}}% humidity)"}, {"type": "divider"}, {"type": "keyvalue", "data": {"City": "{{result.city.name}}, {{result.city.country}}", "Coordinates": "{{result.city.coord.lat}}, {{result.city.coord.lon}}", "Timezone": "{{result.city.timezone}}"}}]}, "text/plain": {"template": "Weather forecast for {{parameters.location|capitalize}}:\n\n{{#each result.list}}{{this.dt_txt}}: {{this.main.temp}}°, {{this.weather[0].description}}\n{{/each}}\n\nLocation: {{result.city.name}}, {{result.city.country}}"}, "text/markdown": {"template": "# Weather Forecast for {{parameters.location|capitalize}}\n\n## {{result.cnt}}-Day Forecast\n\n{{#each result.list}}- **{{this.dt_txt}}**: {{this.main.temp}}°{{#if ../parameters.units == 'imperial'}}F{{else}}C{{/if}}, {{this.weather[0].description}}\n  - Humidity: {{this.main.humidity}}%\n  - Wind: {{this.wind.speed}} {{#if ../parameters.units == 'imperial'}}mph{{else}}m/s{{/if}}\n{{/each}}\n\n---\n\n*Location: {{result.city.name}}, {{result.city.country}} ({{result.city.coord.lat}}, {{result.city.coord.lon}})*"}, "application/json": {"raw": true, "transform": {"location": "{{parameters.location}}", "units": "{{parameters.units}}", "forecast": "{{result}}"}}}}, "error_mapping": {"400": "Invalid request. Please check the location name.", "401": "Weather API authentication failed. Please contact support.", "404": "Location '{{parameters.location}}' not found. Please check the spelling.", "429": "Rate limit exceeded. Please try again later.", "500": "Weather service is temporarily unavailable.", "503": "Weather service is under maintenance."}, "success_indicator": {"status_codes": [200], "required_fields": ["cod", "list", "city"]}}, "test_data": {"mock_responses": {"success": {"cod": "200", "message": 0, "cnt": 3, "list": [{"dt": 1641024000, "main": {"temp": 22.5, "feels_like": 21.8, "humidity": 65, "pressure": 1013}, "weather": [{"id": 800, "main": "Clear", "description": "clear sky", "icon": "01d"}], "wind": {"speed": 3.5, "deg": 180}, "dt_txt": "2025-01-04 12:00:00"}, {"dt": 1641110400, "main": {"temp": 20.2, "feels_like": 19.5, "humidity": 70, "pressure": 1015}, "weather": [{"id": 803, "main": "Clouds", "description": "broken clouds", "icon": "04d"}], "wind": {"speed": 2.8, "deg": 210}, "dt_txt": "2025-01-05 12:00:00"}, {"dt": 1641196800, "main": {"temp": 18.9, "feels_like": 18.1, "humidity": 75, "pressure": 1012}, "weather": [{"id": 500, "main": "Rain", "description": "light rain", "icon": "10d"}], "wind": {"speed": 4.2, "deg": 190}, "dt_txt": "2025-01-06 12:00:00"}], "city": {"id": 2643743, "name": "London", "coord": {"lat": 51.5074, "lon": -0.1278}, "country": "GB", "timezone": 0, "sunrise": 1641023456, "sunset": 1641054321}}, "empty": {"cod": "200", "message": 0, "cnt": 0, "list": [], "city": {"name": "Unknown", "country": "XX"}}, "error": {"cod": "404", "message": "city not found"}}, "sample_parameters": [{"name": "standard", "values": {"location": "London", "units": "metric", "days": 5}}, {"name": "minimal", "values": {"location": "Paris"}}, {"name": "maximal", "values": {"location": "New York City", "units": "imperial", "days": 16, "language": "es"}}], "error_scenarios": [{"name": "missing_location", "parameters": {}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Missing required parameter: location"}}, {"name": "invalid_location", "parameters": {"location": "123!@#$%"}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Invalid location format"}}, {"name": "invalid_units", "parameters": {"location": "London", "units": "kelvin"}, "expected_error": {"status": 400, "code": "VALIDATION_ERROR", "message": "Invalid units value. Must be one of: metric, imperial, standard"}}], "edge_cases": [{"name": "special_characters", "parameters": {"location": "São Paulo"}}, {"name": "multi_word_city", "parameters": {"location": "Salt Lake City"}}, {"name": "with_country_code", "parameters": {"location": "London, UK"}}], "performance_data": {"response_times": {"p50": 150, "p90": 300, "p95": 450, "p99": 800}, "throughput": {"requests_per_second": 100, "concurrent_users": 50}, "rate_limits": {"requests_per_minute": 60, "requests_per_hour": 1000, "burst_limit": 10}, "cache_ttl": 600}}, "documentation": {"description": "Retrieves weather forecast data for a specified location. Supports multiple forecast days and unit systems.", "examples": [{"description": "Get 5-day forecast for London in metric units", "parameters": {"location": "London", "units": "metric", "days": 5}}, {"description": "Get forecast for New York in Fahrenheit", "parameters": {"location": "New York", "units": "imperial"}}], "notes": ["Location can be city name, city name with country code, or coordinates", "Default units are metric (Celsius)", "Maximum forecast period is 16 days", "Results are cached for 10 minutes"], "api_reference": "https://openweathermap.org/forecast5"}}