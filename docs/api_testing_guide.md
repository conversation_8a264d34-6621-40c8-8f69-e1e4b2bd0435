# Coherence API Testing Guide

This guide provides a comprehensive approach to testing the Coherence API endpoints. It includes both curl commands for command-line testing and Swagger UI guidance for interactive testing.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Testing Flow](#testing-flow)
3. [Testing with curl](#testing-with-curl)
4. [Testing with Swagger UI](#testing-with-swagger-ui)
5. [Common Use Cases](#common-use-cases)
6. [Troubleshooting](#troubleshooting)

## Prerequisites

Before you begin testing, ensure you have:

1. A running instance of Coherence API (local or remote)
2. A system admin API key for creating tenants
3. [curl](https://curl.se/) installed (for command-line testing)
4. [jq](https://stedolan.github.io/jq/) installed (optional, for formatting JSON responses)

## Testing Flow

The recommended testing flow follows the typical user journey:

1. Create a tenant (admin operation)
2. Create additional API keys for the tenant
3. Manage tenant settings
4. Test intent resolution with natural language
5. Test conversation continuation
6. Test template management
7. Test OpenAPI integration
8. Test OAuth flows (if needed)

## Testing with curl

### Environment Setup

First, set up environment variables to store your API keys and other values:

```bash
# Base URL for the API
export API_URL="http://localhost:8001/v1"
export ADMIN_URL="http://localhost:8001/v1/admin"

# System admin API key (should be obtained from your admin)
export SYSTEM_ADMIN_KEY="your_system_admin_key"

# Variables that will be populated during testing
export TENANT_ID=""
export TENANT_API_KEY=""
export CONVERSATION_ID=""
```

### 1. Create a Tenant

```bash
# Create a new tenant
response=$(curl -s -X POST "${ADMIN_URL}/tenants" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${SYSTEM_ADMIN_KEY}" \
  -d '{
    "name": "Test Tenant",
    "industry_pack": "General",
    "admin_email": "<EMAIL>"
  }')

# Extract tenant ID and API key from response
export TENANT_ID=$(echo $response | jq -r '.id')
export TENANT_API_KEY=$(echo $response | jq -r '.api_keys[0].key')

echo "Created tenant with ID: $TENANT_ID"
echo "API Key: $TENANT_API_KEY"
```

### 2. Create Additional API Keys

```bash
# Create a regular API key for the tenant
curl -X POST "${ADMIN_URL}/tenants/${TENANT_ID}/api-keys" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d "{
    \"tenant_id\": \"${TENANT_ID}\",
    \"label\": \"Integration Testing Key\"
  }"
```

### 3. Update Tenant Settings

```bash
# Update tenant settings
curl -X PUT "${ADMIN_URL}/tenants/${TENANT_ID}/settings" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d '{
    "tier1_threshold": "0.9",
    "tier2_threshold": "0.75",
    "llm_model": "gpt-4",
    "settings": {
      "debug_mode": "true"
    }
  }'
```

### 4. Test Intent Resolution

```bash
# Generate a random UUID for the user
export USER_ID=$(uuidgen | tr '[:upper:]' '[:lower:]')

# Resolve a simple intent
response=$(curl -s -X POST "${API_URL}/resolve" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d "{
    \"user_id\": \"${USER_ID}\",
    \"role\": \"user\",
    \"message\": \"I need the weather for New York\",
    \"context\": {
      \"location\": \"US\"
    }
  }")

# Extract conversation ID (needed for continuation)
export CONVERSATION_ID=$(echo $response | jq -r '.conversation_id // empty')

echo $response | jq
```

### 5. Test Conversation Continuation

If the previous response was an `ask` type (needing more information):

```bash
# Continue the conversation with additional information
curl -X POST "${API_URL}/continue" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d "{
    \"conversation_id\": \"${CONVERSATION_ID}\",
    \"user_id\": \"${USER_ID}\",
    \"message\": \"Tomorrow afternoon\"
  }" | jq
```

### 6. Check Workflow Status (for async operations)

If you received an `async` response type:

```bash
# Extract workflow ID from the previous response
export WORKFLOW_ID=$(echo $response | jq -r '.workflow_id // empty')

# Check workflow status
curl -X GET "${API_URL}/status/${WORKFLOW_ID}" \
  -H "X-API-Key: ${TENANT_API_KEY}" | jq
```

### 7. Test Template Management

```bash
# List available templates
curl -X GET "${ADMIN_URL}/templates" \
  -H "X-API-Key: ${TENANT_API_KEY}" | jq

# Create a new template
curl -X POST "${ADMIN_URL}/templates" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d '{
    "key": "weather_intent",
    "category": "intent_router",
    "body": "How is the weather in {location} on {date}?",
    "language": "en"
  }' | jq
```

### 8. Test OpenAPI Integration

```bash
# Import an OpenAPI specification
curl -X POST "${ADMIN_URL}/integrations" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d '{
    "name": "Weather API",
    "spec_url": "https://api.example.com/openapi.json"
  }' | jq
```

## Testing with Swagger UI

Coherence provides a Swagger UI interface (accessible at `http://localhost:8001/docs` by default) that allows for interactive API testing.

### 1. Authenticate in Swagger UI

1. Look for the "Authorize" button at the top of the page
2. Enter your API key in the `X-API-Key` field
3. Click "Authorize" and close the dialog

### 2. Create a Tenant

1. Expand the `POST /admin/tenants` endpoint
2. Click "Try it out"
3. Enter the tenant creation payload:
   ```json
   {
     "name": "Swagger Test Tenant",
     "industry_pack": "General",
     "admin_email": "<EMAIL>"
   }
   ```
4. Click "Execute"
5. Save the `id` and `api_keys[0].key` from the response for future requests

### 3. Test Intent Resolution

1. Expand the `POST /resolve` endpoint
2. Click "Try it out"
3. Enter a resolve request payload:
   ```json
   {
     "user_id": "00000000-0000-0000-0000-000000000001",
     "role": "user",
     "message": "I need to book a meeting with John tomorrow at 2pm",
     "context": {
       "timezone": "America/New_York"
     }
   }
   ```
4. Click "Execute"
5. Examine the response

### 4. Continue the Conversation

1. Expand the `POST /continue` endpoint
2. Click "Try it out"
3. Enter a continue request payload:
   ```json
   {
     "conversation_id": "[conversation_id from previous response]",
     "user_id": "00000000-0000-0000-0000-000000000001",
     "message": "Make it a video call"
   }
   ```
4. Click "Execute"
5. Examine the response

## Common Use Cases

The following sections outline common end-to-end use cases for testing.

### Use Case 1: Weather Information

1. Create a tenant with the General industry pack
2. Resolve intent with message: "What's the weather like in Chicago?"
3. Continue with message: "How about tomorrow?" (tests conversation context)

### Use Case 2: Meeting Scheduling

1. Create a tenant with the Calendar industry pack
2. Resolve intent with message: "Schedule a meeting with Mark on Friday"
3. Continue with message: "At 3pm" (parameter completion)
4. Continue with message: "Make it a 30-minute call" (additional parameters)

### Use Case 3: API Integration

1. Create a tenant
2. Import an OpenAPI spec for a CRM system
3. Resolve intent with message: "Find all customers who purchased in the last month"
4. Continue with message: "Show me their total spend" (follow-up query)

## Troubleshooting

### Common Issues and Solutions

1. **"Unauthorized"** error:
   - Verify your API key is correct
   - Ensure your key hasn't been revoked
   - Check if your tenant has the proper permissions

2. **"Not Found"** error:
   - Verify that the resource ID (tenant, template, etc.) is correct
   - Ensure the resource hasn't been deleted

3. **"Intent not recognized"** response:
   - Check that your tenant has the required intent templates for your industry
   - Try rephrasing your query to match expected patterns
   - Ensure the necessary OpenAPI integrations are set up (if applicable)

4. **Request timing out**:
   - Check your network connection
   - Verify the API server is running
   - For complex operations, consider using async endpoints

### Logging and Debugging

To enable more detailed logging for debugging:

1. Update your tenant's settings with:
   ```json
   {
     "settings": {
       "debug_mode": "true",
       "log_level": "debug"
     }
   }
   ```

2. Check the server logs for more detailed information about request processing.

## API Reference

For a complete reference of all API endpoints and schemas, refer to the [Coherence API Reference](http://localhost:8001/redoc) or the OpenAPI specification at `/openapi.json`.

## Using This Guide for SDK Development

This testing guide serves as a foundation for developing client SDKs in various languages. Here's how to leverage it for SDK development:

### 1. API Contract Verification

- Use the curl commands to verify the expected request/response contract
- Document any discrepancies or undocumented behaviors
- Ensure all response formats are accurately captured

### 2. Test Cases to Implement in SDK

- Convert each test scenario into an automated test for your SDK
- Implement both happy path and error cases
- Test handling of different response types (reply, ask, async, fallback)

### 3. Request/Response Object Mapping

- Use the JSON examples to create accurate request/response models
- Ensure proper type handling for UUIDs, timestamps, and enums
- Create helper classes for standard response formats

### 4. Authentication Implementation

- Test different authentication methods and refresh patterns
- Document any security-related behavior or requirements

### 5. SDK Best Practices

- Implement proper error handling with meaningful messages
- Add retry logic for appropriate cases
- Include logging and debugging facilities
- Consider both synchronous and asynchronous interaction patterns

For more detailed guidance on SDK development, see the [SDK Development Guide](/docs/SDK_DEVELOPMENT.md).