- Migrate theme to prism.
- Switch org button./org-selection page: delete create organization section. WE will do that in <PERSON>. The Choose and organization section is not aligned. This page needs to be styled like the other pages.
- Profile link in topbar. Remove it. We get to profile management via avatar button.
- Debug JWT button on main page errors with No JWT template exists with name: default although we have teh coherence_session template. Maybe just the __session var we should look at?
- /admin needs to be the main page of the site
- /admin/integrations Current Selection section All integrations say Unknown Format
- /admin/integrations integrations table columns are empty and with Invalid Date. ellipsis does not work--delete it.
- /admin/integrations/[id] page
    - Details tab
        -View Spec is only a partial spec.
        -View Spec button should not be titled "OpenAPI Spec" as we do other specs, too
    -Credentials tab
        - have we tested this functionality? We need to figure out how to make auth a prereq to other template actions
    -Endpoints tab
        - rate limits are all the same. 
        - Add search field for endpoints
        - Enable button does not work
        - Can we merge the endpooints and Generate tabs? 
- /admin/templates page
    - does it make sense to show all types of templates in this table? Or should we aggregate teh error, param, response, and intent into one row and show all these on the details page? I think this is the way to go. Let's refactor and merge these template types into one comprehensive and beautiful view of the template.
- Workflows: Need to implement and test. Will likely be used primarily for auth. My thinking is that we allow the LLM at runtime to generate the necessary workflows based on user intent in the request. How should we do this?
- Chat page
    - Need to save conversations by user and show them in the left pane ala OpenAI/ChatGPT.
    - Page itself cannot scroll. Only the conversation pan should scroll based on the length of the convo. 
    - Type a message bar should always be locked to the bottom of the chat page (no scrolling away from it)
- Footer
    - All links should work or be deleted.
    - Copyright 2025 SynapseDx Limited