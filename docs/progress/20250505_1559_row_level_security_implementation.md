# Coherence Row-Level Security Implementation

## Overview

This document details the implementation of Row-Level Security (RLS) in the Coherence database infrastructure, providing database-level tenant isolation for the multi-tenant architecture.

## Why Row-Level Security?

Row-Level Security (RLS) enforces tenant isolation at the database level, rather than relying solely on application-level filtering. This provides several significant advantages:

1. **Defense in Depth**: Even if application-level security has flaws, database-level controls prevent unauthorized access
2. **Query Simplification**: Application code doesn't need to include tenant filtering in every query
3. **Prevention of Data Leaks**: Eliminates the risk of accidentally exposing one tenant's data to another
4. **Centralized Security**: Security logic is defined once at the database level, rather than scattered throughout the application

## Implementation Details

### Core Components

1. **PostgreSQL RLS Policies**
   - Applied to all tenant-specific tables
   - Controls both read (SELECT) and write (INSERT, UPDATE, DELETE) operations
   - Special handling for hierarchical templates with global, pack, and tenant scopes

2. **Database Context Functions**
   - `current_tenant_id()`: Returns the current tenant context from session settings
   - `is_system_admin()`: Checks if the current session has system admin privileges

3. **Data Validation Triggers**
   - Trigger function `check_tenant_constraint()` validates tenant context on all data modifications
   - Prevents cross-tenant operations even for direct database access
   - Custom validation logic for different table relationships

### Migration Implementation

We've created the `20250505_153630_add_row_level_security.py` migration file that:

1. Creates the helper functions needed for tenant context
2. Enables RLS on all tenant-related tables
3. Defines policies based on tenant ID and admin status
4. Implements validation triggers for data integrity
5. Provides a clean downgrade path if needed

### Tables Protected with RLS

Row-Level Security has been applied to all multi-tenant tables:

- `api_keys`
- `api_integrations`
- `api_auth_configs`
- `api_endpoints`
- `api_rate_limits`
- `templates`
- `template_versions`
- `template_tests`
- `template_dependencies`
- `tenant_settings`

### Policy Highlights

1. **Simple Tenant Isolation**
   ```sql
   CREATE POLICY tenant_isolation_api_keys ON api_keys
   USING (
       tenant_id = current_tenant_id() OR
       is_system_admin() OR
       current_tenant_id() IS NULL
   );
   ```

2. **Template Hierarchical Access**
   ```sql
   CREATE POLICY tenant_isolation_templates ON templates
   USING (
       -- System admin can see all templates
       is_system_admin() OR
       -- Context not set - allow access (internal operation)
       current_tenant_id() IS NULL OR 
       -- Tenant can see their own templates
       (tenant_id = current_tenant_id()) OR
       -- Everyone can see global templates
       (scope = 'global') OR
       -- Tenant can see templates from their industry pack
       (scope = 'pack' AND EXISTS (
           SELECT 1 FROM tenants
           WHERE tenants.id = current_tenant_id()
           AND tenants.industry_pack::TEXT = templates.scope_id::TEXT
       ))
   );
   ```

3. **Validation Triggers**
   ```sql
   CREATE OR REPLACE FUNCTION check_tenant_constraint()
   RETURNS TRIGGER AS $$
   BEGIN
       -- Skip if system admin or no tenant context set
       IF is_system_admin() OR current_tenant_id() IS NULL THEN
           RETURN NEW;
       END IF;
       
       -- For tables with direct tenant_id column
       IF TG_TABLE_NAME IN ('api_keys', 'api_integrations', 'templates', 'tenant_settings') THEN
           IF NEW.tenant_id IS DISTINCT FROM current_tenant_id() THEN
               RAISE EXCEPTION 'Cannot insert or update records for different tenant';
           END IF;
       END IF;
       
       -- Additional relationship validations...
       
       RETURN NEW;
   END;
   $$ LANGUAGE plpgsql;
   ```

## Security Considerations

1. **Session Context Security**
   - Tenant context is set by trusted application code, not by user input
   - Authentication occurs before tenant context is established
   - Backend middleware validates API keys before setting tenant context

2. **System Administration**
   - System admins can view all tenant data for support purposes
   - Admin privileges are controlled through the `is_system_admin()` function
   - Admin operations are logged for accountability

3. **Hierarchical Access Control**
   - Global templates are accessible to all tenants
   - Industry pack templates are accessible only to tenants in that industry
   - Tenant templates are accessible only to the specific tenant

## Application Integration

The RLS implementation integrates with the application through:

1. **Connection Middleware**
   - Sets the `app.current_tenant_id` and `app.is_system_admin` session parameters
   - Example: `SET LOCAL app.current_tenant_id = '123e4567-e89b-12d3-a456-************';`

2. **API Authentication**
   - `get_tenant_from_api_key` FastAPI dependency extracts tenant from API key
   - Tenant context is established before any database operations

3. **Session Management**
   - Async session support with proper tenant context propagation
   - Connection pooling maintains tenant isolation

## Testing Approach

To ensure the RLS implementation works correctly, we'll implement:

1. **Unit Tests**
   - Test tenant isolation with multiple test tenants
   - Verify that cross-tenant access is properly prevented

2. **Integration Tests**
   - Test full API flow with authentication and tenant context
   - Verify hierarchical template access

3. **Manual Security Testing**
   - Attempt to bypass RLS with various SQL injection scenarios
   - Test direct database access with different user permissions

## Conclusion

The implementation of Row-Level Security completes the security foundation for Coherence's multi-tenant architecture. This enhances data isolation, simplifies application code, and provides a defense-in-depth approach to security.

The database infrastructure is now fully prepared for secure multi-tenant operations with proper isolation guarantees at the database level.