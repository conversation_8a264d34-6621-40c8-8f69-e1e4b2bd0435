# Template System Fixes and Improvements

Date: May 13, 2025  
Author: <PERSON> Code

## Overview

We've identified and fixed critical issues in the template system that were preventing certain templates from appearing in the admin dashboard. This document describes the problems found and solutions implemented.

## Issues Identified

### 1. Frontend Tenant ID Passing

The admin template list page was incorrectly passing the tenant ID to the backend:

- **Problem**: The frontend was setting the `X-Tenant-ID` directly in the headers object:
  ```typescript
  headers: {
      'X-Tenant-ID': adminSession.tenant?.id || ''
  }
  ```
- **Impact**: This bypassed the intended logic in `apiClient.ts` which should handle the tenant ID passing consistently.

### 2. Template Actions Format Mismatch

Certain templates (particularly those generated from OpenAPI integrations like ICD-10) had incompatible data structures:

- **Problem**: Templates were being stored with their `actions` field as an array/list, but the `AdminTemplate` Pydantic schema expected it to be a dictionary/object.
- **Impact**: This caused validation failures during API responses, resulting in templates being silently filtered out before reaching the frontend.

### 3. Template Key Format Inconsistency

Generated templates were using key formats that didn't match the UI's expectations:

- **Problem**: The template generator was creating keys like `icd10cm_openapi_searchicd10cm` while the UI expected the format `endpoint_<endpoint_id>_<operation>`.
- **Impact**: This affected how templates were grouped and displayed in the admin interface.

## Solutions Implemented

### 1. Frontend Tenant ID Fix

Updated how the frontend passes tenant ID to the backend:

```typescript
// Old approach (incorrect)
const response = await apiClient<AdminTemplateType[]>('/admin/templates', { 
    method: 'GET',
    token: adminSession.token,
    headers: {
        'X-Tenant-ID': adminSession.tenant?.id || ''
    }
});

// New approach (correct)
const response = await apiClient<AdminTemplateType[]>('/admin/templates', { 
    method: 'GET',
    token: adminSession.token,
    orgId: adminSession.tenant?.id || ''
});
```

This ensures consistent tenant ID handling through the `apiClient.ts` logic.

### 2. Template Actions Format Fix

Two-pronged approach to fixing the template format:

1. **Existing Templates**: Created and executed a script to fix existing templates:
   ```python
   # From list to dictionary format - snippets from fix_template_actions.py
   if template.actions and len(template.actions) > 0:
       # Extract the first action from the list
       first_action = template.actions[0]
       # Create a dictionary with the first action under a key
       new_actions = {"api_action": first_action}
   else:
       # Empty list becomes empty dict
       new_actions = {}
   ```

2. **Template Generation**: Updated the action generator to create correctly formatted templates:
   ```python
   # Old format
   "actions": [action_config],  # List

   # New format
   "actions": {"api_action": action_config},  # Dictionary
   ```

### 3. Template Key Format Fix

Updated the template key generation logic to match UI expectations:

```python
# Old format
template_key = f"{api_key}_{operation_id}".replace("/", "_").replace("-", "_").lower()

# New format
template_key = f"endpoint_{endpoint_id}_{operation_id}".replace("/", "_").replace("-", "_").lower()
```

## Additional Improvements

1. Added enhanced logging in the API client and template list endpoint to make debugging easier
2. Added better error reporting for template validation failures
3. Created a reusable script (`fix_template_actions.py`) for fixing template format issues

## Lessons Learned

1. **Schema Validation Communication**: The backend and frontend must agree on data formats, and any changes should be communicated clearly across teams.
2. **Frontend-Backend Consistency**: How data is passed between components matters - using the designated methods (like `orgId` parameter vs direct header manipulation) ensures consistent behavior.
3. **Key Format Standardization**: Use clear naming conventions and document them to ensure all parts of the system have the same expectations.

## Next Steps

1. Update documentation to clearly specify the expected format for template actions
2. Consider adding schema validation earlier in the pipeline to catch these issues before they reach the database
3. Improve error reporting to surface validation failures more clearly to API consumers