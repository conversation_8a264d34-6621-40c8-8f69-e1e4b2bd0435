# Template Vectorization Guide

This guide explains how to use the template vectorization system in Coherence, which enables semantic search of templates.

## Overview

The template vectorization system uses vector embeddings to enable semantic searching of templates. This allows for:

- Finding templates similar to a natural language query
- Discovering related templates across different categories
- Semantic grouping of templates with similar functionality

## Architecture

The vectorization system consists of several key components:

1. **VectorIndexer Service**: A service that handles template indexing and searching
2. **Template Service Integration**: Hooks in the TemplateService to automatically index templates when they change
3. **Qdrant Vector Database**: Storage for template embeddings with efficient similarity search
4. **LLM Embedding Provider**: Service to generate vector embeddings from template content

## Index Structure

Templates are indexed in separate collections based on their scope:

- `template_idx_global`: Global templates available to all tenants
- `template_idx_pack_{pack_id}`: Templates for a specific industry pack
- `template_idx_{tenant_id}`: Tenant-specific templates

## How It Works

When a template is created or updated:

1. The `_after_template_change` method in `TemplateService` is called
2. This method tries to import and use the `VectorIndexer` service
3. The template content is converted into embeddings using an LLM
4. The embeddings are stored in Qdrant with template metadata
5. The template can now be found via semantic search

## Usage

### Manually Rebuilding Template Indexes

Use the provided script to rebuild all template indexes:

```bash
python -m scripts.rebuild_template_index
```

To test template vectorization:

```bash
python -m scripts.test_template_vectorization
```

### Searching for Templates

The VectorIndexer provides a `search_templates` method that can be used to find templates semantically similar to a query:

```python
from src.coherence.services.vector_indexer import VectorIndexer

vector_indexer = VectorIndexer()
results = await vector_indexer.search_templates(
    query="How to handle authentication errors",
    tenant_id="optional-tenant-id",
    pack_id="optional-pack-id",
    category="optional-category-filter",
    limit=5,
    score_threshold=0.7
)

# Process search results
for result in results:
    print(f"Template: {result['template_key']}")
    print(f"Score: {result['score']}")
    print(f"Category: {result['template_category']}")
```

## Integration Points

The system integrates with existing components in these ways:

1. **Template Service**: Automatically calls the VectorIndexer when templates change
2. **Qdrant Client**: Provides low-level vector database operations
3. **LLM Factory**: Provides embedding generation capabilities

## Troubleshooting

If templates are not being indexed properly:

1. Check that the VectorIndexer module is being successfully imported
2. Verify that the LLM provider is properly configured for embeddings
3. Ensure the Qdrant service is running and accessible
4. Check that collections are created with the correct vector dimensions

## Performance Considerations

- Template indexing adds a small overhead to template creation/updates
- The indexing is done asynchronously and won't block the main operation
- Embedding generation uses async operations to minimize latency
- Consider rebuilding indexes periodically to ensure consistency

## Metrics and Monitoring

The following metrics are tracked:

- `EMBEDDING_METRICS`: Time spent generating embeddings
- `VECTOR_SEARCH_LATENCY`: Time spent searching for templates
- `VECTOR_UPSERT_LATENCY`: Time spent indexing templates
- `VECTOR_INDEX_SIZE`: Number of templates in each index

## Future Improvements

- Add background workers for batch indexing of templates
- Implement fallback search strategies when vector search returns no results
- Add more metadata to improve filtering capabilities
- Add cross-tenant template discovery for system administrators