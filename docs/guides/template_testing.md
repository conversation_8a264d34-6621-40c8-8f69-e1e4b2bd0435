# Template Testing Guide

This guide covers comprehensive testing strategies for unified templates, including unit testing, integration testing, and performance validation.

## Table of Contents

1. [Overview](#overview)
2. [Test Mode](#test-mode)
3. [Testing Strategies](#testing-strategies)
4. [Unit Testing Templates](#unit-testing-templates)
5. [Integration Testing](#integration-testing)
6. [Performance Testing](#performance-testing)
7. [Test Data Management](#test-data-management)
8. [Automated Testing](#automated-testing)
9. [Manual Testing](#manual-testing)
10. [Troubleshooting](#troubleshooting)

## Overview

Template testing ensures:
- Intent patterns match correctly
- Parameters validate properly
- API calls execute successfully
- Responses format correctly
- Errors handle gracefully
- Performance meets requirements

### Testing Lifecycle

1. **Development**: Test with mock data
2. **Integration**: Test with real APIs
3. **Staging**: Test with production-like data
4. **Production**: Monitor and validate

## Test Mode

### Enabling Test Mode

Test mode allows templates to execute without making actual API calls:

```python
# Using test mode in code
from src.coherence.intent_pipeline.orchestrator import ChatOrchestrator

orchestrator = ChatOrchestrator()
result = await orchestrator.execute_action(
    "template_key",
    {"param": "value"},
    test_mode=True  # Enable test mode
)
```

### API Endpoint for Testing

```bash
# Test via API
curl -X POST http://localhost:8001/v1/templates/{template_id}/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "location": "London"
    },
    "scenario": "success"
  }'
```

### Test Mode Features

1. **Mock Responses**: Returns predefined test data
2. **Validation Testing**: Validates parameters without execution
3. **Format Testing**: Tests all CRFS formats
4. **Error Simulation**: Tests error scenarios

## Testing Strategies

### 1. Progressive Testing

Start simple and add complexity:

```json
{
  "test_progression": [
    {
      "stage": "basic",
      "test": "Single parameter, success response"
    },
    {
      "stage": "validation",
      "test": "Invalid parameters, error handling"
    },
    {
      "stage": "complex",
      "test": "Multiple parameters, conditional logic"
    },
    {
      "stage": "integration",
      "test": "Real API calls, auth flow"
    }
  ]
}
```

### 2. Coverage Strategy

Ensure comprehensive coverage:

- **Intent Matching**: All pattern variations
- **Parameter Validation**: Valid/invalid cases
- **Response Formats**: All CRFS formats
- **Error Scenarios**: All error codes
- **Edge Cases**: Boundary conditions

### 3. Test Pyramid

```
         /\
        /  \  E2E Tests (10%)
       /----\
      /      \  Integration Tests (30%)
     /--------\
    /          \  Unit Tests (60%)
   /____________\
```

## Unit Testing Templates

### 1. Intent Pattern Testing

Test pattern matching accuracy:

```python
import pytest
from src.coherence.intent_pipeline.resolver import IntentResolver

class TestWeatherIntent:
    @pytest.fixture
    def resolver(self):
        return IntentResolver()
    
    def test_basic_pattern_match(self, resolver):
        """Test basic weather pattern matching."""
        result = resolver.resolve("weather in London")
        assert result.template_key == "weather_forecast"
        assert result.confidence > 0.85
        assert result.parameters["location"] == "London"
    
    def test_pattern_variations(self, resolver):
        """Test multiple pattern variations."""
        patterns = [
            "what's the weather in Paris",
            "show me weather for Tokyo",
            "temperature in Berlin"
        ]
        for pattern in patterns:
            result = resolver.resolve(pattern)
            assert result.template_key == "weather_forecast"
    
    def test_parameter_extraction(self, resolver):
        """Test parameter extraction from patterns."""
        result = resolver.resolve("weather in New York for 5 days")
        assert result.parameters["location"] == "New York"
        assert result.parameters["days"] == 5
```

### 2. Validation Testing

Test parameter validation rules:

```python
from src.coherence.services.template_validator import UnifiedTemplateValidator

class TestValidation:
    def test_required_parameters(self, template):
        """Test required parameter validation."""
        validator = UnifiedTemplateValidator()
        
        # Missing required parameter
        result = validator.validate_parameters(
            template,
            {}  # No parameters
        )
        assert not result.is_valid
        assert "location" in result.errors[0]
    
    def test_parameter_constraints(self, template):
        """Test parameter constraint validation."""
        validator = UnifiedTemplateValidator()
        
        # Invalid email format
        result = validator.validate_parameters(
            template,
            {"email": "not-an-email"}
        )
        assert not result.is_valid
        assert "email" in result.errors[0]
    
    def test_enum_validation(self, template):
        """Test enum parameter validation."""
        validator = UnifiedTemplateValidator()
        
        # Invalid enum value
        result = validator.validate_parameters(
            template,
            {"status": "invalid_status"}
        )
        assert not result.is_valid
```

### 3. Transformation Testing

Test parameter transformations:

```python
class TestTransformations:
    def test_string_transformations(self, transformer):
        """Test string transformation functions."""
        # Trim
        assert transformer.apply("  hello  ", ["trim"]) == "hello"
        
        # Case transformations
        assert transformer.apply("Hello", ["lowercase"]) == "hello"
        assert transformer.apply("hello", ["uppercase"]) == "HELLO"
        
        # Multiple transformations
        assert transformer.apply("  HELLO  ", ["trim", "lowercase"]) == "hello"
    
    def test_custom_transformations(self, transformer):
        """Test custom transformation functions."""
        # Email normalization
        assert transformer.apply(
            "<EMAIL>",
            ["normalize_email"]
        ) == "<EMAIL>"
        
        # Currency conversion
        assert transformer.apply(20.50, ["to_cents"]) == 2050
```

### 4. CRFS Formatting Testing

Test response formatting:

```python
from src.coherence.template_system.crfs_formatter import CRFSFormatter

class TestCRFSFormatting:
    def test_structured_format(self, formatter, template, mock_response):
        """Test structured format rendering."""
        result = formatter.format_from_unified_template(
            mock_response,
            template,
            {"accept": "text/html"}
        )
        
        assert result.format == "structured"
        assert len(result.sections) > 0
        assert result.sections[0]["type"] == "text"
    
    def test_plain_text_format(self, formatter, template, mock_response):
        """Test plain text format rendering."""
        result = formatter.format_from_unified_template(
            mock_response,
            template,
            {"accept": "text/plain"}
        )
        
        assert result.format == "text/plain"
        assert isinstance(result.content, str)
        assert "London" in result.content
    
    def test_conditional_rendering(self, formatter, template):
        """Test conditional section rendering."""
        # Response with warnings
        response_with_warning = {
            "data": {"temp": 20},
            "warnings": ["High UV index"]
        }
        
        result = formatter.format_from_unified_template(
            response_with_warning,
            template
        )
        
        # Check warning section appears
        warning_sections = [
            s for s in result.sections 
            if s["type"] == "alert"
        ]
        assert len(warning_sections) > 0
```

## Integration Testing

### 1. API Integration Tests

Test actual API calls:

```python
@pytest.mark.integration
class TestAPIIntegration:
    async def test_real_api_call(self, orchestrator, api_credentials):
        """Test template with real API."""
        # Set up real credentials
        await orchestrator.credential_manager.store(
            "weather_api_key",
            api_credentials["weather_key"]
        )
        
        result = await orchestrator.execute_action(
            "weather_forecast",
            {"location": "London"},
            test_mode=False  # Real mode
        )
        
        assert result["status"] == "success"
        assert "temp" in result["data"]
    
    async def test_authentication_flow(self, orchestrator):
        """Test authentication handling."""
        result = await orchestrator.execute_action(
            "user_auth",
            {
                "email": "<EMAIL>",
                "password": "TestPass123!"
            }
        )
        
        assert "token" in result["data"]
        assert result["data"]["token"].startswith("Bearer")
```

### 2. Error Handling Tests

Test error scenarios:

```python
class TestErrorHandling:
    async def test_404_handling(self, orchestrator):
        """Test 404 error handling."""
        result = await orchestrator.execute_action(
            "user_fetch",
            {"user_id": "nonexistent"}
        )
        
        assert result["status"] == "error"
        assert result["error"]["code"] == 404
        assert "not found" in result["error"]["message"].lower()
    
    async def test_rate_limit_handling(self, orchestrator):
        """Test rate limit error handling."""
        # Make many requests quickly
        for _ in range(100):
            await orchestrator.execute_action(
                "api_endpoint",
                {"param": "value"}
            )
        
        # Should get rate limit error
        result = await orchestrator.execute_action(
            "api_endpoint",
            {"param": "value"}
        )
        
        assert result["error"]["code"] == 429
```

### 3. Multi-Step Flow Tests

Test complex workflows:

```python
class TestWorkflows:
    async def test_payment_flow(self, orchestrator):
        """Test complete payment workflow."""
        # Step 1: Create payment intent
        payment = await orchestrator.execute_action(
            "payment_create",
            {"amount": 50.00, "currency": "USD"}
        )
        
        assert payment["status"] == "success"
        payment_id = payment["data"]["id"]
        
        # Step 2: Confirm payment
        confirmation = await orchestrator.execute_action(
            "payment_confirm",
            {"payment_id": payment_id}
        )
        
        assert confirmation["data"]["status"] == "succeeded"
        
        # Step 3: Send receipt
        receipt = await orchestrator.execute_action(
            "payment_receipt",
            {"payment_id": payment_id}
        )
        
        assert receipt["status"] == "success"
```

## Performance Testing

### 1. Response Time Testing

Measure template execution time:

```python
import time
import statistics

class TestPerformance:
    async def test_response_times(self, orchestrator):
        """Test response time performance."""
        times = []
        
        for _ in range(100):
            start = time.time()
            await orchestrator.execute_action(
                "template_key",
                {"param": "value"},
                test_mode=True
            )
            times.append(time.time() - start)
        
        # Calculate percentiles
        p50 = statistics.median(times)
        p95 = sorted(times)[95]
        p99 = sorted(times)[99]
        
        # Assert performance requirements
        assert p50 < 0.1  # 100ms median
        assert p95 < 0.3  # 300ms 95th percentile
        assert p99 < 0.5  # 500ms 99th percentile
```

### 2. Load Testing

Test under load:

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class TestLoad:
    async def test_concurrent_requests(self, orchestrator):
        """Test concurrent request handling."""
        async def make_request():
            return await orchestrator.execute_action(
                "template_key",
                {"param": "value"}
            )
        
        # Run 50 concurrent requests
        tasks = [make_request() for _ in range(50)]
        results = await asyncio.gather(*tasks)
        
        # All should succeed
        success_count = sum(
            1 for r in results 
            if r["status"] == "success"
        )
        assert success_count == 50
```

### 3. Memory Testing

Monitor memory usage:

```python
import psutil
import os

class TestMemory:
    def test_memory_usage(self, orchestrator):
        """Test memory usage doesn't grow."""
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Execute many times
        for _ in range(1000):
            orchestrator.execute_action(
                "template_key",
                {"param": "value"}
            )
        
        final_memory = process.memory_info().rss
        memory_growth = final_memory - initial_memory
        
        # Should not grow more than 10MB
        assert memory_growth < 10 * 1024 * 1024
```

## Test Data Management

### 1. Test Data Structure

Organize test data effectively:

```json
{
  "test_data": {
    "mock_responses": {
      "success": {
        "description": "Standard success response",
        "response": { /* ... */ }
      },
      "partial_success": {
        "description": "Success with warnings",
        "response": { /* ... */ }
      },
      "error_404": {
        "description": "Resource not found",
        "response": { /* ... */ }
      }
    },
    "test_accounts": {
      "valid_user": {
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "expected_role": "user"
      },
      "admin_user": {
        "email": "<EMAIL>",
        "password": "AdminPass123!",
        "expected_role": "admin"
      }
    },
    "edge_cases": [
      {
        "name": "unicode_characters",
        "input": {"name": "José García"},
        "expected": "success"
      },
      {
        "name": "max_length",
        "input": {"text": "a".repeat(1000)},
        "expected": "validation_error"
      }
    ]
  }
}
```

### 2. Test Data Generation

Generate realistic test data:

```python
from faker import Faker
import random

class TestDataGenerator:
    def __init__(self):
        self.fake = Faker()
    
    def generate_user_data(self, count=10):
        """Generate realistic user test data."""
        users = []
        for _ in range(count):
            users.append({
                "id": self.fake.uuid4(),
                "name": self.fake.name(),
                "email": self.fake.email(),
                "phone": self.fake.phone_number(),
                "address": self.fake.address(),
                "created_at": self.fake.date_time_this_year()
            })
        return users
    
    def generate_payment_data(self):
        """Generate payment test data."""
        return {
            "amount": round(random.uniform(10, 1000), 2),
            "currency": random.choice(["USD", "EUR", "GBP"]),
            "card_last4": self.fake.credit_card_number()[-4:],
            "status": random.choice(["succeeded", "pending", "failed"])
        }
```

### 3. Test Scenarios

Define comprehensive test scenarios:

```python
class TestScenarios:
    @pytest.fixture
    def scenarios(self):
        return {
            "happy_path": {
                "description": "Everything works correctly",
                "steps": [
                    {"action": "login", "expect": "success"},
                    {"action": "fetch_data", "expect": "data"},
                    {"action": "logout", "expect": "success"}
                ]
            },
            "auth_failure": {
                "description": "Authentication fails",
                "steps": [
                    {"action": "login", "expect": "401"},
                    {"action": "fetch_data", "expect": "401"}
                ]
            },
            "partial_failure": {
                "description": "Some operations fail",
                "steps": [
                    {"action": "login", "expect": "success"},
                    {"action": "fetch_invalid", "expect": "404"},
                    {"action": "fetch_valid", "expect": "success"}
                ]
            }
        }
```

## Automated Testing

### 1. CI/CD Integration

GitHub Actions workflow:

```yaml
name: Template Tests

on:
  push:
    paths:
      - 'src/coherence/templates/**'
      - 'tests/templates/**'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Run template validation
      run: |
        python scripts/validate_template_structure.py
    
    - name: Run unit tests
      run: |
        pytest tests/unit/templates -v --cov=src/coherence/templates
    
    - name: Run integration tests
      run: |
        pytest tests/integration/templates -v
      env:
        TEST_API_KEY: ${{ secrets.TEST_API_KEY }}
```

### 2. Automated Test Generation

Generate tests from templates:

```python
def generate_template_tests(template_path):
    """Generate test cases from template definition."""
    template = load_template(template_path)
    
    test_code = f'''
import pytest
from src.coherence.intent_pipeline.orchestrator import ChatOrchestrator

class Test{template["meta"]["key"].title()}:
    """Auto-generated tests for {template["meta"]["key"]}"""
    
    @pytest.fixture
    def template(self):
        return load_template("{template_path}")
    
    @pytest.fixture
    def orchestrator(self):
        return ChatOrchestrator()
'''
    
    # Generate test for each sample parameter
    for sample in template.get("test_data", {}).get("sample_parameters", []):
        test_code += f'''
    
    async def test_{sample["name"]}_scenario(self, orchestrator, template):
        """Test {sample["name"]} scenario."""
        result = await orchestrator.execute_action(
            template["meta"]["key"],
            {sample["values"]},
            test_mode=True
        )
        assert result["status"] == "success"
'''
    
    return test_code
```

### 3. Continuous Monitoring

Monitor template performance in production:

```python
from prometheus_client import Counter, Histogram

# Metrics
template_executions = Counter(
    'template_executions_total',
    'Total template executions',
    ['template_key', 'status']
)

template_duration = Histogram(
    'template_duration_seconds',
    'Template execution duration',
    ['template_key']
)

class MetricsMiddleware:
    async def track_execution(self, template_key, func):
        """Track template execution metrics."""
        start_time = time.time()
        
        try:
            result = await func()
            template_executions.labels(
                template_key=template_key,
                status='success'
            ).inc()
            return result
        except Exception as e:
            template_executions.labels(
                template_key=template_key,
                status='error'
            ).inc()
            raise
        finally:
            duration = time.time() - start_time
            template_duration.labels(
                template_key=template_key
            ).observe(duration)
```

## Manual Testing

### 1. Test Checklist

Manual testing checklist:

```markdown
## Template Test Checklist

### Intent Testing
- [ ] All pattern variations match correctly
- [ ] Parameters extract properly
- [ ] Confidence scores are appropriate
- [ ] Fallback templates trigger correctly

### Validation Testing
- [ ] Required parameters enforce
- [ ] Type validation works
- [ ] Constraints apply correctly
- [ ] Error messages are helpful

### API Testing
- [ ] Authentication works
- [ ] Headers set correctly
- [ ] Body/query parameters map properly
- [ ] Response parses correctly

### CRFS Testing
- [ ] All formats render properly
- [ ] Conditional sections work
- [ ] Variables interpolate correctly
- [ ] Error formats display properly

### Error Testing
- [ ] All error codes handled
- [ ] Error messages are user-friendly
- [ ] Recovery actions available
- [ ] Logging captures errors

### Performance Testing
- [ ] Response times acceptable
- [ ] No memory leaks
- [ ] Handles concurrent requests
- [ ] Caching works properly
```

### 2. Manual Test Script

Step-by-step manual testing:

```bash
# 1. Test intent matching
curl -X POST http://localhost:8001/v1/resolve \
  -H "Content-Type: application/json" \
  -d '{"query": "weather in London"}'

# 2. Test with test mode
curl -X POST http://localhost:8001/v1/templates/weather_forecast/test \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "parameters": {"location": "London"},
    "scenario": "success"
  }'

# 3. Test validation
curl -X POST http://localhost:8001/v1/templates/weather_forecast/test \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "parameters": {},
    "scenario": "success"
  }'
# Should return validation error

# 4. Test different formats
curl -X POST http://localhost:8001/v1/templates/weather_forecast/test \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: text/plain" \
  -d '{
    "parameters": {"location": "London"}
  }'

# 5. Test error scenarios
curl -X POST http://localhost:8001/v1/templates/weather_forecast/test \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "parameters": {"location": "InvalidCity"},
    "scenario": "not_found"
  }'
```

### 3. User Acceptance Testing

UAT scenarios:

```markdown
## User Acceptance Test Scenarios

### Scenario 1: Happy Path
1. User asks "What's the weather in Paris?"
2. System recognizes intent
3. System fetches weather data
4. System displays formatted response
5. User sees temperature and conditions

### Scenario 2: Error Recovery
1. User asks for invalid location
2. System returns helpful error
3. System suggests alternatives
4. User corrects input
5. System returns valid response

### Scenario 3: Multi-Turn Conversation
1. User asks for weather
2. System responds
3. User asks "What about tomorrow?"
4. System maintains context
5. System shows forecast
```

## Troubleshooting

### Common Test Issues

1. **Test Data Mismatch**
```python
# Problem: Mock data doesn't match schema
# Solution: Validate test data against schema
validator.validate_schema(
    template["test_data"]["mock_responses"]["success"],
    template["response"]["schema"]
)
```

2. **Flaky Tests**
```python
# Problem: Tests fail intermittently
# Solution: Add retries and better assertions
@pytest.mark.flaky(reruns=3, reruns_delay=1)
async def test_api_call(self):
    # Test implementation
```

3. **Environment Issues**
```python
# Problem: Tests pass locally but fail in CI
# Solution: Use consistent environments
if os.getenv("CI"):
    # CI-specific configuration
    config.api_url = "https://test-api.example.com"
```

### Debug Strategies

1. **Enable Debug Logging**
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# In test
caplog.set_level(logging.DEBUG)
```

2. **Capture Request/Response**
```python
@pytest.fixture
def capture_http(monkeypatch):
    """Capture HTTP requests for debugging."""
    captured = []
    
    def capture_request(self, *args, **kwargs):
        captured.append({
            "method": self.method,
            "url": self.url,
            "body": self.body
        })
        return original_request(self, *args, **kwargs)
    
    monkeypatch.setattr(
        "httpx.Request.send",
        capture_request
    )
    return captured
```

3. **Test Isolation**
```python
# Ensure tests don't affect each other
@pytest.fixture(autouse=True)
def reset_state():
    """Reset global state between tests."""
    yield
    # Cleanup code
    cache.clear()
    credentials.clear()
```

### Performance Debugging

1. **Profile Slow Tests**
```python
import cProfile
import pstats

def profile_test():
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Run test
    test_function()
    
    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(10)
```

2. **Memory Profiling**
```python
from memory_profiler import profile

@profile
def test_memory_usage():
    # Test implementation
    pass
```

## Best Practices

### 1. Test Organization

- Group tests by feature
- Use descriptive names
- Keep tests focused
- Avoid test interdependence

### 2. Test Data

- Use realistic data
- Cover edge cases
- Maintain test data separately
- Version test data

### 3. Assertions

- Be specific in assertions
- Test positive and negative cases
- Include helpful error messages
- Verify side effects

### 4. Performance

- Set performance baselines
- Monitor trends
- Test under realistic load
- Profile bottlenecks

### 5. Maintenance

- Update tests with templates
- Remove obsolete tests
- Refactor duplicated code
- Document test purpose

## Next Steps

1. Set up test environment
2. Write initial test suite
3. Integrate with CI/CD
4. Monitor test metrics
5. Continuously improve coverage