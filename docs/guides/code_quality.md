# Code Quality Guidelines

## Overview

This guide outlines the code quality standards and best practices for the Coherence project. Following these guidelines ensures consistent, maintainable, and reliable code across the codebase.

## Code Formatting

### Black

All Python code should be formatted using Black with the default line length of 88 characters:

```bash
# Format a single file
black path/to/file.py

# Format all Python files in a directory
black directory_name/

# Check if files would be reformatted without changing them
black --check directory_name/
```

### Import Sorting

Use isort to organize imports consistently:

```bash
# Sort imports in a file
isort path/to/file.py
```

Imports should be grouped into three sections:
1. Standard library imports
2. Third-party imports
3. Local application imports

## Linting

### Ruff

We use Ruff for linting, which provides fast Python linting with rules from popular linters like Flake8:

```bash
# Run linting checks
ruff check src/

# Automatically fix linting issues when possible
ruff check --fix src/
```

### Key Linting Rules

- **E**: pycodestyle errors
- **F**: pyflakes errors
- **B**: flake8-bugbear error detection
- **I**: isort import sorting

### Common Issues to Watch For

1. **B904 - Exception Chaining**: Always use `raise ... from err` or `raise ... from None` in except blocks
2. **F821 - Undefined Names**: Ensure all referenced variables are defined
3. **F823 - Local variable referenced before assignment**: Initialize variables before use

## Type Checking

### Mypy

All code should include proper type annotations and pass mypy checks:

```bash
# Run type checking
mypy src/
```

### Type Annotation Guidelines

1. Annotate all function parameters and return types
2. Use `Optional[T]` for parameters that can be None
3. Use appropriate container types (e.g., `List[str]`, `Dict[str, Any]`)
4. For Django/SQLAlchemy models, follow library-specific annotation patterns

## Error Handling

### Exception Best Practices

1. **Create specific exception classes** for different error categories
2. **Chain exceptions** using `raise NewError(...) from original_error`
3. **Include context information** in exception messages
4. **Use appropriate error codes** for client-facing errors

### Example of Good Exception Handling

```python
try:
    # Attempt an operation
    result = perform_operation(input_data)
    return result
except ValueError as e:
    # Chain the exception to preserve the stack trace
    raise ValidationError("Invalid input data") from e
except ConnectionError as e:
    # Log details and raise a more specific error
    logger.error(f"Connection failed: {e}")
    raise ExternalServiceError(service_name="API", message="Connection failed") from e
```

## Testing

### Test Categories

1. **Unit Tests**: Test individual functions and classes in isolation
2. **Integration Tests**: Test interactions between components
3. **Functional Tests**: Test end-to-end functionality from a user perspective

### Test Guidelines

1. Tests should be independent and repeatable
2. Use meaningful test names that describe the expected behavior
3. Follow the Arrange-Act-Assert pattern
4. Mock external dependencies appropriately
5. For complex tests, add docstrings explaining the test purpose

## Documentation

### Docstrings

Use Google-style docstrings for all public APIs:

```python
def function_name(param1: str, param2: int) -> bool:
    """Brief description of the function.
    
    More detailed description if needed.
    
    Args:
        param1: Description of param1
        param2: Description of param2
        
    Returns:
        Description of return value
        
    Raises:
        ValueError: When and why this exception is raised
    """
```

### Comments

1. Add comments for complex logic or non-obvious implementations
2. Explain "why" more than "what" (the code should explain what it does)
3. Keep comments up-to-date with code changes

## Performance Considerations

1. **Be mindful of database queries** - minimize N+1 problems
2. **Use appropriate data structures** for your use case
3. **Consider async/await patterns** for I/O-bound operations
4. **Profile before optimizing** - fix real bottlenecks, not theoretical ones

## Pre-Commit Workflow

Before committing code, run the following checks:

```bash
# Format code
black .

# Sort imports
isort .

# Run linting
ruff check . --fix

# Run type checking
mypy src/

# Run tests
pytest

# Alternatively, use our configured pre-commit hooks
pre-commit run --all-files
```

## Continuous Integration

The CI pipeline automatically runs these checks on all pull requests. Pull requests will not be merged until all checks pass.

## Resources

- [Black documentation](https://black.readthedocs.io/en/stable/)
- [isort documentation](https://pycqa.github.io/isort/)
- [Ruff documentation](https://beta.ruff.rs/docs/)
- [mypy documentation](https://mypy.readthedocs.io/en/stable/)
- [Google Python Style Guide](https://google.github.io/styleguide/pyguide.html)