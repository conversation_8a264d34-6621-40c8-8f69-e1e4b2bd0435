# Unified Template Developer Guide

This comprehensive guide covers the development, structure, and best practices for creating enhanced unified templates in the Coherence system.

## Table of Contents

1. [Introduction](#introduction)
2. [Template Structure](#template-structure)
3. [Creating Templates](#creating-templates)
4. [Enhanced Features](#enhanced-features)
5. [CRFS Integration](#crfs-integration)
6. [Testing Templates](#testing-templates)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Introduction

Unified templates are self-contained configurations that define:
- How to match user intents
- How to execute API actions
- How to format responses
- How to handle errors
- How to test without live APIs

### Key Benefits

1. **Self-Containment**: All execution details in one place
2. **Type Safety**: Built-in validation rules
3. **Flexibility**: Multiple response formats
4. **Testability**: Mock data for development
5. **Security**: Credential management built-in

## Template Structure

### Complete Template Anatomy

```json
{
  "meta": {
    "key": "unique_template_key",
    "endpoint_id": "METHOD_/path",
    "version": 1,
    "tags": ["category", "feature"],
    "created_at": "2025-01-02T00:00:00Z",
    "updated_at": "2025-01-02T00:00:00Z"
  },
  "intent": {
    "patterns": [],
    "confidence_threshold": 0.85,
    "parameter_hints": {},
    "fallback_templates": []
  },
  "action": {
    "method": "GET|POST|PUT|DELETE",
    "path": "/api/path/{param}",
    "integration": {},
    "authentication": {},
    "headers": {},
    "body": {},
    "query_parameters": {},
    "validation_rules": {},
    "transformations": {},
    "retries": {}
  },
  "response": {
    "crfs": {},
    "error_mapping": {},
    "success_indicator": {}
  },
  "test_data": {
    "mock_responses": {},
    "sample_parameters": [],
    "error_scenarios": []
  },
  "documentation": {
    "description": "",
    "examples": [],
    "notes": []
  }
}
```

### Section Details

#### 1. Meta Section

Contains template metadata:

```json
{
  "meta": {
    "key": "weather_forecast",  // Unique identifier
    "endpoint_id": "GET_/v1/weather",  // API endpoint reference
    "version": 2,  // Template version for tracking changes
    "tags": ["weather", "api", "public"],  // Categorization
    "created_at": "2025-01-02T00:00:00Z",
    "updated_at": "2025-01-02T12:00:00Z"
  }
}
```

#### 2. Intent Section

Defines how to match user requests:

```json
{
  "intent": {
    "patterns": [
      "get weather for {location}",
      "what's the weather in {city}",
      "temperature in {place}"
    ],
    "confidence_threshold": 0.85,  // Minimum match confidence
    "parameter_hints": {
      "location": ["city", "place", "area"],  // Synonyms
      "units": ["celsius", "fahrenheit", "metric"]
    },
    "fallback_templates": ["weather_help", "location_search"]
  }
}
```

#### 3. Action Section

Configures API execution:

```json
{
  "action": {
    "method": "GET",
    "path": "/v1/weather/{location}",
    "integration": {
      "base_url": "${WEATHER_API_URL:https://api.weather.com}",
      "api_version": "v1",
      "credential_ref": "weather_api_key",
      "health_check": {
        "endpoint": "/health",
        "interval": 300,
        "timeout": 10
      }
    },
    "authentication": {
      "type": "api_key",  // or "bearer", "basic", "oauth2"
      "header": "X-API-Key",
      "value": "{{credentials.weather_api_key}}"
    },
    "headers": {
      "Content-Type": "application/json",
      "User-Agent": "Coherence/1.0"
    },
    "query_parameters": {
      "q": "{{parameters.location}}",
      "units": "{{parameters.units|default('metric')}}"
    }
  }
}
```

#### 4. Validation Rules

Define parameter constraints:

```json
{
  "validation_rules": {
    "location": {
      "type": "string",
      "required": true,
      "min_length": 1,
      "max_length": 100,
      "pattern": "^[A-Za-z\\s,]+$"
    },
    "units": {
      "type": "string",
      "enum": ["metric", "imperial"],
      "required": false,
      "default": "metric"
    }
  }
}
```

#### 5. Transformations

Process parameters before use:

```json
{
  "transformations": {
    "location": ["trim", "lowercase"],
    "email": ["trim", "lowercase", "normalize_email"],
    "phone": ["trim", "remove_non_numeric", "format_phone"]
  }
}
```

Available transformations:
- `trim`: Remove whitespace
- `lowercase`/`uppercase`: Change case
- `capitalize`: First letter uppercase
- `remove_non_numeric`: Strip non-digits
- `sanitize_html`: Remove HTML tags
- `to_cents`: Convert dollars to cents
- `to_iso8601`: Format as ISO date

#### 6. CRFS Configuration

Configure response formatting:

```json
{
  "response": {
    "crfs": {
      "type": "structured",
      "auto_select": true,
      "default_format": "structured",
      "formats": {
        "structured": {
          "sections": [
            {
              "type": "text",
              "style": "heading",
              "content": "Weather for {{parameters.location}}"
            },
            {
              "type": "keyvalue",
              "data": {
                "Temperature": "{{result.temp}}°C",
                "Conditions": "{{result.conditions}}"
              }
            }
          ]
        },
        "text/plain": {
          "template": "{{parameters.location}}: {{result.temp}}°C, {{result.conditions}}"
        }
      }
    }
  }
}
```

## Creating Templates

### Step 1: Define the Intent

Start by identifying what users might say:

```json
{
  "intent": {
    "patterns": [
      "primary pattern with {parameter}",
      "alternative pattern {param1} and {param2}",
      "simple trigger phrase"
    ]
  }
}
```

Tips:
- Use `{parameter_name}` for variables
- Include variations users might say
- Keep patterns natural and conversational

### Step 2: Configure the Action

Define how to call the API:

```json
{
  "action": {
    "method": "POST",
    "path": "/api/resource",
    "body": {
      "field1": "{{parameters.param1}}",
      "field2": "{{parameters.param2|default('value')}}"
    }
  }
}
```

### Step 3: Add Validation

Ensure parameters are valid:

```json
{
  "validation_rules": {
    "email": {
      "type": "string",
      "required": true,
      "pattern": "^[^@]+@[^@]+\\.[^@]+$",
      "error_message": "Please provide a valid email"
    }
  }
}
```

### Step 4: Configure Response Formats

Design how responses appear:

```json
{
  "crfs": {
    "formats": {
      "structured": {
        "sections": [
          {
            "type": "text",
            "content": "Result: {{result.message}}"
          }
        ]
      }
    }
  }
}
```

### Step 5: Add Test Data

Enable offline testing:

```json
{
  "test_data": {
    "mock_responses": {
      "success": {
        "status": 200,
        "data": {
          "id": 123,
          "message": "Success"
        }
      }
    },
    "sample_parameters": [
      {
        "name": "standard",
        "values": {
          "param1": "test"
        }
      }
    ]
  }
}
```

## Enhanced Features

### 1. Environment Variables

Use environment variables with defaults:

```json
{
  "integration": {
    "base_url": "${API_URL:https://default.api.com}"
  }
}
```

### 2. Dynamic Values

Generate values at runtime:

```json
{
  "headers": {
    "X-Request-ID": "{{generate_uuid()}}",
    "X-Timestamp": "{{current_timestamp()}}"
  }
}
```

### 3. Conditional Rendering

Show content based on conditions:

```json
{
  "sections": [
    {
      "type": "alert",
      "condition": "{{result.warnings}}",
      "content": "Warning: {{result.warnings[0]}}"
    }
  ]
}
```

### 4. Nested Data Access

Access nested response data:

```json
{
  "content": "User: {{result.user.profile.name}}"
}
```

### 5. Array Iteration

Loop through arrays:

```json
{
  "type": "list",
  "items": "{{result.items}}",
  "template": "{{item.name}}: {{item.value}}"
}
```

## CRFS Integration

### Response Section Types

1. **Text**
```json
{
  "type": "text",
  "style": "heading|normal|success|error|warning",
  "content": "Text content"
}
```

2. **Key-Value**
```json
{
  "type": "keyvalue",
  "data": {
    "Key": "Value",
    "Dynamic": "{{result.field}}"
  }
}
```

3. **List**
```json
{
  "type": "list",
  "style": "bullet|numbered",
  "items": "{{result.array}}",
  "template": "{{item.field}}"
}
```

4. **Table**
```json
{
  "type": "table",
  "columns": [
    {"key": "id", "label": "ID"},
    {"key": "name", "label": "Name"}
  ],
  "data": "{{result.rows}}"
}
```

5. **Actions**
```json
{
  "type": "action",
  "actions": [
    {
      "label": "Click Me",
      "action": "next_action",
      "parameters": {"id": "{{result.id}}"}
    }
  ]
}
```

### Format Auto-Selection

Enable automatic format selection:

```json
{
  "crfs": {
    "auto_select": true,
    "formats": {
      "application/json": { "raw": true },
      "text/plain": { "template": "..." },
      "text/html": { "template": "..." }
    }
  }
}
```

## Testing Templates

### 1. Using Test Mode

Test without API calls:

```python
result = await orchestrator.execute_action(
    "template_key",
    {"param": "value"},
    test_mode=True
)
```

### 2. Mock Responses

Define realistic test data:

```json
{
  "mock_responses": {
    "success": {
      "status": 200,
      "data": { "id": 1, "name": "Test" }
    },
    "error": {
      "status": 404,
      "error": { "message": "Not found" }
    }
  }
}
```

### 3. Error Scenarios

Test error handling:

```json
{
  "error_scenarios": [
    {
      "name": "missing_param",
      "parameters": {},
      "expected_error": {
        "status": 400,
        "message": "Missing required parameter"
      }
    }
  ]
}
```

### 4. Performance Data

Include performance expectations:

```json
{
  "performance_data": {
    "response_times": {
      "p50": 100,
      "p95": 500
    },
    "rate_limits": {
      "requests_per_minute": 60
    }
  }
}
```

## Best Practices

### 1. Naming Conventions

- Template keys: `resource_action_version` (e.g., `user_create_v1`)
- Parameters: `snake_case`
- Pattern variables: Match parameter names

### 2. Security

- Never hardcode credentials
- Use credential references
- Validate all inputs
- Sanitize user data
- Log security events

### 3. Error Handling

- Map all possible error codes
- Provide helpful error messages
- Include recovery actions
- Log errors appropriately

### 4. Performance

- Set appropriate timeouts
- Configure retry logic
- Use caching when possible
- Minimize payload size

### 5. Documentation

- Describe what the template does
- Provide usage examples
- Document parameters
- Note any limitations

### 6. Versioning

- Increment version for breaking changes
- Keep old versions for compatibility
- Document changes
- Test migrations

## Troubleshooting

### Common Issues

1. **Pattern Not Matching**
   - Check confidence threshold
   - Add more pattern variations
   - Verify parameter names

2. **Validation Failing**
   - Check validation rules
   - Verify parameter types
   - Test with sample data

3. **API Errors**
   - Verify credentials
   - Check base URL
   - Test with curl

4. **Response Formatting Issues**
   - Check CRFS syntax
   - Verify data paths
   - Test with mock data

### Debug Tips

1. Enable debug logging
2. Test with mock data first
3. Validate JSON structure
4. Check API documentation
5. Use validation scripts

### Getting Help

- Check example templates
- Review error logs
- Test incrementally
- Ask for assistance

## Advanced Topics

### 1. Custom Transformations

Add custom transformation functions:

```python
def custom_transform(value, context):
    # Custom logic
    return transformed_value

transformer.register("my_transform", custom_transform)
```

### 2. Dynamic Authentication

Use different auth per environment:

```json
{
  "authentication": {
    "type": "{{env.AUTH_TYPE|default('bearer')}}",
    "value": "{{credentials[env.CREDENTIAL_KEY]}}"
  }
}
```

### 3. Webhook Integration

Handle async responses:

```json
{
  "action": {
    "webhook": {
      "callback_url": "{{callback_url}}",
      "timeout": 300,
      "retry_policy": "exponential"
    }
  }
}
```

### 4. Multi-Step Actions

Chain multiple API calls:

```json
{
  "action": {
    "steps": [
      {
        "name": "authenticate",
        "method": "POST",
        "path": "/auth"
      },
      {
        "name": "fetch_data",
        "method": "GET",
        "path": "/data",
        "headers": {
          "Authorization": "{{steps.authenticate.result.token}}"
        }
      }
    ]
  }
}
```

## Migration from Legacy Templates

### 1. Identify Legacy Templates

```bash
docker-compose exec coherence-api python scripts/identify_legacy_templates.py
```

### 2. Run Migration Script

```bash
docker-compose exec coherence-api python scripts/enhance_unified_templates.py
```

### 3. Validate Migration

```bash
docker-compose exec coherence-api python scripts/validate_template_structure.py
```

### 4. Test Enhanced Templates

```bash
docker-compose exec coherence-api python scripts/test_enhanced_templates.py
```

## Next Steps

1. Review example templates in `/docs/examples/`
2. Create your first template
3. Test with mock data
4. Deploy to development
5. Monitor performance
6. Iterate and improve