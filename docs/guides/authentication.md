## 🗝️ Authentication & Authorization Guide for Coherence

This document defines a clear standard for authenticating and authorizing requests within the Coherence application, ensuring consistency across all modules and avoiding confusion during AI-assisted coding.

---

## 1. 🔐 Authentication Overview

Coherence uses **Clerk** for user authentication, leveraging Clerk JWT tokens to securely manage user identity and session state. JWT tokens include organization (tenant) information essential for multi-tenancy:

### JWT Claims Structure:

```json
{
  "org_id": "org_xxx",
  "org_name": "Organization Name",
  "org_role": "org:admin",
  "org_slug": "org-slug",
  "permissions": ["list", "of", "permissions"],
  "is_system_admin": false
}
```

---

## 2. 🌐 Authentication Flow

The authentication process follows these steps:

1. **User Authentication (Frontend)**:

   * User logs in via Clerk frontend component (`<SignIn>`).
   * Clerk provides JWT upon successful login.

2. **Session Initialization**:

   * Frontend sends JWT to `/api/auth/me`.
   * Backend validates JWT via endpoint `/v1/auth/session-info`.
   * Session context (`AdminSessionContext`) stores validated session details.

3. **Tenant Mapping**:

   * JWT's `org_id` is mapped to a Coherence tenant ID internally.
   * This tenant context (`request.state.tenant_id`) is crucial for enforcing tenant-specific data access via Row-Level Security (RLS).

---

## 3. 🛠️ Backend JWT Validation (Standard Pattern)

All backend endpoints must validate JWT tokens using `get_clerk_auth_details` dependency and apply granular permission checks using `RequirePermission`.

### Example Endpoint Template:

```python
from fastapi import Depends, Request
from coherence.api.v1.dependencies.auth import get_clerk_auth_details, RequirePermission
from coherence.permissions import CoherencePermission

async def endpoint(
    request: Request,
    auth_details: ClerkAuthDetails = Depends(get_clerk_auth_details),
    _perm_check: None = Depends(RequirePermission(CoherencePermission.SPECIFIC_PERMISSION)),
):
    tenant_id = getattr(request.state, 'tenant_id', None)
    is_system_admin = getattr(request.state, 'is_system_admin', False)
    # Your endpoint logic here
```

### ⚠️ **Deprecated Methods (Do NOT use):**

* `get_tenant_from_api_key`
* `check_admin_role`
* `get_current_tenant_id`

These methods are deprecated and replaced by the `get_clerk_auth_details` approach above.

---

## 4. 🎯 Permission Management

Permissions must be explicitly defined and verified at endpoint level via:

* **System Admin (`is_system_admin`)**: Full access bypassing tenant-specific checks.
* **Organization Admin (`org_role`)**: Access scoped to specific organizational resources.
* **Custom Permissions**: Granular permissions provided by Clerk JWT and enforced by `RequirePermission`.

### Permission Enforcement Example:

```python
_perm_check: None = Depends(RequirePermission(CoherencePermission.INTEGRATION_MANAGE))
```

---

## 5. 🔄 Frontend Integration & Middleware (Next.js)

Frontend must consistently use Clerk middleware to protect routes, handle redirection, and enforce organization selection:

* Use `<ClerkProvider>` in the root app component.
* Employ Clerk hooks (`useAuth`, `useOrganization`, `useOrganizationList`) for state and context.
* Middleware ensures unauthenticated users redirect to Clerk’s sign-in page.
* Authenticated users without an active organization redirect to `/org-selection`.

### Middleware Configuration Example (`middleware.ts`):

```typescript
import { authMiddleware } from '@clerk/nextjs';

export default authMiddleware({
  publicRoutes: ['/login', '/signup'],
  ignoredRoutes: ['/api/auth/callback'],
});
```

---

## 6. 🚨 Known Issues & Fixes

* **401 JWT Template Errors**:

  * **Issue**: JWT template mismatch causing 401 errors.
  * **Fix**: Ensure JWT template in `.env` matches backend expectations (`coherence_session`).
  * Template Claims:

    ```json
    {
      "__session": {
        "org_id": "{{org.id}}",
        "org_name": "{{org.name}}",
        "org_role": "{{org.role}}",
        "org_slug": "{{org.slug}}"
      }
    }
    ```

* **`jwk-kid-mismatch`**:

  * **Issue**: JWT verification failing due to stale key sets.
  * **Fix**: Use Clerk’s official SDK for JWT validation instead of custom logic.

---

## 7. 🔑 API Key Management (Tenant & System Level)

Each tenant has scoped API keys stored securely:

* API keys linked to Clerk organizations.
* Hashed in database with plaintext displayed once at creation.
* Scoped via permissions for granular access control.

---

## 8. 🔒 Credential Encryption & OAuth

All sensitive credentials (e.g., OAuth tokens, API keys) must use the CredentialManager’s AES-GCM encryption mechanism:

* Credentials stored encrypted in database.
* AES-GCM for secure encryption and authenticated integrity checks.
* Transparent token refresh for OAuth2 integrations.

---

## 9. 📂 File Structure & Important Files

### Frontend:

* Auth Context: `src/context/AdminSessionContext.tsx`
* Auth Middleware: `middleware.ts`
* JWT proxy to backend: `src/app/api/auth/me/route.ts`

### Backend:

* JWT validation & permissions: `src/coherence/api/v1/dependencies/auth.py`
* Session Endpoint: `src/coherence/api/v1/endpoints/session_info.py`
* SQLAlchemy models: `src/coherence/models/`
* CRUD operations: `src/coherence/crud/`

---

## 10. ✅ Development & Debugging Tools

* Check JWT in browser: `debugClerkToken()`

* Organization selector: `/org-selection`

* Auth debug route: `/debug/auth`

* Logs:

  ```bash
  docker-compose logs coherence-admin
  docker-compose logs coherence-api
  ```

* Quick health check scripts for auth errors:

  ```bash
  ./debug_scripts/quick_check.sh
  python debug_scripts/check_auth_status.py
  python debug_scripts/check_integration_permissions.py
  ```

---

## 11. 🚧 Integration with New Modules (Standardized Checklist)

When adding new functionality:

1. Define permissions clearly and implement with `RequirePermission`.
2. Always retrieve tenant context (`request.state.tenant_id`).
3. Ensure JWT validation uses `get_clerk_auth_details`.
4. Encrypt sensitive credentials using CredentialManager.
5. Validate middleware protection on all new frontend routes.

---

## ✅ Conclusion & Enforcement

This document serves as the definitive standard for implementing and managing authentication and authorization in Coherence. Adherence ensures security, scalability, and seamless integration across all modules and development workflows.

---

You should reference this guide rigorously during all future AI-assisted development to ensure consistent application of authentication and authorization best practices.
