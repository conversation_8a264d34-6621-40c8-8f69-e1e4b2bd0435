# Coherence Guides

This directory contains user and developer guides for the Coherence middleware platform.

## Available Guides

1. **[Getting Started Guide](./getting_started.md)**
   - Quick setup instructions
   - First-time usage walkthrough
   - Basic configuration

2. **[Error Handling Guide](./error_handling.md)**
   - Error taxonomy
   - Handling strategies
   - Custom error handlers

3. **[Template System Guide](./template_system.md)**
   - Template architecture
   - Inheritance model
   - Creating and managing templates

4. **[Monitoring Guide](./monitoring.md)**
   - Metrics collection
   - Dashboard configuration
   - Alerting setup

5. **[PostgreSQL Session Variables Guide](./postgresql_session_variables.md)**
   - Working with session variables
   - Row-Level Security (RLS) implementation
   - Handling NULL and Boolean values

## Coming Soon

The following guides are planned for future development:

1. **API Documentation**
   - Detailed API reference
   - Request/response formats
   - Authentication and error handling

2. **Developer Guide**
   - Custom action development
   - Extension points
   - Best practices

3. **OpenAPI Integration Guide**
   - Connecting to external APIs
   - Authentication configuration
   - Intent mapping strategies

4. **Troubleshooting Guide**
   - Common issues and solutions
   - Logging and diagnostics
   - Performance optimization

5. **Administrator Guide**
   - Tenant management
   - User access control
   - System monitoring

6. **Security Guide**
   - Security best practices
   - Authentication methods
   - Data protection

## Using These Guides

These guides are designed to be read in sequence for new users, or referenced individually for specific tasks. The [Getting Started Guide](./getting_started.md) is the recommended starting point for all users.

## Contributing to Documentation

When adding or updating guides:

1. Use clear, concise language
2. Include practical examples
3. Add screenshots or diagrams where helpful
4. Maintain consistent formatting
5. Link to related guides and resources

For questions about documentation or to suggest improvements, please contact the project maintainers.