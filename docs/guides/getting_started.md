# Getting Started with Coherence

This guide will help you set up and start using Coherence, the middleware that transforms natural language into deterministic actions.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Docker** and **Docker Compose** (for local development)
- **Python 3.10+** (for direct installation)
- **Git** (for cloning the repository)
- **An OpenAI API key** (or other supported LLM provider)

## Installation Options

### Option 1: Docker Setup (Recommended)

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/coherence.git
   cd coherence
   ```

2. **Create environment variables file**

   Create a `.env` file in the project root with the following content:

   ```
   COHERENCE_OPENAI_API_KEY=your_openai_api_key
   COHERENCE_POSTGRES_PASSWORD=secure_password
   ```

3. **Start the Docker containers**

   ```bash
   docker-compose up -d
   ```

4. **Run database migrations**

   ```bash
   docker-compose exec backend alembic upgrade head
   ```

5. **Create a tenant and API key**

   ```bash
   docker-compose exec backend python -m scripts.create_tenant --name "Default" --industry "General"
   ```

   Note the API key that is generated - you'll need it for making requests.

### Option 2: Direct Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/coherence.git
   cd coherence
   ```

2. **Create a virtual environment**

   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**

   ```bash
   pip install poetry
   poetry install
   ```

4. **Set up external services**

   You'll need to install and configure:
   - PostgreSQL
   - Redis
   - Qdrant

5. **Configure environment variables**

   Create a `.env` file or set environment variables for your configuration.

6. **Run database migrations**

   ```bash
   alembic upgrade head
   ```

7. **Start the application**

   ```bash
   uvicorn coherence.main:app --host 0.0.0.0 --port 8000 --reload
   ```

## Your First Request

Once Coherence is running, you can make your first request to resolve an intent:

```bash
curl -X POST http://localhost:8000/v1/resolve \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "user_id": "********-0000-0000-0000-************",
    "role": "user",
    "message": "Hello world"
  }'
```

## Next Steps

### Creating Templates

Templates define how Coherence processes intents. To create a custom template:

```bash
curl -X POST http://localhost:8000/v1/admin/templates \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "key": "INTENT_ROUTER_V1",
    "category": "intent_router",
    "body": "You are a routing engine that maps user messages to intents...",
    "actions": [
      {
        "action_type": "greeting",
        "description": "Respond to greetings",
        "parameters": []
      }
    ]
  }'
```

### Integrating with OpenAPI

To connect Coherence to an external API:

1. Upload your OpenAPI specification:

   ```bash
   curl -X POST http://localhost:8000/v1/admin/integrations \
     -H "Content-Type: application/json" \
     -H "X-API-Key: your_api_key" \
     -d '{
       "name": "My API",
       "openapi_spec": {
         "openapi": "3.0.0",
         "info": { "title": "My API", "version": "1.0.0" },
         "paths": {
           "/users": {
             "get": {
               "operationId": "getUsers",
               "responses": { "200": { "description": "OK" } }
             }
           }
         }
       }
     }'
   ```

2. Map intents to endpoints and configure authentication.

### Developing Custom Actions

For custom action development, refer to the [Developer Guide](./developer_guide.md).

## Monitoring

Coherence includes comprehensive monitoring:

- **Prometheus metrics**: Available at `http://localhost:9090`
- **Grafana dashboards**: Available at `http://localhost:3000` 
  (default login: admin/admin)

## Troubleshooting

### Common Issues

1. **API Key Authentication Failures**
   - Ensure you're using the correct API key
   - Check that the tenant exists and hasn't been deleted

2. **Vector Matching Issues**
   - Ensure you've indexed your intents 
   - Check vector threshold configuration

3. **Database Connection Errors**
   - Verify PostgreSQL is running
   - Check connection string in environment variables

For more help, refer to the full [Troubleshooting Guide](./troubleshooting.md).

## Additional Resources

- [Architecture Overview](../architecture/system_architecture.md)
- [API Documentation](./api_documentation.md)
- [Template Development Guide](./template_development.md)
- [OpenAPI Integration Guide](./openapi_integration.md)