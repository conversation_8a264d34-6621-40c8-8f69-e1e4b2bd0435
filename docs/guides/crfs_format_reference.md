# CRFS (Coherence Response Formatting System) Reference

## Overview

CRFS (Coherence Response Formatting System) is a flexible, multi-format response rendering system that enables templates to define how API responses should be presented to users. It supports multiple output formats, conditional rendering, and rich UI components.

## Table of Contents

1. [Core Concepts](#core-concepts)
2. [Configuration Structure](#configuration-structure)
3. [Section Types](#section-types)
4. [Format Types](#format-types)
5. [Template Syntax](#template-syntax)
6. [Conditional Rendering](#conditional-rendering)
7. [Custom Filters](#custom-filters)
8. [Examples](#examples)
9. [Best Practices](#best-practices)

## Core Concepts

### What is CRFS?

CRFS transforms raw API responses into user-friendly formats by:
- Supporting multiple output formats (structured, plain text, JSON, CSV, etc.)
- Providing rich UI components (tables, lists, key-value pairs)
- Enabling conditional rendering based on response data
- Auto-selecting formats based on client preferences

### Key Features

1. **Multi-Format Support**: Define different representations for the same data
2. **Auto-Selection**: Automatically choose format based on Accept headers
3. **Rich Components**: Tables, lists, actions, alerts, and more
4. **Template Engine**: Jinja2-based templating with custom filters
5. **Error Handling**: Specific formatting for error responses

## Configuration Structure

### Basic Structure

```json
{
  "response": {
    "crfs": {
      "type": "structured",
      "auto_select": true,
      "default_format": "structured",
      "formats": {
        "structured": { /* ... */ },
        "text/plain": { /* ... */ },
        "application/json": { /* ... */ }
      }
    },
    "error_mapping": {
      "404": "Resource not found",
      "500": "Internal server error"
    },
    "success_indicator": {
      "status_codes": [200, 201],
      "required_fields": ["id", "status"]
    }
  }
}
```

### Configuration Options

| Field | Type | Description |
|-------|------|-------------|
| `type` | string | Response type (structured, raw, template) |
| `auto_select` | boolean | Enable automatic format selection |
| `default_format` | string | Default format when auto-select is disabled |
| `formats` | object | Format definitions keyed by content type |

## Section Types

### 1. Text Section

Display text with optional styling:

```json
{
  "type": "text",
  "style": "heading|subheading|normal|success|error|warning|info",
  "content": "Your text here with {{variables}}"
}
```

**Styles:**
- `heading`: Large, bold text
- `subheading`: Medium, semi-bold text
- `normal`: Regular text
- `success`: Green text indicating success
- `error`: Red text indicating errors
- `warning`: Orange text for warnings
- `info`: Blue text for information

### 2. Key-Value Section

Display data as key-value pairs:

```json
{
  "type": "keyvalue",
  "style": "default|compact|highlight",
  "data": {
    "Name": "{{result.name}}",
    "Email": "{{result.email}}",
    "Status": "{{result.status|capitalize}}"
  }
}
```

**Styles:**
- `default`: Standard spacing
- `compact`: Reduced spacing
- `highlight`: Emphasized display

### 3. List Section

Display arrays as formatted lists:

```json
{
  "type": "list",
  "style": "bullet|numbered|checklist",
  "items": "{{result.items}}",
  "template": "{{item.name}}: {{item.description}}",
  "empty_message": "No items found"
}
```

**Styles:**
- `bullet`: • Item
- `numbered`: 1. Item
- `checklist`: ☐ Item (or ☑ if item.completed)

### 4. Table Section

Display tabular data:

```json
{
  "type": "table",
  "columns": [
    {
      "key": "id",
      "label": "ID",
      "width": "10%",
      "sortable": true
    },
    {
      "key": "name",
      "label": "Name",
      "width": "40%",
      "format": "link"
    },
    {
      "key": "status",
      "label": "Status",
      "width": "20%",
      "format": "badge"
    },
    {
      "key": "created_at",
      "label": "Created",
      "width": "30%",
      "format": "relative_time"
    }
  ],
  "data": "{{result.rows}}",
  "actions": [
    {
      "label": "View",
      "action": "view_item",
      "parameters": {
        "id": "{{row.id}}"
      }
    }
  ],
  "empty_message": "No data available"
}
```

**Column Formats:**
- `link`: Renders as clickable link
- `badge`: Styled badge/chip
- `status`: Status indicator with color
- `relative_time`: "2 hours ago"
- `currency`: Formatted currency
- `percentage`: Formatted percentage

### 5. Action Section

Interactive buttons/links:

```json
{
  "type": "action",
  "actions": [
    {
      "label": "Primary Action",
      "action": "template_key",
      "parameters": {
        "id": "{{result.id}}"
      },
      "style": "primary"
    },
    {
      "label": "Secondary",
      "url": "{{result.link}}",
      "style": "secondary"
    },
    {
      "label": "Cancel",
      "action": "cancel",
      "style": "danger"
    }
  ]
}
```

**Action Styles:**
- `primary`: Primary button (blue)
- `secondary`: Secondary button (gray)
- `success`: Success button (green)
- `danger`: Danger button (red)
- `link`: Text link

### 6. Alert Section

Conditional alerts:

```json
{
  "type": "alert",
  "condition": "{{result.warning}}",
  "style": "warning|error|info|success",
  "content": "{{result.warning_message}}",
  "dismissible": true
}
```

### 7. Divider Section

Visual separator:

```json
{
  "type": "divider",
  "style": "solid|dashed|dotted"
}
```

### 8. Pagination Section

Navigation for paginated results:

```json
{
  "type": "pagination",
  "current_page": "{{result.meta.current_page}}",
  "total_pages": "{{result.meta.total_pages}}",
  "per_page": "{{result.meta.per_page}}",
  "total_items": "{{result.meta.total}}",
  "actions": {
    "first": "list_items",
    "previous": "list_items",
    "next": "list_items",
    "last": "list_items"
  }
}
```

### 9. Progress Section

Show progress indicators:

```json
{
  "type": "progress",
  "value": "{{result.progress}}",
  "max": 100,
  "label": "{{result.progress}}% Complete",
  "style": "bar|circle"
}
```

### 10. Code Section

Display formatted code:

```json
{
  "type": "code",
  "language": "json|python|javascript|bash",
  "content": "{{result.code}}"
}
```

## Format Types

### 1. Structured Format

Rich UI with sections:

```json
{
  "structured": {
    "sections": [
      /* Array of section objects */
    ]
  }
}
```

### 2. Plain Text Format

Simple text output:

```json
{
  "text/plain": {
    "template": "Result: {{result.value}}\nStatus: {{result.status}}"
  }
}
```

### 3. JSON Format

Raw or transformed JSON:

```json
{
  "application/json": {
    "raw": true  // Return raw response
  }
}
```

Or with transformation:

```json
{
  "application/json": {
    "raw": false,
    "transform": {
      "id": "{{result.id}}",
      "name": "{{result.name}}",
      "computed": "{{result.value * 2}}"
    }
  }
}
```

### 4. CSV Format

Comma-separated values:

```json
{
  "text/csv": {
    "template": "ID,Name,Email\n{{#each result.users}}{{this.id}},\"{{this.name}}\",{{this.email}}\n{{/each}}"
  }
}
```

### 5. Markdown Format

Formatted markdown:

```json
{
  "text/markdown": {
    "template": "# {{result.title}}\n\n{{result.description}}\n\n## Details\n\n- **ID**: {{result.id}}\n- **Status**: {{result.status}}"
  }
}
```

### 6. HTML Format

HTML output:

```json
{
  "text/html": {
    "template": "<h1>{{result.title}}</h1><p>{{result.description}}</p>"
  }
}
```

## Template Syntax

### Variable Access

```
{{variable}}                    // Basic variable
{{result.nested.value}}         // Nested access
{{array[0]}}                    // Array index
{{object['key-with-dash']}}     // Bracket notation
```

### Filters

```
{{value|upper}}                 // Uppercase
{{value|lower}}                 // Lowercase
{{value|capitalize}}            // Capitalize first letter
{{value|default('N/A')}}        // Default value
{{value|truncate(50)}}          // Truncate to 50 chars
{{value|format_currency}}       // Format as currency
{{value|timestamp_to_date}}     // Format timestamp
{{value|json}}                  // JSON stringify
```

### Control Structures

```
{{#if condition}}               // Conditional
  Content when true
{{else}}
  Content when false
{{/if}}

{{#each array}}                 // Loop
  {{this.property}}             // Current item
  {{@index}}                    // Current index
{{/each}}

{{#unless condition}}           // Negative conditional
  Content when false
{{/unless}}
```

## Conditional Rendering

### Section Conditions

Show/hide sections based on data:

```json
{
  "type": "alert",
  "condition": "{{result.errors.length > 0}}",
  "content": "{{result.errors.length}} errors found"
}
```

### Complex Conditions

```json
{
  "condition": "{{result.status == 'pending' && result.retry_count < 3}}",
  "content": "Processing... (Attempt {{result.retry_count + 1}})"
}
```

### Conditional Styles

```json
{
  "type": "text",
  "style": "{{#if result.success}}success{{else}}error{{/if}}",
  "content": "Operation {{#if result.success}}completed{{else}}failed{{/if}}"
}
```

## Custom Filters

### Built-in Filters

| Filter | Description | Example |
|--------|-------------|---------|
| `capitalize` | Capitalize first letter | `{{name\|capitalize}}` |
| `upper` | Convert to uppercase | `{{code\|upper}}` |
| `lower` | Convert to lowercase | `{{email\|lower}}` |
| `truncate(n)` | Truncate to n characters | `{{text\|truncate(50)}}` |
| `default(value)` | Default if empty | `{{name\|default('Unknown')}}` |
| `format_currency` | Format as currency | `{{price\|format_currency}}` |
| `format_percentage` | Format as percentage | `{{rate\|format_percentage}}` |
| `timestamp_to_date` | Format timestamp as date | `{{created\|timestamp_to_date}}` |
| `timestamp_to_relative` | Relative time | `{{updated\|timestamp_to_relative}}` |
| `humanize` | Make human-readable | `{{status\|humanize}}` |
| `pluralize` | Pluralize word | `{{count}} item{{count\|pluralize}}` |
| `join(sep)` | Join array | `{{tags\|join(', ')}}` |
| `length` | Get length | `{{items\|length}}` |
| `json` | JSON stringify | `{{data\|json}}` |

### Creating Custom Filters

```python
# In your template processor
def format_phone(value):
    """Format phone number"""
    # Implementation
    return formatted_phone

# Register filter
formatter.add_filter('format_phone', format_phone)
```

## Examples

### Example 1: Success Response

```json
{
  "crfs": {
    "type": "structured",
    "formats": {
      "structured": {
        "sections": [
          {
            "type": "text",
            "style": "heading",
            "content": "✅ Operation Successful"
          },
          {
            "type": "keyvalue",
            "data": {
              "ID": "{{result.id}}",
              "Status": "{{result.status|capitalize}}",
              "Created": "{{result.created_at|timestamp_to_relative}}"
            }
          },
          {
            "type": "action",
            "actions": [
              {
                "label": "View Details",
                "action": "view_details",
                "parameters": {"id": "{{result.id}}"},
                "style": "primary"
              }
            ]
          }
        ]
      }
    }
  }
}
```

### Example 2: Error Response

```json
{
  "crfs": {
    "formats": {
      "structured": {
        "sections": [
          {
            "type": "text",
            "style": "error",
            "content": "❌ Operation Failed"
          },
          {
            "type": "alert",
            "style": "error",
            "content": "{{error.message}}"
          },
          {
            "type": "list",
            "condition": "{{error.details}}",
            "style": "bullet",
            "items": "{{error.details}}",
            "template": "{{item}}"
          }
        ]
      }
    }
  }
}
```

### Example 3: Data Table

```json
{
  "crfs": {
    "formats": {
      "structured": {
        "sections": [
          {
            "type": "text",
            "style": "heading",
            "content": "User List"
          },
          {
            "type": "table",
            "columns": [
              {"key": "name", "label": "Name", "width": "30%"},
              {"key": "email", "label": "Email", "width": "30%"},
              {"key": "role", "label": "Role", "width": "20%", "format": "badge"},
              {"key": "status", "label": "Status", "width": "20%", "format": "status"}
            ],
            "data": "{{result.users}}"
          },
          {
            "type": "pagination",
            "current_page": "{{result.page}}",
            "total_pages": "{{result.total_pages}}"
          }
        ]
      }
    }
  }
}
```

## Best Practices

### 1. Format Selection

- Use `auto_select` for APIs serving multiple clients
- Provide at least structured and plain text formats
- Include raw JSON for API consumers

### 2. Error Handling

- Map all possible error codes
- Provide helpful error messages
- Include recovery actions when possible

### 3. Performance

- Minimize template complexity
- Use conditions sparingly
- Cache formatted responses when possible

### 4. Accessibility

- Include text alternatives
- Use semantic section types
- Provide clear labels

### 5. Consistency

- Use consistent styling across templates
- Follow naming conventions
- Maintain similar structures

### 6. Testing

- Test all format types
- Verify conditional rendering
- Check edge cases (empty data, errors)

## Troubleshooting

### Common Issues

1. **Template Syntax Errors**
   - Check for unclosed tags
   - Verify variable names
   - Test with mock data

2. **Missing Data**
   - Use default filters
   - Add null checks
   - Handle empty arrays

3. **Format Not Rendering**
   - Verify format key matches content type
   - Check auto_select configuration
   - Test Accept headers

### Debug Tips

1. Enable template debugging
2. Log rendered output
3. Test incrementally
4. Use validation tools

## Advanced Features

### 1. Dynamic Sections

Generate sections based on data:

```json
{
  "sections": [
    {
      "type": "text",
      "content": "Results"
    },
    "{{#each result.categories}}",
    {
      "type": "text",
      "style": "subheading",
      "content": "{{this.name}}"
    },
    {
      "type": "list",
      "items": "{{this.items}}",
      "template": "{{item.title}}"
    },
    "{{/each}}"
  ]
}
```

### 2. Nested Templates

Include templates within templates:

```json
{
  "type": "include",
  "template": "common_footer",
  "context": {
    "timestamp": "{{result.timestamp}}"
  }
}
```

### 3. Custom Components

Register custom section types:

```python
@formatter.register_section('chart')
def render_chart(data, config):
    # Custom chart rendering
    return rendered_html
```

## Migration Guide

### From Raw Responses

1. Identify response structure
2. Map fields to sections
3. Add formatting
4. Test output

### From Legacy Templates

1. Convert to CRFS structure
2. Update template syntax
3. Add multiple formats
4. Validate output