# PostgreSQL Session Variables Guide

## Introduction

This guide explains how PostgreSQL session variables are handled in Coherence, particularly in the Row-Level Security (RLS) implementation. Following best practices for session variables is critical to ensure proper multi-tenant isolation and security.

## Session Variable Basics

PostgreSQL session variables allow storing state for the duration of a database connection. In Coherence, we use them to track:

- Current tenant ID (`app.current_tenant_id`)
- System admin status (`app.is_system_admin`)

## Common Pitfalls

When working with PostgreSQL session variables, be aware of these common issues:

1. **NULL Values**: Unlike regular SQL, PostgreSQL's `SET` command requires NULL values to be represented as the string 'NULL'. A literal NULL will cause errors.

2. **Boolean Values**: Session variables store booleans as strings ('true' or 'false'), not as actual boolean values.

3. **Reading Variables**: When reading session variables with `current_setting()`, always use the second parameter (`missing_ok=TRUE`) to handle cases where the variable is not set.

## Proper Usage

### Setting Variables

```sql
-- CORRECT way to set NULL value
SET app.current_tenant_id = 'NULL';  -- Note: String 'NULL', not actual NULL

-- CORRECT way to set boolean value
SET app.is_system_admin = 'true';    -- Note: String 'true', not boolean TRUE

-- INCORRECT examples
SET app.current_tenant_id = NULL;    -- Will cause errors
SET app.is_system_admin = true;      -- Will cause errors
```

### Reading Variables

```sql
-- CORRECT way to read session variables (with proper NULL handling)
SELECT current_setting('app.current_tenant_id', TRUE);

-- Proper boolean interpretation
IF current_setting('app.is_system_admin', TRUE) = 'true' THEN
    -- System admin logic
END IF;
```

## RLS Functions

Coherence uses two key functions for RLS:

### current_tenant_id()

Returns the UUID of the current tenant or NULL if not set.

```sql
CREATE OR REPLACE FUNCTION current_tenant_id()
RETURNS UUID AS $$
DECLARE
    tenant_id TEXT;
BEGIN
    tenant_id := current_setting('app.current_tenant_id', TRUE);
    -- Handle the case where the setting is 'NULL' or NULL
    IF tenant_id IS NULL OR tenant_id = 'NULL' OR tenant_id = '' THEN
        RETURN NULL;
    END IF;
    RETURN tenant_id::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;
```

### is_system_admin()

Returns TRUE if the current user is a system admin, FALSE otherwise.

```sql
CREATE OR REPLACE FUNCTION is_system_admin()
RETURNS BOOLEAN AS $$
DECLARE
    is_admin TEXT;
BEGIN
    is_admin := current_setting('app.is_system_admin', TRUE);
    -- Handle case where the setting is 'true', 'false', NULL, or ''
    IF is_admin IS NULL OR is_admin = '' THEN
        RETURN FALSE;
    ELSIF is_admin = 'true' THEN
        RETURN TRUE;
    ELSIF is_admin = 'false' THEN
        RETURN FALSE;
    ELSE
        -- Try to cast to boolean if it's not one of the expected values
        RETURN is_admin::BOOLEAN;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
```

## Best Practices

1. **Always Use String Values**: When setting session variables, always use string values ('NULL', 'true', 'false').

2. **Handle Missing Values**: When retrieving session variables, always use the `missing_ok=TRUE` parameter.

3. **Validate Types**: When retrieving typed values (UUIDs, booleans), always validate and properly convert the string representations.

4. **Test Null Cases**: Always test both cases: when a variable is not set, and when it's set to the string 'NULL'.

5. **Error Handling**: Include proper exception handling in PL/pgSQL functions that interact with session variables.

## Common Errors

- `ERROR: unrecognized configuration parameter "app.current_tenant_id"` - Variable was not set before being read
- `ERROR: invalid input syntax for type uuid` - Trying to convert 'NULL' string to UUID without proper handling
- `ERROR: invalid input syntax for type boolean` - Improper string representation of boolean

## See Also

- [PostgreSQL Documentation: SET](https://www.postgresql.org/docs/current/sql-set.html)
- [PostgreSQL Documentation: current_setting](https://www.postgresql.org/docs/current/functions-admin.html#FUNCTIONS-ADMIN-SET)
- [Row-Level Security Implementation](/docs/implementation_plans/implementation_plan_with_existing_code.md)