# Template Migration and Enhancement Guide

This guide covers the migration and enhancement tools introduced in Phase 7 of the unified template enhancement implementation.

## Overview

The template migration and enhancement tools help transform existing unified templates to include:
- Integration configuration with base URLs
- Validation rules from parameter schemas  
- CRFS auto-select configuration
- Test data for development
- Parameter transformations

## Migration Tools

### 1. Template Enhancement Script

The main enhancement script (`scripts/enhance_unified_templates.py`) automatically enhances all unified templates in the database.

#### Usage

```bash
# Run from within Docker container
docker-compose exec coherence-api python scripts/enhance_unified_templates.py
```

#### What It Does

1. **Adds Integration Configuration**
   - Extracts base URL from template or integration
   - Determines API version from path
   - Links credential references
   - Adds health check configuration

2. **Generates Validation Rules**
   - Creates rules from parameter schemas
   - Supports string, number, array validations
   - Includes pattern matching and constraints
   - Marks required fields

3. **Adds Parameter Transformations**
   - Trim for all strings
   - Lowercase for emails and URLs
   - Case transformations based on patterns
   - Format-specific transformations

4. **Enhances CRFS Configuration**
   - Enables auto-selection
   - Adds multiple format options
   - Includes error mappings
   - Sets default formats

5. **Generates Test Data**
   - Mock API responses
   - Sample parameter values
   - Success and error scenarios

#### Example Enhancement

Before enhancement:
```json
{
  "action": {
    "method": "GET",
    "path": "/users/{id}"
  },
  "parameters": {
    "id": {
      "type": "string",
      "format": "uuid",
      "required": true
    }
  }
}
```

After enhancement:
```json
{
  "action": {
    "method": "GET",
    "path": "/users/{id}",
    "integration": {
      "base_url": "${API_BASE_URL}",
      "api_version": "v1",
      "credential_ref": "api_key",
      "health_check": {
        "endpoint": "/health",
        "interval": 300
      }
    },
    "validation_rules": {
      "id": {
        "type": "string",
        "required": true
      }
    },
    "transformations": {
      "id": ["trim"]
    }
  }
}
```

### 2. Test Data Generator

The test data generator (`scripts/generate_template_test_data.py`) creates comprehensive test data for templates.

#### Usage

```bash
docker-compose exec coherence-api python scripts/generate_template_test_data.py
```

#### Generated Test Data

1. **Sample Parameters**
   - Standard case (typical values)
   - Minimal case (only required fields)
   - Maximal case (all fields with max values)

2. **Mock Responses**
   - Success responses
   - Empty responses
   - Partial responses
   - Error responses

3. **Error Scenarios**
   - Missing required parameters
   - Invalid parameter types
   - Authentication failures
   - Resource not found

4. **Edge Cases**
   - Empty strings
   - Maximum length values
   - Special characters
   - Zero/negative numbers
   - Empty arrays

5. **Performance Data**
   - Response time percentiles
   - Throughput metrics
   - Rate limits

#### Example Test Data

```json
{
  "sample_parameters": [
    {
      "name": "standard",
      "values": {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "email": "<EMAIL>",
        "age": 30
      }
    },
    {
      "name": "minimal",
      "values": {
        "id": "550e8400-e29b-41d4-a716-446655440000"
      }
    }
  ],
  "mock_responses": {
    "success": {
      "status": 200,
      "data": {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "name": "John Doe",
        "email": "<EMAIL>"
      }
    },
    "error": {
      "status": 404,
      "error": {
        "code": "NOT_FOUND",
        "message": "User not found"
      }
    }
  }
}
```

### 3. Template Structure Validator

The validator (`scripts/validate_template_structure.py`) checks templates for completeness and correctness.

#### Usage

```bash
# Validate all templates
docker-compose exec coherence-api python scripts/validate_template_structure.py

# Validate with automatic fixes
docker-compose exec coherence-api python scripts/validate_template_structure.py --fix

# Validate specific template
docker-compose exec coherence-api python scripts/validate_template_structure.py --template-key weather_forecast
```

#### Validation Checks

1. **Basic Structure**
   - Required fields (key, category)
   - JSONB field presence
   - Proper categorization

2. **Intent Configuration**
   - Pattern presence and validity
   - Confidence threshold range
   - Pattern length limits

3. **Action Configuration**
   - Valid HTTP methods
   - Path format and parameters
   - Integration configuration
   - Authentication setup

4. **Response Format**
   - CRFS configuration
   - Format definitions
   - Error mappings

5. **Parameter Mappings**
   - Undefined parameter references
   - Required parameter coverage
   - Type consistency

6. **Credential References**
   - Matching credential names
   - Valid reference format

7. **Test Data**
   - Mock response presence
   - Sample parameter completeness
   - Required field coverage

#### Validation Output

```
Template: weather_forecast
  ✅ Valid

Template: user_api
  ❌ Invalid

Errors (3):
  ❌ Missing 'patterns' in intent configuration
  ❌ Path parameter 'id' not defined in parameters
  ❌ Missing 'base_url' in integration configuration

Warnings (2):
  ⚠️  Missing CRFS auto_select configuration
  ⚠️  No error_mapping defined

Info (1):
  ℹ️  No test data present
```

## Migration Process

### Step 1: Backup Current Templates

Before running migrations, backup your templates:

```bash
docker-compose exec coherence-api python -c "
from src.coherence.db.session import get_db_session
from src.coherence.models.template import Template
import json
import asyncio

async def backup():
    async with get_db_session() as db:
        templates = await db.query(Template).all()
        with open('templates_backup.json', 'w') as f:
            json.dump([t.to_dict() for t in templates], f, indent=2)

asyncio.run(backup())
"
```

### Step 2: Validate Current State

Check the current state of templates:

```bash
docker-compose exec coherence-api python scripts/validate_template_structure.py
```

### Step 3: Run Enhancement

Enhance all templates:

```bash
docker-compose exec coherence-api python scripts/enhance_unified_templates.py
```

### Step 4: Generate Test Data

Add test data to templates:

```bash
docker-compose exec coherence-api python scripts/generate_template_test_data.py
```

### Step 5: Validate Results

Verify the enhancement was successful:

```bash
docker-compose exec coherence-api python scripts/validate_template_structure.py
```

## Best Practices

### 1. Incremental Enhancement

- Run enhancement on a subset first
- Monitor for any issues
- Roll out to all templates

### 2. Custom Enhancements

For templates needing custom configuration:

```python
# In enhance_unified_templates.py
if template.key == "special_template":
    # Add custom configuration
    enhanced["action"]["special_config"] = {...}
```

### 3. Validation Rules

Ensure validation rules match your API requirements:

```json
{
  "validation_rules": {
    "email": {
      "type": "string",
      "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
      "required": true
    }
  }
}
```

### 4. Test Data Quality

Generate realistic test data:

```python
# Use Faker for realistic data
from faker import Faker
fake = Faker()

test_data = {
    "name": fake.name(),
    "email": fake.email(),
    "address": fake.address()
}
```

## Troubleshooting

### Enhancement Failures

If enhancement fails:

1. Check logs for specific errors
2. Validate template structure first
3. Fix issues manually if needed
4. Re-run enhancement

### Missing Fields

If fields are missing after enhancement:

1. Check if template was already enhanced
2. Verify source data exists
3. Run with debug logging
4. Manually add missing fields

### Test Data Issues

If test data is incorrect:

1. Verify parameter schemas
2. Check format specifications
3. Review generation logic
4. Add custom test data

## Advanced Usage

### Custom Validators

Add custom validation logic:

```python
class CustomValidator(TemplateStructureValidator):
    def _validate_custom_rules(self, template, result):
        # Add domain-specific validation
        if template.key.startswith("payment_"):
            if "pci_compliance" not in template.action_config:
                result.add_error("Payment templates must include PCI compliance")
```

### Batch Processing

Process templates in batches:

```python
async def enhance_batch(template_keys):
    enhancer = TemplateEnhancer()
    for key in template_keys:
        template = await get_template_by_key(key)
        await enhancer._enhance_template(template)
```

### Integration with CI/CD

Add to your CI/CD pipeline:

```yaml
# .github/workflows/template-validation.yml
- name: Validate Templates
  run: |
    docker-compose exec coherence-api python scripts/validate_template_structure.py
```

## Next Steps

After migrating templates:

1. Test enhanced templates with real API calls
2. Monitor performance improvements
3. Update documentation
4. Train team on new features
5. Plan for ongoing maintenance