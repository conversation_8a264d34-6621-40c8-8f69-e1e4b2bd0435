# Coherence Monitoring Guide

This guide describes the monitoring capabilities of the Coherence application, including metrics collection, dashboards, and operational best practices.

## Overview

Coherence includes a comprehensive monitoring system that provides visibility into the application's performance, error rates, and tenant usage patterns. The monitoring system includes:

- **Tenant-Specific Metrics**: All metrics can be filtered by tenant for multi-tenant deployments
- **Automatic Metrics Collection**: Middleware automatically collects basic metrics for all requests
- **Comprehensive Dashboards**: Pre-built Grafana dashboards for monitoring all aspects of the system
- **Error Tracking**: Detailed error tracking with categorization and context
- **Performance Metrics**: Latency and throughput metrics for all components

## Metrics Collection

Coherence collects a wide range of metrics that are exposed via Prometheus. The metrics are organized into the following categories:

### Intent Resolution Metrics

- `coherence_intent_resolution_total`: Count of intent resolution attempts (labeled by tenant_id and result)
- `coherence_intent_resolution_seconds`: Latency of intent resolution (labeled by tier and result)
- `coherence_intent_confidence`: Distribution of confidence scores for intent matches (labeled by tier)

### Parameter Extraction Metrics

- `coherence_parameter_extraction_seconds`: Latency of parameter extraction (labeled by method)
- `coherence_parameter_extraction_success_total`: Count of successful parameter extractions (labeled by param_type and method)
- `coherence_parameter_extraction_failure_total`: Count of failed parameter extractions (labeled by param_type, method, and reason)

### Action Execution Metrics

- `coherence_action_execution_seconds`: Latency of action execution (labeled by intent and result)
- `coherence_action_execution_total`: Count of action executions (labeled by intent and result)

### Conversation Metrics

- `coherence_conversation_turns`: Distribution of conversation turns (labeled by tenant_id)
- `coherence_conversation_duration_seconds`: Distribution of conversation durations (labeled by tenant_id)
- `coherence_active_conversations`: Gauge of active conversations (labeled by tenant_id)

### LLM and Embedding Metrics

- `coherence_llm_call_seconds`: Latency of LLM API calls (labeled by provider, model, and operation)
- `coherence_llm_token_usage_total`: Count of tokens used in LLM calls (labeled by tenant_id, provider, model, and operation)
- `coherence_embedding_call_seconds`: Latency of embedding API calls (labeled by provider and model)

### Vector Database Metrics

- `coherence_vector_search_seconds`: Latency of vector searches (labeled by collection)
- `coherence_vector_index_size`: Size of vector indices (labeled by tenant_id and collection)

### System Health Metrics

- `coherence_api_request_seconds`: Latency of API requests (labeled by endpoint, method, and status)
- `coherence_database_query_seconds`: Latency of database queries (labeled by operation)
- `coherence_cache_hit_ratio`: Cache hit ratio (labeled by cache_type)

### Error Metrics

- `coherence_error_total`: Count of errors (labeled by component and error_type)
- `coherence_error_by_type_total`: Count of errors by type (labeled by error_code and tenant_id)
- `coherence_error_by_endpoint_total`: Count of errors by endpoint (labeled by endpoint, method, and status_code)

### Fallback and Resilience Metrics

- `coherence_fallback_strategy_usage_total`: Count of fallback strategy activations (labeled by strategy and component)
- `coherence_fallback_strategy_success_total`: Count of successful fallback strategy activations (labeled by strategy and component)
- `coherence_fallback_strategy_failure_total`: Count of failed fallback strategy activations (labeled by strategy and component)
- `coherence_circuit_breaker_trips_total`: Count of circuit breaker trips (labeled by service and circuit_breaker)
- `coherence_circuit_breaker_state`: Current state of circuit breakers (labeled by service and circuit_breaker)
- `coherence_request_retry_attempts_total`: Count of retry attempts (labeled by service and operation)
- `coherence_error_rate`: Rate of errors over time (labeled by component)

## Using the Metrics Collector

The `TenantMetricsCollector` class provides methods for recording metrics with tenant context. Here are some examples of how to use it:

```python
from src.coherence.monitoring import TenantMetricsCollector

# Create a collector with specific tenant ID
metrics = TenantMetricsCollector(tenant_id="tenant-123")

# Create a collector from a request (extracts tenant ID automatically)
metrics = TenantMetricsCollector.from_request(request)

# Record intent resolution metrics
metrics.record_intent_resolution(
    latency=0.15,          # Seconds
    result="success",      # "success", "failure", etc.
    tier="vector",         # "vector", "llm", "rag"
    confidence=0.92,       # 0.0 to 1.0
)

# Record parameter extraction metrics
metrics.record_parameter_extraction(
    latency=0.05,           # Seconds
    method="pattern",       # "pattern", "llm", etc.
    success=True,           # Success or failure
    param_type="date",      # Type of parameter
)

# Record action execution metrics
metrics.record_action_execution(
    latency=0.2,            # Seconds
    intent="get_weather",   # Intent name
    result="success",       # "success", "failure", etc.
)

# Record LLM usage metrics
metrics.record_llm_usage(
    latency=0.5,             # Seconds
    provider="openai",       # LLM provider
    model="gpt-4o",          # Model name
    operation="completion",  # Operation type
    token_count=150,         # Number of tokens
)

# Record error metrics
metrics.record_error(
    error_type="validation_error",       # Type of error
    component="parameter_extraction",    # Component where error occurred
    endpoint="/v1/resolve",              # API endpoint
    status_code=400,                     # HTTP status code
    error_code="coherence.validation_error", # Error code
)
```

## Dashboards

Coherence includes pre-built Grafana dashboards for monitoring the application. The dashboards are generated from templates and can be customized as needed.

### System Health Dashboard

This dashboard provides a high-level overview of the system's health, including:

- **API Request Rate**: Number of API requests per second by endpoint
- **API Error Rate**: Number of API errors per second by endpoint
- **API Latency**: 95th percentile API request latency by endpoint
- **Database Query Latency**: 95th percentile database query latency by operation
- **Active Conversations**: Number of active conversations by tenant
- **Cache Hit Ratio**: Cache hit ratio by cache type
- **Circuit Breaker State**: Current state of circuit breakers
- **Success Rate**: Percentage of successful API requests
- **Error Count**: Total number of errors in the last 5 minutes
- **Intent Resolution Success**: Percentage of successful intent resolutions
- **Parameter Extraction Success**: Percentage of successful parameter extractions

### Error Monitoring Dashboard

This dashboard provides detailed information about errors in the system, including:

- **Error Count by Type**: Number of errors per second by error type
- **Error Count by Component**: Number of errors per second by component
- **Error Count by Endpoint**: Number of errors per second by API endpoint
- **Error Count by Status Code**: Number of errors per second by HTTP status code
- **Fallback Strategy Usage**: Number of fallback strategy activations per second by strategy
- **Fallback Strategy Success Rate**: Success rate of fallback strategies by strategy
- **Circuit Breaker Trips**: Number of circuit breaker trips per second by service and circuit breaker
- **Retry Attempts**: Number of retry attempts per second by service and operation
- **Error Distribution**: Distribution of errors by type, component, and endpoint

### Tenant Usage Dashboard

This dashboard provides information about tenant usage, including:

- **Request Rate by Tenant**: Number of API requests per second by tenant
- **Error Rate by Tenant**: Number of errors per second by tenant
- **Active Conversations by Tenant**: Number of active conversations by tenant
- **Intent Resolution Success by Tenant**: Success rate of intent resolutions by tenant
- **LLM Token Usage by Tenant**: Number of LLM tokens used per second by tenant
- **Average Conversation Turns by Tenant**: Average number of turns per conversation by tenant
- **Vector Index Size by Tenant**: Number of vectors in each collection by tenant
- **Average Conversation Duration by Tenant**: Average duration of conversations by tenant
- **Request Distribution by Tenant**: Distribution of requests by tenant
- **LLM Token Usage Distribution by Tenant**: Distribution of LLM token usage by tenant
- **Error Distribution by Tenant**: Distribution of errors by tenant

### Performance Dashboard

This dashboard provides detailed information about system performance, including:

- **API Request Latency**: API request latency percentiles by endpoint
- **Intent Resolution Latency by Tier**: 95th percentile intent resolution latency by tier
- **LLM Call Latency by Model**: 95th percentile LLM call latency by model
- **Parameter Extraction Latency by Method**: 95th percentile parameter extraction latency by method
- **Action Execution Latency by Intent**: 95th percentile action execution latency by intent
- **Database Query Latency by Operation**: 95th percentile database query latency by operation
- **Vector Search Latency by Collection**: 95th percentile vector search latency by collection
- **Embedding Call Latency by Model**: 95th percentile embedding call latency by model
- **Slowest Endpoints**: Top 5 slowest endpoints by 95th percentile latency
- **LLM Token Usage Rate**: LLM token usage rate by model and operation

## Generating Dashboards

Coherence includes a script for generating dashboards from templates. The script can be run as follows:

```bash
# Generate all dashboards
python -m scripts.generate_dashboards

# Generate specific dashboard
python -m scripts.generate_dashboards --dashboard=error

# Specify custom output directory
python -m scripts.generate_dashboards --output-dir=/custom/path
```

The generated dashboards are JSON files that can be imported into Grafana or used with Grafana's provisioning system.

## Operational Best Practices

Here are some best practices for monitoring the Coherence application:

### Dashboard Organization

- **System Health**: Check this dashboard first for a high-level overview
- **Error Monitoring**: Check this dashboard if errors are occurring
- **Tenant Usage**: Check this dashboard for tenant-specific issues
- **Performance**: Check this dashboard for performance troubleshooting

### Key Metrics to Watch

- **API Error Rate**: Should be near zero; spikes indicate problems
- **API Latency (p95)**: Should be stable; increases indicate performance issues
- **Intent Resolution Success**: Should be above 95%; lower values indicate intent recognition issues
- **Parameter Extraction Success**: Should be above 95%; lower values indicate parameter extraction issues
- **Fallback Strategy Usage**: Higher values indicate more failures that required fallback
- **Circuit Breaker Trips**: Should be rare; frequent trips indicate external service issues
- **LLM Token Usage**: Monitor for cost control and quota management

### Alerting Recommendations

Consider setting up alerts for the following conditions:

- **High Error Rate**: Alert if the error rate exceeds a threshold (e.g., 1% of requests)
- **High Latency**: Alert if API latency exceeds a threshold (e.g., 1 second p95)
- **Circuit Breaker Trips**: Alert if any circuit breaker trips
- **Intent Resolution Failures**: Alert if intent resolution success falls below a threshold (e.g., 90%)
- **Low Cache Hit Ratio**: Alert if cache hit ratio falls below a threshold (e.g., 80%)

## Troubleshooting

Here are some common issues and how to diagnose them:

### High Error Rate

1. Check the **Error Monitoring** dashboard to identify the error types, components, and endpoints
2. Look for patterns in the error distribution (e.g., specific error types or endpoints)
3. Check the logs for error details, using the error code and timestamp as a filter

### High Latency

1. Check the **Performance** dashboard to identify the slow components
2. Look for patterns in the latency distribution (e.g., specific endpoints or operations)
3. Check if the high latency correlates with high load or other system metrics
4. Consider adjusting timeouts or scaling the affected components

### Intent Resolution Issues

1. Check the **Intent Resolution Success** metric to confirm the issue
2. Look at the **Intent Resolution Latency by Tier** to identify which tier is problematic
3. Check the **LLM Call Latency** if the issue is in the LLM tier
4. Consider retraining vector embeddings if the issue is in the vector tier

### External Service Issues

1. Check the **Circuit Breaker State** to see if any circuit breakers are open
2. Look at the **Retry Attempts** to see if retries are occurring
3. Check the **External Service Error** count to identify the specific service with issues
4. Consider checking the external service's status or adding more fallback strategies

## References

- [Prometheus Documentation](https://prometheus.io/docs/introduction/overview/)
- [Grafana Documentation](https://grafana.com/docs/)
- [FastAPI Metrics](https://fastapi.tiangolo.com/advanced/middleware/)
- [Coherence Error Framework](error_handling.md)