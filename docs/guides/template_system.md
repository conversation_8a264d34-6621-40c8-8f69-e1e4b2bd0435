# Coherence Template System Guide

The template system is a core component of Coherence that enables flexible customization of various aspects of the system's behavior. This guide explains how the template system works and how to use it effectively.

## Overview

Templates in Coherence are based on the Jinja2 templating engine and provide a powerful way to:

1. Define prompts for LLM-based intent resolution
2. Create conversational parameter extraction flows
3. Generate response formatting with CRFS v2
4. Handle error cases
5. Implement retrieval-augmented generation
6. Map API responses to user-friendly output

Templates support inheritance and inclusion, allowing you to define base templates and override or extend them for specific tenants or industry packs. The system now includes the Coherence Response Format Standard (CRFS) v2 for consistent response formatting.

## Template Categories

Coherence uses five template categories, each serving a different purpose in the system:

| Category | Purpose | Default Key |
|----------|---------|-------------|
| `intent_router` | Guide LLMs to recognize intents | `INTENT_ROUTER_V1` |
| `param_complete` | Extract/request parameters | `PARAM_COMPLETE_V1` |
| `retrieval` | Knowledge base context integration | `RETRIEVAL_V1` |
| `response_gen` | Format user-facing replies | `RESPONSE_GEN_V1` |
| `error_handler` | Manage failures gracefully | `ERROR_HANDLER_V1` |

## Template Structure

Templates in Coherence use a block-based structure:

```jinja
{# 
    Template name and description
#}

{% block system_instruction %}
You are a specialized AI assistant that...
{% endblock %}

{% block prompt %}
## User Message
{{ user_message }}

## Available Options
...
{% endblock %}

{% block response_format %}
{
  "key": "value"
}
{% endblock %}
```

The three main blocks are:
- `system_instruction`: Instructions for the LLM on its role and task
- `prompt`: The main content of the prompt with context variables
- `response_format`: Expected output format

## Template Inheritance

Templates support inheritance through the `extends` directive:

```jinja
{% extends "base/intent_router.jinja" %}

{% block system_instruction %}
{{ super() }}
Additionally, you should focus on the healthcare domain.
{% endblock %}
```

## Template Scope Hierarchy

Templates follow a strict scope hierarchy that determines precedence:

```
Global Templates (baseline)
       ↓
  Pack Templates (industry-specific)
       ↓
Tenant Templates (customer-specific)
```

When the system looks for a template, it first checks for a tenant-specific version, then falls back to any applicable industry pack version, and finally to the global version.

## Creating Templates

You can create templates using the API or the seed script:

### Using the API

```bash
curl -X POST http://localhost:8001/v1/admin/templates \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "key": "INTENT_ROUTER_V1",
    "category": "intent_router",
    "body": "{% block system_instruction %}...",
    "scope": "tenant"
  }'
```

### Using the Seed Script

The `seed_templates.py` script can be used to seed the default templates:

```bash
python -m scripts.seed_templates --tenant-id "your-tenant-id"
```

## Rendering Templates

Templates are rendered using the template service:

```python
rendered = await template_service.render_template(
    db=db,
    key="INTENT_ROUTER_V1",
    category="intent_router",
    context={
        "user_message": "Book a flight to New York",
        "available_intents": [...],
    },
    tenant_id=tenant_id,
)
```

## Intent Router Templates

Intent router templates are used in Tier 2 (LLM-based) intent resolution when vector matching doesn't produce a high-confidence match.

Example context variables:
- `user_message`: The natural language message from the user
- `available_intents`: List of available intents with their descriptions
- `conversation_history`: Previous messages in the conversation (optional)

Example template:

```jinja
{% block system_instruction %}
You are a specialized AI assistant that analyzes user messages to identify their intent.
Your task is to categorize the user's message into one of the defined intents or determine
if it matches none of them.
{% endblock %}

{% block prompt %}
## User Message
{{ user_message }}

## Available Intents
{% for intent in available_intents %}
- {{ intent.id }}: {{ intent.description }}
{% endfor %}
{% endblock %}

{% block response_format %}
{
  "intent": "string",
  "confidence": 0.0,
  "parameters": {}
}
{% endblock %}
```

## Parameter Completion Templates

Parameter completion templates guide the conversational parameter extraction process, creating a Star Trek-like interface where the system naturally asks for missing information.

Example context variables:
- `intent`: The resolved intent
- `required_params`: Required parameters for the intent
- `optional_params`: Optional parameters for the intent
- `current_params`: Current parameter values
- `conversation`: Conversation history

## Testing Templates

You can test template rendering using the API:

```bash
curl -X POST http://localhost:8001/v1/admin/templates/render \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "key": "INTENT_ROUTER_V1",
    "category": "intent_router",
    "context": {
      "user_message": "Book a flight to New York",
      "available_intents": [...]
    }
  }'
```

## Best Practices

1. **Start with Defaults**: Begin with the default templates and customize them for your needs
2. **Use Scope Properly**: Place common templates at the global scope, industry-specific at pack scope, and very specific customizations at tenant scope
3. **Clear Instructions**: Provide clear instructions in the system_instruction block
4. **Consistent Structure**: Maintain a consistent structure for your templates
5. **Test Thoroughly**: Test your templates with a variety of inputs before deploying
6. **Versioning**: Use versioning in your template keys (e.g., INTENT_ROUTER_V1, INTENT_ROUTER_V2)

## Debugging Templates

If templates aren't working as expected:

1. Use the `/render` endpoint to see exactly what's being sent to the LLM
2. Check for missing variables or syntax errors
3. Ensure the template is in the expected scope
4. Verify the template key and category are correct
5. Look for rendering errors in the logs

## Coherence Response Format Standard (CRFS) v2

CRFS v2 is the standard for formatting API responses in Coherence templates. It provides structured response formatting with support for hierarchical sections, dynamic templates, and content negotiation.

### CRFS Configuration

CRFS configurations are defined in the `response_format` field of action templates:

```json
{
  "name": "get_weather",
  "response_format": {
    "crfs_version": "2.0",
    "kind": "coherence-response",
    "format": {
      "sections": [
        {
          "id": "current",
          "header": {
            "template": "## Current Weather"
          },
          "content": {
            "template": "Temperature: {{ response_mapping.temp }}°F"
          }
        }
      ]
    }
  }
}
```

### Available Filters

#### Text Transformation Filters
- `lowercase` - Convert text to lowercase
- `uppercase` - Convert text to uppercase
- `capitalize` - Capitalize first letter
- `title` - Title case conversion
- `truncate(n)` - Truncate to n characters
- `strip` - Remove whitespace

#### Data Handling Filters
- `default(value)` - Provide default value if none
- `pretty_json(indent=2)` - Pretty print JSON with proper formatting
- `escape` - Escape HTML characters
- `date_format(format)` - Format datetime objects
- `number_format(decimals)` - Format numeric values

### Response Mapping

Response mappings transform API responses into template variables:

```json
{
  "actions": [{
    "response_mapping": {
      "temp": "{{ response.json().current.temperature }}",
      "conditions": "{{ response.json().current.conditions }}",
      "location": "{{ response.json().location.name }}"
    }
  }]
}
```

### Content Negotiation

CRFS supports different output formats based on Accept headers:
- `application/json` - Raw JSON response
- `text/plain` - Plain text formatting
- `text/html` - HTML formatted response
- `*/*` - Default markdown formatting

### Error Handling

CRFS includes built-in error handling with fallback templates:

```json
{
  "error_template": {
    "template": "Error: {{ error.message | default('Unknown error') }}"
  }
}
```

### Best Practices

1. **Use meaningful section IDs** for easy reference
2. **Apply appropriate filters** to ensure data is properly formatted
3. **Handle null values** with the `default` filter
4. **Structure responses hierarchically** with sections
5. **Test with various data types** to ensure robust formatting

### Example: Todo List Response

```json
{
  "crfs_version": "2.0",
  "kind": "coherence-response",
  "format": {
    "sections": [
      {
        "id": "header",
        "content": {
          "template": "# Todo List ({{ response_mapping.count }} items)"
        }
      },
      {
        "id": "items",
        "content": {
          "template": "{% for item in response_mapping.items %}- [{% if item.completed %}x{% else %} {% endif %}] {{ item.title }}\n{% endfor %}"
        }
      }
    ]
  }
}
```

## Further Reading

For more details on Jinja2 templating, see the [Jinja2 documentation](https://jinja.palletsprojects.com/en/3.0.x/templates/).