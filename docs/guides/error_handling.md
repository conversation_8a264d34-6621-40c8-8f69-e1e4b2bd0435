# Coherence Error Handling Guide

This guide describes the error handling capabilities of the Coherence application, including the error taxonomy, fallback strategies, and best practices for error handling.

## Overview

Coherence includes a comprehensive error handling framework that provides structured error handling, consistent error responses, and mechanisms for graceful degradation. The framework includes:

- **Error Taxonomy**: A hierarchy of error classes for different types of errors
- **Standardized Error Responses**: Consistent error response format for all API endpoints
- **Fallback Strategies**: Mechanisms for graceful degradation when errors occur
- **Circuit Breaker Pattern**: Protection against cascading failures with external services
- **Retry Mechanisms**: Automatic retry for transient failures with configurable backoff

## Error Taxonomy

Coherence defines a comprehensive taxonomy of error types through a hierarchy of exception classes. All application errors inherit from the base `CoherenceError` class, which provides common functionality like error codes, status codes, and structured error details.

### Base Error Classes

- **CoherenceError**: Base class for all Coherence errors
  - **ValidationError**: Input validation failures
  - **AuthenticationError**: Authentication issues
  - **AuthorizationError**: Permission-related issues
  - **ResourceNotFoundError**: Resource doesn't exist
  - **ResourceConflictError**: Resource conflicts (e.g., already exists)
  - **ExternalServiceError**: External service call failures
    - **LLMServiceError**: LLM provider issues
  - **DatabaseError**: Database operation failures
  - **ConfigurationError**: Configuration issues
  - **RateLimitError**: Rate limiting and throttling
  - **InternalError**: Unexpected internal errors

### Error Codes

Each error type has a unique error code that identifies the type of error. Error codes follow the format `coherence.<error_type>`, for example:

- `coherence.validation_error`: Validation error
- `coherence.authentication_error`: Authentication error
- `coherence.resource_not_found`: Resource not found
- `coherence.external_service_error`: External service error
- `coherence.llm_service_error`: LLM service error

### HTTP Status Codes

Each error type maps to an appropriate HTTP status code:

- **400 Bad Request**: ValidationError
- **401 Unauthorized**: AuthenticationError
- **403 Forbidden**: AuthorizationError
- **404 Not Found**: ResourceNotFoundError
- **409 Conflict**: ResourceConflictError
- **429 Too Many Requests**: RateLimitError
- **500 Internal Server Error**: InternalError, DatabaseError, ConfigurationError
- **502 Bad Gateway**: ExternalServiceError, LLMServiceError

## Error Response Format

All error responses follow a consistent format:

```json
{
  "error": {
    "error_code": "coherence.validation_error",
    "message": "Invalid input data",
    "details": {
      "field_errors": {
        "name": "Field cannot be empty"
      }
    },
    "request_id": "123e4567-e89b-12d3-a456-426614174000",
    "type": "ValidationError"
  }
}
```

The error response includes:

- **error_code**: A machine-readable error code
- **message**: A human-readable error message
- **details**: Additional error details (optional)
- **request_id**: A unique ID for the request (for correlation)
- **type**: The exception type (for debugging)

## Using the Error Classes

### Raising Errors

You can raise errors in your code using the appropriate error class:

```python
from src.coherence.core.errors import ValidationError, ResourceNotFoundError

# Simple validation error
raise ValidationError(message="Invalid input data")

# Validation error with field errors
raise ValidationError(
    message="Invalid input data",
    field_errors={"name": "Field cannot be empty"}
)

# Resource not found error
raise ResourceNotFoundError(
    resource_type="Template",
    resource_id="123"
)  # Message will be "Template with ID '123' not found"

# External service error
raise ExternalServiceError(
    service_name="OpenAI",
    message="API returned an error",
    details={"status_code": 500}
)
```

### Handling Errors

Error handling can be centralized using exception handlers:

```python
from fastapi import FastAPI
from src.coherence.core.errors import register_exception_handlers

app = FastAPI()

# Register all exception handlers
register_exception_handlers(app)
```

The `register_exception_handlers` function registers handlers for all known error types and ensures that they are converted to the standardized error response format.

## Fallback Strategies

Coherence includes several fallback strategies for graceful degradation when errors occur:

### Default Value Fallback

Returns a default value when a function fails:

```python
from src.coherence.core.errors.fallback import DefaultValueFallback, with_fallback

# Create a fallback strategy
fallback = DefaultValueFallback(default_value=[])

# Apply it to a function
@with_fallback(strategy=fallback)
def get_items():
    # This might fail
    response = api_client.get_items()
    return response.json()

# If get_items fails, it will return [] instead
items = get_items()
```

### Cached Result Fallback

Returns a previously cached result when a function fails:

```python
from src.coherence.core.errors.fallback import CachedResultFallback, with_fallback

# Create a fallback strategy
fallback = CachedResultFallback()

# Apply it to a function
@with_fallback(strategy=fallback)
def get_weather(city):
    # This might fail
    response = api_client.get_weather(city)
    return response.json()

# First call succeeds and caches the result
weather1 = get_weather("New York")

# If the second call fails, it will return the cached result
weather2 = get_weather("New York")
```

### Fallback Chain

Tries multiple fallback strategies in sequence:

```python
from src.coherence.core.errors.fallback import (
    DefaultValueFallback,
    CachedResultFallback,
    FallbackChain,
    with_fallback,
)

# Create fallback strategies
cached_fallback = CachedResultFallback()
default_fallback = DefaultValueFallback(default_value=[])

# Create a fallback chain
chain = FallbackChain(strategies=[cached_fallback, default_fallback])

# Apply it to a function
@with_fallback(strategy=chain)
def get_recommendations(user_id):
    # This might fail
    response = api_client.get_recommendations(user_id)
    return response.json()

# If get_recommendations fails, it will first try the cached result,
# and if that fails, it will return the default value
recommendations = get_recommendations("user123")
```

### Callback Fallback

Calls a custom function when the primary function fails:

```python
from src.coherence.core.errors.fallback import CallbackFallback, with_fallback

# Define a callback function
def generate_local_recommendations(user_id):
    # Generate recommendations locally
    return ["item1", "item2", "item3"]

# Create a fallback strategy
fallback = CallbackFallback(callback=generate_local_recommendations)

# Apply it to a function
@with_fallback(strategy=fallback)
def get_recommendations(user_id):
    # This might fail
    response = api_client.get_recommendations(user_id)
    return response.json()

# If get_recommendations fails, it will call generate_local_recommendations
recommendations = get_recommendations("user123")
```

## Specialized Fallback Decorators

Coherence includes specialized fallback decorators for common cases:

### External Service Fallback

For external service calls:

```python
from src.coherence.core.errors.fallback import (
    DefaultValueFallback,
    fallback_for_external_service,
)

# Create a fallback strategy
fallback = DefaultValueFallback(default_value=[])

# Apply it to a function
@fallback_for_external_service(service_name="RecommendationAPI", strategy=fallback)
def get_recommendations(user_id):
    # This might fail
    response = api_client.get_recommendations(user_id)
    return response.json()
```

### LLM Fallback

For LLM service calls:

```python
from src.coherence.core.errors.fallback import (
    DefaultValueFallback,
    fallback_for_llm,
)

# Create a fallback strategy
fallback = DefaultValueFallback(default_value="I'm sorry, I couldn't process that.")

# Apply it to a function
@fallback_for_llm(strategy=fallback)
def generate_text(prompt):
    # This might fail
    response = llm_client.generate(prompt)
    return response.text
```

## Circuit Breaker Pattern

The circuit breaker pattern helps prevent cascading failures when an external service is experiencing issues:

```python
from src.coherence.openapi_adapter.circuit_breaker import (
    CircuitBreaker,
    with_circuit_breaker,
)

# Create a circuit breaker
breaker = CircuitBreaker(
    name="weather_api",
    failure_threshold=5,
    recovery_timeout=60,
    half_open_success_threshold=3,
)

# Apply it to a function
@with_circuit_breaker(breaker)
def get_weather(city):
    # This might fail
    response = api_client.get_weather(city)
    return response.json()
```

The circuit breaker has three states:

- **Closed**: The service is operating normally.
- **Open**: The service has failed too many times; the circuit breaker will short-circuit requests without calling the service.
- **Half-Open**: After the recovery timeout, the circuit breaker allows some requests through to test if the service is back to normal.

## Retry Mechanisms

Coherence includes retry mechanisms for handling transient failures:

```python
from src.coherence.openapi_adapter.retry import (
    RetryHandler,
    ExponentialBackoff,
    with_retry,
)

# Create a retry handler with exponential backoff
retry_handler = RetryHandler(
    max_retries=3,
    backoff_strategy=ExponentialBackoff(initial_delay=0.1, multiplier=2),
    retryable_exceptions=[ConnectionError, TimeoutError],
)

# Apply it to a function
@with_retry(retry_handler)
def get_weather(city):
    # This might fail with a transient error
    response = api_client.get_weather(city)
    return response.json()
```

The retry mechanism supports different backoff strategies:

- **ConstantBackoff**: Fixed delay between retries
- **LinearBackoff**: Linearly increasing delay
- **ExponentialBackoff**: Exponentially increasing delay
- **FibonacciBackoff**: Fibonacci sequence for delay
- **RandomBackoff**: Random delay within a range

## Error Logging

Coherence includes utilities for structured error logging:

```python
from src.coherence.core.errors.logging import log_error, with_error_logging

# Log a specific error
from src.coherence.core.errors import ValidationError

error = ValidationError(message="Invalid input data")
log_error(
    error=error,
    log_level=logging.ERROR,
    include_traceback=True,
    extra_context={"user_id": "123"},
)

# Add error logging to a function
@with_error_logging(
    log_level=logging.ERROR,
    include_traceback=True,
    extra_context={"component": "user_service"},
)
def process_user(user_id):
    # This might raise an error
    user = get_user(user_id)
    return user
```

## Error Monitoring

Coherence includes metrics for monitoring errors:

```python
from src.coherence.core.metrics import increment_error_counter
from src.coherence.monitoring import TenantMetricsCollector

# Increment error counter
increment_error_counter(
    error_type="ValidationError",
    endpoint="/v1/resolve",
    component="api",
    status_code=400,
    tenant_id="tenant-123",
    error_code="coherence.validation_error",
)

# Or use the tenant metrics collector
metrics = TenantMetricsCollector(tenant_id="tenant-123")
metrics.record_error(
    error_type="ValidationError",
    component="api",
    endpoint="/v1/resolve",
    status_code=400,
    error_code="coherence.validation_error",
)
```

## Best Practices

### Error Handling

1. **Use Specific Error Types**: Use the most specific error type for the situation.
2. **Include Context**: Provide enough context in error messages and details for debugging.
3. **Don't Expose Sensitive Information**: Be careful not to include sensitive information in error responses.
4. **Use Request IDs**: Include request IDs in logs and error responses for correlation.
5. **Log at Appropriate Levels**: Use appropriate log levels based on error severity.

### Fallback Strategies

1. **Define Default Values**: Always define sensible default values for fallback strategies.
2. **Cache Critical Results**: Use cached results for critical operations when appropriate.
3. **Implement Local Alternatives**: Consider implementing local alternatives for external services.
4. **Combine Strategies**: Use fallback chains to combine multiple strategies.
5. **Monitor Fallback Usage**: Monitor how often fallback strategies are used to identify issues.

### Circuit Breakers

1. **Set Appropriate Thresholds**: Set failure and recovery thresholds based on service behavior.
2. **Use Different Circuit Breakers**: Use different circuit breakers for different services.
3. **Combine with Fallbacks**: Combine circuit breakers with fallback strategies for comprehensive resilience.
4. **Monitor Circuit Breaker State**: Monitor circuit breaker state changes for early issue detection.

### Retry Mechanisms

1. **Retry Only Transient Errors**: Only retry errors that are likely to be transient.
2. **Set Maximum Retries**: Set a reasonable maximum number of retries to avoid overloading services.
3. **Use Appropriate Backoff**: Choose an appropriate backoff strategy based on the service behavior.
4. **Add Jitter**: Add jitter to backoff delays to avoid thundering herd problems.

## Reference

- [FastAPI Exception Handling](https://fastapi.tiangolo.com/tutorial/handling-errors/)
- [Circuit Breaker Pattern](https://microservices.io/patterns/reliability/circuit-breaker.html)
- [Retry Pattern](https://docs.microsoft.com/en-us/azure/architecture/patterns/retry)
- [Fallback Pattern](https://docs.microsoft.com/en-us/azure/architecture/patterns/compensating-transaction)