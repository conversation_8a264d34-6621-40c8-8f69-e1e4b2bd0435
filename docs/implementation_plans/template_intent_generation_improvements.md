# Template Intent Generation Improvements

Date: May 13, 2025  
Author: <PERSON> Code

## Overview

This document outlines a plan to enhance the template generation process to create LLM-accessible intent templates for vector matching. Currently, the template generation process creates response templates but lacks a mechanism to generate intent-specific templates that can be used for vector matching in Tier 1 of the intent resolution pipeline.

## Current Limitations

1. **Missing Intent Templates**: 
   - Templates for API actions (like ICD10CM) only generate response templates with generic bodies (e.g., "The API request to Icd10cm_openapi was successful. Here are the results:")
   - No dedicated intent templates are created for vector matching
   - The system relies on other mechanisms for intent identification

2. **Poor LLM Context**: 
   - Generic response templates don't provide enough context for LLMs to understand the purpose of the API
   - Current template bodies focus on API success rather than intent understanding

3. **Disconnected Vector Matching**: 
   - The vector intent matching system (Tier 1) is not directly connected to the template system
   - Intent examples aren't automatically generated from templates

## Proposed Solution

Enhance the template generation process to create three types of templates for each API endpoint:

1. **Intent Template**: Used for vector matching and intent identification
2. **Response Template**: Used for formatting API responses (existing)
3. **Error Handler Template**: Used for handling API errors (existing)

### 1. Template Generation Enhancements

Modify the `_generate_action_templates` method in `src/coherence/openapi_adapter/action_generator.py` to:

```python
async def _generate_action_templates(
    self,
    endpoint_id: uuid.UUID,
    tenant_id: uuid.UUID,
    api_key: str,
) -> List[Dict[str, Any]]:
    # ... existing code ...
    
    # Create templates array with the original templates
    templates = [template_dict, fallback_template_dict]
    
    # Generate intent template
    intent_template_dict = {
        "key": f"{template_key}_intent",
        "category": "intent_router",  # New category for intent templates
        "body": self._generate_intent_template_body(
            operation_id, 
            endpoint_spec, 
            integration.name,
            parameters
        ),
        "scope": "tenant",
        "scope_id": tenant_id,
        "tenant_id": tenant_id,
        "version": 1,
        "description": f"Intent matcher for {description}",
        "actions": {},  # No actions needed for intent templates
        "parameters": parameter_schema,
        "language": "en",
        "metadata": {
            "source": "openapi",
            "integration_id": str(integration_id),
            "endpoint_id": str(endpoint_id),
            "response_template_key": template_key,  # Link to response template
            "vector_indexable": True  # Flag for vector indexing
        }
    }
    
    # Add intent template to templates array
    templates.append(intent_template_dict)
    
    return templates
```

### 2. Intent Template Body Generation

Add a method to generate intent template bodies with LLM-accessible examples:

```python
def _generate_intent_template_body(
    self,
    operation_id: str,
    endpoint_spec: Dict,
    integration_name: str,
    parameters: List[Dict]
) -> str:
    """Generate an intent template body with LLM-accessible examples.
    
    Args:
        operation_id: The OpenAPI operation ID
        endpoint_spec: The OpenAPI endpoint specification
        integration_name: The name of the integration
        parameters: The endpoint parameters
        
    Returns:
        A template body with examples
    """
    # Extract operation information
    summary = endpoint_spec.get("summary", "")
    description = endpoint_spec.get("description", "")
    
    # Create a human-readable operation name
    operation_name = operation_id.replace("_", " ").replace("-", " ")
    
    # Generate examples using variations of the operation and parameters
    parameter_examples = self._generate_parameter_examples(parameters)
    
    # Create the template body
    template_body = f"""# Intent: {operation_name}

## Description
This intent matches user requests to search for {description or operation_name} using the {integration_name} API.

## LLM-Accessible Examples
Here are examples of how users might express this intent:

- Search for {operation_name}
- Find {operation_name} in {integration_name}
- I need to look up {operation_name}
- Can you help me {operation_name.lower()}
- Show me {operation_name} from {integration_name}
- {integration_name} {operation_name.lower()}
{"".join([f"- {ex}" for ex in self._generate_advanced_examples(operation_name, integration_name, description)])}

## Parameter Examples
{parameter_examples}

## API Details
- Integration: {integration_name}
- Operation: {operation_id}
- Endpoint: {endpoint_spec.get("path", "")}
- Method: {endpoint_spec.get("method", "").upper()}
"""
    
    return template_body
```

### 3. Response Template Improvement

Enhance the response template generation to be more user-friendly:

```python
def _generate_response_template_body(
    self,
    operation_id: str,
    endpoint_spec: Dict,
    integration_name: str,
    parameters: List[Dict]
) -> str:
    """Generate an improved response template body.
    
    Args:
        operation_id: The OpenAPI operation ID
        endpoint_spec: The OpenAPI endpoint specification
        integration_name: The name of the integration
        parameters: The endpoint parameters
        
    Returns:
        A template body for API responses
    """
    # Extract operation information
    summary = endpoint_spec.get("summary", "")
    description = endpoint_spec.get("description", "")
    
    # Create a human-readable operation name
    operation_name = operation_id.replace("_", " ").replace("-", " ")
    
    # Generate parameter access template
    param_access = self._generate_parameter_access_string(parameters)
    
    # Create the template body
    template_body = f"""I've searched for {operation_name}{param_access} using the {integration_name} API.

{% if result.data and result.data | length > 0 %}
Here are the results:

{% for item in result.data %}
- {{ item.name }}: {{ item.description }}
{% endfor %}

{% else %}
I couldn't find any {operation_name} matching your criteria. Would you like to try a different search?
{% endif %}
"""
    
    return template_body
```

### 4. Vector Indexing Integration

Add a script to index intent templates for vector matching:

```python
# New script: src/coherence/scripts/index_intent_templates.py

import asyncio
import logging
from typing import List, Dict, Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from coherence.db.session import async_session
from coherence.models.template import Template
from coherence.core.qdrant_client import get_qdrant_client
from coherence.core.llm.factory import get_llm_provider

logger = logging.getLogger(__name__)

def extract_examples_from_template(body: str) -> List[str]:
    """Extract example phrases from an intent template body."""
    examples = []
    lines = body.split("\n")
    in_examples_section = False
    
    for line in lines:
        line = line.strip()
        
        if "## LLM-Accessible Examples" in line:
            in_examples_section = True
            continue
        
        if in_examples_section:
            if line.startswith("##"):
                # End of examples section
                break
                
            if line.startswith("- "):
                # Extract the example text
                example = line[2:].strip()
                if example:
                    examples.append(example)
    
    return examples

async def index_intent_templates(tenant_id: str):
    """Index all intent templates for a tenant in the vector database."""
    # Get Qdrant client
    qdrant_client = await get_qdrant_client()
    
    # Get LLM provider for embeddings
    llm_provider = get_llm_provider()
    
    # Get all intent templates for this tenant
    async with async_session() as session:
        query = select(Template).where(
            Template.tenant_id == tenant_id,
            Template.category == "intent_router",
            Template.metadata.contains({"vector_indexable": True})
        )
        
        result = await session.execute(query)
        templates = result.scalars().all()
    
    if not templates:
        logger.info(f"No indexable intent templates found for tenant {tenant_id}")
        return
    
    logger.info(f"Found {len(templates)} indexable intent templates for tenant {tenant_id}")
    
    # Index each template
    for template in templates:
        try:
            # Extract examples from template body
            examples = extract_examples_from_template(template.body)
            
            if not examples:
                logger.warning(f"No examples found in template {template.key}")
                continue
            
            logger.info(f"Indexing {len(examples)} examples from template {template.key}")
            
            # Get the response template key from metadata
            response_template_key = template.metadata.get("response_template_key")
            
            if not response_template_key:
                logger.warning(f"No response template key in metadata for template {template.key}")
                continue
            
            # Index each example
            for example in examples:
                # Generate embedding
                embedding = await llm_provider.generate_embedding(example)
                
                # Add to Qdrant
                await qdrant_client.upsert_intent_vectors(
                    tenant_id=tenant_id,
                    role="user",
                    vectors=[{
                        "id": f"{template.id}_{examples.index(example)}",
                        "vector": embedding,
                        "payload": {
                            "text": example,
                            "intent": response_template_key,  # Link to response template
                            "template_id": str(template.id),
                            "template_key": template.key
                        }
                    }]
                )
                
                logger.info(f"Indexed example: {example}")
            
        except Exception as e:
            logger.error(f"Error indexing template {template.key}: {str(e)}")
```

### 5. Command-Line Interface

Add a CLI command to trigger the intent indexing:

```python
# Add to src/coherence/cli.py

@app.command("index-intent-templates")
def index_intent_templates_command(tenant_id: str):
    """Index all intent templates for a tenant in the vector database."""
    logger.info(f"Indexing intent templates for tenant {tenant_id}")
    asyncio.run(index_intent_templates(tenant_id))
    logger.info(f"Done indexing intent templates for tenant {tenant_id}")
```

## Integration with API Endpoints

Update the OpenAPI integration endpoint to incorporate intent template generation:

```python
@router.post("/integrations/{integration_id}/generate-templates")
async def generate_templates_for_integration(
    integration_id: uuid.UUID,
    tenant_id: uuid.UUID = Depends(get_current_tenant_id),
):
    """Generate templates for all endpoints in an integration."""
    # ... existing code ...
    
    # After generating templates
    templates = await action_generator.generate_action_templates(
        endpoint_id=endpoint.id,
        tenant_id=tenant_id,
        api_key=integration.name,
    )
    
    # Create all templates
    created_templates = []
    for template_dict in templates:
        template = await template_service.create_template(
            tenant_id=tenant_id,
            template_data=template_dict,
        )
        created_templates.append(template)
    
    # Index intent templates
    intent_templates = [t for t in created_templates if t.category == "intent_router"]
    if intent_templates:
        # Trigger intent indexing
        asyncio.create_task(index_intent_templates(str(tenant_id)))
    
    # ... rest of function ...
```

## Implementation Plan

### Phase 1: Basic Implementation

1. Update the action generator to create intent templates
2. Improve response template generation
3. Create the intent template indexing script
4. Test with existing integrations (ICD10)

### Phase 2: Refinements

1. Add parameter examples to intent templates
2. Implement template validation to ensure quality
3. Create an admin UI for testing intent matching
4. Add bulk reindexing capabilities

### Phase 3: Advanced Features

1. Implement LLM-based example generation for better quality
2. Add multilingual support for templates
3. Create analytics for intent template performance
4. Implement automatic template improvement based on user data

## Rollout Strategy

1. Deploy template generation changes (non-breaking)
2. Generate intent templates for existing integrations
3. Index intent templates in testing environment
4. Validate vector matching performance
5. Roll out to production

## Success Metrics

1. **Coverage**: All API endpoints have corresponding intent templates
2. **Quality**: Intent templates have at least 5 high-quality examples
3. **Performance**: Tier 1 vector matching success rate increases by 20%
4. **Latency**: Tier 1 resolution time remains under 100ms
5. **Accuracy**: False positive rate for intent matching below 5%

## Conclusion

By implementing these improvements, we'll create a complete pipeline from OpenAPI specification to intent resolution through vector matching. This will enable faster, more accurate API action selection while providing better context for LLMs to understand user intents.