Below is a **rooted-in-code** roadmap that connects the pieces already in Coherence to the end-goal you described: *templates that carry executable actions generated from every imported API endpoint, plus an Admin UI flow that lets an LLM propose/refine intents and attach the right action-templates.*  Citations point to the exact source lines I verified.

---

### 1. What’s already in place (verified)

| Capability                                          | Key files / endpoints                                                                                                                                 | Notes                                                                                               |
| --------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------- |
| **OpenAPI import + endpoint registry**              | `OpenAPIAdapter.import_spec` parses the spec, stores an `APIIntegration`, and persists every path+method as `api_endpoints` rows                      | Works end-to-end; RLS enforced via `tenant_id` in the integration.                                  |
| **Action-class generation & validation**            | `ActionGenerator.generate_action_class` turns an endpoint into Python code, validates it, and stores in `generated_actions` with status + versioning  | Endpoint `/admin/integrations/actions/generate` wraps this for bulk generation                      |
| **Template system (CRUD, versioning, inheritance)** | models + service + REST API described in `repomix-output.md`                                                                                          | Categories (`intent_router`, `param_complete`, `response_gen`, …) and scope hierarchy already live. |
| **LLM-driven intent resolution via templates**      | `TemplateBasedIntentResolver` plugs templates into Tier-2 of the intent pipeline                                                                      |                                                                                                     |
| **Admin UI plumbing**                               | PRD & checklist show template pages, integration pages, Clerk auth, RBAC scaffolding in progress                                                      |                                                                                                     |

---

### 2. Gaps to close

1. ✅ **Generated actions are wrapped in templates**—implemented via `_generate_action_templates` in ActionGenerator class and template generation endpoints.
2. ✅ **Intent generation from endpoints** (name, description, examples) is implemented with endpoints for both selected endpoints (`/intents/generate-selected`) and all integration endpoints (`/{integration_id}/intents/generate`).
3. **Admin UI**:

   * ✅ Implemented wizard step to run "Generate Templates & Intents"
   * ✅ Implemented Monaco-based editors for template editing with preview functionality
   * ❓ test console for the dynamic executor end-to-end is still needed.
4. **Vectorisation / search-over-templates** is outlined but not triggered after template save.
5. **Credential picker & auth-config binding** still needs UI and service wiring.

---

### 3. Implementation Plan

#### Phase 1 – Backend plumbing (5-7 dev-days)

| Step    | Action                                                                                                                                                                                                                                                                                                                                                                                                                    |
| ------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **1.1** | ✅ **Extended `ActionGenerator`** with `_generate_action_templates` to create **two template rows per endpoint**:<br/>*Primary* (`response_gen`) with an `actions` array that references `integration_id`, `endpoint_id`, `parameter_mapping`, `response_mapping`, `authentication`.<br/>*Fallback* (`error_handler`) for graceful failures. |
| **1.2** | ✅ Added appropriate template categories and handling for action templates. |                                                                                                                                                                                                                                                                                                                                          |
| **1.3** | ✅ Exposed endpoints for template generation:<br/>1. `POST /admin/integrations/{integration_id}/templates/generate` for all enabled endpoints<br/>2. `POST /admin/openapi/templates/generate-selected` for selected endpoints<br/>Both APIs call the extended `ActionGenerator` for each endpoint and insert templates via `TemplateService`. |                                                                                                                                                          |
| **1.4** | ✅ Implemented intent generation endpoints:<br/>1. `POST /admin/integrations/{integration_id}/intents/generate` for all enabled endpoints<br/>2. `POST /admin/openapi/intents/generate-selected` for selected endpoints<br/>Both use LLM to generate intents with name, description, and examples, and persist them in the `intents` table. |                                                                                                                   |
| **1.5** | ❓ Need to verify implementation of template vectorization after create/update operations. The hooks to publish `template.updated` and call vector-indexer to embed in Qdrant (`template_idx_<scope>`) need to be confirmed. |

#### Phase 2 – Admin UI workflow (6-8 dev-days)

1. ✅ **API Integration Wizard** - COMPLETED
   *Added step 3 "Generate Templates & Intents".* Successfully implemented the UI for calling template and intent generation endpoints. Added progress indicators and result display for generated templates and intents.

2. ✅ **Template Explorer** - COMPLETED
   *Implemented templates view grouped by endpoint.* Created Monaco editor integration for template editing with live preview. Added category filtering and detail/edit views for templates.
   - Added navigation between templates, detail view, and editing screens
   - Implemented actions and parameters editing in separate tabs
   - Fixed authentication issues for proper tenant ID handling

3. **Intent Manager** - NEXT PRIORITY
   *List intents, allow editing description/examples, toggle active.* Uses `/admin/intents` endpoints (list/create/update).
   - ❓ Intent listing page with filtering needs to be implemented
   - ❓ Intent detail and edit views need to be implemented
   - ❓ Ability to activate/deactivate intents needs to be implemented

4. **Test Console**
   *Input box + param editor → calls Orchestrator → shows raw/pretty response.*
   - ❓ Need to implement UI for testing dynamic template execution
   - ❓ Need to wire through the dynamic executor path

#### Phase 3 – Pipeline wiring & auto-improve (4-5 dev-days)

| Task                                                                                                                                                                                | Detail |
| ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------ |
| **3.1 Auto-discover** – During chat, if Tier-1 vector match fails and no intent is chosen, call `TemplateBasedIntentResolver` which will now include the **LLM-generated intents**. |        |
| **3.2 Parameter completion** – For any unmatched required param, render the `PARAM_COMPLETE_V1` template; ask the user; loop until the `parameter_mapping` is fully satisfied.      |        |
| **3.3 Execute action** – Pass parameters + credentials to dynamic executor, capture response, render `response_gen` template, fallback to `error_handler` on failure.               |        |

#### Phase 4 – Security, audit & ops (parallel)

* **RLS migrations** for new tables (`intents`, any join tables) mirroring the pattern used in `generated_actions` RLS ﻿.
* **Credential Manager UI** (already spec’d) with encrypted storage & rotation.
* **Prom-metrics**: tag executor latency, template render time (metrics endpoint already exposed ﻿).

---

### 4. LLM prompt strategy (admin-side)

* **Intent generation prompt** (server-side, not exposed to users):

  ```
  System: "You are an API analyst…"
  User:  <openapi endpoint summary + sample responses>  
  Task:  → Return JSON {name, description, examples[]}  
  ```

* **Intent-router template** `INTENT_ROUTER_V1` stays the same; new intents are appended to `available_intents` list before rendering .

* **Parameter completion template** uses the extracted schema to ask for only missing fields.

Admin users can refine any of these via the Monaco editor; edits auto-version via `template_versions`.

---

### 5. Acceptance checklist

1. Upload a fresh OpenAPI spec → **see templates + intents populate**.
2. Chat test: “Create customer Acme via CRM” → router picks CRM-create intent → param prompt appears → API call succeeds → response rendered.
3. Multitenant check: Clerk org switch shows only that tenant’s templates/actions.
4. Vector search in Admin UI returns semantically similar templates across packs (super-admin only).
5. Metrics endpoint shows <200 ms render, <500 ms executor 95-pctl.

–––

This roadmap keeps every task anchored to verified code paths and existing migrations—no guesswork, no new abstractions, just the missing links to make **template-driven, LLM-assisted actions** fully functional end-to-end.
