# Admin Chat Interface Implementation Plan

## Overview

This document outlines the implementation plan for adding a chat interface to the Coherence admin site. The chat interface will allow administrators to interact with the Coherence intent pipeline, template system, and action executor directly, providing a way to test and improve the underlying processes.

## Goals

- Create an intuitive, user-friendly chat interface within the admin site
- Connect the chat UI to the existing backend API endpoints
- Support all response types (ask, reply, async, fallback, intent_clarification)
- Provide a tool for testing and improving the intent pipeline
- Make the interface accessible and responsive

## Architecture

### File Structure

```
coherence-admin/
├── src/
│   ├── app/
│   │   ├── admin/
│   │   │   ├── chat/
│   │   │   │   ├── page.tsx                 # Main chat page
│   │   │   │   ├── ChatClient.tsx           # Client component for chat interface
│   │   ├── api/
│   │   │   ├── proxy/
│   │   │   │   ├── chat/
│   │   │   │   │   ├── route.ts             # API route for proxying to backend
│   ├── components/
│   │   ├── chat/
│   │   │   ├── ChatContainer.tsx            # Main chat UI container
│   │   │   ├── ChatMessage.tsx              # Individual message component
│   │   │   ├── ChatInput.tsx                # Message input component
│   │   │   ├── ResponseTypes/
│   │   │   │   ├── AskResponse.tsx          # Component for "ask" type responses
│   │   │   │   ├── AsyncResponse.tsx        # Component for "async" type responses
│   │   │   │   ├── ReplyResponse.tsx        # Component for "reply" type responses
│   │   │   │   ├── FallbackResponse.tsx     # Component for "fallback" type responses
│   │   │   │   ├── ClarificationResponse.tsx # Component for intent clarification
│   │   │   ├── ConversationHistory.tsx      # Component to display conversation history
│   ├── lib/
│   │   ├── chatClient.ts                    # Extensions to apiClient for chat endpoints
│   │   ├── hooks/
│   │   │   ├── useChat.ts                   # Custom hook for chat state management
```

### API Integration

The chat interface will connect to the existing backend API endpoints:

- **POST /v1/resolve**: Process a new message to identify user intent
- **POST /v1/continue**: Continue an existing conversation with additional information
- **GET /v1/status/{workflow_id}**: Check status of asynchronous workflows

We'll extend the existing apiClient utility to handle these endpoints with the following methods:

```typescript
// In chatClient.ts
export async function sendMessage(
  message: string,
  userId: string,
  conversationId?: string,
  context?: Record<string, any>,
  options?: ApiClientOptions
): Promise<ResolveResponse>;

export async function continueConversation(
  message: string,
  conversationId: string,
  userId: string,
  field?: string,
  options?: ApiClientOptions
): Promise<ResolveResponse>;

export async function checkWorkflowStatus(
  workflowId: string,
  options?: ApiClientOptions
): Promise<WorkflowStatus>;
```

### State Management

A custom React hook `useChat` will manage the chat state, handling:

- Message history tracking
- Conversation ID management
- API call handling
- Response type processing
- Error handling and recovery

```typescript
// In useChat.ts
interface ChatState {
  messages: Message[];
  conversationId: string | null;
  isLoading: boolean;
  activeWorkflows: Record<string, WorkflowStatus>;
  error: string | null;
  activeField?: string; // For ask responses
}

interface UseChatResult {
  state: ChatState;
  sendMessage: (message: string) => Promise<void>;
  resetConversation: () => void;
  handleFieldResponse: (field: string, value: any) => Promise<void>;
  handleIntentSelection: (intentId: string) => Promise<void>;
}

export function useChat(userId: string): UseChatResult {
  // Implementation will manage state, API calls, and response handling
}
```

### UI Components

#### ChatContainer

The main container for the chat interface, responsible for:
- Layout and styling of the entire chat interface
- Containing the message history and input components
- Auto-scrolling to newest messages
- Managing overall UI state

#### ChatMessage

Represents a single message in the conversation:
- Differentiates between user and system messages
- Handles rendering of different response types
- Supports markdown formatting
- Provides appropriate interactive elements based on response type

#### ChatInput

The user input component:
- Text input field for entering messages
- Send button with appropriate loading states
- Keyboard shortcut support (Enter to send)
- Accessibility features for screen readers

#### Response Type Components

Specialized components for each response type:

1. **AskResponse**: 
   - Displays questions that require additional information
   - Provides appropriate input controls based on field type
   - Handles validation of user input

2. **AsyncResponse**:
   - Displays loading indicator for async operations
   - Shows progress information when available
   - Polls the status endpoint for updates
   - Updates the UI when the operation completes

3. **ReplyResponse**:
   - Displays formatted text responses
   - Renders structured data in a readable format
   - Supports markdown and code formatting

4. **FallbackResponse**:
   - Displays error messages when intent recognition fails
   - Provides suggestions for how to rephrase the request

5. **ClarificationResponse**:
   - Presents options when intent ambiguity is detected
   - Allows the user to select the intended option

### Authentication & Permissions

The chat interface will leverage the existing authentication system:

- Use AdminSessionContext for authentication state and tokens
- Add permission check for chat access (`chat:access`)
- Pass tenant context via X-Tenant-ID header (from orgId)
- Handle token refresh automatically via existing patterns

## Implementation Phases

### Phase 1: Foundation (Days 1-2)

1. **Setup File Structure**
   - Create all required files and directories
   - Set up basic component skeletons

2. **API Client Extensions**
   - Implement chat-specific API client methods
   - Create API proxy routes in Next.js

3. **Basic Page Layout**
   - Create the chat page with basic routing
   - Add the page to the sidebar navigation
   - Setup layout with proper permissions

4. **State Management Implementation**
   - Create the useChat hook with basic functionality
   - Implement message history management
   - Add conversationId tracking

### Phase 2: Core Functionality (Days 3-4)

5. **Core UI Components**
   - Build ChatContainer with flex layout
   - Implement ChatMessage with styling
   - Create ChatInput with send functionality
   - Add auto-scrolling behavior

6. **Basic API Integration**
   - Connect UI to the chat API endpoints
   - Implement basic message sending and receiving
   - Handle loading states during API calls

7. **Authentication Integration**
   - Connect with AdminSessionContext
   - Add permission checks
   - Ensure proper tenant context passing

### Phase 3: Response Types (Days 5-6)

8. **Implement "reply" Responses**
   - Create ReplyResponse component
   - Add markdown rendering
   - Handle structured data display

9. **Implement "ask" Responses**
   - Create AskResponse component
   - Add appropriate form controls
   - Implement field validation
   - Handle continuation flow

10. **Implement "async" Responses**
    - Create AsyncResponse component
    - Add loading indicators
    - Implement polling mechanism
    - Handle completion state updates

11. **Implement "fallback" and "intent_clarification" Responses**
    - Create FallbackResponse component
    - Create ClarificationResponse component
    - Handle option selection

### Phase 4: Enhancement & Polish (Days 7-8)

12. **Error Handling & Recovery**
    - Add robust error handling
    - Implement network error recovery
    - Add retry mechanisms

13. **Conversation History**
    - Implement conversation persistence
    - Add history browsing functionality
    - Create conversation export feature

14. **UI Polish & Accessibility**
    - Add responsive design improvements
    - Implement keyboard navigation
    - Ensure screen reader compatibility
    - Add animations and transitions

15. **Documentation & Testing**
    - Write component tests
    - Create documentation
    - Perform accessibility testing

## Technical Considerations

### Message Typing

```typescript
interface Message {
  id: string;
  content: string;
  timestamp: Date;
  isUser: boolean;
  responseType?: 'reply' | 'ask' | 'async' | 'fallback' | 'intent_clarification';
  responseData?: any;
  status?: 'sending' | 'sent' | 'error';
  error?: string;
}

interface ResolveResponse {
  kind: 'reply' | 'ask' | 'async' | 'fallback' | 'intent_clarification';
  text?: string;
  field?: string;
  question?: string;
  workflow_id?: string;
  status_url?: string;
  options?: Array<{
    id: string;
    text: string;
  }>;
  [key: string]: any;
}
```

### Async Workflow Polling

For "async" responses, we'll implement a polling mechanism:

1. Start with a short interval (1 second)
2. Gradually increase the interval using exponential backoff
3. Set a maximum interval (30 seconds)
4. Continue until the workflow completes or fails
5. Update the UI in real-time as status changes

### Conversation Persistence

To maintain conversation history:

1. Store messages in component state during the session
2. Optionally persist to localStorage for resuming conversations
3. Add the ability to export conversation history
4. Consider adding a conversation selector if multiple threads are supported

### Accessibility Considerations

- Ensure keyboard navigation (Tab, Enter, Escape)
- Add appropriate ARIA attributes to all components
- Include proper focus management
- Maintain sufficient color contrast
- Add visible focus indicators
- Support screen readers with appropriate announcements

## Testing Strategy

### Unit Tests

- Test individual components in isolation
- Mock API responses for all response types
- Verify UI state updates correctly
- Test error handling

### Integration Tests

- Test the complete chat flow
- Verify authentication and permissions
- Test all response types in sequence
- Ensure proper conversation threading

### Specific Test Cases

1. Starting a new conversation
2. Continuing an existing conversation
3. Handling "ask" responses with different field types
4. Processing "async" responses with polling
5. Testing error states and recovery
6. Verifying message history persistence
7. Testing markdown rendering in responses
8. Ensuring all UI elements are accessible
9. Testing responsive design on different screen sizes
10. Verifying that conversation context is maintained

## Success Metrics

- The chat interface successfully connects to the backend endpoints
- All response types are handled appropriately
- The interface is intuitive and easy to use
- The interface is responsive and works on mobile devices
- The interface is accessible to all users
- Administrators can effectively test the intent pipeline
- The interface helps identify areas for improvement in the underlying systems

## Future Enhancements

- Add support for file attachments
- Implement conversation branching
- Add template editor integration
- Create a visual representation of the intent matching process
- Add support for voice input
- Implement internationalization
- Add analytics for tracking common intents and failure points
- Create a conversation library for sharing and reusing test cases