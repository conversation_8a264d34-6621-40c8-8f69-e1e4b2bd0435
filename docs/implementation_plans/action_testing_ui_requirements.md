# Action Testing UI Requirements

Based on an analysis of the current Coherence admin site, this document outlines the requirements for implementing an effective action testing interface. The existing admin interface has a solid foundation for integration management but lacks crucial components for action testing, execution monitoring, and comprehensive API credential handling.

## Current State Assessment (Updated May 13, 2025)

The Coherence admin site currently offers:

- Improved integration management (listing and details)
- OpenAPI spec importing
- Template editing with Monaco editor
- Action configuration in JSON format with improved validation
- Simple workflow creation
- API key management
- Clerk-based authentication with tenant mapping
- Template vectorization and search using standardized 384-dimension embeddings
- Enhanced sidebar navigation with improved ordering (Integrations → Templates → Workflows)
- Backend API endpoints for action testing

## Implementation Progress

### Completed Features ✅
- Template editor with Monaco editor implementation
- Integration management UI enhancements including updated sidebar navigation
- Authentication migration and improvements with better session handling
- Template vectorization with standardized 384-dimension embeddings
- Efficient vector search for templates
- Admin actions API endpoints for testing (`/test`) and retrieving history (`/test-history`)
- ActionTestRequest and ActionTestResponse schemas
- Initial ActionTestConsole component structure

### In Progress 🔄
- Integration management interface improvements
- Action configuration JSON validation enhancements
- Initial workflow management components
- Authentication and permission handling enhancements
- Parameter input form with validation for action testing
- Request/response viewers for action testing

## Critical Gaps in Action Management

1. **Incomplete Action Testing Interface**
   - Backend API endpoints for testing are now implemented ✅
   - Initial component structure created ✅
   - Still missing frontend implementation for parameter input form
   - Need visualization of request/response pairs
   - Need test history UI

2. **Partial API Testing Tools**
   - Backend support for testing endpoints directly now available ✅
   - Still need frontend request builder implementation
   - Missing credential validation mechanism
   - Need result visualization components

3. **Incomplete Credential Management**
   - Limited OAuth flow support
   - No credential health monitoring
   - No rotation management

4. **Absent Action Monitoring**
   - No execution history
   - No performance metrics
   - No error tracking

5. **Missing Relationship Visualization**
   - No clear mapping between templates and endpoints
   - No dependency visualization
   - No impact analysis for changes

## Implementation Requirements

### 1. Action Testing Console (High Priority)

A dedicated interface for testing actions against real APIs with the following components:

- **Parameter Input Form**
  - Dynamic form generation based on action schema
  - Input validation against parameter constraints
  - Default value population
  - Parameter history/suggestions

- **Execution Controls**
  - Execute button with loading state
  - Environment selector (dev/test/prod)
  - Credential selector for the integration
  - Save test case functionality

- **Request Viewer**
  - Formatted request body with syntax highlighting
  - Headers display
  - URL and method indication
  - Raw/formatted toggle

- **Response Viewer**
  - Formatted response with syntax highlighting
  - Status code and timing information
  - Error highlighting and explanation
  - Raw/formatted toggle
  - JSON/XML/Text format options

- **Test History**
  - List of previous test executions
  - Success/failure indicators
  - Timing information
  - Ability to repeat a test
  - Export test results

### 2. Credential Management Enhancements (High Priority)

- **Credential Health Dashboard**
  - Expiration indicators
  - Usage statistics
  - Last used timestamp
  - Success rate visualization

- **OAuth Flow Manager**
  - Visual OAuth flow representation
  - Step-by-step guide for authorization
  - Token refresh monitoring
  - Scope visualization and management

- **Credential Testing Utility**
  - Direct test of credentials
  - Status indicators (valid/invalid/expired)
  - Error diagnostics for invalid credentials
  - Auto-refresh capability for tokens

### 3. Action-Template-Endpoint Relationship Visualizer (Medium Priority)

- **Relationship Graph**
  - Visual representation of connections
  - Template → Action → Endpoint flow visualization
  - Dependency highlighting
  - Filter by integration/template/endpoint

- **Impact Analysis Tool**
  - Change simulation for templates
  - Affected endpoints visualization
  - Version comparison
  - Breaking change detection

### 4. Action Execution Monitoring (Medium Priority)

- **Execution Dashboard**
  - Real-time execution status
  - Historical execution logs
  - Success/failure rates
  - Performance metrics
  - Usage patterns

- **Log Explorer**
  - Detailed execution logs
  - Filter by status, date, integration
  - Parameter and result inspection
  - Error details and resolution suggestions

## UI/UX Design Guidelines

1. **Consistency with Existing UI**
   - Maintain the existing design language
   - Use the same component patterns
   - Ensure responsive design
   - Follow the existing navigation structure

2. **Intuitive Testing Flow**
   - Clear steps for testing an action
   - Immediate feedback on execution
   - Visual cues for status
   - Help text and tooltips

3. **Informative Visualizations**
   - Use charts for metrics
   - Color coding for status
   - Clear relationship indicators
   - Consistent iconography

4. **Accessibility**
   - Keyboard navigation
   - Screen reader compatibility
   - Sufficient color contrast
   - Focus indication

## Technical Implementation Considerations

### Integration with Existing Codebase

- Extend the current integration detail page with a new "Testing" tab
- Add action testing components to the template editor
- Create new routes for monitoring and visualization
- Extend the API client to support test executions

### Component Structure

```typescript
// Key components to implement

// 1. Action testing console
ActionTestConsole
├── ParameterInputForm
├── ExecutionControls
├── RequestViewer
├── ResponseViewer
└── TestHistoryPanel

// 2. Credential management
CredentialHealthDashboard
├── ExpirationIndicator
├── UsageStatistics
└── TokenRefreshControls

OAuthFlowManager
├── AuthorizationSteps
├── TokenDisplay
└── ScopeManager

// 3. Relationship visualizer
RelationshipGraph
├── TemplateNode
├── ActionNode
├── EndpointNode
└── ConnectionLine

ImpactAnalysisTool
├── ChangeSimulator
├── AffectedItemsList
└── VersionComparer

// 4. Execution monitoring
ExecutionDashboard
├── RealTimeStatus
├── HistoricalMetrics
└── ErrorRateChart

LogExplorer
├── LogFilterControls
├── LogEntryList
└── LogDetailView
```

### API Requirements

The frontend components will require these API endpoints:

1. ✅ `/v1/admin/actions/test` - Execute an action with parameters (Implemented)
2. ✅ `/v1/admin/actions/history` - Get execution history (Implemented as `/test-history`)
3. `/v1/admin/credentials/validate` - Test credential validity (Pending)
4. `/v1/admin/integrations/relationships` - Get template-action-endpoint mappings (Pending)
5. `/v1/admin/actions/metrics` - Get execution metrics (Pending)

## Implementation Phases

### Phase 1: Action Testing Core (Current Priority)
- ✅ Implement backend API endpoints for action testing
- ✅ Create ActionTestRequest and ActionTestResponse schemas
- ✅ Set up initial ActionTestConsole component structure
- 🔄 Build the complete Action Testing Console component
- 🔄 Implement parameter input form generation
- 🔄 Create request/response viewers
- ✅ Implement execution against real APIs via backend endpoints

### Phase 2: Credential Management
- Enhance credential visualization
- Add credential testing functionality
- Implement OAuth flow management
- Create credential health indicators

### Phase 3: Monitoring and Relationships
- Build execution history viewer
- Implement relationship visualizer
- Create metrics dashboard
- Develop impact analysis tool

## Success Criteria

The implementation will be considered successful when:

1. Users can test actions against real APIs with actual credentials
2. The system provides clear feedback on execution success/failure
3. Users can visualize relationships between templates, actions, and endpoints
4. The UI provides insights into action performance and reliability
5. Credential health and validity can be easily monitored

## Next Steps

1. Complete the implementation of the ActionTestConsole component:
   - ✅ Basic component structure has been created
   - Implement dynamic parameter form generation based on action schema
   - Build the request/response viewers with formatting and syntax highlighting
   - Add test history panel to display previous executions

2. ✅ Backend API endpoints for action testing:
   - ✅ Implemented `/v1/admin/actions/test` endpoint
   - ✅ Added authentication and permission checks
   - ✅ Ensured proper error handling and formatting
   - ✅ Created `/v1/admin/actions/test-history` endpoint

3. Integrate with the existing integration detail page:
   - Add a "Testing" tab to the integration detail page
   - Connect the testing console to the integration APIs
   - Implement credential selection functionality
   
4. UI Navigation improvements:
   - ✅ Updated sidebar navigation order to prioritize Integrations → Templates → Workflows
   - ✅ Fixed GitHub documentation link
   - Implement breadcrumbs for better navigation within integration testing
   
5. Additional enhancements:
   - Implement credential validation and health checks
   - Create visual feedback for action test results
   - Add export functionality for test results

By implementing these enhancements, the Coherence admin site will provide a comprehensive interface for managing, testing, and monitoring actions across integrations.