# Template-Driven API Action System: Enhanced Implementation Plan

## Next Steps
*Updated: May 8, 2025*

1. **Response Caching Layer (Priority: High)** ✅ COMPLETED
   - ✅ Implemented the ApiResponseCache class with Redis backing
   - ✅ Added deterministic cache key generation based on API parameters
   - ✅ Integrated configurable TTL from template definitions
   - ✅ Implemented pattern-based cache invalidation mechanism
   - ✅ Added metrics collection for cache hit/miss ratios
   - ✅ Wrote comprehensive unit tests for the caching system
   - ✅ Integrated with DynamicActionExecutor for seamless caching

2. **API Health Monitoring (Priority: High)** ✅ COMPLETED
   - ✅ Implemented the ApiHealthMonitor class with Redis-backed storage
   - ✅ Created background task for periodic health checks
   - ✅ Added status change callback system for alerting
   - ✅ Integrated with existing metrics system (TenantMetricsCollector) 
   - ✅ Added circuit breaker integration for seamless error handling
   - ✅ Enabled multi-tenant support with namespace isolation
   - ✅ Implemented automatic API monitoring on application startup
   - ✅ Added comprehensive unit tests for health monitoring
   - ✅ Created integration tests for health monitoring system

3. **Template-Driven API Integration Tests (Priority: High)** ✅ COMPLETED
   - ✅ Implemented integration tests for the full API workflow
   - ✅ Added test cases for fallback handling
   - ✅ Created test utilities for mocking external services
   - ✅ Verified proper interaction between all components

4. **Visual Template Editor (Priority: Medium)** ✅ COMPLETED
   - ✅ Implemented modern template editor UI with Refine.js and Shadcn UI components
   - ✅ Created tabbed interface for template editing (Basic Settings, Template Code, Parameters, Response, Error Handling)
   - ✅ Added version history sidebar with timeline view
   - ✅ Implemented in-browser template testing functionality
   - ✅ Built parameter mapping management system
   - ✅ Added error handling configuration (retry count, fallback values)

5. **Auto-Documentation (Priority: Medium)** 
   - Implement the ApiDocumentationGenerator class as defined in section 4.6
   - Create markdown and HTML output formats
   - Add endpoints to generate and view documentation
   - Integrate with the admin interface

6. **API Testing Tools (Priority: Medium)**
   - Design and implement the ApiTester class
   - Add support for sample data testing
   - Implement mock response system
   - Create user-friendly testing interface in the admin UI

## 1. Overview

This document outlines an enhanced implementation plan for Coherence's template-driven API action system. The system will enable admins to integrate external APIs (starting with OpenAPI/Swagger specifications) without writing API-specific code. All API configurations, endpoints, and rules will be stored in templates attached to intents.

## 2. Core Architecture

### 2.1 Template Structure 

Action templates will use the existing template system infrastructure with the `actions` JSONB field storing the API configuration:

```json
{
  "api_key": "weather_api",
  "spec_id": "openmeteo_v1",
  "endpoint": "/v1/forecast",
  "method": "GET",
  "parameter_mapping": {
    "city": "{{ parameters.location.city }}",
    "lat": "{{ parameters.location.latitude }}",
    "lon": "{{ parameters.location.longitude }}",
    "temperature_unit": "celsius"
  },
  "response_mapping": {
    "current_temp": "{{ response.current.temperature_2m }}",
    "forecast": "{{ response.daily.temperature_2m_max | join(', ') }}",
    "unit": "{{ response.current_units.temperature_2m }}"
  },
  "authentication": {
    "type": "api_key",
    "location": "header",
    "name": "X-API-Key",
    "value": "{{ credentials.weather_api }}"
  },
  "error_handling": {
    "timeout_ms": 5000,
    "retries": 2,
    "fallback_template": "weather_fallback"
  }
}
```

### 2.2 Component Integration

The system consists of the following components:

1. **OpenAPI Adapter Enhancement**: Import and parse OpenAPI specs to automatically generate action templates
2. **DynamicActionExecutor**: Execute API calls based on template configuration 
3. **Parameter Mapper**: Map intent parameters to API parameters
4. **Response Transformer**: Transform API responses into coherent user messages
5. **ApiResponseCache**: Cache API responses to reduce load on external APIs
6. **ApiHealthMonitor**: Monitor the health of integrated APIs
7. **Admin Interface**: Register and manage API integrations without coding

## 3. Implementation Steps

### 3.1 OpenAPI Adapter Enhancement

Extend the existing OpenAPI adapter to generate action templates from API specs:

```python
def generate_action_templates(self, spec_id: str, api_key: str) -> list[dict]:
    """Generate action templates from an OpenAPI specification.
    
    Args:
        spec_id: The ID of the imported OpenAPI spec
        api_key: The key to identify the API
        
    Returns:
        A list of template definitions ready to be created
    """
    templates = []
    
    # Extract paths from the OpenAPI spec
    for path, path_item in self.spec.get("paths", {}).items():
        for method, operation in path_item.items():
            if method.lower() not in ["get", "post", "put", "delete", "patch"]:
                continue
                
            # Create template key
            operation_id = operation.get("operationId") or f"{method}_{path}".lower()
            template_key = f"{api_key}_{operation_id}".replace("/", "_")
            
            # Extract parameters
            parameters = self._extract_parameters(operation)
            
            # Create action configuration
            action = {
                "api_key": api_key,
                "spec_id": spec_id,
                "endpoint": path,
                "method": method.upper(),
                "parameter_mapping": self._generate_parameter_mapping(parameters),
                "response_mapping": self._generate_response_mapping(operation),
                "authentication": self._extract_authentication(),
                "error_handling": {
                    "timeout_ms": 5000,
                    "retries": 2,
                    "fallback_template": f"{template_key}_fallback"
                }
            }
            
            # Create template definition
            template = {
                "key": template_key,
                "category": "action",
                "body": self._generate_default_body(operation),
                "actions": [action],
                "parameters": parameters,
                "metadata": {
                    "description": operation.get("summary", ""),
                    "tags": operation.get("tags", []),
                    "generated": True
                }
            }
            
            templates.append(template)
            
            # Create fallback template
            fallback_template = {
                "key": f"{template_key}_fallback",
                "category": "fallback",
                "body": "I'm sorry, I couldn't get the information you requested due to a technical issue.",
                "actions": [],
                "parameters": []
            }
            
            templates.append(fallback_template)
            
    return templates
```

### 3.2 Dynamic Action Executor

Implement a new class to execute API actions based on template configuration:

```python
class DynamicActionExecutor:
    def __init__(
        self, 
        template_service: TemplateService,
        credential_manager: CredentialManager,
        http_client: Optional[httpx.AsyncClient] = None
    ):
        self.template_service = template_service
        self.credential_manager = credential_manager
        self.http_client = http_client or httpx.AsyncClient()
        self.circuit_breaker = CircuitBreaker(failure_threshold=5, recovery_timeout=60)
        
    async def execute(
        self, 
        action_config: dict,
        parameters: dict,
        tenant_id: UUID
    ) -> dict:
        """Execute an API action based on template configuration.
        
        Args:
            action_config: The action configuration from the template
            parameters: The intent parameters
            tenant_id: The tenant ID for credential lookup
            
        Returns:
            The API response or error information
        """
        # Map parameters using renderer
        mapped_params = await self._map_parameters(action_config, parameters)
        
        # Build request
        url = await self._build_url(action_config, mapped_params)
        headers = await self._build_headers(action_config, tenant_id)
        body = await self._build_body(action_config, mapped_params)
        
        # Execute with circuit breaker
        try:
            async with self.circuit_breaker(action_config["api_key"]):
                response = await self._make_request(
                    method=action_config["method"],
                    url=url,
                    headers=headers,
                    json=body,
                    timeout=action_config.get("error_handling", {}).get("timeout_ms", 5000) / 1000
                )
                
                # Map response
                return await self._map_response(action_config, response.json())
        except CircuitBreakerError:
            # Return fallback
            return await self._handle_fallback(action_config, parameters, tenant_id)
        except Exception as e:
            # Handle and log error
            return await self._handle_error(action_config, e, parameters, tenant_id)
```

### 3.3 ChatOrchestrator Update

Update the ChatOrchestrator to use the DynamicActionExecutor:

```python
async def _execute_action(self, intent: IntentMatch, parameters: dict) -> str:
    """Execute the action associated with the intent.
    
    Args:
        intent: The matched intent
        parameters: The extracted parameters
        
    Returns:
        The response message to the user
    """
    try:
        # Get the template for the intent
        template = await self.template_service.get_template(
            intent.template_key, 
            tenant_id=self.tenant_id
        )
        
        if not template or not template.actions:
            logger.warning(f"No actions found for template {intent.template_key}")
            return await self._handle_fallback(intent, parameters, "no_actions")
            
        # Execute each action in the template
        results = {}
        for action_config in template.actions:
            action_result = await self.action_executor.execute(
                action_config=action_config,
                parameters=parameters,
                tenant_id=self.tenant_id
            )
            results[action_config.get("api_key", "default")] = action_result
            
        # Render the template with the results
        context = {
            "parameters": parameters,
            "results": results,
            "intent": intent.dict()
        }
        
        return await self.template_service.render(
            template.key,
            context=context,
            tenant_id=self.tenant_id
        )
    except Exception as e:
        logger.exception(f"Error executing action for intent {intent.name}")
        return await self._handle_fallback(intent, parameters, str(e))
```

## 4. Enhanced Features

### 4.1 Template Validation System

Implement a robust validation system for action templates:

```python
class ActionTemplateValidator:
    async def validate(self, template: dict) -> tuple[bool, list[str]]:
        """Validate an action template.
        
        Args:
            template: The template to validate
            
        Returns:
            A tuple of (is_valid, [error_messages])
        """
        errors = []
        
        # Check required fields
        required_fields = ["api_key", "spec_id", "endpoint", "method"]
        for field in required_fields:
            if field not in template:
                errors.append(f"Missing required field: {field}")
                
        # Validate parameter mapping
        if "parameter_mapping" in template:
            for param, value in template["parameter_mapping"].items():
                if "{{" in value and "}}" in value:
                    if not await self._validate_template_expression(value):
                        errors.append(f"Invalid template expression in parameter mapping: {value}")
                        
        # Validate method
        if "method" in template and template["method"] not in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
            errors.append(f"Invalid HTTP method: {template['method']}")
            
        # More validation rules...
        
        return len(errors) == 0, errors
```

### 4.2 Visual Template Editor

Design for an admin interface with visual template editing:

```
+-------------------------------------------------------------------+
|                      API Integration Dashboard                     |
+-------------------------------------------------------------------+
|                                                                   |
| [Import OpenAPI Spec] [Manage Templates] [Test Integration]       |
|                                                                   |
| API Name: Weather API                         Status: ✅ Active   |
| Base URL: https://api.open-meteo.com                              |
|                                                                   |
| ┌────────────────────────────────────────────────────────────────┐|
| │                     Template Configuration                     ││
| ├────────────────────────────────────────────────────────────────┤|
| │                                                                ││
| │ Template Key: weather_forecast                                 ││
| │ Category:     action                                           ││
| │                                                                ││
| │ ┌────────────────┐ ┌─────────────────┐ ┌──────────────────┐   ││
| │ │ Basic Settings │ │ Parameters      │ │ Response Mapping │   ││
| │ └────────────────┘ └─────────────────┘ └──────────────────┘   ││
| │                                                                ││
| │ Endpoint: /v1/forecast                                         ││
| │ Method:   GET                                                  ││
| │                                                                ││
| │ Parameter Mapping:                                             ││
| │ ┌───────────────┬───────────────────────────────────────────┐ ││
| │ │ API Parameter │ Template Expression                       │ ││
| │ ├───────────────┼───────────────────────────────────────────┤ ││
| │ │ latitude      │ {{ parameters.location.latitude }}        │ ││
| │ │ longitude     │ {{ parameters.location.longitude }}       │ ││
| │ │ daily         │ temperature_2m_max                        │ ││
| │ │ timezone      │ GMT                                       │ ││
| │ └───────────────┴───────────────────────────────────────────┘ ││
| │                                                                ││
| │ [+ Add Parameter]                                              ││
| │                                                                ││
| │ [Test Request]              [Save Template]                    ││
| │                                                                ││
| └────────────────────────────────────────────────────────────────┘|
|                                                                   |
+-------------------------------------------------------------------+
```

### 4.3 Parameter Transformation Logic

Enhance the parameter mapping with transformation capabilities:

```python
class ParameterTransformer:
    """Transform parameters between formats using built-in and custom functions."""
    
    def __init__(self):
        self.transformers = {
            "to_iso_date": self._to_iso_date,
            "capitalize": self._capitalize,
            "join": self._join,
            "split": self._split,
            "to_number": self._to_number,
            "format": self._format,
            # More transformers...
        }
        
    async def transform(self, value: Any, transformation: str, **kwargs) -> Any:
        """Apply a transformation to a value.
        
        Args:
            value: The value to transform
            transformation: The transformation to apply
            **kwargs: Additional arguments for the transformation
            
        Returns:
            The transformed value
        """
        if transformation not in self.transformers:
            raise ValueError(f"Unknown transformation: {transformation}")
            
        return await self.transformers[transformation](value, **kwargs)
        
    async def _to_iso_date(self, value: str, **kwargs) -> str:
        """Convert a date string to ISO format."""
        # Implementation
        
    # More transformation methods...
```

### 4.4 Caching Layer (✅ COMPLETED)

The ApiResponseCache class has been fully implemented with the following features:
- Redis-backed caching with configurable TTL
- Deterministic cache key generation based on API parameters
- Pattern-based cache invalidation
- Cache hit/miss metrics collection
- Integration with DynamicActionExecutor
- Comprehensive unit tests

```python
class ApiResponseCache:
    """
    Cache API responses to reduce external API calls.
    
    This class provides caching functionality for the template-driven
    API action system, with support for:
    - TTL-based expiration configurable per API call
    - Namespace isolation for multi-tenant deployments
    - Pattern-based cache invalidation
    - Metrics for cache hit/miss rates
    """
    
    def __init__(
        self, 
        redis_client: RedisClient,
        namespace: str = "api_cache",
        default_ttl: int = 300
    ):
        """Initialize the API response cache."""
        self.redis = redis_client
        self.namespace = namespace
        self.default_ttl = default_ttl
        self.metrics = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "invalidations": 0
        }
        
    async def get(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get a cached API response."""
        key = f"{self.namespace}:{cache_key}"
        cached = await self.redis.get(key)
        
        if cached:
            self.metrics["hits"] += 1
            try:
                return json.loads(cached)
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in cache for key: {cache_key}")
                return None
        
        self.metrics["misses"] += 1
        return None
        
    async def set(
        self, 
        cache_key: str, 
        response: Dict[str, Any], 
        ttl_seconds: Optional[int] = None
    ) -> bool:
        """Cache an API response."""
        # Implementation...
        
    # Additional methods...
```

### 4.5 API Health Monitoring (✅ COMPLETED)

The ApiHealthMonitor class has been fully implemented with the following features:
- Redis-backed status storage
- Background health check scheduling
- Support for different health check configurations
- Status change callbacks for alerting
- Integration with metrics system
- Multi-tenant isolation
- Circuit breaker integration
- Comprehensive unit tests

```python
class ApiHealthMonitor:
    """
    Monitor the health of integrated APIs.
    
    This class provides functionality to:
    1. Register health checks for APIs
    2. Run regular health checks in the background
    3. Track API health status
    4. Generate alerts when API status changes
    5. Integrate with metrics and circuit breaker
    """
    
    def __init__(
        self,
        redis_client: RedisClient,
        metrics_collector: Optional[TenantMetricsCollector] = None,
        http_client: Optional[httpx.AsyncClient] = None,
        status_ttl_days: int = 30,
        namespace: str = "api_health"
    ):
        """Initialize the API health monitor."""
        self.redis = redis_client
        self.metrics = metrics_collector or TenantMetricsCollector()
        self.http_client = http_client or httpx.AsyncClient()
        self.status_ttl_days = status_ttl_days
        self.namespace = namespace
        self.health_checks: Dict[str, HealthCheckConfig] = {}
        self.last_status: Dict[str, ApiStatus] = {}
        self.is_running = False
        self.status_change_callbacks: List[Callable[[str, ApiStatus, ApiStatus], None]] = []
        
    async def register_health_check(
        self,
        config: HealthCheckConfig,
        tenant_id: Optional[uuid.UUID] = None
    ) -> None:
        """Register a health check for an API."""
        # Implementation...
        
    # Additional methods...
```

### 4.6 Auto-Documentation

Generate documentation for API integrations automatically:

```python
class ApiDocumentationGenerator:
    """Generate documentation for API integrations."""
    
    async def generate_markdown(
        self,
        api_key: str,
        spec_id: str,
        templates: list[dict]
    ) -> str:
        """Generate Markdown documentation for an API integration.
        
        Args:
            api_key: The API key
            spec_id: The OpenAPI spec ID
            templates: The generated templates
            
        Returns:
            Markdown documentation
        """
        # Implementation...
```

### 4.7 Testing Tools

Create tools for testing and debugging API integrations:

```python
class ApiTester:
    """Test API integrations with mocking capabilities."""
    
    async def test_with_sample_data(
        self,
        template_key: str,
        sample_parameters: dict,
        tenant_id: UUID
    ) -> dict:
        """Test an API integration with sample data.
        
        Args:
            template_key: The template key
            sample_parameters: Sample parameters
            tenant_id: The tenant ID
            
        Returns:
            Test results
        """
        # Implementation...
        
    async def mock_response(
        self,
        template_key: str,
        mock_response: dict,
        tenant_id: UUID
    ) -> None:
        """Register a mock response for an API endpoint.
        
        Args:
            template_key: The template key
            mock_response: The mock response
            tenant_id: The tenant ID
        """
        # Implementation...
```

## 5. Implementation Timeline

### Phase 1: Core Functionality (2 weeks) - COMPLETED
- ✅ Enhance OpenAPI adapter to generate action templates
- ✅ Implement DynamicActionExecutor
- ✅ Update ChatOrchestrator

### Phase 2: Enhanced Features (3 weeks) - COMPLETED
- ✅ Template validation system
- ✅ Parameter transformation logic 
- ✅ Response caching system with Redis backend
- ✅ API health monitoring with circuit breaker integration
- ✅ Template-driven API integration tests

### Phase 3: Admin Interface (2-3 weeks) - PARTIALLY COMPLETED
- ✅ Visual template editor
- ✅ Credential management interface
- ✅ Migrated to Clerk auth for admin UI
- 🔄 Auto-documentation (in progress)
- 🔄 API testing tools (in progress)

## 6. Rollout Strategy

1. **Testing Environment**: Deploy to a staging environment with test tenants
2. **Beta Tenants**: Identify 2-3 tenants for beta testing
3. **Validation**: Confirm that all test cases pass and beta tenants are satisfied
4. **Documentation**: Complete admin and developer documentation
5. **Production Rollout**: Staged rollout to production tenants
6. **Monitoring**: Implement enhanced monitoring during rollout

## 7. Success Metrics

- **Integration Time**: Time required to integrate a new API (target: <30 minutes)
- **API Performance**: Response time for API actions (target: <500ms)
- **Error Rate**: Percentage of failed API actions (target: <1%)
- **Code Reduction**: Lines of code eliminated by using templates (target: >2000)
- **Admin Satisfaction**: Qualitative feedback from admins (target: Very Satisfied)

## 8. Risk Mitigation

| Risk | Mitigation |
|------|------------|
| API schema changes | Implement version tracking and schema validation |
| API downtime | Use circuit breakers and fallback templates |
| Performance bottlenecks | Implement caching and monitoring |
| Security vulnerabilities | Regular security audits and parameter sanitization |
| Template complexity | Provide templates library and validation tools |

## 9. Example Usage

### 9.1 Creating a Weather API Integration

This example demonstrates the complete flow of integrating the Open-Meteo weather API:

#### Step 1: Import the OpenAPI Specification

```bash
# Import the OpenAPI spec via the admin panel or API
curl -X POST "https://api.coherence.ai/v1/admin/integrations" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Weather API",
    "spec_url": "https://api.open-meteo.com/openapi.json"
  }'

# Response includes integration_id which we'll use in subsequent steps
# {
#   "integration_id": "123e4567-e89b-12d3-a456-426614174000",
#   "name": "Weather API",
#   "endpoints": [...]
# }
```

#### Step 2: Generate Action Templates

```bash
# Generate action templates from the imported spec
curl -X POST "https://api.coherence.ai/v1/admin/integrations/123e4567-e89b-12d3-a456-426614174000/templates" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "api_key": "weather_api"
  }'

# Response contains all generated templates
```

#### Step 3: Configure Intent-Template Mapping

```json
// Create a new intent for weather forecasts
{
  "name": "weather_forecast",
  "description": "Get weather forecast for a location",
  "examples": [
    "What's the weather like in New York?",
    "Show me the forecast for San Francisco",
    "Will it rain tomorrow in Chicago?"
  ],
  "parameters": {
    "location": {
      "type": "string",
      "required": true,
      "description": "The location to get weather for",
      "prompt": "What location would you like the weather for?"
    },
    "date": {
      "type": "date",
      "required": false,
      "description": "The date to get weather for",
      "prompt": "What date would you like the weather for?",
      "default_value": "today"
    }
  },
  "template_key": "weather_api_forecast"
}
```

#### Step 4: Store API Credentials

```bash
# Store API credentials (if required)
curl -X POST "https://api.coherence.ai/v1/admin/credentials" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "integration_id": "123e4567-e89b-12d3-a456-426614174000",
    "credentials": {
      "weather_api": "your_api_key_here"
    }
  }'
```

#### Step 5: Test the Integration

```bash
# Test the integration with sample parameters
curl -X POST "https://api.coherence.ai/v1/resolve" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What\'s the weather like in New York?",
    "tenant_id": "tenant-123",
    "user_id": "user-456"
  }'

# Expected response:
# {
#   "type": "action",
#   "outcome": "The current temperature in New York is 72°F. The forecast for the next few days is 75°F, 78°F, 73°F, 71°F, 70°F."
# }
```

### 9.2 Custom API Template Example

Here's how to manually create a template for a custom API:

```json
{
  "key": "stock_price_lookup",
  "category": "response_gen",
  "body": "The current stock price for {{ parameters.symbol }} is ${{ results.stock_api.price }}. The stock has {{ 'increased' if results.stock_api.change > 0 else 'decreased' }} by {{ results.stock_api.change|abs }}% today.",
  "actions": [
    {
      "api_key": "stock_api",
      "endpoint": "https://api.example.com/v1/stock/quote",
      "method": "GET",
      "parameter_mapping": {
        "symbol": "{{ parameters.symbol|upper }}",
        "fields": "price,change,volume"
      },
      "response_mapping": {
        "price": "{{ response.data.price }}",
        "change": "{{ response.data.change }}",
        "volume": "{{ response.data.volume }}"
      },
      "authentication": {
        "type": "api_key",
        "location": "header",
        "name": "X-API-Key",
        "value": "{{ credentials.stock_api }}"
      },
      "error_handling": {
        "timeout_ms": 3000,
        "retries": 1,
        "fallback_template": "stock_price_fallback"
      }
    }
  ],
  "parameters": {
    "symbol": {
      "type": "string",
      "description": "Stock symbol (e.g., AAPL, MSFT)",
      "required": true
    }
  }
}
```

## 10. Testing Strategy

### 10.1 Unit Tests (✅ COMPLETED)

We've implemented comprehensive unit tests for each component of the system:

- OpenAPIAdapter
- DynamicActionExecutor
- ApiResponseCache
- ApiHealthMonitor
- ParameterTransformer
- ActionTemplateValidator

### 10.2 Integration Tests (✅ COMPLETED)

The integration tests verify the end-to-end flow from template generation to API execution:

```python
@pytest.mark.asyncio
async def test_template_driven_weather_api():
    """
    Test the complete flow of the template-driven API action system:
    1. Generate templates from an OpenAPI spec
    2. Execute a weather query based on the template
    3. Verify the response transformation
    """
    # Tests implemented...
```

### 10.3 Manual Testing (🔄 IN PROGRESS)

For manual testing, we provide a comprehensive checklist to ensure all features work as expected:

- [ ] Import an OpenAPI spec for a real weather API
- [ ] Generate action templates
- [ ] Create and link an intent for weather queries
- [ ] Configure API credentials
- [ ] Test basic queries with different locations
- [ ] Test error handling with invalid parameters
- [ ] Test fallback templates by simulating API failures
- [ ] Test parameter transformations
- [ ] Verify response caching behavior
- [ ] Test template validation
- [ ] Test template editing and updating

## 11. Future Enhancements

- **Machine Learning Integration**: Auto-suggest parameter mappings based on field names
- **API Versioning**: Handle API schema versioning and migrations
- **Workflow Orchestration**: Chain multiple API calls together
- **Schema Inference**: Generate parameter schemas from API responses
- **Access Control**: Fine-grained permissions for API integration management