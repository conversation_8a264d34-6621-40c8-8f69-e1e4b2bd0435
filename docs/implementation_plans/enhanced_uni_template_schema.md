# Enhanced Uni-Template Schema Design

## Overview
This document defines the enhanced unified template schema that makes templates fully self-contained for executing any endpoint (API, MCP, or other sources) without requiring external context.

## Core Principles
1. **Self-Contained**: All execution details included in the template
2. **CRFS-Integrated**: Response formatting tightly coupled with endpoint configuration
3. **Extensible**: Support for custom fields and future enhancements
4. **Secure**: Credentials referenced, not embedded
5. **Observable**: Built-in monitoring and debugging support

## Enhanced Schema Structure

```json
{
  "meta": {
    "key": "weather_get_current",
    "endpoint_id": "GET_/v1/current",
    "version": 1,
    "category": "unified",
    "scope": "global|pack|tenant",
    "tags": ["weather", "current", "conditions"],
    "last_validated": "2024-12-04T10:00:00Z"
  },

  "intent": {
    "patterns": [
      "what's the weather in {location}",
      "current weather for {location}",
      "how's the weather in {location}"
    ],
    "confidence_threshold": 0.85,
    "parameter_hints": {
      "location": ["city", "place", "location", "where"]
    },
    "context_requirements": [],
    "fallback_templates": ["general_api_fallback"]
  },

  "credentials": {
    "api_key": {
      "name": "weather_api_key",
      "type": "api_key",
      "source": "vault",
      "rotation_policy": "monthly",
      "fallback_credential": "weather_api_key_secondary"
    }
  },

  "action": {
    "method": "GET",
    "path": "/v1/current",
    "base_url": "${WEATHER_API_URL:https://api.weather.com}",
    "api_version": "v1",
    "timeout": 30000,
    
    "auth": {
      "type": "api_key",
      "placement": "header",
      "header_name": "X-API-Key",
      "credential_ref": "api_key"
    },

    "headers": {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "X-Client-Version": "1.0"
    },

    "query_params": {
      "q": "{{location}}",
      "units": "{{units|default:metric}}"
    },

    "request_body": null,

    "retries": {
      "max_attempts": 3,
      "backoff": "exponential",
      "initial_delay": 1000,
      "max_delay": 10000,
      "retry_on": [502, 503, 504],
      "circuit_breaker": {
        "failure_threshold": 5,
        "timeout": 60000,
        "half_open_requests": 3
      }
    }
  },

  "parameters": {
    "schema": {
      "location": {
        "type": "string",
        "required": true,
        "description": "City name or location",
        "validation": {
          "pattern": "^[A-Za-z\\s,]+$",
          "min_length": 2,
          "max_length": 100
        },
        "normalization": "trim,lowercase",
        "examples": ["New York", "London", "Tokyo"]
      },
      "units": {
        "type": "string",
        "required": false,
        "description": "Temperature units",
        "enum": ["metric", "imperial"],
        "default": "metric"
      }
    },
    
    "ui_fields": {
      "location": {
        "type": "text",
        "label": "Location",
        "placeholder": "Enter city name",
        "help_text": "Enter a city name to get current weather",
        "autocomplete": "geo-location"
      },
      "units": {
        "type": "select",
        "label": "Temperature Units",
        "options": [
          {"value": "metric", "label": "Celsius"},
          {"value": "imperial", "label": "Fahrenheit"}
        ]
      }
    }
  },

  "prompts": {
    "system": "You are a helpful weather assistant. Provide accurate weather information based on API responses.",
    
    "parameter_extraction": {
      "location": {
        "ask": "What location would you like weather information for?",
        "clarify": "I need a specific city or location. Could you please provide one?",
        "validate_error": "Please provide a valid city name (letters and spaces only)."
      }
    },
    
    "completion": {
      "success": "Here's the current weather for {{location}}:\n{{#with response.data}}Temperature: {{temperature}}°{{../parameters.units == 'imperial' ? 'F' : 'C'}}\nConditions: {{conditions}}\nHumidity: {{humidity}}%{{/with}}",
      "no_data": "I couldn't find weather data for {{location}}. Please check the city name and try again.",
      "error": "I'm having trouble getting weather data right now. Please try again later."
    }
  },

  "response": {
    "processing": {
      "extract_paths": {
        "temperature": "$.data.main.temp",
        "conditions": "$.data.weather[0].description",
        "humidity": "$.data.main.humidity",
        "wind_speed": "$.data.wind.speed"
      },
      "transformations": {
        "temperature": "{{round(value, 1)}}",
        "conditions": "{{capitalize(value)}}",
        "wind_speed": "{{value * 3.6}}"
      },
      "validation": {
        "temperature": {"min": -100, "max": 100},
        "humidity": {"min": 0, "max": 100}
      }
    },

    "crfs": {
      "default_format": "structured",
      "formats": {
        "structured": {
          "type": "structured",
          "sections": [
            {
              "type": "text",
              "style": "heading",
              "content": "🌤️ Weather in {{parameters.location|capitalize}}"
            },
            {
              "type": "object",
              "style": "info-card",
              "properties": {
                "Temperature": "{{response.temperature}}°{{parameters.units == 'imperial' ? 'F' : 'C'}}",
                "Conditions": "{{response.conditions}}",
                "Humidity": "{{response.humidity}}%",
                "Wind Speed": "{{response.wind_speed}} km/h"
              }
            },
            {
              "type": "conditional",
              "condition": "{{response.temperature > 30}}",
              "content": {
                "type": "text",
                "style": "warning",
                "content": "⚠️ High temperature alert! Stay hydrated."
              }
            }
          ]
        },
        "minimal": {
          "type": "text",
          "content": "{{parameters.location}}: {{response.temperature}}°, {{response.conditions}}"
        }
      }
    },

    "error_handling": {
      "mapping": {
        "404": {
          "message": "Location '{{parameters.location}}' not found",
          "suggestion": "Please check the spelling or try a nearby major city",
          "crfs_format": "error"
        },
        "401": {
          "message": "Authentication failed",
          "internal_action": "rotate_credential",
          "crfs_format": "error"
        },
        "429": {
          "message": "Rate limit exceeded",
          "retry_after": "{{headers['Retry-After']}}",
          "crfs_format": "rate_limit"
        },
        "500-599": {
          "message": "Weather service is temporarily unavailable",
          "fallback_template": "weather_fallback",
          "crfs_format": "error"
        }
      },
      "recovery_strategies": {
        "rate_limit": {
          "type": "exponential_backoff",
          "use_retry_after": true
        },
        "network_error": {
          "type": "retry",
          "max_attempts": 3
        },
        "timeout": {
          "type": "circuit_breaker",
          "fallback_action": "cache_lookup"
        }
      }
    }
  },

  "caching": {
    "enabled": true,
    "strategy": "time_based",
    "ttl": 600,
    "key_pattern": "weather:{{parameters.location}}:{{parameters.units}}",
    "invalidation": {
      "on_error": false,
      "on_parameter_change": true
    },
    "warm_cache": ["New York", "London", "Tokyo"]
  },

  "execution_context": {
    "timeout": 30000,
    "memory_limit": "128MB",
    "concurrency": {
      "max_parallel": 10,
      "rate_limit": "100/minute"
    },
    "environment": {
      "TZ": "UTC",
      "LOCALE": "en_US"
    }
  },

  "dependencies": {
    "templates": {
      "auth": "weather_auth_template",
      "fallback": "weather_fallback_template"
    },
    "services": {
      "credential_manager": "required",
      "cache": "optional",
      "metrics": "optional"
    }
  },

  "monitoring": {
    "metrics": {
      "latency": {
        "percentiles": [50, 95, 99],
        "alert_threshold": 5000
      },
      "error_rate": {
        "window": "5m",
        "alert_threshold": 0.05
      },
      "success_rate": {
        "window": "5m",
        "alert_threshold": 0.95
      }
    },
    "logging": {
      "level": "INFO",
      "include_request": true,
      "include_response": false,
      "sensitive_fields": ["api_key", "authorization"]
    },
    "tracing": {
      "enabled": true,
      "sample_rate": 0.1
    }
  },

  "docs": {
    "description": "Get current weather conditions for any city worldwide",
    "examples": [
      {
        "input": "What's the weather in Paris?",
        "parameters": {"location": "Paris", "units": "metric"},
        "output": "Paris: 22°C, Partly cloudy"
      }
    ],
    "rate_limits": "100 requests per minute",
    "data_freshness": "Updated every 10 minutes",
    "coverage": "Global coverage with 200,000+ cities"
  },

  "testing": {
    "unit_tests": [
      {
        "name": "test_valid_location",
        "input": {"location": "New York"},
        "mock_response": {"data": {"main": {"temp": 25}}},
        "expected_output": {"temperature": 25}
      }
    ],
    "integration_tests": [
      {
        "name": "test_live_endpoint",
        "input": {"location": "London"},
        "assertions": ["response.status == 200", "response.temperature != null"]
      }
    ]
  }
}
```

## Key Enhancements

### 1. **Credential Management**
- Credentials referenced by name, not embedded
- Support for credential rotation
- Fallback credentials for high availability

### 2. **Request Construction**
- Dynamic URL construction with environment variables
- Query parameter and request body schemas
- Header management with dynamic values

### 3. **Response Processing**
- JSONPath extraction for response data
- Data transformation pipeline
- Response validation rules

### 4. **CRFS Integration**
- Multiple format options (structured, minimal, etc.)
- Conditional rendering based on response data
- Error-specific formatting

### 5. **Error Handling**
- Granular error mapping with suggestions
- Recovery strategies per error type
- Internal actions for self-healing

### 6. **Execution Control**
- Timeout and memory limits
- Rate limiting and concurrency control
- Circuit breaker patterns

### 7. **Observability**
- Built-in metrics collection
- Configurable logging
- Distributed tracing support

### 8. **Testing Support**
- Unit tests with mocked responses
- Integration tests with live endpoints
- Test data generation

## Migration Strategy

1. **Phase 1**: Extend current model to support new fields (backward compatible)
2. **Phase 2**: Create migration tools to enhance existing templates
3. **Phase 3**: Validate and test enhanced templates
4. **Phase 4**: Update orchestrator to use new fields
5. **Phase 5**: Deprecate old template formats

## Benefits

1. **Self-Contained Execution**: LLMs have everything needed in one place
2. **Better Error Handling**: Graceful degradation with clear recovery paths
3. **Enhanced Observability**: Built-in monitoring and debugging
4. **Improved Security**: Credential indirection and rotation
5. **CRFS Integration**: Seamless response formatting
6. **Testing Support**: Validation before deployment