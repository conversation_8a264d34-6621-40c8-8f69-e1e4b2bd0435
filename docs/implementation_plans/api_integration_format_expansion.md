# Implementation Plan: FAPI and BAPI Integration Format Support

## Overview

This implementation plan outlines the approach for expanding Coherence's integration capabilities to support Financial-grade API (FAPI) and Business API (BAPI) formats in addition to the current OpenAPI specification support. The goal is to create a more flexible integration system that can handle multiple API specification formats while maintaining the existing security and integration features.

### Current Implementation

The current system only supports OpenAPI specifications through the following components:

1. **OpenAPIAdapter** (`src/coherence/openapi_adapter/adapter.py`) - Processes OpenAPI specifications, extracts endpoints and auth methods, and creates database records
2. **API Models** (`src/coherence/models/integration.py`) - Stores integration data, endpoints, and authentication configurations
3. **Import Endpoint** (`src/coherence/api/v1/endpoints/openapi.py`) - Handles the API for importing specifications

This implementation leverages strong validation, tenant isolation, and security patterns that must be maintained when adding new format support.

## Business Requirements

1. **Format Support**: Allow importing and processing FAPI and BAPI specifications
2. **Security Compliance**: Support enhanced security requirements, particularly for FAPI
3. **Unified Interface**: Maintain a consistent user experience across all API format types
4. **Backward Compatibility**: Ensure existing OpenAPI integrations continue working

## Technical Approach

We'll implement a specification adapter pattern that transforms various input formats into a standardized internal representation, then leverage our existing OpenAPI processing pipeline. This approach allows us to reuse the extensive functionality already built for OpenAPI processing after converting other formats to a compatible structure.

### Component Architecture

```
┌─────────────┐    ┌─────────────┐    ┌─────────────────┐    ┌───────────────┐
│ Format      │    │ Specification│    │ Unified Internal│    │ Existing      │
│ Detector    ├───►│ Adapters     ├───►│ Representation  ├───►│ OpenAPI       │
└─────────────┘    └─────────────┘    └─────────────────┘    │ Processing    │
                                                             └───────────────┘
```

## Implementation Details

### 1. Database Schema Updates

#### New Tables and Fields

```sql
-- Add format type to api_integrations table
ALTER TABLE api_integrations ADD COLUMN spec_format VARCHAR(50) DEFAULT 'openapi';

-- Create original_spec table to store the original specification
CREATE TABLE api_original_specs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    integration_id UUID NOT NULL REFERENCES api_integrations(id) ON DELETE CASCADE,
    format VARCHAR(50) NOT NULL,
    content JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE (integration_id)
);

-- Add RLS policy
ALTER TABLE api_original_specs ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_policy ON api_original_specs
    USING (integration_id IN (
        SELECT id FROM api_integrations WHERE tenant_id = current_setting('app.current_tenant_id')::UUID
    ));
```

#### Models Implementation

We'll need to update the models in `src/coherence/models/integration.py`:

```python
class SpecFormat(str, Enum):
    """Enum for API specification format values."""
    
    OPENAPI = "openapi"  # OpenAPI specification
    FAPI = "fapi"        # Financial-grade API specification
    BAPI = "bapi"        # Business API specification


class APIIntegration(Base):
    # ... existing fields ...
    
    # Add new column for spec format
    spec_format: Mapped[SpecFormat] = mapped_column(
        ENUM("openapi", "fapi", "bapi", name="spec_format"),
        default="openapi",
        nullable=False,
    )


class APIOriginalSpec(Base):
    """Original API specification for non-OpenAPI formats."""
    
    __tablename__ = "api_original_specs"
    
    # Columns
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    integration_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("api_integrations.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
    )
    format: Mapped[str] = mapped_column(String, nullable=False)
    content: Mapped[Dict[str, object]] = mapped_column(JSONB, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    
    # Relationships
    integration: Mapped["APIIntegration"] = relationship(
        "APIIntegration", back_populates="original_spec"
    )


# Update APIIntegration to add relationship to original_spec
class APIIntegration(Base):
    # ... existing code ...
    
    # Add this relationship
    original_spec: Mapped[Optional["APIOriginalSpec"]] = relationship(
        "APIOriginalSpec",
        back_populates="integration",
        uselist=False,
        cascade="all, delete-orphan",
    )
```

### 2. Format Detection Service

Create a service to automatically detect the specification format from input:

```python
# src/coherence/services/spec_format_detector.py
import json
from enum import Enum
from typing import Dict, Optional, Union


class SpecFormat(str, Enum):
    OPENAPI = "openapi"
    FAPI = "fapi"
    BAPI = "bapi"
    UNKNOWN = "unknown"


class SpecFormatDetector:
    """Detects the format of API specifications."""

    @staticmethod
    def detect_format(spec: Union[Dict, str]) -> SpecFormat:
        """
        Detect the format of an API specification.

        Args:
            spec: The API specification as a dict or JSON string

        Returns:
            SpecFormat enum indicating the detected format
        """
        if isinstance(spec, str):
            try:
                spec = json.loads(spec)
            except json.JSONDecodeError:
                return SpecFormat.UNKNOWN

        # OpenAPI detection
        if "openapi" in spec or "swagger" in spec:
            return SpecFormat.OPENAPI

        # FAPI detection (check for FAPI-specific fields)
        if "financial_api" in spec or any(k.startswith("fapi") for k in spec.keys()):
            return SpecFormat.FAPI

        # BAPI detection (check for BAPI-specific fields)
        if "business_api" in spec or any(k.startswith("bapi") for k in spec.keys()):
            return SpecFormat.BAPI

        # Default to unknown if we can't determine the format
        return SpecFormat.UNKNOWN
```

### 3. Specification Adapter System

Create a flexible adapter system using the Strategy pattern:

```python
# src/coherence/openapi_adapter/spec_adapters/base.py
from abc import ABC, abstractmethod
from typing import Dict, List

from pydantic import BaseModel

class EndpointInfo(BaseModel):
    """Standardized endpoint information."""
    path: str
    method: str
    operation_id: str
    summary: str = ""
    description: str = ""
    parameters: List[Dict] = []
    request_body: Dict = None
    responses: Dict = {}
    security: List[Dict] = []
    deprecated: bool = False
    tags: List[str] = []


class SpecAdapter(ABC):
    """Base abstract adapter for converting API specifications to standardized format."""
    
    @abstractmethod
    def adapt(self, spec: Dict) -> Dict:
        """
        Convert a specification to OpenAPI 3.0 format.
        
        Args:
            spec: The original API specification
            
        Returns:
            Dict containing an OpenAPI 3.0 compatible specification
        """
        pass
    
    @abstractmethod
    def extract_auth_config(self, spec: Dict) -> Dict:
        """
        Extract authentication configuration from the specification.
        
        Args:
            spec: The original API specification
            
        Returns:
            Dict containing auth configuration details
        """
        pass
    
    @abstractmethod
    def extract_endpoints(self, spec: Dict) -> List[EndpointInfo]:
        """
        Extract endpoints from the specification.
        
        Args:
            spec: The original API specification
            
        Returns:
            List of endpoint information objects
        """
        pass
```

### 4. Implementation for Each Format

#### Integration with Existing Import Endpoint

In `src/coherence/api/v1/endpoints/openapi.py`, we need to update the import endpoint:

```python
@router.post("/import", response_model=ImportOpenAPIResponse, status_code=status.HTTP_201_CREATED)
async def import_openapi_spec(
    request: Request,
    request_data: ImportOpenAPIRequest,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """Import an API specification (OpenAPI, FAPI, or BAPI)."""
    try:
        tenant_id = getattr(request.state, "tenant_id", None)
        is_system_admin = getattr(request.state, "is_system_admin", False)

        # Handle tenant context validation as in current implementation
        # ...

        api_spec = None
        
        # Handle different input types (URL, content, file)
        if request_data.url:
            api_spec = await fetch_openapi_spec(request_data.url)
        elif request_data.content:
            api_spec = parse_spec_content(request_data.content)
            
        if not api_spec:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or empty API specification"
            )
            
        # Detect the specification format and get appropriate adapter
        try:
            spec_format = SpecFormatDetector.detect_format(api_spec)
            adapter = SpecAdapterFactory.create_adapter(api_spec)
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported API specification format: {str(e)}"
            )
            
        # Convert to OpenAPI format
        converted_spec = adapter.adapt(api_spec)
        
        # Process the converted specification using existing OpenAPIAdapter
        openapi_adapter = await get_openapi_adapter(db)
        integration = await openapi_adapter.process_openapi_spec(
            tenant_id=tenant_id,
            name=request_data.name or converted_spec.get("info", {}).get("title", "Imported API"),
            spec_data=converted_spec,
            spec_format=spec_format.value
        )
        
        # Store the original specification if it's not OpenAPI
        if spec_format != SpecFormat.OPENAPI:
            original_spec = APIOriginalSpec(
                integration_id=integration.id,
                format=spec_format.value,
                content=api_spec
            )
            db.add(original_spec)
        
        await db.commit()
        
        # Return response with format included
        return {
            "integration_id": str(integration.id),
            "tenant_id": tenant_id,
            "name": integration.name,
            "version": integration.version,
            "base_url": integration.base_url,
            "spec_format": spec_format.value,
            "endpoints": [
                {
                    "id": str(endpoint.id),
                    "path": endpoint.path,
                    "method": endpoint.method,
                    "operation_id": endpoint.operation_id,
                }
                for endpoint in integration.endpoints
            ],
            "auth_methods": integration.auth_methods,
        }
    
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to import API spec: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to import API specification: {str(e)}"
        ) from e
```

#### OpenAPI Adapter (Existing)

```python
# src/coherence/openapi_adapter/spec_adapters/openapi_adapter.py
from typing import Dict, List
from .base import SpecAdapter, EndpointInfo


class OpenAPIAdapter(SpecAdapter):
    """Adapter for OpenAPI specifications (pass-through)."""
    
    def adapt(self, spec: Dict) -> Dict:
        """Pass through since our system already handles OpenAPI."""
        return spec
    
    def extract_auth_config(self, spec: Dict) -> Dict:
        """Extract authentication configuration from OpenAPI spec."""
        auth_config = {
            "auth_type": None,
            "security_schemes": {}
        }
        
        # Extract security schemes from components section
        if "components" in spec and "securitySchemes" in spec["components"]:
            security_schemes = spec["components"]["securitySchemes"]
            
            # Determine primary auth type based on security schemes
            for name, scheme in security_schemes.items():
                scheme_type = scheme.get("type", "").lower()
                
                if scheme_type == "oauth2":
                    auth_config["auth_type"] = "oauth2"
                    auth_config["oauth2_config"] = {
                        "flows": scheme.get("flows", {}),
                        "scopes": self._extract_scopes(scheme)
                    }
                    break
                    
                elif scheme_type == "apikey":
                    auth_config["auth_type"] = "apikey"
                    auth_config["apikey_config"] = {
                        "name": scheme.get("name", ""),
                        "in": scheme.get("in", "header")
                    }
                    break
                    
                elif scheme_type == "http":
                    scheme_scheme = scheme.get("scheme", "").lower()
                    if scheme_scheme == "bearer":
                        auth_config["auth_type"] = "bearer"
                    elif scheme_scheme == "basic":
                        auth_config["auth_type"] = "basic"
                    break
            
            # Store all security schemes for reference
            auth_config["security_schemes"] = security_schemes
        
        return auth_config
    
    def _extract_scopes(self, scheme: Dict) -> Dict[str, str]:
        """Extract OAuth2 scopes from a security scheme."""
        scopes = {}
        
        if "flows" in scheme:
            flows = scheme["flows"]
            
            # Check common OAuth2 flows
            for flow_type in ["implicit", "password", "clientCredentials", "authorizationCode"]:
                if flow_type in flows and "scopes" in flows[flow_type]:
                    scopes.update(flows[flow_type]["scopes"])
        
        return scopes
    
    def extract_endpoints(self, spec: Dict) -> List[EndpointInfo]:
        """Extract endpoints from OpenAPI spec."""
        endpoints = []
        paths = spec.get("paths", {})
        
        for path, path_item in paths.items():
            if not isinstance(path_item, dict):
                continue
                
            for method, operation in path_item.items():
                if method not in ["get", "post", "put", "delete", "patch", "options", "head"]:
                    continue
                    
                if not isinstance(operation, dict):
                    continue
                
                # Generate operation ID if missing
                operation_id = operation.get("operationId") or f"{method}_{path.replace('/', '_')}"
                
                endpoint = EndpointInfo(
                    path=path,
                    method=method.upper(),
                    operation_id=operation_id,
                    summary=operation.get("summary", ""),
                    description=operation.get("description", ""),
                    parameters=operation.get("parameters", []),
                    request_body=operation.get("requestBody"),
                    responses=operation.get("responses", {}),
                    security=operation.get("security", []),
                    deprecated=operation.get("deprecated", False),
                    tags=operation.get("tags", [])
                )
                
                endpoints.append(endpoint)
        
        return endpoints
```

#### FAPI Adapter

```python
# src/coherence/openapi_adapter/spec_adapters/fapi_adapter.py
from typing import Dict, List
from .base import SpecAdapter, EndpointInfo


class FAPIAdapter(SpecAdapter):
    """Adapter for Financial-grade API (FAPI) specifications."""
    
    def adapt(self, spec: Dict) -> Dict:
        """Convert FAPI specification to OpenAPI 3.0 format."""
        # Create a base OpenAPI structure
        openapi_spec = {
            "openapi": "3.0.0",
            "info": {
                "title": spec.get("info", {}).get("title", "FAPI API"),
                "version": spec.get("info", {}).get("version", "1.0.0"),
                "description": spec.get("info", {}).get("description", "Converted from FAPI specification")
            },
            "paths": {},
            "components": {
                "schemas": {},
                "securitySchemes": {}
            }
        }
        
        # Copy over servers if present
        if "servers" in spec:
            openapi_spec["servers"] = spec["servers"]
        
        # Map FAPI paths to OpenAPI paths
        if "endpoints" in spec:
            for endpoint in spec["endpoints"]:
                path = endpoint.get("path", "")
                method = endpoint.get("method", "").lower()
                
                if not path or not method:
                    continue
                
                if path not in openapi_spec["paths"]:
                    openapi_spec["paths"][path] = {}
                
                # Convert endpoint to OpenAPI operation
                openapi_spec["paths"][path][method] = {
                    "operationId": endpoint.get("id", f"{method}_{path.replace('/', '_')}"),
                    "summary": endpoint.get("summary", ""),
                    "description": endpoint.get("description", ""),
                    "parameters": self._convert_parameters(endpoint.get("parameters", [])),
                    "responses": self._convert_responses(endpoint.get("responses", {})),
                    "tags": endpoint.get("tags", [])
                }
                
                # Add security requirements if present
                if "security" in endpoint:
                    openapi_spec["paths"][path][method]["security"] = endpoint["security"]
        
        # Map FAPI security to OpenAPI security schemes
        if "security_profiles" in spec:
            for profile_name, profile in spec["security_profiles"].items():
                profile_type = profile.get("type", "").lower()
                
                if profile_type == "oauth2":
                    openapi_spec["components"]["securitySchemes"][profile_name] = {
                        "type": "oauth2",
                        "flows": self._convert_oauth_flows(profile.get("flows", {})),
                        "description": profile.get("description", "")
                    }
                elif profile_type == "mtls":
                    # Map MTLS to standard OpenAPI http with custom x-mtls extension
                    openapi_spec["components"]["securitySchemes"][profile_name] = {
                        "type": "http",
                        "scheme": "bearer",
                        "description": "Mutual TLS Authentication",
                        "x-mtls": True
                    }
        
        # Add any schemas from the FAPI spec
        if "data_models" in spec:
            for model_name, model in spec["data_models"].items():
                openapi_spec["components"]["schemas"][model_name] = model
        
        return openapi_spec
    
    def _convert_parameters(self, fapi_params: List[Dict]) -> List[Dict]:
        """Convert FAPI parameters to OpenAPI parameters."""
        openapi_params = []
        
        for param in fapi_params:
            openapi_param = {
                "name": param.get("name", ""),
                "in": param.get("location", "query"),
                "required": param.get("required", False),
                "description": param.get("description", "")
            }
            
            # Add schema if present
            if "type" in param:
                openapi_param["schema"] = {
                    "type": param["type"]
                }
                
                if "format" in param:
                    openapi_param["schema"]["format"] = param["format"]
            
            openapi_params.append(openapi_param)
        
        return openapi_params
    
    def _convert_responses(self, fapi_responses: Dict) -> Dict:
        """Convert FAPI responses to OpenAPI responses."""
        openapi_responses = {}
        
        for status_code, response in fapi_responses.items():
            openapi_responses[status_code] = {
                "description": response.get("description", ""),
            }
            
            # Add content if present
            if "content_type" in response and "schema" in response:
                openapi_responses[status_code]["content"] = {
                    response["content_type"]: {
                        "schema": response["schema"]
                    }
                }
        
        return openapi_responses
    
    def _convert_oauth_flows(self, fapi_flows: Dict) -> Dict:
        """Convert FAPI OAuth flows to OpenAPI OAuth flows."""
        openapi_flows = {}
        
        # Map FAPI flow names to OpenAPI flow names
        flow_mapping = {
            "authorization_code": "authorizationCode",
            "client_credentials": "clientCredentials",
            "implicit": "implicit",
            "password": "password"
        }
        
        for fapi_flow_name, flow in fapi_flows.items():
            openapi_flow_name = flow_mapping.get(fapi_flow_name, fapi_flow_name)
            
            openapi_flows[openapi_flow_name] = {
                "scopes": flow.get("scopes", {})
            }
            
            # Add authorization URL if present
            if "authorization_url" in flow:
                openapi_flows[openapi_flow_name]["authorizationUrl"] = flow["authorization_url"]
            
            # Add token URL if present
            if "token_url" in flow:
                openapi_flows[openapi_flow_name]["tokenUrl"] = flow["token_url"]
            
            # Add refresh URL if present
            if "refresh_url" in flow:
                openapi_flows[openapi_flow_name]["refreshUrl"] = flow["refresh_url"]
        
        return openapi_flows
    
    def extract_auth_config(self, spec: Dict) -> Dict:
        """Extract authentication configuration from FAPI spec."""
        auth_config = {
            "auth_type": None,
            "security_schemes": {}
        }
        
        # Extract security profiles
        if "security_profiles" in spec:
            # First, check for OAuth2 (most common in FAPI)
            for name, profile in spec["security_profiles"].items():
                profile_type = profile.get("type", "").lower()
                
                if profile_type == "oauth2":
                    auth_config["auth_type"] = "oauth2"
                    auth_config["oauth2_config"] = {
                        "flows": self._convert_oauth_flows(profile.get("flows", {})),
                        "scopes": self._extract_fapi_scopes(profile)
                    }
                    # Store additional FAPI-specific OAuth2 attributes
                    auth_config["oauth2_config"]["fapi_profile"] = profile.get("profile", "")
                    auth_config["oauth2_config"]["requires_pkce"] = profile.get("requires_pkce", False)
                    break
                
                # Check for MTLS (common in FAPI)
                elif profile_type == "mtls":
                    auth_config["auth_type"] = "mtls"
                    auth_config["mtls_config"] = {
                        "client_cert_required": True,
                        "description": profile.get("description", "")
                    }
                    break
            
            # Store all security profiles for reference
            auth_config["security_schemes"] = spec["security_profiles"]
        
        return auth_config
    
    def _extract_fapi_scopes(self, profile: Dict) -> Dict[str, str]:
        """Extract OAuth2 scopes from FAPI security profile."""
        scopes = {}
        
        if "flows" in profile:
            flows = profile["flows"]
            
            for flow_name, flow in flows.items():
                if "scopes" in flow:
                    scopes.update(flow["scopes"])
        
        return scopes
    
    def extract_endpoints(self, spec: Dict) -> List[EndpointInfo]:
        """Extract endpoints from FAPI spec."""
        endpoints = []
        
        if "endpoints" in spec:
            for endpoint_data in spec["endpoints"]:
                path = endpoint_data.get("path", "")
                method = endpoint_data.get("method", "").upper()
                
                if not path or not method:
                    continue
                
                endpoint = EndpointInfo(
                    path=path,
                    method=method,
                    operation_id=endpoint_data.get("id", f"{method.lower()}_{path.replace('/', '_')}"),
                    summary=endpoint_data.get("summary", ""),
                    description=endpoint_data.get("description", ""),
                    parameters=endpoint_data.get("parameters", []),
                    responses=endpoint_data.get("responses", {}),
                    security=endpoint_data.get("security", []),
                    deprecated=endpoint_data.get("deprecated", False),
                    tags=endpoint_data.get("tags", [])
                )
                
                endpoints.append(endpoint)
        
        return endpoints
```

#### BAPI Adapter

```python
# src/coherence/openapi_adapter/spec_adapters/bapi_adapter.py
from typing import Dict, List
from .base import SpecAdapter, EndpointInfo


class BAPIAdapter(SpecAdapter):
    """Adapter for Business API (BAPI) specifications."""
    
    def adapt(self, spec: Dict) -> Dict:
        """Convert BAPI specification to OpenAPI 3.0 format."""
        # Create a base OpenAPI structure
        openapi_spec = {
            "openapi": "3.0.0",
            "info": {
                "title": spec.get("info", {}).get("title", "BAPI API"),
                "version": spec.get("info", {}).get("version", "1.0.0"),
                "description": spec.get("info", {}).get("description", "Converted from BAPI specification")
            },
            "paths": {},
            "components": {
                "schemas": {},
                "securitySchemes": {}
            }
        }
        
        # Copy over servers if present
        if "servers" in spec:
            openapi_spec["servers"] = spec["servers"]
        
        # Map BAPI services to OpenAPI paths
        if "services" in spec:
            for service in spec["services"]:
                service_name = service.get("name", "")
                
                if "operations" not in service:
                    continue
                
                for operation in service["operations"]:
                    operation_name = operation.get("name", "")
                    method = operation.get("method", "post").lower()
                    
                    # Construct path from service and operation names
                    path = f"/{service_name}/{operation_name}"
                    
                    if "path" in operation:
                        path = operation["path"]
                    
                    if path not in openapi_spec["paths"]:
                        openapi_spec["paths"][path] = {}
                    
                    # Convert operation to OpenAPI operation
                    openapi_spec["paths"][path][method] = {
                        "operationId": operation.get("id", operation_name),
                        "summary": operation.get("summary", operation_name),
                        "description": operation.get("description", ""),
                        "parameters": self._convert_parameters(operation.get("parameters", [])),
                        "responses": self._convert_responses(operation.get("responses", {})),
                        "tags": [service_name]
                    }
                    
                    # Add request body if input is defined
                    if "input" in operation:
                        openapi_spec["paths"][path][method]["requestBody"] = {
                            "content": {
                                "application/json": {
                                    "schema": operation["input"]
                                }
                            },
                            "required": True
                        }
                    
                    # Add security requirements if present
                    if "security" in operation:
                        openapi_spec["paths"][path][method]["security"] = operation["security"]
        
        # Map BAPI authentication to OpenAPI security schemes
        if "authentication" in spec:
            auth_methods = spec["authentication"]
            
            for auth_name, auth_method in auth_methods.items():
                auth_type = auth_method.get("type", "").lower()
                
                if auth_type == "apikey":
                    openapi_spec["components"]["securitySchemes"][auth_name] = {
                        "type": "apiKey",
                        "in": auth_method.get("in", "header"),
                        "name": auth_method.get("name", "X-API-Key"),
                        "description": auth_method.get("description", "")
                    }
                elif auth_type == "oauth2":
                    openapi_spec["components"]["securitySchemes"][auth_name] = {
                        "type": "oauth2",
                        "flows": self._convert_oauth_flows(auth_method.get("flows", {})),
                        "description": auth_method.get("description", "")
                    }
                elif auth_type == "basic":
                    openapi_spec["components"]["securitySchemes"][auth_name] = {
                        "type": "http",
                        "scheme": "basic",
                        "description": auth_method.get("description", "")
                    }
        
        # Add any data models from the BAPI spec
        if "models" in spec:
            for model_name, model in spec["models"].items():
                openapi_spec["components"]["schemas"][model_name] = model
        
        return openapi_spec
    
    def _convert_parameters(self, bapi_params: List[Dict]) -> List[Dict]:
        """Convert BAPI parameters to OpenAPI parameters."""
        openapi_params = []
        
        for param in bapi_params:
            openapi_param = {
                "name": param.get("name", ""),
                "in": param.get("in", "query"),
                "required": param.get("required", False),
                "description": param.get("description", "")
            }
            
            # Add schema if present
            if "type" in param:
                openapi_param["schema"] = {
                    "type": param["type"]
                }
                
                if "format" in param:
                    openapi_param["schema"]["format"] = param["format"]
            
            openapi_params.append(openapi_param)
        
        return openapi_params
    
    def _convert_responses(self, bapi_responses: Dict) -> Dict:
        """Convert BAPI responses to OpenAPI responses."""
        openapi_responses = {}
        
        for status_code, response in bapi_responses.items():
            openapi_responses[status_code] = {
                "description": response.get("description", ""),
            }
            
            # Add content if output is defined
            if "output" in response:
                openapi_responses[status_code]["content"] = {
                    "application/json": {
                        "schema": response["output"]
                    }
                }
        
        return openapi_responses
    
    def _convert_oauth_flows(self, bapi_flows: Dict) -> Dict:
        """Convert BAPI OAuth flows to OpenAPI OAuth flows."""
        openapi_flows = {}
        
        # Map BAPI flow names to OpenAPI flow names
        flow_mapping = {
            "authorization_code": "authorizationCode",
            "client_credentials": "clientCredentials",
            "implicit": "implicit"
        }
        
        for bapi_flow_name, flow in bapi_flows.items():
            openapi_flow_name = flow_mapping.get(bapi_flow_name, bapi_flow_name)
            
            openapi_flows[openapi_flow_name] = {
                "scopes": flow.get("scopes", {})
            }
            
            # Add authorization URL if present
            if "authorization_url" in flow:
                openapi_flows[openapi_flow_name]["authorizationUrl"] = flow["authorization_url"]
            
            # Add token URL if present
            if "token_url" in flow:
                openapi_flows[openapi_flow_name]["tokenUrl"] = flow["token_url"]
            
            # Add refresh URL if present
            if "refresh_url" in flow:
                openapi_flows[openapi_flow_name]["refreshUrl"] = flow["refresh_url"]
        
        return openapi_flows
    
    def extract_auth_config(self, spec: Dict) -> Dict:
        """Extract authentication configuration from BAPI spec."""
        auth_config = {
            "auth_type": None,
            "security_schemes": {}
        }
        
        # Extract authentication methods
        if "authentication" in spec:
            auth_methods = spec["authentication"]
            
            # First check for OAuth2
            for name, method in auth_methods.items():
                auth_type = method.get("type", "").lower()
                
                if auth_type == "oauth2":
                    auth_config["auth_type"] = "oauth2"
                    auth_config["oauth2_config"] = {
                        "flows": self._convert_oauth_flows(method.get("flows", {})),
                        "scopes": self._extract_bapi_scopes(method)
                    }
                    break
                
                # Then check for API Key
                elif auth_type == "apikey":
                    auth_config["auth_type"] = "apikey"
                    auth_config["apikey_config"] = {
                        "name": method.get("name", "X-API-Key"),
                        "in": method.get("in", "header")
                    }
                    break
                
                # Then check for Basic Auth
                elif auth_type == "basic":
                    auth_config["auth_type"] = "basic"
                    break
            
            # Store all auth methods for reference
            auth_config["security_schemes"] = auth_methods
        
        return auth_config
    
    def _extract_bapi_scopes(self, method: Dict) -> Dict[str, str]:
        """Extract OAuth2 scopes from BAPI authentication method."""
        scopes = {}
        
        if "flows" in method:
            flows = method["flows"]
            
            for flow_name, flow in flows.items():
                if "scopes" in flow:
                    scopes.update(flow["scopes"])
        
        return scopes
    
    def extract_endpoints(self, spec: Dict) -> List[EndpointInfo]:
        """Extract endpoints from BAPI spec."""
        endpoints = []
        
        if "services" in spec:
            for service in spec["services"]:
                service_name = service.get("name", "")
                
                if "operations" not in service:
                    continue
                
                for operation in service["operations"]:
                    operation_name = operation.get("name", "")
                    method = operation.get("method", "post").upper()
                    
                    # Construct path from service and operation names
                    path = f"/{service_name}/{operation_name}"
                    
                    if "path" in operation:
                        path = operation["path"]
                    
                    endpoint = EndpointInfo(
                        path=path,
                        method=method,
                        operation_id=operation.get("id", operation_name),
                        summary=operation.get("summary", operation_name),
                        description=operation.get("description", ""),
                        parameters=operation.get("parameters", []),
                        responses=operation.get("responses", {}),
                        security=operation.get("security", []),
                        deprecated=operation.get("deprecated", False),
                        tags=[service_name]
                    )
                    
                    endpoints.append(endpoint)
        
        return endpoints
```

### 5. Adapter Factory

Create a factory to get the appropriate adapter:

```python
# src/coherence/openapi_adapter/spec_adapters/adapter_factory.py
from typing import Dict

from ..services.spec_format_detector import SpecFormat, SpecFormatDetector
from .base import SpecAdapter
from .openapi_adapter import OpenAPIAdapter
from .fapi_adapter import FAPIAdapter
from .bapi_adapter import BAPIAdapter


class SpecAdapterFactory:
    """Factory for creating the appropriate specification adapter."""
    
    @staticmethod
    def create_adapter(spec: Dict) -> SpecAdapter:
        """
        Create the appropriate adapter based on the specification format.
        
        Args:
            spec: The API specification
            
        Returns:
            SpecAdapter: The appropriate adapter for the specification
            
        Raises:
            ValueError: If the specification format is not supported
        """
        spec_format = SpecFormatDetector.detect_format(spec)
        
        if spec_format == SpecFormat.OPENAPI:
            return OpenAPIAdapter()
        elif spec_format == SpecFormat.FAPI:
            return FAPIAdapter()
        elif spec_format == SpecFormat.BAPI:
            return BAPIAdapter()
        else:
            raise ValueError(f"Unsupported specification format: {spec_format}")
```

### 6. Update OpenAPI Import API

Update the import endpoint to use the new adapter system:

```python
# src/coherence/api/v1/endpoints/openapi.py (update import endpoint)

@router.post("/import", response_model=APIIntegrationResponse)
async def import_openapi_spec(
    request_data: ImportOpenAPIRequest,
    request: Request,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
) -> APIIntegrationResponse:
    """Import an OpenAPI specification."""
    try:
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        api_spec = None
        
        # Handle different input types (URL, content, file)
        if request_data.url:
            api_spec = await fetch_openapi_spec(request_data.url)
        elif request_data.content:
            api_spec = parse_spec_content(request_data.content)
            
        if not api_spec:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or empty API specification"
            )
            
        # Detect the specification format and get appropriate adapter
        try:
            spec_format = SpecFormatDetector.detect_format(api_spec)
            adapter = SpecAdapterFactory.create_adapter(api_spec)
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported API specification format: {str(e)}"
            )
            
        # Use the adapter to convert to OpenAPI format
        converted_spec = adapter.adapt(api_spec)
            
        # Process the converted specification
        openapi_adapter = OpenAPIAdapter(db)
        integration = await openapi_adapter.process_openapi_spec(
            tenant_id=tenant_id,
            name=request_data.name or converted_spec.get("info", {}).get("title", "Imported API"),
            version=converted_spec.get("info", {}).get("version", "1.0.0"),
            openapi_spec=converted_spec,
        )
        
        # Store the original specification
        original_spec = APIOriginalSpec(
            integration_id=integration.id,
            format=spec_format.value,
            content=api_spec
        )
        db.add(original_spec)
        
        await db.commit()
        
        return APIIntegrationResponse(
            id=str(integration.id),
            name=integration.name,
            version=integration.version,
            status=integration.status,
            base_url=integration.base_url,
            created_at=integration.created_at,
            updated_at=integration.updated_at,
            spec_format=spec_format.value
        )
            
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to import API spec: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to import API specification: {str(e)}"
        ) from e
```

### 7. Update UI to Support Multiple Formats

Update the admin UI to show the specification format:

```typescript
// Update IntegrationDetailClient.tsx

// Update interface to include format
interface ApiIntegration {
  id: string;
  name: string;
  version: string;
  status: string;
  base_url: string;
  created_at: string;
  updated_at: string;
  spec_format: string; // Add this field
  endpoints: ApiEndpoint[];
}

// Add format badge to integration details
<div className="flex items-center mb-4">
  <h2 className="text-2xl font-bold">{integration.name}</h2>
  <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
    {integration.spec_format === 'openapi' ? 'OpenAPI' : 
     integration.spec_format === 'fapi' ? 'FAPI' :
     integration.spec_format === 'bapi' ? 'BAPI' : 'Unknown'}
  </span>
  <span className="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded">
    v{integration.version}
  </span>
</div>
```

## Implementation Timeline

### Phase 1: Core Infrastructure (Week 1)
1. Create database migrations for new tables and fields
   - Create alembic migration for adding `spec_format` to `api_integrations`
   - Create alembic migration for adding `api_original_specs` table with RLS policy
2. Implement SpecFormatDetector service
   - Create format detection based on known FAPI and BAPI markers
   - Add unit tests for reliable detection
3. Create base SpecAdapter interface
   - Define common methods needed for all format adapters
   - Ensure proper error handling and validation
4. Build the OpenAPIAdapter implementation (wrapping existing functionality)
   - Create pass-through adapter for OpenAPI format

### Phase 2: Format-Specific Adapters (Week 2)
1. Implement FAPIAdapter
   - Create mapping functions for FAPI-specific structures
   - Add specialized security handling for FAPI requirements
2. Implement BAPIAdapter
   - Create mapping functions for BAPI-specific structures
   - Handle service-based operations in BAPI
3. Create adapter factory
   - Implement factory pattern for adapter creation based on format
4. Write unit tests for all adapters
   - Create test fixtures with sample specifications
   - Test format detection and conversion accuracy

### Phase 3: Integration Updates (Week 3)
1. Update OpenAPI import endpoint to use the adapter system
   - Modify `import_openapi_spec` to detect format and use appropriate adapter
   - Store original specifications for non-OpenAPI formats
2. Modify credential handling to support FAPI-specific auth requirements
   - Update `_extract_auth_methods` to handle FAPI security extensions
   - Add support for MTLS and PKCE requirements
3. Add enhanced security handling for FAPI
   - Implement request object signing for OAuth flows
   - Add token binding support
4. Integration tests for all formats
   - Create end-to-end tests for importing each format
   - Validate correct extraction of endpoints and security settings

### Phase 4: UI Updates and Documentation (Week 4)
1. Update admin UI to display and handle multiple formats
   - Add format badges to integration list and detail views
   - Update import form to support format selection
2. Add format-specific help text and documentation
   - Create help text explaining each format's capabilities
   - Document security considerations for each format
3. Create sample specifications for testing
   - Create representative examples of each format
   - Add to test fixtures for automated testing
4. Documentation for users and developers
   - Update API documentation with format support
   - Create developer documentation for extending format support

## Testing Strategy

1. **Unit Tests**:
   - Test format detection with sample specifications
   - Test each adapter with representative sample files
   - Verify auth config extraction for each format

2. **Integration Tests**:
   - End-to-end test of import process for each format
   - Verify proper database storage of specifications
   - Test credential management with each format

3. **Security Tests**:
   - Verify FAPI security compliance
   - Test enhanced auth flows

4. **UI Tests**:
   - Test format detection and display in UI
   - Verify proper handling of format-specific features

## Rollout Plan

1. **Development Environment**: Test the new functionality in a controlled environment
   - Deploy to development environment first
   - Run automated test suite with all format types
2. **Beta Testing**: Invite select customers to test the new format support
   - Identify customers with FAPI/BAPI requirements
   - Collect feedback on conversion accuracy and usability
3. **Production Deployment**: Roll out to all users
   - Add feature flags if needed for gradual rollout
   - Monitor error rates and performance metrics

## Risks and Mitigation

1. **Risk**: Inconsistent behavior between formats
   **Mitigation**: Thorough testing with representative samples; detailed logging of format conversion

2. **Risk**: Security vulnerabilities in format conversion
   **Mitigation**: Security review; strict validation of input specifications

3. **Risk**: Performance issues with complex specifications
   **Mitigation**: Optimization of conversion process; consider async processing for large specs

4. **Risk**: Backward compatibility issues
   **Mitigation**: Maintain full support for existing OpenAPI integrations; extensive regression testing

## Appendix: Sample Specifications

Include sample FAPI and BAPI specifications for testing.

### Example FAPI Specification Structure
```json
{
  "info": {
    "title": "Financial API Example",
    "version": "1.0.0"
  },
  "financial_api": "1.0",
  "security_profiles": {
    "oauth2_profile": {
      "type": "oauth2",
      "profile": "FAPI 1.0",
      "requires_pkce": true,
      "flows": {
        "authorization_code": {
          "authorization_url": "https://auth.example.com/authorize",
          "token_url": "https://auth.example.com/token",
          "refresh_url": "https://auth.example.com/token",
          "scopes": {
            "accounts": "Access to account information",
            "payments": "Make payments"
          }
        }
      }
    }
  },
  "endpoints": [
    {
      "path": "/accounts",
      "method": "get",
      "id": "get_accounts",
      "summary": "Get Accounts",
      "description": "Retrieve a list of accounts",
      "parameters": [],
      "responses": {
        "200": {
          "description": "Successful response",
          "content_type": "application/json",
          "schema": {
            "type": "object",
            "properties": {
              "accounts": {
                "type": "array",
                "items": {
                  "$ref": "#/data_models/Account"
                }
              }
            }
          }
        }
      },
      "security": [
        {"oauth2_profile": ["accounts"]}
      ]
    }
  ],
  "data_models": {
    "Account": {
      "type": "object",
      "properties": {
        "id": {"type": "string"},
        "name": {"type": "string"},
        "balance": {"type": "number"}
      }
    }
  }
}
```

### Example BAPI Specification Structure
```json
{
  "info": {
    "title": "Business API Example",
    "version": "1.0.0"
  },
  "business_api": "1.0",
  "authentication": {
    "api_key_auth": {
      "type": "apikey",
      "name": "X-API-Key",
      "in": "header"
    }
  },
  "services": [
    {
      "name": "orders",
      "operations": [
        {
          "name": "createOrder",
          "method": "post",
          "path": "/orders",
          "summary": "Create a new order",
          "description": "Submit a new order to the system",
          "input": {
            "type": "object",
            "properties": {
              "customer_id": {"type": "string"},
              "items": {
                "type": "array",
                "items": {"$ref": "#/models/OrderItem"}
              }
            },
            "required": ["customer_id", "items"]
          },
          "responses": {
            "201": {
              "description": "Order created",
              "output": {
                "type": "object",
                "properties": {
                  "order_id": {"type": "string"},
                  "status": {"type": "string"}
                }
              }
            }
          },
          "security": [
            {"api_key_auth": []}
          ]
        }
      ]
    }
  ],
  "models": {
    "OrderItem": {
      "type": "object",
      "properties": {
        "product_id": {"type": "string"},
        "quantity": {"type": "integer"},
        "price": {"type": "number"}
      },
      "required": ["product_id", "quantity"]
    }
  }
}
```