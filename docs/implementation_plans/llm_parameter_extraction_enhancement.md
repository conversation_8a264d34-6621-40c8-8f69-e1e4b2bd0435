# LLM-Based Parameter Extraction Enhancement Plan

## Overview

This plan outlines enhancements to <PERSON>herence's parameter extraction system to rely more heavily on LLM capabilities rather than deterministic pattern matching. The goal is to create a more flexible, natural system for collecting parameters through conversational interaction.

## Background

The current system uses a hybrid approach:
1. Pattern-based extraction using regex for simple types
2. LLM-based extraction for complex parameters  
3. Conversational parameter collection for missing values

This plan shifts the balance to prioritize LLM extraction, improving flexibility and natural language understanding.

## Proposed Architecture

### Initial Parameter Extraction

1. **Pass user message to LLM** with instructions to identify:
   - The intended action or API endpoint
   - All mentioned parameters (mandatory and optional)
   - Structured output format (JSON) with detected parameters

2. **Identification of Missing Required Parameters**
   - Compare LLM's extracted parameters with API endpoint requirements
   - Generate conversational prompts for missing required parameters

3. **Handling Optional Parameters**
   - LLM captures optional parameters from natural phrasing
   - No rigid pattern matching for ordinals or variations
   - Raw values passed through LLM interpretation

4. **Iterative Refinement**
   - Continue conversation until all required parameters collected
   - User confirmation of gathered parameters before execution

5. **Execute the Action**
   - Form API call with LLM-extracted parameters
   - Return response in appropriate format (CRFS)

## Implementation Tasks

### High Priority

1. **Enhance ParameterExtractor to rely more on LLM for initial parameter extraction**
   - Modify extraction flow to prioritize LLM over regex patterns
   - Keep regex as fallback for simple cases only
   - Update `extract_parameters` method to call `_extract_with_llm` first

2. **Modify extract_parameters method to use LLM for all parameter types**
   - Remove or minimize pattern-based extraction phase
   - Direct all extraction through LLM pipeline
   - Update parameter type handling to work with LLM output

3. **Create unit tests for enhanced LLM-based parameter extraction**
   - Test various natural language inputs including ordinals
   - Test parameter validation with LLM-extracted values
   - Test edge cases and error scenarios

4. **Update integration tests for complete parameter collection flow**
   - Test multi-turn conversations for parameter collection
   - Test parameter confirmation workflows
   - Verify end-to-end functionality

### Medium Priority

5. **Update parameter extraction prompts for better handling**
   - Improve system prompts for ordinal numbers and variations
   - Add examples showing "the second option", "option #2", etc.
   - Enhance prompt templates with more natural language examples

6. **Implement iterative refinement loop in ChatOrchestrator**
   - Enhance `continue_conversation` method for better flow
   - Add logic for parameter confirmation rounds
   - Improve state management between conversation turns

7. **Add parameter summary confirmation step**
   - Present collected parameters to user before execution
   - Allow user to modify parameters
   - Implement confirmation/cancellation logic

8. **Update parameter validation for flexibility**
   - Handle type conversion more gracefully
   - Improve error messages for validation failures
   - Add smart type inference from LLM output

9. **Implement error handling for LLM extraction failures**
   - Add fallback strategies when LLM fails
   - Implement retry logic with different prompts
   - Graceful degradation to pattern matching if needed

### Low Priority

10. **Document the LLM-based parameter extraction architecture**
    - Update CLAUDE.md with new parameter handling approach
    - Create architectural diagrams showing the flow
    - Add developer guide for extending the system

## Benefits

1. **Improved Flexibility**: Handles natural language variations without rigid patterns
2. **Better User Experience**: More conversational parameter collection
3. **Reduced Maintenance**: Less code to maintain, LLM handles complexity
4. **Enhanced Accuracy**: Better context understanding from user input

## Risks and Mitigations

1. **LLM Latency**: Mitigate with caching and optimized prompts
2. **LLM Failures**: Implement fallback to pattern matching
3. **Type Mismatches**: Add robust validation layer after extraction
4. **Cost**: Monitor LLM usage and optimize prompt efficiency

## Technical Details

### Affected Components

- `src/coherence/intent_pipeline/parameter_extraction.py`
- `src/coherence/intent_pipeline/orchestrator.py`
- `src/coherence/schemas/request.py`
- `src/coherence/schemas/openapi.py`

### Key Methods to Modify

1. `ParameterExtractor.extract_parameters()`
2. `ParameterExtractor._extract_with_llm()`
3. `ChatOrchestrator.continue_conversation()`
4. `ChatOrchestrator._handle_resolved_intent()`

### Example LLM Prompt Structure

```python
system_prompt = """You are a parameter extraction system. 
Extract parameters from the user's message based on the given schema.
Handle variations like ordinals ("the second one"), numbers ("#2"), 
and natural language references ("the middle option").
Return ONLY valid JSON with extracted parameters."""

user_prompt = f"""Intent: {intent_description}

Parameter Schema:
{param_schema}

User Message:
{message}

Extract all parameters mentioned in the user message.
Handle ordinal references, numbered options, and natural language variations.
"""
```

## Success Criteria

1. Successfully extract parameters from varied natural language inputs
2. Reduced reliance on regex patterns
3. Improved conversation flow for parameter collection
4. Passing test coverage for all scenarios
5. No regression in existing functionality

## Timeline

- Phase 1 (Week 1-2): High priority tasks 1-4
- Phase 2 (Week 3-4): Medium priority tasks 5-8
- Phase 3 (Week 5): Medium priority task 9 and low priority task 10

## Notes

This enhancement aligns with Coherence's goal of natural language understanding and conversational interfaces. The existing infrastructure supports this enhancement with minimal architectural changes required.