# CRFS Admin Chat Styling Implementation Plan

## Overview
This document outlines the implementation plan for styling CRFS (Coherence Response Format Standard) v2 responses in the admin chat interface. The goal is to enhance the chat experience by rendering structured API responses in a visually appealing and consistent manner that aligns with the existing cyberpunk theme.

## Background
- **CRFS v2** is Coherence's standardized system for formatting API responses into human-readable content
- The backend already has a complete CRFS implementation in `src/coherence/template_system/crfs_formatter.py`
- The admin chat interface currently displays raw responses without structured formatting
- The cyberpunk theme uses dark backgrounds with neon accents (blue, purple, green)

## Implementation Approach

### 1. Backend Modifications

#### Update Response Structure
Modify the orchestrator to include CRFS metadata in responses:

```python
# src/coherence/api/v1/endpoints/resolve.py
from coherence.template_system.crfs_formatter import CR<PERSON>Formatter

async def resolve(...):
    # Existing logic...
    
    if response_format and response_format.get("crfs_version"):
        formatter = CRFSFormatter(response_format)
        formatted_response = formatter.format(context)
        
        # Include both formatted content and metadata
        return ResolveResponse(
            type="reply",
            outcome=formatted_response,
            raw_response=action_result,  # Include raw response
            crfs_metadata={
                "version": response_format.get("crfs_version"),
                "kind": response_format.get("kind"),
                "format": response_format.get("format"),
                "sections": response_format.get("format", {}).get("sections", [])
            }
        )
```

#### Schema Updates
Extend the response schemas to include CRFS metadata:

```python
# src/coherence/schemas/response.py
from typing import Optional, Dict, Any

class CRFSMetadata(BaseModel):
    version: str
    kind: str
    format: Dict[str, Any]
    sections: List[Dict[str, Any]]

class ResolveResponse(BaseModel):
    type: str
    outcome: str
    status_id: Optional[str] = None
    parameters_to_collect: Optional[List[ParameterToCollect]] = None
    raw_response: Optional[Any] = None
    crfs_metadata: Optional[CRFSMetadata] = None
```

### 2. Frontend Components

#### Create CRFS Renderer
Create a dedicated component for rendering CRFS formatted content:

```typescript
// src/components/chat/CRFSRenderer.tsx
import React from 'react';
import { cn } from '@/lib/utils';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { atomOneDark } from 'react-syntax-highlighter/dist/esm/styles/hljs';

interface CRFSSection {
  id: string;
  header?: {
    template: string;
    level?: number;
  };
  content: {
    template: string;
    type?: 'text' | 'code' | 'json' | 'table' | 'list';
  };
  metadata?: {
    visible: boolean;
    order: number;
  };
}

interface CRFSRendererProps {
  sections: CRFSSection[];
  data: any;
  className?: string;
}

const CRFSRenderer: React.FC<CRFSRendererProps> = ({ sections, data, className }) => {
  const renderSection = (section: CRFSSection) => {
    if (section.metadata?.visible === false) return null;

    return (
      <div key={section.id} className="mb-6">
        {section.header && (
          <div className={cn(
            "mb-3 text-cyan-400",
            `text-${section.header.level === 1 ? '2xl' : section.header.level === 2 ? 'xl' : 'lg'}`
          )}>
            {section.header.template}
          </div>
        )}
        
        <div className="crfs-content">
          {renderContent(section.content)}
        </div>
      </div>
    );
  };

  const renderContent = (content: CRFSSection['content']) => {
    switch (content.type) {
      case 'code':
      case 'json':
        return (
          <div className="relative">
            <SyntaxHighlighter
              language={content.type}
              style={atomOneDark}
              className="rounded-lg border border-purple-500/30"
              customStyle={{
                background: 'rgba(10, 10, 10, 0.8)',
                padding: '1rem'
              }}
            >
              {content.template}
            </SyntaxHighlighter>
          </div>
        );
      
      case 'table':
        return (
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse">
              {/* Table rendering logic */}
            </table>
          </div>
        );
      
      case 'list':
        return (
          <ul className="list-disc list-inside space-y-2 text-gray-300">
            {/* List rendering logic */}
          </ul>
        );
      
      default:
        return (
          <div className="prose prose-invert max-w-none">
            <p className="text-gray-300">{content.template}</p>
          </div>
        );
    }
  };

  // Sort sections by order metadata
  const sortedSections = [...sections].sort(
    (a, b) => (a.metadata?.order ?? 0) - (b.metadata?.order ?? 0)
  );

  return (
    <div className={cn("crfs-renderer", className)}>
      {sortedSections.map(renderSection)}
    </div>
  );
};

export default CRFSRenderer;
```

#### Update Chat Message Component
Enhance the existing chat message component to support CRFS:

```typescript
// src/components/chat/ChatMessage.tsx
import CRFSRenderer from './CRFSRenderer';

interface ExtendedMessage extends Message {
  crfsMetadata?: {
    version: string;
    kind: string;
    format: any;
    sections: CRFSSection[];
  };
  rawResponse?: any;
}

const ChatMessage: React.FC<{ message: ExtendedMessage }> = ({ message }) => {
  const [viewMode, setViewMode] = useState<'formatted' | 'raw'>('formatted');
  
  const renderContent = () => {
    if (message.crfsMetadata && viewMode === 'formatted') {
      return (
        <CRFSRenderer
          sections={message.crfsMetadata.sections}
          data={message.rawResponse}
          className="mt-4"
        />
      );
    }
    
    // Fallback to default rendering
    return <div className="text-gray-300">{message.content}</div>;
  };
  
  return (
    <div className={cn(
      "chat-message p-4 rounded-lg",
      message.role === 'assistant' 
        ? "bg-gradient-to-r from-purple-900/20 to-blue-900/20 border-l-2 border-cyan-500"
        : "bg-gray-800/50"
    )}>
      {message.crfsMetadata && (
        <div className="flex justify-end mb-2">
          <button
            onClick={() => setViewMode(viewMode === 'formatted' ? 'raw' : 'formatted')}
            className="text-xs text-cyan-400 hover:text-cyan-300 transition-colors"
          >
            {viewMode === 'formatted' ? 'Show Raw' : 'Show Formatted'}
          </button>
        </div>
      )}
      
      {renderContent()}
    </div>
  );
};
```

### 3. Styling with Tailwind

Create CRFS-specific styles in globals.css:

```css
/* coherence-admin/src/app/globals.css */

/* CRFS Base Styles */
.crfs-renderer {
  @apply space-y-4;
}

/* CRFS Code Blocks */
.crfs-code {
  @apply font-mono text-sm bg-gray-900/80 border border-purple-500/30 rounded-lg p-4;
  @apply hover:border-purple-400/50 transition-all duration-300;
}

/* CRFS Tables */
.crfs-table {
  @apply w-full border-collapse;
}

.crfs-table th {
  @apply bg-purple-900/30 text-cyan-400 font-semibold p-3 text-left;
  @apply border-b border-purple-500/50;
}

.crfs-table td {
  @apply p-3 text-gray-300 border-b border-gray-800;
}

.crfs-table tr:hover {
  @apply bg-blue-900/10 transition-colors duration-200;
}

/* CRFS Lists */
.crfs-list {
  @apply space-y-2 text-gray-300;
}

.crfs-list-item {
  @apply flex items-start space-x-2;
}

.crfs-list-item::before {
  @apply content-['▸'] text-cyan-400 mt-1;
}

/* CRFS Headers */
.crfs-header-1 {
  @apply text-2xl font-bold text-cyan-400 mb-4;
  @apply bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent;
}

.crfs-header-2 {
  @apply text-xl font-semibold text-purple-400 mb-3;
}

.crfs-header-3 {
  @apply text-lg font-medium text-blue-400 mb-2;
}

/* CRFS Cards */
.crfs-card {
  @apply bg-gradient-to-br from-purple-900/10 to-blue-900/10;
  @apply border border-purple-500/30 rounded-lg p-4;
  @apply hover:border-purple-400/50 transition-all duration-300;
}

/* CRFS Highlights */
.crfs-highlight {
  @apply bg-yellow-500/20 text-yellow-300 px-1 rounded;
}

/* CRFS Info Box */
.crfs-info {
  @apply bg-blue-900/20 border-l-4 border-blue-500 p-4 rounded-r-lg;
}

/* CRFS Success Message */
.crfs-success {
  @apply bg-green-900/20 border-l-4 border-green-500 p-4 rounded-r-lg;
}

/* CRFS Error Message */
.crfs-error {
  @apply bg-red-900/20 border-l-4 border-red-500 p-4 rounded-r-lg;
}

/* CRFS Loading Animation */
@keyframes crfs-pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.crfs-loading {
  @apply animate-pulse;
  animation: crfs-pulse 2s ease-in-out infinite;
}
```

### 4. Chat Client Updates

Update the chat client to handle CRFS metadata:

```typescript
// src/lib/chatClient.ts
export interface ChatResponse {
  type: ResponseType;
  outcome?: string;
  status_id?: string;
  parameters_to_collect?: any[];
  raw_response?: any;
  crfs_metadata?: {
    version: string;
    kind: string;
    format: any;
    sections: any[];
  };
}

// Update the resolve method to pass through metadata
async resolve(message: string, sessionId?: string): Promise<ChatResponse> {
  const response = await this.apiClient.post('/resolve', {
    message,
    user_id: this.userId,
    role: this.role,
    session_id: sessionId
  });
  
  return response.data; // Now includes crfs_metadata
}
```

## Implementation Phases

### Phase 1: Backend Integration (2-3 days)
1. Update response schemas to include CRFS metadata
2. Modify orchestrator to expose formatted responses
3. Add unit tests for CRFS metadata inclusion

### Phase 2: Frontend Components (3-4 days)
1. Create CRFSRenderer component
2. Update ChatMessage component
3. Add view mode toggle (formatted/raw)
4. Implement error boundaries

### Phase 3: Styling & Polish (2-3 days)
1. Create comprehensive CRFS-specific styles
2. Ensure cyberpunk theme consistency
3. Add loading states and animations
4. Implement responsive design

### Phase 4: Testing & Optimization (2 days)
1. Unit tests for CRFS components
2. Integration tests with various response types
3. Performance optimization
4. Accessibility improvements

## Testing Strategy

### Unit Tests
- Test CRFS renderer with various section types
- Test view mode toggling
- Test error handling

### Integration Tests
- Test end-to-end flow from API to rendered UI
- Test with different CRFS formats (weather, API responses, etc.)
- Test fallback behavior for non-CRFS responses

### Visual Tests
- Screenshot tests for different CRFS sections
- Theme consistency validation
- Responsive design testing

## Success Metrics

1. All CRFS response types render correctly
2. Consistent cyberpunk theme across all elements
3. Smooth transitions between formatted/raw views
4. No performance degradation
5. Maintains accessibility standards

## Risks & Mitigations

| Risk | Mitigation |
|------|------------|
| Breaking existing chat functionality | Feature flag for CRFS rendering |
| Performance issues with large responses | Implement virtual scrolling |
| Complex CRFS formats not rendering properly | Fallback to raw view |
| Theme inconsistency | Design system documentation |

## Future Enhancements

1. Interactive CRFS elements (expandable sections, tooltips)
2. Copy-to-clipboard for code blocks
3. Export formatted responses (PDF, markdown)
4. Custom CRFS themes
5. Real-time CRFS preview in template editor

## References

- [CRFS v2 Specification](../implementation_plans/coherence_response_format_standard.md)
- [Admin Site Architecture](../architecture/admin_site_architecture.md)
- [Cyberpunk Theme Guide](../design-system/cyber-wave.md)
- [Chat Interface Implementation](../implementation_plans/admin_chat_interface.md)