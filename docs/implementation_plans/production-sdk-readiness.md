# Coherence Production & SDK Readiness Guide v1.0

> **Purpose** – Provide an actionable, sprint‑level playbook (with copy‑paste AI‑assistant prompts) to close all remaining gaps between the current codebase and a production‑grade, SDK‑friendly release.

---

## 1. Overview

This guide is split into two tracks:

1. **Platform Hardening** – durability, security, observability, reliability.
2. **Developer eXperience (DX)** – public SDK packaging, CI/CD, documentation.

Each section lists **Goals**, **Tasks**, **Acceptance Criteria**, and a ready‑to‑run **AI Coding‑Assistant Prompt** (compatible with your XGen workflow: PRD Architect → Scaffolding Agent → Prompt Composer).

---

## 2. Two‑Week Sprint Roadmap (example)

| Sprint Day | Theme                    | Key Deliverables                                    |
| ---------- | ------------------------ | --------------------------------------------------- |
|  1 – 2     | Persistence & Migrations | Workflow + template version tables; Alembic scripts |
|  3 – 4     | Observability Deep‑Dive  | Tenant‑labelled metrics, Grafana boards, /healthz   |
|  5 – 6     | Security Hardening       | KMS integration, admin rate‑limit, audit log        |
|  7 – 8     | SDK Packaging            | Python + TypeScript clients, semantic versioning    |
|  9 – 10    | CI/CD Upgrade            | GitHub Actions, coverage ≥ 90 %, contract tests     |
|  11 – 12   | Fail‑fast & Reliability  | Strict provider checks, circuit‑breaker tuning      |
|  13 – 14   | DX Polish & Release Prep | Docs, quick‑start, CHANGELOG, tagging v1.0‑rc1      |

*(Adjust to your own cadence.)*

---

## 3. Detailed Work Packages & Prompts

### 3.1 Persistence & Migrations

**Goals**

* Survive restarts without data loss.
* Enable template rollback/history.

**Tasks**

1. Design tables: `workflow_status`, `template_versions`, `audit_log`.
2. Write Alembic migrations + models.
3. Add Redis pub‑sub cache bust for template hot‑reload.

**Acceptance Criteria**

* ✅ Server restarts restore in‑flight job status.
* ✅ Template diff/rollback API passes integration tests.

**AI Prompt (to paste into XGen 02 Scaffolding Agent)**

```text
«SYSTEM»
You are a backend architect. Generate SQLAlchemy models and Alembic migrations for workflow_status, template_versions, and audit_log with tenant RLS. Ensure idempotent migrations.
«INPUT»
Current repo structure is in /mnt/data/repomix-output.md. Implement in src/coherence/models and migrations/.
```

---

### 3.2 Observability & Monitoring

**Goals**

* Tenant‑level visibility on latency, error, and cost metrics.
* Deep `/healthz` endpoint.

**Tasks**

1. Inject `tenant_id` label into Prom metrics via middleware.
2. Expand Grafana boards; add SLO panels (p95 < 500 ms, error < 1 %).
3. Implement `/healthz?deep=true` to test DB, vector store, LLM, external APIs.

**Acceptance Criteria**

* ✅ Grafana shows per‑tenant heatmap.
* ✅ Alert fires on SLO breach in staging.

**Prompt**

```text
«SYSTEM»
As a DevOps engineer, add tenant labelling to all Prometheus metrics and ship updated Grafana dashboard JSON under monitoring/.
```

---

### 3.3 Security Hardening

**Goals**

* Secrets managed by cloud KMS.
* Admin endpoints rate‑limited and audited.

**Tasks**

1. Wrap CredentialManager with AWS KMS (fallback to GCP).
2. Add FastAPI‑Limiter (Redis) on `/admin/*` (100 req/min).
3. Write `audit_log` middleware recording user, route, payload hash.

**Acceptance Criteria**

* ✅ Keys encrypted at rest & rotated.
* ✅ Pen‑test script blocked on >100 req/min.

**Prompt**

```text
«SYSTEM»
Integrate AWS KMS for encryption keys in credential_manager.py. Add rate‑limit and audit logging middleware as described.
```

---

### 3.4 SDK Packaging & Release Engineering

**Goals**

* Publish `coherence-sdk` (PyPI) & @coherence/sdk (npm).
* Auto‑generated, type‑safe clients.

**Tasks**

1. Split `sdk/` sub‑module; add `pyproject.toml`.
2. Use `openapi-python-client` & `orval` in CI for code‑gen.
3. Add semantic versioning (CalVer YYYY.MM).
4. Prepare `README.md`, quick‑start snippets.

**Acceptance Criteria**

* ✅ `pip install coherence-sdk==2025.05` works in clean venv.
* ✅ Example TS script fetches `/v1/intents`.

**Prompt**

```text
«SYSTEM»
Generate a Python & TypeScript SDK from openapi.json. Scaffold packaging, publish‑to‑TestPyPI, and npm beta tag.
```

---

### 3.5 CI/CD Upgrade

**Goals**

* Test, lint, type‑check, coverage gate.
* Contract tests against running docker‑compose.

**Tasks**

1. GitHub Actions workflow: `ruff`, `mypy`, `pytest-cov`, `openapi‑spec‑validator`.
2. Start services via docker‑compose up ‑d; run contract tests.
3. Enforce 90 % coverage; badge in README.

**Acceptance Criteria**

* ✅ PR fails if coverage < 90 %.
* ✅ Contract tests green on main.

**Prompt**

```text
«SYSTEM»
Write .github/workflows/ci.yml implementing the above pipeline with matrix for Python 3.10/3.11.
```

---

### 3.6 Fail‑fast & Reliability Enhancements

**Goals**

* No silent downgrade to mock LLM.
* Circuit‑breaker timeouts tuned for prod.

**Tasks**

1. Replace fallback with 503 error + Prom alert.
2. Tune CB thresholds; add exponential back‑off.

**Prompt**

```text
«SYSTEM»
Modify llm_provider_factory to raise HTTP_503 if provider unavailable; add CB tuning per OpenAI guidelines.
```

---

## 4. Definition of Done (DoD)

* All acceptance criteria met.
* `v1.0‑rc1` tag created; CHANGELOG updated.
* Docs site deployed (MkDocs Material) with SDK example.
* Staging environment runs 72h soak test without SLA breach.

---

## 5. Roll‑out Plan

1. **Beta** – single‑tenant pilot (non‑critical workload).
2. **Release Candidate** – multi‑tenant staging, external SDK consumers.
3. **GA** – tag `v1.0.0`, publish blog, migrate prod traffic.

---

## 6. Appendix: Template for XGen PRD‑Architect Prompt

```text
«SYSTEM»
You are XGen 01 PRD Architect. Convert the following work package into master_prd.md. Include spec, rationale, edge‑cases, metrics, risks.
«INPUT»
<copy task description here>
```

*(Repeat similar template snippets for Scaffolding Agent & Prompt Composer as needed.)*

---

### End of Guide
