# Multi-Turn LLM Parameter Collection Plan

## Overview
This plan outlines the transformation of Coherence's parameter extraction system from pattern-based recognition to a conversational, multi-turn approach using LLM capabilities.

## Goals
- Replace rigid pattern matching with flexible LLM-driven parameter collection
- Enable natural, conversational parameter gathering
- Support progressive parameter collection (multiple params per turn)
- Provide context-aware questions and validation
- Maintain conversation state across turns

## Architecture Overview

### Current System
```
User Message → Pattern Extraction → LLM Fallback → Missing Params → Ask Response
```

### Proposed System
```
User Message → LLM Analysis → Natural Question Generation → 
User Response → LLM Validation → Repeat Until Complete → Execute Action
```

## Core Components

### 1. ConversationalParameterExtractor
```python
class ConversationalParameterExtractor:
    """
    Handles multi-turn parameter collection through natural conversation
    """
    
    async def collect_parameters(
        self,
        message: str,
        intent_definition: IntentDefinition,
        conversation_history: List[Message] = None
    ) -> Union[ParameterCollectionResult, AskResponse]:
        """
        Main entry point for conversational parameter collection
        Returns either completed parameters or next question
        """
        pass
    
    async def validate_and_refine(
        self,
        user_response: str,
        pending_params: Dict[str, ParameterDefinition],
        collected_params: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate user response and potentially refine understanding
        """
        pass
```

### 2. Enhanced Response Types
```python
class ParameterQuestion(AskResponse):
    type: Literal["parameter_question"]
    parameter_name: str
    parameter_type: str
    question: str
    examples: Optional[List[str]]
    
class ParameterClarification(AskResponse):
    type: Literal["parameter_clarification"]
    parameter_name: str
    ambiguous_value: str
    clarification_question: str
    options: Optional[List[str]]
    
class ParameterConfirmation(AskResponse):
    type: Literal["parameter_confirmation"]
    collected_params: Dict[str, Any]
    confirmation_message: str

class APIDocumentation(AskResponse):
    type: Literal["api_documentation"]
    documentation: str
    related_parameters: List[str]
    examples: Optional[List[Dict[str, Any]]]
```

### 3. Conversation State Management
```python
class ParameterCollectionState:
    """
    Tracks state across multi-turn parameter collection
    """
    intent_key: str
    conversation_id: str
    collected_params: Dict[str, Any]
    pending_params: List[str]
    validation_attempts: Dict[str, int]
    conversation_history: List[Message]
    
    def update_with_response(self, user_response: str, extracted_params: Dict[str, Any]):
        """Update state with new information from user"""
        pass
```

## Implementation Phases

### Phase 1: Core LLM-Based Extraction
1. Replace pattern-based extraction with LLM-first approach
2. Implement confidence scoring for extracted parameters
3. Create natural language question generation
4. Add basic conversation state tracking

### Phase 2: Multi-Turn Conversation Support
1. Implement conversation history tracking
2. Add context-aware question generation
3. Support progressive parameter collection
4. Handle clarification requests

### Phase 3: Advanced Features
1. Add parameter dependency handling
2. Implement smart validation with helpful feedback
3. Support parameter correction/modification
4. Add confirmation summaries

## LLM Prompt Templates

### Parameter Extraction Prompt
```
You are extracting parameters for a user intent. Analyze the conversation and extract all relevant parameters.

Intent: {intent_description}
Required Parameters: {parameter_definitions}
Conversation History: {history}
Current Message: {message}

Extract parameters with confidence scores. Return JSON:
{
    "extracted": {"param": {"value": ..., "confidence": "high|medium|low"}},
    "ambiguous": ["list of ambiguous references"],
    "missing": ["list of missing required params"]
}
```

### Question Generation Prompt
```
Generate a natural question to collect a parameter.

Context: {intent_context}
Already Collected: {collected_params}
Need to Collect: {parameter_name} (type: {parameter_type})
Description: {parameter_description}

Create a conversational, context-aware question that feels natural.
```

### Validation Prompt
```
Validate if the user's response provides the requested parameter.

Parameter Needed: {parameter_name} (type: {parameter_type})
User Response: {response}
Conversation Context: {context}

Determine if the response contains the parameter. If ambiguous, suggest clarification.
Return JSON with validation result and extracted value if present.
```

## Example Conversation Flow

```
User: "I need to book a flight to Paris"

System: (Extracts: destination=Paris, missing: departure_date, departure_city, passengers)
Assistant: "I'll help you book a flight to Paris. What date would you like to travel?"

User: "Next Friday morning"

System: (Extracts: departure_date=next_friday, time_preference=morning, missing: departure_city, passengers)
Assistant: "Great! Next Friday morning it is. Which city will you be flying from?"

User: "NYC, and it's just for me"

System: (Extracts: departure_city=NYC, passengers=1, complete)
Assistant: "Perfect! Let me search for morning flights from New York to Paris on December 20th for 1 passenger."
```

## Benefits
1. **Natural Experience**: Users can provide information conversationally
2. **Flexible Input**: Handles variations, synonyms, and natural language
3. **Progressive Collection**: Gathers multiple parameters per turn when possible
4. **Context Awareness**: Questions adapt based on conversation flow
5. **Better UX**: Clear, helpful questions instead of generic forms

## Testing Strategy
1. Unit tests for LLM parameter extraction
2. Integration tests for multi-turn flows
3. Edge case handling (ambiguous inputs, corrections)
4. Performance testing for conversation latency
5. User experience testing with real conversations

## Success Metrics
- Reduced parameter collection turns (avg turns per intent)
- Improved extraction accuracy (% correct on first attempt)
- Better user satisfaction (qualitative feedback)
- Lower abandonment rate during parameter collection
- Faster overall intent completion time

## Risks and Mitigations
- **Risk**: LLM hallucination of parameter values
  - **Mitigation**: Strict validation and confirmation steps
  
- **Risk**: Increased latency from LLM calls
  - **Mitigation**: Optimize prompts, use smaller models where possible
  
- **Risk**: Conversation state management complexity
  - **Mitigation**: Clear state boundaries, proper error recovery

## Timeline
- Week 1: Implement core LLM-based extraction
- Week 2: Add multi-turn conversation support
- Week 3: Implement advanced features and testing
- Week 4: Integration testing and refinement