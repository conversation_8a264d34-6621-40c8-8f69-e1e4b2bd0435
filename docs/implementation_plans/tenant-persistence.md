**Planning implementation**

We need to implement tenant ID across the app, using the integrations page as a test case. This involves the frontend (with context and provider), backend middleware, database RLS, and tests. I don't need to browse for external info—I'll rely on existing code and knowledge. The output should include milestones, tasks for frontend, backend, DB, QA, and release, as well as architectural blueprints and best practices. Everything will be clear, no ambiguity or user info required. I'll produce the plan in plain text, not commentary.


Thought for 11 seconds


Below is a **step-by-step implementation roadmap** that institutionalises tenant scoping everywhere in the Coherence stack and uses the **/integrations** page as the “pilot” to prove the pattern works.  Follow the phases in order; each phase closes a feedback loop before you spread the change further.

---

## Phase 0 Ground rules & pre-checks  (½ day)

1. **Freeze the schema**: be sure the `tenants` table has **id (PK)**, **clerk\_org\_id (UNIQUE)**, and timestamps.
2. **Audit RLS**: every table that should be tenant-scoped must already have a policy that calls `current_tenant_id()` or the patch will leak data.
3. **Pick one UUID shape**: choose dashed lower-case canonical form (e.g. `6e8f…c0f5`). Treat anything else as invalid.

---

## Phase 1 Plumbing the tenant context end-to-end  (2 days)

### 1-A Frontend (Next JS / React)

* Create a **`TenantProvider`** (if you haven’t already) that:

  * At sign-in, calls `/api/tenants?clerk_org_id=…` once.
  * Stores the returned `tenant_id` in:

    * React context (`selectedTenantId`)
    * `localStorage.selectedTenantId`
    * `tenantId=` **secure cookie** (SameSite=Lax; Path=/).
* Write a **single tiny wrapper** `fetchWithTenant()` that:

  * Reads `selectedTenantId` or falls back to cookie.
  * Injects header `X-Tenant-ID`.
  * Adds `tenantId` to the JSON body of POST/PUT/PATCH when the body is an object and the field is defined in the schema.
* Do **not** allow ad-hoc `fetch()` anywhere else—export the wrapper from `/lib/fetch.ts` and ban raw fetches with an ESLint rule (`no-restricted-imports`).

### 1-B Next API routes & server actions

* Add `withTenantHeaders()` middleware that copies `X-Tenant-ID` from the client request to outgoing backend calls.
* If the header is missing **or** equals the all-zeros UUID, short-circuit with HTTP 400. That turns silent bugs into loud failures.

### 1-C FastAPI

* **TenantContextMiddleware** reads `X-Tenant-ID` then JWT claim.

* The SQLAlchemy **engine event** sets two PostgreSQL parameters on every checkout:

  ```python
  conn.execute(text("SET app.current_tenant_id = :tid"), {"tid": tenant_id})
  conn.execute(text("SET app.is_system_admin = :flag"), {"flag": str(is_admin)})
  ```

* Add a Pydantic **`TenantMixin`** that all tenant-scoped models inherit from (field `tenant_id: UUID`), so CRUD routes can rely on `tenant_id` existing.

### 1-D Back-end fast tests

* Unit-test that a request missing `X-Tenant-ID` returns 400.
* Unit-test that the engine listener really sets `current_tenant_id()` by querying `SHOW app.current_tenant_id`.

---

## Phase 2 /Integrations pilot (1 day)

1. **Refactor page**

   * Replace Clerk-only ternary with `const { selectedTenantId } = useTenant();`.
   * All API calls go through `fetchWithTenant()`.
   * Render a spinner while `!selectedTenantId`.

2. **Server route**

   * Confirm the header arrives by logging `request.headers["x-tenant-id"]` in dev only.

3. **Playwright e2e**

   * Scenario: sign in ➜ switch to Org B ➜ visit `/integrations` ➜ assert that at least one integration card appears and the network request contained the correct UUID.
   * Mark the test **required** in CI.

4. **Smoke test**: in staging, run two test users in two Clerk orgs and verify they see different integration counts.

---

## Phase 3 App-wide rollout (3–5 days)

1. **Inventory pages & API routes**

   * Use `grep -R "fetch("` and `grep -R "axios"` to list all calls.
   * Group by module: Patients, Clinics, Templates, etc.

2. **Bulk update**

   * Convert each callsite to `fetchWithTenant`.
   * If a route uses SSR `getServerSideProps`, inject the cookie‐based fallback.

3. **Regression suite**

   * For every major entity type add a quick Playwright spec: create → reload → list → confirm visibility only in the same org.

4. **Code-owner enforcement**

   * Add an ESLint rule that fails PRs importing `node-fetch` or `axios` directly.

---

## Phase 4 Observability & guard rails (1 day)

* **Backend log filter**: if `tenant_id` resolves to all-zeros, emit a `WARNING` with the request path and clerk user id. Pipe to Grafana/Loki.
* **DB sentinel query**: schedule a cron job that counts rows where `tenant_id = '0000…'`; expected count is **0**.
* **Frontend Sentry breadcrumb**: attach `selectedTenantId` to every error event for easier triage.

---

## Phase 5 Documentation & developer onboarding (½ day)

* **ADR-008 Tenant Context Handling**—capture the rationale, the data flow diagram, and coding rules.
* **Cheat-sheet** in the repo root:

  ```
  // How to call the API from React
  import { api } from "@/lib/fetch";
  const resp = await api("/v1/clinics", { method: "POST", body: {...}})

  // Gotcha checklist
  ✓ useTenant() returns null? Wait.
  ✓ never hardcode tenantId.
  ✓ tests live in /tests/tenant/
  ```

---

## Success criteria

* **Zero** requests reach FastAPI without `X-Tenant-ID` in production (Grafana alert).
* Switching Clerk organizations live-updates UI data in < 1 s client-side.
* Playwright suite green in CI; staging deploy blocked if any tenant test fails.
* No rows exist with the fallback UUID.

Total effort: **\~7–10 developer-days** spread across frontend, backend, and QA.  Once the Integrations pilot is green you can sweep the rest of the app confidently.
