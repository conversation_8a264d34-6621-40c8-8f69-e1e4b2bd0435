# Multi-Template Vector Search Implementation Plan

## Overview
With the introduction of documentation templates alongside intent, action, and fallback templates, the vector search system needs to handle multiple template types and intelligently route based on user intent.

## Template Types
1. **Intent Template** - For matching user intent to actions
2. **Action Template** - For executing API operations
3. **Fallback Template** - For error handling
4. **Documentation Template** - For providing API help and parameter documentation

## Vector Search Updates

### 1. Enhanced Search Result Structure
```python
class TemplateSearchResult:
    template_key: str
    template_type: Literal["intent", "action", "fallback", "documentation"]
    similarity_score: float
    endpoint: str
    method: str
    metadata: Dict[str, Any]
```

### 2. Intent Classification System
```python
class IntentClassification:
    type: Literal["action", "documentation", "clarification", "unknown"]
    primary_template: TemplateSearchResult
    related_templates: List[TemplateSearchResult]
    confidence: float
    reasoning: str
```

### 3. Multi-Template <PERSON> Handler
```python
class MultiTemplateSearchHandler:
    async def search_and_classify(
        self,
        query: str,
        tenant_id: str,
        context: ConversationContext = None
    ) -> IntentClassification:
        """
        Search for all relevant templates and classify user intent
        """
        # Search across all template types
        results = await self.vector_store.search(
            query=query,
            filter={"tenant_id": tenant_id},
            limit=10
        )
        
        # Group by template type
        grouped_results = self._group_by_type(results)
        
        # Use LLM to classify intent
        classification = await self._classify_with_llm(
            query=query,
            grouped_results=grouped_results,
            context=context
        )
        
        return classification
```

### 4. LLM-Based Intent Classification
```python
async def _classify_with_llm(
    self,
    query: str,
    grouped_results: Dict[str, List[TemplateSearchResult]],
    context: ConversationContext
) -> IntentClassification:
    """
    Use LLM to determine if user wants action or documentation
    """
    prompt = f"""
    Analyze the user query and determine their intent:
    
    Query: {query}
    
    Available Templates:
    - Intent Templates: {[t.metadata for t in grouped_results.get('intent', [])]}
    - Documentation Templates: {[t.metadata for t in grouped_results.get('documentation', [])]}
    
    Context: {context}
    
    Classify as:
    1. "action" - User wants to perform an action
    2. "documentation" - User wants help or information
    3. "clarification" - Ambiguous, need more info
    
    Consider:
    - Questions starting with "how", "what", "explain" usually want documentation
    - Direct commands usually want actions
    - Parameter questions during action flow want contextual help
    """
    
    response = await self.llm_provider.generate(prompt)
    return self._parse_classification(response, grouped_results)
```

### 5. Context-Aware Response Generation
```python
class ContextAwareResponseGenerator:
    async def generate_response(
        self,
        classification: IntentClassification,
        message: str,
        context: ConversationContext
    ) -> ChatResponse:
        """
        Generate appropriate response based on classification
        """
        if classification.type == "documentation":
            return await self._generate_docs_response(
                template=classification.primary_template,
                message=message,
                related_actions=classification.related_templates
            )
        
        elif classification.type == "action":
            # Check if docs template exists for same endpoint
            docs_template = self._find_related_docs(classification.primary_template)
            
            return await self._process_action_with_docs(
                action_template=classification.primary_template,
                docs_template=docs_template,
                message=message
            )
        
        elif classification.type == "clarification":
            return await self._generate_clarification(
                templates=classification.related_templates,
                message=message
            )
```

### 6. Parameter Collection with Inline Documentation
```python
class DocumentationAwareParameterCollector:
    async def collect_with_docs(
        self,
        message: str,
        intent_template: IntentTemplate,
        docs_template: DocumentationTemplate,
        context: ConversationContext
    ) -> Union[CollectedParameters, AskResponse]:
        """
        Collect parameters with ability to answer questions using docs
        """
        # Check if asking about specific parameter
        param_question = self._extract_parameter_question(message)
        
        if param_question:
            # Use docs template to answer
            param_docs = docs_template.parameters.get(param_question.parameter_name)
            
            if param_docs:
                return APIDocumentation(
                    type="api_documentation",
                    documentation=param_docs.description,
                    examples=param_docs.examples,
                    related_parameters=[param_question.parameter_name]
                )
        
        # Normal parameter collection continues...
```

## Implementation Considerations

### 1. Vector Indexing Strategy
- Index intent templates with action-oriented phrases
- Index docs templates with question-oriented phrases
- Use metadata to link related templates (same endpoint)

### 2. Performance Optimization
- Cache frequently accessed template pairs
- Pre-filter search based on conversation context
- Use smaller embeddings model for classification

### 3. Fallback Behavior
- If no docs template exists, generate from OpenAPI spec
- If classification uncertain, offer both options
- Maintain conversation flow during doc lookups

## Example Scenarios

### Scenario 1: Direct Action Request
```
User: "Get the weather in Boston"
Search Results:
  - weather_current_get_intent (0.95)
  - weather_current_get_docs (0.75)
Classification: "action"
Response: Proceed with weather API call
```

### Scenario 2: Documentation Request
```
User: "How do I use the weather API?"
Search Results:
  - weather_current_get_docs (0.92)
  - weather_current_get_intent (0.78)
Classification: "documentation"
Response: Show weather API documentation
```

### Scenario 3: Mid-Flow Help Request
```
Context: Collecting weather parameters
User: "What format should I use for the location?"
Search Results:
  - weather_current_get_docs (0.88)
Classification: "documentation" (contextual)
Response: Show location parameter documentation
```

## Benefits
1. More intelligent intent routing
2. Seamless documentation access
3. Better user experience during parameter collection
4. Reduced confusion between actions and help requests
5. Contextual help based on conversation state