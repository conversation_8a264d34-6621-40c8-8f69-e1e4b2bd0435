### Implementation Tasks to Complete the Integration Wizard

*(Front-end + Back-end, in eight small, self-contained requests you can hand to any coding assistant or teammate)*

| #                                           | Purpose                                                        | Detailed To-Dos                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | “Done” Means                                                                               |
| ------------------------------------------- | -------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------ |
| **1. Scaffold Wizard API End-points**       | Turn scattered integration calls into a single guided workflow | • Add `api/v1/endpoints/integrations_wizard.py` mounted at `/v1/admin/integrations/wizard`.<br>• Create three POST routes:<br>  `/start` – persist placeholder `APIIntegration` (name, base\_url) and return `wizard_id`.<br>  `/upload-spec/{wizard_id}` – accept OpenAPI JSON/YAML, store raw text in new `integration_specs` table (Alembic migration) and validate parse.<br>  `/generate-actions/{wizard_id}` – call existing `openapi_adapter.generate_actions()`; return a list of `ActionSummary`.<br>• 400/422 on validation errors.<br>• Update FastAPI docs.<br>• Add unit tests in `tests/integrations/test_wizard_routes.py` for happy path and invalid spec. | All tests pass; the three routes appear in `/docs`.                                        |
| **2. Add LLM-Powered Endpoint Suggestions** | Give admins smart “top endpoints” advice after spec upload     | • Create `services/suggestions.py` with `rank_endpoints(openapi_dict) -> List[EndpointScore]`, calling OpenAI `gpt-4o` (temperature 0.2).<br>• Prompt: *“Given this OpenAPI JSON, return the five endpoints most valuable to typical end-users. Include path, method, short rationale, usefulness\_score 0-1.”*<br>• Add GET `/suggestions/{wizard_id}` in `integrations_wizard.py` returning that list.<br>• New `integration_suggestion_log` model (wizard\_id, endpoint\_path, accepted bool, created\_at).<br>• Unit test with Petstore spec: list length = 5, scores within 0-1.                                                                                      | `pytest -k suggestions` green; round-trip < 3 s for Petstore.                              |
| **3. Build the Wizard Stepper UI**          | Replace three disjoint pages with one smooth flow              | • Install `@radix-ui/react-tabs` and `react-hook-form`.<br>• Create `components/integrations/WizardStepper.tsx` with five tabs:<br>  1 Details (old `IntegrationForm`)<br>  2 Upload Spec<br>  3 AI Suggestions<br>  4 Generate Actions<br>  5 Credentials (placeholder)<br>• Persist step in URL `?step=` so refresh keeps progress.<br>• Disable “Next” until current step is valid.                                                                                                                                                                                                                                                                                     | `npm run storybook` shows a working stepper; changing `step=` in URL jumps to correct tab. |
| **4. Wire Upload → Suggestions → Accept**   | Make the middle steps automatic and track choices              | • In **Upload Spec** tab, POST file to `/wizard/upload-spec/{wizard_id}`.<br>• On success, fetch `/suggestions/{wizard_id}` and save to context.<br>• Build `components/integrations/SuggestionCard.tsx` listing the five suggestions with “Accept” / “Skip”.<br>• POST acceptance to `/wizard/suggestions-log` for each selection.<br>• Advance to **Generate Actions** once ≥1 suggestion accepted or user clicks “Skip All”.                                                                                                                                                                                                                                            | Manual test with Petstore: upload → see suggestions → accept two → auto-advance.           |
| **5. Generate Actions, Review, Commit**     | Finalize the integration and surface the new actions           | • On entering **Generate Actions** tab, POST to `/wizard/generate-actions/{wizard_id}`.<br>• Display results in existing `DataTable` with columns *Intent*, *Endpoint*, *Params*.<br>• “Finish Integration” button PATCHes the integration (wizard → active) and redirects to `/integrations/{id}`.<br>• Toast “Integration ready”.                                                                                                                                                                                                                                                                                                                                        | New integration row appears in list with actions\_count > 0.                               |
| **6. Credentials Step (MVP stub)**          | Let admins enter an API key or launch OAuth                    | • In **Credentials** tab:<br>  – If `auth_type = apiKey`, show key-name & value inputs; POST to `/v1/admin/integrations/{id}/credentials`.<br>  – If `auth_type = oauth2`, show “Authorize” button linking to `/v1/admin/oauth/authorize?integration_id=…`.<br>• Show a green check once credentials saved.                                                                                                                                                                                                                                                                                                                                                                | API-key row stored in DB; UI shows success badge.                                          |
| **7. End-to-End Playwright Smoke Test**     | Guard against regressions with one quick scenario              | • Add `e2e/integration-wizard.spec.ts`.<br>• Flow: login (Clerk stub) → run wizard with `petstore.yaml` → accept first suggestion → finish → assert new integration row exists.<br>• Add GitHub Actions workflow to spin up Docker stack, run migrations, seed demo tenant, then run Playwright headless.                                                                                                                                                                                                                                                                                                                                                                  | `npm run e2e` passes locally and in CI; pull requests fail if the flow breaks.             |
| **8. Update Docs & Changelog**              | Keep the knowledge current for teammates and demo hosts        | • In `docs/implementation_plans/awesome_admin_site_plan.md`, add an **“Integration Wizard”** section: architecture diagram, route list, state diagram, UI screenshots.<br>• Append migration note to `CHANGELOG.md`.<br>• Update `README.md` quick-start: *“docker compose up → visit `/admin` to try the wizard.”*                                                                                                                                                                                                                                                                                                                                                        | `mkdocs serve` renders new docs without errors; screenshots display.                       |

---

#### Suggested Order of Execution

1. **Step 1** (routes & DB)
2. **Step 3** (basic stepper UI)
3. **Step 4** (connect upload → suggestions)
4. **Step 2** (LLM suggestions)
5. **Step 5** (generate & commit)
6. **Step 6** (credentials)
7. **Step 7** (smoke-test + CI)
8. **Step 8** (docs)

Complete these eight focused tasks and you’ll have a polished, AI-assisted Integration Wizard that imports any OpenAPI spec, suggests high-value endpoints, generates working actions, and stores credentials—all in one seamless admin flow.
