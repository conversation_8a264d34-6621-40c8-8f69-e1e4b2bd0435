# Uni-Template Enhancement Implementation Plan

## Overview
This document outlines the implementation plan for enhancing the unified template architecture to make templates fully self-contained and tightly integrated with CRFS. Based on analysis of the current codebase, the system already has a sophisticated foundation with `UnifiedTemplateGenerator`, `ChatOrchestrator`, `DynamicActionExecutor`, and `CRFSFormatter`.

## Current State Assessment

### Existing Infrastructure
1. **Template Model** (`src/coherence/models/template.py`)
   - Already has JSONB fields: `intent_config`, `action_config`, `ui_fields`, `prompts`, `docs_config`, `response_format`
   - Supports unified templates with `category = "unified"`

2. **Unified Template Generator** (`src/coherence/template_system/unified_generator.py`)
   - Generates complete templates from OpenAPI specs
   - Includes LLM-enhanced intent patterns
   - Creates action configurations with auth and retries

3. **Chat Orchestrator** (`src/coherence/intent_pipeline/orchestrator.py`)
   - Multi-turn conversation support with Redis
   - Parameter extraction and validation
   - Template-based response rendering

4. **CRFS Formatter** (`src/coherence/template_system/crfs_formatter.py`)
   - Multi-format support (raw, template, structured)
   - Jinja2 templating with custom filters
   - `from_unified_template()` method for integration

5. **Credential Manager** (`src/coherence/openapi_adapter/credential_manager.py`)
   - KMS-based encryption for production
   - Secure storage and retrieval

## Enhancement Phases

### Phase 1: Extend Existing Template Structure (Week 1)

#### 1.1 Enhance Template Model Fields
Since the model already has JSONB fields, we'll enhance the content structure within these fields:

```python
# Update the structure within existing JSONB fields:
# - action_config: Add validation_rules, transformations, integration section
# - response_format: Enhance with auto_select, multiple formats, error_mapping
# - Add new field for test_data (requires migration)
test_data = Column(JSONB, nullable=True, comment="Test data and mock responses for development")
```

#### 1.2 Create Targeted Migration
```python
# alembic/versions/xxx_enhance_unified_templates.py
def upgrade():
    op.add_column('templates', 
        sa.Column('test_data', JSONB, nullable=True)
    )
    # Update existing templates with enhanced structure
```

### Phase 2: Enhance Unified Generator (Week 1-2)

#### 2.1 Update UnifiedTemplateGenerator
```python
# src/coherence/template_system/unified_generator.py
class UnifiedTemplateGenerator:
    def generate_template(self, endpoint: APIEndpoint, integration: Integration) -> Dict:
        # Existing functionality
        template = self._generate_base_template(endpoint)
        
        # Enhancements
        template['action']['integration'] = {
            'base_url': integration.base_url,
            'api_version': self._extract_api_version(endpoint),
            'credential_ref': integration.credential_name,
            'health_check': self._generate_health_check(integration)
        }
        
        template['action']['validation_rules'] = self._generate_validation_rules(
            endpoint.parameters
        )
        
        template['action']['transformations'] = self._generate_transformations(
            endpoint.parameters
        )
        
        template['response']['crfs']['auto_select'] = True
        template['response']['crfs']['formats'] = self._generate_crfs_formats(
            endpoint.responses
        )
        
        template['test_data'] = self._generate_test_data(endpoint)
        
        return template
```

#### 2.2 Add Validation Rule Generation
```python
def _generate_validation_rules(self, parameters: List[Parameter]) -> Dict:
    """Generate validation rules from OpenAPI parameter schemas."""
    rules = {}
    for param in parameters:
        if param.schema:
            rules[param.name] = {
                'type': param.schema.type,
                'min_length': param.schema.minLength,
                'max_length': param.schema.maxLength,
                'pattern': param.schema.pattern,
                'enum': param.schema.enum
            }
    return rules
```

### Phase 3: Enhance ChatOrchestrator (Week 2)

#### 3.1 Update Action Execution
```python
# src/coherence/intent_pipeline/orchestrator.py
class ChatOrchestrator:
    async def execute_action(self, action: str, parameters: Dict, context: Dict) -> Dict:
        template = await self._get_template(action)
        
        # Enhanced validation using template rules
        validation_errors = self._validate_parameters(
            parameters, 
            template.get('action', {}).get('validation_rules', {})
        )
        if validation_errors:
            return self._format_validation_errors(validation_errors)
        
        # Apply transformations
        transformed_params = self._apply_transformations(
            parameters,
            template.get('action', {}).get('transformations', {})
        )
        
        # Use integration config for base URL priority
        base_url = self._resolve_base_url(template, context)
        
        # Execute with existing DynamicActionExecutor
        result = await self.action_executor.execute_action(
            template, transformed_params, base_url=base_url
        )
        
        # Enhanced CRFS formatting with auto-selection
        return self._format_with_crfs(result, template, context)
```

#### 3.2 Add Parameter Transformation
```python
def _apply_transformations(self, params: Dict, transformations: Dict) -> Dict:
    """Apply transformation functions to parameters."""
    transformed = params.copy()
    for param, transforms in transformations.items():
        if param in transformed and transforms:
            value = transformed[param]
            for transform in transforms:
                if transform == 'trim':
                    value = value.strip()
                elif transform == 'lowercase':
                    value = value.lower()
                elif transform == 'uppercase':
                    value = value.upper()
                # Add more transformations as needed
            transformed[param] = value
    return transformed
```

### Phase 4: Enhance CRFS Integration (Week 2-3)

#### 4.1 Update CRFSFormatter
```python
# src/coherence/template_system/crfs_formatter.py
class CRFSFormatter:
    def format_from_unified_template(
        self, 
        data: Dict, 
        template: Dict, 
        context: Optional[Dict] = None
    ) -> FormattedResponse:
        crfs_config = template.get('response', {}).get('crfs', {})
        
        # Auto-select format based on accept header or context
        if crfs_config.get('auto_select'):
            format_name = self._auto_select_format(crfs_config, context)
        else:
            format_name = crfs_config.get('default_format', 'structured')
        
        # Get format configuration
        formats = crfs_config.get('formats', {})
        format_config = formats.get(format_name, formats.get('default', {}))
        
        # Enhanced formatting with error handling
        try:
            return self._format_response(data, format_config, context)
        except Exception as e:
            # Use error-specific formatting
            return self._format_error(e, template, context)
```

#### 4.2 Add Format Auto-Selection
```python
def _auto_select_format(self, crfs_config: Dict, context: Dict) -> str:
    """Auto-select format based on content type preference."""
    accept_header = context.get('headers', {}).get('accept', '')
    formats = crfs_config.get('formats', {})
    
    # Map content types to format names
    if 'application/json' in accept_header and 'application/json' in formats:
        return 'application/json'
    elif 'text/plain' in accept_header and 'text/plain' in formats:
        return 'text/plain'
    
    return crfs_config.get('default_format', 'structured')
```

### Phase 5: Template Validation Service (Week 3)

#### 5.1 Create Template Validator
```python
# src/coherence/services/template_validator.py
class UnifiedTemplateValidator:
    def __init__(self, credential_manager: CredentialManager):
        self.credential_manager = credential_manager
    
    def validate_completeness(self, template: Dict) -> ValidationResult:
        """Validate template contains all required execution details."""
        errors = []
        warnings = []
        
        # Check required sections
        required_sections = ['meta', 'intent', 'action', 'response']
        for section in required_sections:
            if section not in template:
                errors.append(f"Missing required section: {section}")
        
        # Validate credential references
        if 'credentials' in template:
            for cred_name, cred_config in template['credentials'].items():
                if not self._validate_credential_ref(cred_config):
                    errors.append(f"Invalid credential reference: {cred_name}")
        
        # Validate CRFS configuration
        crfs_errors = self._validate_crfs_config(
            template.get('response', {}).get('crfs', {})
        )
        errors.extend(crfs_errors)
        
        # Validate parameter mappings
        param_errors = self._validate_parameter_mappings(template)
        errors.extend(param_errors)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def validate_execution_readiness(self, template: Dict) -> bool:
        """Check if template can execute without external dependencies."""
        # Check for base URL
        if not self._has_base_url(template):
            return False
        
        # Check for required auth configuration
        if not self._has_valid_auth(template):
            return False
        
        # Check for parameter schemas
        if not self._has_parameter_schemas(template):
            return False
        
        return True
```

### Phase 6: Test Data Support (Week 3)

#### 6.1 Add Test Mode to Templates
```python
# src/coherence/intent_pipeline/orchestrator.py
class ChatOrchestrator:
    async def execute_action_with_test_mode(
        self, 
        action: str, 
        parameters: Dict, 
        test_mode: bool = False
    ) -> Dict:
        template = await self._get_template(action)
        
        if test_mode and 'test_data' in template:
            # Use mock responses from template
            mock_response = template['test_data'].get('mock_responses', {})
            response = mock_response.get('success', {})
            
            # Apply CRFS formatting to mock data
            return self._format_with_crfs(response, template, {'test_mode': True})
        
        # Normal execution
        return await self.execute_action(action, parameters, {})
```

### Phase 7: Migration and Enhancement Tools (Week 4)

#### 7.1 Template Enhancement Script
```python
# scripts/enhance_unified_templates.py
async def enhance_existing_templates():
    """Enhance existing templates with new fields."""
    async with get_db() as db:
        templates = await db.query(Template).filter(
            Template.category == TemplateCategory.UNIFIED
        ).all()
        
        for template in templates:
            enhanced = enhance_template_structure(template)
            
            # Add integration section
            if 'action' in enhanced.action_config:
                enhanced.action_config['action']['integration'] = {
                    'base_url': extract_base_url(template),
                    'credential_ref': extract_credential_ref(template)
                }
            
            # Add validation rules
            enhanced.action_config['action']['validation_rules'] = (
                generate_validation_rules(enhanced.parameters)
            )
            
            # Add CRFS auto-select
            if 'response' in enhanced.response_format:
                enhanced.response_format['response']['crfs']['auto_select'] = True
            
            # Add test data
            enhanced.test_data = generate_test_data(enhanced)
            
            await db.commit()
```

### Phase 8: Documentation and Examples (Week 4)

#### 8.1 Create Template Examples
```python
# docs/examples/enhanced_unified_template.json
{
  "meta": {
    "key": "weather_forecast_enhanced",
    "endpoint_id": "GET_/v1/forecast",
    "version": 2,
    "tags": ["weather", "forecast", "enhanced"]
  },
  "intent": {
    "patterns": ["weather forecast for {location}", "forecast in {city}"],
    "confidence_threshold": 0.85,
    "parameter_hints": {
      "location": ["city", "place", "location"]
    }
  },
  "action": {
    "method": "GET",
    "path": "/v1/forecast",
    "integration": {
      "base_url": "${WEATHER_API_URL:https://api.openweather.org}",
      "api_version": "v1",
      "credential_ref": "openweather_api_key"
    },
    "authentication": {
      "type": "api_key",
      "header": "X-API-Key",
      "value": "{{credentials.openweather_api_key}}"
    },
    "parameter_mapping": {
      "q": "{{parameters.location}}",
      "units": "{{parameters.units|default('metric')}}"
    },
    "validation_rules": {
      "location": {
        "type": "string",
        "min_length": 1,
        "max_length": 100,
        "pattern": "^[A-Za-z\\s,]+$"
      }
    },
    "transformations": {
      "location": ["trim", "lowercase"]
    }
  },
  "response": {
    "crfs": {
      "type": "structured",
      "auto_select": true,
      "default_format": "structured",
      "formats": {
        "structured": {
          "sections": [
            {
              "type": "text",
              "style": "heading",
              "content": "🌤️ Weather Forecast for {{parameters.location|capitalize}}"
            },
            {
              "type": "list",
              "items": "{{result.daily}}",
              "template": "{{item.date}}: {{item.temp}}°, {{item.conditions}}"
            }
          ]
        },
        "text/plain": {
          "template": "Weather forecast for {{parameters.location}}: {{result.summary}}"
        }
      }
    },
    "error_mapping": {
      "404": "Location '{{parameters.location}}' not found",
      "401": "Weather API authentication failed"
    }
  },
  "test_data": {
    "mock_responses": {
      "success": {
        "daily": [
          {"date": "Today", "temp": 22, "conditions": "Sunny"},
          {"date": "Tomorrow", "temp": 20, "conditions": "Partly cloudy"}
        ],
        "summary": "Sunny today, partly cloudy tomorrow"
      }
    },
    "sample_parameters": {
      "location": "London"
    }
  }
}
```

## Success Criteria

1. **Enhanced Self-Containment**
   - Templates include integration config with base URLs
   - Validation rules embedded in templates
   - Test data for offline development

2. **Improved CRFS Integration**
   - Auto-format selection based on content type
   - Multiple format options per template
   - Error-specific formatting

3. **Backward Compatibility**
   - Existing templates continue working
   - Graceful fallbacks for missing fields
   - Migration tools for enhancement

4. **Validation Framework**
   - Complete validation before execution
   - CRFS configuration validation
   - Parameter mapping verification

## Risk Mitigation

1. **Incremental Enhancement**
   - Build on existing infrastructure
   - No breaking changes to current APIs
   - Feature flags for new functionality

2. **Thorough Testing**
   - Unit tests for each enhancement
   - Integration tests with real templates
   - Performance benchmarking

3. **Gradual Rollout**
   - Test with subset of templates first
   - Monitor performance impact
   - Rollback plan for each phase

## Timeline

- **Week 1**: Extend template structure and update generator
- **Week 2**: Enhance orchestrator and CRFS integration
- **Week 3**: Add validation service and test mode support
- **Week 4**: Migration tools and documentation
- **Week 5**: Testing and refinement
- **Week 6**: Production rollout

## Next Steps

1. Create Alembic migration for `test_data` field
2. Enhance UnifiedTemplateGenerator with new sections
3. Update ChatOrchestrator for validation and transformations
4. Extend CRFSFormatter with auto-selection
5. Build template validation service