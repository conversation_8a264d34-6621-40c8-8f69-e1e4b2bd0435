# Selective Endpoint Integration Implementation Plan

## Overview
This implementation plan addresses two key requirements:

1. **Store and display endpoint descriptions**: Extract descriptions from OpenAPI spec and store them in the database for UI display
2. **Selective endpoint processing**: Allow users to select specific endpoints for action/intent generation with checkboxes

## 1. Database Schema Changes

### Update APIEndpoint Model

```python
# Add new fields to src/coherence/models/integration.py
class APIEndpoint(Base):
    # ... existing fields ...
    
    # New fields from OpenAPI spec
    summary: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    description: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    tags: Mapped[Optional[List[str]]] = mapped_column(JSONB, nullable=True)
    deprecated: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # OpenAPI specification excerpt for this endpoint
    # Store the relevant part of OpenAPI spec for this endpoint
    openapi_snippet: Mapped[Optional[Dict[str, object]]] = mapped_column(JSONB, nullable=True)
```

### Migration Script

Create a migration file `alembic/versions/xxx_add_endpoint_descriptions.py`:

```python
"""Add endpoint descriptions and OpenAPI snippet

Revision ID: add_endpoint_descriptions
Revises: [previous_revision]
Create Date: 2025-01-XX XX:XX:XX.XXXXXX

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = 'add_endpoint_descriptions'
down_revision = '[previous_revision]'
branch_labels = None
depends_on = None

def upgrade():
    # Add new columns
    op.add_column('api_endpoints', sa.Column('summary', sa.String(), nullable=True))
    op.add_column('api_endpoints', sa.Column('description', sa.String(), nullable=True))
    op.add_column('api_endpoints', sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('api_endpoints', sa.Column('deprecated', sa.Boolean(), default=False))
    op.add_column('api_endpoints', sa.Column('openapi_snippet', postgresql.JSONB(astext_type=sa.Text()), nullable=True))

def downgrade():
    # Remove added columns
    op.drop_column('api_endpoints', 'openapi_snippet')
    op.drop_column('api_endpoints', 'deprecated')
    op.drop_column('api_endpoints', 'tags')
    op.drop_column('api_endpoints', 'description')
    op.drop_column('api_endpoints', 'summary')
```

## 2. Backend Updates

### Update OpenAPI Adapter

Modify `src/coherence/openapi_adapter/adapter.py` to extract and store endpoint details:

```python
# In OpenAPIAdapter._extract_endpoints method
async def _extract_endpoints(
    self, integration_id: uuid.UUID, api_spec: Dict
) -> List[APIEndpoint]:
    """Extract and create endpoint records from an OpenAPI specification."""
    endpoints = []
    paths = api_spec.get("paths", {})

    for path, path_item in paths.items():
        if not isinstance(path_item, dict) or path == "parameters":
            continue

        for method, operation in path_item.items():
            if method not in ["get", "post", "put", "delete", "patch", "options", "head"]:
                continue

            if not isinstance(operation, dict):
                logger.warning(f"Skipping invalid operation for {method} {path}")
                continue

            # Extract operation details
            operation_id = operation.get("operationId") or f"{method}_{path.replace('/', '_')}"
            
            # Extract OpenAPI operation details
            summary = operation.get("summary", "")
            description = operation.get("description", "")
            tags = operation.get("tags", [])
            deprecated = operation.get("deprecated", False)
            
            # Store the operation snippet for UI display
            openapi_snippet = {
                "summary": summary,
                "description": description,
                "tags": tags,
                "deprecated": deprecated,
                "parameters": operation.get("parameters", []),
                "requestBody": operation.get("requestBody"),
                "responses": operation.get("responses", {}),
                "security": operation.get("security", [])
            }

            # Create endpoint record with new fields
            endpoint = APIEndpoint(
                integration_id=integration_id,
                path=path,
                method=method.upper(),
                operation_id=operation_id,
                summary=summary,
                description=description,
                tags=tags,
                deprecated=deprecated,
                openapi_snippet=openapi_snippet,
                enabled=True,
            )

            self.db.add(endpoint)
            await self.db.flush()
            endpoints.append(endpoint)

            # Add default rate limit
            rate_limit = APIRateLimit(
                endpoint_id=endpoint.id,
                requests_per_min=60,
                burst_size=5,
                cooldown_sec=60,
            )
            self.db.add(rate_limit)

    await self.db.flush()
    return endpoints
```

### Update Generate Actions/Intents Endpoints

Modify endpoints to accept selected endpoint IDs instead of using all enabled endpoints:

```python
# Update src/coherence/api/v1/endpoints/openapi.py

# Add new request schemas
class GenerateActionsForEndpointsRequest(BaseModel):
    integration_id: uuid.UUID
    endpoint_ids: List[uuid.UUID]  # Specific endpoints to process
    force_regenerate: bool = False

class GenerateIntentsForEndpointsRequest(BaseModel):
    integration_id: uuid.UUID
    endpoint_ids: List[uuid.UUID]  # Specific endpoints to process

# New endpoints for selective generation
@router.post("/actions/generate-selected", response_model=GenerateActionsResponse)
async def generate_actions_for_selected_endpoints(
    request_data: GenerateActionsForEndpointsRequest,
    request: Request,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """Generate actions for selected endpoints only."""
    try:
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        # Verify integration ownership
        integration = await db.get(APIIntegration, request_data.integration_id)
        if not integration or integration.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found or not owned by tenant",
            )

        # Verify all endpoints belong to this integration
        endpoint_check = await db.execute(
            text("""
                SELECT COUNT(*) as count
                FROM api_endpoints 
                WHERE id = ANY(:endpoint_ids) AND integration_id = :integration_id
            """),
            {
                "endpoint_ids": [str(id) for id in request_data.endpoint_ids],
                "integration_id": request_data.integration_id
            },
        )
        
        count = endpoint_check.scalar()
        if count != len(request_data.endpoint_ids):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Some endpoints don't belong to this integration",
            )

        # Generate actions for selected endpoints
        action_generator = await get_action_generator(db)
        actions = []
        
        for endpoint_id in request_data.endpoint_ids:
            try:
                action_result = await action_generator.generate_action_class(
                    endpoint_id=endpoint_id,
                    force_regenerate=request_data.force_regenerate,
                    validate=True,
                    save=True,
                )
                
                actions.append(
                    GeneratedAction(
                        endpoint_id=action_result["endpoint_id"],
                        action_name=action_result["class_name"].replace("Action", ""),
                        class_name=action_result["class_name"],
                        parameters=action_result["parameters"],
                        description=action_result.get("description", ""),
                    )
                )
            except Exception as endpoint_err:
                logger.warning(f"Failed to generate action for endpoint {endpoint_id}: {str(endpoint_err)}")

        await db.commit()
        return {"actions": actions}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate actions: {str(e)}",
        ) from e

@router.post("/intents/generate-selected", response_model=Dict)
async def generate_intents_for_selected_endpoints(
    request_data: GenerateIntentsForEndpointsRequest,
    request: Request,
    clerk_auth: ClerkAuthDetails = Depends(get_clerk_auth_details),
    db: AsyncSession = Depends(get_db),
):
    """Generate intents for selected endpoints only."""
    try:
        tenant_id = getattr(request.state, "tenant_id", None)
        if not tenant_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant context not found")

        # Verify integration ownership
        integration = await db.get(APIIntegration, request_data.integration_id)
        if not integration or integration.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found or not owned by tenant",
            )

        # Generate intents for selected endpoints
        intent_mapper = await get_intent_mapper(db)
        created_intents = []
        
        for endpoint_id in request_data.endpoint_ids:
            intent_def = await intent_mapper.generate_intent(
                endpoint_id=endpoint_id,
                use_llm=True
            )
            
            if intent_def:
                created_intents.append({
                    "id": str(intent_def.get("id")),
                    "name": intent_def.get("name"),
                    "description": intent_def.get("description"),
                    "endpoint_id": str(endpoint_id),
                    "examples": intent_def.get("examples", [])
                })
        
        return {"intents": created_intents}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate intents: {str(e)}"
        ) from e
```

## 3. Frontend Updates

### Update TypeScript Interfaces

```typescript
// Update src/app/admin/integrations/[id]/IntegrationDetailClient.tsx

interface ApiEndpointExtended {
  id: string;
  integration_id: string;
  path: string;
  method: string;
  operation_id?: string | null;
  action_class_name?: string | null;
  intent_id?: string | null;
  enabled: boolean;
  rate_limits: ApiRateLimit[];
  
  // New fields from OpenAPI spec
  summary?: string | null;
  description?: string | null;
  tags?: string[] | null;
  deprecated?: boolean;
  openapi_snippet?: Record<string, any> | null;
}
```

### Enhanced Endpoints Display

Add a new component for detailed endpoint cards with selection:

```typescript
// New component: SelectableEndpointCard.tsx
interface SelectableEndpointCardProps {
  endpoint: ApiEndpointExtended;
  isSelected: boolean;
  onToggle: (endpointId: string) => void;
  showDetails?: boolean;
}

const SelectableEndpointCard: React.FC<SelectableEndpointCardProps> = ({
  endpoint,
  isSelected,
  onToggle,
  showDetails = false
}) => {
  const [expanded, setExpanded] = useState(false);
  
  return (
    <div className={`border border-gray-200 rounded-lg shadow-sm p-4 transition-all duration-200 ${
      isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-md'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onToggle(endpoint.id)}
            className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <span className={`px-2 py-1 text-xs font-bold rounded uppercase ${
                endpoint.method.toLowerCase() === 'get' ? 'bg-blue-100 text-blue-700' :
                endpoint.method.toLowerCase() === 'post' ? 'bg-green-100 text-green-700' :
                endpoint.method.toLowerCase() === 'put' ? 'bg-yellow-100 text-yellow-700' :
                endpoint.method.toLowerCase() === 'delete' ? 'bg-red-100 text-red-700' :
                'bg-gray-100 text-gray-700'
              }`}>
                {endpoint.method}
              </span>
              <span className="text-lg font-semibold font-mono text-gray-700">{endpoint.path}</span>
              {endpoint.deprecated && (
                <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                  Deprecated
                </span>
              )}
            </div>
            
            {endpoint.summary && (
              <p className="text-gray-700 font-medium mb-1">{endpoint.summary}</p>
            )}
            
            {endpoint.description && (
              <p className="text-sm text-gray-600 mb-2">{endpoint.description}</p>
            )}
            
            {endpoint.tags && endpoint.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-2">
                {endpoint.tags.map((tag) => (
                  <span key={tag} className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {showDetails && endpoint.openapi_snippet && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpanded(!expanded)}
            className="ml-4"
          >
            {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        )}
      </div>
      
      {expanded && endpoint.openapi_snippet && (
        <div className="mt-4 border-t pt-4">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">OpenAPI Details</h4>
          <div className="bg-gray-50 p-3 rounded text-xs overflow-auto max-h-60">
            <pre>{JSON.stringify(endpoint.openapi_snippet, null, 2)}</pre>
          </div>
        </div>
      )}
    </div>
  );
};
```

### Update Generate Tab with Endpoint Selection

Replace the existing Generate tab implementation:

```typescript
// In IntegrationDetailClient.tsx, update the Generate tab content

const [selectedEndpoints, setSelectedEndpoints] = useState<Set<string>>(new Set());
const [selectAll, setSelectAll] = useState(false);

// Toggle individual endpoint selection
const toggleEndpoint = (endpointId: string) => {
  const newSelected = new Set(selectedEndpoints);
  if (newSelected.has(endpointId)) {
    newSelected.delete(endpointId);
  } else {
    newSelected.add(endpointId);
  }
  setSelectedEndpoints(newSelected);
  setSelectAll(newSelected.size === enabledEndpoints.length);
};

// Toggle select all
const toggleSelectAll = () => {
  if (selectAll) {
    setSelectedEndpoints(new Set());
  } else {
    setSelectedEndpoints(new Set(enabledEndpoints.map(e => e.id)));
  }
  setSelectAll(!selectAll);
};

// Updated Generate tab content
<TabsContent value="generate" className="bg-white shadow-lg rounded-xl p-6 md:p-8">
  <h2 className="text-xl font-bold mb-4">Generate Templates & Intents</h2>
  <p className="text-gray-600 mb-6">Select endpoints to generate templates and intents for.</p>
  
  {integration.endpoints && integration.endpoints.length > 0 ? (
    <div className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Select Endpoints</h3>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={selectAll}
              onChange={toggleSelectAll}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label className="text-sm text-gray-700">Select All</label>
          </div>
        </div>
        
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {enabledEndpoints.map((endpoint) => (
            <SelectableEndpointCard
              key={endpoint.id}
              endpoint={endpoint}
              isSelected={selectedEndpoints.has(endpoint.id)}
              onToggle={toggleEndpoint}
              showDetails={true}
            />
          ))}
        </div>
        
        <div className="mt-4 flex items-center justify-between">
          <p className="text-sm text-gray-600">
            {selectedEndpoints.size} of {enabledEndpoints.length} endpoints selected
          </p>
          
          <div className="flex space-x-3">
            <Button 
              onClick={handleGenerateSelectedTemplates} 
              disabled={generatingTemplates || generatingIntents || selectedEndpoints.size === 0}
              className="flex items-center space-x-2"
            >
              {generatingTemplates ? (
                <>
                  <RefreshCcw className="h-4 w-4 animate-spin" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <FileTemplate className="h-4 w-4" />
                  <span>Generate Templates ({selectedEndpoints.size})</span>
                </>
              )}
            </Button>
            
            <Button 
              onClick={handleGenerateSelectedIntents} 
              variant="outline"
              disabled={generatingTemplates || generatingIntents || selectedEndpoints.size === 0}
              className="flex items-center space-x-2"
            >
              {generatingIntents ? (
                <>
                  <RefreshCcw className="h-4 w-4 animate-spin" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4" />
                  <span>Generate Intents ({selectedEndpoints.size})</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
      
      {/* Rest of the results section remains the same */}
    </div>
  ) : (
    <p className="text-gray-500 italic">No endpoints available for this integration.</p>
  )}
</TabsContent>
```

### Update Generation Handlers

```typescript
// Update generation handlers to use selected endpoints

const handleGenerateSelectedTemplates = async () => {
  if (!integration || !adminSession.token || selectedEndpoints.size === 0) return;

  try {
    setGeneratingTemplates(true);
    setGenerationResults(null);
    
    const response = await apiClient<{templates: TemplateResponse[]}>(`/v1/openapi/actions/generate-selected`, {
      method: 'POST',
      token: adminSession.token,
      body: JSON.stringify({
        integration_id: integration.id,
        endpoint_ids: Array.from(selectedEndpoints),
        force_regenerate: false,
      }),
    });
    
    setGenerationResults(prev => ({
      ...prev,
      templates: response.templates,
      error: undefined
    }));
    
    alert(`Successfully generated templates for ${response.templates.length} endpoints!`);
  } catch (err) {
    console.error('Error generating templates:', err);
    const errorMessage = err instanceof Error ? err.message : 'Failed to generate templates';
    setGenerationResults(prev => ({
      ...prev,
      error: errorMessage
    }));
    alert(`Error generating templates: ${errorMessage}`);
  } finally {
    setGeneratingTemplates(false);
  }
};

const handleGenerateSelectedIntents = async () => {
  if (!integration || !adminSession.token || selectedEndpoints.size === 0) return;
  
  try {
    setGeneratingIntents(true);
    setGenerationResults(prev => ({
      ...prev,
      error: undefined
    }));
    
    const response = await apiClient<{intents: IntentResponse[]}>(`/v1/openapi/intents/generate-selected`, {
      method: 'POST',
      token: adminSession.token,
      body: JSON.stringify({
        integration_id: integration.id,
        endpoint_ids: Array.from(selectedEndpoints),
      }),
    });
    
    setGenerationResults(prev => ({
      ...prev,
      intents: response.intents,
    }));
    
    alert(`Successfully generated intents for ${response.intents.length} endpoints!`);
  } catch (err) {
    console.error('Error generating intents:', err);
    const errorMessage = err instanceof Error ? err.message : 'Failed to generate intents';
    setGenerationResults(prev => ({
      ...prev,
      error: errorMessage
    }));
    alert(`Error generating intents: ${errorMessage}`);
  } finally {
    setGeneratingIntents(false);
  }
};
```

## 4. Implementation Steps

### Phase 1: Database Schema Update
1. Create and run the migration to add new fields to `api_endpoints` table
2. Update the `APIEndpoint` model with new fields

### Phase 2: Backend Updates 
1. Update `OpenAPIAdapter._extract_endpoints()` to extract and store OpenAPI details
2. Add new endpoints for selective generation
3. Test the existing functionality still works

### Phase 3: Frontend Updates
1. Update TypeScript interfaces
2. Create the `SelectableEndpointCard` component
3. Update the Generate tab with endpoint selection
4. Update generation handlers to use selected endpoints

### Phase 4: Testing
1. Test endpoint import with descriptions and details
2. Test endpoint selection UI
3. Test selective template/intent generation
4. Verify backward compatibility

## 5. Additional Considerations

### Error Handling
- Handle cases where OpenAPI spec doesn't have descriptions
- Gracefully handle malformed endpoint data
- Provide clear feedback when no endpoints are selected

### Performance
- Consider pagination for integrations with many endpoints
- Optimize database queries for large endpoint sets
- Cache endpoint details for better performance

### Future Enhancements
- Add search/filter functionality for endpoints
- Allow saving endpoint selection preferences
- Add bulk operations (enable/disable selected endpoints)
- Export/import endpoint configurations

This implementation provides a comprehensive solution for both requirements while maintaining backward compatibility and following the existing architectural patterns in the codebase.