# Documentation Templates Implementation Plan

## Phase 1: Documentation Template Foundation

### 1.1 Database Schema Updates
Add documentation templates to the database structure:

```sql
-- Add to template_types enum
ALTER TYPE template_type ADD VALUE 'documentation';

-- Update template generation to include documentation templates
-- The existing template structure should already support this
```

### 1.2 Documentation Template Model
```python
# src/coherence/models/template.py (update existing)
class Template(TimestampMixin, CoherenceBase):
    # Existing fields...
    
    # Add validation for documentation template type
    @validates('template_type')
    def validate_template_type(self, key, value):
        valid_types = ['intent', 'action', 'fallback', 'documentation']
        if value not in valid_types:
            raise ValueError(f"Invalid template type: {value}")
        return value
```

### 1.3 Documentation Template Schema
```python
# src/coherence/schemas/admin_template.py
class DocumentationContent(BaseModel):
    description: str
    parameters: Dict[str, ParameterDocumentation]
    responses: Dict[str, ResponseDocumentation]
    examples: List[ExampleDocumentation]
    rate_limits: Optional[RateLimitDocumentation]
    authentication: Optional[AuthenticationDocumentation]
    
class ParameterDocumentation(BaseModel):
    description: str
    type: str
    format: Optional[str]
    required: bool
    default: Optional[Any]
    examples: List[Any]
    constraints: Optional[Dict[str, Any]]
    
class ExampleDocumentation(BaseModel):
    title: str
    description: Optional[str]
    request: str  # Natural language request
    parameters: Dict[str, Any]
    response: Dict[str, Any]
    
class DocumentationTemplate(TemplateBase):
    template_type: Literal["documentation"]
    content: DocumentationContent
    related_action_template: Optional[str]  # Link to action template
    related_intent_template: Optional[str]  # Link to intent template
```

### 1.4 Action Generator Updates
```python
# src/coherence/openapi_adapter/action_generator.py
class ActionGenerator:
    async def generate_templates_from_spec(
        self,
        spec: OpenAPISpec,
        tenant_id: str,
        existing_templates: Dict[str, Template]
    ) -> List[Template]:
        """Generate all template types including documentation"""
        templates = []
        
        for path, path_item in spec.paths.items():
            for method, operation in path_item.items():
                # Generate action template (existing)
                action_template = await self._generate_action_template(...)
                templates.append(action_template)
                
                # Generate intent template (existing)
                intent_template = await self._generate_intent_template(...)
                templates.append(intent_template)
                
                # Generate fallback template (existing)
                fallback_template = await self._generate_fallback_template(...)
                templates.append(fallback_template)
                
                # NEW: Generate documentation template
                docs_template = await self._generate_documentation_template(
                    path=path,
                    method=method,
                    operation=operation,
                    spec=spec,
                    action_template_key=action_template.template_key,
                    intent_template_key=intent_template.template_key
                )
                templates.append(docs_template)
        
        return templates
    
    async def _generate_documentation_template(
        self,
        path: str,
        method: str,
        operation: Dict[str, Any],
        spec: OpenAPISpec,
        action_template_key: str,
        intent_template_key: str
    ) -> Template:
        """Generate documentation template from OpenAPI operation"""
        
        # Extract parameter documentation
        param_docs = self._extract_parameter_documentation(operation)
        
        # Extract response documentation
        response_docs = self._extract_response_documentation(operation)
        
        # Generate examples based on parameters
        examples = await self._generate_documentation_examples(
            operation=operation,
            parameters=param_docs
        )
        
        # Build documentation content
        content = DocumentationContent(
            description=operation.get('description', operation.get('summary', '')),
            parameters=param_docs,
            responses=response_docs,
            examples=examples,
            rate_limits=self._extract_rate_limits(operation),
            authentication=self._extract_auth_info(operation, spec)
        )
        
        # Create template
        template_key = self._generate_key(path, method, "documentation")
        
        return Template(
            template_key=template_key,
            template_type="documentation",
            tenant_id=self.tenant_id,
            name=f"{operation.get('operationId', '')} Documentation",
            description=f"Documentation for {method.upper()} {path}",
            content=content.dict(),
            tags=operation.get('tags', []) + ['documentation'],
            metadata={
                'endpoint': path,
                'method': method.upper(),
                'operation_id': operation.get('operationId'),
                'related_action': action_template_key,
                'related_intent': intent_template_key
            }
        )
```

### 1.5 Vector Indexing Updates
```python
# src/coherence/services/vector_indexer.py
class VectorIndexer:
    async def index_documentation_template(
        self,
        template: Template,
        tenant_id: str
    ) -> None:
        """Index documentation template with appropriate triggers"""
        
        # Generate embedding focused on help/documentation queries
        doc_text = self._build_documentation_text(template)
        
        # Add question-oriented prefixes to improve matching
        augmented_texts = [
            f"How do I use {template.name}",
            f"What parameters does {template.name} need",
            f"Documentation for {template.name}",
            f"Help with {template.name}",
            doc_text
        ]
        
        # Generate embeddings for all variations
        embeddings = await self._generate_embeddings(augmented_texts)
        
        # Store in vector database with metadata
        await self.vector_store.upsert(
            collection_name=f"templates_{tenant_id}",
            points=[
                {
                    "id": f"doc_{template.template_key}_{i}",
                    "vector": embedding,
                    "payload": {
                        "template_key": template.template_key,
                        "template_type": "documentation",
                        "tenant_id": tenant_id,
                        "text": text,
                        "metadata": template.metadata
                    }
                }
                for i, (text, embedding) in enumerate(zip(augmented_texts, embeddings))
            ]
        )
```

### 1.6 Testing Documentation Templates
```python
# tests/unit/test_documentation_templates.py
class TestDocumentationTemplates:
    async def test_generate_documentation_template(self):
        """Test documentation template generation from OpenAPI"""
        generator = ActionGenerator()
        operation = {
            "operationId": "getWeather",
            "summary": "Get current weather",
            "parameters": [...],
            "responses": {...}
        }
        
        template = await generator._generate_documentation_template(
            path="/weather",
            method="get",
            operation=operation,
            spec=mock_spec,
            action_template_key="weather_get_action",
            intent_template_key="weather_get_intent"
        )
        
        assert template.template_type == "documentation"
        assert template.content["parameters"]
        assert template.content["examples"]
    
    async def test_vector_search_returns_docs(self):
        """Test that documentation templates are returned in search"""
        indexer = VectorIndexer()
        
        # Index a documentation template
        await indexer.index_documentation_template(doc_template, tenant_id)
        
        # Search with documentation-oriented query
        results = await indexer.search(
            query="how do I use the weather API",
            tenant_id=tenant_id
        )
        
        # Should find documentation template
        doc_results = [r for r in results if r.template_type == "documentation"]
        assert len(doc_results) > 0
```

## Phase 2: LLM-Based Parameter Collection

### 2.1 Conversational Parameter Extractor
```python
# src/coherence/intent_pipeline/conversational_parameter_extractor.py
class ConversationalParameterExtractor:
    def __init__(
        self,
        llm_provider: BaseLLMProvider,
        template_service: TemplateService
    ):
        self.llm_provider = llm_provider
        self.template_service = template_service
    
    async def extract_parameters(
        self,
        message: str,
        intent_definition: IntentDefinition,
        conversation_context: ConversationContext,
        docs_template: Optional[Template] = None
    ) -> Union[Dict[str, Any], ParameterCollectionResponse]:
        """
        Extract parameters using LLM with conversational context
        """
        # Build comprehensive context
        context = self._build_extraction_context(
            message=message,
            intent_definition=intent_definition,
            conversation_history=conversation_context.history,
            docs_template=docs_template
        )
        
        # Use LLM to extract parameters
        extraction_result = await self._llm_extract(context)
        
        # Handle different response types
        if extraction_result.all_parameters_collected:
            return extraction_result.parameters
        
        elif extraction_result.needs_clarification:
            return ParameterClarification(
                type="parameter_clarification",
                parameter_name=extraction_result.unclear_parameter,
                clarification_question=extraction_result.clarification_question,
                options=extraction_result.suggested_options
            )
        
        elif extraction_result.missing_required:
            # Generate natural question for next parameter
            question = await self._generate_parameter_question(
                missing_param=extraction_result.next_missing_param,
                collected_params=extraction_result.collected_params,
                intent_context=intent_definition.description,
                docs_template=docs_template
            )
            
            return ParameterQuestion(
                type="parameter_question",
                parameter_name=extraction_result.next_missing_param.name,
                parameter_type=extraction_result.next_missing_param.type,
                question=question,
                examples=extraction_result.next_missing_param.examples
            )
        
        elif extraction_result.documentation_requested:
            # User asking about parameters
            return await self._generate_parameter_documentation(
                query=message,
                docs_template=docs_template,
                relevant_params=extraction_result.relevant_parameters
            )
```

### 2.2 Enhanced Chat Orchestrator
```python
# src/coherence/intent_pipeline/orchestrator.py (updates)
class ChatOrchestrator:
    async def process_message(
        self,
        message: str,
        user_id: str,
        tenant_id: str,
        conversation_id: Optional[str] = None
    ) -> ChatResponse:
        """Enhanced to handle documentation and conversational parameter collection"""
        
        # Get conversation context
        context = await self._get_conversation_context(conversation_id)
        
        # Resolve intent with multi-template support
        resolution = await self.intent_resolver.resolve_with_classification(
            message=message,
            tenant_id=tenant_id,
            context=context
        )
        
        # Handle based on classification
        if resolution.classification == "documentation":
            # Return documentation response
            docs_template = await self.template_service.get_template(
                resolution.template_key,
                tenant_id
            )
            return await self._generate_documentation_response(
                message=message,
                docs_template=docs_template,
                context=context
            )
        
        elif resolution.classification == "action":
            # Get related documentation template
            action_template = await self.template_service.get_template(
                resolution.template_key,
                tenant_id
            )
            docs_template = await self._get_related_docs_template(
                action_template,
                tenant_id
            )
            
            # Use conversational parameter extraction
            param_result = await self.conversational_extractor.extract_parameters(
                message=message,
                intent_definition=resolution.intent_definition,
                conversation_context=context,
                docs_template=docs_template
            )
            
            if isinstance(param_result, dict):
                # All parameters collected, execute action
                return await self._execute_action(
                    action_template=action_template,
                    parameters=param_result
                )
            else:
                # Need more information
                return param_result
```

### 2.3 Parameter Collection State Management
```python
# src/coherence/models/generated_action.py (updates)
class GeneratedAction(TimestampMixin, CoherenceBase):
    # Existing fields...
    
    # Add parameter collection state
    parameter_collection_state = Column(
        JSON,
        nullable=True,
        comment="State of parameter collection process"
    )
    
    conversation_history = Column(
        JSON,
        nullable=True,
        comment="Conversation history during parameter collection"
    )

# src/coherence/schemas/response.py
class ParameterCollectionState(BaseModel):
    collected_parameters: Dict[str, Any]
    pending_parameters: List[str]
    validation_attempts: Dict[str, int]
    last_question_asked: Optional[str]
    documentation_shown: List[str]
```

## Implementation Timeline

### Week 1: Documentation Templates Foundation
- Day 1-2: Database schema updates and models
- Day 2-3: Update action generator to create documentation templates
- Day 3-4: Implement vector indexing for documentation
- Day 4-5: Testing and validation

### Week 2: Vector Search Enhancement
- Day 1-2: Implement multi-template search handling
- Day 2-3: Add intent classification system
- Day 3-4: Update response generation for documentation
- Day 4-5: Integration testing

### Week 3: Conversational Parameter Collection
- Day 1-2: Create ConversationalParameterExtractor
- Day 2-3: Update ChatOrchestrator for multi-turn flow
- Day 3-4: Implement state management
- Day 4-5: Testing conversational flows

### Week 4: Integration and Polish
- Day 1-2: End-to-end integration testing
- Day 2-3: Performance optimization
- Day 3-4: Documentation and examples
- Day 4-5: Final testing and deployment prep

## Success Criteria
1. Documentation templates generated for all API endpoints
2. Vector search correctly returns both intent and doc templates
3. LLM-based classification accurately routes requests
4. Conversational parameter collection reduces average turns by 40%
5. Users can ask for help mid-flow and resume seamlessly

## Risks and Mitigations
- **Risk**: Increased vector search latency with more templates
  - **Mitigation**: Optimize search with metadata filters, caching
  
- **Risk**: LLM classification confusion between intents and docs
  - **Mitigation**: Clear prompt engineering, fallback to user choice
  
- **Risk**: Complex state management for conversations
  - **Mitigation**: Clear state boundaries, robust error recovery