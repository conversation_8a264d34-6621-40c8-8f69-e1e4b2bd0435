# General Process for Generating Templates from API Integrations

The process of generating templates from API integrations involves several key steps, leveraging OpenAPI specifications to automate the creation of executable templates within the system. This allows for the integration of external APIs without requiring manual code development for each endpoint.

```mermaid
graph TD
    A[Admin/User Initiates Import] --> B{Provide OpenAPI Spec};
    B --> C[OpenAPI Adapter receives Spec];
    C --> D{Validate and Parse Spec};
    D -- Valid Spec --> E[Extract API Integration Details];
    E --> F[Store API Integration in DB];
    E --> G[Extract Endpoints and Auth Methods];
    G --> H[Store Endpoints and Auth Config in DB];
    H --> I{Generate Action Templates};
    I -- For each Endpoint --> J[Extract Endpoint Spec and Parameters];
    J --> K[Generate Parameter Mapping];
    J --> L[Generate Response Mapping];
    J --> M[Generate Authentication Config];
    J --> N[Generate Default Template Body];
    J --> O[Convert Parameters to Schema];
    K & L & M & N & O --> P[Create Action Template Definition];
    P --> Q[Create Fallback Template Definition];
    P & Q --> R[Store Templates in DB];
    R --> S[Templates Available for Intent Mapping];
    S --> T[Intent Pipeline];
    T --> U{Intent Matched to Template};
    U --> V[DynamicActionExecutor Executes Template];
    V --> W[API Call Made];
    W --> X[Response Received];
    X --> Y[Response Mapped];
    Y --> Z[Template Rendered with Results];
    Z --> AA[Response to User];

    D -- Invalid Spec --> AB[Return Error];
    I -- Error Generating Template --> AC[Log Warning/Error];
```

**Step-by-Step Process:**

1.  **Initiate API Integration Import:**
    *   An administrator or authorized user initiates the process, typically through an Admin Interface (e.g., `coherence-admin`).
    *   The user provides the OpenAPI specification for the API they want to integrate. This can be done by providing a URL to the spec file or by uploading the file content directly.

2.  **Receive and Validate OpenAPI Specification:**
    *   The system's OpenAPI Adapter (`src/coherence/openapi_adapter/adapter.py`) receives the provided specification data.
    *   The adapter's `import_spec` function is called.
    *   The `_validate_spec` function within the adapter performs basic validation on the specification to ensure it is a valid OpenAPI document and is in a supported version (e.g., 3.0.x or 3.1.x).

3.  **Extract API Integration Details:**
    *   If the specification is valid, the adapter extracts high-level information about the API, such as the API name, version, and base URL (`_extract_base_url`).
    *   A record for the API Integration is created in the database, storing the name, version, base URL, and the raw OpenAPI specification data.

4.  **Extract Endpoints and Authentication Methods:**
    *   The adapter parses the "paths" section of the OpenAPI specification to identify all available API endpoints (`_extract_endpoints`). For each path and supported HTTP method (GET, POST, PUT, DELETE, PATCH), an API Endpoint record is created in the database, including the path, method, and operation ID (if available). Default rate limits are also associated with each endpoint.
    *   The adapter also extracts information about the authentication methods supported by the API from the "securitySchemes" or "securityDefinitions" sections (`_extract_auth_methods`).
    *   If authentication methods are found, an API Auth Config record is created in the database, determining the primary authentication type (`_determine_primary_auth_type`).

5.  **Generate Action Templates:**
    *   After the API integration and its endpoints are stored, the `generate_action_templates` function in the OpenAPI Adapter is called. This function iterates through each stored API Endpoint associated with the integration.
    *   For each endpoint, the adapter retrieves the specific endpoint details from the original OpenAPI specification (`_get_endpoint_spec`).
    *   A unique `template_key` is generated for the action template, typically based on the API key and the endpoint's operation ID or a normalized path and method (`_generate_action_name`).
    *   **Parameter Handling:** The adapter extracts the parameters defined for the endpoint (path, query, header, and request body parameters) (`_extract_parameters`). It then generates a `parameter_mapping` dictionary (`_generate_parameter_mapping`) that maps the API parameter names to template expressions (e.g., `{{ parameters.location.latitude }}`). This mapping defines how parameters extracted from user intent will be passed to the API call. The extracted parameters are also converted into a parameter schema for the template (`_convert_parameters_to_schema`).
    *   **Response Mapping:** The adapter analyzes the response schemas defined in the OpenAPI spec for successful responses (e.g., 200, 201) (`_generate_response_mapping`). It generates a `response_mapping` dictionary that maps keys for the template context (e.g., `current_temp`, `forecast`) to paths within the API response JSON (e.g., `{{ response.current.temperature_2m }}`). This mapping determines how data from the API response will be made available for rendering the final user-facing response.
    *   **Authentication Configuration:** The adapter generates an `authentication` configuration based on the stored API Auth Config for the integration (`_generate_auth_config`). This configuration specifies the type of authentication (e.g., API key, Bearer token) and how the necessary credentials (retrieved from a credential manager using template expressions like `{{ credentials.weather_api }}`) should be included in the API request (e.g., in headers or query parameters).
    *   **Default Template Body:** A default `body` for the template is generated (`_generate_default_body`). This body uses the `response_mapping` to create a human-readable message incorporating data from the API response using template expressions.
    *   An action template definition is created, including the generated `template_key`, `category` (e.g., `response_gen`), the default `body`, the `actions` array containing the API call configuration (with `api_key`, `integration_id`, `endpoint_id`, `endpoint`, `method`, `parameter_mapping`, `response_mapping`, and `authentication`), the extracted `parameters` schema, and relevant `metadata` (indicating it was OpenAPI-generated, the associated integration and endpoint IDs, etc.).
    *   A fallback template is also generated for each action template (`generate_action_templates`). This template has a simple body indicating an error occurred and is used when the primary API call fails.

6.  **Store Generated Templates:**
    *   The generated action and fallback template definitions are stored in the database via the Template Service (`src/coherence/template_system/services/template_service.py`).

7.  **Integration into Intent Pipeline:**
    *   The generated templates become available for use within the Intent Pipeline (`src/coherence/intent_pipeline/`).
    *   When a user provides input, the Intent Pipeline's Resolver attempts to match the user's intent to a relevant template based on the template's description and parameters.
    *   If a generated action template is matched, the Orchestrator (`src/coherence/intent_pipeline/orchestrator.py`) retrieves the template.
    *   The Orchestrator uses the Dynamic Action Executor (`src/coherence/openapi_adapter/dynamic_executor.py`) to execute the API call defined in the template's `actions` configuration.
    *   The Dynamic Action Executor uses the `parameter_mapping` to map the parameters extracted from the user's intent to the required API parameters. It retrieves necessary credentials using the `authentication` configuration and a credential manager.
    *   The API call is made to the external service.
    *   Upon receiving the API response, the Dynamic Action Executor uses the `response_mapping` to extract relevant data and make it available in the template rendering context.
    *   The Orchestrator then renders the template's `body` using the response data and the original intent parameters, generating the final response message for the user.
    *   Error handling configured in the template (e.g., retries, fallback templates) is managed by the Dynamic Action Executor and the Orchestrator.