# Comprehensive Migration Plan: Remove Coherence Tenants, Use Clerk Organizations + API Key Management

## Phase 1: Remove Current Tenant System

### Step 1.1: Update TypeScript Types
```typescript
// In /src/lib/types/api.ts
// Remove these interfaces:
// - Tenant
// - TenantInput  
// - TenantSettings
// - ComplianceSettings
// - IntegrationLimits
// - CustomBranding

// Keep these interfaces:
// - ApiKey (but update to reference orgId instead of tenantId)
// - ApiKeyInput
// - ApiResponse

// Add new interface:
interface OrganizationApiKey extends ApiKey {
  organization_id: string; // Clerk org ID
  name: string;
  permissions: string[];
  created_by: string;
  last_used_at: string | null;
  expires_at: string | null;
}

// Update AdminSession interface:
interface AdminSession {
  clerkUserId: string;
  clerkOrgId: string | null;
  user: ClerkUser | null;
  organization: any; // Clerk Organization object
  isSystemAdmin: boolean;
  apiKeys: OrganizationApiKey[];
  primaryApiKey: string | null;
  isLoading: boolean;
  error: Error | null;
}
```

### Step 1.2: Remove Tenant API Routes
```bash
# Delete these directories:
rm -rf /src/app/api/tenant/
rm -rf /src/app/api/test-coherence/

# Remove tenant references from existing routes
# Check and update /src/app/api/clerk-auth-status/route.ts if needed
```

### Step 1.3: Remove Tenant Hooks
```bash
# Delete these files:
rm /src/lib/hooks/api/useTenants.ts

# Update /src/lib/hooks/api/index.ts to remove tenant exports
```

## Phase 2: Create API Key Management System

### Step 2.1: Create API Key Hooks
```typescript
// /src/lib/hooks/api/useApiKeys.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAdminSession } from '../useAdminSession';

export const useOrganizationApiKeys = () => {
  const { organization } = useAdminSession();
  
  return useQuery({
    queryKey: ['api-keys', organization?.id],
    queryFn: async () => {
      const response = await fetch(`/api/api-keys?org_id=${organization?.id}`);
      return response.json();
    },
    enabled: !!organization?.id,
  });
};

export const useCreateApiKey = () => {
  const queryClient = useQueryClient();
  const { organization } = useAdminSession();
  
  return useMutation({
    mutationFn: async ({ name, permissions }: { name: string; permissions: string[] }) => {
      const response = await fetch('/api/api-keys', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ org_id: organization?.id, name, permissions }),
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['api-keys', organization?.id] });
    },
  });
};

export const useRevokeApiKey = () => {
  const queryClient = useQueryClient();
  const { organization } = useAdminSession();
  
  return useMutation({
    mutationFn: async (keyId: string) => {
      const response = await fetch(`/api/api-keys/${keyId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ org_id: organization?.id }),
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['api-keys', organization?.id] });
    },
  });
};
```

### Step 2.2: Create API Key API Routes
```typescript
// /src/app/api/api-keys/route.ts
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const { userId, orgId, orgRole } = auth();
  
  if (!userId || !orgId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { searchParams } = new URL(request.url);
  const requestedOrgId = searchParams.get('org_id');
  
  if (requestedOrgId !== orgId) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_COHERENCE_API_URL}/admin/organizations/${orgId}/api-keys`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.COHERENCE_MASTER_API_KEY}`,
        },
      }
    );
    
    return NextResponse.json(await response.json());
  } catch (error) {
    console.error('API key fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  const { userId, orgId, orgRole } = auth();
  
  if (!userId || !orgId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  if (!['admin', 'owner'].includes(orgRole)) {
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
  }
  
  const { org_id, name, permissions } = await request.json();
  
  if (org_id !== orgId) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_COHERENCE_API_URL}/admin/organizations/${orgId}/api-keys`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.COHERENCE_MASTER_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          permissions,
          created_by: userId,
        }),
      }
    );
    
    if (!response.ok) {
      throw new Error(`API key creation failed: ${response.status}`);
    }
    
    return NextResponse.json(await response.json());
  } catch (error) {
    console.error('API key creation error:', error);
    return NextResponse.json({ error: 'Failed to create API key' }, { status: 500 });
  }
}

// /src/app/api/api-keys/[keyId]/route.ts
export async function DELETE(
  request: Request,
  { params }: { params: { keyId: string } }
) {
  const { userId, orgId, orgRole } = auth();
  
  if (!userId || !orgId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  if (!['admin', 'owner'].includes(orgRole)) {
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
  }
  
  const { org_id } = await request.json();
  
  if (org_id !== orgId) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_COHERENCE_API_URL}/admin/organizations/${orgId}/api-keys/${params.keyId}`,
      {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${process.env.COHERENCE_MASTER_API_KEY}`,
        },
      }
    );
    
    if (!response.ok) {
      throw new Error(`API key deletion failed: ${response.status}`);
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('API key deletion error:', error);
    return NextResponse.json({ error: 'Failed to delete API key' }, { status: 500 });
  }
}
```

### Step 2.3: Create API Key Management UI
```typescript
// /src/app/admin/api-keys/page.tsx
"use client";

import { useOrganization } from "@clerk/nextjs";
import { useOrganizationApiKeys, useCreateApiKey, useRevokeApiKey } from "@/lib/hooks/api/useApiKeys";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";

export default function ApiKeysPage() {
  const { organization, membership } = useOrganization();
  const { data: apiKeys, isLoading } = useOrganizationApiKeys();
  const createApiKey = useCreateApiKey();
  const revokeApiKey = useRevokeApiKey();
  
  const [newKeyName, setNewKeyName] = useState('');
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  
  const canManageKeys = ['admin', 'owner'].includes(membership?.role || '');
  
  if (!canManageKeys) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">API Keys</h1>
        <p className="text-gray-600">You don't have permission to manage API keys for this organization.</p>
      </div>
    );
  }
  
  const handleCreateKey = async () => {
    if (!newKeyName.trim()) return;
    
    await createApiKey.mutateAsync({
      name: newKeyName,
      permissions: selectedPermissions,
    });
    
    setNewKeyName('');
    setSelectedPermissions([]);
  };
  
  const handleRevokeKey = async (keyId: string) => {
    if (confirm('Are you sure you want to revoke this API key?')) {
      await revokeApiKey.mutateAsync(keyId);
    }
  };
  
  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Keys for {organization?.name}</h1>
      
      {/* Create New API Key */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Create New API Key</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-4">
            <Input
              placeholder="API Key Name"
              value={newKeyName}
              onChange={(e) => setNewKeyName(e.target.value)}
              className="flex-1"
            />
            <Button onClick={handleCreateKey} disabled={!newKeyName.trim()}>
              Create Key
            </Button>
          </div>
          
          {/* Permission selector would go here */}
          <div className="text-sm text-gray-600">
            Note: New API keys will have full organization permissions.
          </div>
        </CardContent>
      </Card>
      
      {/* Existing API Keys */}
      <Card>
        <CardHeader>
          <CardTitle>Existing API Keys</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p>Loading API keys...</p>
          ) : apiKeys?.length === 0 ? (
            <p className="text-gray-600">No API keys found. Create one to get started.</p>
          ) : (
            <div className="space-y-4">
              {apiKeys?.map((key) => (
                <div key={key.id} className="flex items-center justify-between p-4 border rounded">
                  <div>
                    <h3 className="font-medium">{key.name}</h3>
                    <div className="text-sm text-gray-600">
                      <p>Created: {new Date(key.created_at).toLocaleDateString()}</p>
                      <p>Last used: {key.last_used_at ? new Date(key.last_used_at).toLocaleDateString() : 'Never'}</p>
                      <p>Key: {key.key_prefix}••••••••</p>
                    </div>
                  </div>
                  <Button
                    variant="destructive"
                    onClick={() => handleRevokeKey(key.id)}
                  >
                    Revoke
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
```

## Phase 3: Backend Coherence API - Tenant to Organization Migration

This phase focuses on adapting the Python backend (Coherence API) to transition from its internal tenant management system to utilizing Clerk organizations as the primary identifier for tenancy and to introduce robust API key management tied to these organizations. It assumes the decision to use a **new `organization_api_keys` table (Option B)** and **require users to regenerate API keys**.

### 1. Objectives
*   Fully integrate `clerk_org_id` as the primary identifier for tenants/organizations within the Coherence API.
*   Implement a new API key management system where API keys are scoped to `clerk_org_id` and support distinct names, permissions, and tracking.
*   Update authentication and authorization mechanisms to leverage Clerk organization IDs and user roles.
*   Ensure data integrity and provide a migration path for existing tenants to associate with `clerk_org_id`.
*   Modify or deprecate old tenant-centric API endpoints and introduce new organization-centric endpoints.

### 2. Key Tasks

#### 2.1. Database Schema Modifications (Alembic Migrations)

*   **`tenants` Table:**
    *   **Confirm `clerk_org_id` Column:** Already added via migration [`20250509_180645_add_clerk_org_id.py`](../../../../../alembic/versions/20250509_180645_add_clerk_org_id.py). Ensure it's indexed for efficient lookups.
    *   **Future:** Plan for making `clerk_org_id` non-nullable once data migration is complete and new tenant creation exclusively uses it.
*   **New `organization_api_keys` Table:**
    *   **Alembic Script:** Create a new migration to define the `organization_api_keys` table with columns:
        *   `id` (UUID, Primary Key)
        *   `clerk_org_id` (String, Indexed, Foreign Key conceptually linking to `tenants.clerk_org_id` if `tenants` table is kept as the org master)
        *   `name` (String, for user-friendly identification)
        *   `key_hash` (String, Unique, for secure storage of the key)
        *   `key_prefix` (String, e.g., first 8 chars of the key for display)
        *   `permissions` (JSONB, to store a list or structure of allowed actions/scopes)
        *   `created_by` (String, storing the Clerk User ID of the creator)
        *   `created_at` (DateTime, with timezone, server default now)
        *   `last_used_at` (DateTime, with timezone, nullable)
        *   `expires_at` (DateTime, with timezone, nullable)
        *   `revoked` (Boolean, default False)

#### 2.2. Python Model Updates (SQLAlchemy)

*   **`Tenant` Model ([`src/coherence/models/tenant.py`](../../../../../src/coherence/models/tenant.py)):**
    *   Ensure the relationship to the new `OrganizationAPIKey` model is defined if `tenants` remains the master organizational record.
    *   Example: `organization_api_keys: Mapped[List["OrganizationAPIKey"]] = relationship(back_populates="tenant_organization")` (adjust `back_populates` based on the new model).
*   **New `OrganizationAPIKey` Model:**
    *   Create a new SQLAlchemy model in a relevant file (e.g., [`src/coherence/models/api_key.py`](../../../../../src/coherence/models/api_key.py) or keep in [`src/coherence/models/tenant.py`](../../../../../src/coherence/models/tenant.py) if closely tied) corresponding to the `organization_api_keys` table schema.
*   **Schema Updates ([`src/coherence/schemas/tenant.py`](../../../../../src/coherence/schemas/tenant.py) or new `api_key_schemas.py`):**
    *   Create new Pydantic schemas: `OrganizationAPIKeyCreateRequest`, `OrganizationAPIKeyCreateResponse` (includes full key value only on creation), `OrganizationAPIKeyRead` (omits full key value, shows prefix), `OrganizationAPIKeyUpdate`.
    *   These schemas should reflect the new model and fields like `name`, `permissions`, `key_prefix`.

#### 2.3. Data Migration Scripts

*   **Populate `tenants.clerk_org_id`:**
    *   Develop an Alembic data migration script or a standalone script to map existing Coherence `tenants.id` to their corresponding Clerk `organization_id`. This will populate the `tenants.clerk_org_id` column for all existing tenant records. This step is crucial for linking old tenant data to the new Clerk organization structure.
*   **API Key Regeneration:**
    *   No data migration for API keys as users will be required to regenerate them under the new system. Communicate this change clearly to users.

#### 2.4. API Endpoint Modifications & New Endpoints

*   **New Organization API Key Endpoints:**
    *   Create a new router, e.g., in `src/coherence/api/v1/endpoints/admin_api_keys.py`.
    *   Mount it at a path like `/admin/organizations`.
    *   Endpoints (mirroring frontend calls to `NEXT_PUBLIC_COHERENCE_API_URL/admin/organizations/{orgId}/api-keys`):
        *   `GET /{clerk_org_id}/api-keys` -> `OrganizationAPIKeyRead`: List API keys for the given `clerk_org_id`.
        *   `POST /{clerk_org_id}/api-keys` -> `OrganizationAPIKeyCreateResponse`: Create a new API key. Request body: `name`, `permissions`. `created_by` derived from authenticated Clerk user.
        *   `DELETE /{clerk_org_id}/api-keys/{api_key_id}` -> HTTP 204: Revoke (soft delete by setting `revoked=True`) the specified API key.
*   **Authentication & Authorization for New Endpoints:**
    *   Protect these endpoints. Authentication can be via a master API key (for initial setup/admin UI backend calls) or a Clerk JWT.
    *   If Clerk JWT, extract `userId`, `orgId`, and `orgRole`.
    *   Authorization: Verify the authenticated Clerk user (via `orgRole` like 'admin' or 'owner') has permission to manage API keys for the `clerk_org_id` in the path.
*   **Modify Existing Tenant Endpoints ([`src/coherence/api/v1/endpoints/tenants.py`](../../../../../src/coherence/api/v1/endpoints/tenants.py)):**
    *   `GET /by-clerk-org`: Retain. This is useful for associating Clerk orgs with internal tenant entries.
    *   `POST /tenants/`: Ensure `clerk_org_id` is primary. If `name` is still part of `TenantCreate`, ensure it's clear how it relates to Clerk org name.
    *   `GET /tenants/`: Transition to system-admin only for listing all. Individual lookups should prefer `clerk_org_id`.
    *   `GET /tenants/{tenant_id}`, `PUT /tenants/{tenant_id}`: These operate on the internal UUID. Adapt authorization to allow access if the caller's `clerk_org_id` maps to the internal `tenant_id`, or if the caller is a system admin.
    *   Deprecate and eventually remove old API key endpoints: `GET /tenants/{tenant_id}/api-keys`, `POST /tenants/{tenant_id}/api-keys`, etc.
*   **Update Authentication Dependencies ([`src/coherence/api/v1/dependencies/auth.py`](../../../../../src/coherence/api/v1/dependencies/auth.py)):**
    *   `get_tenant_from_api_key`: This will likely be replaced by a new dependency, e.g., `get_clerk_org_from_api_key`, which validates an API key from the new `organization_api_keys` table and returns the associated `clerk_org_id` and potentially the `Tenant` object.
    *   `check_admin_role`: Modify to check Clerk roles (e.g., a custom 'system_admin' role in Clerk user metadata) or a master API key, rather than relying on `tenant.settings.is_admin`.

#### 2.5. Row-Level Security (RLS) Policy Updates

*   **`tenants` Table:**
    *   The existing policy from migration [`20250510_191045_add_rls_policy_for_tenant_clerk_org_.py`](../../../../../alembic/versions/20250510_191045_add_rls_policy_for_tenant_clerk_org_.py) using `current_setting('app.rls.lookup_clerk_org_id', true)` is a good foundation.
    *   Ensure a session variable like `app.current_clerk_org_id` is set upon user authentication (from Clerk JWT) and RLS policies use this for general data access.
*   **`organization_api_keys` Table (New):**
    *   Implement RLS policies:
        *   `SELECT`: Allow if `clerk_org_id` matches `app.current_clerk_org_id` OR user is system admin.
        *   `INSERT`, `UPDATE`, `DELETE`: Stricter, potentially only if `clerk_org_id` matches `app.current_clerk_org_id` AND user has appropriate Clerk role (e.g., 'admin' for the org) OR user is system admin.
*   **Other Tenant-Scoped Tables (e.g., `templates`, `generated_actions`, `tenant_settings`):**
    *   Review and update RLS policies. They should join to the `tenants` table and filter based on `tenants.clerk_org_id = app.current_clerk_org_id` or similar, replacing/supplementing old `tenant_id` checks.

#### 2.6. Business Logic Updates

*   Audit services, background tasks, and any logic currently using `tenant_id` for scoping or identification. Refactor to use `clerk_org_id`.
*   Ensure logging and auditing correctly reference `clerk_org_id`.

### 3. Dependencies and Prerequisites
*   **Clerk Integration:** Backend ability to validate Clerk JWTs and fetch user/organization details (including roles).
*   **Frontend Phases 1 & 2 Completion:** As per [`docs/implementation_plans/clerk-tenant-remediation.md`](../../../../../docs/implementation_plans/clerk-tenant-remediation.md).
*   **Database Access:** Permissions for migrations and application runtime.
*   **Clear Mapping Strategy:** A defined process for mapping existing Coherence tenant IDs to Clerk Organization IDs for the `tenants.clerk_org_id` backfill.

### 4. Expected Deliverables/Outcomes
*   Backend API primarily uses `clerk_org_id` for tenancy.
*   New, secure, permission-aware API key management tied to Clerk organizations.
*   Updated RLS policies enforcing access via `clerk_org_id`.
*   Refactored models, schemas, and authentication dependencies.
*   Alembic migrations for all changes.
*   Clear communication plan for API key regeneration.

### 5. Diagram: Simplified Entity Relationship (Updated)

```mermaid
erDiagram
    TENANTS {
        UUID id PK
        String name
        String clerk_org_id UK "Links to Clerk Organization"
        JSONB settings
        # ... other fields
    }

    ORGANIZATION_API_KEYS {
        UUID id PK
        String clerk_org_id FK "References TENANTS(clerk_org_id)"
        String name
        String key_hash UK
        String key_prefix
        JSONB permissions
        String created_by "Clerk User ID"
        DateTime created_at
        DateTime last_used_at
        DateTime expires_at
        Boolean revoked
    }

    %% Concept: Clerk User and Organization exist in Clerk's system
    %% CLERK_USER {
    %%     String clerk_user_id PK
    %% }
    %%
    %% CLERK_ORGANIZATION {
    %%     String clerk_org_id PK
    %% }

    TENANTS ||--o{ ORGANIZATION_API_KEYS : "manages"
```

## Phase 4: Update API Client

### Step 4.1: Update API Client to Use Organization IDs
```typescript
// /src/lib/api/client.ts
class CoherenceAPIClient {
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_COHERENCE_API_URL || '';
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {},
    session?: AdminSession | null
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    // Updated headers to use organization ID
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    // Use primary API key from session
    if (session?.primaryApiKey) {
      headers['Authorization'] = `Bearer ${session.primaryApiKey}`;
    }
    
    // Use Clerk organization ID
    if (session?.clerkOrgId) {
      headers['X-Organization-ID'] = session.clerkOrgId;
    }
    
    if (session?.clerkUserId) {
      headers['X-User-ID'] = session.clerkUserId;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Remove tenant-related methods, update others to work with organization
  // Keep all existing workflow, template, integration methods
  // Remove the entire tenants property
}
```

## Phase 5: Update UI Components

### Step 5.1: Update Dashboard
```typescript
// /src/app/admin/page.tsx
"use client";

import { useUser, useOrganization } from "@clerk/nextjs";
import { useAdminSession } from "@/lib/hooks/useAdminSession";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function AdminDashboard() {
  const { user } = useUser();
  const { organization } = useOrganization();
  const { apiKeys, primaryApiKey, isLoading } = useAdminSession();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back{user?.firstName ? `, ${user.firstName}` : ''}!
        </h1>
        <p className="mt-2 text-sm text-gray-600">
          You're successfully authenticated and on the admin dashboard.
        </p>
      </div>

      {/* Organization Information */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Organization Details
        </h2>
        <dl className="space-y-2">
          <div>
            <dt className="text-sm font-medium text-gray-500">Organization ID</dt>
            <dd className="text-sm text-gray-900 font-mono">{organization?.id}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Organization Name</dt>
            <dd className="text-sm text-gray-900">{organization?.name}</dd>
          </div>
        </dl>
      </div>

      {/* API Key Status */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900">
            API Key Status
          </h2>
          <Link href="/admin/api-keys">
            <Button variant="outline">Manage API Keys</Button>
          </Link>
        </div>
        
        {isLoading ? (
          <p className="text-sm text-gray-600">Loading API key status...</p>
        ) : apiKeys.length === 0 ? (
          <div className="text-center py-4">
            <p className="text-sm text-gray-600 mb-2">No API keys configured</p>
            <Link href="/admin/api-keys">
              <Button>Create Your First API Key</Button>
            </Link>
          </div>
        ) : (
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              <span className="text-sm text-green-700">
                {apiKeys.length} API key{apiKeys.length > 1 ? 's' : ''} active
              </span>
            </div>
            <p className="text-xs text-gray-500">
              Primary key: {primaryApiKey}••••••••
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
```

### Step 5.2: Update Navigation
```typescript
// /src/app/admin/layout.tsx - Update navigation items
const navigation = [
  { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
  { name: 'Workflows', href: '/admin/workflows', icon: Workflow },
  { name: 'Templates', href: '/admin/templates', icon: FileText },
  { name: 'Integrations', href: '/admin/integrations', icon: Puzzle },
  { name: 'API Keys', href: '/admin/api-keys', icon: Key }, // New item
  { name: 'Settings', href: '/admin/settings', icon: Settings },
];
```

## Phase 6: Environment & Configuration

### Step 6.1: Update Environment Variables
```env
# Add these to .env.local:
COHERENCE_MASTER_API_KEY=your_coherence_master_api_key_here
NEXT_PUBLIC_COHERENCE_API_URL=https://your-coherence-api.com

# Remove these if they exist:
# COHERENCE_API_KEY (no longer needed)
```

## Phase 7: Testing & Validation

### Step 7.1: Create Testing Checklist
```markdown
## Migration Testing Checklist

1. Authentication Flow
   - [ ] User can sign in with Clerk
   - [ ] Organization switching works
   - [ ] Permissions are correctly applied

2. API Key Management
   - [ ] Can list organization's API keys
   - [ ] Can create new API key
   - [ ] Can revoke API key
   - [ ] Only admins can manage keys

3. API Functionality
   - [ ] Workflows API calls work with organization ID
   - [ ] Templates API calls work
   - [ ] Integrations API calls work
   - [ ] Proper headers are sent (X-Organization-ID)

4. UI Components
   - [ ] Dashboard shows organization info
   - [ ] API key status is displayed
   - [ ] API key management page works
   - [ ] No tenant references remain

5. Data Migration (if applicable)
   - [ ] Existing tenant data mapped to organizations
   - [ ] API keys transferred correctly
   - [ ] User permissions maintained
```

## Phase 8: Deployment & Rollback Plan

### Step 8.1: Deployment Strategy
```markdown
## Deployment Plan

1. Pre-deployment
   - Backup existing tenant data
   - Prepare migration scripts
   - Test in staging environment

2. Deployment
   - Deploy backend changes first
   - Deploy frontend changes
   - Run migration scripts
   - Monitor for errors

3. Post-deployment
   - Verify all organizations can access their data
   - Confirm API keys work correctly
   - Check error logs

4. Rollback Plan (if needed)
   - Revert to previous deployment
   - Restore tenant database
   - Re-enable tenant endpoints
```

This comprehensive plan completely removes the separate tenant concept while maintaining all API key management functionality through Clerk organizations. The migration provides a cleaner architecture while preserving all necessary features for multi-tenant API access.