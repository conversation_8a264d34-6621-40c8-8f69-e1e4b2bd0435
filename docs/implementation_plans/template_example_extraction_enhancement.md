# Template Example Extraction Enhancement Plan

## Current State Analysis

After examining the template generation code, I've identified several areas for improvement in how parameter descriptions and examples are extracted from OpenAPI specifications and integrated into templates.

### Key Findings

1. **Missing Example Extraction Implementation**:
   - The code calls a method `_extract_example_value_from_description()` in `action_generator.py` but this method isn't implemented
   - This results in fallback to generic example values like `example_parameter_name` rather than meaningful examples

2. **Incomplete OpenAPI Schema Utilization**:
   - The OpenAPI specification allows for rich parameter examples through:
     - Direct `example` fields in parameter schemas
     - `examples` collections with named examples
     - `enum` values that can suggest allowed values
   - Currently, only parameter descriptions are used, not actual examples

3. **Parameter Schema Information**:
   - Parameters are extracted with their schema information in `_extract_parameters()` method
   - This schema information is stored in the template but not fully utilized for examples

4. **Template Structure**:
   - Templates have a well-defined structure with support for examples 
   - The `{% block examples %}` section exists but populated examples lack real values
   - Intent patterns are generated but could be more domain-specific with real examples

## Enhancement Plan

### 1. Implement Missing Example Extraction Method

```python
def _extract_example_value_from_description(self, param_name: str, description: str, param_type: str) -> str:
    """Extract example values from parameter descriptions.
    
    Args:
        param_name: Name of the parameter
        description: Parameter description
        param_type: Parameter type (string, integer, etc.)
        
    Returns:
        A reasonable example value based on the description
    """
    # Look for common patterns like "Example: X" or "e.g. X"
    example_patterns = [
        r'example:?\s+[\'"]?([^\'"\n,]+)[\'"]?',
        r'e\.g\.?\s+[\'"]?([^\'"\n,]+)[\'"]?',
        r'for example:?\s+[\'"]?([^\'"\n,]+)[\'"]?',
        r'such as:?\s+[\'"]?([^\'"\n,]+)[\'"]?',
    ]
    
    for pattern in example_patterns:
        match = re.search(pattern, description, re.IGNORECASE)
        if match:
            return match.group(1).strip()
    
    # If no explicit example, generate reasonable values based on parameter name and type
    if param_type == "string":
        if any(key in param_name.lower() for key in ["name", "title"]):
            return "Example Name"
        elif "email" in param_name.lower():
            return "<EMAIL>"
        elif "phone" in param_name.lower():
            return "+1234567890"
        elif "date" in param_name.lower():
            return "2025-05-15"
        elif "id" in param_name.lower():
            return "123e4567-e89b-12d3-a456-426614174000"
        elif "key" in param_name.lower():
            return "api_key_12345"
    elif param_type == "integer":
        return "42"
    elif param_type == "number":
        return "42.5"
    elif param_type == "boolean":
        return "true"
    
    # Default fallback
    return f"example_{param_name}"
```

### 2. Extract Examples from Parameter Schema

Extend the parameter extraction to properly handle OpenAPI examples:

```python
def _extract_examples_from_schema(self, schema: Dict[str, Any], param_name: str, param_type: str) -> str:
    """Extract example values from parameter schemas.
    
    Args:
        schema: Parameter schema
        param_name: Name of the parameter
        param_type: Parameter type
        
    Returns:
        Example value from schema if available
    """
    # Check for direct example
    if "example" in schema:
        example = schema["example"]
        if example is not None:
            return str(example)
    
    # Check for examples collection
    if "examples" in schema and schema["examples"]:
        # Take the first example
        for _, example_obj in schema["examples"].items():
            if isinstance(example_obj, dict) and "value" in example_obj:
                return str(example_obj["value"])
            else:
                return str(example_obj)
    
    # Check for enum values
    if "enum" in schema and schema["enum"]:
        return str(schema["enum"][0])  # Use first enum value
    
    # Check for default value
    if "default" in schema:
        return str(schema["default"])
    
    # Fall back to extraction from description
    if "description" in schema:
        return self._extract_example_value_from_description(
            param_name, schema["description"], param_type
        )
    
    # Last resort
    return f"example_{param_name}"
```

### 3. Enhance Parameter Extraction in Template Generation

Modify the `_extract_parameters` method to collect and store more complete schema information:

```python
def _extract_parameters(self, endpoint_spec: Dict[str, Any]) -> List[Dict[str, Any]]:
    # Existing code...
    
    # For each parameter, enhance with:
    parameters.append({
        "name": param.get("name"),
        "type": param_type,
        "required": param.get("required", param.get("in") == "path"),
        "description": param.get("description", ""),
        "location": param.get("in", "query"),
        "schema": param.get("schema", {}),
        "example": self._extract_examples_from_schema(
            param.get("schema", {}), 
            param.get("name"),
            param_type
        ),
        # Add more metadata
        "format": param.get("schema", {}).get("format"),
        "enum": param.get("schema", {}).get("enum"),
        "pattern": param.get("schema", {}).get("pattern"),
    })
    
    # Request body handling would need similar enhancement
    # ...
```

### 4. Improve Intent Pattern Generation

Enhance the intent pattern generation to include real examples:

```python
# Generate intent patterns based on the API method
if endpoint.method.upper() == "GET":
    for param in parameters[:2]:  # Use first two parameters for examples
        if param.get("required", False) and "example" in param:
            example_value = param["example"]
            intent_patterns.extend([
                f"Get {resource_name} with {param['name']} {example_value} from {integration.name}",
                f"Find {resource_name} where {param['name']} is {example_value}"
            ])
```

### 5. Improve Examples in Template Generation

Enhance the example mapping generation in the template creation:

```python
# Generate examples in the format: "Query example" -> action_name(param="value")
example_mappings = []
operation_name = operation_id.replace("_", " ")
for i, pattern in enumerate(intent_patterns[:4]):  # Use up to 4 patterns for examples
    param_examples = []
    for j, param in enumerate(parameters[:3]):  # Use up to 3 parameters
        param_name = param.get("name")
        example_value = param.get("example", f"example_{param_name}")
                
        param_examples.append(f'{param_name}="{example_value}"')
    
    example_mappings.append(f'"{pattern}" -> {operation_id}({", ".join(param_examples)})')
```

## Expected Improvements

1. **Better Parameter Examples**: Templates will contain realistic examples extracted from OpenAPI specs.

2. **Higher Quality Intent Matching**: With more specific examples, the vector indexing will work better for actual user queries.

3. **Improved Developer Experience**: When viewing templates, developers will see informative examples.

4. **Better End-User Guidance**: Templates can be used to suggest proper parameter values to users.

## Implementation Timeline

1. Implement the missing `_extract_example_value_from_description` method
2. Add the new `_extract_examples_from_schema` method 
3. Enhance the parameter extraction to include examples
4. Improve intent pattern generation with real examples
5. Update template generation to use the enhanced examples
6. Add unit tests to verify example extraction
7. Add integration tests to verify end-to-end template generation with examples

## Future Enhancements

1. **Parameter Type Customization**: Add more specific example extraction based on common parameter formats (dates, email addresses, etc.)

2. **Response Example Templates**: Extract response examples from the OpenAPI spec to improve response templates

3. **Contextual Example Selection**: Choose examples based on template context and use case

4. **User-Customizable Examples**: Allow developers to override examples through the admin interface

5. **Multilingual Example Support**: Generate examples in multiple languages for global applications