# Awesome Admin Site - Detailed Implementation Plan

## 1. Introduction

This document outlines the detailed implementation plan for the "Awesome Admin Site." It breaks down the features defined in the PRD ([`docs/prds/new-admin-site.md`](docs/prds/new-admin-site.md)), considering the Clerk integration ([`docs/analysis/clerk_admin_site_analysis.md`](docs/analysis/clerk_admin_site_analysis.md)) and the high-level architecture ([`docs/architecture/admin_site_architecture.md`](docs/architecture/admin_site_architecture.md)), with the clarified permissions model (Coherence-specific permissions mapped from Clerk roles).

The plan is divided into phases, with tasks/epics detailed for each.

## 2. Core Permissions Model Implementation (Coherence Backend)

Before feature development, we need to establish the Coherence-specific permission mapping.

*   **Task:** [x] Define and Implement Coherence Permission Service
    *   **Feature:** [x] Core Authentication/Authorization
    *   **Frontend:** [x] No direct UI. Will be consumed by the `useAdminSession` hook.
    *   **Backend:**
        *   [x] Create a new service/module (e.g., `src/coherence/services/permission_service.py`).
        *   [x] Define all Coherence-specific permission strings (e.g., `workflow:create`, `workflow:read`, `tenant:list_all`, `system_settings:edit`).
        *   [x] Implement logic to map Clerk roles (e.g., `org_admin`, `org_member` from `request.state.clerk_org_role`) and the `is_system_admin` flag (from `request.state.is_system_admin`) to the list of Coherence-specific permissions.
        *   [x] Example mapping:
            *   `org_admin` -> [`workflow:create`, `workflow:read`, `workflow:update`, `workflow:delete`, `template:create`, ..., `apikey:read`, `apikey:create`, `apikey:delete`, `tenant:view_own_dashboard`]
            *   `org_member` -> [`workflow:read`, `workflow:execute`, `template:read`, `tenant:view_own_dashboard`]
            *   `is_system_admin` -> [`tenant:list_all`, `tenant:create`, `tenant:edit`, `system_settings:view`, `system_settings:edit`, `system_health:view`, ...] (potentially all permissions).
        *   [x] Create a new API endpoint (e.g., `/auth/session-info` or similar, as suggested in architecture doc) that:
            *   [x] Is protected by [`get_clerk_auth_details`](src/coherence/api/v1/dependencies/auth.py:45).
            *   [x] Uses the new permission service to get the list of Coherence permissions for the authenticated user based on their Clerk roles and `is_system_admin` status within their `clerk_org_id`.
            *   [x] Returns these permissions along with other session info (`clerkUserId`, `tenantId`, `tenantName`, `isSystemAdmin`).
        *   [x] Update API endpoint dependencies to use this new permission service for action-specific checks (e.g., a `Depends(check_coherence_permission("workflow:create"))` decorator).
    *   **Database:** [x] No direct schema changes.
    *   **Clerk Configuration:**
        *   [/] Ensure standard Clerk roles (`admin`, `member`) are understood.
        *   [/] Define how the `is_system_admin: true` custom claim will be set for designated system administrator users (e.g., via Clerk user metadata and a JWT hook if necessary, or a specific Clerk "System Admin" custom role that the backend interprets).
    *   **Dependencies:** [x] None. This is foundational.

## 3. Phasing and Task Breakdown

### Phase 1: Foundation & Core Tenant/User Experience

**Goal:** Establish the basic admin site shell, authentication, navigation, and core views for tenant admins.

**Tasks/Epics:**

**Epic 1.1: Admin Site Shell & Authentication**

*   **Feature:** [x] Basic Application Structure & Login
*   **Frontend:**
    *   [x] Set up Next.js 15 project structure in [`coherence-admin/`](coherence-admin/) as per PRD ([`docs/prds/new-admin-site.md:14`](docs/prds/new-admin-site.md:14)).
    *   [x] Implement Clerk SDK (`@clerk/nextjs`) for login, sign-up, user profile UI.
    *   [x] Create main admin layout (`coherence-admin/src/app/admin/layout.tsx`) with basic navigation (sidebar/header).
    *   [/] Implement Clerk `auth()` middleware in `coherence-admin/src/middleware.ts` to protect `/admin` routes.
    *   [x] Implement `AdminSessionContext` and `useAdminSession` hook ([`docs/prds/new-admin-site.md:144`](docs/prds/new-admin-site.md:144)) to:
        *   [x] Fetch session info (including Coherence permissions) from the backend `/auth/session-info` endpoint.
        *   [x] Provide session data (user, tenant, permissions) to the application.
    *   [/] Implement basic `ProtectedRoute` component ([`docs/prds/new-admin-site.md:158`](docs/prds/new-admin-site.md:158)) that checks against `AdminSession.permissions`.
    *   [x] Implement API client ([`coherence-admin/src/lib/apiClient.ts`](coherence-admin/src/lib/apiClient.ts)) to include Clerk JWT and `X-Tenant-ID`.
*   **Backend:**
    *   [x] (Covered by "Core Permissions Model Implementation" task above for the `/auth/session-info` endpoint).
    *   [x] Ensure all new admin API endpoints are protected by [`get_clerk_auth_details`](src/coherence/api/v1/dependencies/auth.py:45).
*   **Database:** [x] No changes.
*   **Clerk Configuration:**
    *   [/] Configure Clerk Next.js SDK (publishable key, domain).
    *   [/] Set up basic sign-in/sign-up flows.
*   **Dependencies:** [x] Core Permissions Model Implementation.

**Epic 1.2: Organization Dashboard (View-Only)**

**Features:**
- [x] Create a new Next.js page at `coherence-admin/src/app/admin/organizations/[id]/page.tsx` to display organization-specific dashboard information.
    - This page will be the primary interface for organization administrators to view their organization's details and metrics.
    - It should be accessible via a URL like `/admin/organizations/org_xxxxxxxxxxxx`.
    - The `[id]` parameter will correspond to the Clerk `organization_id`.
- [x] Implement an API endpoint `GET /admin/organizations/{organization_id}` that fetches and returns data relevant to the specified organization, ensuring that non-System Admins can only view their own organization's dashboard.
    - This endpoint will be secured and should only return data if the requesting user has the appropriate permissions (e.g., `organization:view_own_dashboard`) and is part of the organization or is a System Admin.
- [x] The page should use the `useAdminSession` hook to access the user's permissions and session details.
    - This ensures that the frontend has the necessary context about the logged-in user, their roles, and specifically their permissions related to organization management.
- [x] Access to this dashboard should be restricted based on a new permission string, e.g., `organization:view_own_dashboard`.
    - This permission will be assigned to organization administrators and members who need to view the dashboard.
    - The backend API endpoint will enforce this permission.
    - The frontend page will also check for this permission to provide a better user experience (e.g., showing a "not authorized" message or redirecting).
- [x] Display key organization metrics and information (details TBD, start with basic placeholders like Organization Name, ID, and a list of users if feasible).
    - Initial data points: Organization Name, Organization ID (Clerk ID), Creation Date, Last Updated Date.
    - Future enhancements could include user counts, active services, compliance status indicators, etc.
- [x] Integrate with Clerk to ensure that `organization_id` from the URL matches the user's current active organization if they are not a System Admin.
    - This check should primarily occur on the backend when fetching data. The API should return a 403 or 404 if a non-System Admin tries to access an organization they don't belong to.

**Risks for Phase 1:**
*   [/] Complexity in correctly setting up `AdminSession` with Coherence-specific permissions.
*   [/] Initial setup of Clerk custom claim for `is_system_admin` might require careful configuration.

### Phase 2: Core Workflow & Template Management

**Goal:** Implement the primary functionalities for creating, managing, and using workflows and templates.

**Tasks/Epics:**

**Epic 2.1: Workflow Management (CRUD & Basic Builder)**

*   **Feature:** [/] Workflow Management
*   **Frontend:**
    *   [x] Implement routes under `coherence-admin/src/app/admin/workflows/`.
    *   [x] List Workflows (`page.tsx`): Display workflows for the tenant. Protected by `workflow:read`.
        *   [x] Use `DataTable` component ([`docs/prds/new-admin-site.md:446`](docs/prds/new-admin-site.md:446)).
    *   [ ] Create/Edit Workflow (`create/page.tsx`, `[id]/page.tsx`):
        *   [ ] Implement `WorkflowForm` ([`docs/prds/new-admin-site.md:406`](docs/prds/new-admin-site.md:406)). Protected by `workflow:create` / `workflow:update`.
    *   [ ] Delete Workflow functionality. Protected by `workflow:delete`.
*   **Backend:**
    *   [x] Create API endpoints for workflows (as per PRD [`docs/prds/new-admin-site.md:332-339`](docs/prds/new-admin-site.md:332-339)):
        *   [x] `GET /admin/workflows` (List) - permission: `workflow:read`
        *   [x] `POST /admin/workflows` (Create) - permission: `workflow:create`
        *   [x] `GET /admin/workflows/{id}` (Get) - permission: `workflow:read`
        *   [x] `PUT /admin/workflows/{id}` (Update) - permission: `workflow:update`
        *   [x] `DELETE /admin/workflows/{id}` (Delete) - permission: `workflow:delete`
    *   [x] All endpoints use RLS via `clerk_org_id`.
    *   [x] Business logic for creating/updating workflow structures.
*   **Database:**
    *   [x] Verify `workflows` table schema and RLS policies are adequate. (RLS for `generated_actions` and related tables should already be in place as per [`alembic/versions/20250510_203113_update_generated_actions_rls_for_clerk.py`](alembic/versions/20250510_203113_update_generated_actions_rls_for_clerk.py)).
*   **Clerk Configuration:** [x] None specific.
*   **Dependencies:** Epic 1.1, Core Permissions Model.

**Epic 2.2: Template Management (CRUD & Basic Editor)**

*   **Feature:** [/] Template Management
*   **Frontend:**
    *   [x] Implement routes under `coherence-admin/src/app/admin/templates/`.
    *   [x] List Templates (`page.tsx`): Display templates for the tenant. Protected by `template:read`.
    *   [ ] Create/Edit Template (`create/page.tsx`, `[id]/page.tsx`):
        *   [ ] Implement `TemplateForm`. Protected by `template:create` / `template:update`.
        *   [ ] Implement basic `TemplateEditor` ([`docs/prds/new-admin-site.md:210`](docs/prds/new-admin-site.md:210)) using Monaco editor for content.
    *   [x] Delete Template functionality. Protected by `template:delete`.
    *   [ ] Version history (read-only initially). Protected by `template:read_versions`.
*   **Backend:**
    *   [x] Refactor existing template API endpoints (currently in `src/coherence/api/v1/endpoints/templates.py`) or create new, admin-specific versions to meet the following requirements (as per PRD [`docs/prds/new-admin-site.md:342-349`](docs/prds/new-admin-site.md:342-349)). This includes updating paths, authentication to use `get_clerk_auth_details`, and authorization to use `RequirePermission` with the specified Coherence permissions:
        *   [x] `GET /admin/templates` (List) - permission: `template:read`
        *   [x] `POST /admin/templates` (Create) - permission: `template:create`
        *   [x] `GET /admin/templates/{id}` (Get) - permission: `template:read`
        *   [x] `PUT /admin/templates/{id}` (Update) - permission: `template:update`
        *   [x] `DELETE /admin/templates/{id}` (Delete) - permission: `template:delete`
        *   [x] `GET /admin/templates/{id}/versions` (List versions) - permission: `template:read_versions`
    *   [x] All endpoints use RLS via `clerk_org_id`.
*   **Database:**
    *   [x] Verify `templates`, `template_versions` table schemas and RLS policies are adequate (e.g., [`alembic/versions/20250510_203201_update_templates_rls_for_clerk.py`](alembic/versions/20250510_203201_update_templates_rls_for_clerk.py)).
*   **Clerk Configuration:** [x] None specific.
*   **Dependencies:** Epic 1.1, Core Permissions Model.

**Risks for Phase 2:**
*   [ ] Complexity of `WorkflowBuilder` and `TemplateEditor` components, even in their basic forms.
*   [/] Ensuring robust data validation for workflow and template structures.

### Phase 3: Integrations & System Administration Basics

**Goal:** Enable API integration management and provide foundational tools for system administrators.

**Tasks/Epics:**

**Epic 3.1: API Integration Management (Basic CRUD)**

*   **Feature:** API Integration Management
*   **Frontend:**
    *   Implement routes under `coherence-admin/src/app/admin/integrations/`.
    *   List Integrations (`page.tsx`). Protected by `integration:read`.
    *   Import OpenAPI (`import/page.tsx`). Protected by `integration:create`.
    *   View Integration Detail (`[id]/page.tsx`). Protected by `integration:read`.
    *   Basic credential management UI (e.g., fields for API keys, OAuth client IDs - actual secure storage is backend). Protected by `integration:manage_credentials`.
*   **Backend:**
    *   Create API endpoints for integrations (as per PRD [`docs/prds/new-admin-site.md:352-366`](docs/prds/new-admin-site.md:352-366)):
        *   `GET /admin/integrations/integrations` (List) - permission: `integration:read`
        *   `POST /admin/integrations/import` (Import OpenAPI) - permission: `integration:create`
        *   `GET /admin/integrations/integrations/{id}` (Get) - permission: `integration:read`
        *   `GET /admin/credentials/{integration_id}` (List credentials - metadata only) - permission: `integration:manage_credentials`
        *   `PUT /admin/credentials/{integration_id}/{type}` (Store credentials) - permission: `integration:manage_credentials`
    *   RLS on integration tables (e.g., [`alembic/versions/20250510_203523_add_rls_to_integration_tables.py`](alembic/versions/20250510_203523_add_rls_to_integration_tables.py)).
    *   Logic for parsing OpenAPI specs and storing integration details.
    *   Secure credential storage mechanism (e.g., HashiCorp Vault, or encrypted in DB - needs careful design beyond Clerk).
*   **Database:**
    *   Verify `integrations`, `credentials` table schemas and RLS.
*   **Clerk Configuration:** None specific.
*   **Dependencies:** Epic 1.1, Core Permissions Model.

**Epic 3.2: Tenant Management (System Admin View)**

*   **Feature:** System-Level Tenant Management
*   **Frontend:**
    *   `coherence-admin/src/app/admin/tenants/page.tsx` (for system admins).
    *   List all tenants. Protected by `tenant:list_all` (System Admin only).
    *   Link to individual tenant dashboards.
    *   Basic tenant creation/editing form (linking to Clerk `org_id`). Protected by `tenant:create` / `tenant:edit`.
*   **Backend:**
    *   Enhance `GET /admin/tenants` to list all tenants if user is `is_system_admin` and has `tenant:list_all`.
    *   `POST /admin/tenants` (Create Tenant) - permission: `tenant:create` (System Admin).
    *   `PUT /admin/tenants/{id}` (Update Tenant) - permission: `tenant:edit` (System Admin).
    *   Logic for associating Coherence Tenant with Clerk `org_id`.
*   **Database:** No changes (relies on existing `tenants` table and RLS allowing sysadmin bypass).
*   **Clerk Configuration:** System admin identification.
*   **Dependencies:** Epic 1.1, Core Permissions Model.

**Epic 3.3: System Health & Settings (Basic Read-Only for System Admins)**

*   **Feature:** System Administration (Health & Settings)
*   **Frontend:**
    *   Routes `admin/system/health/` and `admin/system/settings/`.
    *   Display basic system health indicators. Protected by `system_health:view`.
    *   Display global settings (read-only). Protected by `system_settings:view`.
*   **Backend:**
    *   API endpoint for system health (e.g., `GET /admin/system/health`). Permission: `system_health:view`.
    *   API endpoint for global settings (e.g., `GET /admin/system/settings`). Permission: `system_settings:view`.
*   **Database:** May need new tables for global settings if not already present.
*   **Clerk Configuration:** System admin identification.
*   **Dependencies:** Epic 1.1, Core Permissions Model.

**Risks for Phase 3:**
*   Secure credential storage for API integrations is critical and complex.
*   Defining the exact scope and operations for system admin tenant management.

### Phase 4: Advanced Features & Polish

**Goal:** Implement advanced workflow capabilities, testing, analytics, and refine the user experience.

**Tasks/Epics:** (To be detailed further once core is stable)

*   **Epic 4.1: Advanced Workflow Builder Features**
    *   Full interactivity for all node types (Action, Condition, Output, Integration).
    *   Step, Condition, Output editors.
*   **Epic 4.2: Workflow Testing Playground**
    *   Chat simulator, test history, run tests.
    *   PRD: [`docs/prds/new-admin-site.md:275`](docs/prds/new-admin-site.md:275)
*   **Epic 4.3: Analytics & Monitoring Views**
    *   Tenant-specific analytics.
    *   System-wide analytics for admins.
    *   PRD: [`docs/prds/new-admin-site.md:754`](docs/prds/new-admin-site.md:754)
*   **Epic 4.4: API Key Management (Org Admin)**
    *   UI for `org_admin` to manage their API keys.
    *   Backend endpoints for CRUD of `OrganizationAPIKey` scoped by `clerk_org_id`.
    *   RLS on `organization_api_keys` table ([`alembic/versions/20250510_202911_add_rls_to_organization_api_keys.py`](alembic/versions/20250510_202911_add_rls_to_organization_api_keys.py)).
    *   Permissions: `apikey:create`, `apikey:read`, `apikey:delete`.
*   **Epic 4.5: Audit Logs Viewer**
    *   UI for viewing audit logs, filterable by `clerk_org_id`.
    *   Backend endpoint to query `audit_log` table with RLS.
    *   Permissions: `auditlog:view`.
*   **Epic 4.6: UI/UX Polish & Responsive Design**
    *   Thorough testing across devices.
    *   Address UI inconsistencies.
*   **Epic 4.7: Full System Admin Capabilities**
    *   Editable global settings.
    *   Advanced system monitoring dashboards.

**Risks for Phase 4:**
*   Performance of complex UI components like the full Workflow Builder.
*   Data volume and query performance for analytics and audit logs.

## 4. General Considerations (Across all Phases)

*   **Error Handling:** Robust error handling on both frontend (e.g., toast notifications, error boundaries) and backend (standardized error responses).
*   **Loading States:** Consistent use of loading indicators for asynchronous operations.
*   **Accessibility (a11y):** Adhere to WCAG 2.1 AA guidelines.
*   **Testing:** Unit and integration tests for frontend components and backend APIs.
*   **Documentation:** Update API documentation (OpenAPI spec) for new admin endpoints. Inline code comments.

## 5. Proposed Phasing/Prioritization Summary

1.  **Core Permissions Model Implementation (Backend)** - Foundational, must be first.
2.  **Phase 1: Foundation & Core Tenant/User Experience** - Get the basic site up and users authenticated.
3.  **Phase 2: Core Workflow & Template Management** - Deliver core value proposition.
4.  **Phase 3: Integrations & System Administration Basics** - Expand capabilities and admin oversight.
5.  **Phase 4: Advanced Features & Polish** - Enhance and refine.

This plan provides a structured approach to developing the Awesome Admin Site. Each phase builds upon the previous, allowing for iterative development and feedback.