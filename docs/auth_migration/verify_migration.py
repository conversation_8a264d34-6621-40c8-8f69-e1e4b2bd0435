#!/usr/bin/env python3
"""
<PERSON>ript to verify auth migration and find any remaining deprecated auth usage
"""

import re
from pathlib import Path

# Deprecated functions to search for
DEPRECATED_FUNCTIONS = [
    'get_tenant_from_api_key',
    'check_admin_role',
    'get_current_tenant_id',
    'system_admin_context',
]

# New functions that should be used instead
NEW_FUNCTIONS = [
    'get_clerk_auth_details',
    'check_is_system_admin',
    'check_clerk_org_admin',
    'RequirePermission',
]

def scan_file(file_path):
    """Scan a Python file for deprecated auth functions"""
    results = {}
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        for func in DEPRECATED_FUNCTIONS:
            # Look for function usage (not just imports)
            pattern = rf'{func}\s*\('
            matches = re.findall(pattern, content)
            if matches:
                results[func] = len(matches)
                
        # Also check for imports of deprecated functions
        import_pattern = r'from.*auth import.*({})'.format('|'.join(DEPRECATED_FUNCTIONS))
        import_matches = re.findall(import_pattern, content)
        if import_matches:
            results['imports'] = import_matches
            
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return results

def main():
    # Scan the endpoints directory
    endpoints_dir = Path("src/coherence/api/v1/endpoints")
    
    print("=== Auth Migration Verification ===\n")
    print("Scanning for deprecated auth function usage...\n")
    
    deprecated_found = False
    
    for py_file in endpoints_dir.glob("*.py"):
        if py_file.name.startswith("__"):
            continue
            
        results = scan_file(py_file)
        if results:
            deprecated_found = True
            print(f"❌ {py_file}:")
            for func, count in results.items():
                if func == 'imports':
                    print(f"   - Imports: {count}")
                else:
                    print(f"   - {func}: {count} usage(s)")
            print()
    
    if not deprecated_found:
        print("✅ No deprecated auth functions found in endpoints!")
        print("\nVerifying updated files contain new auth patterns...")
        
        # Check that our updated files use the new auth
        updated_files = [
            "admin.py",
            "templates.py", 
            "credentials.py"
        ]
        
        for file_name in updated_files:
            file_path = endpoints_dir / file_name
            if file_path.exists():
                with open(file_path, 'r') as f:
                    content = f.read()
                    
                has_new_auth = any(func in content for func in NEW_FUNCTIONS)
                if has_new_auth:
                    print(f"✅ {file_name} - Uses new auth system")
                else:
                    print(f"⚠️  {file_name} - May need verification")
    
    print("\n=== Recommendations ===")
    print("1. Run the application and check logs for deprecation warnings")
    print("2. Test the /v1/admin/templates endpoint")
    print("3. Verify admin dashboard functionality")
    print("4. Test template operations with both admin types")
    
    if deprecated_found:
        print("\n⚠️  Action required: Update the files listed above to use new auth system")
    else:
        print("\n✅ Migration appears complete! Run tests to verify functionality.")

if __name__ == "__main__":
    main()
