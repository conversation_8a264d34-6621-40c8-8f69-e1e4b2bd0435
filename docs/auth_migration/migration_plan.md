# Auth Migration Plan

## Current Issue
The templates page and other admin endpoints are using deprecated authentication functions and getting 401 errors when accessed via the admin UI.

## Deprecated Functions to Replace

1. **`get_tenant_from_api_key`** - Uses old API key system
2. **`check_admin_role`** - Uses tenant.settings.is_admin flag
3. **`system_admin_context`** - Deprecated system admin check
4. **`get_current_tenant_id`** - Returns tenant ID from deprecated auth

## New Functions to Use

1. **`get_clerk_auth_details`** - New Clerk JWT authentication
2. **`check_is_system_admin`** - Check if user is system admin
3. **`check_clerk_org_admin`** - Check if user is org admin
4. **`RequirePermission`** - Permission-based access control
5. **`get_org_api_key_principal`** - For organization API keys

## Files to Update

### 1. Admin Endpoints (`/src/coherence/api/v1/endpoints/admin.py`)
**Current Issues:**
- Using `get_tenant_from_api_key`
- Using `check_admin_role`
- Routes include `/admin/templates` which redirects incorrectly

**Required Changes:**
- Replace with `get_clerk_auth_details` + permission checks
- Use `RequirePermission` for granular permissions
- Ensure proper route configuration

### 2. Templates Issue (Routing)
The issue in the logs shows:
- `GET /v1/admin/templates` returns 307 redirect
- `GET /v1/admin/templates/` returns 401 with deprecated auth

**Root Cause:**
The admin router is included in two places:
1. In api_router with prefix `/admin/templates`
2. In main.py with prefix `/api`

This creates conflicting routes. The actual templates endpoints are in `admin_templates.py` which uses modern auth.

### 3. Tenant Routes (Partially Fixed)
Some endpoints in tenants.py still use deprecated auth for legacy API key management.

## Migration Strategy

1. **Fix Route Configuration**
   - Remove duplicate admin router inclusion
   - Ensure templates route correctly to `admin_templates.py`

2. **Update Admin Routes**
   - Replace all deprecated auth with new Clerk auth
   - Add proper permission checks

3. **Clean Up Dependencies**
   - Remove unused imports of deprecated functions
   - Update dependencies to use new auth patterns

## Implementation Steps

1. First, fix the routing issue causing the 401 errors
2. Update admin.py to use new auth
3. Verify all endpoints work correctly
4. Remove deprecated function calls from other files
