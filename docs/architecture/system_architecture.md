# Coherence System Architecture

## Overview

Coherence employs a multi-tier architecture for intent recognition and processing, designed for speed, accuracy, and scalability. This document provides a high-level overview of the system's key components and how they interact.

## Core Architecture

```
┌─────────────────────────────────────────────────────────────────────┐
│                              Client                                  │
└───────────────────────────────┬─────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                           Chat Gateway                               │
└───────────────────────────────┬─────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                        Coherence Middleware                          │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐  │
│  │   TIER 1: 100ms │    │   TIER 2: 300ms │    │   TIER 3: 500ms │  │
│  │                 │    │                 │    │                 │  │
│  │  Vector Matcher │    │    Local LLM    │    │ RAG Augmentation│  │
│  │    (Qdrant)     │◄───┤  Intent Router  │◄───┤  (Full Context) │  │
│  │                 │    │   (Mistral 7B)  │    │    (OpenAI)     │  │
│  └────────┬────────┘    └────────┬────────┘    └────────┬────────┘  │
│           │                      │                      │           │
│           └──────────────────────┼──────────────────────┘           │
│                                  │                                  │
│                                  ▼                                  │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │                 Parameter Completion Service                 │   │
│  │                  (Conversational, LLM-guided)                │   │
│  └──────────────────────────────┬──────────────────────────────┘   │
│                                  │                                  │
│                                  ▼                                  │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │                       Action Registry                        │   │
│  │    ┌───────────────┐  ┌────────────────┐  ┌─────────────┐   │   │
│  │    │ Sync Actions  │  │ Async Workflows│  │External APIs│   │   │
│  │    └───────────────┘  └────────────────┘  └─────────────┘   │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

## Key Components

### Intent Recognition Pipeline

The heart of Coherence is a three-tiered intent recognition system:

1. **Vector Matcher (Tier 1)**
   - Uses vector embeddings for fast intent matching (<100ms)
   - Implemented with Qdrant vector database
   - Tenant-isolated vector collections
   - High precision for common intents

2. **Local LLM Intent Router (Tier 2)**
   - Activated when Tier 1 confidence is below threshold
   - Uses smaller models like Mistral 7B
   - Handles more complex intent recognition
   - Response time target: <300ms

3. **RAG Augmentation (Tier 3)**
   - For novel or ambiguous requests
   - Retrieves relevant context from knowledge base
   - Uses more powerful models like OpenAI's GPT-4
   - Fallback for the most challenging requests
   - Response time target: <500ms

### Parameter Completion Service

Once an intent is identified, the Parameter Completion Service:

- Extracts parameters from user input
- Validates parameters against schema
- Requests missing parameters conversationally
- Maintains conversation state
- Uses Star Trek-like interface for natural interaction

### Template Management System

The Template Management System supports customization of system behavior:

- Templates with inheritance hierarchy (global → pack → tenant)
- Five template categories: Intent Router, Parameter Completion, Retrieval, Response Generation, and Error Handling
- Version control with history and rollback
- Jinja2-based templating engine
- Template testing and validation

### OpenAPI Integration

Automatic connection to external services:

- Parse and validate OpenAPI specifications
- Map API operations to intents and actions
- Generate action classes from endpoints
- Secure credential management
- Rate limiting and quota enforcement

### Action Registry

Executes actions based on resolved intents:

- Synchronous actions for immediate responses
- Asynchronous workflows for complex operations
- External API integrations
- Error handling and retry logic

### Multi-Tenant Architecture

Comprehensive isolation between tenants:

- Row-Level Security (RLS) in database
  - PostgreSQL session variables for tenant context
  - RLS policies on all tenant-related tables
  - Proper NULL and boolean handling in session variables
- Tenant-specific vector collections
- Tiered API key authentication
  - Regular API keys for standard access
  - Admin API keys for tenant administration
- Isolated template overrides
- Usage tracking and billing

## Database Schema

The core data models include:

- **Tenants**: Customer organizations with isolated data
- **Templates**: Configurable behavior definitions with inheritance
- **API Integrations**: External service connections
- **Intents**: Mappings from natural language to actions

## API Endpoints

The main API interface consists of:

- `/v1/resolve`: Process a natural language message
- `/v1/continue`: Continue a conversation with more information
- `/v1/status/{workflow_id}`: Check status of async workflows
- `/admin/*`: Management endpoints for templates, integrations, etc.

## Deployment Architecture

The system is designed for containerized deployment:

- FastAPI application
- PostgreSQL database with RLS
- Redis for caching and message queues
- Qdrant for vector storage
- Prometheus and Grafana for monitoring

## Security Architecture

Enterprise-grade security is built into all layers:

- API key authentication
  - Regular keys for standard API access
  - Admin keys for tenant administration
  - System admin keys for cross-tenant operations
- Encrypted credential storage
- Row-Level Security (RLS) for data isolation
  - Function-based RLS policies
  - Database-level tenant isolation
  - Proper session variable handling
- Token validation, refresh, and revocation for OAuth2
- Circuit breakers for external service resilience
- Retry mechanisms with backoff for API calls
- Comprehensive audit logging

## Further Reading

For more detailed information on specific components, refer to:
- [Master PRD document](/docs/prds/master_prd.md)
- [Implementation plans](/docs/implementation_plans/implementation_plan_with_existing_code.md)
- [PostgreSQL Session Variables Guide](/docs/guides/postgresql_session_variables.md)
- [Error Handling Guide](/docs/guides/error_handling.md)
- [Monitoring Guide](/docs/guides/monitoring.md)
- [Template System Guide](/docs/guides/template_system.md)