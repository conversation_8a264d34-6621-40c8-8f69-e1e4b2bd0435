# Proposed High-Level Architecture: Coherence Admin Site

## 1. Introduction

This document outlines the proposed high-level architecture for the new Coherence Admin Site. It builds upon the existing PRD ([`docs/prds/new-admin-site.md`](docs/prds/new-admin-site.md)) and the Clerk integration analysis ([`docs/analysis/clerk_admin_site_analysis.md`](docs/analysis/clerk_admin_site_analysis.md)). The primary goal is to establish a robust, scalable, and secure foundation for the admin site, with a clear permissions model.

## 2. Role of `coherence-admin/` Directory

The new admin site will be built **within the existing [`coherence-admin/`](coherence-admin/) directory**. This directory will be significantly expanded from its current minimal Next.js setup to accommodate the full feature set and structure outlined in the PRD ([`docs/prds/new-admin-site.md:14`](docs/prds/new-admin-site.md:14)). The existing `pnpm-lock.yaml`, Next.js configuration files (`next.config.ts`, `postcss.config.mjs`, `tailwind.config.ts`), and TypeScript setup (`tsconfig.json`) will serve as the base for this expansion.

## 3. Frontend Architecture

*   **Technology Stack:**
    *   **Framework:** Next.js 15 (App Router) - Confirmed from PRD ([`docs/prds/new-admin-site.md:102`](docs/prds/new-admin-site.md:102)).
    *   **Language:** TypeScript - Confirmed from PRD ([`docs/prds/new-admin-site.md:104`](docs/prds/new-admin-site.md:104)).
    *   **UI Components:** React 19 with shadcn/ui - Confirmed from PRD ([`docs/prds/new-admin-site.md:103`](docs/prds/new-admin-site.md:103), [`docs/prds/new-admin-site.md:105`](docs/prds/new-admin-site.md:105)).
    *   **Styling:** Tailwind CSS - Confirmed from PRD ([`docs/prds/new-admin-site.md:105`](docs/prds/new-admin-site.md:105)).
    *   **State Management:** Zustand + React Query (TanStack Query) - Confirmed from PRD ([`docs/prds/new-admin-site.md:106`](docs/prds/new-admin-site.md:106)).
    *   **Forms:** React Hook Form + Zod for validation - Confirmed from PRD ([`docs/prds/new-admin-site.md:109`](docs/prds/new-admin-site.md:109)).
    *   **API Client:** Axios, with a structured API client as per PRD ([`docs/prds/new-admin-site.md:315`](docs/prds/new-admin-site.md:315)).
    *   **Visualization:** React Flow (or `react-flow-renderer`) and Recharts - Confirmed from PRD ([`docs/prds/new-admin-site.md:108`](docs/prds/new-admin-site.md:108)).

*   **Key Frontend Components/Modules:**
    *   The frontend will follow the detailed project structure outlined in the PRD ([`docs/prds/new-admin-site.md:14-95`](docs/prds/new-admin-site.md:14-95)). This includes:
        *   `src/app/admin/`: Main container for all admin routes and features.
            *   `layout.tsx`: Admin shell, including navigation and Clerk-based auth protection.
            *   Feature-specific directories: `workflows/`, `integrations/`, `templates/`, `tenants/`, `system/` etc.
        *   `src/components/`: Reusable UI components, including those from shadcn/ui, and feature-specific components (e.g., `WorkflowBuilder.tsx`, `TemplateEditor.tsx`).
        *   `src/lib/`: Utilities, API client, custom hooks (`useWorkflows.ts`, etc.), Zustand stores (`useAuthStore.ts`, etc.), and TypeScript types.
        *   The PRD provides excellent examples for `WorkflowBuilder` ([`docs/prds/new-admin-site.md:175`](docs/prds/new-admin-site.md:175)), `TemplateEditor` ([`docs/prds/new-admin-site.md:210`](docs/prds/new-admin-site.md:210)), etc.

*   **Clerk Integration (Frontend):**
    *   **Authentication UI:** Utilize Clerk's Next.js SDK (`@clerk/nextjs`) for pre-built UI components for login, sign-up, user profile, and organization management.
    *   **Session Management:**
        *   Clerk's `auth()` middleware in [`src/middleware.ts`](coherence-admin/src/middleware.ts) will protect admin routes.
        *   The `AdminSessionContext` ([`coherence-admin/src/context/AdminSessionContext.tsx`](coherence-admin/src/context/AdminSessionContext.tsx)) and the `useAdminSession` hook (as defined in PRD [`docs/prds/new-admin-site.md:144`](docs/prds/new-admin-site.md:144)) will fetch and provide session details including `clerkUserId`, `tenantId` (derived from `clerk_org_id`), `isSystemAdmin` status, and an array of `permissions`.
    *   **API Client:** The API client ([`coherence-admin/src/lib/apiClient.ts`](coherence-admin/src/lib/apiClient.ts)) will automatically include the Clerk JWT in the `Authorization` header for all backend requests. It will also send the `X-Tenant-ID` header, populated with the Coherence `tenant.id` that corresponds to the active `clerk_org_id`.
    *   **Role-Based UI:** Components like `ProtectedRoute` (PRD [`docs/prds/new-admin-site.md:158`](docs/prds/new-admin-site.md:158)) will use the `permissions` array from `AdminSession` to control access to UI elements and features.

## 4. Backend Architecture

*   **API Services/Endpoints:**
    *   The existing FastAPI backend structure in [`src/coherence/api/v1/endpoints/`](src/coherence/api/v1/endpoints/) will be **extended** to support the new admin site functionalities.
    *   New routers and endpoints will be added under an `/admin` prefix (e.g., `/admin/workflows`, `/admin/tenants`) to mirror the frontend structure and API client definitions in the PRD ([`docs/prds/new-admin-site.md:331-368`](docs/prds/new-admin-site.md:331-368)).
    *   For example, new endpoints will be needed for:
        *   CRUD operations for workflows, templates, integrations.
        *   Tenant management (listing, details).
        *   System health and settings.
        *   Fetching user-specific permissions for the `AdminSession`.

*   **Permission Enforcement (Backend):**
    *   **Authentication:** All admin API endpoints will be protected by the existing [`get_clerk_auth_details`](src/coherence/api/v1/dependencies/auth.py:45) dependency, which validates the Clerk JWT and extracts user/organization details into `request.state`.
    *   **Data Scoping (RLS):** Row Level Security policies, primarily based on `app.current_clerk_org_id` (set from `request.state.clerk_org_id`) and `app.is_system_admin` (set from `request.state.is_system_admin`), will continue to ensure data isolation at the database level. This is already well-established as per the Clerk analysis document.
    *   **Action-Specific Permissions:**
        *   Beyond RLS, API endpoints will implement checks for specific permissions. This will involve:
            *   A new dependency or utility function that takes the required permission(s) for an action (e.g., `"workflow:create"`, `"tenant:list_all"`).
            *   This function will check against the user's effective permissions derived from their Clerk roles (standard and custom) and `is_system_admin` status.
            *   Existing checks like [`check_clerk_org_admin`](src/coherence/api/v1/dependencies/auth.py:315) and [`check_is_system_admin`](src/coherence/api/v1/dependencies/auth.py:336) will be augmented or integrated into this more comprehensive permission system.

## 5. Permissions Model

This model addresses the need for fine-grained permissions and system-level administrative roles.

*   **Foundation: Clerk Roles & Organization Context:**
    *   Standard Clerk organization roles (`admin`, `member`) provide a baseline. Users operate within the context of their selected Clerk organization (`clerk_org_id`).
    *   Data is primarily scoped to this `clerk_org_id` via RLS.

*   **Fine-Grained Permissions:**
    *   **Clerk Custom Permissions:** We will define custom permissions directly within Clerk for each organization. Examples:
        *   `workflow:create`, `workflow:read`, `workflow:update`, `workflow:delete`, `workflow:execute`
        *   `template:create`, `template:read`, `template:update`, `template:delete`
        *   `integration:create`, `integration:read`, `integration:update`, `integration:delete`
        *   `tenant:view_own_dashboard`
        *   `apikey:create`, `apikey:read`, `apikey:delete`
    *   **Clerk Custom Roles (Optional but Recommended):** To simplify management, create custom roles in Clerk (e.g., "Workflow Editor", "Integration Manager", "Tenant Viewer") and assign sets of these custom permissions to them. Users can then be assigned these custom roles in addition to Clerk's built-in `admin` or `member` roles.
    *   **`AdminSession.permissions` (Frontend):** On login or org switch, the frontend will call a dedicated backend endpoint (e.g., `/auth/session-info`) that resolves and returns all effective permissions for the user within their current `clerk_org_id`. This list populates the `permissions: Permission[]` array in the `AdminSession` object ([`docs/prds/new-admin-site.md:139`](docs/prds/new-admin-site.md:139)).
    *   **Backend Derivation of Permissions:** The backend endpoint responsible for providing session info will:
        1.  Get the user's Clerk ID and current `clerk_org_id` from the validated JWT.
        2.  Fetch the user's roles (standard and custom) and direct custom permissions from Clerk for that organization.
        3.  Compile a flat list of all granted permission strings.

*   **System Administrator Role:**
    *   **Identification:** A "System Administrator" will be identified by a custom claim in their Clerk JWT, such as `is_system_admin: true`. This aligns with the mock implementation noted in [`src/coherence/api/v1/dependencies/auth.py:79`](src/coherence/api/v1/dependencies/auth.py:79) and the `request.state.is_system_admin` flag.
        *   This claim can be added to specific users via Clerk's metadata or a custom hook during token generation (e.g., using Clerk's "Short-lived JWT templates" or a webhook).
    *   **Privileges:**
        *   System Admins bypass standard `clerk_org_id`-based RLS for read operations where appropriate (e.g., viewing all tenants, all workflows across all orgs). This is already partially supported by RLS policies checking `is_system_admin()`.
        *   They have access to dedicated system-level modules (`admin/system/health/`, `admin/system/monitoring/`, `admin/system/settings/`).
        *   They can manage global configurations, view system-wide analytics, and potentially manage tenant lifecycle.
    *   **Permissions:** System admins will implicitly have all fine-grained permissions or a specific set of system-level permissions (e.g., `system:*`, `tenant:list_all`, `tenant:create`).

## 6. Data Flow Example: Admin Edits a Workflow

```mermaid
sequenceDiagram
    participant User
    participant AdminFE (Next.js)
    participant Clerk
    participant AdminBE (FastAPI)
    participant Database (PostgreSQL)

    User->>AdminFE: Navigates to edit workflow 'WF123'
    AdminFE->>AdminFE: Loads WorkflowBuilder component
    Note over AdminFE: Clerk JWT already present from login

    AdminFE->>AdminBE: GET /admin/workflows/WF123 (Authorization: Bearer JWT, X-Tenant-ID: tenant_abc)
    AdminBE->>AdminBE: get_clerk_auth_details (validates JWT, extracts org_id, user_id, roles, is_system_admin claim)
    AdminBE->>AdminBE: Sets rls_clerk_org_id, is_system_admin in request.state
    AdminBE->>AdminBE: Permission Check: Does user have 'workflow:read' for this org_id OR is_system_admin?
    alt Has Permission
        AdminBE->>Database: Sets session variables (app.current_clerk_org_id, app.is_system_admin)
        Database->>AdminBE: SELECT * FROM workflows WHERE id='WF123' (RLS applied)
        AdminBE-->>AdminFE: Returns workflow data
        AdminFE->>User: Displays workflow data in editor
    else No Permission
        AdminBE-->>AdminFE: Returns 403 Forbidden
        AdminFE->>User: Shows access denied message
    end

    User->>AdminFE: Modifies workflow and clicks "Save"
    AdminFE->>AdminBE: PUT /admin/workflows/WF123 (Data, Auth: Bearer JWT, X-Tenant-ID: tenant_abc)
    AdminBE->>AdminBE: get_clerk_auth_details
    AdminBE->>AdminBE: Sets rls_clerk_org_id, is_system_admin in request.state
    AdminBE->>AdminBE: Permission Check: Does user have 'workflow:update' for this org_id OR is_system_admin?
    alt Has Permission
        AdminBE->>Database: Sets session variables
        Database->>AdminBE: UPDATE workflows SET ... WHERE id='WF123' (RLS applied)
        AdminBE-->>AdminFE: Returns success
        AdminFE->>User: Shows "Workflow saved" notification
    else No Permission
        AdminBE-->>AdminFE: Returns 403 Forbidden
        AdminFE->>User: Shows access denied message
    end
```

## 7. Summary

This architecture leverages the detailed frontend plan from the PRD and the existing robust Clerk integration for authentication and basic tenancy. The key additions are:

*   A clear strategy for **fine-grained permissions** using Clerk custom permissions/roles, reflected in the frontend `AdminSession`.
*   A defined method for identifying and empowering **System Administrators** via a custom JWT claim.
*   Extension of backend APIs under an `/admin` namespace with robust permission checks beyond RLS.
*   Confirmation that the new admin site will be an expansion of the current [`coherence-admin/`](coherence-admin/) Next.js project.

This approach provides a clear separation of concerns, builds on existing strengths, and addresses the critical need for a flexible and secure permissions model.