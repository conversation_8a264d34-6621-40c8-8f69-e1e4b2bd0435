# CRFS (Coherence Response Formatting System) Implementation Analysis

## Overview

CRFS (Coherence Response Formatting System) is a comprehensive response formatting framework that provides structured, flexible, and human-readable API response formatting. It serves as the bridge between raw API responses and user-friendly output in the Coherence platform.

## Core Components

### 1. Backend Implementation

#### CRFSFormatter (`src/coherence/template_system/crfs_formatter.py`)
The heart of CRFS implementation, providing:

- **Version Support**: Currently implements CRFS v2.0
- **Response Types**:
  - `raw`: Direct JSON output
  - `template`: Simple template string rendering
  - `structured`: Section-based formatting with headers, lists, objects, etc.

- **Key Features**:
  - Jinja2 template integration for dynamic content
  - Custom filters (json_pretty, currency, date formatting, etc.)
  - Safe property access with `.get()` method conversion
  - Special character handling (e.g., `@graph`, `@context`)
  - Error handling with fallback mechanisms
  - Conditional rendering based on data availability

#### Integration Points

1. **Unified Templates** (`src/coherence/template_system/unified_generator.py`)
   - Generates CRFS configurations automatically from OpenAPI specs
   - Creates response sections based on schema properties
   - Maps error codes to user-friendly messages

2. **Intent Pipeline** (`src/coherence/intent_pipeline/orchestrator.py`)
   - Uses CRFS formatter to render API responses
   - Handles multi-format responses with content negotiation
   - Applies error mapping for failed requests

3. **API Endpoints** (`src/coherence/api/v1/endpoints/unified_templates.py`)
   - Stores CRFS configurations in template `response_format` field
   - Provides CRUD operations for CRFS-enabled templates

### 2. Frontend Implementation

#### Response Types (TypeScript/React)
Located in `coherence-admin/src/components/chat/ResponseTypes/`:

- **AskResponse**: Interactive parameter collection
- **AsyncResponse**: Long-running operation status
- **ClarificationResponse**: Intent disambiguation
- **FallbackResponse**: Error/unknown intent handling

#### ChatMessage Component
- Renders different response types based on `responseType` field
- Applies cyberpunk-themed styling
- Supports accessibility features

### 3. CRFS Configuration Structure

```json
{
  "crfs_version": "2.0",
  "kind": "coherence-response",
  "data_path": "result",
  "format": {
    "type": "structured",
    "structure": {
      "header": "{{ operation_title }}",
      "sections": [
        {
          "id": "summary",
          "type": "text",
          "title": "Summary",
          "content": "{{ summary_text }}"
        },
        {
          "id": "data",
          "type": "list",
          "title": "Results",
          "data_path": "result.items",
          "item_format": {
            "template": "{{ item.name }}: {{ item.value }}",
            "style": "bullet"
          }
        }
      ]
    }
  },
  "error_mapping": {
    "404": "Resource not found",
    "401": "Authentication required"
  }
}
```

## How CRFS Works with Uni-Templates

### 1. Template Generation
When generating unified templates from OpenAPI:
- Response schemas are analyzed to determine appropriate CRFS sections
- Array properties become list sections
- Object properties become object or text sections
- Error responses are mapped to user-friendly messages

### 2. Response Formatting Flow
1. API response is received by the orchestrator
2. Template's `response_format` (CRFS config) is retrieved
3. CRFSFormatter processes the response:
   - Unwraps nested API response wrappers
   - Extracts data using configured paths
   - Renders sections based on type and visibility conditions
   - Applies Jinja2 templating for dynamic content
4. Formatted response is returned to the user

### 3. Multi-Format Support
CRFS supports content negotiation:
- Templates can define multiple response formats
- System selects format based on Accept headers
- Fallback to default format if preferred isn't available

## Key Features

### 1. Dynamic Data Extraction
- JSON-Path-lite notation for simple paths
- `eval()` support for complex expressions (e.g., `result.get('@graph')`)
- Safe property access with automatic `.get()` conversion

### 2. Conditional Rendering
- `show_if`/`hide_if` conditions for sections
- Visibility based on data availability
- Complex boolean expressions supported

### 3. Template Filters
- **Formatting**: json_pretty, upper, lower, title
- **Numbers**: number, currency, percentage
- **Dates**: date, datetime with custom formats
- **Math**: multiply, divide, add, subtract

### 4. Error Handling
- Graceful fallback for template errors
- Custom error templates
- Detailed error context for debugging

## Integration with Response Types

### 1. Reply (Standard Response)
- Uses CRFS to format API results
- Supports all section types
- Applies theme-consistent styling

### 2. Ask (Parameter Collection)
- CRFS can generate parameter prompts
- Integrates with UI field configurations
- Supports validation messages

### 3. Async (Long-Running Operations)
- CRFS formats status updates
- Progressive rendering of results
- Error state handling

### 4. Fallback (Error Handling)
- Uses error_mapping from CRFS config
- Renders user-friendly error messages
- Provides suggestions for recovery

## Best Practices

### 1. Template Design
- Use semantic section IDs
- Provide clear titles for sections
- Include conditionals for optional data
- Define comprehensive error mappings

### 2. Performance
- Minimize complex expressions in conditions
- Use data_path to limit data extraction
- Cache formatted responses when possible

### 3. User Experience
- Keep sections focused and concise
- Use appropriate section types (list, table, etc.)
- Provide helpful error messages
- Include examples in documentation

## Future Enhancements

### 1. Advanced Formatting
- Table section type with column definitions
- Chart/graph rendering support
- Interactive elements (collapsible sections)

### 2. Frontend Integration
- Rich CRFS renderer component
- Live preview in template editor
- Format switching (raw/formatted views)

### 3. Extended Features
- Custom filter registration
- Plugin system for section types
- Streaming response support
- Internationalization

## Testing Considerations

### 1. Unit Tests
- Test each section type rendering
- Verify conditional logic
- Validate error handling
- Check filter functionality

### 2. Integration Tests
- End-to-end response formatting
- Multi-format content negotiation
- Error mapping verification
- Performance benchmarks

### 3. Visual Tests
- Rendered output consistency
- Theme compliance
- Responsive design
- Accessibility standards

## Conclusion

CRFS is a powerful and flexible system that bridges the gap between raw API responses and user-friendly output. Its integration with the unified template system provides a comprehensive solution for API response formatting in the Coherence platform. The combination of backend flexibility and frontend rendering capabilities ensures that users receive clear, well-formatted responses regardless of the underlying API complexity.