# Analysis of "Awesome Admin Site" PRD and Clerk Integration

## 1. Introduction

This document analyzes the Product Requirements Document (PRD) for the new "Awesome Admin Site" in the context of the existing Clerk integration within the Coherence project. The primary goal is to understand how Clerk-based authentication, organization management via `clerk_org_id`, data scoping using Row Level Security (RLS), and user roles will support the specified features.

Key investigation areas include:
*   Clerk-based authentication mechanisms.
*   The role of `clerk_org_id` in organization management and tenancy.
*   Data scoping and isolation via RLS tied to `clerk_org_id`.
*   The application and potential extension of Clerk user roles for feature-level permissions.

## 2. Overview of Current Clerk Integration

### Authentication Flow

The authentication flow leverages Clerk for user and organization management:

1.  **Client-Side (Next.js Frontend - `coherence-admin/`):**
    *   Users will interact with the Clerk SDK for login, sign-up, and session management within the Next.js application.
    *   The PRD ([`docs/prds/new-admin-site.md:107`](docs/prds/new-admin-site.md:107)) specifies "<PERSON>" as the auth provider.
    *   Files like [`coherence-admin/src/middleware.ts`](coherence-admin/src/middleware.ts) and [`coherence-admin/src/context/AdminSessionContext.tsx`](coherence-admin/src/context/AdminSessionContext.tsx) will likely handle Clerk session state and route protection.
    *   The PRD's `AdminSession` interface ([`docs/prds/new-admin-site.md:134`](docs/prds/new-admin-site.md:134)) suggests fetching session details, including `clerkUserId` and `tenantId` (derived from Clerk org).

2.  **Backend (FastAPI):**
    *   Authenticated requests from the frontend will include a Clerk JWT in the `Authorization: Bearer <token>` header.
    *   The [`get_clerk_auth_details`](src/coherence/api/v1/dependencies/auth.py:45) dependency in [`src/coherence/api/v1/dependencies/auth.py`](src/coherence/api/v1/dependencies/auth.py) validates this JWT.
    *   Upon successful validation, Clerk claims such as `sub` (user_id), `org_id`, `org_role`, and `org_slug` are extracted.
    *   These claims are stored in `request.state` (e.g., `request.state.clerk_user_id`, `request.state.clerk_org_id`, `request.state.clerk_org_role`).
    *   Crucially, `request.state.rls_clerk_org_id` is set to the `org_id` from the token.

3.  **PostgreSQL Session Context for RLS:**
    *   A FastAPI middleware (assumed to be [`src/coherence/middleware/tenant_context.py`](src/coherence/middleware/tenant_context.py)) is responsible for taking values from `request.state` (like `rls_clerk_org_id` and `is_system_admin`) and setting them as PostgreSQL session variables (e.g., `app.current_clerk_org_id`, `app.is_system_admin`).
    *   These session variables are then used by RLS policies, as seen in migrations like [`alembic/versions/20250510_202911_add_rls_to_organization_api_keys.py`](alembic/versions/20250510_202911_add_rls_to_organization_api_keys.py).

### Organization Management & Tenancy Model

*   The Coherence `Tenant` model ([`src/coherence/models/tenant.py`](src/coherence/models/tenant.py)) features a `clerk_org_id: Mapped[Optional[str]]` field ([`src/coherence/models/tenant.py:35`](src/coherence/models/tenant.py:35)). This field, added by migration [`alembic/versions/20250509_180645_add_clerk_org_id.py`](alembic/versions/20250509_180645_add_clerk_org_id.py), directly links a Coherence `Tenant` to a Clerk organization.
*   RLS policies heavily rely on `clerk_org_id` for data isolation. For instance:
    *   The `tenants` table RLS policy ([`alembic/versions/20250510_203008_update_tenants_rls_for_clerk_org_id.py`](alembic/versions/20250510_203008_update_tenants_rls_for_clerk_org_id.py)) allows selection if `clerk_org_id = current_session_clerk_org_id()`.
    *   The `organization_api_keys` table RLS policies ([`alembic/versions/20250510_202911_add_rls_to_organization_api_keys.py`](alembic/versions/20250510_202911_add_rls_to_organization_api_keys.py)) use `(clerk_org_id = current_session_clerk_org_id()) OR (is_system_admin())`.
*   This consistent use of `clerk_org_id` in RLS policies across various tables ensures data scoping.

### User Roles & Permissions (Current Implementation)

*   **Clerk Roles:** Backend auth ([`src/coherence/api/v1/dependencies/auth.py`](src/coherence/api/v1/dependencies/auth.py)) anticipates Clerk roles like `admin`, `member`, and a mock `system_admin` (potentially via custom claim [`src/coherence/api/v1/dependencies/auth.py:79`](src/coherence/api/v1/dependencies/auth.py:79)).
*   **Backend Authorization Checks:**
    *   [`check_clerk_org_admin`](src/coherence/api/v1/dependencies/auth.py:315) verifies `org_role` ("admin" or "owner").
    *   [`check_is_system_admin`](src/coherence/api/v1/dependencies/auth.py:336) verifies the `is_system_admin` flag.
*   **PRD `AdminSession` & Permissions:** The PRD ([`docs/prds/new-admin-site.md:134`](docs/prds/new-admin-site.md:134)) `AdminSession` includes `permissions: Permission[]`. How these are derived (from Clerk roles or a separate Coherence system) needs clarification.
*   The PRD's `ProtectedRoute` ([`docs/prds/new-admin-site.md:158`](docs/prds/new-admin-site.md:158)) uses `requiredRole`, but `hasPermission` logic is not detailed.

## 3. PRD Feature Analysis (Clerk Integration Context)

**Common Principles for All Features:**
*   **Authentication:** All features protected by Clerk. Next.js middleware for routes, backend API calls require Clerk JWT verified by [`get_clerk_auth_details`](src/coherence/api/v1/dependencies/auth.py:45).
*   **Data Scoping:** Data scoped to user's `clerk_org_id` via RLS, unless accessed by a "system_admin".

### 3.1. User Management

*   **Authentication:** Secured by Clerk.
*   **Data Scoping:** `org_admin` manages users within their `clerk_org_id`. System admins might have broader access.
*   **Clerk Roles:** `org_admin` (invite, remove, change roles in their org), `org_member` (read-only self-profile), `system_admin` (cross-org view/manage system admins).
*   **Constraints/Challenges:** Clarity on direct Clerk API interaction vs. local sync for user data. Scope of role management capabilities.

### 3.2. Tenant Management
*(PRD Path: `admin/tenants/`)*

*   **Authentication:** Secured by Clerk.
*   **Data Scoping:** Primarily for `system_admin` to list all Coherence `Tenant` records. `org_admin` access limited to own `Tenant` record (per RLS on `tenants` table).
*   **Clerk Roles:** `system_admin` (CRUD on `Tenant` entries, linking to Clerk `org_id`), `org_admin` (read-only own `Tenant` details, view own tenant dashboard).
*   **Constraints/Challenges:** Tenant creation flow and Clerk `org_id` association. Backfill for pre-existing tenants (addressed by migration [`alembic/versions/20250510_201654_populate_existing_tenants_clerk_org_id.py`](alembic/versions/20250510_201654_populate_existing_tenants_clerk_org_id.py)).

### 3.3. API Key Management

*   **Authentication:** Secured by Clerk.
*   **Data Scoping:** `OrganizationAPIKey` model ([`src/coherence/models/tenant.py:141`](src/coherence/models/tenant.py:141)) tied to `clerk_org_id`. RLS ([`alembic/versions/20250510_202911_add_rls_to_organization_api_keys.py`](alembic/versions/20250510_202911_add_rls_to_organization_api_keys.py)) ensures users manage keys for their `clerk_org_id`. System admins have broader access.
*   **Clerk Roles:** `org_admin` (CRUD on own org's API keys), `org_member` (likely no access), `system_admin` (manage any org's keys).
*   **Constraints/Challenges:** Definition and management of `permissions` JSONB field on `OrganizationAPIKey`.

### 3.4. Workflow Management
*(PRD Path: `admin/workflows/`, includes Workflow Templates)*

*   **Authentication:** Secured by Clerk.
*   **Data Scoping:** Workflows/templates scoped by `clerk_org_id` via RLS (e.g., migrations [`alembic/versions/20250510_203201_update_templates_rls_for_clerk.py`](alembic/versions/20250510_203201_update_templates_rls_for_clerk.py), [`alembic/versions/20250510_203256_add_rls_to_template_children_tables.py`](alembic/versions/20250510_203256_add_rls_to_template_children_tables.py)).
*   **Clerk Roles:** `org_admin` (full CRUD in own org), `org_member` (read-only/execute based on granular permissions if defined), `system_admin` (view all, manage global/system templates).
*   **Constraints/Challenges:** Access control for shared/marketplace templates.

### 3.5. API Integrations Management
*(PRD Path: `admin/integrations/`)*

*   **Authentication:** Secured by Clerk.
*   **Data Scoping:** Integrations/credentials scoped by `clerk_org_id` via RLS (e.g., migration [`alembic/versions/20250510_203523_add_rls_to_integration_tables.py`](alembic/versions/20250510_203523_add_rls_to_integration_tables.py)).
*   **Clerk Roles:** `org_admin` (configure, manage credentials in own org), `org_member` (read-only/no access), `system_admin` (manage global types, oversee all).
*   **Constraints/Challenges:** Secure storage of third-party API credentials (independent of Clerk).

### 3.6. Template Management (General Content Templates)
*(PRD Path: `admin/templates/`)*

*   **Authentication:** Secured by Clerk.
*   **Data Scoping:** Scoped by `clerk_org_id` via RLS (similar to workflow templates).
*   **Clerk Roles:** `org_admin` (full CRUD in own org), `org_member` (read-only/use), `system_admin` (manage global library, view all).
*   **Constraints/Challenges:** Managing global vs. org-specific templates.

### 3.7. Analytics / Monitoring
*(PRD Paths: `admin/system/monitoring/`, `admin/testing/analytics/`)*

*   **Authentication:** Secured by Clerk.
*   **Data Scoping:** Analytics data strictly scoped by `clerk_org_id`. Underlying data sources must be tagged and subject to RLS.
*   **Clerk Roles:** `org_admin` (view own org's analytics), `system_admin` (system-wide or any org's analytics).
*   **Constraints/Challenges:** Ensuring all data points for analytics are associated with `clerk_org_id`.

### 3.8. Audit Logs

*   **Authentication:** Secured by Clerk.
*   **Data Scoping:** Audit logs queryable by `clerk_org_id`. `AuditLog` model needs `clerk_org_id` and RLS. (Migration [`alembic/versions/20250510_203433_add_rls_to_audit_log.py`](alembic/versions/20250510_203433_add_rls_to_audit_log.py) likely handles this).
*   **Clerk Roles:** `org_admin` (view own org's logs), `system_admin` (view all logs or system-level events).
*   **Constraints/Challenges:** Capturing `clerk_org_id` and `clerk_user_id` for all auditable actions.

### 3.9. System Administration (Health, Global Settings)
*(PRD Paths: `admin/system/health/`, `admin/system/settings/`)*

*   **Authentication:** Secured by Clerk; primarily for `system_admin`.
*   **Data Scoping:** Not organization-specific. Access controlled by `system_admin` role/claim. RLS might allow access if `is_system_admin()` is true.
*   **Clerk Roles:** `system_admin` (full access), `org_admin`/`org_member` (no access).
*   **Constraints/Challenges:** Robust definition and identification of "system_admin" in Clerk.

## 4. Client-Side Clerk Integration Insights (`coherence-admin/`)

*   **Clerk SDK Usage:** Next.js frontend will use Clerk SDKs (e.g., `@clerk/nextjs`) for UI components, session management, and hooks.
*   **Middleware:** [`coherence-admin/src/middleware.ts`](coherence-admin/src/middleware.ts) will use Clerk middleware for route protection.
*   **Session Context:** [`coherence-admin/src/context/AdminSessionContext.tsx`](coherence-admin/src/context/AdminSessionContext.tsx) and `useAdminSession` hook ([`docs/prds/new-admin-site.md:144`](docs/prds/new-admin-site.md:144)) will manage Clerk session data and fetch app-specific session details.
*   **Authenticated API Client:** API client ([`coherence-admin/src/lib/apiClient.ts`](coherence-admin/src/lib/apiClient.ts), PRD [`docs/prds/new-admin-site.md:315`](docs/prds/new-admin-site.md:315)) will use Clerk JWT for backend calls. `X-Tenant-ID` header will likely use Coherence `tenant.id` corresponding to `clerk_org_id`.

## 5. Key Questions & Areas Requiring Clarification

1.  **Fine-Grained Permissions (`Permission[]`):** How are `Permission[]` in `AdminSession` defined, managed, and enforced? Are they derived from Clerk roles or a separate Coherence system?
2.  **System Administrator Definition:** What is the definitive method for identifying a "system_admin" (Clerk role, custom claim, specific org)?
3.  **User Management Operations:** Direct Clerk API interaction or intermediary Coherence system for user management?
4.  **Multiple Clerk Organizations per User:** How is context handled if a user belongs to multiple Coherence-linked Clerk orgs?
5.  **Tenant Provisioning Flow:** Exact process for creating a Coherence `Tenant` and linking/creating a Clerk `org_id`.
6.  **Global vs. Org-Specific Resources:** Mechanism for managing and scoping global templates/resources vs. org-specific ones.
7.  **Audit Log Granularity:** Specific fields and detail level required for audit logs.
8.  **`ProtectedRoute` Role Mapping:** How do roles like `tenant_admin` in `ProtectedRoute` map to Clerk roles or `Permission[]`?

## 6. Conclusion

The existing Clerk integration, centered on `clerk_org_id` and RLS, provides a strong foundation for the "Awesome Admin Site." Backend auth ([`src/coherence/api/v1/dependencies/auth.py`](src/coherence/api/v1/dependencies/auth.py)) and database schema correctly link tenancy to `clerk_org_id`.

Key considerations:
*   Define and implement the `Permission[]` model and its relation to Clerk roles.
*   Finalize "system_admin" identification and privileges.
*   Ensure seamless client-side Clerk integration for JWTs and org context.
*   Address questions in Section 5, especially regarding permissions and resource scoping.

The architecture aligns well with PRD requirements. Future work involves UI/API development and consistent application of role/permission checks on top of Clerk auth and RLS.