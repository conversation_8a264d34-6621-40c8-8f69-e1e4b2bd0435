# Admin UI for Coherence Template-Driven API Action System

This document outlines the implementation status of the Coherence admin interface built with Next.js, Refine.js, and Shadcn UI components.

## 1. Architecture Overview

### 1.1 Current Architecture
We have implemented a modern admin interface with the following stack:

- **Next.js 15.2.4**: Provides the application framework and routing
- **Refine.js Framework**: Handles core admin framework, data management, and resource routing
- **Shadcn UI**: Provides high-quality UI components built on Radix UI primitives
- **Clerk Authentication**: Manages user authentication and access control for admin users
- **Tailwind CSS**: Handles styling with utility-first approach

### 1.2 Authentication Implementation

The admin UI uses <PERSON> for authentication, providing:

- ✅ Secure login/signup pages with Clerk's built-in components
- ✅ Integration with Refine.js authentication provider
- ✅ User profile management via Clerk's UserButton
- ✅ Protected routes requiring authentication 
- 🔄 Role-based access control (in progress)
- 🔄 Organization management for tenant switching (in progress)

## 2. Core UI Components Implementation Status

### 2.1 Dashboard Overview
- ✅ API status grid with health indicators
- ✅ Activity feed component showing recent actions 
- ✅ Performance metrics visualization
- 🔄 Cache hit/miss ratio tracking (in progress)

### 2.2 API Integration Management
- **Import OpenAPI Spec UI**: ✅ COMPLETED
  - ✅ OpenAPI import wizard component
  - ✅ File upload area for spec files
  - ✅ URL input for direct import
  - ✅ API name/description fields
  - ✅ Base URL configuration
  - ✅ Validation status indicator
  
- **API Credentials Manager**: ✅ COMPLETED
  - ✅ Secure credential storage interface
  - ✅ Support for multiple authentication types (API Key, Basic Auth, Bearer Token, OAuth)
  - ✅ Auto-hiding secrets with manual reveal
  - ✅ Credential testing functionality
  - ✅ Credential rotation capability
  - ✅ Status indicators (active/expired/revoked)

### 2.3 Template Editor
- **Visual Template Editor**: ✅ COMPLETED
  - ✅ Modern UI with tabbed interface
  - ✅ Basic settings configuration (name, category, description)
  - ✅ Template code editor with Jinja2 syntax
  - ✅ Parameter mapping management
  - ✅ Response mapping interface with JSON Path support
  - ✅ Error handling configuration (retry count, fallback values)
  - ✅ Version history with timeline visualization
  
- **Testing Console**: ✅ COMPLETED
  - ✅ Integrated test panel
  - ✅ Parameter input interface with JSON editor
  - ✅ Test execution controls
  - ✅ Response visualization in JSON format

### 2.4 Intent Integration
- 🔄 Intent-to-template mapping UI (in progress)
- 🔄 Parameter definition interface (in progress)
- 🔄 Example utterance management (in progress)
- 🔄 Test console for simulating user inputs (planned)

### 2.5 Monitoring & Administration
- **API Health Dashboard**: ✅ COMPLETED
  - ✅ Status indicators for each API integration
  - ✅ Response time visualization
  - ✅ Error rate tracking
  - ✅ Circuit breaker status indicators
  - ✅ Health metrics by endpoint

- **Documentation View**: 🔄 IN PROGRESS
  - 🔄 Auto-generated documentation from templates (in progress)
  - 🔄 Example requests/responses (planned)
  
- **Audit Log**: 🔄 PLANNED
  - 🔄 Template modification history (in progress)
  - 🔄 API credential changes tracking (planned)
  - 🔄 Filtering capabilities (planned)

## 3. Implementation Details

### 3.1 Current Refine.js Framework Setup

```tsx
// From /coherence-admin/app/refine-provider.tsx
"use client"

import type React from "react"
import { Refine } from "@refinedev/core"
import { RefineThemes, notificationProvider } from "@refinedev/antd"
import routerProvider from "@refinedev/nextjs-router/app"
import { ConfigProvider } from "antd"
import { authProvider } from "@/providers/auth-provider"
import { dataProvider } from "@/providers/data-provider"

export function RefineProvider({ children }: { children: React.ReactNode }) {
  return (
    <ConfigProvider theme={RefineThemes.Blue}>
      <Refine
        authProvider={authProvider}
        dataProvider={dataProvider()}
        routerProvider={routerProvider}
        notificationProvider={notificationProvider}
        resources={[
          {
            name: "dashboard",
            list: "/dashboard",
            meta: {
              label: "Dashboard",
              icon: "LayoutDashboard",
            },
          },
          {
            name: "integrations",
            list: "/integrations",
            create: "/integrations/create",
            edit: "/integrations/edit/:id",
            show: "/integrations/show/:id",
            meta: {
              label: "API Integrations",
              icon: "Plug",
            },
          },
          {
            name: "templates",
            list: "/templates",
            create: "/templates/create",
            edit: "/templates/edit/:id",
            show: "/templates/show/:id",
            meta: {
              label: "Templates",
              icon: "FileCode",
            },
          },
          {
            name: "credentials",
            list: "/credentials",
            create: "/credentials/create",
            edit: "/credentials/edit/:id",
            meta: {
              label: "Credentials",
              icon: "Key",
            },
          },
          {
            name: "monitoring",
            list: "/monitoring",
            meta: {
              label: "Monitoring",
              icon: "BarChart",
            },
          },
        ]}
        options={{
          syncWithLocation: true,
          warnWhenUnsavedChanges: true,
        }}
      >
        {children}
      </Refine>
    </ConfigProvider>
  )
}
```

### 3.2 Clerk Authentication Integration

We've successfully integrated Clerk for authentication in our Next.js app, which provides:

- Secure sign-in and sign-up pages
- User profile management
- Protected routes requiring authentication
- Integration with Refine's auth provider

The actual implementation uses Next.js App Router for authentication:

```tsx
// In /coherence-admin/app/[...sign-in]/page.tsx
import { SignIn } from "@clerk/nextjs";

export default function Page() {
  return <SignIn />;
}
```

### 3.3 Component Implementation

#### Template Editor

The Template Editor component has been fully implemented with the following features:

```tsx
// Simplified structure from template-editor.tsx
export function TemplateEditor({ initialTemplate, onSave, onTest }) {
  // State management
  const [template, setTemplate] = useState(initialTemplate)
  const [activeTab, setActiveTab] = useState("basic")
  const [testParams, setTestParams] = useState({})
  const [testResult, setTestResult] = useState(null)
  const [showVersionHistory, setShowVersionHistory] = useState(false)

  // Handler functions...

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
      {/* Version History Sidebar */}
      {showVersionHistory && (
        <div className="lg:col-span-1 h-full">
          <Card className="h-full">
            {/* Version history display */}
          </Card>
        </div>
      )}

      {/* Main Editor Area */}
      <div className="h-full flex flex-col">
        {/* Top Control Buttons */}
        <div className="flex items-center justify-between mb-4">
          {/* Button controls */}
        </div>

        <Card className="flex-1">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-5 w-full">
              <TabsTrigger value="basic">Basic Settings</TabsTrigger>
              <TabsTrigger value="template">Template Code</TabsTrigger>
              <TabsTrigger value="parameters">Parameter Mapping</TabsTrigger>
              <TabsTrigger value="response">Response Mapping</TabsTrigger>
              <TabsTrigger value="error">Error Handling</TabsTrigger>
            </TabsList>

            {/* Tab content for each section */}
          </Tabs>
        </Card>

        {/* Test Panel */}
        <Card className="mt-4">
          <CardContent className="p-4">
            {/* Test interface */}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
```

#### Credential Manager

The Credential Manager has been implemented with these key features:

```tsx
// From credential-manager.tsx (simplified)
export function CredentialManager({ credentials, onAdd, onDelete, onTest, onRotate }) {
  // State for managing credentials and UI
  const [showSecret, setShowSecret] = useState<Record<string, boolean>>({})
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newCredential, setNewCredential] = useState({ /* initial values */ })
  const [testResults, setTestResults] = useState<Record<string, boolean>>({})

  // Auto-hiding secrets after 30 seconds
  const toggleShowSecret = (id: string) => {
    // Implementation...
    if (!showSecret[id]) {
      setTimeout(() => {
        setShowSecret((prev) => ({
          ...prev,
          [id]: false,
        }))
      }, 30000)
    }
  }

  // Handler functions for testing, rotating, etc.

  return (
    <div className="space-y-6">
      {/* Header with Add Credential button */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Credential Management</h2>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          {/* Add credential dialog with tabs for different auth types */}
        </Dialog>
      </div>

      {/* Credential list with cards */}
      <div className="grid gap-4">
        {credentials.map((credential) => (
          <Card key={credential.id}>
            {/* Credential display with test/rotate/delete buttons */}
            {/* Masked credential value with reveal option */}
          </Card>
        ))}
      </div>
    </div>
  )
}
```

## 4. Implementation Timeline and Status

### 4.1 Current Implementation Status (May 2025)

- ✅ **Core Framework Setup**: Refine.js with Next.js App Router
- ✅ **Authentication**: Clerk integration with protected routes
- ✅ **Template Management**: Complete template editor with testing UI
- ✅ **Credential Management**: Secure credential handling with multiple auth types
- ✅ **Monitoring Dashboard**: API health monitoring visualization
- 🔄 **Intent Integration**: In progress
- 🔄 **Documentation Generation**: In progress
- 🔄 **Multi-tenant Support**: Organization/tenant switching in progress

### 4.2 Remaining Tasks

1. **Intent Integration UI**:
   - Implement intent-to-template mapping interface
   - Create example utterance management
   - Build test console for simulating user inputs

2. **Documentation Generation**:
   - Complete auto-generation of docs from templates
   - Add example requests/responses
   - Create download functionality

3. **Audit Log System**:
   - Implement template modification history tracking
   - Add API credential changes tracking
   - Create filtering capabilities by date/user/action

4. **Multi-tenant Improvements**:
   - Finish organization/tenant switching
   - Implement role-based access control
   - Add tenant-specific settings

## 5. Key UI Screens and Workflows

### 5.1 API Integration Workflow

1. **API Registration Screen**
   - Form to add API details (name, description, base URL)
   - OpenAPI spec upload/URL input
   - Connection testing
   - Credential configuration

2. **Template Generation Screen**
   - Auto-generated templates from OpenAPI spec
   - Batch edit/approve functionality
   - Template categorization by API section
   - Edit individual templates

3. **Template Editor Screen**
   - Endpoint/method configuration
   - Parameter mapping builder
   - Response mapping with JSONPath helper
   - Authentication settings
   - Error handling configuration
   - Caching configuration (TTL settings)
   - Template inheritance controls

4. **Testing Console**
   - Parameter input form
   - Execute test button
   - Response display (formatted JSON)
   - Raw response view
   - Response transformation preview
   - Error simulation controls
   - Performance metrics

5. **Credential Manager Screen**
   - Add/edit/delete credentials
   - Encrypted storage indication
   - Test credential validity
   - OAuth configuration
   - Credential rotation scheduling

### 4.2 Tenant Management Workflow

With Clerk's Organizations feature, we can implement a powerful tenant management system:

1. **System Admin Dashboard**
   - Overview of all tenants
   - Ability to create/edit/delete tenants
   - Organization switching via Clerk UI
   - Tenant usage statistics

2. **Tenant Admin Dashboard**
   - View/manage resources for a specific tenant
   - User management within tenant
   - Tenant-specific settings
   - API key management

## 5. Backend API Endpoints

These endpoints will support the admin UI:

```
/v1/admin/integrations
  POST - Create new API integration
  GET - List all integrations

/v1/admin/integrations/{id}
  GET - Get integration details
  PUT - Update integration
  DELETE - Remove integration

/v1/admin/integrations/{id}/templates
  POST - Generate templates from spec
  GET - List templates for integration

/v1/admin/templates
  POST - Create template
  GET - List templates (with filters)

/v1/admin/templates/{id}
  GET - Get template details
  PUT - Update template
  DELETE - Delete template

/v1/admin/templates/{id}/test
  POST - Test template with parameters

/v1/admin/credentials
  POST - Store credentials
  GET - List credentials (masked)

/v1/admin/health
  GET - Get API health status
```

## 6. UX Recommendations

1. **Progressive Disclosure**: Hide advanced options until needed
2. **Inline Documentation**: Help text and tooltips for complex features
3. **Consistent Validation**: Real-time validation with clear error messages
4. **Template Versioning**: Clear indication of template versions and history
5. **Visual Feedback**: Status indicators for operations (success/failure)
6. **Responsive Design**: Works on various screen sizes for admin use
7. **Dark/Light Mode**: Support for both themes

## 7. Optimized v0 Prompts

For detailed v0 prompts to generate UI components, refer to the separate document:
[Optimized Vercel v0 Prompts](./admin_v0_prompt.md)

## 8. Implementation Timeline

### Phase 1: Core Framework (2 weeks)
- Set up Refine.js framework with basic CRUD operations
- Implement Clerk authentication
- Generate initial UI components with v0
- Create basic dashboard layout

### Phase 2: API Integration Features (3 weeks)
- Implement OpenAPI import workflow
- Build template editor and testing console
- Create credential management interface
- Set up tenant context switching

### Phase 3: Monitoring & Admin Features (2 weeks)
- Build API health monitoring dashboard
- Implement audit logging
- Create documentation generation system
- Finalize user/tenant management

## 9. Benefits of the Hybrid Approach

1. **Leverages Both Tools' Strengths**:
   - Uses v0 for high-quality UI component generation
   - Uses Refine for data management, resource handling, and admin framework structure
   - Uses Clerk for secure authentication and user management

2. **Maintainable Architecture**:
   - Clear separation of UI components and data management
   - TypeScript integration for type safety
   - Consistent styling with Shadcn UI and Tailwind

3. **Developer Experience**:
   - Rapid UI development with v0
   - Structured data handling with Refine
   - Standard React patterns throughout

4. **User Experience**:
   - Professional, polished interface
   - Consistent interaction patterns
   - Responsive and accessible design

This hybrid approach gives us the speed and flexibility of v0's AI-generated components with the robust data handling and admin features of Refine, all secured with Clerk's authentication system.