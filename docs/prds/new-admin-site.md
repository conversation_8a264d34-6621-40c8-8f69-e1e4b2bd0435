# COHERENCE ADMIN UI - TECHNICAL PRD
## Implementation Specification for Revolutionary Workflow Management

### 🏗️ TECHNICAL OVERVIEW

**Project**: `coherence-admin` (Next.js 15)
**Vision**: Build the workflow orchestration control center 
**Timeline**: 4-6 weeks (with AI assistance)
**Architecture**: Component-driven, API-first, mobile-responsive

### 📁 PROJECT STRUCTURE

```
coherence-admin/
├── src/
│   ├── app/                    # Next.js 15 App Router
│   │   ├── admin/             # Admin routes (auth protected)
│   │   │   ├── layout.tsx     # Admin shell layout
│   │   │   ├── page.tsx       # Dashboard
│   │   │   ├── workflows/     # Workflow management
│   │   │   │   ├── page.tsx       # List workflows
│   │   │   │   ├── create/        # Create new workflow
│   │   │   │   ├── [id]/          # Edit workflow
│   │   │   │   └── templates/     # Template marketplace
│   │   │   ├── integrations/  # API integrations
│   │   │   │   ├── page.tsx       # List integrations
│   │   │   │   ├── import/        # Import OpenAPI
│   │   │   │   ├── [id]/          # Integration detail
│   │   │   │   └── credentials/   # Credential management
│   │   │   ├── templates/     # Template management
│   │   │   │   ├── page.tsx       # List templates
│   │   │   │   ├── create/        # Create template
│   │   │   │   ├── [id]/          # Edit template
│   │   │   │   └── versions/      # Version control
│   │   │   ├── testing/       # Testing lab
│   │   │   │   ├── playground/    # Workflow sandbox
│   │   │   │   ├── scenarios/     # Test scenarios
│   │   │   │   └── analytics/     # Test analytics
│   │   │   ├── tenants/       # Tenant management
│   │   │   │   ├── page.tsx       # List tenants
│   │   │   │   ├── create/        # Create tenant
│   │   │   │   └── [id]/          # Tenant dashboard
│   │   │   └── system/        # System admin
│   │   │       ├── health/        # System health
│   │   │       ├── monitoring/    # Analytics
│   │   │       └── settings/      # Global settings
│   │   └── api/               # API routes (if needed)
│   ├── components/            # React components
│   │   ├── ui/               # shadcn/ui components
│   │   ├── workflow/         # Workflow-specific
│   │   │   ├── WorkflowBuilder.tsx
│   │   │   ├── StepEditor.tsx
│   │   │   ├── ConditionEditor.tsx
│   │   │   └── OutputDesigner.tsx
│   │   ├── forms/            # Form components
│   │   │   ├── WorkflowForm.tsx
│   │   │   ├── IntegrationForm.tsx
│   │   │   └── TemplateForm.tsx
│   │   ├── visualization/    # Data viz components
│   │   │   ├── FlowDiagram.tsx
│   │   │   ├── MetricCharts.tsx
│   │   │   └── PerformanceGraphs.tsx
│   │   └── testing/          # Testing components
│   │       ├── ChatSimulator.tsx
│   │       ├── TestScenario.tsx
│   │       └── PerformanceMonitor.tsx
│   ├── lib/                  # Utilities & helpers
│   │   ├── api/             # API client
│   │   │   ├── client.ts        # Axios instance
│   │   │   ├── workflows.ts     # Workflow endpoints
│   │   │   ├── integrations.ts  # Integration endpoints
│   │   │   ├── templates.ts     # Template endpoints
│   │   │   └── auth.ts          # Auth helpers
│   │   ├── hooks/           # Custom hooks
│   │   │   ├── useWorkflows.ts
│   │   │   ├── useIntegrations.ts
│   │   │   ├── useTemplates.ts
│   │   │   └── useTesting.ts
│   │   ├── stores/          # Zustand stores
│   │   │   ├── useAuthStore.ts
│   │   │   ├── useWorkflowStore.ts
│   │   │   └── useUIStore.ts
│   │   ├── types/           # TypeScript types
│   │   │   ├── workflow.ts
│   │   │   ├── integration.ts
│   │   │   ├── template.ts
│   │   │   └── api.ts
│   │   └── utils/           # Helper functions
│   │       ├── validation.ts
│   │       ├── formatting.ts
│   │       └── workflow-utils.ts
│   └── styles/              # Global styles
├── public/                  # Static assets
└── tests/                   # Test files
```

### 🔧 TECHNICAL STACK

#### Core Technologies
```json
{
  "framework": "Next.js 15",
  "react": "^19.0.0",
  "typescript": "^5.0.0",
  "styling": "Tailwind CSS + shadcn/ui",
  "state": "Zustand + React Query",
  "auth": "Clerk",
  "visualization": "React Flow + Recharts",
  "forms": "React Hook Form + Zod",
  "api": "Axios + OpenAPI client",
  "testing": "Jest + Testing Library"
}
```

#### UI Libraries
```json
{
  "design-system": "shadcn/ui",
  "icons": "lucide-react",
  "flow-diagrams": "react-flow-renderer",
  "charts": "recharts",
  "markdown": "react-markdown",
  "syntax-highlighting": "prism-react-renderer",
  "drag-and-drop": "react-beautiful-dnd",
  "notifications": "react-hot-toast"
}
```

### 🔐 AUTHENTICATION & AUTHORIZATION

#### Session Management
```typescript
// lib/auth.ts
interface AdminSession {
  clerkUserId: string;
  tenantId: string;
  tenantName: string;
  isSystemAdmin: boolean;
  permissions: Permission[];
  apiKey: string;
  preferences: UserPreferences;
}

export const useAdminSession = () => {
  const { user } = useAuth();
  const { data: session } = useQuery(['admin-session'], async () => {
    // Fetch tenant by Clerk org
    // Get user permissions
    // Return full session context
  });
  return session;
};
```

#### Role-Based UI
```typescript
// components/auth/ProtectedRoute.tsx
const ProtectedRoute = ({ 
  children, 
  requiredRole = 'tenant_admin' 
}) => {
  const session = useAdminSession();
  
  if (!session || !hasPermission(session, requiredRole)) {
    return <AccessDenied />;
  }
  
  return children;
};
```

### 🎯 KEY COMPONENTS

#### 1. Workflow Builder
```typescript
// components/workflow/WorkflowBuilder.tsx
import ReactFlow from 'react-flow-renderer';

const WorkflowBuilder = ({ workflow, onUpdate }) => {
  const [nodes, setNodes] = useState(workflowToNodes(workflow));
  const [edges, setEdges] = useState(workflowToEdges(workflow));
  
  const nodeTypes = {
    action: ActionNode,
    condition: ConditionNode,
    output: OutputNode,
    integration: IntegrationNode
  };
  
  return (
    <div className="h-full">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={handleConnect}
      >
        <Background />
        <Controls />
        <MiniMap />
      </ReactFlow>
    </div>
  );
};
```

#### 2. Template Editor
```typescript
// components/templates/TemplateEditor.tsx
const TemplateEditor = ({ templateId }) => {
  const { data: template, isLoading } = useTemplate(templateId);
  const [content, setContent] = useState('');
  const [variables, setVariables] = useState([]);
  
  return (
    <div className="grid grid-cols-2 gap-6">
      <div className="border rounded-lg p-4">
        <h3>Template Content</h3>
        <MonacoEditor
          value={content}
          language="handlebars"
          theme="vs-dark"
          onChange={setContent}
        />
      </div>
      
      <div className="border rounded-lg p-4">
        <h3>Variables</h3>
        <VariableManager
          variables={variables}
          onUpdate={setVariables}
        />
        <TemplatePreview
          content={content}
          variables={variables}
        />
      </div>
    </div>
  );
};
```

#### 3. Integration Manager
```typescript
// components/integrations/IntegrationManager.tsx
const IntegrationManager = () => {
  const { data: integrations } = useIntegrations();
  const [selectedIntegration, setSelectedIntegration] = useState(null);
  
  return (
    <div className="flex h-full">
      <IntegrationSidebar
        integrations={integrations}
        selected={selectedIntegration}
        onSelect={setSelectedIntegration}
      />
      
      <main className="flex-1">
        {selectedIntegration ? (
          <IntegrationDetail
            integration={selectedIntegration}
          />
        ) : (
          <IntegrationEmpty />
        )}
      </main>
    </div>
  );
};
```

#### 4. Testing Playground
```typescript
// components/testing/TestPlayground.tsx
const TestPlayground = ({ workflowId }) => {
  const [testInput, setTestInput] = useState('');
  const [testHistory, setTestHistory] = useState([]);
  const { mutate: runTest, isLoading } = useTestWorkflow();
  
  const handleTest = async () => {
    const result = await runTest({
      workflowId,
      input: testInput,
      context: buildTestContext()
    });
    
    setTestHistory([...testHistory, result]);
  };
  
  return (
    <div className="grid grid-cols-2 gap-6 h-full">
      <div className="border rounded-lg p-4">
        <ChatSimulator
          messages={testHistory}
          onInput={setTestInput}
          isLoading={isLoading}
        />
      </div>
      
      <div className="border rounded-lg p-4">
        <TestResults results={testHistory} />
        <PerformanceMetrics workflow={workflowId} />
      </div>
    </div>
  );
};
```

### 📡 API INTEGRATION

#### API Client Architecture
```typescript
// lib/api/client.ts
import axios from 'axios';

class CoherenceAPI {
  private client: AxiosInstance;
  
  constructor(session: AdminSession) {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL,
      headers: {
        'Authorization': `Bearer ${session.apiKey}`,
        'X-Tenant-ID': session.tenantId
      }
    });
  }
  
  // Workflow endpoints
  workflows = {
    list: () => this.client.get('/admin/workflows'),
    create: (data) => this.client.post('/admin/workflows', data),
    get: (id) => this.client.get(`/admin/workflows/${id}`),
    update: (id, data) => this.client.put(`/admin/workflows/${id}`, data),
    test: (id, input) => this.client.post(`/admin/workflows/${id}/test`, input),
    deploy: (id) => this.client.post(`/admin/workflows/${id}/deploy`)
  };
  
  // Template endpoints
  templates = {
    list: (params) => this.client.get('/admin/templates', { params }),
    create: (data) => this.client.post('/admin/templates', data),
    get: (id) => this.client.get(`/admin/templates/${id}`),
    update: (id, data) => this.client.put(`/admin/templates/${id}`, data),
    versions: (id) => this.client.get(`/admin/templates/${id}/versions`),
    rollback: (id, version) => this.client.post(`/admin/templates/${id}/rollback`, { version })
  };
  
  // Integration endpoints
  integrations = {
    list: () => this.client.get('/admin/integrations/integrations'),
    import: (spec) => this.client.post('/admin/integrations/import', spec),
    get: (id) => this.client.get(`/admin/integrations/integrations/${id}`),
    credentials: {
      list: (id) => this.client.get(`/admin/credentials/${id}`),
      store: (id, type, data) => this.client.put(`/admin/credentials/${id}/${type}`, data),
      delete: (id) => this.client.delete(`/admin/credentials/${id}`)
    },
    oauth: {
      init: (id, flow) => this.client.post(`/admin/oauth/initialize`, { integration_id: id, flow_type: flow }),
      refresh: (id) => this.client.post(`/admin/oauth/token/refresh`, { integration_id: id }),
      config: (id) => this.client.get(`/admin/oauth/config/${id}`)
    }
  };
}
```

#### React Query Integration
```typescript
// lib/hooks/useWorkflows.ts
export const useWorkflows = () => {
  const session = useAdminSession();
  const api = useAPI(session);
  
  return useQuery(['workflows'], () => api.workflows.list(), {
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000
  });
};

export const useCreateWorkflow = () => {
  const queryClient = useQueryClient();
  const api = useAPI();
  
  return useMutation(
    (data) => api.workflows.create(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['workflows']);
        toast.success('Workflow created successfully!');
      },
      onError: (error) => {
        toast.error(`Failed to create workflow: ${error.message}`);
      }
    }
  );
};
```

### 🎨 UI COMPONENT PATTERNS

#### Form Architecture
```typescript
// components/forms/WorkflowForm.tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

const workflowSchema = z.object({
  name: z.string().min(3).max(50),
  description: z.string().optional(),
  category: z.enum(['medical', 'travel', 'analytics', 'custom']),
  steps: z.array(workflowStepSchema),
  outputTemplate: z.string()
});

const WorkflowForm = ({ initialData, onSubmit }) => {
  const form = useForm({
    resolver: zodResolver(workflowSchema),
    defaultValues: initialData
  });
  
  return (
    <Form {...form}>
      <FormField name="name" render={({ field }) => (
        <FormItem>
          <FormLabel>Workflow Name</FormLabel>
          <FormControl>
            <Input {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )} />
      
      {/* More fields... */}
      
      <Button type="submit">Save Workflow</Button>
    </Form>
  );
};
```

#### Reusable Components
```typescript
// components/ui/DataTable.tsx
interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  loading?: boolean;
  error?: Error;
}

const DataTable = <T,>({ data, columns, loading, error }: DataTableProps<T>) => {
  const [sorting, setSorting] = useState([]);
  const [columnFilters, setColumnFilters] = useState([]);
  
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel()
  });
  
  if (loading) return <TableSkeleton />;
  if (error) return <TableError error={error} />;
  
  return (
    <div className="space-y-4">
      <DataTableToolbar table={table} />
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <DataTablePagination table={table} />
    </div>
  );
};
```

### 📊 STATE MANAGEMENT

#### Zustand Stores
```typescript
// lib/stores/useWorkflowStore.ts
interface WorkflowStore {
  workflows: Workflow[];
  selectedWorkflow: Workflow | null;
  isEditing: boolean;
  draft: Partial<Workflow>;
  
  actions: {
    setWorkflows: (workflows: Workflow[]) => void;
    selectWorkflow: (id: string) => void;
    startEditing: () => void;
    updateDraft: (changes: Partial<Workflow>) => void;
    saveWorkflow: () => Promise<void>;
    cancelEditing: () => void;
  };
}

const useWorkflowStore = create<WorkflowStore>((set, get) => ({
  workflows: [],
  selectedWorkflow: null,
  isEditing: false,
  draft: {},
  
  actions: {
    setWorkflows: (workflows) => set({ workflows }),
    
    selectWorkflow: (id) => {
      const workflow = get().workflows.find(w => w.id === id);
      set({ selectedWorkflow: workflow });
    },
    
    startEditing: () => {
      const { selectedWorkflow } = get();
      set({ 
        isEditing: true, 
        draft: selectedWorkflow ? { ...selectedWorkflow } : {} 
      });
    },
    
    updateDraft: (changes) => {
      set(state => ({
        draft: { ...state.draft, ...changes }
      }));
    },
    
    saveWorkflow: async () => {
      const { draft, selectedWorkflow } = get();
      // API call to save
      // Update state on success
    },
    
    cancelEditing: () => {
      set({ isEditing: false, draft: {} });
    }
  }
}));
```

### 🧪 TESTING STRATEGY

#### Unit Tests
```typescript
// tests/components/WorkflowBuilder.test.tsx
describe('WorkflowBuilder', () => {
  it('renders workflow steps correctly', () => {
    const mockWorkflow = {
      id: '1',
      steps: [
        { id: 'step1', type: 'action', config: {} },
        { id: 'step2', type: 'condition', config: {} }
      ]
    };
    
    render(<WorkflowBuilder workflow={mockWorkflow} />);
    
    expect(screen.getByText('step1')).toBeInTheDocument();
    expect(screen.getByText('step2')).toBeInTheDocument();
  });
  
  it('handles adding new steps', async () => {
    const onUpdate = jest.fn();
    render(<WorkflowBuilder workflow={emptyWorkflow} onUpdate={onUpdate} />);
    
    const addButton = screen.getByText('Add Step');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(onUpdate).toHaveBeenCalled();
    });
  });
});
```

#### Integration Tests
```typescript
// tests/integration/workflow-creation.test.tsx
describe('Workflow Creation Flow', () => {
  it('creates a complete workflow', async () => {
    render(<App />);
    
    // Navigate to workflow creation
    fireEvent.click(screen.getByText('Create Workflow'));
    
    // Fill form
    fireEvent.change(screen.getByLabelText('Name'), {
      target: { value: 'Test Workflow' }
    });
    
    // Add steps
    await addWorkflowStep('API Call');
    await addWorkflowStep('Condition');
    
    // Save workflow
    fireEvent.click(screen.getByText('Save Workflow'));
    
    // Verify success
    await waitFor(() => {
      expect(screen.getByText('Workflow created successfully')).toBeInTheDocument();
    });
  });
});
```

### 🚀 PERFORMANCE OPTIMIZATION

#### Code Splitting
```typescript
// Dynamic imports for heavy components
const WorkflowBuilder = dynamic(() => import('../components/workflow/WorkflowBuilder'), {
  loading: () => <WorkflowBuilderSkeleton />,
  ssr: false
});

const TemplateEditor = dynamic(() => import('../components/templates/TemplateEditor'), {
  loading: () => <EditorSkeleton />
});
```

#### Memoization
```typescript
// Memoize expensive computations
const MemoizedFlowDiagram = memo(({ nodes, edges }) => {
  const processedData = useMemo(() => {
    return processFlowData(nodes, edges);
  }, [nodes, edges]);
  
  return <FlowDiagram data={processedData} />;
});
```

#### Virtual Scrolling
```typescript
// For large lists
import { FixedSizeList as List } from 'react-window';

const VirtualizedTemplateList = ({ templates }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      <TemplateCard template={templates[index]} />
    </div>
  );
  
  return (
    <List
      height={600}
      itemCount={templates.length}
      itemSize={100}
      width="100%"
    >
      {Row}
    </List>
  );
};
```

### 📱 RESPONSIVE DESIGN

#### Mobile-First Approach
```typescript
// Responsive layout components
const AdminLayout = ({ children }) => {
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);
  
  return (
    <div className="flex h-screen">
      {isMobile ? (
        <MobileSidebar />
      ) : (
        <DesktopSidebar />
      )}
      <main className="flex-1 overflow-auto">
        {children}
      </main>
    </div>
  );
};
```

### 🔍 MONITORING & ANALYTICS

#### Error Tracking
```typescript
// Global error boundary
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    // Log to monitoring service
    logError({
      error,
      errorInfo,
      user: getCurrentUser(),
      route: window.location.pathname
    });
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    
    return this.props.children;
  }
}
```

#### User Analytics
```typescript
// Track user interactions
const useAnalytics = () => {
  const trackEvent = useCallback((event, properties) => {
    analytics.track(event, {
      ...properties,
      timestamp: Date.now(),
      user: getCurrentUser(),
      page: window.location.pathname
    });
  }, []);
  
  return { trackEvent };
};
```

### 📦 DEPLOYMENT

#### Docker Configuration
```dockerfile
# Dockerfile
FROM node:20-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM node:20-alpine AS runner
WORKDIR /app

ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

#### Environment Configuration
```typescript
// lib/config.ts
const config = {
  apiUrl: process.env.NEXT_PUBLIC_API_URL,
  clerkPublishableKey: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
  environment: process.env.NODE_ENV,
  features: {
    enableAnalytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
    enableExperiments: process.env.NEXT_PUBLIC_ENABLE_EXPERIMENTS === 'true'
  }
};

export default config;
```

### 📋 DEVELOPMENT WORKFLOW

#### Git Workflow
```
main
├── develop
│   ├── feature/workflow-builder
│   ├── feature/template-editor
│   └── feature/integration-manager
├── release/v1.0.0
└── hotfix/critical-bug
```

#### Code Quality
```typescript
// .eslintrc.js
module.exports = {
  extends: [
    'next/core-web-vitals',
    'plugin:@typescript-eslint/recommended',
    'prettier'
  ],
  rules: {
    'react-hooks/exhaustive-deps': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    'import/order': ['error', {
      'groups': ['builtin', 'external', 'internal'],
      'newlines-between': 'always',
      'alphabetize': { 'order': 'asc' }
    }]
  }
};
```

### 🎯 SUCCESS CRITERIA

#### Technical Metrics
- **Performance**: First contentful paint < 1.8s
- **Accessibility**: WCAG 2.1 AA compliance
- **Code Quality**: 90%+ test coverage
- **Security**: Zero critical vulnerabilities

#### User Experience
- **Task Completion**: 90% success rate for key workflows
- **Error Rate**: < 2% for critical paths
- **User Satisfaction**: 4.8+ rating from internal users
- **Adoption**: 80% daily active user rate

---

## 🚀 NEXT STEPS

1. **Initialize Project**: Set up Next.js 15 with full type safety
2. **Build Foundation**: Auth, routing, and API client
3. **Implement Core Features**: Workflow builder, template manager
4. **Add Intelligence**: AI suggestions, testing framework
5. **Polish & Optimize**: Performance, UX refinements
6. **Deploy & Iterate**: Production deployment with monitoring

This technical specification provides the blueprint for building the world's most powerful workflow automation admin interface. Let's revolutionize how enterprises create and manage their automations! 💥