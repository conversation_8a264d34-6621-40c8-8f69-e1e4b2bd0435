# Coherence — Master Product Requirements Document

### Version 1.5 • 2025‑05‑05
### Status: Draft updated for multi‑tenant + multi‑industry scope

---

## 0. Executive Summary

Coherence is PhaseLoch's stand‑alone, language‑agnostic middleware that transforms natural conversation into deterministic actions through an intelligent, multi-tiered pipeline. 

Our vision: **A user speaks naturally; the system understands instantly, asks only essential questions, and takes action flawlessly.**

Key capabilities:

* **Lightning-fast** responses: Vector matches in <100ms, LLM fallbacks in <500ms
* **Conversational** parameter completion with Star Trek computer-like interaction
* **Multi-tenant** architecture with complete data isolation
* **Industry Packs** for healthcare, travel, fintech, and more
* **Pluggable Actions** from synchronous HTTP calls to complex multi-step workflows
* **OpenAPI Auto-Integration** for seamless connectivity to external services
* **Enterprise-grade** security, compliance, and observability

---

## 1. Goals & Non‑Goals

| Goals | Non‑Goals |
|-------|-----------|
| • Understand user intent conversationally with minimal latency | • Build end‑user UI components |
| • Tiered processing: vectors → local LLMs → retrieval augmentation | • Off‑line fine‑tuning of LLMs |
| • Multi‑tenant isolation with enterprise-grade security | • Real‑time language translation (future) |
| • Conversational parameter collection with context awareness | • Payment processing (only plumbing spec now) |
| • Industry‑specific packs with intent catalogues and actions | |
| • Auto-generate actions from API specifications | |
| • Sub-100ms responses for common intents | |
| • HIPAA & GDPR compliance tiers | |

---

## 2. User Stories

| # | Actor | Story | Acceptance Criteria |
|---|-------|-------|---------------------|
| US‑01 | End User | "Computer, schedule a meeting with my team for tomorrow at 10 AM." | Intent recognized within 100ms, necessary parameters collected conversationally, calendar event created. |
| US‑02 | End User | "I've been having chest pain that comes and goes." | Health intent recognized, appropriate medical follow-up questions asked, urgent consultation scheduled if needed. |
| US‑03 | Tenant Admin | "Add a new 'refill prescription' intent for Clinic A only." | Intent appears in Clinic A's bot; Clinic B unaffected. |
| US‑04 | Super‑Admin | "Publish a Travel Pack with hotel‑booking intents." | Any tenant bound to `travel_pack` sees new skills after index rebuild. |
| US‑05 | Compliance Officer | "Download audit logs for tenant X last 30 days." | CSV includes request\_id, template version, action outcome; PHI masked. |
| US‑06 | Finance | "See token usage by tenant to generate invoices." | `/admin/billing/report?month=2025‑05` returns per‑tenant token counts. |
| US‑07 | DevOps | "Know immediately if latency exceeds thresholds." | Alerts trigger when p95 latency exceeds target thresholds per processing tier. |
| US‑08 | Template Author | "Test my new intent detection template without impacting production." | Test interface allows preview of template changes before applying them. |
| US‑09 | API Integration Engineer | "Connect our system to the EHR API without writing custom code." | Upload OpenAPI spec, map intents to endpoints, and auto-generate actions. |
| US‑10 | Developer | "Build a custom action component that integrates with our proprietary system." | Can use SDK to create, test, and deploy custom action handlers within standard framework. |
| US‑11 | Security Analyst | "Verify that all API integrations comply with our security policies." | Security dashboard shows compliance status for all integrations with detailed reports. |

---

## 3. System Architecture

### 3.1 Processing Tiers

Coherence employs a three-tiered architecture for intent recognition and processing:

```
┌─────────────────────────────────────────────────────────────────────┐
│                              Client                                  │
└───────────────────────────────┬─────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                           Chat Gateway                               │
└───────────────────────────────┬─────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                        Coherence Middleware                          │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐  │
│  │   TIER 1: 100ms │    │   TIER 2: 300ms │    │   TIER 3: 500ms │  │
│  │                 │    │                 │    │                 │  │
│  │  Vector Matcher │    │    Local LLM    │    │ RAG Augmentation│  │
│  │    (Qdrant)     │◄───┤  Intent Router  │◄───┤  (Full Context) │  │
│  │                 │    │   (Mistral 7B)  │    │    (OpenAI)     │  │
│  └────────┬────────┘    └────────┬────────┘    └────────┬────────┘  │
│           │                      │                      │           │
│           └──────────────────────┼──────────────────────┘           │
│                                  │                                  │
│                                  ▼                                  │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │                 Parameter Completion Service                 │   │
│  │                  (Conversational, LLM-guided)                │   │
│  └──────────────────────────────┬──────────────────────────────┘   │
│                                  │                                  │
│                                  ▼                                  │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │                       Action Registry                        │   │
│  │    ┌───────────────┐  ┌────────────────┐  ┌─────────────┐   │   │
│  │    │ Sync Actions  │  │ Async Workflows│  │External APIs│   │   │
│  │    └───────────────┘  └────────────────┘  └─────────────┘   │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

### 3.2 OpenAPI to Action Generation

The OpenAPI Adapter automatically transforms API specifications into Coherence Actions:

```
┌────────────────────────────────────────────────────────────────────┐
│                     OpenAPI to Action Generator                     │
├────────────────────────────────────────────────────────────────────┤
│                                                                    │
│    ┌─────────────────┐   ┌─────────────────┐   ┌──────────────┐    │
│    │   Spec Ingestion │   │  Schema Analysis│   │ Code Generation │ │
│    │                 │   │                 │   │                │  │
│    │ • OAS Parser    │   │ • Type Mapping  │   │ • Action Class │  │
│    │ • Validation    │──▶│ • Path Analysis │──▶│ • Intent YAML  │  │
│    │ • Normalization │   │ • Auth Detection│   │ • Parameter    │  │
│    │                 │   │                 │   │   Schemas      │  │
│    └─────────────────┘   └─────────────────┘   └──────────────┘    │
│                                                        │           │
│                                                        ▼           │
│    ┌─────────────────────────────────────────────────────────┐     │
│    │                   Integration Registry                   │     │
│    │ ┌─────────────┐  ┌──────────────┐  ┌───────────────┐     │     │
│    │ │ Auth Configs│  │ Rate Limiting │  │ Tenant Bindings│    │     │
│    │ └─────────────┘  └──────────────┘  └───────────────┘     │     │
│    └─────────────────────────────────────────────────────────┘     │
│                                                                    │
└────────────────────────────────────────────────────────────────────┘
```

### 3.3 Tenant Isolation

All system components enforce tenant isolation:

```
                ┌───────────┐   row‑level auth   ┌─────────────────┐
Client → GW →   │  Coherence │──────────────────▶│  Tenant DB view  │
                └────┬──────┘                    └─────────────────┘
                     │ 
                     ▼
         +─────────────────────────+
         | VectorIntentMatcher     |  (collection: intent_idx_<tenant>_<role>)
         +─────────────────────────+
               │            │
               │            │
         +─────────────────────────+
         | Knowledge & Template DB  |  (tenant-isolated collections)
         +─────────────────────────+
```

### 3.4 Template Management System

The Template Management System is a core component that enables the configuration and customization of Coherence's behavior:

```
┌───────────────────────────────────────────────────────────────────────┐
│                        Template Management System                      │
├───────────────────────────────────────────────────────────────────────┤
│                                                                       │
│    ┌─────────────────┐   ┌─────────────────┐   ┌─────────────────┐    │
│    │   Admin UI      │   │  Template API   │   │ Version Control │    │
│    │                 │   │                 │   │                 │    │
│    │ • Monaco Editor │   │ • CRUD          │   │ • History       │    │
│    │ • Preview       │   │ • Validation    │   │ • Diff View     │    │
│    │ • Diff View     │   │ • Scope Control │   │ • Rollback      │    │
│    └────────┬────────┘   └────────┬────────┘   └────────┬────────┘    │
│             │                     │                     │              │
│             └─────────────────────┼─────────────────────┘              │
│                                   │                                    │
│                                   ▼                                    │
│    ┌─────────────────────────────────────────────────────────────┐    │
│    │                     Template Database                        │    │
│    │ ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │    │
│    │ │ Global      │  │ Pack        │  │ Tenant      │           │    │
│    │ │ Templates   │  │ Templates   │  │ Templates   │           │    │
│    │ └─────────────┘  └─────────────┘  └─────────────┘           │    │
│    └─────────────────────────────────────────────────────────────┘    │
│                                   │                                    │
│                                   ▼                                    │
│    ┌─────────────────────────────────────────────────────────────┐    │
│    │                     Template Distribution                    │    │
│    │ ┌────────────────┐ ┌────────────────┐ ┌────────────────┐    │    │
│    │ │ Cache          │ │ Vectorization  │ │ Index Rebuild  │    │    │
│    │ │ Invalidation   │ │ Pipeline       │ │ Triggers       │    │    │
│    │ └────────────────┘ └────────────────┘ └────────────────┘    │    │
│    └─────────────────────────────────────────────────────────────┘    │
│                                                                       │
└───────────────────────────────────────────────────────────────────────┘
```

### 3.5 Global Distribution Architecture

For enterprises with global operations, Coherence provides multi-region deployment capabilities:

```
┌─────────────────────┐   ┌─────────────────────┐   ┌─────────────────────┐
│      US Region      │   │    EU Region        │   │    APAC Region      │
│  ┌───────────────┐  │   │  ┌───────────────┐  │   │  ┌───────────────┐  │
│  │ API Gateway   │  │   │  │ API Gateway   │  │   │  │ API Gateway   │  │
│  └───────┬───────┘  │   │  └───────┬───────┘  │   │  └───────┬───────┘  │
│          │          │   │          │          │   │          │          │
│  ┌───────┴───────┐  │   │  ┌───────┴───────┐  │   │  ┌───────┴───────┐  │
│  │   Coherence   │◄─┼───┼─►│   Coherence   │◄─┼───┼─►│   Coherence   │  │
│  └───────┬───────┘  │   │  └───────┬───────┘  │   │  └───────┬───────┘  │
│          │          │   │          │          │   │          │          │
│  ┌───────┴───────┐  │   │  ┌───────┴───────┐  │   │  ┌───────┴───────┐  │
│  │ Regional DB   │  │   │  │ Regional DB   │  │   │  │ Regional DB   │  │
│  └───────────────┘  │   │  └───────────────┘  │   │  └───────────────┘  │
└─────────┬───────────┘   └─────────┬───────────┘   └─────────┬───────────┘
          │                         │                         │            
          └─────────────────────────┼─────────────────────────┘            
                                    │                                      
                           ┌────────┴────────┐                             
                           │  Global Config  │                             
                           │  Replication    │                             
                           └─────────────────┘                             
```

---

## 4. Component Specifications

### 4.1 Three-Tier Intent Recognition *(FOUNDATIONAL P0)*

| Tier | Latency Target | Technology | Purpose | 
|------|---------------|------------|---------|
| Tier 1 | <100ms p95 | Vector embedding + Qdrant | Handle common, well-defined intents with near-instant response |
| Tier 2 | <300ms p95 | Local LLM (Mistral 7B) | Process complex but recognizable intents requiring more context |
| Tier 3 | <500ms p95 | RAG + Cloud LLM | Handle novel, ambiguous requests or those needing knowledge retrieval |

**Processing Flow**:
1. Every user message first attempts Tier 1 vector matching
2. If confidence below threshold τ1, escalate to Tier 2 local LLM
3. If confidence below threshold τ2, escalate to Tier 3 RAG
4. Results from all tiers feed back into training data pipeline

**Confidence Scoring**:
- Vector similarity threshold τ1: Default 0.85, configurable per tenant/domain
- LLM confidence threshold τ2: Default 0.75, using LLM self-evaluation

**Fallback Strategy**:
- If all tiers fail to reach confidence threshold, default to clarification question
- System learns from these failures through human-in-the-loop annotation

### 4.2 Tenant Model & Isolation *(FOUNDATIONAL P0)*

| Facet | Spec |
|-------|------|
| Identifier | JWT claims **or** per‑tenant **API Key**. API keys are 256‑bit random strings stored hashed; header `X‑API‑Key` required if JWT absent. |
| DB | Single Postgres DB, Row‑Level Security per table (`tenant_id` column). |
| Vector | Qdrant collection per tenant+role (`intent_idx_<tid>_<role>`). |
| Redis keyspace | Prefix `tenant:<tid>:`. |
| Config | Tenants table → pack binding, compliance tier, quotas, **api\_key\_hash**, key rotation date. |

*Auth flow*: Gateway passes either a bearer JWT **or** `X‑API‑Key`; Coherence resolves tenant → role, verifies RLS, then processes request.

### 4.3 Star Trek Conversational Interface *(P1)*

The parameter completion service employs a Star Trek-like conversational interface:

**Principles**:
- **Concise**: Responses are brief and to the point
- **Intelligent**: Demonstrates understanding of context and intent
- **Helpful**: Proactively suggests reasonable defaults
- **Direct**: Asks for missing information in a natural way

**Implementation**:
- Parameter schemas define required fields with validation rules
- LLM-guided extraction attempts to pull parameters from initial request
- Missing parameters trigger natural language follow-up questions
- Context is maintained throughout the conversation
- User can provide multiple parameters in a single response

**Example Conversation**:
```
User: "Schedule a meeting with the engineering team"
System: "When would you like to schedule this meeting?"
User: "Tomorrow afternoon"
System: "I'll schedule a meeting with engineering tomorrow at 2:00 PM. The meeting will be 1 hour. Is that correct?"
User: "Make it 3:00 PM and invite the design team too"
System: "Meeting scheduled: Engineering and Design teams, tomorrow at 3:00 PM for 1 hour."
```

### 4.4 Industry Packs *(P1 foundation)*

* Folder `packs/<pack_id>/catalogues/*.yaml` + default templates
* Super‑admin API `/admin/packs` CRUD; assigns pack to tenant
* Pack fallback chain: **tenant override → pack → global**

**Initial Industry Packs**:
1. **Healthcare Pack**:
   - HIPAA compliant intents and actions
   - Medical appointment scheduling
   - Symptom recording and triage
   - Medication management

2. **Travel Pack**:
   - Flight and hotel booking intents
   - Itinerary management
   - Travel recommendations
   - Reservation modifications

3. **Enterprise Pack**:
   - Meeting scheduling
   - Document management
   - Task assignment
   - Knowledge base queries

### 4.5 Advanced Template Management System *(P1)*

The Template Management System is a comprehensive solution for managing, versioning, and distributing templates across the Coherence ecosystem. It encompasses five key categories of templates, each serving a distinct purpose in the system's processing pipeline.

#### 4.5.1 Template Categories

| Category | Purpose | Example Key | Format |
|----------|---------|------------|--------|
| **Intent Router** | Guide LLM to recognize intents | `INTENT_ROUTER_V1` | Prompt template with JSON output format |
| **Parameter Completion** | Extract/request parameters | `PARAM_COMPLETE_V1` | Conversational prompt with parameter extraction logic |
| **Retrieval** | Knowledge base context integration | `RETRIEVAL_V1` | RAG prompt with citation requirements |
| **Response Generation** | Format user-facing replies | `RESPONSE_GEN_V1` | Output template with customizable tone/style |
| **Action Error Handling** | Manage failures gracefully | `ACTION_ERROR_V1` | Error categorization and user communication |

#### 4.5.2 Scope Hierarchy & Templating System

Templates follow a strict scope hierarchy that determines precedence:

```
Global Templates (baseline)
       ↓
  Pack Templates (industry-specific)
       ↓
Tenant Templates (customer-specific)
```

**Inheritance Model**:
- **Override**: A tenant template completely replaces a pack/global template
- **Extension**: A template can `{% extends "base_template" %}` and override only specific blocks
- **Inclusion**: Templates can include other templates with `{% include "partial_template" %}`

**Template Engine**:
- Jinja2-based with custom extensions for secure sandboxing
- Context variables explicitly defined per template type
- Custom filters for text processing (e.g., `{{ text | remove_phi }}`)

**Example Template (Parameter Completion)**:
```jinja
{# PARAM_COMPLETE_V1 for appointment scheduling #}
{% extends "base/param_complete.jinja" %}

{% block system_instruction %}
You are a helpful assistant specialized in scheduling appointments.
Extract or request missing parameters for the appointment intent.
Required parameters: date, time, duration, participants.
Optional parameters: location, agenda, recurring.
{% endblock %}

{% block prompt %}
User message: "{{ user_message }}"
Current parameters: {{ current_params | tojson }}
Missing required parameters: {{ missing_params | join(", ") }}

If parameters are missing, ask for them conversationally.
If all required parameters are present, confirm details with the user.
{% endblock %}

{% block response_format %}
{
  "extracted_params": {}, // any parameters extracted from user message
  "reply": "", // conversational response to user
  "complete": false, // true if all required parameters are present
  "confidence": 0.0 // confidence in parameter extraction (0.0-1.0)
}
{% endblock %}
```

#### 4.5.3 Template Database Schema

| Table | Schema |
|-------|--------|
| **templates** | `id uuid PK`, `scope enum`, `scope_id uuid` (NULL for global), `key text`, `category enum`, `body text`, `language varchar(5)`, `version int`, `created_at ts`, `updated_at ts`, `created_by uuid` |
| **template_versions** | `template_id uuid FK`, `version int`, `body text`, `editor_id uuid`, `edited_at ts`, `change_reason text` |
| **template_tests** | `template_id uuid FK`, `test_name text`, `test_input jsonb`, `expected_output jsonb`, `created_at ts` |
| **template_dependencies** | `template_id uuid FK`, `depends_on_template_id uuid FK`, `dependency_type enum` (extends, includes) |

#### 4.5.4 Template Admin Interface

The Template Admin Interface provides powerful tools for creating, testing, and managing templates:

**Monaco Editor Integration**:
- Syntax highlighting for Jinja2 templates
- Schema validation for template structure
- Auto-completion for context variables
- Linting for template best practices

**Template Testing**:
- Interactive playground to test templates with sample inputs
- A/B comparison between template versions
- Batch testing against predefined test cases
- Automated regression tests

**Template Versioning**:
- Complete version history with diff view
- Comments on changes with change reason tracking
- Rollback to previous versions
- Version tagging for releases

**Approval Workflow**:
- Draft/review/publish states for templates
- Approval requirements for production templates
- Staging environment for template testing
- Automated validation checks

**Screenshot: Template Editor**
```
┌────────────────────────────────────────────────────────────────┐
│ INTENT_ROUTER_V2 (Tenant: MedClinic) - Version 3  ✓ Published  │
├────────────────────────────────────────────────────────────────┤
│ ┌────────────────────┐  ┌─────────────────────────────────────┐│
│ │  Monaco Editor     │  │  Preview & Test                     ││
│ │                    │  │                                     ││
│ │ {# INTENT_ROUTER_V2│  │ Input: "I need to refill my         ││
│ │    Healthcare Pack │  │         medication"                 ││
│ │ #}                 │  │                                     ││
│ │ {% extends "base/in│  │ Output:                             ││
│ │                    │  │ {                                   ││
│ │ {% block system_ins│  │   "intent": "medication_refill",    ││
│ │ You are a healthcar│  │   "confidence": 0.98,               ││
│ │ Your task is to ide│  │   "params": {                       ││
│ │ ...                │  │     "medication_name": null         ││
│ │                    │  │   }                                 ││
│ └────────────────────┘  └─────────────────────────────────────┘│
│                                                                │
│ ┌──────────────────────────────────────────────────────────┐   │
│ │ Tests: ● 12 Passing  ○ 1 Failing                         │   │
│ │ ✓ recognize_refill   ✓ recognize_appointment  ✘ ambiguous │   │
│ └──────────────────────────────────────────────────────────┘   │
│                                                                │
│ History: V1 (Initial) → V2 (Add symptoms) → V3 (Fix bug)       │
└────────────────────────────────────────────────────────────────┘
```

#### 4.5.5 Template Distribution & Hot Reload

**Distribution Pipeline**:
1. Template saved to database
2. Self-validation tests run automatically
3. Pub/sub event `template.updated` emitted
4. Pod cache invalidated across cluster
5. For intent templates: Index rebuild job triggered
6. Template change audit logged

**Pod-Level Caching**:
- In-memory LRU cache (50 templates, 5-minute TTL)
- Keyed by `scope + key + version`
- Redis pub/sub for cross-pod invalidation
- Shadow cache for atomic updates

**Vectorization**:
- Templates embedded via same model as intents
- Stored in Qdrant collection `template_idx_<scope>`
- Used for semantic search in Admin UI
- Enables finding similar templates across tenants (Super-Admin only)

**API Contract**:

```http
# Template CRUD
POST /admin/templates
  body: { scope, scope_id, key, category, body, language }
  
PUT /admin/templates/{id}
  body: { body, change_reason }
  
GET /admin/templates/{id}
  response: { id, scope, scope_id, key, category, body, language, version, created_at, updated_at, created_by }
  
DELETE /admin/templates/{id}
  
# Template Version Management
GET /admin/templates/{id}/versions
  response: [ { version, editor_id, edited_at, change_reason }, ... ]
  
GET /admin/templates/{id}/versions/{version}
  response: { version, body, editor_id, edited_at, change_reason }
  
POST /admin/templates/{id}/rollback
  body: { version, reason }
  
GET /admin/templates/{id}/diff
  query: from=3&to=4
  response: { unified_diff, line_by_line_diff }
  
# Template Testing
POST /admin/templates/{id}/test
  body: { test_input: { ... } }
  response: { output: { ... }, execution_time_ms, logs }
  
GET /admin/templates/{id}/tests
  response: [ { test_name, test_input, expected_output, last_result }, ... ]
  
POST /admin/templates/{id}/tests
  body: { test_name, test_input, expected_output }
  
# Template Search
GET /admin/templates/search
  query: q=appointment%20scheduling
  response: [ { id, key, score, preview }, ... ]
```

#### 4.5.6 RBAC & Permissions

| Role | Permissions |
|------|------------|
| **Super-Admin** | Full CRUD on all scopes, approve global templates |
| **Pack Maintainer** | CRUD on pack templates for assigned packs |
| **Tenant Admin** | CRUD on tenant templates for their tenant |
| **Template Author** | Create/edit tenant templates (requires approval) |
| **Template Viewer** | View templates for assigned scope |

### 4.6 OpenAPI Integration System *(P1)*

The OpenAPI Integration System automatically transforms OpenAPI/Swagger specifications into Coherence Actions with minimal configuration, enabling seamless integration with external APIs.

#### 4.6.1 OpenAPI Adapter Overview

| Component | Purpose |
|-----------|---------|
| **Spec Parser** | Ingest and validate OpenAPI specifications (v2 and v3) |
| **Schema Mapper** | Transform OpenAPI schemas to Coherence parameter schemas |
| **Action Generator** | Generate Action classes from API endpoints |
| **Intent Mapper** | Create intent definitions that map to generated actions |
| **Integration Registry** | Manage API integrations with auth credentials and rate limits |

#### 4.6.2 Integration Workflow

**Step 1: Spec Import**
- Upload OpenAPI JSON/YAML spec via Admin UI or API endpoint
- System validates the specification for correctness
- Detects authentication methods (API key, OAuth2, etc.)

**Step 2: Schema Mapping**
- Maps OpenAPI parameter and response schemas to Coherence types
- Generates JSON schema validation rules for parameters
- Supports complex objects, arrays, and nested properties
- Validates required vs. optional parameters

**Step 3: Action Generation**
- Generates Python action class definitions for each API endpoint
- Creates appropriate class hierarchy based on endpoint paths
- Implements auth handling based on spec security definitions
- Adds error handling with appropriate retry logic

**Step 4: Intent Configuration**
- Creates default intent configuration for each endpoint
- Generates sample user utterances for intent training
- Maps parameters to conversational extraction patterns
- Defines parameter validation rules

**Step 5: Deployment**
- Previews generated actions and intents before activation
- Deploys to staging environment for testing
- Performs automated validation against test endpoints
- Promotes to production when validation passes

#### 4.6.3 Admin Interface

The API Integration Manager provides a visual interface for managing OpenAPI integrations:

```
┌────────────────────────────────────────────────────────────────┐
│ API Integration Manager                                         │
├────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐                                            │
│ │ Available APIs  │  ┌────────────────────────────────────────┐│
│ │                 │  │ Electronic Health Record API v2.1      ││
│ │ ● EHR API       │  │ Status: ✓ Active                       ││
│ │ ● Payment API   │  │                                        ││
│ │ ● Calendar API  │  │ Endpoints: 32 total                    ││
│ │ ● Email API     │  │ • 12 Patient data (GET, POST, PUT)     ││
│ │                 │  │ • 8 Appointments (GET, POST, DELETE)   ││
│ │ + Import New    │  │ • 6 Medications (GET, POST)            ││
│ │                 │  │ • 4 Lab Results (GET)                  ││
│ └─────────────────┘  │ • 2 Billing (GET)                      ││
│                      │                                        ││
│                      │ Authentication: OAuth2                 ││
│                      │ Base URL: https://api.ehr-system.com/v2││
│                      │                                        ││
│                      │ [Edit Auth] [Test Connection] [Disable]││
│                      └────────────────────────────────────────┘│
│                                                                │
│ ┌────────────────────────────────────────────────────────────┐ │
│ │ Generated Actions & Intents                                │ │
│ │                                                            │ │
│ │ ● GET /patients/{id}          → Intent: get_patient_info   │ │
│ │   Parameters: patient_id                                   │ │
│ │   [Edit Intent] [Test Action]                              │ │
│ │                                                            │ │
│ │ ● POST /appointments          → Intent: schedule_appointment│ │
│ │   Parameters: date, time, patient_id, doctor_id, reason    │ │
│ │   [Edit Intent] [Test Action]                              │ │
│ │                                                            │ │
│ │ [Regenerate All] [Deploy to Production]                    │ │
│ └────────────────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────────────┘
```

#### 4.6.4 Code Generation

The system auto-generates action code from OpenAPI specs. Example of generated code:

```python
# Auto-generated from EHR API OpenAPI v2.1
# Endpoint: POST /appointments
@intent_handler("schedule_appointment", schema=AppointmentSchema, roles=["clinician", "assistant"])
class CreateAppointmentAction(SyncAction):
    """Schedule a new appointment in the EHR system."""
    
    # Generated from OpenAPI spec
    api_base_url = "https://api.ehr-system.com/v2"
    endpoint = "/appointments"
    method = "POST"
    
    async def execute(self):
        # Parameter validation (auto-generated from schema)
        if not self.params.patient_id:
            raise ValidationError("Patient ID is required")
        if not self.params.date:
            raise ValidationError("Appointment date is required")
            
        # Auth handling (auto-configured)
        auth_header = await self.get_oauth_token(scope="appointments:write")
        
        # API call with proper error handling
        try:
            response = await self.http_client.post(
                f"{self.api_base_url}{self.endpoint}",
                json={
                    "patientId": self.params.patient_id,
                    "scheduledDate": self.params.date,
                    "scheduledTime": self.params.time,
                    "doctorId": self.params.doctor_id,
                    "reasonForVisit": self.params.reason
                },
                headers={
                    "Authorization": f"Bearer {auth_header}",
                    "Content-Type": "application/json",
                    "X-Request-ID": self.request_id
                },
                timeout=5.0
            )
            
            # Response handling (auto-generated from schema)
            if response.status_code == 201:
                appointment = response.json()
                return f"Appointment scheduled for {appointment['scheduledDate']} at {appointment['scheduledTime']} with Dr. {appointment['doctorName']}."
            elif response.status_code == 409:
                return "That time slot is not available. Please choose another time."
            else:
                # Generic error handling with proper logging
                self.log.error(f"EHR API error: {response.status_code}", extra={"response": response.text})
                return "There was a problem scheduling your appointment. Please try again later."
                
        except Exception as e:
            # Error handling with retry logic
            self.log.exception("Error calling EHR API", extra={"error": str(e)})
            raise ActionExecutionError("Failed to schedule appointment due to EHR system error")
```

#### 4.6.5 Intent Schema Generation

Automatically generates intent configurations from endpoint definitions:

```yaml
# Auto-generated from EHR API OpenAPI v2.1
# Endpoint: POST /appointments
intent:
  id: schedule_appointment
  name: Schedule Appointment
  description: Schedule a new appointment in the EHR system
  roles:
    - clinician
    - assistant
  examples:
    - Schedule an appointment for patient John Smith
    - Book a follow-up visit with Dr. Jones
    - I need to set up an appointment for tomorrow
    - Create a new appointment for physical therapy
  parameters:
    patient_id:
      type: string
      required: true
      description: Patient identifier
      examples:
        - P123456
        - John Smith's patient ID
    date:
      type: string
      format: date
      required: true
      description: Appointment date
      examples:
        - tomorrow
        - next Tuesday
        - June 15th
    time:
      type: string
      format: time
      required: true
      description: Appointment time
      examples:
        - 2:30 PM
        - in the afternoon
        - morning
    doctor_id:
      type: string
      required: false
      description: Doctor identifier
    reason:
      type: string
      required: false
      description: Reason for appointment
  handler:
    type: sync_action
    class: CreateAppointmentAction
```

#### 4.6.6 Authentication & Security

The Integration System provides robust authentication handling:

| Authentication Type | Implementation |
|--------------------|----------------|
| **API Key** | Securely stores API keys in encrypted format with tenant isolation |
| **OAuth2** | Full OAuth flow support with token management and refresh logic |
| **Bearer Token** | JWT validation and handling |
| **Basic Auth** | Encrypted credential storage |
| **Custom Headers** | Support for proprietary auth schemes |

**Security Features**:
- All credentials stored encrypted in the database
- Tenant-specific auth configurations with RLS protection
- Audit logging for all API access
- Rate limiting to prevent abuse
- IP allowlisting for sensitive integrations

#### 4.6.7 Rate Limiting & Quotas

Automatically implements best-practice API consumption patterns:

- Respects API rate limits specified in OpenAPI specs
- Implements token bucket algorithm for rate limiting
- Provides tenant-specific quota management
- Graceful backoff during rate limit errors
- Priority queuing for critical operations

#### 4.6.8 Integration Registry

Manages all API integrations in a central registry:

| Table | Schema |
|-------|--------|
| **api_integrations** | `id uuid PK`, `tenant_id uuid`, `name text`, `version text`, `openapi_spec jsonb`, `base_url text`, `created_at ts`, `updated_at ts`, `status enum` |
| **api_auth_configs** | `integration_id uuid FK`, `auth_type enum`, `credentials jsonb encrypted`, `scopes text[]`, `refresh_token text encrypted`, `expires_at ts` |
| **api_endpoints** | `id uuid PK`, `integration_id uuid FK`, `path text`, `method text`, `operation_id text`, `action_class_name text`, `intent_id text`, `enabled bool` |
| **api_rate_limits** | `endpoint_id uuid FK`, `requests_per_min int`, `burst_size int`, `cooldown_sec int` |

### 4.7 Developer Experience & SDK *(P1)*

The Developer Experience provides tools, SDKs, and documentation for extending and customizing Coherence.

#### 4.7.1 Coherence SDK

The SDK enables developers to create custom components, actions, and integrations:

| Component | Purpose |
|-----------|---------|
| **Action SDK** | Framework for building custom action handlers |
| **Template SDK** | Tools for creating and testing templates |
| **API Client** | Typed client libraries for Coherence APIs |
| **Testing Framework** | Mock services and test harnesses |
| **CLI Tools** | Command-line utilities for common operations |

**Language Support**:
- Python (primary/reference implementation)
- TypeScript/JavaScript
- Java
- Go
- .NET

**Example: Custom Action in Python**
```python
from coherence.sdk.actions import SyncAction, intent_handler
from coherence.sdk.schema import Parameter, ResponseSchema
from coherence.sdk.validation import validate_params

@intent_handler(
    intent_id="custom_analytics",
    description="Analyze custom metrics from proprietary system",
    examples=["show me yesterday's metrics", "analyze last week's performance"]
)
class CustomAnalyticsAction(SyncAction):
    # Define parameter schema
    time_period = Parameter(
        type="string", 
        required=True,
        description="Time period to analyze",
        examples=["yesterday", "last week", "Q1"]
    )
    
    metric_type = Parameter(
        type="string",
        required=False,
        description="Type of metric to analyze",
        enum=["revenue", "usage", "engagement", "conversion"]
    )
    
    async def execute(self):
        # Parameter validation
        validate_params(self.params)
        
        # Business logic
        result = await self.proprietary_client.get_analytics(
            period=self.params.time_period,
            metric=self.params.metric_type or "all"
        )
        
        # Format response
        return {
            "summary": f"Analysis for {self.params.time_period}",
            "metrics": result.metrics,
            "trend": result.trend_direction,
            "visualization_url": result.chart_url
        }
```

#### 4.7.2 Local Development Environment

**Components**:
- Docker Compose setup for local development
- Local instance of Ollama for LLM testing
- Embedded Qdrant for vector testing
- Mock services for external dependencies
- Hot-reload for templates and actions

**Local Test Flow**:
1. Run `coherence dev start` to initialize environment
2. Edit code or templates with live reloading
3. Test via CLI, Swagger UI, or test console
4. Run test suite with `coherence test`
5. Package with `coherence build`

#### 4.7.3 CI/CD Integration

**Pipeline Support**:
- GitHub Actions
- GitLab CI
- Jenkins
- Azure DevOps
- CircleCI

**Build Process**:
1. Validate schema and configuration files
2. Run unit tests with mocks
3. Run integration tests with containers
4. Package components for deployment
5. Deploy to staging environment
6. Run acceptance tests
7. Deploy to production

#### 4.7.4 Documentation & Samples

**Documentation Tools**:
- Auto-generated API docs from code
- Interactive Swagger UI
- Executable examples in Jupyter notebooks
- Template galleries with annotated examples
- Video tutorials for common tasks

**Sample Projects**:
- Healthcare integration starter
- Enterprise workflow demo
- Travel booking example
- Custom action showcase
- Multi-tenant demo

### 4.8 VectorIntentMatcher *(P1)*

| Aspect | Spec |
|--------|------|
| Model | `nomic‑embed‑text` via Ollama `/api/embeddings`. |
| Index creation | CLI `rebuild_intent_index --tenant <tid> --role patient`. |
| Threshold τ | Stored per tenant+role (`tenant_thresholds`). Default 0.85. |
| Shadow index | Rebuilds happen to shadow collection, then atomic swap on completion. |
| Refresh cycle | Auto-refresh triggered by template changes or on schedule (configurable). |
| Performance | Vector operations batched for efficiency; 10,000 intents searched in <30ms. |
| Scaling | Distributed index sharding for tenants with >100k intents. |

### 4.9 LLM IntentRouter (Tier 2) *(P2)*

* Engine: `mistral:7b‑instruct‑q4_K_M` (Ollama)
* Prompt key: `INTENT_ROUTER_V{version}` in TemplateStore
* JSON‑only response enforced by `response_format` + logit bias
* Token budget ≤ 450; completion target ≤ 1 token
* Confidence score required in response
* Batched for throughput on multi-GPU systems
* Fallback to CPU if GPU unavailable (with latency warning)

### 4.10 RAG Augmentation (Tier 3) *(P2)*

* Triggered when Tier 1 and 2 confidence below threshold
* Searches knowledge base using embedding similarity
* Augments prompt with relevant context from knowledge base
* Uses more powerful models (OpenAI) for high-quality understanding
* Structured output ensures action compatibility
* Logs novel requests for future intent training

### 4.11 Parameter Completion Service *(P2)*

* Conversational parameter collection guided by LLM
* Validates params against schema from catalogue
* Stores/updates **PendingIntent** (DB) with JSON payload & expiry
* Loop limit 3; TTL default 15 min (configurable per tenant)
* On timeout emits `intent.timeout` event
* All operations enforce RLS row writes for tenant isolation
* Supports context carryover between related intents

### 4.12 Unified Action Registry *(P2)*

The Action Registry supports three categories of actions:

1. **Synchronous Actions**: Single API calls or DB operations
   * Executed within the request cycle
   * Hard timeout limit of 5 seconds
   * Result returned directly to user

2. **Asynchronous Workflows**: Multi-step processes
   * Queued for background execution
   * Progress tracking via webhook callbacks
   * Status polling endpoint available
   * Results delivered via subscribed channels

3. **External Integrations**: Third-party action providers
   * Secured webhook protocol
   * OAuth2 authorization flow
   * Rate limiting and quotas per provider
   * Failure retry with exponential backoff

**Implementation**:
```python
# Synchronous action example
@intent_handler("log_weight", schema=WeightLogPayload, roles=["patient"])
class WeightLogAction(SyncAction):
    async def execute(self):
        await api_client.post("/weights", json=self.params)
        return "Weight saved!"

# Async workflow example
@intent_handler("schedule_appointment", schema=AppointmentPayload, roles=["patient"])
class ScheduleAppointmentWorkflow(AsyncWorkflow):
    async def execute(self):
        # Queue the multi-step workflow
        workflow_id = await self.queue_workflow([
            CheckAvailabilityStep(self.params),
            ReserveSlotStep(self.params),
            SendNotificationsStep(self.params)
        ])
        return f"Scheduling your appointment. I'll notify you when confirmed. (Ref: {workflow_id})"
    
# External integration example
@intent_handler("book_flight", schema=FlightBookingPayload, roles=["traveler"])
class BookFlightAction(ExternalAction):
    provider_id = "travelapi"
    endpoint = "/flights/book"
    
    async def pre_execute(self):
        # Custom validation before sending to external provider
        if self.params.departure_date < datetime.now():
            raise ValidationError("Departure date must be in the future")
        
    async def handle_callback(self, result):
        # Process webhook result from provider
        if result.status == "confirmed":
            return f"Flight booked! Confirmation: {result.booking_reference}"
        else:
            return f"Booking status: {result.status}. {result.message}"
```

### 4.13 API‑Key Management *(P0 Foundation)*

| Aspect | Spec |
|--------|------|
| Storage | Table `api_keys` (RLS): `id uuid PK`, `tenant_id uuid`, `label text`, `key_hash char(64)`, `created_at ts`, `last_used_at ts`, `revoked bool`, `expires_at ts` (nullable). |
| Generation | `POST /admin/api-keys` (body: `tenant_id`, optional `label`, optional `expires_at`). Returns **plaintext key once**. |
| Rotation | `POST /admin/api-keys/rotate/{id}` → new key, old key valid for `grace_period` (default 24 h). |
| Revocation | `DELETE /admin/api-keys/{id}` — sets `revoked=true`. |
| Listing | `GET /admin/api-keys?tenant_id=` — Tenant Admin sees own keys, Super‑Admin sees all. |
| Auth header | `X‑API‑Key: <key>` (base64‑url, 44 chars). |
| Rate limit | Keys inherit tenant quota; misuse increments `tenant_throttle_total`. |
| Audit | Key create/rotate/revoke events logged (`actor_id`, `key_id`). |
| UI Flow | Admin UI → **Settings › API Keys**: list keys, show last used, rotate, copy once modal. |

### 4.14 Error Handling Framework *(P2)*

A comprehensive approach to error management across the system:

#### 4.14.1 Error Taxonomy

| Error Category | Examples | Handling Strategy |
|----------------|----------|-------------------|
| **Transient Infrastructure** | Network timeouts, temporary DB unavailability | Automatic retry with backoff |
| **Authentication/Authorization** | Invalid tokens, missing permissions | Clear error with remediation steps |
| **Input Validation** | Schema violations, missing parameters | User-friendly explanation with examples |
| **Business Logic** | Scheduling conflicts, insufficient funds | Alternative suggestions when possible |
| **External System** | Third-party API failures | Graceful degradation, alternate paths |
| **System Limits** | Rate limits, quota exhaustion | Throttling, priority queue for critical operations |
| **Unexpected Errors** | Bugs, edge cases | Safe failure mode, detailed logging for debugging |

#### 4.14.2 Circuit Breakers

Circuit breakers protect the system from cascading failures:

- **Implementation**: Custom decorator `@circuit_breaker` for critical services
- **States**: Closed (normal), Open (failing), Half-open (testing recovery)
- **Configuration**: Failure threshold, reset timeout, success threshold
- **Granularity**: Per tenant, per API endpoint, per action
- **Fallbacks**: Default responses, cached responses, degraded functionality

#### 4.14.3 Retry Strategies

Intelligent retry mechanisms for transient failures:

- **Exponential backoff**: Base delay doubles after each attempt
- **Jitter**: Random variance to prevent thundering herd
- **Timeout limits**: Maximum retry duration to prevent indefinite waiting
- **Retry count**: Configurable per operation type
- **Idempotency**: Guaranteed safety for repeated operations

#### 4.14.4 Graceful Degradation

The system maintains functionality during partial outages:

- **Tier fallbacks**: Use lower quality but more reliable services when needed
- **Cached responses**: Serve stale data with freshness indicators
- **Minimal modes**: Core functions continue during maintenance
- **Progressive enhancement**: Add features back as services recover
- **User communication**: Clear status updates during degraded operation

### 4.15 LLM Evaluation Framework *(P2)*

Tools and methodologies for evaluating and selecting LLM providers:

#### 4.15.1 Benchmark Suite

Standardized tests to compare LLM performance:

- **Intent recognition accuracy**: Correctly identifying user intents
- **Parameter extraction**: Extracting structured data from natural language
- **Instruction following**: Adhering to system prompts and constraints
- **Output formatting**: Consistent JSON/structured responses
- **Context utilization**: Effectively using provided context
- **Hallucination avoidance**: Sticking to facts and provided information
- **Latency/throughput**: Response time and request handling capacity
- **Token efficiency**: Achieving results with minimal token usage

#### 4.15.2 Evaluation Pipeline

Automated testing infrastructure:

- **Test datasets**: Categorized by domain, complexity, language
- **Scoring system**: Weighted metrics based on use case importance
- **A/B comparisons**: Direct evaluation between providers
- **Cost modeling**: Token usage × pricing for TCO analysis
- **Reporting dashboard**: Visual comparison of metrics

#### 4.15.3 Provider Management

Framework for managing multiple LLM providers:

- **Provider abstraction**: Common interface for different LLMs
- **Automatic failover**: Switch providers when primary is unavailable
- **Traffic splitting**: Route requests based on cost, quality, latency
- **Quota management**: Distribute usage across providers to optimize cost
- **Version pinning**: Lock to specific model versions for consistency

### 4.16 Billing Hooks *(P3)*

* Prometheus counters: `tenant_llm_tokens_total`, `tenant_embedding_tokens_total`
* Granular tracking by tier: tier1_hits, tier2_hits, tier3_hits
* Cron job aggregates into `tenant_usage` table monthly
* API `/admin/billing/report?month=YYYY‑MM` returns CSV
* Cost optimization suggestions based on usage patterns

---

## 5. Data Artefacts

* **Tenant table** (`id`, `name`, `industry_pack`, `compliance_tier`)
* **Quota table** (`tenant_id`, `req_per_min`, `llm_tokens_per_month`)
* **Intent Catalogue YAML** per pack + per tenant override
* **Templates** (scope hierarchy with versioning)
* **PendingIntent** (RLS for conversation state)
* **Audit log** (RLS, 6‑year HIPAA retention)
* **Knowledge Base** (tenant-isolated for RAG)
* **Action Results** (success/failure metrics with error categorization)
* **API Integrations** (OpenAPI specs and auth configurations)

---

## 6. API Contracts

### 6.1 `POST /v1/resolve` *(multi‑tenant)*

Headers:
```
X‑Tenant‑ID: <uuid>
X‑API‑Key: <key>   # optional if JWT bearer token is supplied
Authorization: Bearer <jwt>   # optional if X‑API‑Key supplied
```

Body:
```jsonc
{
  "conversation_id": "uuid",
  "user_id": "uuid",
  "role": "patient",
  "tenant_id": "t1",
  "message": "I weigh 180 lb",
  "context": {
    "timezone": "America/Los_Angeles",
    "locale": "en-US",
    "devices": ["mobile"],
    "previous_intents": ["log_blood_pressure"]
  }
}
```

**Responses**:
* `200` – `{"kind":"reply","text":"Weight saved!"}`
* `200` – `{"kind":"ask","field":"weight","question":"What's your exact weight?"}`
* `200` – `{"kind":"intent_clarification","options":[{"id":"log_weight","text":"Log your weight"},{"id":"track_medication","text":"Record medication"}]}`
* `202` – `{"kind":"async","workflow_id":"uuid","status_url":"/v1/status/uuid"}`
* `503` – Unable to reach vector DB or LLM

### 6.2 `POST /v1/continue`

Ingests user answer; same response schema as `/v1/resolve`.

### 6.3 `GET /v1/status/{workflow_id}`

Checks status of async workflows.

**Response**:
```jsonc
{
  "workflow_id": "uuid",
  "status": "running|completed|failed",
  "progress": 0.75,
  "current_step": "Checking doctor availability",
  "result": {} // Only present when completed
}
```

### 6.4 Admin APIs

* `/admin/templates` CRUD (JWT `role=admin`)
* `/admin/catalogues` upload & version list
* `/admin/packs` CRUD for industry packs
* `/admin/integrations` OpenAPI spec management
* `/metrics` Prometheus
* `/healthz` deep health: Postgres, Redis, Qdrant, Ollama

### 6.5 `GET /admin/billing/report`

Query `month=YYYY‑MM` → CSV with columns: `tenant_id, llm_tokens, embed_tokens, vector_hits`.

### 6.6 OpenAPI Integration APIs

```http
# Import an OpenAPI specification
POST /admin/integrations
  body: { name, version, openapi_spec (JSON/YAML), tenant_id }
  response: { integration_id, imported_endpoints_count }

# Get integration details
GET /admin/integrations/{id}
  response: { id, name, version, base_url, endpoints, status }

# Configure authentication
POST /admin/integrations/{id}/auth
  body: { auth_type, credentials, scopes }
  
# Test connection
POST /admin/integrations/{id}/test
  response: { success, message, latency_ms }
  
# Generate/update actions and intents
POST /admin/integrations/{id}/generate
  response: { generated_actions, generated_intents }

# Enable/disable integration
PUT /admin/integrations/{id}/status
  body: { status: "enabled"|"disabled" }
```

---

## 7. Non‑Functional Requirements

| Category | Target |
|----------|--------|
| **Latency** | Tier 1: <100ms p95, Tier 2: <300ms p95, Tier 3: <500ms p95 |
| **Throughput** | 1000 req/s per replica (vector only), 300 req/s (all tiers) |
| **Reliability** | 99.99% for Tier 1, 99.95% for Tier 2, 99.9% overall |
| **Scalability** | Horizontal scaling via k8s; stateless except for Redis session store |
| **Security** | RLS enforced; mTLS; data encrypted at rest; audit logging |
| **Compliance tiers** | HIPAA: PHI masking, 6‑year retention. GDPR: right‑to‑erasure API |
| **Billing ready** | Usage metrics emitted per tenant with tier breakdown |

---

## 8. Observability

### 8.1 Metrics & Monitoring

* Prometheus counters with tenant and tier labels
* Metrics: `intent_match_total`, `intent_latency_seconds`, `param_completion_iterations`, `action_success_total`, `action_failure_total`
* API integration metrics: `api_call_total`, `api_latency_seconds`, `api_error_total`
* LLM usage metrics: `llm_tokens_total`, `llm_latency_seconds`, `llm_error_total`
* System metrics: `cpu_usage`, `memory_usage`, `disk_io`, `network_io`

### 8.2 Dashboards

* **Executive Dashboard**: High-level system health and KPIs
* **Tenant Dashboard**: Per-tenant usage, performance, and billing metrics
* **Operations Dashboard**: Detailed system performance and resource utilization
* **Developer Dashboard**: API usage, error rates, and performance details
* **Security Dashboard**: Authentication attempts, authorization failures, and audit events

### 8.3 Tracing & Logging

* OpenTelemetry traces tagged with `request_id`, `tenant_id`, `intent_id`
* Structured logging with consistent metadata
* Log levels: ERROR, WARN, INFO, DEBUG, TRACE
* Context propagation across service boundaries
* Sampling strategies for high-volume production environments

### 8.4 Alerting

* Threshold-based alerts for critical metrics
* Anomaly detection for unusual patterns
* Escalation policies for different severity levels
* On-call rotation integration
* Notification channels: Email, SMS, Slack, PagerDuty

### 8.5 Synthetic Testing

* Canary testing with sample intents per tenant
* Scheduled health checks for critical paths
* End-to-end transaction monitoring
* Regional availability testing
* Load testing for capacity planning

---

## 9. Operational Concerns

### 9.1 Deployment Strategies

Coherence employs a multi-stage deployment process to ensure reliability:

**Environment Pipeline**:
1. **Development**: Individual developer environments with mocked dependencies
2. **Integration**: Shared environment for testing feature combinations
3. **Staging**: Production-like environment for final validation
4. **Canary**: Small percentage of production traffic for real-world testing
5. **Production**: Full deployment with gradual rollout

**Deployment Methods**:
- **Blue/Green**: Parallel environments with atomic cutover
- **Canary**: Incremental traffic routing to new version
- **Feature Flags**: Runtime toggling of new capabilities
- **A/B Testing**: Controlled exposure for performance comparison

**Rollback Procedures**:
- Automated health checks trigger automatic rollback
- Manual rollback procedure documented for each component
- Database migration reversibility testing
- State reconciliation processes

### 9.2 Backup & Restore

Comprehensive data protection strategy for all persistent state:

**Backup Schedule**:
- PostgreSQL: Full nightly backup, WAL archiving for point-in-time recovery
- Redis: RDB snapshots every 15 minutes, AOF for transaction logging
- Qdrant: Full snapshot every 6 hours
- Configuration: Version-controlled and backed up with each change

**Retention Policies**:
- Daily backups: 14 days
- Weekly backups: 3 months
- Monthly backups: 1 year
- Yearly backups: 7 years (for HIPAA compliance)

**Restore Testing**:
- Weekly automated restore validation
- Quarterly full disaster recovery exercise
- Regular corruption testing and recovery validation

### 9.3 Disaster Recovery

Comprehensive planning for system-wide recovery:

**Recovery Objectives**:
- Recovery Time Objective (RTO): 1 hour for critical functions
- Recovery Point Objective (RPO): 5 minutes for transaction data
- Continuity of Operation: Regional failover within 15 minutes

**Disaster Scenarios**:
- Single availability zone failure
- Complete regional outage
- Data corruption
- Security breach
- Third-party service outage

**Recovery Procedures**:
- Detailed runbooks for each scenario
- Regular tabletop exercises
- Automated recovery for common failures
- Multi-region deployment options for mission-critical tenants

### 9.4 Maintenance Windows

Planned maintenance with minimal disruption:

**Maintenance Types**:
- Database migrations
- Index rebuilds
- Security patches
- Infrastructure updates
- Component version upgrades

**Scheduling Policy**:
- Low-impact maintenance: Rolling updates with no downtime
- Medium-impact maintenance: Off-peak hours with notification
- High-impact maintenance: Scheduled downtime with 7-day notice

**Communication Plan**:
- Maintenance calendar accessible to tenants
- Automated notifications before and after maintenance
- Status page updates during maintenance
- Post-maintenance summary reports

### 9.5 Scaling Strategy

Component-specific scaling approaches:

| Component | Scaling Approach | Scaling Trigger | Scaling Limit |
|-----------|------------------|----------------|---------------|
| **API Gateway** | Horizontal | Requests per second | Cost-based |
| **Vector Service** | Horizontal + Vertical | Query latency | Memory-bound |
| **LLM Service** | Horizontal (GPU pods) | Queue depth | GPU availability |
| **Parameter Service** | Horizontal | Active conversations | None |
| **Action Service** | Horizontal | Action execution rate | None |
| **Database** | Vertical + Read replicas | CPU/IO utilization | Storage class |
| **Vector DB** | Cluster expansion | Collection size/latency | Memory-bound |
| **Redis** | Cluster + Sharding | Memory utilization | Network I/O |

**Global Distribution**:
- Multi-region deployment for latency reduction
- Data sovereignty compliance through regional isolation
- Active-active configuration for high availability
- Traffic routing based on geographical proximity

---

## 10. Migration Framework

### 10.1 Schema Evolution

Strategies for evolving database schemas without disruption:

**Migration Types**:
- **Additive**: New fields, tables, or indices (non-breaking)
- **Transformative**: Data restructuring or type changes (potentially breaking)
- **Subtractive**: Field or table removal (breaking)

**Migration Process**:
1. Database version tracked in schema_versions table
2. Migration scripts stored in version control
3. Automated testing against sample datasets
4. Execution during maintenance windows
5. Verification and automatic rollback on failure

**Compatibility Guarantees**:
- Backward compatibility for N-1 version
- Read compatibility for N-2 version
- Migration path from any version to latest

### 10.2 API Versioning

API evolution strategy to maintain compatibility:

**Versioning Strategy**:
- API version in URL path: `/v1/resolve`, `/v2/resolve`
- Explicit request/response schema versioning
- Deprecation headers for sunset features
- Documentation of breaking vs. non-breaking changes

**Compatibility Timeline**:
- New major version: Minimum 6 months overlap with previous
- Security patches: All supported versions
- Feature additions: Latest version only
- Deprecation notices: 90 days before removal

**Client Transition**:
- SDK support for multiple versions
- Migration guides between versions
- Automated compatibility testing
- Version-specific documentation

### 10.3 Template Migration

Safe evolution of the template ecosystem:

**Template Version Compatibility**:
- Backward compatible changes favored
- Breaking changes trigger new template key
- Deprecation warnings for sunset templates
- Automated migration paths where possible

**Migration Support**:
- Template conversion tools
- Side-by-side testing environment
- Bulk migration utilities
- Gradual rollout options

**Template Dependencies**:
- Explicit version dependencies
- Compatibility checking at runtime
- Graceful fallback to base templates
- Dependency warnings in UI

### 10.4 LLM Provider Migration

Strategy for changing LLM providers or models:

**Provider Abstraction**:
- Common interface across providers
- Model-specific optimizations as extensions
- Feature detection for provider capabilities
- Fallback paths for unsupported features

**Model Evaluation**:
- Comprehensive benchmark suite
- Performance metrics tracking
- Cost modeling tools
- A/B testing infrastructure

**Migration Strategy**:
- Shadow testing before cutover
- Gradual traffic shifting
- Template adaptation utilities
- Performance impact monitoring

---

## 11. End-User Metrics & Quality

### 11.1 Conversation Quality

Measuring and optimizing the end-user experience:

**Quality Metrics**:
- Intent recognition accuracy
- First-time resolution rate
- Conversation completion rate
- Parameter extraction attempts
- Conversation length (turns)
- User correction frequency

**Collection Methods**:
- Automated logging of conversation flows
- Optional explicit user feedback
- Human evaluation sampling
- A/B comparison testing

**Improvement Process**:
- Weekly quality review meetings
- Root cause analysis for failed conversations
- Template improvement based on failures
- Regular model re-evaluation

### 11.2 User Satisfaction Measurement

Direct and indirect measures of user experience:

**Explicit Measurements**:
- Optional thumbs up/down feedback
- Periodic satisfaction surveys
- Focus group testing
- User interview program

**Implicit Measurements**:
- Task completion rates
- Abandon rates
- Repeat usage patterns
- Error recovery success
- Usage growth trends

**Satisfaction Goals**:
- ≥85% explicit satisfaction rate
- ≥90% task completion rate
- ≤5% abandon rate
- ≥80% first-attempt success rate

### 11.3 Continuous Improvement Framework

Structured approach to ongoing system enhancement:

**Improvement Cycle**:
1. Collect performance data and user feedback
2. Identify top improvement opportunities
3. Implement template or system enhancements
4. Test changes in controlled environment
5. Monitor impact after deployment
6. Document learnings

**Prioritization Criteria**:
- Impact on user satisfaction
- Technical feasibility
- Implementation effort
- Cross-tenant applicability
- Alignment with roadmap

**Review Cadence**:
- Daily: Critical issues and service disruptions
- Weekly: Performance trends and emerging patterns
- Monthly: Feature improvement prioritization
- Quarterly: Strategic enhancement planning

---

## 12. Security Review Process

### 12.1 Integration Security Review

Rigorous vetting process for new integrations:

**Review Checklist**:
- Authentication mechanism assessment
- Data handling analysis
- Permission model review
- Rate limiting and quota enforcement
- Error handling and logging practices

**Approval Workflow**:
1. Initial security scan of API specification
2. Sensitive data mapping and handling review
3. Authentication & authorization verification
4. Integration testing in sandbox environment
5. Penetration testing for critical integrations
6. Final security signoff before production deployment

**Ongoing Monitoring**:
- Periodic security re-assessment
- Vulnerability scanning
- Authentication audit
- Data access patterns analysis

### 12.2 Template Security

Ensuring templates adhere to security best practices:

**Security Controls**:
- Sandboxed template execution environment
- Input validation and sanitization
- Output format enforcement
- Resource usage limitations
- Sensitive data filtering

**Review Process**:
- Automated security scanning for common issues
- Manual review for sensitive templates
- Pattern matching for security anti-patterns
- Performance impact assessment
- Privilege escalation testing

**Template Permissions**:
- Template-specific access controls
- Approval workflows for sensitive changes
- Principle of least privilege enforcement
- Audit logging for all template changes

### 12.3 Industry Pack Compliance

Verification of pack compliance with industry regulations:

**Compliance Domains**:
- Healthcare: HIPAA, HITECH, FDA requirements
- Finance: PCI-DSS, GLBA, SOX
- Public Sector: FedRAMP, FISMA, CJIS
- General: GDPR, CCPA, ISO 27001

**Verification Process**:
1. Compliance requirements mapping
2. Control implementation verification
3. Documentation review
4. Test environment validation
5. Audit preparation artifacts
6. Certification support materials

**Maintenance Requirements**:
- Annual compliance review
- Change impact assessment
- Regulatory update monitoring
- Compliance documentation updates

### 12.4 Vulnerability Management

Systematic identification and remediation of security issues:

**Vulnerability Sources**:
- Dependency scanning
- Static code analysis
- Dynamic application testing
- Penetration testing
- Bug bounty program
- Third-party security advisories

**Severity Classification**:
- Critical: Immediate remediation (<24 hours)
- High: Prioritized fix (<1 week)
- Medium: Scheduled fix (<1 month)
- Low: Backlog item (next release cycle)

**Remediation Process**:
1. Vulnerability identification and validation
2. Severity and impact assessment
3. Fix development and testing
4. Security validation of fix
5. Deployment to production
6. Post-deployment verification

---

## 13. Rollout Plan

| Phase | Priority | Milestone | Timeline |
|-------|----------|-----------|----------|
| P0 | **Foundation** | Repo scaffold, RLS tables, CI pipeline, API key management | Week 1-2 |
| P1a | **Foundation** | Tenant model, Tier 1 (Vector) intent matching | Week 3-4 |
| P1b | **Foundation** | TemplateStore hierarchy, Industry Pack framework | Week 5-6 |
| P1c | **Foundation** | OpenAPI Adapter core functionality | Week 7-8 |
| P2a | Application | Parameter Completion with conversational interface | Week 9-10 |
| P2b | Application | Tier 2 (Local LLM) intent routing | Week 11-12 |
| P2c | Application | Action Registry with sync and async support | Week 13-14 |
| P2d | Application | OpenAPI schema to intent mapping | Week 15-16 |
| P3a | Application | Tier 3 (RAG) for complex intents | Week 17-18 |
| P3b | Ops | Billing counters, dashboards, compliance tools | Week 19-20 |
| P4 | Roll‑out | Canary tenant (internal), QA, general availability | Week 21-24 |

---

## 14. Open Questions

1. External Action plugins over MCP: security handshake spec?
2. Auto‑scaling per‑tenant vector rebuild workers
3. Long‑term pack marketplace governance
4. Compliance certification timeline and requirements
5. Cold-start optimization for local LLM routing
6. Cross-tenant knowledge sharing options (opt-in)
7. Integration with existing analytics platforms
8. Template migration strategy when updating base templates
9. OpenAPI versioning and backward compatibility strategy
10. Multi-region data synchronization approach
11. LLM provider selection criteria and evaluation process
12. Backup strategy for stateful components in Kubernetes

---

## 15. Changelog

| Date | Author | Change |
|------|--------|--------|
| 2025‑05‑05 | ChatGPT | Update v1.1: multi‑tenant, packs, RLS, billing foundation |
| 2025‑05‑05 | Claude | Update v1.2: Three-tier architecture, Star Trek interface, expanded actions |
| 2025‑05‑05 | Claude | Update v1.3: Advanced Template Management System, template categories, inheritance model |
| 2025‑05‑05 | Claude | Update v1.4: OpenAPI Integration System, automated action generation, API management |
| 2025‑05‑05 | Claude | Update v1.5: Added Developer Experience, Operational Concerns, Migration Framework, Security Reviews and more |