# Clerk Organization-Tenant Integration PRD

## Overview
Establish a robust relationship between Clerk organizations and Coherence tenants to ensure consistent multi-tenant isolation and improved user management.

## Problem Statement
Currently, tenant IDs are stored in Clerk organization metadata but lack a formal database relationship, making it difficult to maintain data consistency and perform tenant lookups.

## Goals
1. Create a reliable mapping between Clerk organizations and Coherence tenants
2. Minimize changes to existing frontend and API implementations
3. Ensure backward compatibility with existing tenant management
4. Provide reliable tenant resolution during user sessions

## Non-Goals
1. Changing the existing Row Level Security implementation
2. Modifying the current API authentication flow
3. Restructuring the existing tenant data model beyond adding the relationship

## Technical Design

### Database Changes
```sql
ALTER TABLE tenants ADD COLUMN clerk_org_id VARCHAR UNIQUE;
CREATE INDEX ix_tenants_clerk_org_id ON tenants(clerk_org_id);
```

### Code Changes

1. **Tenant Model**:
```python
class Tenant(Base):
    clerk_org_id: Mapped[Optional[str]] = mapped_column(String, nullable=True, unique=True)
```

2. **Tenant Creation**:
```python
@router.post("/")
async def create_tenant(tenant_in: TenantCreate):
    db_tenant = Tenant(
        name=tenant_in.name,
        clerk_org_id=clerk_org_id,  # New field
        # ... existing fields
    )
```

### Migration Plan
1. Create database migration for new column
2. Write migration script for existing tenants:
   - Query all Clerk organizations
   - Match with existing tenants using metadata
   - Update tenant records with clerk_org_id

## Implementation Phases

### Phase 1: Database Setup (Day 1)
- [x] Create database migration
- [x] Add model changes
- [x] Update tenant creation endpoint

### Phase 2: Migration (Day 2)
- [x] Create migration script for existing tenants
- [x] Test migration in staging
- [x] Document rollback procedure

### Phase 3: Testing & Validation (Day 3)
- [ ] Write integration tests
- [ ] Test tenant resolution flow
- [ ] Verify existing functionality

## Success Metrics
1. All existing tenants successfully mapped to Clerk organizations
2. No disruption to existing tenant operations
3. Successful tenant resolution in all API calls
4. Zero data inconsistencies between Clerk and Coherence

## Risks and Mitigations

### Risks
1. Data inconsistency during migration
2. Potential service disruption
3. Missing tenant-org mappings

### Mitigations
1. Run migration in dry-run mode first
2. Schedule migration during low-traffic period
3. Create reconciliation report post-migration
4. Maintain backup of pre-migration state

## Timeline
- Day 1: Database changes and model updates
- Day 2: Migration implementation and testing
- Day 3: Validation and monitoring

## Future Considerations
1. Add admin UI for managing org-tenant relationships
2. Implement automatic tenant creation on org creation
3. Add audit logging for tenant-org operations