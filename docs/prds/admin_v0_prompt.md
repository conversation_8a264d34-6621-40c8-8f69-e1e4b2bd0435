# Optimized Vercel v0 Prompt for Coherence Admin UI Components

This optimized prompt is designed to generate high-quality UI components for the Coherence admin interface that will integrate with a Refine.js admin framework, following a hybrid approach that leverages the strengths of both v0 and Refine, with Clerk for authentication.

---

## 🚀 **Core Component Library Prompt**

**Prompt:**

```
Create a modern, professional-grade admin UI component library for Coherence, an AI middleware that transforms natural language into API calls. These components will integrate with a Refine.js admin framework and use Clerk for authentication. Focus on creating responsive, accessible UI components using React, TypeScript, Tailwind CSS, and Shadcn UI.

## Core Components to Generate

### 1. Dashboard Components:
- Create a responsive dashboard overview with status cards, API health indicators, and performance metrics
- Include an API integration status grid showing health metrics with color-coded statuses (green/yellow/red)
- Design performance chart components using Tremor for:
  - API response time visualization (line chart)
  - Request volume visualization (bar chart)
  - Error rate visualization (area chart)
- Activity feed component showing recent template edits, API additions with timestamp and user info
- Include tenant switching dropdown for system administrators that integrates with Clerk's Organization Switcher

### 2. API Integration Management Components:
- Create an OpenAPI spec import wizard with file upload and URL input options
- Design an API testing console with request builder and split-view response visualizer
- Build an endpoint explorer component for navigating API structure
- Include circuit breaker status visualization cards
- API health monitoring dashboard with real-time status checks

### 3. Template System Components:
- Design a template editor with Monaco-based code editor for Jinja2 syntax
- Create a split-view component with editor on left, preview on right
- Add template version history component with diff visualization
- Build template test bench component with parameter input form and result display
- Include template inheritance visualization diagram
- Add parameter schema editor with validation

### 4. Credential Management Components:
- Create secure credential input forms for different auth types (API key, Basic, Bearer, OAuth2)
- Design masked credential display cards with reveal toggle and copy button
- Include credential testing component with status indicator
- Build credential rotation scheduler with calendar picker
- Add security audit features that show credential usage history

### 5. Monitoring & Alert Components:
- Design system health dashboard with real-time indicators
- Create comprehensive API status grid with expandable details
- Build alert rules configuration card with condition builder
- Include notification channel configuration component
- Add historical metrics views with date range selectors

## Technical Requirements
- Use TypeScript for type-safety with proper interface definitions
- Optimize components for use within Refine.js admin framework
- Add Clerk authentication integration with proper user/role handling
- Support dark/light mode with a theme toggle component
- Ensure keyboard accessibility and screen reader compatibility
- Use React Query for data fetching with proper loading/error states
- Create responsive designs that work on desktop and tablet (admin interfaces)
- Include loading, error, and empty states for all data-dependent components
- Add proper error boundary components to prevent UI crashes

## Visual Style
- Professional, clean interface with subtle shadows and rounded corners
- Use a color scheme that supports both light and dark modes
- Include visual indicators for status (success, warning, error)
- Use Shadcn UI components as the foundation
- Maintain whitespace and visual hierarchy for readability
- Add subtle animations for transitions and feedback
- Use consistent iconography (preferably using Lucide icons)

## Authentication & Authorization
- Include components that integrate with Clerk for authentication
- Support for role-based access control (system admin vs. tenant admin)
- Add tenant context components that show current tenant information
- Implement organization switching UI that uses Clerk's Organizations feature

## Component Export Structure
Please organize the generated components into these logical modules:
- `components/dashboard/` - Dashboard and overview components
- `components/integration/` - API integration and import components
- `components/templates/` - Template editor and management components
- `components/credentials/` - Credential management components
- `components/monitoring/` - Monitoring and alerting components
- `components/common/` - Shared components, layouts, and utilities
- `hooks/` - Custom hooks for data fetching and state management

Include examples of how each component can be integrated with Refine's hooks.

Focus on creating high-quality, reusable UI components that solve the specific challenges of managing API integrations, templates, and credentials in the Coherence platform, while ensuring they'll work seamlessly when integrated into a Refine.js admin framework with Clerk authentication.
```

---

## 📝 **Component-Specific Prompts**

For best results with v0, use these specific prompts to generate individual components:

### 1. Template Editor Component

```
Create a powerful template editor component for a template-driven API action system. The component should include:

1. A split-view layout with Monaco-based code editor on the left and a preview panel on the right
2. Syntax highlighting for Jinja2 template language
3. Tabs above the editor for:
   - "Basic Settings" (name, category)
   - "Template Code" (the main editor)
   - "Parameter Mapping" (form-based editor for mapping parameters)
   - "Response Mapping" (JSON path mapping editor)
   - "Error Handling" (configuring fallback behavior)

4. Real-time validation with error highlighting
5. Version history sidebar showing previous versions with timestamps
6. Template testing panel at bottom that can be expanded/collapsed
7. "Save", "Test", and "Reset" buttons

The component should integrate with Refine's useForm hook for data management, and include proper TypeScript interfaces.

Use Shadcn UI components, Tailwind CSS for styling, and ensure it's fully responsive. The component should handle both light and dark modes.

The template code editor should support autocompletion for common Jinja2 syntax and template variables.

Include examples of integrating with Refine's hooks:
- How to connect the editor to useForm
- How to handle loading/saving with Refine's data provider
- How to implement validation using Refine's validation system
```

### 2. API Health Monitoring Dashboard

```
Create a comprehensive API health monitoring dashboard component for a multi-tenant system that manages external API integrations. The component should:

1. Display a grid of API integration status cards, each showing:
   - API name and version
   - Current status (Healthy/Degraded/Down) with appropriate color coding
   - Last check timestamp
   - Average response time
   - Success rate percentage
   - Circuit breaker status (Open/Closed)

2. Include an expandable details panel for each API that shows:
   - Response time trend chart (line chart, last 24h)
   - Error rate chart (area chart, last 24h)
   - Endpoint-specific health metrics in a table
   - Recent error log entries if available

3. Provide top-level summary metrics:
   - Total APIs monitored
   - APIs with issues (count)
   - Overall system health score
   - Tenant-specific health overview (when multi-tenant)

4. Include action buttons for:
   - Manual health check trigger
   - Configure monitoring settings
   - View detailed logs
   - Reset circuit breaker (for ops users)

5. Add a tenant filter that integrates with Clerk's organization context

Use Tremor charts for data visualization, Shadcn UI components for the interface, and Tailwind CSS for styling. Ensure the component is responsive and works in both light and dark modes.

Include examples of integrating with Refine:
- How to fetch API health data using useList or useCustom
- How to handle refreshing data
- How to manage tenant context from Clerk's organization features
```

### 3. Credential Management Interface

```
Create a secure credential management interface for storing and managing authentication credentials for external API integrations. The component should:

1. Display a credential list with:
   - Credential name/label
   - API integration it belongs to
   - Credential type (API Key, Basic Auth, Bearer Token, OAuth2)
   - Last used timestamp
   - Status indicator (Active/Expired/Revoked)
   - Actions (Edit, Delete, Test, Rotate)

2. Include modal forms for each credential type:
   - API Key form with header name configuration
   - Basic Auth form with username/password fields
   - Bearer Token form with token input
   - OAuth2 form with client ID, client secret, token URL, scopes, and auth URL

3. Security features:
   - Masked credential displays with reveal toggle
   - Copy-to-clipboard button for credentials
   - Credential test functionality with status feedback
   - Credential rotation scheduling
   - Auto-expire options with notification settings

4. Use secure input patterns:
   - Masked inputs for sensitive fields
   - Auto-timeout for revealed credentials (auto-hide after 30 seconds)
   - Warning indicators for credentials without rotation schedules
   - Last usage tracking and alerts for unused credentials

5. Add proper integration with tenant context using Clerk's organization features

Use Shadcn UI components, Tailwind CSS for styling, and ensure it works in both light and dark modes. The component should be responsive with a focus on security best practices.

Include examples of integrating with Refine:
- How to use useTable for credential listing
- How to use useForm for credential creation/editing
- How to handle secure mutation functions
- How to implement tenant-scoped data access
```

### 4. OpenAPI Import Wizard

```
Create an OpenAPI specification import wizard component for an API integration platform. The component should:

1. Provide multiple import options:
   - File upload zone for OpenAPI JSON/YAML files (with drag-and-drop support)
   - URL input field for fetching specs from a remote source
   - Paste area for directly inputting specification text
   - GitHub repository URL with automatic spec detection

2. Include a multi-step wizard interface:
   - Step 1: Choose import method and provide spec
   - Step 2: Validation results with warnings/errors highlighted
   - Step 3: Configuration options:
     * API name and description fields
     * Base URL configuration
     * Environment selection (Development/Production)
     * Authentication method selection
     * Rate limiting and timeout settings

3. Preview features:
   - Endpoint count and breakdown by method (GET, POST, etc.)
   - Authentication requirements detection
   - Schema complexity assessment
   - Visual indicator for supported vs. unsupported features
   - Schema visualization with interactive endpoint explorer

4. Final confirmation step with summary of what will be imported:
   - Template creation preview
   - Credential requirements
   - Potential issues or warnings
   - Estimated setup time

5. Integration with tenant context using Clerk's organization features

Use Shadcn UI components for the interface, with proper form validation, loading states, and error handling. Ensure the component is responsive and works in both light and dark modes.

Include examples of integrating with Refine:
- How to use useForm for the wizard steps
- How to implement file upload with Refine
- How to handle the import process with Refine's mutation hooks
- How to manage tenant-specific imports
```

### 5. Tenant Management Dashboard

```
Create a tenant management dashboard for system administrators of a multi-tenant API orchestration platform. The component should:

1. Display an overview of all tenants with:
   - Tenant name and industry
   - Active/Inactive status with toggle
   - Usage metrics (API calls, templates, active users)
   - Creation date and last activity
   - Compliance tier indicator
   - Actions (View, Edit, Suspend)

2. Include a tenant creation wizard with:
   - Basic information form (name, industry, contact info)
   - Resource allocation settings
   - User invitation system
   - Template pack selection
   - Initial API key generation

3. Add a tenant detail view with:
   - Usage dashboard with charts (daily/weekly/monthly activity)
   - User management interface
   - API key management
   - Template library access
   - Audit log of administrative actions

4. Implement tenant switching that integrates with Clerk's Organization Switcher:
   - Quick-switch dropdown in header
   - Current tenant indicator
   - Permission-based access controls
   - Context persistence when switching

5. Include a system health overview that shows:
   - Global system metrics
   - Per-tenant health indicators
   - Rate limiting status
   - Resource utilization

Use Shadcn UI components with Tailwind CSS for styling, and implement both light and dark modes. Make the interface responsive for desktop and tablet use.

Include examples of integrating with Refine and Clerk:
- How to use Clerk's organization features for tenant context
- How to manage resources with Refine's data provider
- How to implement role-based access control
- How to handle tenant-specific authentication flows
```

---

## 🔄 **Integration Strategy with Clerk Authentication**

When implementing the hybrid approach with v0-generated components, Refine framework, and Clerk authentication, include these code examples:

### Setting Up Clerk with Refine

```jsx
import { ClerkProvider, useAuth, useUser, useOrganization } from '@clerk/clerk-react';
import { Refine } from '@refinedev/core';
import routerProvider from "@refinedev/react-router-v6";
import dataProvider from "./dataProvider";

// Custom auth provider that connects Clerk to Refine
const authProvider = {
  login: async () => {
    // Clerk handles this through its components
    return { success: true };
  },
  logout: async () => {
    // Clerk handles this
    return { success: true };
  },
  checkAuth: async () => {
    const { isSignedIn } = useAuth();
    return isSignedIn ? Promise.resolve() : Promise.reject();
  },
  getPermissions: async () => {
    const { user } = useUser();
    // Map Clerk roles to Coherence roles
    const roles = user?.publicMetadata?.roles || [];
    return Promise.resolve(roles);
  },
  getUserIdentity: async () => {
    const { user } = useUser();
    const { organization } = useOrganization();
    
    if (!user) return Promise.reject();
    
    // Get tenant context from Clerk organization
    const tenantId = organization?.publicMetadata?.tenantId;
    
    return Promise.resolve({
      id: user.id,
      name: `${user.firstName} ${user.lastName}`,
      email: user.primaryEmailAddress?.emailAddress,
      avatar: user.imageUrl,
      tenantId,
      isSystemAdmin: user.publicMetadata?.isSystemAdmin || false,
    });
  }
};

// App with Clerk Provider
function App() {
  return (
    <ClerkProvider publishableKey={import.meta.env.VITE_CLERK_PUBLISHABLE_KEY}>
      <Refine
        authProvider={authProvider}
        dataProvider={dataProvider}
        routerProvider={routerProvider}
        // Other Refine configuration
      >
        {/* Your v0-generated components */}
      </Refine>
    </ClerkProvider>
  );
}
```

### Integrating v0 Components with Refine and Clerk

```jsx
import { useTable } from "@refinedev/core";
import { useOrganization } from "@clerk/clerk-react";
import { APIHealthDashboard } from "../components/monitoring/APIHealthDashboard";

const MonitoringPage = () => {
  const { organization } = useOrganization();
  const tenantId = organization?.publicMetadata?.tenantId;
  
  // Use tenant context from Clerk in Refine's data fetching
  const { tableQueryResult, tableProps } = useTable({
    resource: "api-integrations",
    filters: [
      {
        field: "tenant_id",
        operator: "eq",
        value: tenantId,
      },
    ],
  });

  const apiIntegrations = tableQueryResult.data?.data || [];

  return (
    <div className="p-4">
      {/* Pass tenant context to your v0 component */}
      <APIHealthDashboard 
        apiIntegrations={apiIntegrations}
        tenantId={tenantId}
        isLoading={tableQueryResult.isLoading}
        refreshData={tableQueryResult.refetch}
      />
    </div>
  );
};
```

### Managing Form State with Refine and v0 Components

```jsx
import { useForm } from "@refinedev/react-hook-form";
import { useOrganization } from "@clerk/clerk-react";
import { TemplateEditor } from "../components/templates/TemplateEditor";

const EditTemplatePage = () => {
  const { organization } = useOrganization();
  const tenantId = organization?.publicMetadata?.tenantId;
  
  const { 
    refineCore: { formLoading, onFinish },
    saveButtonProps,
    register,
    formState,
    setValue,
    watch
  } = useForm({
    refineCoreProps: {
      resource: "templates",
      action: "edit",
      id: params?.id,
      meta: {
        // Pass tenant context to the backend
        tenantId,
      },
    }
  });

  // Connect TemplateEditor to Refine's form state
  const handleTemplateChange = (code) => {
    setValue("body", code);
  };

  return (
    <div className="p-4">
      <TemplateEditor
        initialCode={watch("body")}
        onChange={handleTemplateChange}
        errors={formState.errors}
        isLoading={formLoading}
        onSave={saveButtonProps.onClick}
        tenantId={tenantId}
      />
    </div>
  );
};
```

### Tenant Switching with Clerk Organizations

```jsx
import { OrganizationSwitcher, UserButton } from "@clerk/clerk-react";

const AppHeader = () => {
  return (
    <header className="flex justify-between items-center p-4 border-b">
      <div className="flex items-center gap-2">
        <img src="/logo.svg" alt="Coherence" className="h-8 w-8" />
        <h1 className="text-xl font-semibold">Coherence Admin</h1>
      </div>
      
      <div className="flex items-center gap-4">
        {/* Custom organization switcher that maintains tenant context */}
        <OrganizationSwitcher
          hidePersonal
          appearance={{
            elements: {
              rootBox: {
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              },
            },
          }}
        />
        
        <UserButton />
      </div>
    </header>
  );
};
```

---

## 🔑 **Key Benefits of This Approach**

1. **Leverages Three Technologies' Strengths**:
   - Uses v0 for high-quality UI component generation
   - Uses Refine for data management, resource handling, and admin framework structure
   - Uses Clerk for secure authentication and multi-tenant organization management

2. **Maintainable Architecture**:
   - Clear separation of UI components and data management
   - TypeScript integration for type safety
   - Consistent styling with Shadcn UI and Tailwind
   - Secure auth with Clerk's enterprise-grade authentication

3. **Developer Experience**:
   - Rapid UI development with v0
   - Structured data handling with Refine
   - Simplified auth management with Clerk
   - Standard React patterns throughout

4. **User Experience**:
   - Professional, polished interface
   - Consistent interaction patterns
   - Responsive and accessible design
   - Seamless tenant switching

This hybrid approach gives you the speed and flexibility of v0's AI-generated components with the robust data handling and admin features of Refine, all secured with Clerk's authentication system.