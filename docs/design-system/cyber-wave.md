# Cyber Wave Design System

A futuristic, neon-inspired design system for the Coherence Admin portal that draws inspiration from cyberpunk aesthetics and Warp Terminal's Cyber Wave theme.

## Colors

The Cyber Wave color palette is defined in `tailwind.config.js` and exposed as CSS variables in our global stylesheets. The theme includes:

### Base Colors

| Variable | Hex | Usage |
|----------|-----|-------|
| `--cw-bg-top` | #002633 | Top gradient background |
| `--cw-bg-bottom` | #000000 | Bottom gradient background |
| `--cw-accent-left` | #007972 | Left accent gradient color |
| `--cw-accent-right` | #7b008f | Right accent gradient color |
| `--cw-foreground` | #ffffff | Primary text color |

### Normal & Bright Colors

The theme includes a set of normal (n) and bright (b) variations for different interface elements:

| Normal | Bright | Usage |
|--------|--------|-------|
| `--cw-n-black` | `--cw-b-black` | Text and borders |
| `--cw-n-red` | `--cw-b-red` | Destructive actions, errors |
| `--cw-n-green` | `--cw-b-green` | Success states |
| `--cw-n-yellow` | `--cw-b-yellow` | Warnings, highlights |
| `--cw-n-blue` | `--cw-b-blue` | Information, focus states |
| `--cw-n-magenta` | `--cw-b-magenta` | Secondary accent |
| `--cw-n-cyan` | `--cw-b-cyan` | Tertiary accent, borders |
| `--cw-n-white` | `--cw-b-white` | Backgrounds, text |

## Typography

Cyber Wave uses two primary typefaces:

- **JetBrains Mono** - Primary font for body text, code, and UI elements
  - `font-mono` utility class
  - Monospaced for better readability of data and metrics

- **Orbitron** - Display font for headings and special elements
  - `font-display` utility class
  - Futuristic, geometric appearance that aligns with the cyberpunk theme

## Gradients

Two primary gradients are available:

- **`bg-gradient-cw`** - The primary background gradient
  - `linear-gradient(180deg, var(--cw-bg-top), var(--cw-bg-bottom))`
  - Used for page backgrounds and large surfaces

- **`bg-gradient-cw-accent`** - The accent gradient for interactive elements
  - `linear-gradient(135deg, var(--cw-accent-left), var(--cw-accent-right))`
  - Used for buttons, highlights, and active states

## Animations

Custom animations to enhance the futuristic feel:

- **`animate-pulse-accent`** - Subtle pulsing effect (4s cycle)
  - Applied to accent elements and important UI components
  - Helps draw attention without being distracting

- **`animate-wave-slide`** - Horizontal sliding animation (8s cycle)
  - Used for decorative elements like progress bars
  - Creates a sense of movement and flow

## Components

### Buttons

Cyber Wave introduces several button variants:

```jsx
// Default button with Cyber Wave styling
<Button>Standard Button</Button>

// Cyber button with special hover effects
<Button variant="cyber">Cyber Button</Button>

// Outline button with backdrop blur
<Button variant="outline">Outline Button</Button>
```

### Cards

Cards in Cyber Wave follow a distinctive style:

```jsx
<div className="card-cyberpunk">
  <h3 className="font-display text-lg mb-2">Card Title</h3>
  <p>Card content goes here with Cyber Wave styling.</p>
</div>
```

### Interactive Elements

The accent-glow effect can be applied to any interactive element:

```jsx
<div className="accent-glow p-4 rounded-md border border-border">
  This element will show a subtle gradient glow on hover
</div>
```

## Theme Toggle

The Cyber Wave theme works in both light and dark modes, controlled via the ThemeProvider:

```jsx
import { useTheme } from '@/context/ThemeProvider';

function MyComponent() {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <button onClick={toggleTheme}>
      Toggle {theme === 'dark' ? 'Light' : 'Dark'} Mode
    </button>
  );
}
```

## Usage Examples

### Dashboard Card

```jsx
<div className="card-cyberpunk">
  <h3 className="font-display text-lg mb-2 text-accent">Active Templates</h3>
  <div className="text-3xl font-mono">42</div>
  <div className="mt-2 h-1 w-full bg-gradient-cw-accent rounded-full"></div>
</div>
```

### Action Button

```jsx
<Button variant="cyber" size="lg">
  <span>Generate Template</span>
  <svg className="animate-pulse-accent" /* ... */ />
</Button>
```

### Navigation Item

```jsx
<Link 
  href="/admin/integrations" 
  className="flex items-center px-3 py-2 rounded-lg text-sm font-medium accent-glow text-sidebar-foreground hover:text-primary hover:bg-sidebar-primary/10"
>
  <span className="mr-3">
    <IntegrationIcon />
  </span>
  Integrations
</Link>
```

## Implementation Notes

1. The Cyber Wave theme is implemented via Tailwind, with core definitions in:
   - `tailwind.config.js` - Theme definitions
   - `globals.css` - CSS variables and base styles

2. The theme supports both light and dark modes via the `.dark` class strategy

3. All colors are exposed as CSS variables for consistent usage

4. Custom utilities like `.card-cyberpunk` and `.accent-glow` provide easy access to common patterns

## Accessibility

Despite its vibrant, neon-inspired design, Cyber Wave maintains accessibility through:

- Sufficient contrast ratios between text and backgrounds
- Clear focus indicators for keyboard navigation
- Animation that can be reduced via `prefers-reduced-motion`
- Semantically meaningful color usage with additional visual indicators

## Future Enhancements

Planned enhancements for the Cyber Wave design system:

1. Add retro-inspired grid background options
2. Implement more advanced animation patterns
3. Create specialized UI components for data visualization
4. Develop a complete icon set in the Cyber Wave style