import React from 'react';
import { renderHook } from '@testing-library/react-hooks';
import { useHasPermission, useHasAllPermissions, useHasAnyPermission } from '../useHasPermission';
import { AdminSessionContext } from '@/contexts/AdminSessionContext';

// Helper to create a wrapper with session context
const createWrapper = (contextValue) => {
  return ({ children }) => (
    <AdminSessionContext.Provider value={contextValue}>
      {children}
    </AdminSessionContext.Provider>
  );
};

describe('useHasPermission', () => {
  // Basic permission checking
  it('should return true when user has the specified permission', () => {
    const wrapper = createWrapper({
      isSystemAdmin: false,
      permissions: ['workflow:read', 'template:read'],
    });
    
    const { result } = renderHook(() => useHasPermission('workflow:read'), { wrapper });
    
    expect(result.current).toBe(true);
  });
  
  it('should return false when user does not have the specified permission', () => {
    const wrapper = createWrapper({
      isSystemAdmin: false,
      permissions: ['workflow:read'],
    });
    
    const { result } = renderHook(() => useHasPermission('template:read'), { wrapper });
    
    expect(result.current).toBe(false);
  });
  
  // System admin tests
  it('should return true for any permission if user is system admin', () => {
    const wrapper = createWrapper({
      isSystemAdmin: true,
      permissions: [], // No specific permissions
    });
    
    const { result } = renderHook(() => useHasPermission('some:random:permission'), { wrapper });
    
    expect(result.current).toBe(true);
  });
  
  // Wildcard permission test
  it('should return true for any permission if user has system:* wildcard', () => {
    const wrapper = createWrapper({
      isSystemAdmin: false,
      permissions: ['system:*', 'workflow:read'],
    });
    
    const { result } = renderHook(() => useHasPermission('template:create'), { wrapper });
    
    expect(result.current).toBe(true);
  });
});

describe('useHasAllPermissions', () => {
  it('should return true when user has all specified permissions', () => {
    const wrapper = createWrapper({
      isSystemAdmin: false,
      permissions: ['workflow:read', 'template:read', 'integration:read'],
    });
    
    const { result } = renderHook(
      () => useHasAllPermissions(['workflow:read', 'template:read']), 
      { wrapper }
    );
    
    expect(result.current).toBe(true);
  });
  
  it('should return false when user is missing any specified permission', () => {
    const wrapper = createWrapper({
      isSystemAdmin: false,
      permissions: ['workflow:read', 'template:read'],
    });
    
    const { result } = renderHook(
      () => useHasAllPermissions(['workflow:read', 'template:read', 'integration:read']), 
      { wrapper }
    );
    
    expect(result.current).toBe(false);
  });
  
  it('should return true for any permissions if user is system admin', () => {
    const wrapper = createWrapper({
      isSystemAdmin: true,
      permissions: [],
    });
    
    const { result } = renderHook(
      () => useHasAllPermissions(['workflow:create', 'template:delete', 'integration:update']), 
      { wrapper }
    );
    
    expect(result.current).toBe(true);
  });
});

describe('useHasAnyPermission', () => {
  it('should return true when user has at least one specified permission', () => {
    const wrapper = createWrapper({
      isSystemAdmin: false,
      permissions: ['workflow:read'],
    });
    
    const { result } = renderHook(
      () => useHasAnyPermission(['workflow:read', 'template:read']), 
      { wrapper }
    );
    
    expect(result.current).toBe(true);
  });
  
  it('should return false when user has none of the specified permissions', () => {
    const wrapper = createWrapper({
      isSystemAdmin: false,
      permissions: ['integration:read'],
    });
    
    const { result } = renderHook(
      () => useHasAnyPermission(['workflow:read', 'template:read']), 
      { wrapper }
    );
    
    expect(result.current).toBe(false);
  });
  
  it('should return true for any permissions if user is system admin', () => {
    const wrapper = createWrapper({
      isSystemAdmin: true,
      permissions: [],
    });
    
    const { result } = renderHook(
      () => useHasAnyPermission(['workflow:create', 'template:delete']), 
      { wrapper }
    );
    
    expect(result.current).toBe(true);
  });
});