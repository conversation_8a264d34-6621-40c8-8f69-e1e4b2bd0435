import { useContext } from 'react';
import { AdminSessionContext } from '@/contexts/AdminSessionContext';

/**
 * A hook that checks if the current user has a specific permission.
 * 
 * This centralizes permission checking logic across the application:
 * - Automatically handles system admin checks
 * - Supports the wildcard "system:*" permission
 * - Provides a clean, consistent API for checking permissions
 * 
 * @param permission The permission string to check
 * @returns boolean indicating if the user has the specified permission
 */
export function useHasPermission(permission: string): boolean {
  const { isSystemAdmin, permissions } = useContext(AdminSessionContext);
  
  // System admins automatically have all permissions
  if (isSystemAdmin) {
    return true;
  }
  
  // Check for the specific permission or wildcard permission
  return permissions?.includes(permission) || permissions?.includes('system:*') || false;
}

/**
 * A hook that checks if the current user has all of the specified permissions.
 * 
 * @param requiredPermissions Array of permission strings that must all be present
 * @returns boolean indicating if the user has all the specified permissions
 */
export function useHasAllPermissions(requiredPermissions: string[]): boolean {
  const { isSystemAdmin, permissions } = useContext(AdminSessionContext);
  
  // System admins automatically have all permissions
  if (isSystemAdmin) {
    return true;
  }
  
  // Wildcard permission grants all permissions
  if (permissions?.includes('system:*')) {
    return true;
  }
  
  // Check that all required permissions are present
  return requiredPermissions.every(p => permissions?.includes(p)) || false;
}

/**
 * A hook that checks if the current user has any of the specified permissions.
 * 
 * @param acceptablePermissions Array of permission strings where at least one must be present
 * @returns boolean indicating if the user has at least one of the specified permissions
 */
export function useHasAnyPermission(acceptablePermissions: string[]): boolean {
  const { isSystemAdmin, permissions } = useContext(AdminSessionContext);
  
  // System admins automatically have all permissions
  if (isSystemAdmin) {
    return true;
  }
  
  // Wildcard permission grants all permissions
  if (permissions?.includes('system:*')) {
    return true;
  }
  
  // Check if any of the acceptable permissions are present
  return acceptablePermissions.some(p => permissions?.includes(p)) || false;
}