["apikey:create", "apikey:delete", "apikey:read", "auditlog:view", "chat:access", "integration:create", "integration:delete", "integration:manage_credentials", "integration:read", "integration:update", "organization:view_own_dashboard", "system_health:view", "system_settings:edit", "system_settings:view", "template:create", "template:delete", "template:read", "template:read_versions", "template:update", "tenant:create", "tenant:delete", "tenant:edit", "tenant:list_all", "workflow:create", "workflow:delete", "workflow:execute", "workflow:read", "workflow:update"]