import { auth } from '@clerk/nextjs/server';
import { NextResponse, NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Check if Authorization header is present
    const authHeader = request.headers.get('authorization');
    
    // Try to get auth context
    const { getToken, userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized: No user ID found' }, { status: 401 });
    }

    // If we have an Authorization header, extract the token
    let token = null;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    } else {
      // Fallback to trying getToken
      const templateName = process.env.CLERK_SESSION_TOKEN_TEMPLATE || 'coherence_session';
      token = await getToken({ template: templateName });
      
      // If that fails, try without a template
      if (!token) {
        token = await getToken();
      }
    }
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized: Could not retrieve token' }, { status: 401 });
    }

    // URL for backend endpoint
    const backendApiUrl = process.env.API_URL || process.env.NEXT_PUBLIC_API_URL;
    if (!backendApiUrl) {
      return NextResponse.json({ error: 'Server configuration error: API_URL not set' }, { status: 500 });
    }
    
    // Determine API URL (handle Docker vs local)
    let sessionInfoUrl;
    if (process.env.NODE_ENV === 'production') {
      const internalApiUrl = process.env.API_URL || 'http://coherence-api:8000/v1';
      sessionInfoUrl = `${internalApiUrl.endsWith('/v1') ? internalApiUrl : internalApiUrl + '/v1'}/auth/session-info`;
    } else {
      sessionInfoUrl = `${backendApiUrl.endsWith('/v1') ? backendApiUrl : backendApiUrl + '/v1'}/auth/session-info`;
    }

    // Fetch data from backend
    const backendResponse = await fetch(sessionInfoUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!backendResponse.ok) {
      const errorData = await backendResponse.text();
      return NextResponse.json({ 
        error: `Backend error: ${backendResponse.statusText}`, 
        details: errorData 
      }, { status: backendResponse.status });
    }

    // Parse backend response
    const data = await backendResponse.json();

    // Return structured data with canonical isSystemAdmin and permissions
    return NextResponse.json({
      org: {
        id: data.org_id,
        name: data.org_name,
        role: data.org_role,
        slug: data.org_slug || null,
        public_metadata: data.org_metadata || {}
      },
      tenant: {
        id: data.tenant_id,
        name: data.tenant_name
      },
      permissions: data.permissions || [],
      isSystemAdmin: !!data.is_system_admin
    });

  } catch (error) {
    // Enhanced error details
    const errorDetails = error instanceof Error ? error.message : 'An unknown error occurred';
    
    return NextResponse.json({
      error: 'Internal server error',
      details: errorDetails
    }, { status: 500 });
  }
}