'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@clerk/nextjs';
import useSWR from 'swr';
import { PERMISSIONS } from '@/lib/generated/permissions';

// Type for permissions (generated from backend)
export type Permission = typeof PERMISSIONS[number];

// Define custom error type with status and info properties
interface ApiError extends Error {
  status?: number;
  info?: Record<string, unknown>;
}

// Window extension for development tools
interface CustomWindow extends Window {
  adminSessionData?: Record<string, unknown>;
  _tokenRefreshTimeout?: NodeJS.Timeout;
}

interface OrganizationDetails {
  id: string;
  name?: string;
  role?: string;
  slug?: string | null;
  public_metadata?: Record<string, unknown>;
}

interface TenantDetails {
  id: string;
  name?: string;
}

interface MeResponseData {
  org: OrganizationDetails | null;
  tenant: TenantDetails | null;
  permissions: string[] | null;
  isSystemAdmin: boolean;
}

interface AdminSession {
  userId: string | null;
  sessionId: string | null;
  
  // Data from /api/auth/me
  organization: OrganizationDetails | null;
  tenant: TenantDetails | null;
  permissions: string[] | null;
  isSystemAdmin: boolean;
  
  // SWR states & derived states
  isLoading: boolean;
  error: Error | null;
  isAuthenticated: boolean;
}

const defaultSessionValue: AdminSession = {
  userId: null,
  sessionId: null,
  organization: null,
  tenant: null,
  permissions: null,
  isSystemAdmin: false,
  isLoading: true,
  error: null,
  isAuthenticated: false,
};

export const AdminSessionContext = createContext<AdminSession>(defaultSessionValue);

export const useAdminSession = () => {
  const context = useContext(AdminSessionContext);
  if (context === undefined) {
    throw new Error('useAdminSession must be used within an AdminSessionProvider');
  }
  return context;
};

interface AdminSessionProviderProps {
  children: ReactNode;
}

export const AdminSessionProvider = ({ children }: AdminSessionProviderProps) => {
  const { isSignedIn, userId, sessionId, getToken, isLoaded: isClerkLoaded } = useAuth();
  const [authToken, setAuthToken] = useState<string | null>(null);

  // Fetch token from Clerk
  useEffect(() => {
    if (isClerkLoaded && isSignedIn && getToken) {
      const fetchToken = async () => {
        try {
          const templateName = process.env.CLERK_SESSION_TOKEN_TEMPLATE || 'coherence_session';
          const token = await getToken({ template: templateName });
          setAuthToken(token);
        } catch (error) {
          console.error('Error fetching token:', error);
          setAuthToken(null);
        }
      };
      
      fetchToken();
    } else if (isClerkLoaded && !isSignedIn) {
      setAuthToken(null);
    }
  }, [isClerkLoaded, isSignedIn, getToken]);

  // Fetcher function for SWR
  const fetcher = async (url: string) => {
    if (!authToken) {
      throw new Error('Authentication token not available');
    }
    
    const res = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!res.ok) {
      const error = new Error(`Failed to fetch session data from ${url}`);
      try {
        const errorData = await res.json();
        (error as ApiError).info = errorData;
      } catch {
        // Non-JSON error response
      }
      (error as ApiError).status = res.status;
      throw error;
    }
    
    return await res.json();
  };

  // Fetch session data with SWR
  const { 
    data: meData, 
    error: swrError, 
    isLoading: isSWRFetchingMe 
  } = useSWR<MeResponseData>(
    isClerkLoaded && isSignedIn && userId ? '/api/auth/me' : null,
    fetcher,
    {
      shouldRetryOnError: false,
      revalidateOnFocus: false,
      dedupingInterval: 5000,
    }
  );

  const isLoading = !isClerkLoaded || (!!isSignedIn && !!userId && !!isSWRFetchingMe);
  
  // Determine if user is authenticated
  let isAuthenticated = false;
  if (!isLoading && isClerkLoaded && isSignedIn && !swrError && meData) {
    if (meData.org?.id) {
      isAuthenticated = true;
    }
  }

  // Create session context value
  const sessionContextValue: AdminSession = {
    userId: userId || null,
    sessionId: sessionId || null,
    organization: meData?.org || null,
    tenant: meData?.tenant || null,
    permissions: meData?.permissions || null,
    isSystemAdmin: meData?.isSystemAdmin || false,
    isLoading,
    error: swrError || null,
    isAuthenticated: isAuthenticated,
  };

  return (
    <AdminSessionContext.Provider value={sessionContextValue}>
      {children}
    </AdminSessionContext.Provider>
  );
};