{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(cp:*)", "Bash(ls:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(grep:*)", "Bash(pnpm dev:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(find:*)", "mcp__isaac__write_neo4j_cypher", "mcp__isaac__read_neo4j_cypher", "mcp__github-official__get_issue", "mcp__github-official__list_issues", "mcp__isaac__get_neo4j_schema", "mcp__github-official__add_issue_comment", "mcp__github-official__update_issue", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"bg-purple-100|text-purple-800\" src/app/admin/integrations/)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"bg-blue-100|text-blue-800\" src/app/admin/integrations/)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -l \"DataTable|table.*className\" src/app/admin/ --type tsx)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -l \"DataTable|href.*admin\" src/app/admin/ --include=\"*.tsx\")", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -l \"DataTable|href.*admin\" src/app/admin/ -g \"*.tsx\")", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"table\\|DataTable\" src/app/admin/api-keys/page.tsx)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"table|DataTable\" src/app/admin/api-keys/page.tsx)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"table|DataTable\" src/app/admin/workflows/page.tsx)", "mcp__github-official__create_issue", "Bash(git add:*)", "mcp__github-official__create_pull_request_review", "Bash(git checkout:*)", "Bash(git pull:*)", "mcp__github-official__get_pull_request_reviews", "mcp__github-official__merge_pull_request", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(python:*)"], "deny": []}, "enableAllProjectMcpServers": false}