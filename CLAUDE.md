# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🧠 CORE RULES
1. **ALWAYS QUERY KG BEFORE WRITING ANY CODE** - The Knowledge Graph contains critical patterns, test frameworks, and implementation guidelines
2. Production-ready code only. Query KG before any action.
3. All Python in Docker: `docker-compose exec coherence-api`
4. Branch workflow only, never touch main

### ⚠️ CRITICAL: Knowledge Graph First!
Before writing ANY code, you MUST:
```python
# 1. Check for existing patterns
patterns = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern) 
  WHERE p.description CONTAINS $keyword OR p.name CONTAINS $keyword
  RETURN p.name, p.category, p.code, p.context
""", params={"keyword": "your_feature"})

# 2. Check test framework and patterns
test_patterns = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {category: 'testing_pattern'})
  RETURN p.code, p.context
""")

# 3. Check for component/function signatures
components = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (c:Component) WHERE c.name CONTAINS $name
  RETURN c.path, c.exports, c.framework
""", params={"name": "component_name"})
```

**Why?** The KG contains project-specific patterns that prevent errors:
- Test framework (pytest, not unittest)
- Import paths and patterns
- Database migration patterns
- Component structures
- Authentication patterns

## 🤖 ISAAC BOOTSTRAP

For detailed ISAAC documentation, see `/docs/ISAAC/README.md`

When user types any `/isaac-*` command:

```python
# Extract command name
cmd_name = user_input.split()[0].replace('/', '').replace('-', '_')

# Get command pattern from KG
cmd_pattern = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {name: $cmd_name, category: 'isaac_command'})
  RETURN p.code, p.description, p.requires_parameter
""", params={"cmd_name": cmd_name})

if not cmd_pattern:
    print(f"Unknown ISAAC command: {user_input}")
    return

# For /isaac-init, show response and dashboard
if cmd_name == 'isaac_init':
    print(cmd_pattern[0]['code'])
    dashboard = mcp__isaac__read_neo4j_cypher(query="""
      MATCH (p:Pattern {name: 'isaac_dashboard_init'})
      RETURN p.code
    """)
    exec(dashboard[0]['code'])
    return

# For other commands, get and execute workflow steps
workflow = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {category: 'isaac_workflow', command: $cmd})
  RETURN p.sequence, p.name, p.code
  ORDER BY p.sequence
""", params={"cmd": user_input.split()[0]})

for step in workflow:
    exec(step['code'])
```

### Get Validation/Rules On-Demand

```python
# Get any pattern by name
pattern = mcp__isaac__read_neo4j_cypher(query="""
  MATCH (p:Pattern {name: $name})
  RETURN p.code, p.context
""", params={"name": pattern_name})
```

## MCP Tools
- `mcp__isaac__read_neo4j_cypher` - Read KG
- `mcp__isaac__write_neo4j_cypher` - Write KG
- `mcp__isaac__get_neo4j_schema` - Schema

## 📁 ARCHITECTURE

**Stack**: FastAPI/PostgreSQL/Redis/Qdrant | React19/Next15/Tailwind
**Ports**: API:8001, Admin:3003, DB:5433, Redis:6380, Qdrant:6334
**Auth**: Clerk JWT → RLS (Row-Level Security)

### Core Architecture
- **Three-tier Intent Recognition**:
  1. Vector Matching (<100ms) - Qdrant for fast intent matching
  2. Local LLM Router (<300ms) - Complex intent recognition
  3. RAG Augmentation (<500ms) - Novel/ambiguous requests

- **Multi-tenant**: Complete data isolation via PostgreSQL RLS
- **Template System**: Hierarchical inheritance, OpenAPI auto-generation
- **Action System**: Synchronous HTTP → Complex workflows
- **Monitoring**: Prometheus metrics, Grafana dashboards

## 🔧 COMMANDS

```bash
# Backend (ALL commands in Docker)
docker-compose up -d
docker-compose exec coherence-api pytest tests/unit/ -v      # Unit tests
docker-compose exec coherence-api pytest tests/integration/ -v # Integration tests
docker-compose exec coherence-api pytest tests/unit/test_intent_pipeline.py -k "test_name" -v # Single test
docker-compose exec coherence-api alembic upgrade head       # Apply migrations
docker-compose exec coherence-api alembic revision --autogenerate -m "description" # New migration
docker-compose exec coherence-api python -m scripts.create_tenant --name "Default" --industry "General" # Create tenant
docker-compose exec coherence-api python -m scripts.create_admin_key <tenant_id> "Admin Key" # Create API key

# Frontend
cd coherence-admin && pnpm dev       # Dev server on :3003
cd coherence-admin && pnpm build     # Production build
cd coherence-admin && pnpm lint      # Run linter
cd coherence-admin && pnpm typecheck # TypeScript check
cd coherence-admin && pnpm test      # Run Jest tests

# Testing intent pipeline interactively
docker-compose exec coherence-api python -m scripts.run_intent_test

# Monitoring
# Metrics: http://localhost:8002/metrics
# Grafana: http://localhost:3001 (admin/admin)
```

## 🧪 TESTING
- **Framework**: pytest (NOT unittest!)
- **Fixtures**: See tests/conftest.py
- **Async Tests**: Use @pytest.mark.asyncio
- **DB Sessions**: Use sync_db_session or async_db_session fixtures

```python
# Example test pattern
@pytest.mark.asyncio
async def test_example(async_db_session):
    # Your test here
    pass
```

## 🗺️ KEY PATHS

Backend:
- `/src/coherence/api/v1/` - API endpoints
- `/src/coherence/models/` - SQLAlchemy models
- `/src/coherence/schemas/` - Pydantic schemas
- `/src/coherence/intent_pipeline/` - Intent resolution
- `/src/coherence/template_system/` - Template management
- `/src/coherence/openapi_adapter/` - OpenAPI integration

Frontend:
- `/coherence-admin/src/app/` - Next.js app directory
- `/coherence-admin/src/components/` - React components
- `/coherence-admin/src/lib/` - Utilities and hooks
- `/coherence-admin/src/context/` - React contexts

## 🔒 AUTHENTICATION FLOW
1. User authenticates with Clerk
2. Clerk JWT contains `clerk_org_id` 
3. Backend validates JWT and extracts claims
4. PostgreSQL RLS policies use `clerk_org_id` for tenant isolation
5. All queries automatically filtered by tenant

## 📊 DATABASE PATTERNS
- **Migrations**: Alembic with auto-generation
- **RLS**: All tables have policies using `coherence.clerk_org_id`
- **Audit**: Automatic tracking via `audit_log` table
- **Soft Delete**: Use `deleted_at` timestamp

## 🚀 DEPLOYMENT
- Docker Compose for local/dev
- Environment variables in `.env`
- Health checks on all services
- Circuit breakers for external APIs
- Response caching with Redis