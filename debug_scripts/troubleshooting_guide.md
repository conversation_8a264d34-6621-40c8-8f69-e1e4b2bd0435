# Troubleshooting Guide: 401 Errors on /admin/integrations Page

## Overview

The `/admin/integrations` page is experiencing 401 Unauthorized errors. This guide will help you diagnose and fix authentication issues.

## Architecture Overview

1. **Frontend (coherence-admin)**: Next.js app on port 3003
2. **Backend (coherence-api)**: FastAPI app on port 8001  
3. **Authentication Flow**:
   - User logs in via <PERSON>
   - <PERSON> provides JWT token
   - Admin app calls `/api/auth/me` with JWT
   - `/api/auth/me` forwards to `/v1/auth/session-info` on backend
   - Backend validates JWT and returns user/org/permissions
   - Frontend makes API calls with JWT

## Common Issues and Solutions

### 1. No Organization Selected in Clerk

**Symptoms**: 401 error, user appears logged in but can't access admin pages

**Solution**:
```bash
# Check Clerk organization
# Visit: http://localhost:3003/debug/auth
# Ensure organization is selected and user has admin role
```

### 2. Missing JWT Template

**Symptoms**: JWT validation fails, template variables in token

**Solution**:
1. <PERSON><PERSON> to Clerk Dashboard
2. Go to Sessions → JWT Templates  
3. Create template named `coherence_session`
4. Add custom claims:
   ```
   org_id: {{org.id}}
   org_name: {{org.name}}
   org_role: {{org.role}}
   org_slug: {{org.slug}}
   ```

### 3. Missing Environment Variables

**Symptoms**: 500 errors, authentication not working

**Check .env file**:
```bash
# Required variables
CLERK_SECRET_KEY=sk_...
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_...
API_URL=http://localhost:8001
NEXT_PUBLIC_API_URL=http://localhost:8001
SYSTEM_ADMIN_CLERK_USER_ID=user_... (optional)
```

### 4. Docker Service Issues

**Check services**:
```bash
cd /path/to/coherence
docker-compose ps
docker-compose logs coherence-admin
docker-compose logs coherence-api
```

### 5. Permission Issues

**Required permissions for integrations**:
- `integration:read` - View integrations
- `integration:create` - Create integrations
- System admins have all permissions automatically
- Organization admins (admin/owner roles) have integration permissions

## Debugging Steps

### Step 1: Check Service Status
```bash
cd /path/to/coherence
docker-compose ps
```

### Step 2: Check Authentication Endpoints
```bash
# Test admin auth endpoint
curl http://localhost:3003/api/auth/me

# Test API auth endpoint  
curl http://localhost:8001/v1/auth/session-info
```

### Step 3: Check Logs
```bash
# Admin service logs
docker-compose logs coherence-admin | grep -i auth

# API service logs
docker-compose logs coherence-api | grep -i auth
```

### Step 4: Debug Authentication Flow
1. Visit `http://localhost:3003/debug/auth`
2. Check browser console for errors
3. Verify JWT token is present
4. Check organization selection

### Step 5: Test with System Admin
```bash
# Set system admin in .env
SYSTEM_ADMIN_CLERK_USER_ID=user_xxxxxxxxxxxxx

# Restart services
docker-compose restart
```

## Code-Level Debugging

### Check AdminSessionContext
The issue might be in `/coherence-admin/src/context/AdminSessionContext.tsx`:
- Verify token is being fetched correctly
- Check template name is correct (`coherence_session`)
- Ensure error handling is working

### Check Backend Auth Dependencies
The issue might be in `/src/coherence/api/v1/dependencies/auth.py`:
- Verify Clerk client initialization
- Check JWT validation logic
- Ensure organization mapping works

### Check Permission Service
Verify permissions in `/src/coherence/services/permission_service.py`:
- System admins should have all permissions
- Organization admins should have integration permissions

## Testing Solutions

### Test 1: Basic Authentication
```bash
# Run debug script
python debug_scripts/check_auth_status.py
```

### Test 2: API Endpoints
```bash
# Run endpoint test
python debug_scripts/check_integrations_auth.py
```

### Test 3: Manual JWT Test
1. Login to admin interface
2. Open browser dev tools
3. Check Local Storage for Clerk token
4. Use token to test API endpoints manually

## Quick Fixes

### Fix 1: Restart Services
```bash
docker-compose restart coherence-admin coherence-api
```

### Fix 2: Check Organization
1. Visit Clerk dashboard
2. Ensure user is in organization
3. Check user role is admin/owner

### Fix 3: Verify JWT Template
1. Clerk Dashboard → Sessions → JWT Templates
2. Check `coherence_session` template exists
3. Verify custom claims are configured

### Fix 4: Check Environment Variables
```bash
# Check if all required env vars are set
grep -E "(CLERK_|API_)" .env
```

## Advanced Debugging

### Enable Debug Logging
```bash
# Add to docker-compose.yml
services:
  coherence-api:
    environment:
      - LOG_LEVEL=DEBUG
  coherence-admin:
    environment:
      - NEXT_PUBLIC_DEBUG=true
```

### Check Database State
```bash
# Connect to database
docker-compose exec postgres psql -U coherence -d coherence

# Check tenants and organizations
SELECT id, name, clerk_org_id FROM tenants;
```

### Verify Network Communication
```bash
# Check if services can communicate
docker-compose exec coherence-admin curl http://coherence-api:8000/health
```

## Expected Behavior

When working correctly:
1. User logs in via Clerk
2. Organization is selected
3. JWT token includes organization claims
4. `/api/auth/me` returns user/org/permissions
5. User can access `/admin/integrations`
6. Page loads integration list

## Contact Information

If this guide doesn't resolve the issue:
1. Check logs again with fresh eyes
2. Try minimal reproduction steps
3. Document exact error messages
4. Consider if recent changes might have caused the issue
