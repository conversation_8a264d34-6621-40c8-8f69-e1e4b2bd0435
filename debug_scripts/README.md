# Debug Scripts for Coherence Authentication Issues

This directory contains scripts to help diagnose and fix 401 authentication errors on the `/admin/integrations` page.

## Quick Start

```bash
cd /path/to/coherence
chmod +x debug_scripts/quick_check.sh
./debug_scripts/quick_check.sh
```

## Available Scripts

### 1. `quick_check.sh`
Quick health check of services and auth endpoints.

```bash
./debug_scripts/quick_check.sh
```

### 2. `check_auth_status.py`
Comprehensive authentication system status check.

```bash
python debug_scripts/check_auth_status.py
```

### 3. `check_integration_permissions.py`
Specific analysis of integration permission flow.

```bash
python debug_scripts/check_integration_permissions.py
```

### 4. `test_integrations_401.py`
Simple test to reproduce the 401 error.

```bash
python debug_scripts/test_integrations_401.py
```

### 5. `check_integrations_auth.py`
Test authentication endpoints with curl.

```bash
python debug_scripts/check_integrations_auth.py
```

### 6. `troubleshooting_guide.md`
Comprehensive troubleshooting guide.

```bash
less debug_scripts/troubleshooting_guide.md
```

## Common Issues and Quick Fixes

### Issue 1: Services Not Running
```bash
docker-compose up -d
docker-compose ps
```

### Issue 2: No Organization Selected
1. Visit http://localhost:3003/debug/auth
2. Check if organization is selected
3. Select an organization in Clerk

### Issue 3: JWT Template Missing
1. Login to Clerk Dashboard
2. Go to Sessions → JWT Templates
3. Create `coherence_session` template
4. Add required custom claims

### Issue 4: Permission Issues
1. Check user role in organization (must be admin/owner)
2. Verify SYSTEM_ADMIN_CLERK_USER_ID in .env for system admin
3. Check permission logs:
   ```bash
   docker-compose logs coherence-api | grep -i permission
   ```

## Debug Endpoints

- http://localhost:3003/debug/auth - Frontend auth debug
- http://localhost:3003/api/auth/me - Admin auth endpoint
- http://localhost:8001/v1/auth/session-info - API auth endpoint
- http://localhost:8001/v1/admin/integrations - Integrations endpoint

## Log Commands

```bash
# Check admin logs
docker-compose logs coherence-admin | grep -i auth

# Check API logs
docker-compose logs coherence-api | grep -i auth

# Check real-time logs
docker-compose logs -f coherence-admin
docker-compose logs -f coherence-api
```

## Environment Variables

Make sure these are set in `.env`:

```bash
CLERK_SECRET_KEY=sk_...
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_...
API_URL=http://localhost:8001
NEXT_PUBLIC_API_URL=http://localhost:8001
SYSTEM_ADMIN_CLERK_USER_ID=user_... (optional, for system admin)
```

## Next Steps

1. Run quick_check.sh first
2. If issues found, run specific diagnostic scripts
3. Check troubleshooting_guide.md for detailed solutions
4. Test with browser at http://localhost:3003/admin/integrations
5. Check browser console for JavaScript errors

## Manual Testing

1. Open http://localhost:3003/admin/integrations
2. Open browser Dev Tools → Console
3. Check for errors and API requests
4. Verify JWT token contains organization claims
5. Check AdminSessionContext state in console
