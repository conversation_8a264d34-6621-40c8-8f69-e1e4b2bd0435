#!/usr/bin/env python3
"""
Verify the JWT template mismatch issue
"""

import os
import re


def check_jwt_template_mismatch():
    """Check for JWT template configuration mismatch"""
    
    print("=== JWT Template Mismatch Check ===")
    print()
    
    # Check admin .env file
    admin_env = "/Users/<USER>/Documents/projects/coherence/coherence-admin/.env"
    
    admin_template = None
    if os.path.exists(admin_env):
        with open(admin_env, 'r') as f:
            for line in f:
                if 'CLERK_SESSION_TOKEN_TEMPLATE' in line:
                    admin_template = line.strip().split('=')[1] if '=' in line else None
                    break
    
    print(f"Admin template configuration: {admin_template}")
    
    # Check backend code
    auth_file = "/Users/<USER>/Documents/projects/coherence/src/coherence/api/v1/dependencies/auth.py"
    
    backend_templates = []
    if os.path.exists(auth_file):
        with open(auth_file, 'r') as f:
            content = f.read()
            # Look for template patterns
            template_patterns = re.findall(r"template[:\s]*['\"]([^'\"]+)['\"]", content)
            backend_templates = list(set(template_patterns))
    
    print(f"Backend expects templates: {backend_templates}")
    
    # Check AdminSessionContext 
    context_file = "/Users/<USER>/Documents/projects/coherence/coherence-admin/src/context/AdminSessionContext.tsx"
    
    context_templates = []
    if os.path.exists(context_file):
        with open(context_file, 'r') as f:
            content = f.read()
            # Look for template patterns
            template_patterns = re.findall(r"template[:\s]*['\"]([^'\"]+)['\"]", content)
            context_templates = list(set(template_patterns))
    
    print(f"Frontend context expects templates: {context_templates}")
    
    print()
    print("=== Analysis ===")
    
    # Check for mismatches
    if admin_template == 'default' and 'coherence_session' in backend_templates:
        print("❌ MISMATCH FOUND!")
        print("   Admin .env uses 'default'")
        print("   Backend expects 'coherence_session'")
        print("   This will cause 401 authentication errors!")
        print()
        print("Solution:")
        print("   Change CLERK_SESSION_TOKEN_TEMPLATE=default")
        print("   To:   CLERK_SESSION_TOKEN_TEMPLATE=coherence_session")
        print("   In:   coherence-admin/.env")
    elif admin_template == 'coherence_session' and 'coherence_session' in backend_templates:
        print("✅ Configuration looks correct!")
        print("   Both admin and backend expect 'coherence_session'")
        print("   The 401 error might be caused by something else.")
    else:
        print("⚠️ Configuration unclear")
        print("   Please check the template configuration manually")
    
    print()
    
    # Additional checks
    print("=== Additional Checks ===")
    
    # Check if Clerk keys are present
    if os.path.exists(admin_env):
        with open(admin_env, 'r') as f:
            content = f.read()
            
        has_secret = 'CLERK_SECRET_KEY=' in content
        has_publishable = 'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=' in content
        
        print(f"Clerk Secret Key present: {'✅' if has_secret else '❌'}")
        print(f"Clerk Publishable Key present: {'✅' if has_publishable else '❌'}")
    
    # Check API URL configuration
    if os.path.exists(admin_env):
        with open(admin_env, 'r') as f:
            for line in f:
                if 'NEXT_PUBLIC_API_URL' in line:
                    api_url = line.strip().split('=')[1] if '=' in line else None
                    print(f"API URL configured as: {api_url}")
                    
                    if api_url and not api_url.endswith('/v1'):
                        print("⚠️ Warning: API URL doesn't end with /v1")
                    break

if __name__ == "__main__":
    check_jwt_template_mismatch()
