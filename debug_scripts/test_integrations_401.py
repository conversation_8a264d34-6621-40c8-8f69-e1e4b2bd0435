#!/usr/bin/env python3
"""
Simple test to reproduce the 401 error on integrations page
"""


import requests


def test_integrations_endpoint():
    """Test accessing the integrations endpoint"""
    
    print("=== Testing Integrations Endpoint ===")
    print()
    
    # URLs
    admin_url = "http://localhost:3003"
    api_url = "http://localhost:8001"
    
    # Test 1: Check if services are running
    print("1. Checking if services are running...")
    
    try:
        admin_response = requests.get(f"{admin_url}/health", timeout=5)
        print(f"   Admin service: {admin_response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   Admin service: ERROR - {e}")
        return
    
    try:
        api_response = requests.get(f"{api_url}/health", timeout=5)
        print(f"   API service: {api_response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   API service: ERROR - {e}")
        return
    
    print()
    
    # Test 2: Try accessing integrations endpoint without auth
    print("2. Testing integrations endpoint without authentication...")
    try:
        response = requests.get(f"{api_url}/v1/admin/integrations", timeout=5)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 401:
            print("   ✓ Expected 401 - endpoint requires authentication")
        else:
            print("   ⚠ Unexpected status code")
    except requests.exceptions.RequestException as e:
        print(f"   Error: {e}")
    
    print()
    
    # Test 3: Try accessing auth endpoints
    print("3. Testing auth endpoints...")
    
    try:
        response = requests.get(f"{admin_url}/api/auth/me", timeout=5)
        print(f"   /api/auth/me status: {response.status_code}")
        if response.status_code == 401:
            print("   ✓ Expected 401 - auth required")
    except requests.exceptions.RequestException as e:
        print(f"   Error: {e}")
    
    try:
        response = requests.get(f"{api_url}/v1/auth/session-info", timeout=5)
        print(f"   /v1/auth/session-info status: {response.status_code}")
        if response.status_code == 401:
            print("   ✓ Expected 401 - auth required")
    except requests.exceptions.RequestException as e:
        print(f"   Error: {e}")
    
    print()
    
    # Test 4: Simulate frontend access
    print("4. Simulating frontend page access...")
    print("   The /admin/integrations page should:")
    print("   a. Load the React component")
    print("   b. useAdminSession hook gets auth state")
    print("   c. Check if user has 'integration:read' permission")
    print("   d. Make API call to /v1/admin/integrations")
    print("   e. Display integrations or show error")
    print()
    
    # Test 5: Check error scenarios
    print("5. Common 401 scenarios:")
    print("   - User not logged in")
    print("   - No organization selected in Clerk")
    print("   - JWT template missing custom claims")
    print("   - User doesn't have admin role in organization")
    print("   - Backend can't validate JWT")
    print()
    
    print("=== Test Complete ===")
    print()
    print("To debug the actual issue:")
    print("1. Open http://localhost:3003/admin/integrations in browser")
    print("2. Open browser Developer Tools → Console")
    print("3. Look for error messages and network requests")
    print("4. Check if JWT token is present and contains org info")
    print("5. Verify the API calls being made")

if __name__ == "__main__":
    test_integrations_endpoint()
