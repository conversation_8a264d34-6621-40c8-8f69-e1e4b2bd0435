#!/usr/bin/env python3
"""
Check the current status of Coherence authentication system
"""

import asyncio
import os
import subprocess
from datetime import datetime


async def check_docker_services():
    """Check if Docker services are running"""
    print("=== Docker Services Status ===")
    
    try:
        # Check if docker-compose is running
        result = subprocess.run(
            ["docker-compose", "ps"], 
            capture_output=True, 
            text=True,
            cwd="/Users/<USER>/Documents/projects/coherence"
        )
        
        if result.returncode == 0:
            print("Docker services:")
            print(result.stdout)
        else:
            print("Error checking docker services:")
            print(result.stderr)
            
        # Get logs for auth-related services
        print("\n=== Recent Auth Logs ===")
        
        # Check admin logs
        result = subprocess.run(
            ["docker-compose", "logs", "--tail=20", "coherence-admin"], 
            capture_output=True, 
            text=True,
            cwd="/Users/<USER>/Documents/projects/coherence"
        )
        
        if result.returncode == 0:
            print("Admin service logs:")
            print(result.stdout)
        
        # Check API logs
        result = subprocess.run(
            ["docker-compose", "logs", "--tail=20", "coherence-api"], 
            capture_output=True, 
            text=True,
            cwd="/Users/<USER>/Documents/projects/coherence"
        )
        
        if result.returncode == 0:
            print("\nAPI service logs:")
            print(result.stdout)
            
    except Exception as e:
        print(f"Error checking Docker services: {e}")

def check_environment_variables():
    """Check if required environment variables are set"""
    print("\n=== Environment Variables ===")
    
    env_vars = [
        'CLERK_SECRET_KEY',
        'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
        'API_URL',
        'NEXT_PUBLIC_API_URL',
        'SYSTEM_ADMIN_CLERK_USER_ID'
    ]
    
    # Check .env file
    env_file = "/Users/<USER>/Documents/projects/coherence/.env"
    print(f"\nChecking {env_file}:")
    
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            lines = f.readlines()
            for var in env_vars:
                found = False
                for line in lines:
                    if line.strip().startswith(f"{var}="):
                        found = True
                        # Don't print the actual value for security
                        print(f"✓ {var} is set")
                        break
                if not found:
                    print(f"✗ {var} is NOT set")
    else:
        print(f"Error: {env_file} not found")

def check_clerk_configuration():
    """Check Clerk configuration"""
    print("\n=== Clerk Configuration ===")
    
    print("Required Clerk setup:")
    print("1. Organization-based authentication must be enabled")
    print("2. JWT template 'coherence_session' should be configured with:")
    print("   - Custom claim 'org_id': {{org.id}}")
    print("   - Custom claim 'org_name': {{org.name}}")
    print("   - Custom claim 'org_role': {{org.role}}")
    print("   - Custom claim 'org_slug': {{org.slug}}")
    print("3. Users must select an organization in Clerk")
    print("4. Organization admins (admin/owner roles) can access integrations")

def check_permission_service():
    """Check permission service configuration"""
    print("\n=== Permission Service ===")
    
    print("The integrations page requires:")
    print("1. 'integration:read' permission to view integrations")
    print("2. 'integration:create' permission to create integrations")
    print("3. System admins have all permissions ('system:*')")
    print("4. Organization admins (admin/owner) have integration permissions")

def provide_troubleshooting_steps():
    """Provide troubleshooting steps"""
    print("\n=== Troubleshooting Steps ===")
    
    print("1. Check if services are running:")
    print("   docker-compose ps")
    print()
    
    print("2. Check authentication logs:")
    print("   docker-compose logs coherence-admin | grep -i auth")
    print("   docker-compose logs coherence-api | grep -i auth")
    print()
    
    print("3. Visit debug endpoints:")
    print("   http://localhost:3003/debug/auth")
    print("   http://localhost:8001/v1/debug/auth")
    print()
    
    print("4. Verify Clerk organization selection:")
    print("   - Log into the admin interface")
    print("   - Make sure an organization is selected")
    print("   - Check user role in organization")
    print()
    
    print("5. Check JWT template in Clerk Dashboard:")
    print("   - Go to Sessions -> JWT Templates")
    print("   - Verify 'coherence_session' template exists")
    print("   - Check custom claims configuration")
    print()
    
    print("6. Test with system admin:")
    print("   - Set SYSTEM_ADMIN_CLERK_USER_ID in .env")
    print("   - Login with system admin account")

async def main():
    print("Coherence Authentication Debug Report")
    print(f"Generated at: {datetime.now().isoformat()}")
    print("="*50)
    
    await check_docker_services()
    check_environment_variables()
    check_clerk_configuration()
    check_permission_service()
    provide_troubleshooting_steps()
    
    print("\n" + "="*50)
    print("Debug report complete. Please check the output above for any issues.")

if __name__ == "__main__":
    asyncio.run(main())
