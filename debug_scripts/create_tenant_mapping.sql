-- SQL script to create tenant mapping for Clerk organization

-- First, check if tenant already exists
SELECT id, name, clerk_org_id 
FROM tenants 
WHERE clerk_org_id = 'org_2wx2bWKn0Off8bfc2gCNvlMv8IW';

-- If no tenant exists, create one
INSERT INTO tenants (
    id,
    name,
    clerk_org_id,
    clerk_organization_id,
    industry_pack,
    compliance_tier,
    settings,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'Phaseloch',
    'org_2wx2bWKn0Off8bfc2gCNvlMv8IW',
    'org_2wx2bWKn0Off8bfc2gCNvlMv8IW',
    'technology',
    'standard',
    '{"is_admin": true}',
    NOW(),
    NOW()
)
ON CONFLICT (clerk_org_id) DO NOTHING;

-- Verify the tenant was created
SELECT id, name, clerk_org_id, created_at
FROM tenants 
WHERE clerk_org_id = 'org_2wx2bWKn0Off8bfc2gCNvlMv8IW';
