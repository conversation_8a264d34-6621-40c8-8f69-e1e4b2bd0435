#!/usr/bin/env python3
"""
Create tenant mapping for Clerk organization
"""

import asyncio
import sys

sys.path.append('/Users/<USER>/Documents/projects/coherence')

async def create_tenant_mapping():
    """Create tenant mapping for the Clerk organization"""
    
    print("=== Creating Tenant Mapping ===")
    print()
    
    from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
    from sqlalchemy.orm import sessionmaker

    from src.coherence.core.config import settings
    from src.coherence.models.tenant import Tenant
    
    # Your Clerk organization details from the logs
    clerk_org_id = "org_2wx2bWKn0Off8bfc2gCNvlMv8IW"
    clerk_org_name = "Phaseloch"
    
    print(f"Clerk Organization ID: {clerk_org_id}")
    print(f"Clerk Organization Name: {clerk_org_name}")
    print()
    
    # Create database connection
    engine = create_async_engine(
        settings.DATABASE_URL,
        echo=True
    )
    
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    try:
        async with async_session() as db:
            # Check if tenant already exists
            from sqlalchemy.future import select
            
            result = await db.execute(
                select(Tenant).where(Tenant.clerk_org_id == clerk_org_id)
            )
            existing_tenant = result.scalar_one_or_none()
            
            if existing_tenant:
                print("✓ Tenant already exists:")
                print(f"  ID: {existing_tenant.id}")
                print(f"  Name: {existing_tenant.name}")
                print(f"  Clerk Org ID: {existing_tenant.clerk_org_id}")
                return
            
            # Create new tenant
            new_tenant = Tenant(
                name=clerk_org_name,
                clerk_org_id=clerk_org_id,
                clerk_organization_id=clerk_org_id,  # For backward compatibility
                industry_pack="technology",  # Default
                compliance_tier="standard",  # Default
                settings={"is_admin": True}  # Give admin privileges
            )
            
            db.add(new_tenant)
            await db.commit()
            await db.refresh(new_tenant)
            
            print("✓ Created new tenant:")
            print(f"  ID: {new_tenant.id}")
            print(f"  Name: {new_tenant.name}")
            print(f"  Clerk Org ID: {new_tenant.clerk_org_id}")
            print()
            print("The tenant mapping is now complete!")
            print("You should be able to access /admin/integrations now.")
            
    except Exception as e:
        print(f"Error creating tenant mapping: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await engine.dispose()

if __name__ == "__main__":
    asyncio.run(create_tenant_mapping())
