# Fix for 401 Errors on /admin/integrations

## Root Cause Identified

The issue is with the JWT template configuration. Looking at your files:

### Current Configuration (INCORRECT):
```bash
# In coherence-admin/.env
CLERK_SESSION_TOKEN_TEMPLATE=default
```

### Expected Configuration (CORRECT):
```bash
# Should be:
CLERK_SESSION_TOKEN_TEMPLATE=coherence_session
```

## The Problem

1. **Frontend** is configured to use template `default`
2. **Backend** expects template `coherence_session`
3. This causes JWT validation to fail, resulting in 401 errors

## Solution

### Option 1: Fix the Environment Variable (Recommended)

Update `coherence-admin/.env`:

```bash
# Change this line:
CLERK_SESSION_TOKEN_TEMPLATE=default

# To this:
CLERK_SESSION_TOKEN_TEMPLATE=coherence_session
```

### Option 2: Update Backend Code

Alternative: Modify the backend to use `default` template instead.

## Steps to Fix

1. **Edit the admin environment file**:
```bash
nano /Users/<USER>/Documents/projects/coherence/coherence-admin/.env
```

2. **Change the template line**:
```bash
# From:
CLERK_SESSION_TOKEN_TEMPLATE=default

# To:
CLERK_SESSION_TOKEN_TEMPLATE=coherence_session
```

3. **Ensure the JWT template exists in Clerk**:
   - Login to Clerk Dashboard
   - Go to Sessions → JWT Templates
   - Create template named `coherence_session` if it doesn't exist
   - Add these custom claims:
   ```json
   {
     "__session": {
       "org_id": "{{org.id}}",
       "org_name": "{{org.name}}",
       "org_role": "{{org.role}}",
       "org_slug": "{{org.slug}}"
     }
   }
   ```

4. **Restart the services**:
```bash
docker-compose restart coherence-admin coherence-api
```

## Alternative Check

If you prefer to keep using `default`, you can update the backend code in:
`src/coherence/api/v1/dependencies/auth.py`

Change line that requests the token:
```python
# From:
token = await getToken({ template: 'coherence_session' });

# To:
token = await getToken({ template: 'default' });
```

## Verification

After making the changes:

1. Visit http://localhost:3003/admin/integrations
2. Check browser console for errors
3. Run the diagnostic script:
```bash
python debug_scripts/check_integrations_flow.py
```

The authentication should now work properly and you should be able to access the integrations page.
