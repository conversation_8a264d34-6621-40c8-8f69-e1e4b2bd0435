#!/bin/bash

# Quick check script for Coherence authentication issues
echo "=== Coherence Authentication Quick Check ==="
echo

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "Error: Please run this script from the Coherence project root directory"
    exit 1
fi

# Check Docker Compose services
echo "1. Checking Docker services status..."
docker-compose ps
echo

# Check if services are responding
echo "2. Testing service health..."

echo "Admin health:"
curl -s http://localhost:3003/health || echo "Admin service not responding"
echo

echo "API health:"
curl -s http://localhost:8001/health || echo "API service not responding"
echo

# Check environment variables
echo "3. Checking key environment variables..."
if [ -f ".env" ]; then
    echo "Checking .env file:"
    grep -E "^(CLERK_SECRET_KEY|NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY|API_URL)" .env | sed 's/=.*/=***masked***/'
else
    echo ".env file not found"
fi
echo

# Check recent logs for auth errors
echo "4. Checking recent authentication logs..."
echo "Admin logs (last 10 lines with 'auth'):"
docker-compose logs --tail=50 coherence-admin | grep -i auth | tail -10
echo

echo "API logs (last 10 lines with 'auth'):"
docker-compose logs --tail=50 coherence-api | grep -i auth | tail -10
echo

# Test auth endpoints
echo "5. Testing authentication endpoints..."
echo "Testing /api/auth/me (should return 401 without token):"
curl -s -o /dev/null -w "Status: %{http_code}\n" http://localhost:3003/api/auth/me
echo

echo "Testing /v1/auth/session-info (should return 401 without token):"
curl -s -o /dev/null -w "Status: %{http_code}\n" http://localhost:8001/v1/auth/session-info
echo

echo "Testing /v1/admin/integrations (should return 401 without token):"
curl -s -o /dev/null -w "Status: %{http_code}\n" http://localhost:8001/v1/admin/integrations
echo

echo "=== Quick Check Complete ==="
echo
echo "Next steps if you see issues:"
echo "1. Make sure services are running: docker-compose up -d"
echo "2. Check Clerk organization selection at: http://localhost:3003/debug/auth"
echo "3. Verify JWT template in Clerk Dashboard"
echo "4. Check full troubleshooting guide: debug_scripts/troubleshooting_guide.md"
