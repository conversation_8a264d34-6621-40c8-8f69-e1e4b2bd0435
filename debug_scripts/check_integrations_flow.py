#!/usr/bin/env python3
"""
Specific diagnostic for integrations endpoint behavior
"""

import subprocess

import requests


def check_integrations_flow():
    """Check the specific flow for integrations page"""
    
    print("=== Integrations Flow Diagnostic ===")
    print()
    
    # 1. Check if we can access the admin page
    print("1. Testing admin routes...")
    
    try:
        # Try the health endpoint with full path
        response = requests.get("http://localhost:3003/health", timeout=5)
        print(f"   Admin health (/health): {response.status_code}")
        
        # Try a different path
        response = requests.get("http://localhost:3003/", timeout=5)
        print(f"   Admin root (/): {response.status_code}")
        
        # Try the admin path
        response = requests.get("http://localhost:3003/admin", timeout=5)
        print(f"   Admin dashboard (/admin): {response.status_code}")
        
    except Exception as e:
        print(f"   Error testing admin routes: {e}")
    
    print()
    
    # 2. Check the API integrations endpoint with full path
    print("2. Testing API integrations endpoint...")
    
    try:
        # Test with different paths since we got 307 (redirect)
        response = requests.get("http://localhost:8001/v1/admin/integrations", timeout=5, allow_redirects=False)
        print(f"   /v1/admin/integrations (no redirect): {response.status_code}")
        
        if response.status_code == 307:
            location = response.headers.get('Location', 'No location header')
            print(f"   Redirect location: {location}")
        
        # Test with redirects allowed
        response = requests.get("http://localhost:8001/v1/admin/integrations", timeout=5)
        print(f"   /v1/admin/integrations (with redirect): {response.status_code}")
        
    except Exception as e:
        print(f"   Error testing integrations endpoint: {e}")
    
    print()
    
    # 3. Check the recent logs more specifically
    print("3. Checking recent logs for integrations...")
    
    try:
        # Get more recent logs
        result = subprocess.run(
            ["docker-compose", "logs", "--tail=50", "coherence-api"],
            capture_output=True,
            text=True,
            cwd="/Users/<USER>/Documents/projects/coherence"
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            # Look for integration or admin related logs
            relevant_lines = []
            for line in lines:
                if any(keyword in line.lower() for keyword in ['integration', 'admin', 'v1/admin']):
                    relevant_lines.append(line)
            
            if relevant_lines:
                print("   Recent API logs (integration/admin related):")
                for line in relevant_lines[-10:]:
                    print(f"   {line}")
            else:
                print("   No integration-related logs in recent API logs")
        
    except Exception as e:
        print(f"   Error checking logs: {e}")
    
    print()
    
    # 4. Test with authentication
    print("4. The issue might be client-side...")
    print("   The logs show authentication is working:")
    print("   - JWT validation successful")
    print("   - User has org:admin role")
    print("   - User is marked as system admin")
    print("   - Organization is properly selected")
    print()
    print("   This suggests the issue might be in the frontend:")
    print("   - JavaScript errors in the browser")
    print("   - Client-side routing issues")
    print("   - AdminSessionContext not properly handling the auth state")
    print()
    
    # 5. Browser testing recommendations
    print("5. Next debugging steps:")
    print("   a. Open browser and go to: http://localhost:3003/admin/integrations")
    print("   b. Open Developer Tools → Console")
    print("   c. Look for any JavaScript errors")
    print("   d. Check Network tab for failed requests")
    print("   e. Check if AdminSessionContext is getting the right data")
    print()
    print("   f. Visit debug endpoint: http://localhost:3003/debug/auth")
    print("   g. Check if organization is selected and permissions are correct")
    print()
    
    # 6. Check environment variables more thoroughly
    print("6. Checking environment configuration...")
    
    env_file = "/Users/<USER>/Documents/projects/coherence/.env"
    try:
        with open(env_file, 'r') as f:
            lines = f.readlines()
            
        # Look for key auth-related vars
        key_vars = ['CLERK_SECRET_KEY', 'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY', 'API_URL', 'NEXT_PUBLIC_API_URL']
        
        print("   Environment variables:")
        for var in key_vars:
            found = False
            for line in lines:
                if line.strip().startswith(f"{var}="):
                    print(f"   ✓ {var} is set")
                    found = True
                    break
            if not found:
                print(f"   ✗ {var} is NOT set")
    except Exception as e:
        print(f"   Error reading .env file: {e}")
    
    print()
    print("=== Diagnostic Complete ===")
    print()
    print("Based on the logs, authentication appears to be working correctly.")
    print("The 401 error is likely occurring in the browser/frontend.")
    print("Please check the browser console and network tab for more details.")

if __name__ == "__main__":
    check_integrations_flow()
