# SOLUTION: 401 Error on /admin/integrations

## Root Cause Found! 🎯

The logs show that authentication is working perfectly, but **your Clerk organization is not mapped to a Coherence tenant**.

### What's Happening:
1. ✅ You're successfully authenticated
2. ✅ You have all required permissions (`integration:read`, etc.)
3. ✅ You're marked as system admin
4. ❌ **BUT**: The session shows `tenant_id: None`
5. ❌ The integrations endpoint requires a tenant context

### Key Log Evidence:
```
'tenant_id': None,
'tenant_name': None,
'org_id': 'org_2wx2bWKn0Off8bfc2gCNvlMv8IW',
'org_name': 'Phaseloch',
'permissions': {...'integration:read'...}
```

## Quick Fix Options

### Option 1: Using SQL (Fastest)

1. **Connect to the database**:
```bash
docker-compose exec coherence-db psql -U coherence -d coherence
```

2. **Run this SQL**:
```sql
INSERT INTO tenants (
    id,
    name,
    clerk_org_id,
    clerk_organization_id,
    industry_pack,
    compliance_tier,
    settings,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'Phaseloch',
    'org_2wx2bWKn0Off8bfc2gCNvlMv8IW',
    'org_2wx2bWKn0Off8bfc2gCNvlMv8IW',
    'technology',
    'standard',
    '{"is_admin": true}',
    NOW(),
    NOW()
)
ON CONFLICT (clerk_org_id) DO NOTHING;
```

3. **Exit and test**:
```bash
\q
# Visit http://localhost:3003/admin/integrations
```

### Option 2: Using Python Script

```bash
cd /Users/<USER>/Documents/projects/coherence
python debug_scripts/create_tenant_mapping.py
```

### Option 3: Using Admin API (If you have system admin privileges)

Since you're already a system admin, you could also create the tenant via the admin API, but the SQL method is simpler.

## Verification

After creating the tenant mapping:

1. **Restart the services** (optional but recommended):
```bash
docker-compose restart coherence-admin coherence-api
```

2. **Check the logs again**:
```bash
docker-compose logs coherence-api | grep session-info
```

3. **Visit the page**:
- Go to http://localhost:3003/admin/integrations
- You should now see the integrations page without 401 errors

## What This Fixes

- The `/v1/auth/session-info` will now return a proper `tenant_id`
- The integrations endpoint will have the required tenant context
- You'll be able to view and manage integrations

## Why This Happened

This is a common issue when:
1. Clerk is set up and working
2. But the Coherence database doesn't have a corresponding tenant record
3. The system knows who you are, but doesn't know which "tenant" (organization) context to use

Creating this mapping connects your Clerk organization to a Coherence tenant, enabling full functionality.
