// Fixed apiClient.ts - adds Authorization header when token is provided

interface ApiClientOptions extends RequestInit {
  orgId?: string | null;
  token?: string | null; // Add token support
}

async function apiClient<T = unknown>(
  endpoint: string,
  options: ApiClientOptions = {}
): Promise<T> {
  const { orgId, token, ...fetchOptions } = options;
  const headers = new Headers(fetchOptions.headers || {});

  // Add Authorization header if token is provided
  if (token) {
    headers.append('Authorization', `Bearer ${token}`);
  }

  if (orgId) {
    headers.append('X-Tenant-ID', orgId);
  }
  headers.append('Content-Type', 'application/json');

  // Check if this is a Next.js API route (starts with /api/) or a backend endpoint
  let fullUrl: string;
  if (endpoint.startsWith('/api/')) {
    // This is a Next.js API route - don't prepend the backend URL
    fullUrl = endpoint;
  } else if (endpoint.startsWith('http')) {
    // This is already a full URL
    fullUrl = endpoint;
  } else {
    // This is a backend endpoint - prepend the API base URL
    let apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_COHERENCE_API_URL || 'http://localhost:8001/v1';
    
    // If we're in the browser and the API URL contains Docker hostnames, convert to localhost
    if (typeof window !== 'undefined') {
      // Replace Docker hostnames with localhost for browser requests
      apiBaseUrl = apiBaseUrl
        .replace('http://coherence-api:8000', 'http://localhost:8001')
        .replace('http://coherence-api:8001', 'http://localhost:8001');
    }
    
    fullUrl = `${apiBaseUrl}${endpoint}`;
  }
  
  console.log(`Making API call to: ${fullUrl}`);
  console.log(`With Authorization header: ${token ? 'Yes' : 'No'}`);
  
  const response = await fetch(fullUrl, {
    ...fetchOptions,
    headers,
  });

  if (!response.ok) {
    let errorData;
    try {
      errorData = await response.json();
    } catch {
      errorData = { message: response.statusText || 'An unknown error occurred' };
    }
    const errorMessage = errorData?.detail || errorData?.message || `HTTP error ${response.status}`;
    const error = new Error(errorMessage);
    throw error;
  }

  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json() as Promise<T>;
  } else {
    const text = await response.text();
    return text ? (JSON.parse(text) as T) : (null as unknown as T);
  }
}

export default apiClient;
