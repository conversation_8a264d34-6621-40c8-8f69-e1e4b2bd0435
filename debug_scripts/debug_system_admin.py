#!/usr/bin/env python
"""
Debug script to check system admin status determination and permissions.

This script checks:
1. How the system admin status is determined in backend auth
2. Current environment variables for system admin configuration
3. JWT token content related to system admin status
4. Permissions assigned to system admin

Usage: python -m debug_scripts.debug_system_admin
"""

import json
import logging
import os
import sys
from pprint import pprint

import requests

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("system-admin-debug")

def check_system_admin_env_vars():
    """Check environment variables related to system admin configuration."""
    logger.info("Checking environment variables for system admin configuration")
    
    # The primary variable used for system admin determination
    system_admin_clerk_user_id = os.environ.get("SYSTEM_ADMIN_CLERK_USER_ID")
    if system_admin_clerk_user_id:
        logger.info(f"SYSTEM_ADMIN_CLERK_USER_ID is set to: {system_admin_clerk_user_id}")
    else:
        logger.warning("SYSTEM_ADMIN_CLERK_USER_ID is not set!")
        
    # The system admin API key variable
    system_admin_api_key = os.environ.get("SYSTEM_ADMIN_API_KEY")
    if system_admin_api_key:
        logger.info("SYSTEM_ADMIN_API_KEY is set")
    else:
        logger.warning("SYSTEM_ADMIN_API_KEY is not set!")
    
    # Clerk configuration variables
    clerk_secret_key = os.environ.get("CLERK_SECRET_KEY")
    if clerk_secret_key:
        logger.info("CLERK_SECRET_KEY is set")
    else:
        logger.error("CLERK_SECRET_KEY is not set! This will cause JWT validation to fail.")
    
    # JWT template configuration
    clerk_jwt_template = os.environ.get("CLERK_SESSION_TOKEN_TEMPLATE")
    if clerk_jwt_template:
        logger.info(f"CLERK_SESSION_TOKEN_TEMPLATE is set to: {clerk_jwt_template}")
    else:
        logger.info("CLERK_SESSION_TOKEN_TEMPLATE is not set, using default 'coherence_session'")
    
    # Database connection
    database_url = os.environ.get("COHERENCE_DATABASE_URL")
    if database_url:
        logger.info("COHERENCE_DATABASE_URL is set")
    else:
        logger.warning("COHERENCE_DATABASE_URL is not set!")

def check_session_info_endpoint(token=None, api_key=None, base_url='http://localhost:8001'):
    """
    Test the session-info endpoint to check how system admin status is returned.
    """
    logger.info(f"Testing session-info endpoint at {base_url}/v1/auth/session-info")
    
    headers = {'Content-Type': 'application/json'}
    
    if token:
        headers['Authorization'] = f'Bearer {token}'
        logger.info(f"Using JWT token (first 20 chars): {token[:20]}...")
    elif api_key:
        headers['X-API-Key'] = api_key
        logger.info(f"Using API key (first 8 chars): {api_key[:8]}...")
    else:
        logger.error("No authentication provided. Please provide a token or API key.")
        return None
    
    try:
        response = requests.get(f"{base_url}/v1/auth/session-info", headers=headers)
        logger.info(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info("Session info response:")
            pprint(data)
            
            # Check system admin status
            is_system_admin = data.get("is_system_admin", False)
            logger.info(f"is_system_admin flag: {is_system_admin}")
            
            # Check permissions
            permissions = data.get("permissions", [])
            logger.info(f"Number of permissions: {len(permissions)}")
            if "system:*" in permissions:
                logger.info("'system:*' permission is present")
            else:
                logger.warning("'system:*' permission is NOT present")
            
            return data
        else:
            logger.error(f"Error response: {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"Exception when calling session-info: {str(e)}")
        return None

def check_me_endpoint(token=None, base_url='http://localhost:3003'):
    """
    Test the frontend /api/auth/me endpoint to check how system admin status is forwarded.
    """
    logger.info(f"Testing frontend /api/auth/me endpoint at {base_url}/api/auth/me")
    
    headers = {'Content-Type': 'application/json'}
    
    if token:
        headers['Authorization'] = f'Bearer {token}'
        logger.info(f"Using JWT token (first 20 chars): {token[:20]}...")
    else:
        logger.error("No JWT token provided for /api/auth/me endpoint.")
        return None
    
    try:
        response = requests.get(f"{base_url}/api/auth/me", headers=headers)
        logger.info(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info("Frontend /api/auth/me response:")
            pprint(data)
            
            # Check org data structure
            org_data = data.get("org", {})
            logger.info(f"Organization data: {org_data}")
            
            # Check permissions
            permissions = data.get("permissions", [])
            logger.info(f"Number of permissions: {len(permissions)}")
            if "system:*" in permissions:
                logger.info("'system:*' permission is present in frontend response")
            else:
                logger.warning("'system:*' permission is NOT present in frontend response")
            
            return data
        else:
            logger.error(f"Error response: {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"Exception when calling /api/auth/me: {str(e)}")
        return None

def parse_jwt_token(token):
    """Parse and display the content of a JWT token."""
    if not token:
        logger.error("No token provided for parsing")
        return None
    
    try:
        # JWT tokens consist of 3 parts separated by dots
        parts = token.split('.')
        if len(parts) != 3:
            logger.error(f"Invalid JWT token format - expected 3 parts, got {len(parts)}")
            return None
        
        # Decode the payload (middle part)
        import base64
        
        # Ensure proper padding
        def fix_padding(jwt_part):
            padding = 4 - (len(jwt_part) % 4)
            if padding < 4:
                jwt_part += '=' * padding
            return jwt_part
            
        # Extract and decode the payload
        payload = parts[1]
        payload = fix_padding(payload)
        payload = base64.b64decode(payload)
        payload_json = json.loads(payload)
        
        logger.info("JWT Token Payload:")
        pprint(payload_json)
        
        # Check for system admin related claims
        if 'is_system_admin' in payload_json:
            logger.info(f"JWT contains 'is_system_admin' claim: {payload_json['is_system_admin']}")
        else:
            logger.warning("JWT does NOT contain 'is_system_admin' claim")
        
        # Check for session data that might contain system admin info
        if '__session' in payload_json:
            session_data = payload_json['__session']
            logger.info("JWT __session data:")
            pprint(session_data)
            
            if 'is_system_admin' in session_data:
                logger.info(f"JWT __session contains 'is_system_admin': {session_data['is_system_admin']}")
            else:
                logger.warning("JWT __session does NOT contain 'is_system_admin'")
        else:
            logger.info("JWT does not contain __session data")
            
        # Check for public metadata that might contain system admin info
        if 'actor' in payload_json and 'public_metadata' in payload_json['actor']:
            public_metadata = payload_json['actor']['public_metadata']
            logger.info("JWT actor public_metadata:")
            pprint(public_metadata)
            
            if 'isSystemAdmin' in public_metadata:
                logger.info(f"JWT actor public_metadata contains 'isSystemAdmin': {public_metadata['isSystemAdmin']}")
            elif 'is_system_admin' in public_metadata:
                logger.info(f"JWT actor public_metadata contains 'is_system_admin': {public_metadata['is_system_admin']}")
            else:
                logger.warning("JWT actor public_metadata does NOT contain system admin flags")
        
        return payload_json
        
    except Exception as e:
        logger.error(f"Error parsing JWT token: {str(e)}")
        return None

def check_database_system_admin():
    """Check system admin entries in the database."""
    logger.info("Checking database for system_admin table entries")
    
    # Import SQLAlchemy and models here to avoid importing outside Docker
    try:
        from sqlalchemy import create_engine, text
        
        # Get database URL
        database_url = os.environ.get("COHERENCE_DATABASE_URL")
        if not database_url:
            logger.error("COHERENCE_DATABASE_URL not set. Cannot connect to database.")
            return
        
        # For direct SQL queries convert async URL to sync if needed
        sync_db_url = database_url
        if database_url.startswith('postgresql+asyncpg://'):
            sync_db_url = database_url.replace('postgresql+asyncpg://', 'postgresql://')
        
        engine = create_engine(sync_db_url)
        
        # Query system_admin table
        with engine.connect() as conn:
            # Get system admin users
            result = conn.execute(text("SELECT id, clerk_user_id, created_at FROM system_admin"))
            rows = result.fetchall()
            
            if rows:
                logger.info(f"Found {len(rows)} system admin entries:")
                for row in rows:
                    logger.info(f"ID: {row[0]}, Clerk User ID: {row[1]}, Created: {row[2]}")
                    
                    # Check API keys for this system admin
                    api_key_result = conn.execute(
                        text("SELECT id, name, revoked, last_used_at FROM system_admin_api_key WHERE system_admin_id = :admin_id"),
                        {"admin_id": row[0]}
                    )
                    api_keys = api_key_result.fetchall()
                    
                    if api_keys:
                        logger.info(f"  Found {len(api_keys)} API keys for this system admin:")
                        for key in api_keys:
                            logger.info(f"  Key ID: {key[0]}, Name: {key[1]}, Revoked: {key[2]}, Last Used: {key[3]}")
                    else:
                        logger.warning(f"  No API keys found for system admin {row[0]}")
            else:
                logger.warning("No system admin entries found in the database")
            
            # Check environment variable match
            system_admin_clerk_user_id = os.environ.get("SYSTEM_ADMIN_CLERK_USER_ID")
            if system_admin_clerk_user_id:
                result = conn.execute(
                    text("SELECT id FROM system_admin WHERE clerk_user_id = :clerk_id"),
                    {"clerk_id": system_admin_clerk_user_id}
                )
                match = result.fetchone()
                
                if match:
                    logger.info(f"Found matching system admin for SYSTEM_ADMIN_CLERK_USER_ID: {match[0]}")
                else:
                    logger.warning(f"No system admin found for SYSTEM_ADMIN_CLERK_USER_ID: {system_admin_clerk_user_id}")
    
    except Exception as e:
        logger.error(f"Error checking database: {str(e)}")

def main():
    """Main function to run all checks."""
    logger.info("Starting system admin status debug")
    
    # Check environment variables
    check_system_admin_env_vars()
    
    # Check system admin entries in database
    check_database_system_admin()
    
    # If a JWT token is provided as argument, parse and analyze it
    if len(sys.argv) > 1:
        token = sys.argv[1]
        logger.info("JWT token provided as argument, analyzing")
        parse_jwt_token(token)
        
        # Check session info endpoint with provided token
        check_session_info_endpoint(token=token)
        
        # Check frontend endpoint with provided token
        check_me_endpoint(token=token)
    else:
        logger.info("No JWT token provided. To analyze a token, pass it as an argument.")
        logger.info("Example: python -m debug_scripts.debug_system_admin <JWT_TOKEN>")
    
    logger.info("System admin status debug completed")

if __name__ == "__main__":
    main()