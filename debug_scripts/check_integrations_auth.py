#!/usr/bin/env python3
"""
Debug script to check authentication flow for /admin/integrations page
"""

import os

import requests


def check_admin_auth_flow():
    """Check the authentication flow for the admin integrations page"""
    
    print("=== Debugging Admin Integrations Authentication ===\n")
    
    # Get the admin URL from docker-compose environment
    admin_url = os.getenv('ADMIN_URL', 'http://localhost:3003')
    api_url = os.getenv('API_URL', 'http://localhost:8001')
    
    print(f"Admin URL: {admin_url}")
    print(f"API URL: {api_url}")
    print()
    
    # 1. Check if admin health endpoint works
    print("1. Checking admin health endpoint...")
    try:
        response = requests.get(f"{admin_url}/health", timeout=5)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    print()
    
    # 2. Check API health endpoint
    print("2. Checking API health endpoint...")
    try:
        response = requests.get(f"{api_url}/health", timeout=5)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    print()
    
    # 3. Check auth endpoints
    print("3. Checking auth endpoints...")
    
    # Check /api/auth/me endpoint (admin route)
    print("3a. Checking /api/auth/me (admin route)...")
    try:
        response = requests.get(f"{admin_url}/api/auth/me", timeout=5)
        print(f"Status: {response.status_code}")
        if response.status_code == 401:
            print("Auth required (expected without token)")
        else:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    print()
    
    # Check /v1/auth/session-info endpoint (API route)
    print("3b. Checking /v1/auth/session-info (API route)...")
    try:
        response = requests.get(f"{api_url}/v1/auth/session-info", timeout=5)
        print(f"Status: {response.status_code}")
        if response.status_code == 401:
            print("Auth required (expected without token)")
        else:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    print()
    
    # 4. Check integrations endpoint
    print("4. Checking integrations endpoint...")
    try:
        response = requests.get(f"{api_url}/v1/admin/integrations", timeout=5)
        print(f"Status: {response.status_code}")
        if response.status_code == 401:
            print("Auth required (expected without token)")
        else:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    print()
    
    # 5. Check permissions service configuration
    print("5. Checking permission requirements...")
    print("The integrations page requires 'integration:read' permission")
    print("System admins have this permission automatically")
    print("Regular users need organization admin role in Clerk")
    print()
    
    # 6. Common issues and solutions
    print("6. Common issues and solutions:")
    print("- 401 Unauthorized: Missing or invalid JWT token")
    print("- No organization selected in Clerk")
    print("- JWT template 'coherence_session' not configured in Clerk")
    print("- User doesn't have required permissions")
    print()
    print("To debug further:")
    print("1. Check browser console for authentication errors")
    print("2. Check Clerk Dashboard for organization selection")
    print("3. Check JWT template configuration in Clerk")
    print("4. Check Docker logs: docker-compose logs coherence-admin coherence-api")
    print()

if __name__ == "__main__":
    check_admin_auth_flow()
