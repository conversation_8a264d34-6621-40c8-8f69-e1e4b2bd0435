/**
 * Debug Clerk <PERSON><PERSON>
 * This script helps debug issues with the Clerk JWT token and system admin status.
 * 
 * To use: Copy and paste this into the browser console while on any page of the admin site.
 */

async function debugClerkToken() {
  console.log("Starting Clerk token debugging...");
  
  // Get token from localStorage if available (not typical for Clerk)
  const localStorageToken = localStorage.getItem('clerk-session-token');
  if (localStorageToken) {
    console.log("Found token in localStorage:", localStorageToken.substring(0, 20) + "...");
  } else {
    console.log("No token found in localStorage (expected for Clerk)");
  }
  
  // Get session details from window.adminSessionData
  if (window.adminSessionData) {
    console.log("Found window.adminSessionData:", window.adminSessionData);
    console.log("System admin status:", 
      window.adminSessionData.isSystemAdmin ? "TRUE" : "FALSE");
    
    if (window.adminSessionData.clerkActor?.publicMetadata) {
      console.log("Actor public metadata:", window.adminSessionData.clerkActor.publicMetadata);
    }
    
    if (window.adminSessionData.permissions) {
      console.log("Permissions:", window.adminSessionData.permissions);
      console.log("Has system:* permission:", 
        window.adminSessionData.permissions.includes("system:*") ? "YES" : "NO");
    }
  } else {
    console.log("No window.adminSessionData found - session data not initialized");
  }
  
  // Try to get token via API
  try {
    console.log("Attempting to fetch token from /api/debug-token...");
    const response = await fetch('/api/debug-token');
    
    if (response.ok) {
      const data = await response.json();
      console.log("Debug token response:", data);
      
      if (data.token) {
        const token = data.token;
        console.log("Token (first 40 chars):", token.substring(0, 40) + "...");
        
        // Parse JWT token
        const parts = token.split('.');
        if (parts.length === 3) {
          try {
            const payload = JSON.parse(atob(parts[1]));
            console.log("JWT Payload:", payload);
            
            // Look for system admin flags in various locations
            if (payload.is_system_admin !== undefined) {
              console.log("Root is_system_admin:", payload.is_system_admin);
            }
            
            if (payload.__session) {
              console.log("Session data:", payload.__session);
              
              if (payload.__session.is_system_admin !== undefined) {
                console.log("Session is_system_admin:", payload.__session.is_system_admin);
              }
            }
            
            if (payload.actor && payload.actor.public_metadata) {
              console.log("Actor public_metadata:", payload.actor.public_metadata);
              
              // Check for camelCase and snake_case variations
              if (payload.actor.public_metadata.isSystemAdmin !== undefined) {
                console.log("public_metadata.isSystemAdmin:", payload.actor.public_metadata.isSystemAdmin);
              }
              
              if (payload.actor.public_metadata.is_system_admin !== undefined) {
                console.log("public_metadata.is_system_admin:", payload.actor.public_metadata.is_system_admin);
              }
            }
            
            // Check for permissions
            if (payload.permissions) {
              console.log("JWT permissions:", payload.permissions);
              console.log("Has system:* in JWT:", payload.permissions.includes("system:*"));
            }
          } catch (e) {
            console.error("Error parsing JWT payload:", e);
          }
        } else {
          console.error("Invalid JWT format - expected 3 parts");
        }
      } else {
        console.log("No token returned from debug endpoint");
      }
    } else {
      console.error("Error fetching token:", response.statusText);
    }
  } catch (e) {
    console.error("Error with debug token API call:", e);
  }
  
  // Try to make a fetch request to /api/auth/me to see what comes back
  try {
    console.log("Fetching /api/auth/me to check response...");
    const meResponse = await fetch('/api/auth/me');
    
    if (meResponse.ok) {
      const meData = await meResponse.json();
      console.log("/api/auth/me response:", meData);
      
      if (meData.org) {
        console.log("Organization data:", meData.org);
      }
      
      if (meData.tenant) {
        console.log("Tenant data:", meData.tenant);
      }
      
      if (meData.permissions) {
        console.log("Permissions:", meData.permissions);
        console.log("Has system:* permission:", meData.permissions.includes("system:*"));
      }
    } else {
      console.error("Error fetching /api/auth/me:", meResponse.statusText);
      try {
        const errorData = await meResponse.json();
        console.log("Error details:", errorData);
      } catch (e) {
        console.log("No JSON error response");
      }
    }
  } catch (e) {
    console.error("Error with /api/auth/me request:", e);
  }
  
  // Check session data from Clerk (if available)
  if (window.Clerk) {
    try {
      console.log("Checking Clerk session...");
      const session = await window.Clerk.session;
      if (session) {
        console.log("Current Clerk session:", session);
        
        const user = session.user;
        if (user) {
          console.log("User:", user);
          
          if (user.publicMetadata) {
            console.log("User publicMetadata:", user.publicMetadata);
            console.log("isSystemAdmin in publicMetadata:", 
              user.publicMetadata.isSystemAdmin !== undefined ? 
                user.publicMetadata.isSystemAdmin : "NOT FOUND");
          }
          
          const org = session.organization;
          if (org) {
            console.log("Organization:", org);
            console.log("Organization membership:", session.organizationMemberships);
          } else {
            console.warn("No organization in Clerk session!");
          }
        }
      } else {
        console.warn("No active Clerk session found");
      }
    } catch (e) {
      console.error("Error accessing Clerk session:", e);
    }
  } else {
    console.warn("Clerk client not available on window object");
  }
  
  console.log("Clerk token debugging complete");
}

// Execute the function
debugClerkToken();

// Provide instructions for reuse
console.log("To run this debug function again, call: debugClerkToken()");

// Add function to global scope for reuse
window.debugClerkToken = debugClerkToken;