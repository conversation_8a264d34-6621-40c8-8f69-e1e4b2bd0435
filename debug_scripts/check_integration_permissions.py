#!/usr/bin/env python3
"""
Debug script specifically for integrations permission issues
"""

import os
import subprocess
from datetime import datetime


def check_permission_flow():
    """Check the permission flow for integrations"""
    
    print("=== Integrations Permission Debug ===")
    print(f"Time: {datetime.now().isoformat()}")
    print()
    
    print("1. Permission Requirements for /admin/integrations:")
    print("   - integration:read (to view integrations)")
    print("   - integration:create (to create integrations)")
    print("   - System admins: get all permissions automatically")
    print("   - Organization admins (admin/owner): get integration permissions")
    print()
    
    print("2. Authentication Flow:")
    print("   a. User logs in via Clerk")
    print("   b. Clerk JWT contains organization info (if template configured)")
    print("   c. Frontend calls /api/auth/me with JWT")
    print("   d. /api/auth/me calls /v1/auth/session-info on backend")
    print("   e. Backend validates JWT, extracts org info, calculates permissions")
    print("   f. Frontend stores permissions and makes API calls")
    print()
    
    print("3. Common Issues:")
    print("   - No organization selected in Clerk")
    print("   - JWT template 'coherence_session' missing custom claims")
    print("   - User doesn't have admin role in organization")
    print("   - SYSTEM_ADMIN_CLERK_USER_ID not set for system admin")
    print()

def check_logs_for_permission_errors():
    """Check recent logs for permission-related errors"""
    
    print("4. Checking recent logs for permission errors...")
    
    try:
        # Check admin logs
        result = subprocess.run(
            ["docker-compose", "logs", "--tail=100", "coherence-admin"],
            capture_output=True,
            text=True,
            cwd="/Users/<USER>/Documents/projects/coherence"
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            permission_lines = [line for line in lines if 'permission' in line.lower() or 'integration' in line.lower()]
            if permission_lines:
                print("   Admin logs (permission/integration related):")
                for line in permission_lines[-10:]:  # Last 10 relevant lines
                    print(f"   {line}")
            else:
                print("   No permission-related entries in admin logs")
        
        # Check API logs
        result = subprocess.run(
            ["docker-compose", "logs", "--tail=100", "coherence-api"],
            capture_output=True,
            text=True,
            cwd="/Users/<USER>/Documents/projects/coherence"
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            permission_lines = [line for line in lines if 'permission' in line.lower() or 'integration' in line.lower()]
            if permission_lines:
                print("\n   API logs (permission/integration related):")
                for line in permission_lines[-10:]:  # Last 10 relevant lines
                    print(f"   {line}")
            else:
                print("\n   No permission-related entries in API logs")
                
    except Exception as e:
        print(f"   Error checking logs: {e}")
    
    print()

def check_jwt_template_config():
    """Check JWT template configuration"""
    
    print("5. JWT Template Configuration:")
    print("   Required in Clerk Dashboard → Sessions → JWT Templates:")
    print("   Template name: 'coherence_session'")
    print("   Custom claims:")
    print("   ```")
    print("   {")
    print("     \"__session\": {")
    print("       \"org_id\": \"{{org.id}}\",")
    print("       \"org_name\": \"{{org.name}}\",")
    print("       \"org_role\": \"{{org.role}}\",")
    print("       \"org_slug\": \"{{org.slug}}\"")
    print("     }")
    print("   }")
    print("   ```")
    print()

def check_admin_session_context():
    """Check AdminSessionContext behavior"""
    
    print("6. AdminSessionContext Debug Points:")
    print("   File: /coherence-admin/src/context/AdminSessionContext.tsx")
    print("   Key points to check:")
    print("   - Token is being fetched with template: 'coherence_session'")
    print("   - Fallback to default token if template fails")
    print("   - Authorization header is set correctly")
    print("   - Error handling for 401/403/404 responses")
    print("   - Permissions are extracted from /api/auth/me response")
    print()

def check_permission_service():
    """Check permission service logic"""
    
    print("7. Permission Service Logic:")
    print("   File: /src/coherence/services/permission_service.py")
    print("   Key mappings:")
    print("   - 'admin' role → includes 'integration:read' and 'integration:create'")
    print("   - 'owner' role → same permissions as admin")
    print("   - System admin → gets all permissions including 'system:*'")
    print("   - Simplified roles: 'admin' or 'member' (from Clerk's role)")
    print()

def provide_debugging_commands():
    """Provide specific debugging commands"""
    
    print("8. Debugging Commands:")
    print()
    
    print("   Check service status:")
    print("   docker-compose ps")
    print()
    
    print("   Check auth-related logs:")
    print("   docker-compose logs coherence-admin | grep -i -E '(auth|permission|integration)'")
    print("   docker-compose logs coherence-api | grep -i -E '(auth|permission|integration)'")
    print()
    
    print("   Test auth endpoints manually:")
    print("   curl -v http://localhost:3003/api/auth/me")
    print("   curl -v http://localhost:8001/v1/auth/session-info")
    print("   curl -v http://localhost:8001/v1/admin/integrations")
    print()
    
    print("   Check browser console:")
    print("   1. Open http://localhost:3003/admin/integrations")
    print("   2. Open Developer Tools → Console")
    print("   3. Look for admin session data and error messages")
    print()
    
    print("   Visit debug endpoint:")
    print("   http://localhost:3003/debug/auth")
    print()

def check_environment_for_permissions():
    """Check environment variables affecting permissions"""
    
    print("9. Environment Variables for Permissions:")
    print()
    
    env_file = "/Users/<USER>/Documents/projects/coherence/.env"
    
    if os.path.exists(env_file):
        print("   Checking .env file for permission-related variables:")
        with open(env_file, 'r') as f:
            lines = f.readlines()
            
            # Check for system admin user ID
            for line in lines:
                if line.strip().startswith('SYSTEM_ADMIN_CLERK_USER_ID='):
                    print(f"   ✓ {line.strip()}")
                    break
            else:
                print("   ⚠ SYSTEM_ADMIN_CLERK_USER_ID not set (needed for system admin access)")
            
            # Check Clerk keys
            for line in lines:
                if line.strip().startswith('CLERK_SECRET_KEY='):
                    print("   ✓ CLERK_SECRET_KEY is set")
                    break
            else:
                print("   ✗ CLERK_SECRET_KEY not set")
                
            for line in lines:
                if line.strip().startswith('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY='):
                    print("   ✓ NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY is set")
                    break
            else:
                print("   ✗ NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY not set")
    else:
        print(f"   Error: {env_file} not found")
    
    print()

def main():
    check_permission_flow()
    check_logs_for_permission_errors()
    check_jwt_template_config()
    check_admin_session_context()
    check_permission_service()
    check_environment_for_permissions()
    provide_debugging_commands()
    
    print("=== Debug Complete ===")
    print()
    print("Next Steps:")
    print("1. Visit http://localhost:3003/debug/auth to check current auth state")
    print("2. Check Clerk Dashboard for organization and JWT template setup")
    print("3. Run: docker-compose logs coherence-admin | tail -50")
    print("4. Run: docker-compose logs coherence-api | tail -50")
    print("5. Check browser console when accessing /admin/integrations")

if __name__ == "__main__":
    main()
