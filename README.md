# Coherence

Coherence is a stand-alone, language-agnostic middleware that transforms natural conversation into deterministic actions through an intelligent, multi-tiered pipeline.

## Overview

Coherence enables:
- **Lightning-fast** responses: Vector matches in <100ms, LLM fallbacks in <500ms
- **Conversational** parameter completion with Star Trek computer-like interaction
- **Multi-tenant** architecture with complete data isolation via Row-Level Security
- **Industry Packs** for domain-specific functionality
- **Pluggable Actions** from synchronous HTTP calls to complex workflows
- **OpenAPI Auto-Integration** for seamless connectivity to external services
- **Fault Tolerance** with circuit breakers and retry mechanisms
- **Comprehensive Monitoring** via Prometheus and Grafana dashboards

## Quick Start

1. Clone the repository
2. Start the services with `docker-compose up -d`
3. Access metrics at `http://localhost:8002/metrics`
4. View dashboards at `http://localhost:3001` (admin/admin)

## Architecture

Coherence uses a three-tier architecture for intent recognition:

1. **Tier 1: Vector Matching (100ms)** - Fast, accurate matches for common intents
2. **Tier 2: Local LLM Router (300ms)** - Handles more complex intent recognition
3. **Tier 3: RAG Augmentation (500ms)** - Processes novel or ambiguous requests

## Getting Started

### Prerequisites

- Python 3.10+
- Docker and Docker Compose
- OpenAI API key (or other supported LLM provider)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/coherence.git
   cd coherence
   ```

2. Create a .env file with your configuration:
   ```
   COHERENCE_OPENAI_API_KEY=your_openai_api_key
   ```

3. Start the containers:
   ```
   docker-compose up -d
   ```

4. Run database migrations:
   ```
   docker-compose exec coherence-api alembic upgrade head
   ```

5. Create a tenant and API key:
   ```
   docker-compose exec coherence-api python -m scripts.create_tenant --name "Default" --industry "General"
   ```

6. Create a tenant admin key (for administrative access):
   ```
   docker-compose exec coherence-api python -m scripts.create_admin_key <tenant_id> "Admin Key"
   ```

### Quick Start

Send your first request:

```bash
curl -X POST http://localhost:8001/v1/resolve \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "user_id": "00000000-0000-0000-0000-000000000000",
    "role": "user",
    "message": "Hello world"
  }'
```

### Testing the Intent Pipeline

To interactively test the intent resolution pipeline:

```bash
# With Docker
docker-compose exec coherence-api python -m scripts.run_intent_test

# Without Docker
export OPENAI_API_KEY=your_openai_api_key
python -m scripts.run_intent_test
```

## Development

### Project Structure

```
coherence/
├── src/
│   └── coherence/
│       ├── api/            # API endpoints
│       ├── core/           # Core functionality
│       ├── db/             # Database utilities
│       ├── intent_pipeline/ # Intent resolution
│       ├── models/         # Database models
│       ├── openapi_adapter/ # OpenAPI integration
│       ├── schemas/        # API schemas
│       ├── services/       # Business logic services
│       ├── template_system/ # Template management
│       └── utils/          # Utility functions
├── tests/                  # Test suite
├── alembic/                # Database migrations
└── docker-compose.yml      # Deployment configuration
```

### Running Tests

```bash
# Run all tests
docker-compose exec coherence-api pytest

# Run specific test categories
docker-compose exec coherence-api pytest tests/unit/
docker-compose exec coherence-api pytest tests/integration/
docker-compose exec coherence-api pytest tests/functional/

# Run a specific test
docker-compose exec coherence-api pytest tests/unit/test_intent_pipeline.py -v
```

For more details about testing, see [tests/README.md](tests/README.md).

## Project Status

Coherence is currently in a production-ready state with all planned features implemented:

- ✅ Core Intent Pipeline with tiered recognition approach
- ✅ Multi-tenant architecture with Row-Level Security
- ✅ Template system with hierarchical inheritance
- ✅ OpenAPI integration with OAuth support
- ✅ Error handling framework with circuit breakers and retries
- ✅ Monitoring and observability tooling
- ✅ Real-world testing in Docker environment
- ✅ Production-ready SDK with Python and TypeScript support

For the latest status updates, see [Project Status Update](/docs/progress/20250507_1900_project_status_update.md) and [SDK Production Readiness](/docs/progress/20250507_2000_sdk_production_readiness.md).

## Documentation

- [Getting Started Guide](/docs/guides/getting_started.md)
- [System Architecture](/docs/architecture/system_architecture.md)
- [Code Quality Guidelines](/docs/guides/code_quality.md)
- [PostgreSQL Session Variables Guide](/docs/guides/postgresql_session_variables.md)
- [Error Handling Guide](/docs/guides/error_handling.md)
- [Full Implementation Plan](/docs/implementation_plans/implementation_plan_with_existing_code.md)

### API Testing Resources

- [API Testing Guide](/docs/API_TESTING.md) - Quick reference for testing the API
- [Detailed Testing Guide](/docs/api_testing_guide.md) - Comprehensive curl commands and examples
- [Test Automation Script](/scripts/test_api_flow.sh) - Script for automated API testing
- [Postman Collection](/docs/postman_collection.json) - Importable Postman collection
- [OpenAPI Summary](/docs/openapi-summary.yaml) - Simplified OpenAPI specification

### SDK

- [Python SDK](/sdk/) - Production-ready Python client package
- [TypeScript SDK](/sdk/typescript/) - Generated TypeScript client (npm package)
- [SDK Release Notes](/docs/SDK_RELEASE_NOTES.md) - Latest SDK release information
- [SDK Development Guide](/docs/SDK_DEVELOPMENT.md) - Guide for building client SDKs
- OpenAPI specification available at `/openapi.json` when the server is running

## License

This project is licensed under the MIT License - see the LICENSE file for details.