version: '3.8'

services:
  coherence-api:
    # Container applies fixes for:
    # 1. Template trigger issue (fixing content vs body field mismatch)
    # 2. UUID validation in audit logging and tenant context
    # These fixes are applied during container startup
    container_name: coherence-api
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8001:8000"  # Changed from 8000:8000
      - "8002:8001"  # Changed metrics port from 8001:8001
    volumes:
      - ./src:/app/src
      - ./alembic:/app/alembic
      - ./scripts:/app/scripts
      - ./tests:/app/tests
      - ./README.md:/app/README.md
      - ./monitoring:/app/monitoring
    environment:
      - PYTHONPATH=/app
      - COHERENCE_POSTGRES_SERVER=coherence-db
      - COHERENCE_POSTGRES_USER=postgres
      - COHERENCE_POSTGRES_PASSWORD=postgres
      - COHERENCE_POSTGRES_DB=coherence
      - COHERENCE_REDIS_HOST=coherence-cache
      - COHERENCE_QDRANT_HOST=coherence-vectors
      - COHERENCE_QDRANT_PORT=6333
      - COHERENCE_BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:3002","http://localhost:3003"]
      - COHERENCE_OPENAI_API_KEY=${OPENAI_API_KEY}
      - COHERENCE_SYSTEM_ADMIN_API_KEY=coh_test_sys_admin_key_123
      - LOG_LEVEL=DEBUG  # Setting the log level to DEBUG to capture all log messages
      - APPLY_FIXES=true  # Flag to apply fixes during startup
      - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YXNzdXJpbmctbW9jY2FzaW4tNjYuY2xlcmsuYWNjb3VudHMuZGV2JA
      - CLERK_SECRET_KEY=sk_test_1cUNtPZCsK6NxmEKoLPZFMwFZfWIsCm3qXkKxwYfA7
      - SYSTEM_ADMIN_CLERK_USER_ID=user_2wx2TX71qggQp9ogWOzFhR1Lu6n
      - COHERENCE_POSTGRES_PORT=5432 # Add this line to specify the internal DB port
    depends_on:
      coherence-db:
        condition: service_healthy
      coherence-cache:
        condition: service_healthy
    networks:
      - coherence-network
    # Add a startup script to apply fixes before starting the API server
    command: >
      bash -c "
        # Wait for all services to be fully ready
        echo 'Waiting for all services to be fully ready...'
        sleep 10
        
        # Skipping Qdrant readiness check
        
        # Apply system fixes
        if [ \"$$APPLY_FIXES\" = 'true' ]; then
          echo 'Applying system fixes...'
          python -m scripts.apply_fixes
          echo 'Fixes applied successfully!'
        fi
        
        # Skipping database readiness check
        
        echo 'Starting API server...'
        uvicorn src.coherence.main:app --host 0.0.0.0 --port 8000 --reload
      "

  coherence-db:
    container_name: coherence-db
    image: postgres:16
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=coherence
    ports:
      - "5433:5432"  # Changed from 5432:5432
    networks:
      - coherence-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  coherence-cache:
    container_name: coherence-cache
    image: redis:7-alpine
    ports:
      - "6380:6379"  # Changed from 6379:6379
    volumes:
      - redis-data:/data
    networks:
      - coherence-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  coherence-vectors:
    container_name: coherence-vectors
    # Pin to Qdrant v1.6.1 to use stable collections endpoint
    image: qdrant/qdrant:v1.6.1
    ports:
      - "6335:6333"  # Changed from 6333:6333
      - "6336:6334"  # Changed from 6334:6334
    volumes:
      - qdrant-data:/qdrant/storage
    networks:
      - coherence-network
    environment:
      - QDRANT_ALLOW_ANONYMOUS_TELEMETRY=false

  coherence-metrics:
    container_name: coherence-metrics
    image: prom/prometheus:v2.46.0
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - "9091:9090"  # Changed from 9090:9090
    networks:
      - coherence-network

  coherence-dashboard:
    container_name: coherence-dashboard
    image: grafana/grafana:10.2.0
    ports:
      - "3001:3000"  # Changed from 3000:3000
    volumes:
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - grafana-data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - coherence-network
    depends_on:
      - coherence-metrics

  coherence-admin:
    container_name: coherence-admin
    build:
      context: ./coherence-admin
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YXNzdXJpbmctbW9jY2FzaW4tNjYuY2xlcmsuYWNjb3VudHMuZGV2JA
        - NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
        - NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
        - NEXT_PUBLIC_CLERK_FALLBACK_REDIRECT_URL=/
        - NEXT_PUBLIC_API_URL=http://localhost:8001/v1
        - NEXT_PUBLIC_COHERENCE_API_URL=http://localhost:8001/v1
        - NEXT_PUBLIC_APP_URL=http://localhost:3003
        - NEXT_PUBLIC_CLERK_FRONTEND_API=https://assuring-moccasin-66.clerk.accounts.dev
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YXNzdXJpbmctbW9jY2FzaW4tNjYuY2xlcmsuYWNjb3VudHMuZGV2JA
      - CLERK_SECRET_KEY=sk_test_1cUNtPZCsK6NxmEKoLPZFMwFZfWIsCm3qXkKxwYfA7
      - NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
      - NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
      - NEXT_PUBLIC_CLERK_FALLBACK_REDIRECT_URL=/
      - NEXT_PUBLIC_API_URL=http://localhost:8001/v1
      - API_URL=http://coherence-api:8000/v1
      - COHERENCE_MASTER_API_KEY=${COHERENCE_MASTER_API_KEY}
      - NEXT_PUBLIC_COHERENCE_API_URL=http://localhost:8001/v1
      - NEXT_PUBLIC_CLERK_FRONTEND_API=https://assuring-moccasin-66.clerk.accounts.dev
      - NEXT_PUBLIC_APP_URL=http://localhost:3003
      - CLERK_SESSION_TOKEN_TEMPLATE=coherence_session
    networks:
      - coherence-network
    depends_on:
      - coherence-api
    restart: unless-stopped

networks:
  coherence-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
  qdrant-data:
  prometheus-data:
  grafana-data: