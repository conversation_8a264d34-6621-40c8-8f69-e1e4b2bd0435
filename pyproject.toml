[tool.poetry]
name = "coherence"
version = "0.1.0"
description = "Coherence: AI middleware for converting natural language to actions"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "coherence", from = "src"}]

[tool.poetry.dependencies]
python = ">=3.10,<3.12"
fastapi = "^0.103.1"
uvicorn = {extras = ["standard"], version = "^0.23.2"}
starlette = "0.27.0"  # Add compatible Starlette version
pydantic = "^2.3.0"
pydantic-settings = "^2.0.3"
sqlalchemy = {extras = ["asyncio"], version = "^2.0.20"}
asyncpg = "^0.28.0"
alembic = "^1.12.0"
psycopg2-binary = "^2.9.9"  # Added for PostgreSQL connection
redis = {extras = ["hiredis"], version = "^5.0.0"}
structlog = "^23.1.0"
prometheus-client = "^0.17.1"
prometheus-fastapi-instrumentator = "*"  # Add the instrumentator
jinja2 = "^3.1.2"
qdrant-client = "^1.6.4"
openai = "1.77.0"
boto3 = "^1.34.0"  # AWS SDK for KMS
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
python-multipart = "^0.0.6"
email-validator = "^2.0.0"
tenacity = "^8.2.3"
backoff = "^2.2.1"
pyyaml = "^6.0.1"
tiktoken = "^0.5.1"
aiohttp = "^3.9"
clerk-backend-api = "^2.1.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.2"
pytest-asyncio = "^0.21.1"
httpx = "^0.28.1"
black = "^23.9.1"
isort = "^5.12.0"
mypy = "^1.5.1"
types-pyyaml = "^*********"
types-redis = "^*******"
pytest-cov = "^4.1.0"
ruff = "^0.0.290"
pytest-mock = "^3.14.0"
faker = "^22.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ["py310"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3

[tool.mypy]
python_version = "3.10"
warn_redundant_casts = true
warn_unused_ignores = true
disallow_any_generics = true
check_untyped_defs = true
disallow_untyped_defs = true

[[tool.mypy.overrides]]
module = [
    "qdrant_client.*",
    "passlib.*",
    "jose.*",
    "tenacity.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
python_classes = "Test*"
python_paths = ["."]

[tool.ruff]
line-length = 88
target-version = "py310"
select = ["E", "F", "B", "I"]
ignore = ["E203", "E501"]

[tool.ruff.per-file-ignores]
"src/coherence/api/**/*.py" = ["B008"]