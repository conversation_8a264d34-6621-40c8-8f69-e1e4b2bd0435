"""
Tests for API routes
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
import json

from src.api.app import create_app
from src.api.dependencies import get_kg_client, get_auth_context


class TestHealthRoutes:
    """Test health check endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        from fastapi import FastAPI

        # Create a simple test app without lifespan for testing
        app = FastAPI(title="Test App")

        # Add basic health route
        @app.get("/health")
        async def health():
            return {
                "status": "healthy",
                "service": "coherence-kg-api",
                "version": "1.0.0"
            }

        @app.get("/health/live")
        async def liveness():
            return {
                "status": "alive",
                "service": "coherence-kg-api"
            }

        @app.get("/health/ready")
        async def readiness():
            return {
                "status": "ready",
                "database": "connected"
            }

        return TestClient(app)
    
    def test_basic_health_check(self, client):
        """Test basic health endpoint"""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "coherence-kg-api"
        assert "version" in data
    
    def test_liveness_check(self, client):
        """Test liveness endpoint"""
        response = client.get("/health/live")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "alive"
        assert data["service"] == "coherence-kg-api"
    
    @patch('src.api.app.kg_client')
    def test_readiness_check_success(self, mock_kg_client, client):
        """Test readiness when database is connected"""
        # Mock successful query
        mock_kg_client.query = AsyncMock(return_value=Mock(result_set=[[1]]))
        
        response = client.get("/health/ready")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ready"
        assert data["database"] == "connected"
    
    @patch('src.api.app.kg_client')
    def test_readiness_check_failure(self, mock_kg_client, client):
        """Test readiness when database is disconnected"""
        # Mock failed query
        mock_kg_client.query = AsyncMock(side_effect=Exception("Connection failed"))
        
        response = client.get("/health/ready")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "not_ready"
        assert data["database"] == "disconnected"
        assert "error" in data


class TestNodeRoutes:
    """Test node-related endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client with mocked dependencies"""
        app = create_app()
        
        # Mock KG client
        mock_kg = AsyncMock()
        mock_kg.create_node.return_value = {"id": "123", "name": "Test"}
        mock_kg.get_node_by_id.return_value = {"id": "123", "name": "Test", "tenant_id": "tenant-123"}
        mock_kg.update_node.return_value = True
        mock_kg.delete_node.return_value = True
        
        # Mock auth context
        mock_auth = {"tenant_id": "tenant-123", "user_id": "user-123"}
        
        # Override dependencies
        app.dependency_overrides[get_kg_client] = lambda: mock_kg
        app.dependency_overrides[get_auth_context] = lambda: mock_auth
        
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        """Auth headers for requests"""
        return {"Authorization": "Bearer test-token"}
    
    def test_create_node_success(self, client, auth_headers):
        """Test successful node creation"""
        node_data = {
            "label": "User",
            "properties": {
                "name": "Test User",
                "email": "<EMAIL>"
            }
        }
        
        response = client.post("/api/v1/nodes/", json=node_data, headers=auth_headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["status"] == "created"
        assert data["node"]["id"] == "123"
    
    def test_create_node_without_auth(self, client):
        """Test node creation without authentication"""
        app = create_app()
        test_client = TestClient(app)  # Client without auth override
        
        response = test_client.post("/api/v1/nodes/", json={"label": "User"})
        
        assert response.status_code == 401
    
    def test_get_node_success(self, client, auth_headers):
        """Test getting node by ID"""
        response = client.get("/api/v1/nodes/123", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "123"
        assert data["name"] == "Test"
    
    def test_get_node_not_found(self, client, auth_headers):
        """Test getting non-existent node"""
        # Override mock to return None
        app = client.app
        mock_kg = AsyncMock()
        mock_kg.get_node_by_id.return_value = None
        app.dependency_overrides[get_kg_client] = lambda: mock_kg
        
        response = client.get("/api/v1/nodes/nonexistent", headers=auth_headers)
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_get_node_access_denied(self, client, auth_headers):
        """Test access denied for node from different tenant"""
        # Override mock to return node from different tenant
        app = client.app
        mock_kg = AsyncMock()
        mock_kg.get_node_by_id.return_value = {"id": "123", "tenant_id": "other-tenant"}
        app.dependency_overrides[get_kg_client] = lambda: mock_kg
        
        response = client.get("/api/v1/nodes/123", headers=auth_headers)
        
        assert response.status_code == 403
        assert "Access denied" in response.json()["detail"]
    
    def test_update_node_success(self, client, auth_headers):
        """Test updating node"""
        update_data = {
            "properties": {
                "name": "Updated Name",
                "status": "active"
            }
        }
        
        response = client.put("/api/v1/nodes/123", json=update_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "updated"
        assert data["node_id"] == "123"
    
    def test_delete_node_success(self, client, auth_headers):
        """Test deleting node"""
        response = client.delete("/api/v1/nodes/123?label=User", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "deleted"
        assert data["node_id"] == "123"
    
    def test_search_nodes(self, client, auth_headers):
        """Test searching nodes"""
        search_data = {
            "label": "User",
            "filters": {"status": "active"},
            "include_global": False
        }
        
        # Mock search results
        app = client.app
        mock_kg = app.dependency_overrides[get_kg_client]()
        mock_service = Mock()
        mock_service.find_nodes = AsyncMock(return_value=[
            {"id": "1", "name": "User 1"},
            {"id": "2", "name": "User 2"}
        ])
        
        with patch('src.api.routes.nodes.NodeService', return_value=mock_service):
            response = client.post("/api/v1/nodes/search", json=search_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["count"] == 2
        assert len(data["nodes"]) == 2


class TestRelationshipRoutes:
    """Test relationship endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client with mocked dependencies"""
        app = create_app()
        
        # Mock KG client
        mock_kg = AsyncMock()
        mock_kg.create_relationship.return_value = True
        mock_kg.get_node_by_id.return_value = {"id": "123", "tenant_id": "tenant-123"}
        mock_kg.query.return_value = Mock(result_set=[])
        
        # Mock auth
        mock_auth = {"tenant_id": "tenant-123", "user_id": "user-123"}
        
        app.dependency_overrides[get_kg_client] = lambda: mock_kg
        app.dependency_overrides[get_auth_context] = lambda: mock_auth
        
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        """Auth headers"""
        return {"Authorization": "Bearer test-token"}
    
    def test_create_relationship_success(self, client, auth_headers):
        """Test creating relationship"""
        rel_data = {
            "from_label": "User",
            "from_id": "user-123",
            "to_label": "Post",
            "to_id": "post-456",
            "rel_type": "CREATED",
            "properties": {"timestamp": "2024-01-01T12:00:00"}
        }
        
        response = client.post("/api/v1/relationships/", json=rel_data, headers=auth_headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["status"] == "created"
        assert data["relationship"]["from_id"] == "user-123"
        assert data["relationship"]["to_id"] == "post-456"
    
    def test_create_relationship_access_denied(self, client, auth_headers):
        """Test access denied when creating relationship to other tenant's node"""
        # Mock different tenant for target node
        app = client.app
        mock_kg = app.dependency_overrides[get_kg_client]()
        mock_kg.get_node_by_id.side_effect = [
            {"id": "user-123", "tenant_id": "tenant-123"},
            {"id": "post-456", "tenant_id": "other-tenant"}
        ]
        
        rel_data = {
            "from_label": "User",
            "from_id": "user-123",
            "to_label": "Post",
            "to_id": "post-456",
            "rel_type": "CREATED"
        }
        
        response = client.post("/api/v1/relationships/", json=rel_data, headers=auth_headers)
        
        assert response.status_code == 403
        assert "Access denied" in response.json()["detail"]
    
    def test_query_relationships(self, client, auth_headers):
        """Test querying relationships"""
        query_data = {
            "from_id": "user-123",
            "rel_type": "CREATED",
            "include_properties": True
        }
        
        # Mock query results
        app = client.app
        mock_kg = app.dependency_overrides[get_kg_client]()
        mock_kg.query.return_value = Mock(result_set=[
            [
                Mock(properties={"id": "user-123", "name": "User"}),
                Mock(properties={"created_at": "2024-01-01"}),
                Mock(properties={"id": "post-456", "title": "Post"})
            ]
        ])
        mock_kg._parse_node.side_effect = lambda x: dict(x.properties)
        
        response = client.post("/api/v1/relationships/query", json=query_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["count"] == 1
    
    def test_delete_relationship(self, client, auth_headers):
        """Test deleting relationship"""
        # Mock successful deletion
        app = client.app
        mock_kg = app.dependency_overrides[get_kg_client]()
        mock_kg.query.return_value = Mock(result_set=[[1]])  # 1 deleted
        
        response = client.delete(
            "/api/v1/relationships/?from_id=user-123&to_id=post-456",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "deleted"
        assert data["count"] == 1


class TestStatsRoutes:
    """Test statistics endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        app = create_app()
        
        # Mock KG client
        mock_kg = AsyncMock()
        mock_kg.query.return_value = Mock(result_set=[])
        
        # Mock auth
        mock_auth = {"tenant_id": "tenant-123", "user_id": "user-123"}
        
        app.dependency_overrides[get_kg_client] = lambda: mock_kg
        app.dependency_overrides[get_auth_context] = lambda: mock_auth
        
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        """Auth headers"""
        return {"Authorization": "Bearer test-token"}
    
    def test_get_statistics(self, client, auth_headers):
        """Test getting graph statistics"""
        # Mock statistics query results
        app = client.app
        mock_kg = app.dependency_overrides[get_kg_client]()
        mock_kg.query.side_effect = [
            Mock(result_set=[[["User"], 10], [["Post"], 20]]),  # Node counts
            Mock(result_set=[[15]])  # Relationship count
        ]
        
        response = client.get("/api/v1/stats/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["tenant_id"] == "tenant-123"
        assert data["statistics"]["User"] == 10
        assert data["statistics"]["Post"] == 20
        assert data["total_nodes"] == 30
        assert data["total_relationships"] == 15
    
    def test_get_node_types(self, client, auth_headers):
        """Test getting available node types"""
        # Mock node types query
        app = client.app
        mock_kg = app.dependency_overrides[get_kg_client]()
        mock_kg.query.return_value = Mock(result_set=[
            ["User"],
            ["Post"],
            ["Comment"]
        ])
        
        response = client.get("/api/v1/stats/node-types", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["count"] == 3
        assert "User" in data["node_types"]
        assert "Post" in data["node_types"]
    
    def test_get_relationship_types(self, client, auth_headers):
        """Test getting available relationship types"""
        # Mock relationship types query
        app = client.app
        mock_kg = app.dependency_overrides[get_kg_client]()
        mock_kg.query.return_value = Mock(result_set=[
            ["CREATED"],
            ["LIKES"],
            ["FOLLOWS"]
        ])
        
        response = client.get("/api/v1/stats/relationship-types", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["count"] == 3
        assert "CREATED" in data["relationship_types"]


class TestErrorHandling:
    """Test API error handling"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        app = create_app()
        return TestClient(app)
    
    def test_demo_exception_handler(self, client):
        """Test demo exception handler for unexpected errors"""
        # Create an endpoint that raises an exception
        app = client.app
        
        @app.get("/test-error")
        async def raise_error():
            raise RuntimeError("Unexpected error")
        
        response = client.get("/test-error")
        
        assert response.status_code == 500
        data = response.json()
        assert data["error"] == "Demo system error"
        assert data["message"] == "Please try again"
    
    def test_validation_error(self, client):
        """Test request validation error"""
        # Send invalid data
        invalid_data = {
            "label": "",  # Empty label should fail validation
            "properties": "not-a-dict"  # Should be dict
        }
        
        response = client.post("/api/v1/nodes/", json=invalid_data)
        
        assert response.status_code == 422  # Unprocessable Entity


class TestAuthenticationIntegration:
    """Test authentication integration with routes"""
    
    @pytest.fixture
    def client(self):
        """Create test client without auth override"""
        app = create_app()
        return TestClient(app)
    
    def test_missing_auth_header(self, client):
        """Test request without authentication"""
        response = client.get("/api/v1/nodes/123")
        
        assert response.status_code == 401
        assert "authentication" in response.json()["detail"].lower()
    
    def test_invalid_auth_header_format(self, client):
        """Test request with invalid auth header format"""
        headers = {"Authorization": "InvalidFormat token"}
        response = client.get("/api/v1/nodes/123", headers=headers)
        
        assert response.status_code == 401
    
    @patch('src.api.app.auth_middleware')
    def test_system_admin_access(self, mock_auth_middleware, client):
        """Test system admin has full access"""
        # Mock system admin auth
        mock_auth_middleware.authenticate_request.return_value = {
            "is_system_admin": True,
            "authenticated_via": "system_admin_key"
        }
        mock_auth_middleware.extract_tenant_id.return_value = None
        
        headers = {"X-API-Key": "system-admin-key"}
        response = client.get("/api/v1/stats/", headers=headers)
        
        # System admin should have access without tenant restrictions
        assert response.status_code == 200


if __name__ == "__main__":
    pytest.main([__file__, "-v"])