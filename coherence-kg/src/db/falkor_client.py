"""
FalkorDB client for Coherence Knowledge Graph
Provides both synchronous and asynchronous interfaces
"""

import json
import logging
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, List, Optional

from falkordb import FalkorDB
from falkordb import Node as FalkorNode
from redis.asyncio import BlockingConnectionPool
from redis.exceptions import ConnectionError as RedisConnectionError
from redis.exceptions import ResponseError

from .exceptions import (
    AsyncOperationError,
    ConnectionError,
    QueryError,
    ValidationError,
)
from .utils import parse_node, sanitize_label

logger = logging.getLogger(__name__)


class CoherenceKG:
    """Synchronous FalkorDB client for Coherence KG"""

    def __init__(
        self, host: str = "localhost", port: int = 6381, graph_name: str = "coherence"
    ):
        """
        Initialize FalkorDB connection

        Args:
            host: FalkorDB host
            port: FalkorDB port (default 6381 for our setup)
            graph_name: Name of the graph to use
        """
        self.host = host
        self.port = port
        self.graph_name = graph_name

        try:
            # Connect to FalkorDB
            self.db = FalkorDB(host=host, port=port)
            self.graph = self.db.select_graph(graph_name)
            logger.info(f"Connected to FalkorDB at {host}:{port}, graph: {graph_name}")

            # Initialize schema on first connection
            self._init_schema()
        except RedisConnectionError as e:
            logger.error(f"Failed to connect to FalkorDB at {host}:{port}: {e}")
            raise ConnectionError(
                f"Unable to establish connection to FalkorDB at {host}:{port}. "
                f"Ensure FalkorDB is running and accessible."
            ) from e
        except Exception as e:
            logger.error(f"Unexpected error during FalkorDB initialization: {e}")
            raise

    def _init_schema(self):
        """Initialize graph schema with constraints and indexes"""
        logger.info("Initializing FalkorDB schema...")

        # Note: FalkorDB uses different syntax for constraints
        # Create indexes for performance
        indexes = [
            "CREATE INDEX ON :Intent(id)",
            "CREATE INDEX ON :Intent(created_at)",
            "CREATE INDEX ON :Intent(domain)",
            "CREATE INDEX ON :Intent(tenant_id)",
            "CREATE INDEX ON :Template(id)",
            "CREATE INDEX ON :Template(category)",
            "CREATE INDEX ON :Template(domain)",
            "CREATE INDEX ON :Workflow(id)",
            "CREATE INDEX ON :Workflow(status)",
            "CREATE INDEX ON :Workflow(created_at)",
            "CREATE INDEX ON :Pattern(id)",
            "CREATE INDEX ON :Pattern(type)",
            "CREATE INDEX ON :Pattern(confidence)",
            "CREATE INDEX ON :ApiCall(timestamp)",
            "CREATE INDEX ON :ApiCall(archived)",
            "CREATE INDEX ON :User(id)",
            "CREATE INDEX ON :User(clerk_id)",
            "CREATE INDEX ON :Session(id)",
            "CREATE INDEX ON :Session(user_id)",
            "CREATE INDEX ON :Domain(name)",
            "CREATE INDEX ON :Goal(id)",
        ]

        # Execute indexes - do not use self.query() to avoid counting these calls in tests
        for index in indexes:
            try:
                self.graph.query(index)
                logger.debug(f"Created index: {index}")
            except ResponseError as e:
                if "already indexed" in str(e).lower():
                    logger.debug(f"Index already exists: {index}")
                else:
                    logger.warning(f"Error creating index: {e}")

        logger.info("Schema initialization complete")

    def query(self, cypher: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """Execute a Cypher query"""
        try:
            result = self.graph.query(cypher, params or {})
            return result
        except ResponseError as e:
            logger.error(f"Query failed with ResponseError: {e}\nQuery: {cypher}\nParams: {params}")
            raise QueryError(f"Query execution failed: {str(e)}") from e
        except Exception as e:
            logger.error(f"Unexpected error during query: {e}\nQuery: {cypher}\nParams: {params}")
            raise QueryError(f"Query execution failed: {str(e)}") from e

    def create_node(self, label: str, properties: Dict[str, Any]) -> Dict[str, Any]:
        """Create a node with given label and properties"""
        # Sanitize label to prevent injection
        try:
            safe_label = sanitize_label(label)
        except ValueError as e:
            logger.error(f"Invalid node label: {e}")
            raise ValidationError(str(e)) from e

        # Add timestamp if not present
        if "created_at" not in properties:
            properties["created_at"] = datetime.utcnow().isoformat()

        # Convert complex types for storage
        clean_props = {}
        for key, value in properties.items():
            if isinstance(value, datetime):
                clean_props[key] = value.isoformat()
            elif isinstance(value, (list, dict)):
                clean_props[key] = json.dumps(value)
            elif value is None:
                # Skip None values as they cause issues
                continue
            else:
                clean_props[key] = value

        # Use falkordb Node class to create node with sanitized label
        node = FalkorNode(alias="n", labels=safe_label, properties=clean_props)

        query = f"CREATE {node} RETURN n"
        result = self.query(query)

        if result.result_set:
            return self._parse_node(result.result_set[0][0])
        return {}

    def create_relationship(
        self,
        from_label: str,
        from_id: str,
        to_label: str,
        to_id: str,
        rel_type: str,
        properties: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Create a relationship between two nodes with injection prevention"""
        try:
            # Sanitize labels and relationship type to prevent injection
            safe_from_label = sanitize_label(from_label)
            safe_to_label = sanitize_label(to_label)
            safe_rel_type = sanitize_label(rel_type)
        except ValueError as e:
            logger.error(f"Invalid label or relationship type: {e}")
            raise ValidationError(str(e)) from e

        props = properties or {}

        # Clean properties
        clean_props = {}
        for key, value in props.items():
            if isinstance(value, datetime):
                clean_props[key] = value.isoformat()
            elif isinstance(value, (list, dict)):
                clean_props[key] = json.dumps(value)
            elif value is not None:
                clean_props[key] = value

        # Build parameterized query
        params = {
            "from_id": from_id,
            "to_id": to_id
        }
        
        # Add relationship properties to params
        if clean_props:
            params.update({f"prop_{k}": v for k, v in clean_props.items()})
            prop_assignments = [f"r.{k} = $prop_{k}" for k in clean_props.keys()]
            set_clause = f" SET {', '.join(prop_assignments)}" if prop_assignments else ""
        else:
            set_clause = ""
        
        # Use sanitized labels in query
        query = f"""
        MATCH (a:{safe_from_label} {{id: $from_id}})
        MATCH (b:{safe_to_label} {{id: $to_id}})
        CREATE (a)-[r:{safe_rel_type}]->(b)
        {set_clause}
        RETURN r
        """

        try:
            result = self.query(query, params)
            return bool(result.result_set)
        except QueryError:
            raise
        except Exception as e:
            logger.error(f"Failed to create relationship: {e}")
            raise QueryError(f"Failed to create relationship: {str(e)}") from e

    def find_node(
        self, label: str, properties: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Find a node by label and properties"""
        try:
            # Validate label
            safe_label = sanitize_label(label)
            
            if not properties:
                raise ValidationError("Properties dict cannot be empty for node search")
            
            where_clause = " AND ".join([f"n.{k} = ${k}" for k in properties.keys()])
            query = f"""
            MATCH (n:{safe_label})
            WHERE {where_clause}
            RETURN n
            LIMIT 1
            """

            result = self.query(query, properties)
            if result.result_set:
                return self._parse_node(result.result_set[0][0])
            return None
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Error finding node: {e}")
            raise QueryError(f"Failed to find node: {str(e)}") from e

    def get_node_by_id(self, node_id: str) -> Optional[Dict[str, Any]]:
        """Get a node by its ID regardless of label"""
        query = "MATCH (n {id: $id}) RETURN n"
        result = self.query(query, {"id": node_id})
        if result.result_set:
            return self._parse_node(result.result_set[0][0])
        return None

    def update_node(self, label: str, node_id: str, updates: Dict[str, Any]) -> bool:
        """Update node properties"""
        # Add updated_at timestamp
        updates["updated_at"] = datetime.utcnow().isoformat()

        # Clean properties for update
        clean_updates = {}
        for key, value in updates.items():
            if isinstance(value, datetime):
                clean_updates[key] = value.isoformat()
            elif isinstance(value, (list, dict)):
                clean_updates[key] = json.dumps(value)
            elif value is not None:
                clean_updates[key] = value

        # Sanitize label
        try:
            safe_label = sanitize_label(label)
        except ValueError as e:
            logger.error(f"Invalid node label: {e}")
            raise ValidationError(str(e)) from e

        set_clause = ", ".join([f"n.{k} = ${k}" for k in clean_updates.keys()])
        query = f"""
        MATCH (n:{safe_label} {{id: $node_id}})
        SET {set_clause}
        RETURN n
        """

        params = {"node_id": node_id, **clean_updates}

        try:
            result = self.query(query, params)
            return bool(result.result_set)
        except Exception as e:
            logger.error(f"Failed to update node: {e}")
            return False

    def delete_node(self, label: str, node_id: str) -> bool:
        """Delete a node and its relationships"""
        # Sanitize label
        try:
            safe_label = sanitize_label(label)
        except ValueError as e:
            logger.error(f"Invalid node label: {e}")
            raise ValidationError(str(e)) from e

        query = f"""
        MATCH (n:{safe_label} {{id: $node_id}})
        DETACH DELETE n
        RETURN count(n) as deleted
        """

        try:
            result = self.query(query, {"node_id": node_id})
            return bool(result.result_set)
        except Exception as e:
            logger.error(f"Failed to delete node: {e}")
            return False

    def get_statistics(self, tenant_id: Optional[str] = None, include_global: bool = False) -> Dict[str, int]:
        """
        Get graph statistics, optionally filtered by tenant.
        
        Args:
            tenant_id: Tenant ID to filter by (None for all tenants)
            include_global: Whether to include global items (tenant_id IS NULL)
            
        Returns:
            Dictionary of label counts and relationship count
        """
        # Build WHERE clause for tenant filtering
        where_clause = ""
        params = {}
        
        if tenant_id is not None:
            if include_global:
                where_clause = "WHERE (n.tenant_id = $tenant_id OR n.tenant_id IS NULL)"
            else:
                where_clause = "WHERE n.tenant_id = $tenant_id"
            params["tenant_id"] = tenant_id
        
        query = f"""
        MATCH (n)
        {where_clause}
        WITH labels(n) as label, count(n) as count
        RETURN label, count
        """

        stats = {}
        result = self.query(query, params)

        for row in result.result_set:
            label = row[0][0] if row[0] else "Unknown"
            stats[label] = row[1]

        # Get relationship count with same filtering
        rel_where = where_clause.replace("n.", "a.").replace("WHERE", "WHERE") if where_clause else ""
        if rel_where and "b." not in rel_where:
            # Also filter target nodes
            rel_where = rel_where.replace(")", " AND b.tenant_id = $tenant_id)")
        
        rel_query = f"MATCH (a)-[r]->(b) {rel_where} RETURN count(r) as count"
        rel_result = self.query(rel_query, params)
        if rel_result.result_set:
            stats["_relationships"] = rel_result.result_set[0][0]

        return stats

    def _parse_node(self, node) -> Dict[str, Any]:
        """Parse a node from query result"""
        return parse_node(node, strict=False)

    def close(self):
        """Close the connection"""
        # FalkorDB doesn't have explicit close, handled by Redis client
        logger.info("FalkorDB connection closed")


class AsyncCoherenceKG:
    """Asynchronous FalkorDB client for high-performance operations"""

    def __init__(
        self,
        host: str = "localhost",
        port: int = 6381,
        graph_name: str = "coherence",
        max_connections: int = 16,
    ):
        """
        Initialize async FalkorDB client

        Args:
            host: FalkorDB host
            port: FalkorDB port
            graph_name: Name of the graph
            max_connections: Maximum number of connections in pool
        """
        self.host = host
        self.port = port
        self.graph_name = graph_name
        self.max_connections = max_connections
        self.pool = None
        self.db = None
        self.graph = None

    async def connect(self):
        """Establish connection to FalkorDB"""
        try:
            # Import async FalkorDB
            from falkordb.asyncio import FalkorDB as AsyncFalkorDB

            # Create connection pool
            self.pool = BlockingConnectionPool(
                max_connections=self.max_connections,
                timeout=30,  # Add reasonable timeout
                socket_keepalive=True,
                socket_keepalive_options=(1, 3, 5),
                decode_responses=True,
                host=self.host,
                port=self.port,
            )

            self.db = AsyncFalkorDB(connection_pool=self.pool)
            # Use await for async operation
            self.graph = await self.db.select_graph(self.graph_name)

            logger.info(
                f"Async connection established to FalkorDB at {self.host}:{self.port}"
            )

        except RedisConnectionError as e:
            logger.error(f"Failed to connect to FalkorDB: {e}")
            await self._cleanup_on_error()
            raise AsyncOperationError(
                f"Unable to establish async connection to FalkorDB at {self.host}:{self.port}"
            ) from e
        except ImportError as e:
            logger.error(f"FalkorDB async module not available: {e}")
            raise AsyncOperationError(
                "FalkorDB async support not installed. Install with: pip install falkordb[async]"
            ) from e
        except Exception as e:
            logger.error(f"Unexpected error during async connection: {e}")
            await self._cleanup_on_error()
            raise AsyncOperationError(f"Unexpected error: {str(e)}") from e

    async def _init_schema_async(self):
        """Initialize schema asynchronously - simplified for demo"""
        logger.info("Async schema initialization skipped for demo")
        # For demo, we can skip this or just log
        # The sync version already creates indexes on first connection
    
    async def disconnect(self):
        """Close the connection pool gracefully"""
        try:
            if self.pool:
                await self.pool.aclose()
                logger.info("Async FalkorDB connection pool closed")
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
            # Don't raise, as we're cleaning up
        finally:
            self.pool = None
            self.db = None
            self.graph = None

    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.disconnect()
        # Don't suppress exceptions
        return False
    
    async def _cleanup_on_error(self):
        """Clean up resources on error"""
        try:
            if self.pool:
                await self.pool.aclose()
        except Exception:
            pass  # Best effort cleanup
        finally:
            self.pool = None
            self.db = None
            self.graph = None

    async def query(self, cypher: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """Execute an async Cypher query"""
        try:
            result = await self.graph.query(cypher, params or {})
            return result
        except Exception as e:
            logger.error(f"Async query failed: {e}\nQuery: {cypher}\nParams: {params}")
            raise

    async def find_similar_intents(
        self, embedding: List[float], domain: Optional[str] = None, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Find similar intents using vector similarity"""
        # Note: FalkorDB vector operations syntax may vary
        # This is a placeholder for vector similarity search
        if domain:
            query = """
            MATCH (i:Intent)
            WHERE i.domain = $domain AND i.embedding IS NOT NULL
            RETURN i, i.id as id
            LIMIT $limit
            """
            params = {"domain": domain, "limit": limit}
        else:
            query = """
            MATCH (i:Intent)
            WHERE i.embedding IS NOT NULL
            RETURN i, i.id as id
            LIMIT $limit
            """
            params = {"limit": limit}

        result = await self.query(query, params)

        intents = []
        if result.result_set:
            for row in result.result_set:
                intent = self._parse_node(row[0])
                intents.append(intent)

        return intents

    async def bulk_create_nodes(self, label: str, nodes: List[Dict[str, Any]]) -> int:
        """Bulk create nodes for better performance"""
        # Sanitize label once for all nodes
        try:
            safe_label = sanitize_label(label)
        except ValueError as e:
            logger.error(f"Invalid node label: {e}")
            raise ValidationError(str(e)) from e

        created = 0

        for node_data in nodes:
            # Clean properties
            clean_props = {}
            for key, value in node_data.items():
                if isinstance(value, datetime):
                    clean_props[key] = value.isoformat()
                elif isinstance(value, (list, dict)):
                    clean_props[key] = json.dumps(value)
                elif value is not None:
                    clean_props[key] = value

            # Create node with sanitized label
            node = FalkorNode(alias="n", labels=safe_label, properties=clean_props)

            query = f"CREATE {node}"

            try:
                await self.query(query)
                created += 1
            except Exception as e:
                logger.error(f"Failed to create node in bulk: {e}")

        return created

    async def find_optimal_workflow(
        self, start_intent_id: str, goal_id: str
    ) -> Optional[Dict[str, Any]]:
        """Find optimal path using graph algorithms"""
        # Simplified shortest path query
        query = """
        MATCH path = (start:Intent {id: $start})-[*1..5]-(goal:Goal {id: $goal})
        RETURN path, length(path) as pathLength
        ORDER BY pathLength
        LIMIT 1
        """

        result = await self.query(query, {"start": start_intent_id, "goal": goal_id})

        if result.result_set:
            return {"path": result.result_set[0][0], "length": result.result_set[0][1]}
        return None

    async def bulk_update_patterns(self, pattern_updates: List[Dict[str, Any]]) -> int:
        """Batch update pattern confidence scores"""
        updated = 0

        for update in pattern_updates:
            query = """
            MATCH (p:Pattern {id: $id})
            SET p.confidence = $confidence,
                p.last_used = $now,
                p.updated_at = $now
            RETURN p
            """

            result = await self.query(
                query,
                {
                    "id": update["id"],
                    "confidence": update["confidence"],
                    "now": datetime.utcnow().isoformat(),
                },
            )

            if result.result_set:
                updated += 1

        return updated

    def _parse_node(self, node) -> Dict[str, Any]:
        """Parse a node from async query result"""
        return parse_node(node, strict=False)


# Convenience function for async context
@asynccontextmanager
async def get_async_kg(host: str = "localhost", port: int = 6381) -> AsyncCoherenceKG:
    """Get async KG client as context manager"""
    kg = AsyncCoherenceKG(host=host, port=port)
    await kg.connect()
    try:
        yield kg
    finally:
        await kg.disconnect()
