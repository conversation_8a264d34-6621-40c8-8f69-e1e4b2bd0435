"""
Service for managing graph nodes
"""

import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, TypeVar

from ..db import AsyncCoherenceKG, CoherenceKG
from ..db.exceptions import ValidationError
from ..models.nodes import (
    ApiCall,
    BaseNode,
    Context,
    Domain,
    Goal,
    Insight,
    Integration,
    Intent,
    LearningEvent,
    Metric,
    NodeType,
    Organization,
    Pattern,
    Session,
    Template,
    Tenant,
    User,
    Workflow,
)
from .base import AsyncTenantIsolatedService, TenantIsolatedService

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=BaseNode)


class NodeService(TenantIsolatedService):
    """Service for managing graph nodes with tenant isolation"""

    # Mapping of node types to model classes
    NODE_MODELS = {
        NodeType.INTENT: Intent,
        NodeType.TEMPLATE: Template,
        NodeType.WORKFLOW: Workflow,
        NodeType.USER: User,
        NodeType.SESSION: Session,
        NodeType.PATTERN: Pattern,
        NodeType.DOMAIN: Domain,
        NodeType.CONTEXT: Context,
        NodeType.API_CALL: ApiCall,
        NodeType.INTEGRATION: Integration,
        NodeType.ORGANIZATION: Organization,
        NodeType.TENANT: Tenant,
        NodeType.LEARNING_EVENT: LearningEvent,
        NodeType.GOAL: Goal,
        NodeType.METRIC: Metric,
        NodeType.INSIGHT: Insight,
    }

    def __init__(self, kg: CoherenceKG, tenant_id: Optional[str] = None):
        """Initialize with KG client and optional tenant ID"""
        super().__init__(kg, tenant_id)

    def create_node(
        self, node_type: NodeType, data: Dict[str, Any], tenant_id: Optional[str] = None
    ) -> Optional[BaseNode]:
        """Create a node of specified type with tenant isolation"""
        try:
            # Get the model class
            model_class = self.NODE_MODELS.get(node_type)
            if not model_class:
                raise ValueError(f"Unknown node type: {node_type}")

            # Add tenant_id if the node type supports it
            if hasattr(model_class, "model_fields") and "tenant_id" in model_class.model_fields:
                data["tenant_id"] = self._get_tenant_id(tenant_id)

            # Validate data with Pydantic model
            node = model_class(**data)

            # Create in graph
            created = self.kg.create_node(node_type.value, node.model_dump())

            if created:
                # Return as model instance
                return model_class(**created)

            return None

        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Failed to create {node_type} node: {e}")
            raise

    def get_node_by_id(
        self, 
        node_id: str,
        tenant_id: Optional[str] = None,
        skip_tenant_check: bool = False
    ) -> Optional[Dict[str, Any]]:
        """Get a node by ID regardless of type (searches all node types)"""
        # Try each node type until we find the node
        for node_type in NodeType:
            try:
                node_data = self.kg.get_node_by_id(node_type.value, node_id)
                if node_data:
                    # Check tenant access if not skipped
                    if not skip_tenant_check and "tenant_id" in node_data:
                        node_tenant_id = node_data.get("tenant_id")
                        expected_tenant_id = self._get_tenant_id(tenant_id)
                        
                        # Deny access if node belongs to different tenant
                        if node_tenant_id and node_tenant_id != expected_tenant_id:
                            from ..db.exceptions import TenantIsolationError
                            raise TenantIsolationError(
                                f"Access denied: Node {node_id} belongs to tenant {node_tenant_id}"
                            )
                    
                    return node_data
            except Exception:
                # Continue searching other node types
                continue
        
        return None

    def get_node(
        self, 
        node_type: NodeType, 
        node_id: str,
        tenant_id: Optional[str] = None,
        skip_tenant_check: bool = False
    ) -> Optional[BaseNode]:
        """Get a node by type and ID with tenant validation"""
        try:
            node_data = self.kg.get_node_by_id(node_type.value, node_id)

            if node_data:
                model_class = self.NODE_MODELS.get(node_type)
                if not model_class:
                    raise ValueError(f"Unknown node type: {node_type}")
                
                # Check tenant access if not skipped
                if not skip_tenant_check:
                    has_tenant_field = (
                        hasattr(model_class, "model_fields") and 
                        "tenant_id" in model_class.model_fields
                    )
                    
                    if has_tenant_field:
                        node_tenant_id = node_data.get("tenant_id")
                        expected_tenant_id = self._get_tenant_id(tenant_id)
                        
                        # Allow access if node belongs to tenant or is global
                        if node_tenant_id and node_tenant_id != expected_tenant_id:
                            logger.warning(
                                f"Tenant isolation violation: Node {node_id} belongs to "
                                f"tenant {node_tenant_id}, not {expected_tenant_id}"
                            )
                            return None
                
                return model_class(**node_data)

            return None

        except Exception as e:
            logger.error(f"Failed to get {node_type} node {node_id}: {e}")
            raise

    def update_node(
        self, node_type: NodeType, node_id: str, updates: Dict[str, Any]
    ) -> bool:
        """Update a node's properties"""
        try:
            # Validate updates don't break model constraints
            existing = self.get_node(node_type, node_id)
            if not existing:
                return False

            # Merge updates
            updated_data = existing.model_dump()
            updated_data.update(updates)

            # Validate with model
            model_class = self.NODE_MODELS.get(node_type)
            model_class(**updated_data)

            # Update in graph (only send the updates)
            return self.kg.update_node(node_type.value, node_id, updates)

        except Exception as e:
            logger.error(f"Failed to update {node_type} node {node_id}: {e}")
            return False

    def delete_node(self, node_type: NodeType, node_id: str) -> bool:
        """Delete a node and its relationships"""
        try:
            return self.kg.delete_node(node_type.value, node_id)
        except Exception as e:
            logger.error(f"Failed to delete {node_type} node {node_id}: {e}")
            return False

    def find_nodes(
        self, 
        node_type: NodeType, 
        filters: Dict[str, Any], 
        limit: int = 100,
        tenant_id: Optional[str] = None,
        include_global: bool = False
    ) -> List[BaseNode]:
        """Find nodes matching filters with tenant isolation"""
        try:
            model_class = self.NODE_MODELS.get(node_type)
            if not model_class:
                raise ValueError(f"Unknown node type: {node_type}")
            
            # Check if this node type has tenant_id field
            has_tenant_field = (
                hasattr(model_class, "model_fields") and 
                "tenant_id" in model_class.model_fields
            )
            
            # Build WHERE clause
            where_parts = []
            params = {}

            # Add tenant filtering if applicable
            if has_tenant_field:
                tenant_where, tenant_params = self._build_tenant_where_clause(
                    "n", tenant_id, include_global
                )
                where_parts.append(tenant_where)
                params.update(tenant_params)

            # Add other filters
            for key, value in filters.items():
                param_name = f"filter_{key}"
                where_parts.append(f"n.{key} = ${param_name}")
                params[param_name] = value

            where_clause = " AND ".join(where_parts) if where_parts else "1=1"

            query = f"""
            MATCH (n:{node_type.value})
            WHERE {where_clause}
            RETURN n
            ORDER BY n.created_at DESC
            LIMIT $limit
            """
            params["limit"] = limit

            result = self.kg.query(query, params)

            nodes = []
            if result.result_set:
                for row in result.result_set:
                    node_data = self.kg._parse_node(row[0])
                    if node_data:
                        try:
                            nodes.append(model_class(**node_data))
                        except Exception as e:
                            logger.warning(f"Failed to parse node: {e}")

            return nodes

        except Exception as e:
            logger.error(f"Failed to find {node_type} nodes: {e}")
            raise

    # Specialized methods for specific node types

    def create_intent(
        self,
        content: str,
        domain: str,
        user_id: str,
        session_id: str,
        tenant_id: str,
        confidence: float = 0.95,
        embedding: Optional[List[float]] = None,
        parameters: Optional[Dict[str, Any]] = None,
    ) -> Optional[Intent]:
        """Create an Intent node"""
        data = {
            "content": content,
            "domain": domain,
            "user_id": user_id,
            "session_id": session_id,
            "tenant_id": tenant_id,
            "confidence": confidence,
            "embedding": embedding,
            "parameters": parameters or {},
        }
        return self.create_node(NodeType.INTENT, data)

    def create_workflow(
        self,
        name: str,
        intent_id: str,
        user_id: str,
        session_id: str,
        tenant_id: str,
        template_id: Optional[str] = None,
    ) -> Optional[Workflow]:
        """Create a Workflow node"""
        data = {
            "name": name,
            "intent_id": intent_id,
            "template_id": template_id,
            "user_id": user_id,
            "session_id": session_id,
            "tenant_id": tenant_id,
            "status": "pending",
            "started_at": datetime.now(timezone.utc),
        }
        return self.create_node(NodeType.WORKFLOW, data)

    def create_user(
        self,
        clerk_id: str,
        organization_id: str, 
        tenant_id: str,
        email: Optional[str] = None,
        name: Optional[str] = None,
        preferences: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[User]:
        """Create a User node"""
        data = {
            "clerk_id": clerk_id,
            "organization_id": organization_id,
            "tenant_id": tenant_id,
            "email": email,
            "name": name,
            "preferences": preferences or {},
            "metadata": metadata or {},
        }
        return self.create_node(NodeType.USER, data)

    def create_template(
        self,
        name: str,
        description: str,
        category: str,
        schema: Dict[str, Any],
        domain: Optional[str] = None,
        version: str = "1.0.0",
        response_format: Optional[Dict[str, Any]] = None,
        required_parameters: Optional[List[str]] = None,
        optional_parameters: Optional[List[str]] = None,
        examples: Optional[List[Dict[str, Any]]] = None,
        tags: Optional[List[str]] = None,
        tenant_id: Optional[str] = None,
        is_active: bool = True,
    ) -> Optional[Template]:
        """Create a Template node"""
        data = {
            "name": name,
            "description": description,
            "category": category,
            "schema": schema,  # Will be aliased to template_schema
            "domain": domain,
            "version": version,
            "response_format": response_format,
            "required_parameters": required_parameters or [],
            "optional_parameters": optional_parameters or [],
            "examples": examples or [],
            "tags": tags or [],
            "tenant_id": tenant_id,
            "is_active": is_active,
        }
        return self.create_node(NodeType.TEMPLATE, data)

    def update_workflow_status(
        self,
        workflow_id: str,
        status: str,
        result: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
    ) -> bool:
        """Update workflow execution status"""
        updates = {"status": status, "updated_at": datetime.now(timezone.utc)}

        if status == "completed":
            updates["completed_at"] = datetime.now(timezone.utc)
            if result:
                updates["result"] = result
        elif status == "failed" and error:
            updates["error"] = error
            updates["completed_at"] = datetime.now(timezone.utc)

        return self.update_node(NodeType.WORKFLOW, workflow_id, updates)

    def get_active_sessions(self, tenant_id: Optional[str] = None) -> List[Session]:
        """Get all active sessions for a tenant"""
        return self.find_nodes(
            NodeType.SESSION, 
            {"is_active": True},
            tenant_id=tenant_id
        )

    def get_tenant_templates(
        self, tenant_id: Optional[str] = None, include_global: bool = True
    ) -> List[Template]:
        """Get templates available to a tenant"""
        return self.find_nodes(
            NodeType.TEMPLATE,
            {"is_active": True},
            tenant_id=tenant_id,
            include_global=include_global
        )

    def record_metric(
        self,
        name: str,
        value: float,
        source_id: str,
        source_type: str,
        tenant_id: str,
        dimensions: Optional[Dict[str, str]] = None,
    ) -> Optional[Metric]:
        """Record a performance metric"""
        data = {
            "name": name,
            "value": value,
            "source_id": source_id,
            "source_type": source_type,
            "tenant_id": tenant_id,
            "type": "gauge",
            "dimensions": dimensions or {},
        }
        return self.create_node(NodeType.METRIC, data)


class AsyncNodeService(AsyncTenantIsolatedService):
    """Async version of NodeService with tenant isolation"""

    def __init__(self, kg: AsyncCoherenceKG, tenant_id: Optional[str] = None):
        """Initialize with async KG client and optional tenant ID"""
        super().__init__(kg, tenant_id)
        self.sync_service = None  # For model validation

    async def create_nodes_bulk(
        self, node_type: NodeType, nodes_data: List[Dict[str, Any]]
    ) -> int:
        """Bulk create nodes of the same type"""
        try:
            # Validate all nodes
            model_class = NodeService.NODE_MODELS.get(node_type)
            validated_nodes = []

            for data in nodes_data:
                node = model_class(**data)
                validated_nodes.append(node.model_dump())

            # Bulk create
            return await self.kg.bulk_create_nodes(node_type.value, validated_nodes)

        except Exception as e:
            logger.error(f"Failed to bulk create {node_type} nodes: {e}")
            return 0

    async def find_similar_intents(
        self,
        embedding: List[float],
        domain: Optional[str] = None,
        tenant_id: Optional[str] = None,
        limit: int = 10,
    ) -> List[Intent]:
        """Find intents similar to the given embedding"""
        # This is a placeholder - real vector search would be implemented
        # based on FalkorDB's vector capabilities
        similar = await self.kg.find_similar_intents(embedding, domain, limit)

        intents = []
        for intent_data in similar:
            if tenant_id and intent_data.get("tenant_id") != tenant_id:
                continue
            intents.append(Intent(**intent_data))

        return intents[:limit]

    async def create_user(
        self,
        clerk_id: str,
        organization_id: str, 
        tenant_id: str,
        email: Optional[str] = None,
        name: Optional[str] = None,
        preferences: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[User]:
        """Create a User node asynchronously"""
        data = {
            "clerk_id": clerk_id,
            "organization_id": organization_id,
            "tenant_id": tenant_id,
            "email": email,
            "name": name,
            "preferences": preferences or {},
            "metadata": metadata or {},
        }
        
        # Validate with model
        model_class = NodeService.NODE_MODELS.get(NodeType.USER)
        node = model_class(**data)
        
        # Create in graph
        created = await self.kg.create_node(NodeType.USER.value, node.model_dump())
        if created:
            return model_class(**created)
        
        return None

    async def bulk_create_users(
        self, users_data: List[Dict[str, Any]]
    ) -> int:
        """Bulk create user nodes"""
        # Prepare nodes with tenant_id
        nodes = []
        model_class = NodeService.NODE_MODELS.get(NodeType.USER)
        
        for data in users_data:
            # Add required fields if missing
            if "tenant_id" not in data:
                data["tenant_id"] = self._get_tenant_id()
            if "organization_id" not in data:
                data["organization_id"] = data.get("tenant_id", "default-org")
            if "clerk_id" not in data:
                data["clerk_id"] = f"user-{data.get('email', 'unknown')}"
                
            # Validate
            try:
                user = model_class(**data)
                nodes.append({
                    "label": NodeType.USER.value,
                    "properties": user.model_dump()
                })
            except ValidationError as e:
                logger.warning(f"Skipping invalid user data: {e}")
                continue
        
        if nodes:
            return await self.kg.bulk_create_nodes(nodes)
        
        return 0
