"""
Node type definitions for Coherence Knowledge Graph
"""

import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, ConfigDict, field_validator


class NodeType(str, Enum):
    """Enumeration of all node types in the graph"""

    INTENT = "Intent"
    TEMPLATE = "Template"
    ACTION = "Action"
    WORKFLOW = "Workflow"
    USER = "User"
    SESSION = "Session"
    PATTERN = "Pattern"
    DOMAIN = "Domain"
    CONTEXT = "Context"
    API_CALL = "ApiCall"
    INTEGRATION = "Integration"
    ORGANIZATION = "Organization"
    TENANT = "Tenant"
    LEARNING_EVENT = "LearningEvent"
    GOAL = "Goal"
    METRIC = "Metric"
    INSIGHT = "Insight"


class BaseNode(BaseModel):
    """Base class for all graph nodes"""
    
    model_config = ConfigDict()

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), serialization_alias="created_at")
    updated_at: Optional[datetime] = Field(default=None, serialization_alias="updated_at")
    
    @field_validator('created_at', 'updated_at', mode='before')
    @classmethod
    def parse_datetime(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v


class Intent(BaseNode):
    """User intention or request"""

    content: str
    domain: str
    confidence: float = Field(ge=0.0, le=1.0)
    embedding: Optional[List[float]] = None
    user_id: str
    session_id: str
    tenant_id: str
    resolved: bool = False
    parameters: Dict[str, Any] = Field(default_factory=dict)

    @field_validator("embedding")
    def validate_embedding(cls, v):
        if v and len(v) != 384:  # Assuming 384-dim embeddings
            raise ValueError("Embedding must be 384 dimensions")
        return v


class Template(BaseNode):
    """Reusable action template"""

    name: str
    description: str
    category: str
    domain: Optional[str] = None
    version: str = "1.0.0"
    template_schema: Dict[str, Any] = Field(..., alias="schema")
    response_format: Optional[Dict[str, Any]] = None
    required_parameters: List[str] = Field(default_factory=list)
    optional_parameters: List[str] = Field(default_factory=list)
    embedding: Optional[List[float]] = None
    examples: List[Dict[str, Any]] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)
    tenant_id: Optional[str] = None  # None for global templates
    is_active: bool = True


class Workflow(BaseNode):
    """Executed workflow instance"""

    name: str
    status: str = "pending"  # pending, running, completed, failed
    intent_id: str
    template_id: Optional[str] = None
    user_id: str
    session_id: str
    tenant_id: str
    steps: List[Dict[str, Any]] = Field(default_factory=list)
    parameters: Dict[str, Any] = Field(default_factory=dict)
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_ms: Optional[int] = None


class User(BaseNode):
    """System user"""

    clerk_id: str
    email: Optional[str] = None
    name: Optional[str] = None
    organization_id: str
    tenant_id: str
    preferences: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    last_active: Optional[datetime] = None
    total_sessions: int = 0
    total_workflows: int = 0


class Action(BaseNode):
    """Executable action or operation"""
    
    name: str
    type: str  # api_call, workflow, notification, etc.
    endpoint: Optional[str] = None
    method: Optional[str] = None  # GET, POST, etc.
    parameters: Dict[str, Any] = Field(default_factory=dict)
    headers: Dict[str, Any] = Field(default_factory=dict)
    response_schema: Optional[Dict[str, Any]] = None
    timeout: int = 30
    retry_count: int = 3
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Session(BaseNode):
    """User conversation session"""

    user_id: str
    tenant_id: str
    started_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    ended_at: Optional[datetime] = None
    context: Dict[str, Any] = Field(default_factory=dict)
    message_count: int = 0
    workflow_count: int = 0
    metadata: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool = True


class Pattern(BaseNode):
    """Learned behavioral pattern"""

    name: str
    type: str  # sequence, frequency, preference, optimization
    confidence: float = Field(ge=0.0, le=1.0)
    support: int = 0  # Number of occurrences
    tenant_id: str
    domain: Optional[str] = None
    conditions: Dict[str, Any] = Field(default_factory=dict)
    actions: List[Dict[str, Any]] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    last_used: Optional[datetime] = None
    is_active: bool = True


class Domain(BaseNode):
    """Domain knowledge node"""

    name: str
    description: str
    parent_domain: Optional[str] = None
    ontology: Dict[str, Any] = Field(default_factory=dict)
    entities: List[str] = Field(default_factory=list)
    relationships: List[Dict[str, str]] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Context(BaseNode):
    """Contextual information node"""

    type: str  # temporal, spatial, user_state, system_state
    session_id: str
    data: Dict[str, Any]
    valid_from: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    valid_until: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ApiCall(BaseNode):
    """External API call record"""

    workflow_id: str
    integration_id: str
    endpoint: str
    method: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    headers: Dict[str, Any] = Field(default_factory=dict)
    response: Optional[Dict[str, Any]] = None
    status_code: Optional[int] = None
    duration_ms: Optional[int] = None
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    error: Optional[str] = None
    archived: bool = False


class Integration(BaseNode):
    """Third-party service integration"""

    name: str
    type: str  # rest_api, graphql, webhook, etc.
    base_url: str
    auth_type: str  # api_key, oauth2, basic, etc.
    tenant_id: str
    credentials_encrypted: Optional[str] = None  # Encrypted credentials
    endpoints: List[Dict[str, Any]] = Field(default_factory=list)
    rate_limits: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool = True
    last_used: Optional[datetime] = None


class Organization(BaseNode):
    """Organization entity"""

    name: str
    clerk_org_id: str
    tenant_id: str
    plan: str = "free"  # free, pro, enterprise
    settings: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_by: Optional[str] = None
    is_active: bool = True


class Tenant(BaseNode):
    """Multi-tenant isolation node"""

    name: str
    clerk_org_id: str
    settings: Dict[str, Any] = Field(default_factory=dict)
    quotas: Dict[str, int] = Field(default_factory=dict)
    features: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool = True


class LearningEvent(BaseNode):
    """Meta-learning event for self-improvement"""

    type: str  # pattern_discovered, optimization_applied, error_corrected
    source_id: str  # ID of the source node (workflow, pattern, etc.)
    source_type: str
    tenant_id: str
    improvement: Dict[str, Any]
    impact_score: float = Field(ge=0.0, le=1.0)
    applied: bool = False
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Goal(BaseNode):
    """High-level objective for orchestration"""

    name: str
    description: str
    type: str  # efficiency, accuracy, user_satisfaction, cost_reduction
    tenant_id: str
    target_value: float
    current_value: float = 0.0
    priority: int = Field(ge=1, le=10)
    constraints: Dict[str, Any] = Field(default_factory=dict)
    strategies: List[Dict[str, Any]] = Field(default_factory=list)
    is_active: bool = True
    achieved_at: Optional[datetime] = None


class Metric(BaseNode):
    """Performance and usage metrics"""

    name: str
    type: str  # counter, gauge, histogram, summary
    value: float
    unit: Optional[str] = None
    source_id: str
    source_type: str
    tenant_id: str
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    dimensions: Dict[str, str] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Insight(BaseNode):
    """Discovered insights and recommendations"""

    title: str
    description: str
    type: str  # optimization, anomaly, trend, recommendation
    confidence: float = Field(ge=0.0, le=1.0)
    impact: str  # high, medium, low
    tenant_id: str
    source_metrics: List[str] = Field(default_factory=list)
    recommendations: List[Dict[str, Any]] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    acknowledged: bool = False
    implemented: bool = False
