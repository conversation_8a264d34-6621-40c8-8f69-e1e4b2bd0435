"""
Relationship type definitions for Coherence Knowledge Graph
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, ConfigDict


class RelationshipType(str, Enum):
    """Enumeration of all relationship types"""

    TRIGGERS = "TRIGGERS"
    USES_TEMPLATE = "USES_TEMPLATE"
    CREATES_SESSION = "CREATES_SESSION"
    REQUIRES = "REQUIRES"
    PRECEDES = "PRECEDES"
    LEARNED_FROM = "LEARNED_FROM"
    BELONGS_TO = "BELONGS_TO"
    MAPS_TO = "MAPS_TO"
    CALLS = "CALLS"
    NEXT_STEP = "NEXT_STEP"
    USES_INTEGRATION = "USES_INTEGRATION"
    ACHIEVES = "ACHIEVES"
    MEASURES = "MEASURES"
    DISCOVERED_FROM = "DISCOVERED_FROM"
    SIMILAR_TO = "SIMILAR_TO"
    DERIVED_FROM = "DERIVED_FROM"
    OPTIMIZES = "OPTIMIZES"
    PART_OF = "PART_OF"
    INFLUENCES = "INFLUENCES"
    CORRELATES_WITH = "CORRELATES_WITH"


class BaseRelationship(BaseModel):
    """Base class for all relationships"""
    
    model_config = ConfigDict()

    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    metadata: Dict[str, Any] = Field(default_factory=dict)


# Intent Relationships


class TRIGGERS(BaseRelationship):
    """Intent triggers Workflow"""

    confidence: float = Field(ge=0.0, le=1.0)
    trigger_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class USES_TEMPLATE(BaseRelationship):
    """Intent uses Template"""

    match_score: float = Field(ge=0.0, le=1.0)
    parameter_mapping: Dict[str, str] = Field(default_factory=dict)


# User/Session Relationships


class CREATES_SESSION(BaseRelationship):
    """User creates Session"""

    device_info: Optional[Dict[str, Any]] = None
    location: Optional[str] = None


# Workflow Relationships


class REQUIRES(BaseRelationship):
    """Workflow requires another Workflow (dependency)"""

    requirement_type: str = "data"  # data, completion, approval
    is_blocking: bool = True


class PRECEDES(BaseRelationship):
    """Workflow precedes another Workflow (sequence)"""

    time_gap_ms: Optional[int] = None
    is_immediate: bool = False


class NEXT_STEP(BaseRelationship):
    """Workflow step to next step"""

    step_number: int
    condition: Optional[Dict[str, Any]] = None


# Learning Relationships


class LEARNED_FROM(BaseRelationship):
    """Pattern learned from Workflow/Session"""

    learning_rate: float = Field(ge=0.0, le=1.0)
    sample_size: int = 1
    algorithm: Optional[str] = None


class DISCOVERED_FROM(BaseRelationship):
    """Insight discovered from Metrics/Patterns"""

    analysis_type: str  # statistical, ml, rule-based
    significance: float = Field(ge=0.0, le=1.0)


# Organizational Relationships


class BELONGS_TO(BaseRelationship):
    """Entity belongs to Organization/Tenant"""

    role: Optional[str] = None
    permissions: List[str] = Field(default_factory=list)


class PART_OF(BaseRelationship):
    """Hierarchical relationship (e.g., Domain part of Domain)"""

    hierarchy_level: int = 0
    order: Optional[int] = None


# Mapping Relationships


class MAPS_TO(BaseRelationship):
    """Intent/Template maps to Domain"""

    mapping_strength: float = Field(ge=0.0, le=1.0)
    mapping_type: str = "semantic"  # semantic, syntactic, functional


# Integration Relationships


class CALLS(BaseRelationship):
    """Workflow calls ApiCall"""

    call_order: int
    is_async: bool = False
    timeout_ms: Optional[int] = None


class USES_INTEGRATION(BaseRelationship):
    """Template/Workflow uses Integration"""

    endpoints_used: List[str] = Field(default_factory=list)
    auth_method: Optional[str] = None


# Goal-Oriented Relationships


class ACHIEVES(BaseRelationship):
    """Workflow/Pattern achieves Goal"""

    contribution: float = Field(ge=-1.0, le=1.0)  # Can be negative
    strategy_id: Optional[str] = None


class OPTIMIZES(BaseRelationship):
    """Pattern/LearningEvent optimizes Workflow/Goal"""

    optimization_type: str  # performance, cost, quality, user_experience
    improvement_percentage: float
    baseline_value: float
    optimized_value: float


# Analytics Relationships


class MEASURES(BaseRelationship):
    """Metric measures Workflow/Pattern/Goal"""

    measurement_type: str  # direct, derived, aggregate
    aggregation: Optional[str] = None  # sum, avg, max, min, count


class INFLUENCES(BaseRelationship):
    """Pattern/Context influences Workflow outcome"""

    influence_type: str  # positive, negative, neutral
    influence_score: float = Field(ge=-1.0, le=1.0)
    causality_confidence: float = Field(ge=0.0, le=1.0)


class CORRELATES_WITH(BaseRelationship):
    """Metric/Pattern correlates with another"""

    correlation_coefficient: float = Field(ge=-1.0, le=1.0)
    p_value: Optional[float] = None
    sample_size: int
    time_window: Optional[str] = None  # e.g., "1h", "1d", "1w"


class SIMILAR_TO(BaseRelationship):
    """Intent/Template similar to another (for recommendations)"""

    similarity_score: float = Field(ge=0.0, le=1.0)
    similarity_type: str  # semantic, structural, behavioral
    features_compared: List[str] = Field(default_factory=list)


class DERIVED_FROM(BaseRelationship):
    """Template/Pattern derived from another (evolution tracking)"""

    derivation_type: str  # fork, enhancement, specialization
    changes: List[Dict[str, Any]] = Field(default_factory=list)
    version_delta: Optional[str] = None


# Relationship factory for easy creation


def create_relationship(rel_type: RelationshipType, **kwargs) -> BaseRelationship:
    """Factory function to create relationship instances"""
    relationship_classes = {
        RelationshipType.TRIGGERS: TRIGGERS,
        RelationshipType.USES_TEMPLATE: USES_TEMPLATE,
        RelationshipType.CREATES_SESSION: CREATES_SESSION,
        RelationshipType.REQUIRES: REQUIRES,
        RelationshipType.PRECEDES: PRECEDES,
        RelationshipType.LEARNED_FROM: LEARNED_FROM,
        RelationshipType.BELONGS_TO: BELONGS_TO,
        RelationshipType.MAPS_TO: MAPS_TO,
        RelationshipType.CALLS: CALLS,
        RelationshipType.NEXT_STEP: NEXT_STEP,
        RelationshipType.USES_INTEGRATION: USES_INTEGRATION,
        RelationshipType.ACHIEVES: ACHIEVES,
        RelationshipType.MEASURES: MEASURES,
        RelationshipType.DISCOVERED_FROM: DISCOVERED_FROM,
        RelationshipType.SIMILAR_TO: SIMILAR_TO,
        RelationshipType.DERIVED_FROM: DERIVED_FROM,
        RelationshipType.OPTIMIZES: OPTIMIZES,
        RelationshipType.PART_OF: PART_OF,
        RelationshipType.INFLUENCES: INFLUENCES,
        RelationshipType.CORRELATES_WITH: CORRELATES_WITH,
    }

    rel_class = relationship_classes.get(rel_type)
    if not rel_class:
        raise ValueError(f"Unknown relationship type: {rel_type}")

    return rel_class(**kwargs)
