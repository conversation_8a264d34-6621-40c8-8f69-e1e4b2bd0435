#!/usr/bin/env python3
"""
Seed data generator for Coherence Knowledge Graph
Generates realistic test data for all node types
"""

import os
import random
import sys
from datetime import datetime, timedelta, timezone

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.db import CoherenceKG
from src.models.nodes import NodeType
from src.services import NodeService, RelationshipService


class SeedDataGenerator:
    """Generate seed data for the knowledge graph"""

    def __init__(self, kg: CoherenceKG):
        self.kg = kg
        self.node_service = NodeService(kg)
        self.rel_service = RelationshipService(kg)

        # Track created entities for relationships
        self.tenants = []
        self.organizations = []
        self.users = []
        self.sessions = []
        self.intents = []
        self.templates = []
        self.workflows = []
        self.domains = []
        self.patterns = []
        self.goals = []
        self.metrics = []
        self.integrations = []

    def generate_all(self):
        """Generate all seed data"""
        print("🌱 Generating seed data...")

        # Core entities
        self._generate_tenants()
        self._generate_organizations()
        self._generate_users()
        self._generate_domains()
        self._generate_templates()
        self._generate_integrations()
        self._generate_sessions()
        self._generate_intents()
        self._generate_workflows()

        # Analytics entities
        self._generate_patterns()
        self._generate_goals()
        self._generate_metrics()
        self._generate_insights()

        # Create relationships
        self._generate_relationships()

        # Print statistics
        stats = self.kg.get_statistics()
        print("\n📊 Graph Statistics:")
        for label, count in sorted(stats.items()):
            print(f"  {label}: {count}")

    def _generate_tenants(self):
        """Generate tenant nodes"""
        print("Creating tenants...")

        tenant_configs = [
            {
                "name": "Acme Corporation",
                "clerk_org_id": "org_acme_123",
                "settings": {"theme": "dark", "language": "en"},
                "features": ["chat", "workflows", "analytics", "integrations"],
                "quotas": {"workflows": 1000, "api_calls": 50000},
            },
            {
                "name": "TechStart Inc",
                "clerk_org_id": "org_techstart_456",
                "settings": {"theme": "light", "language": "en"},
                "features": ["chat", "workflows"],
                "quotas": {"workflows": 100, "api_calls": 5000},
            },
            {
                "name": "Global Retail",
                "clerk_org_id": "org_retail_789",
                "settings": {"theme": "dark", "language": "en", "timezone": "UTC"},
                "features": [
                    "chat",
                    "workflows",
                    "analytics",
                    "integrations",
                    "ml_insights",
                ],
                "quotas": {"workflows": 5000, "api_calls": 200000},
            },
        ]

        for config in tenant_configs:
            tenant = self.node_service.create_node(NodeType.TENANT, config)
            self.tenants.append(tenant)
            print(f"  ✅ Created tenant: {tenant.name}")

    def _generate_organizations(self):
        """Generate organization nodes"""
        print("\nCreating organizations...")

        for i, tenant in enumerate(self.tenants):
            # Main org
            main_org = self.node_service.create_node(
                NodeType.ORGANIZATION,
                {
                    "name": f"{tenant.name} Main",
                    "clerk_org_id": tenant.clerk_org_id,
                    "tenant_id": tenant.id,
                    "plan": ["free", "pro", "enterprise"][i % 3],
                    "settings": {"notifications": True},
                },
            )
            self.organizations.append(main_org)

            # Sub-orgs for enterprise
            if i == 2:  # Global Retail
                for dept in ["Sales", "Marketing", "Support"]:
                    sub_org = self.node_service.create_node(
                        NodeType.ORGANIZATION,
                        {
                            "name": f"Global Retail - {dept}",
                            "clerk_org_id": f"org_retail_{dept.lower()}",
                            "tenant_id": tenant.id,
                            "plan": "enterprise",
                            "settings": {"department": dept},
                        },
                    )
                    self.organizations.append(sub_org)

        print(f"  ✅ Created {len(self.organizations)} organizations")

    def _generate_users(self):
        """Generate user nodes"""
        print("\nCreating users...")

        user_names = [
            ("Alice Johnson", "<EMAIL>"),
            ("Bob Smith", "<EMAIL>"),
            ("Carol Williams", "<EMAIL>"),
            ("David Brown", "<EMAIL>"),
            ("Eve Davis", "<EMAIL>"),
            ("Frank Miller", "<EMAIL>"),
            ("Grace Wilson", "<EMAIL>"),
            ("Henry Moore", "<EMAIL>"),
        ]

        for i, (name, email) in enumerate(user_names):
            org = self.organizations[i % len(self.organizations)]
            tenant = self.tenants[i % len(self.tenants)]

            user = self.node_service.create_node(
                NodeType.USER,
                {
                    "clerk_id": f"user_{email.split('@')[0]}_{i}",
                    "email": email,
                    "name": name,
                    "organization_id": org.id,
                    "tenant_id": tenant.id,
                    "preferences": {
                        "notifications": True,
                        "theme": ["light", "dark"][i % 2],
                    },
                    "last_active": datetime.now(timezone.utc)
                    - timedelta(hours=random.randint(1, 72)),
                },
            )
            self.users.append(user)

        print(f"  ✅ Created {len(self.users)} users")

    def _generate_domains(self):
        """Generate domain knowledge nodes"""
        print("\nCreating domains...")

        domains_config = [
            {
                "name": "customer_service",
                "description": "Customer service and support domain",
                "entities": ["ticket", "customer", "agent", "issue", "resolution"],
                "ontology": {
                    "intents": [
                        "create_ticket",
                        "check_status",
                        "escalate",
                        "close_ticket",
                    ],
                    "entities": ["priority", "category", "status"],
                },
            },
            {
                "name": "sales",
                "description": "Sales and CRM domain",
                "entities": ["lead", "opportunity", "account", "contact", "deal"],
                "ontology": {
                    "intents": ["create_lead", "update_opportunity", "close_deal"],
                    "stages": [
                        "prospect",
                        "qualified",
                        "proposal",
                        "negotiation",
                        "closed",
                    ],
                },
            },
            {
                "name": "analytics",
                "description": "Data analytics and reporting",
                "entities": ["metric", "report", "dashboard", "kpi", "trend"],
                "ontology": {
                    "metrics": ["revenue", "conversion", "churn", "satisfaction"],
                    "visualizations": ["chart", "table", "heatmap", "timeline"],
                },
            },
            {
                "name": "communication",
                "description": "Communication and messaging",
                "entities": ["email", "sms", "notification", "template", "campaign"],
                "ontology": {
                    "channels": ["email", "sms", "push", "in-app"],
                    "actions": ["send", "schedule", "track", "analyze"],
                },
            },
        ]

        for config in domains_config:
            domain = self.node_service.create_node(NodeType.DOMAIN, config)
            self.domains.append(domain)
            print(f"  ✅ Created domain: {domain.name}")

    def _generate_templates(self):
        """Generate template nodes"""
        print("\nCreating templates...")

        templates_config = [
            # Customer Service Templates
            {
                "name": "Create Support Ticket",
                "description": "Create a new customer support ticket",
                "category": "customer_service",
                "domain": "customer_service",
                "schema": {
                    "input": {
                        "title": {"type": "string", "required": True},
                        "description": {"type": "string", "required": True},
                        "priority": {
                            "type": "string",
                            "enum": ["low", "medium", "high"],
                        },
                        "category": {"type": "string"},
                    },
                    "output": {
                        "ticket_id": {"type": "string"},
                        "status": {"type": "string"},
                    },
                },
                "required_parameters": ["title", "description"],
                "optional_parameters": ["priority", "category"],
                "examples": [
                    {
                        "title": "Login issue",
                        "description": "Cannot login to account",
                        "priority": "high",
                    }
                ],
            },
            # Sales Templates
            {
                "name": "Create Lead",
                "description": "Create a new sales lead",
                "category": "sales",
                "domain": "sales",
                "schema": {
                    "input": {
                        "name": {"type": "string", "required": True},
                        "company": {"type": "string"},
                        "email": {"type": "string", "required": True},
                        "phone": {"type": "string"},
                        "source": {"type": "string"},
                    },
                    "output": {
                        "lead_id": {"type": "string"},
                        "score": {"type": "number"},
                    },
                },
                "required_parameters": ["name", "email"],
                "optional_parameters": ["company", "phone", "source"],
            },
            # Analytics Templates
            {
                "name": "Generate Revenue Report",
                "description": "Generate revenue analytics report",
                "category": "analytics",
                "domain": "analytics",
                "schema": {
                    "input": {
                        "start_date": {"type": "string", "format": "date"},
                        "end_date": {"type": "string", "format": "date"},
                        "group_by": {
                            "type": "string",
                            "enum": ["day", "week", "month"],
                        },
                        "segments": {"type": "array"},
                    },
                    "output": {
                        "report_url": {"type": "string"},
                        "summary": {"type": "object"},
                    },
                },
                "required_parameters": ["start_date", "end_date"],
                "optional_parameters": ["group_by", "segments"],
            },
            # Communication Templates
            {
                "name": "Send Email",
                "description": "Send an email message",
                "category": "communication",
                "domain": "communication",
                "schema": {
                    "input": {
                        "to": {"type": "array", "required": True},
                        "subject": {"type": "string", "required": True},
                        "body": {"type": "string", "required": True},
                        "cc": {"type": "array"},
                        "attachments": {"type": "array"},
                    },
                    "output": {
                        "message_id": {"type": "string"},
                        "status": {"type": "string"},
                    },
                },
                "required_parameters": ["to", "subject", "body"],
                "optional_parameters": ["cc", "attachments"],
            },
        ]

        # Create global templates
        for config in templates_config:
            template = self.node_service.create_node(NodeType.TEMPLATE, config)
            self.templates.append(template)

        # Create tenant-specific templates
        for tenant in self.tenants[:2]:  # First two tenants get custom templates
            custom_template = self.node_service.create_node(
                NodeType.TEMPLATE,
                {
                    "name": f"Custom Workflow - {tenant.name}",
                    "description": f"Custom workflow template for {tenant.name}",
                    "category": "custom",
                    "tenant_id": tenant.id,
                    "schema": {
                        "input": {"data": {"type": "object"}},
                        "output": {"result": {"type": "object"}},
                    },
                    "required_parameters": ["data"],
                },
            )
            self.templates.append(custom_template)

        print(f"  ✅ Created {len(self.templates)} templates")

    def _generate_integrations(self):
        """Generate integration nodes"""
        print("\nCreating integrations...")

        integrations_config = [
            {
                "name": "Salesforce CRM",
                "type": "rest_api",
                "base_url": "https://api.salesforce.com",
                "auth_type": "oauth2",
                "tenant_id": self.tenants[0].id,
                "endpoints": [
                    {"path": "/leads", "method": "POST", "description": "Create lead"},
                    {
                        "path": "/opportunities",
                        "method": "GET",
                        "description": "List opportunities",
                    },
                ],
                "rate_limits": {"requests_per_minute": 100},
            },
            {
                "name": "SendGrid Email",
                "type": "rest_api",
                "base_url": "https://api.sendgrid.com/v3",
                "auth_type": "api_key",
                "tenant_id": self.tenants[0].id,
                "endpoints": [
                    {
                        "path": "/mail/send",
                        "method": "POST",
                        "description": "Send email",
                    }
                ],
                "rate_limits": {"requests_per_minute": 600},
            },
            {
                "name": "Stripe Payments",
                "type": "rest_api",
                "base_url": "https://api.stripe.com/v1",
                "auth_type": "api_key",
                "tenant_id": self.tenants[2].id,
                "endpoints": [
                    {
                        "path": "/charges",
                        "method": "POST",
                        "description": "Create charge",
                    },
                    {
                        "path": "/customers",
                        "method": "POST",
                        "description": "Create customer",
                    },
                ],
                "rate_limits": {"requests_per_minute": 100},
            },
        ]

        for config in integrations_config:
            integration = self.node_service.create_node(NodeType.INTEGRATION, config)
            self.integrations.append(integration)

        print(f"  ✅ Created {len(self.integrations)} integrations")

    def _generate_sessions(self):
        """Generate session nodes"""
        print("\nCreating sessions...")

        for user in self.users:
            # Each user gets 1-3 sessions
            num_sessions = random.randint(1, 3)
            for i in range(num_sessions):
                session_start = datetime.now(timezone.utc) - timedelta(
                    days=random.randint(0, 30), hours=random.randint(0, 23)
                )

                session = self.node_service.create_node(
                    NodeType.SESSION,
                    {
                        "user_id": user.id,
                        "tenant_id": user.tenant_id,
                        "started_at": session_start,
                        "ended_at": session_start
                        + timedelta(minutes=random.randint(5, 120))
                        if i > 0
                        else None,
                        "context": {
                            "device": random.choice(["desktop", "mobile", "tablet"]),
                            "browser": random.choice(
                                ["chrome", "firefox", "safari", "edge"]
                            ),
                            "location": random.choice(["US", "UK", "CA", "AU"]),
                        },
                        "is_active": i == 0,  # Most recent session is active
                    },
                )
                self.sessions.append(session)

                # Create relationship
                self.rel_service.user_creates_session(
                    user.id, session.id, {"ip": f"192.168.1.{random.randint(1, 255)}"}
                )

        print(f"  ✅ Created {len(self.sessions)} sessions")

    def _generate_intents(self):
        """Generate intent nodes"""
        print("\nCreating intents...")

        intent_examples = [
            (
                "I need to create a support ticket for login issues",
                "customer_service",
                0.92,
            ),
            ("Show me revenue for last quarter", "analytics", 0.88),
            ("Create a new lead for John Smith at Acme Corp", "sales", 0.95),
            (
                "Send an email to the marketing team about the campaign",
                "communication",
                0.90,
            ),
            ("What's the status of ticket #12345?", "customer_service", 0.87),
            ("Generate a conversion funnel report", "analytics", 0.91),
            ("Update opportunity stage to closed won", "sales", 0.93),
            ("Schedule email campaign for next Monday", "communication", 0.89),
        ]

        for session in self.sessions[:10]:  # First 10 sessions get intents
            num_intents = random.randint(1, 3)
            for _ in range(num_intents):
                content, domain, confidence = random.choice(intent_examples)

                # Find matching user
                user = next(u for u in self.users if u.id == session.user_id)

                intent = self.node_service.create_intent(
                    content=content,
                    domain=domain,
                    user_id=user.id,
                    session_id=session.id,
                    tenant_id=session.tenant_id,
                    confidence=confidence + random.uniform(-0.05, 0.05),
                    parameters={"extracted": True},
                )
                self.intents.append(intent)

        print(f"  ✅ Created {len(self.intents)} intents")

    def _generate_workflows(self):
        """Generate workflow nodes"""
        print("\nCreating workflows...")

        for intent in self.intents:
            # Find matching template
            matching_templates = [
                t for t in self.templates if t.domain == intent.domain
            ]
            if not matching_templates:
                continue

            template = random.choice(matching_templates)

            # Create workflow
            workflow = self.node_service.create_workflow(
                name=f"Execute: {template.name}",
                intent_id=intent.id,
                template_id=template.id,
                user_id=intent.user_id,
                session_id=intent.session_id,
                tenant_id=intent.tenant_id,
            )

            # Store workflow before update
            self.workflows.append(workflow)

            # Simulate execution
            status = random.choice(["completed", "completed", "completed", "failed"])
            self.node_service.update_node(
                NodeType.WORKFLOW,
                workflow.id,
                {
                    "status": status,
                    "started_at": datetime.now(timezone.utc) - timedelta(minutes=5),
                    "completed_at": datetime.now(timezone.utc) - timedelta(minutes=4),
                    "duration_ms": random.randint(100, 5000),
                    "result": {"success": True} if status == "completed" else None,
                    "error": "API timeout" if status == "failed" else None,
                },
            )

            # Create relationships
            self.rel_service.intent_triggers_workflow(
                intent.id, workflow.id, intent.confidence
            )
            self.rel_service.intent_uses_template(intent.id, template.id, 0.85)

        print(f"  ✅ Created {len(self.workflows)} workflows")

    def _generate_patterns(self):
        """Generate pattern nodes"""
        print("\nCreating patterns...")

        pattern_types = [
            ("High Success Rate - Email Sends", "frequency", "communication"),
            ("Support Ticket Escalation Pattern", "sequence", "customer_service"),
            ("Revenue Report Generation Peak Hours", "temporal", "analytics"),
            ("Lead Conversion Optimization", "optimization", "sales"),
        ]

        for tenant in self.tenants:
            for name, ptype, domain in pattern_types:
                pattern = self.node_service.create_node(
                    NodeType.PATTERN,
                    {
                        "name": name,
                        "type": ptype,
                        "confidence": random.uniform(0.7, 0.95),
                        "support": random.randint(10, 100),
                        "tenant_id": tenant.id,
                        "domain": domain,
                        "conditions": {"min_occurrences": 5, "time_window": "7d"},
                        "last_used": datetime.now(timezone.utc)
                        - timedelta(hours=random.randint(1, 48)),
                    },
                )
                self.patterns.append(pattern)

        print(f"  ✅ Created {len(self.patterns)} patterns")

    def _generate_goals(self):
        """Generate goal nodes"""
        print("\nCreating goals...")

        goals_config = [
            ("Reduce Average Response Time", "efficiency", 200.0, 500.0),
            ("Increase Customer Satisfaction", "user_satisfaction", 4.5, 3.8),
            ("Optimize API Usage", "cost_reduction", 10000, 15000),
            ("Improve Intent Recognition Accuracy", "accuracy", 0.95, 0.85),
        ]

        for tenant in self.tenants:
            for name, gtype, target, current in goals_config:
                goal = self.node_service.create_node(
                    NodeType.GOAL,
                    {
                        "name": name,
                        "description": f"Goal to {name.lower()} for {tenant.name}",
                        "type": gtype,
                        "tenant_id": tenant.id,
                        "target_value": target,
                        "current_value": current + random.uniform(-0.1, 0.1) * current,
                        "priority": random.randint(5, 9),
                        "constraints": {"budget": "within_limits"},
                    },
                )
                self.goals.append(goal)

        print(f"  ✅ Created {len(self.goals)} goals")

    def _generate_metrics(self):
        """Generate metric nodes"""
        print("\nCreating metrics...")

        # Workflow metrics
        for workflow in self.workflows[:20]:  # First 20 workflows
            if workflow.duration_ms:
                metric = self.node_service.record_metric(
                    name="workflow_duration",
                    value=float(workflow.duration_ms),
                    source_id=workflow.id,
                    source_type="workflow",
                    tenant_id=workflow.tenant_id,
                    unit="ms",
                    dimensions={"status": workflow.status},
                )
                self.metrics.append(metric)

        # Pattern metrics
        for pattern in self.patterns[:10]:
            metric = self.node_service.record_metric(
                name="pattern_confidence",
                value=pattern.confidence,
                source_id=pattern.id,
                source_type="pattern",
                tenant_id=pattern.tenant_id,
                dimensions={"type": pattern.type},
            )
            self.metrics.append(metric)

        print(f"  ✅ Created {len(self.metrics)} metrics")

    def _generate_insights(self):
        """Generate insight nodes"""
        print("\nCreating insights...")

        insights_config = [
            {
                "title": "Peak Usage Hours Identified",
                "description": "System usage peaks between 2-4 PM EST",
                "type": "trend",
                "confidence": 0.92,
                "impact": "medium",
                "recommendations": [
                    {"action": "Scale resources during peak hours"},
                    {"action": "Implement request queuing"},
                ],
            },
            {
                "title": "Email Campaign Performance Optimization",
                "description": "Tuesday morning emails have 35% higher open rates",
                "type": "optimization",
                "confidence": 0.88,
                "impact": "high",
                "recommendations": [
                    {"action": "Schedule campaigns for Tuesday mornings"},
                    {"action": "A/B test subject lines on high-performing days"},
                ],
            },
        ]

        for tenant in self.tenants[:2]:  # First two tenants
            for config in insights_config:
                insight_data = config.copy()
                insight_data["tenant_id"] = tenant.id
                insight_data["source_metrics"] = [m.id for m in self.metrics[:3]]

                self.node_service.create_node(NodeType.INSIGHT, insight_data)

        print("  ✅ Created insights")

    def _generate_relationships(self):
        """Generate additional relationships between entities"""
        print("\nCreating relationships...")

        # Organizations belong to tenants
        for org in self.organizations:
            self.rel_service.entity_belongs_to_tenant(
                NodeType.ORGANIZATION, org.id, org.tenant_id, "member"
            )

        # Users belong to tenants
        for user in self.users:
            self.rel_service.entity_belongs_to_tenant(
                NodeType.USER, user.id, user.tenant_id, "user"
            )

        # Some workflows achieve goals
        for i, workflow in enumerate(self.workflows[:10]):
            if workflow.status == "completed":
                goal = self.goals[i % len(self.goals)]
                if goal.tenant_id == workflow.tenant_id:
                    self.rel_service.workflow_achieves_goal(
                        workflow.id, goal.id, random.uniform(0.1, 0.5)
                    )

        # Metrics measure workflows
        for metric in self.metrics:
            if metric.source_type == "workflow":
                self.rel_service.metric_measures_entity(
                    metric.id, NodeType.WORKFLOW, metric.source_id
                )

        # Patterns learned from workflows
        for pattern in self.patterns[:5]:
            matching_workflows = [
                w for w in self.workflows if w.tenant_id == pattern.tenant_id
            ]
            if matching_workflows:
                workflow = random.choice(matching_workflows)
                self.rel_service.pattern_learned_from_workflow(
                    pattern.id, workflow.id, random.uniform(0.1, 0.3)
                )

        print("  ✅ Created relationships")


def main():
    """Run the seed data generator"""
    print("🚀 Coherence KG Seed Data Generator")
    print("=" * 50)

    try:
        # Initialize KG
        kg = CoherenceKG(host="localhost", port=6381)

        # Create generator
        generator = SeedDataGenerator(kg)

        # Generate all data
        generator.generate_all()

        print("\n✅ Seed data generation complete!")

    except Exception as e:
        print(f"\n❌ Error generating seed data: {e}")
        raise
    finally:
        kg.close()


if __name__ == "__main__":
    main()
