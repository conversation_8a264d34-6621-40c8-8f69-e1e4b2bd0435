# Auto detect text files and perform LF normalization
* text=auto eol=lf

# Python files
*.py text diff=python

# JavaScript/TypeScript files
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf

# Shell scripts
*.sh text eol=lf
*.bash text eol=lf

# Documentation
*.md text diff=markdown
*.mdx text diff=markdown
*.txt text
*.rst text

# HTML/CSS
*.html text diff=html
*.css text diff=css
*.scss text diff=css

# Docker
Dockerfile text
*.dockerfile text
docker-compose.yml text
docker-compose.*.yml text

# SQL
*.sql text

# Config files
*.yml text
*.yaml text
*.toml text
*.ini text
*.conf text

# Binary files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg text
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary

# Excel files
*.xlsx binary
*.xls binary

# PDF files
*.pdf binary

# Specific files that should not be modified
alembic/versions/* -text