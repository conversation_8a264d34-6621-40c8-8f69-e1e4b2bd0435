#!/bin/bash
# Script to generate TypeScript SDK from OpenAPI spec

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"
OPENAPI_SPEC_URL=${1:-"http://localhost:8001/openapi.json"}
OUTPUT_DIR="$ROOT_DIR/typescript"

echo "Generating TypeScript SDK from $OPENAPI_SPEC_URL"

# Ensure orval is installed
if ! command -v orval &> /dev/null; then
    echo "orval not found, installing globally..."
    npm install -g orval
fi

# Create TypeScript SDK directory
mkdir -p "$OUTPUT_DIR"

# Create orval configuration
cat > "$ROOT_DIR/orval.config.js" << EOL
module.exports = {
  coherence: {
    input: {
      target: '${OPENAPI_SPEC_URL}',
    },
    output: {
      mode: 'split',
      target: '${OUTPUT_DIR}/src/api',
      client: 'axios',
      schemas: '${OUTPUT_DIR}/src/models',
      mock: false,
      override: {
        mutator: {
          path: '${OUTPUT_DIR}/src/api/mutator.ts',
          name: 'customInstance',
        },
      },
    },
  },
};
EOL

# Create package.json for TypeScript SDK
cat > "$OUTPUT_DIR/package.json" << EOL
{
  "name": "@coherence/sdk",
  "version": "2025.5.0",
  "description": "TypeScript SDK for Coherence AI middleware",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "generate": "orval --config ../orval.config.js",
    "prepublishOnly": "npm run build"
  },
  "author": "Coherence Team <<EMAIL>>",
  "license": "MIT",
  "dependencies": {
    "axios": "^1.3.0"
  },
  "devDependencies": {
    "typescript": "^4.9.5",
    "orval": "^6.17.0",
    "@types/node": "^18.15.0"
  }
}
EOL

# Create TypeScript configuration
cat > "$OUTPUT_DIR/tsconfig.json" << EOL
{
  "compilerOptions": {
    "target": "es2018",
    "module": "commonjs",
    "declaration": true,
    "outDir": "./dist",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
EOL

# Create the mutator file for Axios customization
mkdir -p "$OUTPUT_DIR/src/api"
cat > "$OUTPUT_DIR/src/api/mutator.ts" << EOL
import axios, { AxiosRequestConfig } from 'axios';

export interface ClientOptions {
  apiKey: string;
  tenantId: string;
  baseURL?: string;
  timeout?: number;
}

export const customInstance = (options: ClientOptions) => {
  const instance = axios.create({
    baseURL: options.baseURL || 'https://api.coherence.ai',
    timeout: options.timeout || 30000,
    headers: {
      'Authorization': \`Bearer \${options.apiKey}\`,
      'X-Tenant-ID': options.tenantId,
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'coherence-sdk-typescript/2025.05'
    }
  });

  return {
    instance,
    request: <T>(config: AxiosRequestConfig): Promise<T> => {
      return instance.request(config).then((response) => response.data);
    }
  };
};
EOL

# Create the SDK entry point
mkdir -p "$OUTPUT_DIR/src"
cat > "$OUTPUT_DIR/src/index.ts" << EOL
import { ClientOptions, customInstance } from './api/mutator';

// Export all generated API functions
export * from './api';
export * from './models';

export class CoherenceClient {
  private options: ClientOptions;

  constructor(options: ClientOptions) {
    this.options = {
      baseURL: options.baseURL || 'https://api.coherence.ai',
      apiKey: options.apiKey,
      tenantId: options.tenantId,
      timeout: options.timeout || 30000
    };
  }

  getInstance() {
    return customInstance(this.options);
  }
}

export { ClientOptions };
EOL

# Create README.md for TypeScript SDK
cat > "$OUTPUT_DIR/README.md" << EOL
# Coherence TypeScript SDK

The official TypeScript client for [Coherence](https://github.com/coherence-ai/coherence), an AI middleware that transforms natural language into actionable API calls.

## Installation

```bash
npm install @coherence/sdk
# or
yarn add @coherence/sdk
```

## Quick Start

```typescript
import { CoherenceClient, resolveResolve } from '@coherence/sdk';

// Initialize the client with your API key and tenant ID
const client = new CoherenceClient({
  apiKey: 'your_api_key',
  tenantId: 'your_tenant_id',
  baseURL: 'https://api.coherence.ai'
});

// Resolve natural language to an intent
async function main() {
  try {
    const response = await resolveResolve(
      {
        message: "What's the weather like in San Francisco?",
        conversation_id: "my-conversation-id"
      }, 
      client.getInstance()
    );
    
    console.log(\`Intent: \${response.intent}\`);
    console.log(\`Parameters: \${JSON.stringify(response.parameters)}\`);
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
```

## Features

- **Intent Resolution**: Convert natural language to structured intents
- **Conversation Management**: Maintain context across multiple messages
- **Parameter Extraction**: Automatically extract parameters from messages
- **Multi-Tenant Support**: Isolate data between different tenants
- **Strong Type Safety**: Full TypeScript typing support

## Documentation

For full documentation, visit [docs.coherence.ai](https://docs.coherence.ai).

## License

This SDK is licensed under the MIT License - see the LICENSE file for details.
EOL

# Create directory for examples
mkdir -p "$OUTPUT_DIR/examples"
cat > "$OUTPUT_DIR/examples/basic-usage.ts" << EOL
import { CoherenceClient, resolveResolve, continuePost } from '@coherence/sdk';

// Initialize the client with your API key and tenant ID
const client = new CoherenceClient({
  apiKey: process.env.COHERENCE_API_KEY || 'your_api_key',
  tenantId: process.env.COHERENCE_TENANT_ID || 'your_tenant_id',
  baseURL: process.env.COHERENCE_API_URL || 'https://api.coherence.ai'
});

async function main() {
  try {
    // Resolve natural language to an intent
    const response = await resolveResolve(
      {
        message: "What's the weather like in San Francisco?",
        conversation_id: "my-conversation-id"
      }, 
      client.getInstance()
    );
    
    console.log(\`Intent: \${response.intent}\`);
    console.log(\`Parameters: \${JSON.stringify(response.parameters)}\`);
    
    // Continue the conversation
    const followUp = await continuePost(
      {
        message: "How about tomorrow?",
        conversation_id: "my-conversation-id"
      },
      client.getInstance()
    );
    
    console.log(\`Follow-up intent: \${followUp.intent}\`);
    console.log(\`Updated parameters: \${JSON.stringify(followUp.parameters)}\`);
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
EOL

chmod +x "$SCRIPT_DIR/generate_typescript_sdk.sh"

echo "TypeScript SDK structure created at $OUTPUT_DIR"
echo "To generate the SDK from OpenAPI spec, run: cd $OUTPUT_DIR && npm install && npm run generate && npm run build"