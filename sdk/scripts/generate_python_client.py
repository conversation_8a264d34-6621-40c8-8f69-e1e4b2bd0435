#!/usr/bin/env python3
"""
Script to generate Python client from OpenAPI spec.

This script uses openapi-python-client to generate a Python client
from the Coherence OpenAPI specification, and then copies the generated
code into the coherence_sdk package.
"""

import argparse
import os
import shutil
import subprocess
import sys
import tempfile

# Set up argument parser
parser = argparse.ArgumentParser(description="Generate Python client from OpenAPI spec")
parser.add_argument(
    "--url",
    default="http://localhost:8001/openapi.json",
    help="URL to the OpenAPI spec (default: http://localhost:8001/openapi.json)",
)
parser.add_argument(
    "--output-dir",
    default=None,
    help="Output directory for the generated code (default: sdk/coherence_sdk/api/generated)",
)

# Parse arguments
args = parser.parse_args()

# Set up paths
script_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(script_dir)
default_output_dir = os.path.join(root_dir, "coherence_sdk", "api", "generated")
output_dir = args.output_dir or default_output_dir

# Check if openapi-python-client is installed
try:
    subprocess.check_call(
        [sys.executable, "-m", "pip", "show", "openapi-python-client"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )
except subprocess.CalledProcessError:
    print("Installing openapi-python-client...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "openapi-python-client"])

# Create a temporary directory for the generated code
with tempfile.TemporaryDirectory() as tmpdir:
    print(f"Generating Python client from {args.url}...")
    
    # Generate the client
    generate_cmd = [
        sys.executable,
        "-m",
        "openapi_python_client",
        "generate",
        "--url",
        args.url,
        "--output",
        tmpdir,
    ]
    subprocess.check_call(generate_cmd)
    
    # Determine the name of the generated package (should be coherence-client)
    generated_dirs = [d for d in os.listdir(tmpdir) if os.path.isdir(os.path.join(tmpdir, d))]
    if not generated_dirs:
        print("Error: No directories were generated")
        sys.exit(1)
    
    generated_dir = os.path.join(tmpdir, generated_dirs[0])
    src_dir = os.path.join(generated_dir, "coherence_client")
    
    # Ensure the output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Copy generated models and api modules to the output directory
    print(f"Copying generated code to {output_dir}...")
    
    # Create __init__.py files
    with open(os.path.join(output_dir, "__init__.py"), "w") as f:
        f.write(
            """\"\"\"
Generated API client for Coherence.

This module is auto-generated from the OpenAPI specification
and should not be edited directly.
\"\"\"

from coherence_sdk.api.generated.models import *
from coherence_sdk.api.generated.api import *
"""
        )
    
    # Copy models directory
    models_dir = os.path.join(src_dir, "models")
    dest_models_dir = os.path.join(output_dir, "models")
    if os.path.exists(dest_models_dir):
        shutil.rmtree(dest_models_dir)
    shutil.copytree(models_dir, dest_models_dir)
    
    # Copy api directory
    api_dir = os.path.join(src_dir, "api")
    dest_api_dir = os.path.join(output_dir, "api")
    if os.path.exists(dest_api_dir):
        shutil.rmtree(dest_api_dir)
    shutil.copytree(api_dir, dest_api_dir)
    
    # Remove the client.py file from the source - we'll use our own
    client_file = os.path.join(output_dir, "client.py")
    if os.path.exists(client_file):
        os.remove(client_file)
    
    print("Python client generation complete!")
    print(f"Generated code is available in {output_dir}")

    # Add a note about importing the generated code
    print("""
To use the generated client in your code, add the following import:

from coherence_sdk.api.generated import models, api

Example:
    from coherence_sdk.api.generated.api.resolve import resolve_resolve
    from coherence_sdk.api.generated.models.resolve_request import ResolveRequest
    
    client = CoherenceClient(...)
    response = await resolve_resolve(client=client.get_client(), json_body=ResolveRequest(message="Hello"))
""")

# Make the script executable
os.chmod(os.path.join(script_dir, "generate_python_client.py"), 0o755)