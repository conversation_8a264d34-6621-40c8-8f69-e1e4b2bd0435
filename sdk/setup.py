#!/usr/bin/env python3
"""
Setup script for the Coherence SDK.

This script is mainly used for compatibility with setuptools and pip.
The primary build system is poetry, defined in pyproject.toml.
"""

import os
import re

from setuptools import find_packages, setup

# Read the version from __init__.py
init_path = os.path.join(
    os.path.dirname(__file__), "coherence_sdk", "__init__.py"
)
version_re = r'^__version__\s*=\s*[\'"]([^\'"]*)[\'"]'

with open(init_path, "r") as f:
    match = re.search(version_re, f.read(), re.MULTILINE)
    if match:
        version = match.group(1)
    else:
        raise RuntimeError("Cannot find version information")

# Read README.md for the long description
with open("README.md", "r") as f:
    long_description = f.read()

setup(
    name="coherence-sdk",
    version=version,
    description="Python SDK for interacting with the Coherence AI middleware",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Coherence Team",
    author_email="<EMAIL>",
    url="https://github.com/coherence-ai/coherence",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries",
    ],
    python_requires=">=3.10,<3.12",
    install_requires=[
        "httpx>=0.24.1",
        "pydantic>=2.3.0",
        "tenacity>=8.2.3",
        "typing-extensions>=4.5.0",
    ],
)