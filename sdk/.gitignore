# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# TypeScript
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
typescript/dist/
typescript/src/api/
typescript/src/models/
.cache/

# Coverage
.coverage
htmlcov/
coverage/

# Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Jupyter
.ipynb_checkpoints

# Generated code
coherence_sdk/api/generated

# Logs
logs/
*.log