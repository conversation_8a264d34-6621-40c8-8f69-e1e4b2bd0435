"""
API models for the Coherence SDK.

These Pydantic models represent the request and response structures
for interacting with the Coherence API.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class IntentResolutionResponse(BaseModel):
    """Response from intent resolution API."""

    intent: Optional[str] = Field(None, description="The resolved intent name")
    confidence: float = Field(..., description="Confidence score (0.0-1.0)")
    parameters: Dict[str, Any] = Field(
        default_factory=dict, description="Extracted parameters"
    )
    missing_parameters: List[str] = Field(
        default_factory=list, description="Parameters still needed"
    )
    alternatives: Optional[List[Dict[str, Any]]] = Field(
        None, description="Alternative intent matches"
    )
    conversation_id: Optional[str] = Field(
        None, description="ID for continuing the conversation"
    )
    workflow_id: Optional[str] = Field(
        None, description="ID for checking async workflow status"
    )


class ConversationStatus(str, Enum):
    """Status of a conversation."""

    ACTIVE = "active"
    COMPLETED = "completed"
    WAITING = "waiting"
    FAILED = "failed"


class ConversationResponse(BaseModel):
    """Response from conversation continuation API."""

    conversation_id: str = Field(..., description="Conversation identifier")
    status: ConversationStatus = Field(..., description="Conversation status")
    intent: Optional[str] = Field(None, description="Current resolved intent")
    parameters: Dict[str, Any] = Field(
        default_factory=dict, description="Current parameters"
    )
    missing_parameters: List[str] = Field(
        default_factory=list, description="Parameters still needed"
    )
    message: Optional[str] = Field(None, description="Response message")
    next_action: Optional[str] = Field(None, description="Next action to take")
    workflow_id: Optional[str] = Field(
        None, description="ID for checking async workflow status"
    )


class TemplateType(str, Enum):
    """Type of template."""

    CONVERSATION = "conversation"
    RESPONSE = "response"
    SYSTEM = "system"
    PARAMETER = "parameter"
    CUSTOM = "custom"


class TemplateResponse(BaseModel):
    """Response for template API."""

    id: str = Field(..., description="Template identifier")
    name: str = Field(..., description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    type: TemplateType = Field(..., description="Template type")
    content: str = Field(..., description="Template content")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    version: int = Field(..., description="Template version")
    is_active: bool = Field(..., description="Whether the template is active")
    tags: List[str] = Field(default_factory=list, description="Template tags")


class WorkflowStatus(str, Enum):
    """Status of an async workflow."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class WorkflowStatusResponse(BaseModel):
    """Response for workflow status API."""

    id: str = Field(..., description="Workflow identifier")
    status: WorkflowStatus = Field(..., description="Workflow status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    result: Optional[Dict[str, Any]] = Field(None, description="Workflow result")
    error: Optional[Dict[str, Any]] = Field(None, description="Error details if failed")
    progress: Optional[int] = Field(None, description="Progress percentage")
    eta_seconds: Optional[int] = Field(
        None, description="Estimated time to completion in seconds"
    )