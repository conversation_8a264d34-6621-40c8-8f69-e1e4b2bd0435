"""
Main client for interacting with the Coherence API.
"""

import logging
from typing import Any, Dict, List, Optional

import httpx

from coherence_sdk.api.models import (
    ConversationResponse,
    IntentResolutionResponse,
    TemplateResponse,
)

logger = logging.getLogger(__name__)


class CoherenceClientError(Exception):
    """Exception raised when an error occurs in the Coherence client."""

    def __init__(
        self, message: str, status_code: Optional[int] = None, response: Optional[Any] = None
    ):
        self.message = message
        self.status_code = status_code
        self.response = response
        super().__init__(self.message)


class CoherenceClient:
    """
    Client for interacting with the Coherence API.

    This class provides methods for calling the Coherence API to perform
    natural language processing, intent resolution, and related tasks.
    """

    def __init__(
        self,
        api_key: str,
        tenant_id: str,
        base_url: str = "https://api.coherence.ai",
        timeout: float = 30.0,
    ):
        """
        Initialize the Coherence client.

        Args:
            api_key: API key for authentication
            tenant_id: Tenant ID for multi-tenant isolation
            base_url: Base URL of the Coherence API
            timeout: Request timeout in seconds
        """
        self.api_key = api_key
        self.tenant_id = tenant_id
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.http_client = httpx.AsyncClient(
            timeout=timeout,
            headers={
                "Authorization": f"Bearer {api_key}",
                "X-Tenant-ID": tenant_id,
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "coherence-sdk-python/2025.05",
            },
        )

    async def __aenter__(self) -> "CoherenceClient":
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type: Optional[type], exc_val: Optional[Exception], exc_tb: Optional[Any]) -> None:
        """Async context manager exit."""
        await self.close()

    async def close(self) -> None:
        """Close the HTTP client session."""
        await self.http_client.aclose()

    async def _request(
        self,
        method: str,
        path: str,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Make an HTTP request to the Coherence API.

        Args:
            method: HTTP method (GET, POST, etc.)
            path: API endpoint path
            params: Query parameters
            json_data: JSON request body

        Returns:
            Response data as dictionary

        Raises:
            CoherenceClientError: If the request fails
        """
        url = f"{self.base_url}{path}"
        logger.debug(f"Making {method} request to {url}")

        try:
            response = await self.http_client.request(
                method=method,
                url=url,
                params=params,
                json=json_data,
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            # Try to parse error response
            error_detail = "Unknown error"
            try:
                error_json = e.response.json()
                if "error" in error_json and "message" in error_json["error"]:
                    error_detail = error_json["error"]["message"]
            except Exception:
                error_detail = e.response.text or str(e)

            raise CoherenceClientError(
                message=f"HTTP error: {e.response.status_code} - {error_detail}",
                status_code=e.response.status_code,
                response=e.response,
            ) from e
        except httpx.RequestError as e:
            raise CoherenceClientError(f"Request error: {str(e)}") from e
        except Exception as e:
            raise CoherenceClientError(f"Unexpected error: {str(e)}") from e

    async def resolve(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        user_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> IntentResolutionResponse:
        """
        Resolve a natural language message to an intent.

        Args:
            message: Natural language input from the user
            conversation_id: ID for continuing a conversation
            user_id: ID of the user sending the message
            context: Additional context for intent resolution

        Returns:
            Intent resolution response
        """
        data: Dict[str, Any] = {
            "message": message,
        }

        if conversation_id:
            data["conversation_id"] = conversation_id
        if user_id:
            data["user_id"] = user_id
        if context:
            data["context"] = context

        response = await self._request(
            method="POST",
            path="/v1/resolve",
            json_data=data,
        )

        return IntentResolutionResponse(**response)

    async def continue_conversation(
        self,
        message: str,
        conversation_id: str,
        context: Optional[Dict[str, Any]] = None,
    ) -> ConversationResponse:
        """
        Continue an existing conversation.

        Args:
            message: Natural language input from the user
            conversation_id: ID of the conversation to continue
            context: Additional context for the conversation

        Returns:
            Conversation response
        """
        data: Dict[str, Any] = {
            "message": message,
            "conversation_id": conversation_id,
        }

        if context:
            data["context"] = context

        response = await self._request(
            method="POST",
            path="/v1/continue",
            json_data=data,
        )

        return ConversationResponse(**response)

    async def get_templates(
        self, template_type: Optional[str] = None
    ) -> List[TemplateResponse]:
        """
        Get available templates.

        Args:
            template_type: Optional filter by template type

        Returns:
            List of template responses
        """
        params = {}
        if template_type:
            params["type"] = template_type

        response = await self._request(
            method="GET",
            path="/v1/templates",
            params=params,
        )

        return [TemplateResponse(**template) for template in response["templates"]]

    async def get_status(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get the status of an async workflow.

        Args:
            workflow_id: ID of the workflow to check

        Returns:
            Workflow status
        """
        response = await self._request(
            method="GET",
            path=f"/v1/status/{workflow_id}",
        )

        return response  # This is a Dict[str, Any]