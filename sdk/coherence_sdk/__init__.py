"""
Coherence SDK - Client for Coherence AI middleware.

This SDK provides a Python client for interacting with the Coherence API,
which transforms natural language into actionable API calls.
"""

from coherence_sdk.api import (
    ConversationResponse,
    ConversationStatus,
    IntentResolutionResponse,
    TemplateResponse,
    TemplateType,
    WorkflowStatus,
    WorkflowStatusResponse,
)
from coherence_sdk.client import CoherenceClient, CoherenceClientError

__version__ = "2025.05"  # Using CalVer (YYYY.MM)

__all__ = [
    "CoherenceClient",
    "CoherenceClientError",
    "ConversationResponse",
    "ConversationStatus",
    "IntentResolutionResponse",
    "TemplateResponse",
    "TemplateType",
    "WorkflowStatus",
    "WorkflowStatusResponse",
]