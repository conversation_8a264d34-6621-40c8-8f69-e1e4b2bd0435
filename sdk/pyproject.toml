[tool.poetry]
name = "coherence-sdk"
version = "2025.05"  # Using <PERSON>Ver (YYYY.MM)
description = "Python SDK for interacting with the Coherence AI middleware"
authors = ["Coherence Team <<EMAIL>>"]
readme = "README.md"
repository = "https://github.com/coherence-ai/coherence"
documentation = "https://docs.coherence.ai"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries",
]
packages = [{include = "coherence_sdk"}]

[tool.poetry.dependencies]
python = ">=3.10,<3.12"
httpx = "^0.24.1"
pydantic = "^2.3.0"
tenacity = "^8.2.3"
typing-extensions = "^4.5.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.2"
pytest-asyncio = "^0.21.1"
black = "^23.9.1"
isort = "^5.12.0"
mypy = "^1.5.1"
ruff = "^0.0.290"
pytest-cov = "^4.1.0"
pytest-mock = "^3.14.0"
respx = "^0.20.2"  # For mocking HTTP requests in tests

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ["py310"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3

[tool.mypy]
python_version = "3.10"
warn_redundant_casts = true
warn_unused_ignores = true
disallow_any_generics = true
check_untyped_defs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
python_classes = "Test*"