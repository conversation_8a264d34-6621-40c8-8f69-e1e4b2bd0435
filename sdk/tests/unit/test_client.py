"""Unit tests for the CoherenceClient class."""

import pytest
import respx
from coherence_sdk import CoherenceClient, CoherenceClientError
from coherence_sdk.api import IntentResolutionResponse
from httpx import Response
from pytest_mock import MockerFixture


@pytest.fixture
def client() -> CoherenceClient:
    """Create a test client instance."""
    return CoherenceClient(
        api_key="test_key",
        tenant_id="test_tenant",
        base_url="https://test.coherence.ai",
    )


@respx.mock
async def test_resolve_success(client: CoherenceClient) -> None:
    """Test successful intent resolution."""
    # Mock the response
    respx.post("https://test.coherence.ai/v1/resolve").mock(
        return_value=Response(
            200,
            json={
                "intent": "get_weather",
                "confidence": 0.95,
                "parameters": {"location": "San Francisco"},
                "missing_parameters": [],
                "conversation_id": "test_convo_123",
            },
        )
    )

    # Call the resolve method
    response = await client.resolve(
        message="What's the weather in San Francisco?",
        conversation_id="test_convo_123",
    )

    # Verify the response
    assert isinstance(response, IntentResolutionResponse)
    assert response.intent == "get_weather"
    assert response.confidence == 0.95
    assert response.parameters == {"location": "San Francisco"}
    assert response.missing_parameters == []
    assert response.conversation_id == "test_convo_123"


@respx.mock
async def test_resolve_error(client: CoherenceClient) -> None:
    """Test error handling in intent resolution."""
    # Mock an error response
    respx.post("https://test.coherence.ai/v1/resolve").mock(
        return_value=Response(
            400,
            json={
                "error": {
                    "error_code": "coherence.validation_error",
                    "message": "Invalid input data",
                    "type": "ValidationError",
                }
            },
        )
    )

    # Call the resolve method and expect an exception
    with pytest.raises(CoherenceClientError) as excinfo:
        await client.resolve(message="")

    # Verify the exception details
    assert "400" in str(excinfo.value)
    assert "Invalid input data" in str(excinfo.value)


@respx.mock
async def test_continue_conversation(client: CoherenceClient) -> None:
    """Test continuing a conversation."""
    # Mock the response
    respx.post("https://test.coherence.ai/v1/continue").mock(
        return_value=Response(
            200,
            json={
                "conversation_id": "test_convo_123",
                "status": "active",
                "intent": "get_weather",
                "parameters": {"location": "San Francisco", "date": "tomorrow"},
                "missing_parameters": [],
                "message": "Here's the weather for tomorrow in San Francisco",
            },
        )
    )

    # Call the continue_conversation method
    response = await client.continue_conversation(
        message="What about tomorrow?",
        conversation_id="test_convo_123",
    )

    # Verify the response
    assert response.conversation_id == "test_convo_123"
    assert response.status == "active"
    assert response.intent == "get_weather"
    assert response.parameters == {"location": "San Francisco", "date": "tomorrow"}
    assert response.missing_parameters == []
    assert "tomorrow in San Francisco" in response.message


@respx.mock
async def test_get_templates(client: CoherenceClient) -> None:
    """Test getting templates."""
    # Mock the response
    respx.get("https://test.coherence.ai/v1/templates").mock(
        return_value=Response(
            200,
            json={
                "templates": [
                    {
                        "id": "template1",
                        "name": "Weather Template",
                        "description": "Template for weather intents",
                        "type": "conversation",
                        "content": "Weather content",
                        "created_at": "2025-01-01T00:00:00Z",
                        "updated_at": "2025-01-01T00:00:00Z",
                        "version": 1,
                        "is_active": True,
                        "tags": ["weather"],
                    }
                ]
            },
        )
    )

    # Call the get_templates method
    templates = await client.get_templates()

    # Verify the response
    assert len(templates) == 1
    assert templates[0].id == "template1"
    assert templates[0].name == "Weather Template"
    assert templates[0].type == "conversation"


@respx.mock
async def test_get_status(client: CoherenceClient) -> None:
    """Test getting workflow status."""
    # Mock the response
    respx.get("https://test.coherence.ai/v1/status/workflow123").mock(
        return_value=Response(
            200,
            json={
                "id": "workflow123",
                "status": "completed",
                "result": {"data": "result data"},
            },
        )
    )

    # Call the get_status method
    status = await client.get_status("workflow123")

    # Verify the response
    assert status["id"] == "workflow123"
    assert status["status"] == "completed"
    assert status["result"] == {"data": "result data"}


async def test_context_manager(mocker: MockerFixture) -> None:
    """Test using the client as a context manager."""
    # Create a client with a mocked aclose method
    client = CoherenceClient(
        api_key="test_key",
        tenant_id="test_tenant",
    )
    mocker.patch.object(client.http_client, "aclose")

    # Use the client as a context manager
    async with client as c:
        assert c is client

    # Verify that aclose was called
    client.http_client.aclose.assert_called_once()