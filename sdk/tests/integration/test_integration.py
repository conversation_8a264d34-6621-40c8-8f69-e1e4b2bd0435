"""Integration tests for the Coherence SDK.

These tests require a running Coherence API server. They will be
skipped if the server is not available.
"""

import os
from typing import Async<PERSON>enerator

import pytest
from coherence_sdk import CoherenceClient


@pytest.fixture
def api_url() -> str:
    """Get the API URL from environment or use a default."""
    return os.environ.get("COHERENCE_API_URL", "http://localhost:8001")


@pytest.fixture
def api_key() -> str:
    """Get the API key from environment or use a default."""
    return os.environ.get("COHERENCE_API_KEY", "test_key")


@pytest.fixture
def tenant_id() -> str:
    """Get the tenant ID from environment or use a default."""
    return os.environ.get("COHERENCE_TENANT_ID", "test_tenant")


@pytest.fixture
async def client(api_url: str, api_key: str, tenant_id: str) -> AsyncGenerator[CoherenceClient, None]:
    """Create a client instance for testing."""
    client = CoherenceClient(
        api_key=api_key,
        tenant_id=tenant_id,
        base_url=api_url,
    )
    try:
        yield client
    finally:
        await client.close()


@pytest.mark.integration
@pytest.mark.asyncio
async def test_server_is_running(client: CoherenceClient) -> None:
    """Test that the server is running."""
    # This test checks if the server is running by making a simple request
    # If the server is not running, the test will fail with a connection error
    # This is used as a prerequisite for other integration tests
    try:
        await client._request("GET", "/health", {})
    except Exception as e:
        pytest.skip(f"Server is not running: {e}")


@pytest.mark.integration
@pytest.mark.asyncio
async def test_resolve_integration(client: CoherenceClient) -> None:
    """Test the resolve endpoint with a real server."""
    # First check if the server is running
    try:
        await client._request("GET", "/health", {})
    except Exception as e:
        pytest.skip(f"Server is not running: {e}")

    # Call the resolve method
    response = await client.resolve(
        message="What's the weather in San Francisco?",
    )

    # Verify we got a response (specifics will depend on server configuration)
    assert response is not None
    # The intent might vary depending on the server's templates, but we should have something
    assert response.confidence >= 0.0
    

@pytest.mark.integration
@pytest.mark.asyncio
async def test_continue_conversation_integration(client: CoherenceClient) -> None:
    """Test the continue conversation endpoint with a real server."""
    # First check if the server is running
    try:
        await client._request("GET", "/health", {})
    except Exception as e:
        pytest.skip(f"Server is not running: {e}")

    # First call resolve to start a conversation
    resolve_response = await client.resolve(
        message="What's the weather in San Francisco?",
    )
    
    # Make sure we got a conversation ID
    assert resolve_response.conversation_id is not None

    # Continue the conversation
    continue_response = await client.continue_conversation(
        message="What about tomorrow?",
        conversation_id=resolve_response.conversation_id,
    )

    # Verify we got a response
    assert continue_response is not None
    assert continue_response.conversation_id == resolve_response.conversation_id