name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
    paths:
      - 'sdk/**'
      - '.github/workflows/ci.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'sdk/**'
      - '.github/workflows/ci.yml'

jobs:
  lint:
    name: Lint and Format Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          cache: 'pip'
          
      - name: Install dependencies
        working-directory: ./sdk
        run: |
          python -m pip install --upgrade pip
          pip install poetry
          poetry install
          
      - name: Check formatting with black
        working-directory: ./sdk
        run: poetry run black --check .
        
      - name: Check imports with isort
        working-directory: ./sdk
        run: poetry run isort --check-only .
        
      - name: Lint with ruff
        working-directory: ./sdk
        run: poetry run ruff .
        
      - name: Type check with mypy
        working-directory: ./sdk
        run: poetry run mypy coherence_sdk

  test:
    name: Test with Python ${{ matrix.python-version }}
    runs-on: ubuntu-latest
    needs: lint
    strategy:
      fail-fast: false
      matrix:
        python-version: ['3.10', '3.11']
        
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'
          
      - name: Install dependencies
        working-directory: ./sdk
        run: |
          python -m pip install --upgrade pip
          pip install poetry
          poetry install
          
      - name: Run tests with pytest
        working-directory: ./sdk
        run: poetry run pytest tests/unit --cov=coherence_sdk --cov-report=xml
        
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./sdk/coverage.xml
          fail_ci_if_error: false
          
      - name: Verify coverage is above 90%
        working-directory: ./sdk
        run: |
          COVERAGE=$(poetry run coverage report | grep TOTAL | awk '{print $NF}' | sed 's/%//')
          if (( $(echo "$COVERAGE < 90" | bc -l) )); then
            echo "Coverage is below 90% (actual: $COVERAGE%)"
            exit 1
          fi
          echo "Coverage is $COVERAGE%"

  publish:
    name: Build and Publish
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: Install dependencies
        working-directory: ./sdk
        run: |
          python -m pip install --upgrade pip
          pip install poetry
          poetry install
          
      - name: Build package
        working-directory: ./sdk
        run: poetry build
        
      - name: Publish to TestPyPI
        working-directory: ./sdk
        env:
          TESTPYPI_TOKEN: ${{ secrets.TESTPYPI_TOKEN }}
        run: |
          poetry config repositories.testpypi https://test.pypi.org/legacy/
          poetry config pypi-token.testpypi $TESTPYPI_TOKEN
          poetry publish --repository testpypi

  build-typescript:
    name: Build TypeScript SDK
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        working-directory: ./sdk
        run: |
          if [ -f package.json ]; then
            npm ci
          else
            mkdir -p typescript-sdk
            cd typescript-sdk
            npm init -y
            npm install --save-dev typescript orval @openapitools/openapi-generator-cli
          fi
          
      - name: Generate TypeScript SDK from OpenAPI
        working-directory: ./sdk
        run: |
          bash scripts/generate_typescript_sdk.sh
          
      - name: Build TypeScript SDK
        working-directory: ./sdk/typescript-sdk
        run: |
          if [ -f tsconfig.json ]; then
            npm run build
          else
            echo "Build step skipped - no tsconfig.json found"
          fi