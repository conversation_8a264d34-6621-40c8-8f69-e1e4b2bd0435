# Coherence SDK

The official Python client for [Coherence](https://github.com/coherence-ai/coherence), an AI middleware that transforms natural language into actionable API calls.

## Installation

```bash
pip install coherence-sdk
```

## Quick Start

```python
import asyncio
from coherence_sdk import CoherenceClient

async def main():
    # Initialize the client with your API key and tenant ID
    client = CoherenceClient(
        api_key="your_api_key",
        tenant_id="your_tenant_id",
        base_url="https://api.coherence.ai"
    )
    
    # Resolve natural language to an intent
    response = await client.resolve(
        message="What's the weather like in San Francisco?",
        conversation_id="my-conversation-id"
    )
    
    print(f"Intent: {response.intent}")
    print(f"Parameters: {response.parameters}")
    
    # Continue the conversation
    follow_up = await client.continue_conversation(
        message="How about tomorrow?",
        conversation_id="my-conversation-id"
    )
    
    print(f"Follow-up intent: {follow_up.intent}")
    print(f"Updated parameters: {follow_up.parameters}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Features

- **Intent Resolution**: Convert natural language to structured intents
- **Conversation Management**: Maintain context across multiple messages
- **Parameter Extraction**: Automatically extract parameters from messages
- **Multi-Tenant Support**: Isolate data between different tenants
- **Strong Type Safety**: Full typing support for Python

## Documentation

For full documentation, visit [docs.coherence.ai](https://docs.coherence.ai).

## License

This SDK is licensed under the MIT License - see the LICENSE file for details.