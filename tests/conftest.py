import os
import sys

# Add the project root (parent directory of 'tests') to sys.path
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
# SRC_ROOT = os.path.join(PROJECT_ROOT, 'src') # No longer adding SRC_ROOT directly
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

import pytest  # noqa: E402
from httpx import AsyncClient  # noqa: E402

from src.coherence.db.session import (  # noqa: E402, reverted to src.
    SessionLocal,
    async_session,
)
from src.coherence.main import app  # noqa: E402, reverted to src.

"""
Shared pytest fixtures for the Coherence test suite.

This module contains fixtures that can be used across multiple test files.
"""


@pytest.fixture(scope="function")
def sync_db_session():
    """Create a synchronous database session for testing."""
    # In a real test, you'd use a test database with transactions
    # For now, just return a session instance
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture(scope="function")
async def async_db_session():
    """Create an asynchronous database session for testing."""
    session = async_session()
    try:
        yield session
        await session.commit()
    except Exception:
        await session.rollback()
        raise
    finally:
        await session.close()


@pytest.fixture
async def client():
    """Create an async client for testing the FastAPI application.
    
    This fixture provides an AsyncClient instance configured to work with
    the FastAPI app in the Docker environment.
    """
    # Use base_url that works in Docker environment
    # The service name is used as the hostname in Docker networking
    async with AsyncClient(app=app, base_url="http://coherence-api:8000") as ac:
        yield ac


@pytest.fixture
def mock_openai_response():
    """Create a mock OpenAI response."""
    return {
        "id": "chatcmpl-123456789",
        "object": "chat.completion",
        "created": 1700000000,
        "model": "gpt-4o",
        "usage": {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30},
        "choices": [
            {
                "message": {"role": "assistant", "content": "This is a test response."},
                "finish_reason": "stop",
                "index": 0,
            }
        ],
    }


@pytest.fixture
def mock_openai_embedding_response():
    """Create a mock OpenAI embedding response."""
    return {
        "object": "list",
        "data": [
            {
                "object": "embedding",
                "embedding": [0.1] * 384,  # 384-dimension embedding
                "index": 0,
            }
        ],
        "model": "text-embedding-3-small",
        "usage": {"prompt_tokens": 8, "total_tokens": 8},
    }
