import json
import pathlib

from src.coherence.services.permission_service import CoherencePermission


def test_permissions_sync():
    """
    Test that the generated permissions JSON matches the permissions in the CoherencePermission enum.
    This test will fail if permissions are added to the enum but the JSON is not regenerated.
    """
    # Check if the JSON file exists
    json_path = pathlib.Path('apps/admin/lib/generated/permissions.json')
    
    # If the file doesn't exist, this test is explicitly failing to remind to run the generator
    assert json_path.exists(), (
        f"permissions.json not found at {json_path}. "
        "Run 'python scripts/generate_permissions_ts.py' to generate it."
    )
    
    # Load the permissions from the JSON file
    ts_list = json.loads(json_path.read_text())
    
    # Get the permissions from the enum
    enum_permissions = {p.value for p in CoherencePermission}
    
    # Verify that the JSON contains all the permissions from the enum
    assert set(ts_list) == enum_permissions, (
        "The permissions in TypeScript don't match the Python enum. "
        "Run 'python scripts/generate_permissions_ts.py' to update them."
    )