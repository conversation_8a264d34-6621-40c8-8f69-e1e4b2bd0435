"""Unit tests for the intent pipeline components.

This tests the core functionality of:
- IntentResolver
- ParameterExtractor
- ChatOrchestrator

Most external dependencies are mocked to isolate the tests.
"""

import uuid
from unittest.mock import AsyncMock, Mock, patch

import pytest
from qdrant_client.http.models import ScoredPoint

from src.coherence.core.llm.base import LLMResponse
from src.coherence.intent_pipeline.orchestrator import ChatOrchestrator
from src.coherence.intent_pipeline.parameter_extraction import ParameterExtractor
from src.coherence.intent_pipeline.resolver import IntentResolver
from src.coherence.intent_pipeline.schemas.intent import (
    IntentDefinition,
    IntentResolution,
    ParameterDefinition,
    ParameterType,
)


@pytest.fixture
def mock_llm_provider():
    """Create a mock LLM provider."""
    provider = AsyncMock()
    provider.generate.return_value = LLMResponse(
        content='{"intent": "get_weather", "confidence": 0.95, "filled": {"location": "New York"}, "missing": []}',
    )
    provider.generate_embedding.return_value = [0.1] * 10
    return provider


@pytest.fixture
def mock_qdrant_client():
    """Create a mock Qdrant client."""
    client = AsyncMock()

    # Setup mock search response for vector matching
    mock_point = Mock(spec=ScoredPoint)
    mock_point.score = 0.9
    mock_point.payload = {
        "intent": "get_weather",
        "template_id": str(uuid.uuid4()),
        "parameters": {"location": "string"},
    }

    client.search.return_value = [mock_point]
    client.collection_exists.return_value = True

    return client


@pytest.fixture
def mock_redis_client():
    """Create a mock Redis client."""
    client = AsyncMock()
    client.get.return_value = None
    client.set.return_value = True
    return client


@pytest.fixture
def mock_db_session():
    """Create a mock database session."""
    return Mock()


@pytest.fixture
def sample_intent_definitions():
    """Create sample intent definitions for testing."""
    return [
        IntentDefinition(
            name="get_weather",
            description="Get the weather for a location",
            examples=[
                "What's the weather like in New York?",
                "Show me the forecast for San Francisco",
            ],
            parameters={
                "location": ParameterDefinition(
                    name="location",
                    type=ParameterType.STRING,
                    required=True,
                    description="The location to get weather for",
                    validation_regex=r"in ([\w\s]+)\?"
                ),
                "date": ParameterDefinition(
                    name="date",
                    type=ParameterType.DATE,
                    required=False,
                    description="The date to get weather for",
                ),
            },
            required_fields={"location"},
        ),
        IntentDefinition(
            name="log_health_metric",
            description="Log a health measurement",
            examples=[
                "Log my weight as 150 pounds",
                "Record my blood pressure as 120/80",
            ],
            parameters={
                "metric_type": ParameterDefinition(
                    name="metric_type",
                    type=ParameterType.STRING,
                    required=True,
                    description="Type of health metric",
                ),
                "value": ParameterDefinition(
                    name="value",
                    type=ParameterType.STRING,
                    required=True,
                    description="Value of the measurement",
                ),
            },
            required_fields={"metric_type", "value"},
        ),
    ]


class TestIntentResolver:
    """Tests for the IntentResolver class."""

    @pytest.mark.asyncio
    async def test_resolve_tier1_success(
        self, mock_qdrant_client, mock_llm_provider, sample_intent_definitions
    ):
        """Test successful Tier 1 (vector) intent resolution."""
        # Create the resolver
        resolver = IntentResolver(
            qdrant_client=mock_qdrant_client,
            llm_provider=mock_llm_provider,
            tier1_threshold=0.8,
            tier2_threshold=0.7,
        )

        # Test intent resolution
        resolution = await resolver.resolve(
            tenant_id="test-tenant",
            user_id="test-user",
            user_role="user",
            message="What's the weather like in New York?",
            intents=sample_intent_definitions,
        )

        # Verify the result
        assert resolution.intent == "get_weather"
        assert resolution.confidence >= 0.8
        assert mock_qdrant_client.search.called

    @pytest.mark.asyncio
    async def test_resolve_tier2_fallback(
        self, mock_qdrant_client, mock_llm_provider, sample_intent_definitions
    ):
        """Test fallback to Tier 2 (LLM) intent resolution."""
        # Configure mock to return low confidence for Tier 1
        mock_point = Mock(spec=ScoredPoint)
        mock_point.score = 0.6  # Below threshold
        mock_point.payload = {
            "intent": "get_weather",
            "template_id": str(uuid.uuid4()),
            "parameters": {"location": "string"},
        }
        mock_qdrant_client.search.return_value = [mock_point]

        # Create the resolver
        resolver = IntentResolver(
            qdrant_client=mock_qdrant_client,
            llm_provider=mock_llm_provider,
            tier1_threshold=0.8,
            tier2_threshold=0.7,
        )

        # Test intent resolution
        resolution = await resolver.resolve(
            tenant_id="test-tenant",
            user_id="test-user",
            user_role="user",
            message="What's the weather like in New York?",
            intents=sample_intent_definitions,
        )

        # Verify the result
        assert resolution.intent == "get_weather"  # From LLM response
        assert mock_llm_provider.generate.called


class TestParameterExtractor:
    """Tests for the ParameterExtractor class."""

    @pytest.mark.asyncio
    async def test_extract_parameters_pattern(
        self, mock_llm_provider, sample_intent_definitions
    ):
        """Test parameter extraction using pattern matching."""
        # Create the extractor
        extractor = ParameterExtractor(
            llm_provider=mock_llm_provider,
        )

        # Test parameter extraction
        filled, missing = await extractor.extract_parameters(
            message="What's the weather like in New York?",
            intent_definition=sample_intent_definitions[0],  # get_weather
        )

        # Verify the result - should extract location from pattern
        assert "location" in filled
        assert filled["location"] == "New York"
        assert "date" not in filled  # Optional parameter
        assert "location" not in missing

    @pytest.mark.asyncio
    async def test_extract_parameters_llm(
        self, mock_llm_provider, sample_intent_definitions
    ):
        """Test parameter extraction using LLM for complex cases."""
        # Configure mock LLM to return parameters
        mock_llm_provider.generate.return_value = LLMResponse(
            content='{"metric_type": "weight", "value": "150 pounds"}',
        )

        # Create the extractor
        extractor = ParameterExtractor(
            llm_provider=mock_llm_provider,
        )

        # Test parameter extraction
        filled, missing = await extractor.extract_parameters(
            message="Log my weight as 150 pounds",
            intent_definition=sample_intent_definitions[1],  # log_health_metric
        )

        # Verify the result
        assert "metric_type" in filled
        assert filled["metric_type"] == "weight"
        assert "value" in filled
        assert filled["value"] == "150 pounds"
        assert not missing  # All required parameters are filled


class TestChatOrchestrator:
    """Tests for the ChatOrchestrator class."""

    @pytest.mark.asyncio
    async def test_handle_message_complete_intent(
        self,
        mock_db_session,
        mock_redis_client,
        mock_qdrant_client,
        mock_llm_provider,
        sample_intent_definitions,
    ):
        """Test handling a message with all parameters present."""
        # Create mocked components
        intent_resolver = AsyncMock()
        intent_resolver.resolve.return_value = IntentResolution(
            intent="get_weather",
            confidence=0.95,
            filled={"location": "New York"},
            missing=[],
        )

        parameter_extractor = AsyncMock()
        parameter_extractor.extract_parameters.return_value = (
            {"location": "New York"},
            [],
        )

        # Mock the template service
        template_service = AsyncMock()
        action_executor = AsyncMock()
        
        # Create the orchestrator
        orchestrator = ChatOrchestrator(
            db=mock_db_session,
            redis_client=mock_redis_client,
            intent_resolver=intent_resolver,
            parameter_extractor=parameter_extractor,
            llm_provider=mock_llm_provider,
            template_service=template_service,
            action_executor=action_executor
        )
        
        # Mock the _execute_action method to return success
        with patch.object(
            orchestrator,
            "_execute_action",
            return_value={"type": "action", "outcome": "Weather in New York is sunny."}
        ), patch.object(
            orchestrator,
            "_get_available_intents",
            return_value=sample_intent_definitions,
        ):
            # Test message handling
            result = await orchestrator.handle_message(
                tenant_id="test-tenant",
                user_id="test-user",
                user_role="user",
                message="What's the weather like in New York?",
            )

        # Verify the result
        assert (
            result["type"] == "action"
        )  # Should execute action if all params are present
        assert "outcome" in result
        assert intent_resolver.resolve.called
        assert parameter_extractor.extract_parameters.called

    @pytest.mark.asyncio
    async def test_handle_message_missing_parameter(
        self,
        mock_db_session,
        mock_redis_client,
        mock_qdrant_client,
        mock_llm_provider,
        sample_intent_definitions,
    ):
        """Test handling a message with missing parameters."""
        # Create mocked components
        intent_resolver = AsyncMock()
        intent_resolver.resolve.return_value = IntentResolution(
            intent="log_health_metric",
            confidence=0.95,
            filled={"metric_type": "weight"},
            missing=["value"],
        )

        parameter_extractor = AsyncMock()
        parameter_extractor.extract_parameters.return_value = (
            {"metric_type": "weight"},
            ["value"],
        )
        parameter_extractor.generate_parameter_prompt.return_value = (
            "What is your weight?"
        )

        # Mock the template service
        template_service = AsyncMock()
        action_executor = AsyncMock()
        
        # Create the orchestrator
        orchestrator = ChatOrchestrator(
            db=mock_db_session,
            redis_client=mock_redis_client,
            intent_resolver=intent_resolver,
            parameter_extractor=parameter_extractor,
            llm_provider=mock_llm_provider,
            template_service=template_service,
            action_executor=action_executor
        )

        # Patch the _get_available_intents method
        with patch.object(
            orchestrator,
            "_get_available_intents",
            return_value=sample_intent_definitions,
        ):
            # Test message handling
            result = await orchestrator.handle_message(
                tenant_id="test-tenant",
                user_id="test-user",
                user_role="user",
                message="I want to log my weight",
            )

        # Verify the result
        assert result["type"] == "ask"  # Should ask for missing parameter
        assert result["missing_field"] == "value"
        assert result["question"] == "What is your weight?"
        assert parameter_extractor.generate_parameter_prompt.called
