"""
Tests for conversational parameter extraction using LLM-first approach.

Tests the enhanced parameter extraction that can handle natural language,
ordinal references, and context-aware parameter collection.
"""

import uuid
import json
from unittest.mock import AsyncMock, Mock, patch
import pytest
from datetime import datetime, date, time

from src.coherence.intent_pipeline.parameter_extraction import ParameterExtractor
from src.coherence.intent_pipeline.schemas.intent import (
    IntentDefinition,
    ParameterDefinition,
    ParameterType,
)
from src.coherence.core.llm.base import LLMProvider


class TestConversationalParameterExtraction:
    """Test conversational parameter extraction functionality."""

    @pytest.fixture
    def mock_llm_provider(self):
        """Mock LLM provider."""
        provider = AsyncMock(spec=LLMProvider)
        provider.provider_name = "mock"
        provider.provider_model = "mock-model"
        return provider

    @pytest.fixture
    def parameter_extractor(self, mock_llm_provider):
        """Create parameter extractor with mocked LLM."""
        return ParameterExtractor(llm_provider=mock_llm_provider)

    @pytest.fixture
    def sample_intent_definition(self):
        """Create sample intent definition with various parameter types."""
        return IntentDefinition(
            name="book_appointment",
            description="Book an appointment with a doctor",
            parameters={
                "doctor_id": ParameterDefinition(
                    name="doctor_id",
                    type=ParameterType.STRING,
                    required=True,
                    description="ID of the doctor",
                    prompt="Which doctor would you like to see?",
                ),
                "appointment_date": ParameterDefinition(
                    name="appointment_date",
                    type=ParameterType.DATE,
                    required=True,
                    description="Date for the appointment",
                    prompt="What date would you like the appointment?",
                ),
                "appointment_time": ParameterDefinition(
                    name="appointment_time",
                    type=ParameterType.TIME,
                    required=True,
                    description="Time for the appointment",
                    prompt="What time would you prefer?",
                ),
                "reason": ParameterDefinition(
                    name="reason",
                    type=ParameterType.STRING,
                    required=False,
                    description="Reason for visit",
                    prompt="What is the reason for your visit?",
                ),
            },
            required_fields=["doctor_id", "appointment_date", "appointment_time"],
        )

    @pytest.mark.asyncio
    async def test_conversational_extraction_with_natural_language(
        self, parameter_extractor, mock_llm_provider, sample_intent_definition
    ):
        """Test extracting parameters from natural language."""
        # Mock LLM response with extracted parameters
        mock_llm_response = json.dumps({
            "extracted_parameters": {
                "doctor_id": "dr_smith_123",
                "appointment_date": "2024-12-25",
                "appointment_time": "14:30",
                "reason": "annual checkup"
            },
            "missing_parameters": [],
            "parameter_explanations": {
                "doctor_id": "Extracted from 'Dr. Smith'",
                "appointment_date": "Parsed 'next Tuesday' relative to today",
                "appointment_time": "Converted 'afternoon' to 14:30",
                "reason": "Extracted from natural language"
            }
        })
        
        mock_llm_provider.async_generate.return_value = mock_llm_response

        # Test message with natural language
        message = "I'd like to book an appointment with Dr. Smith next Tuesday afternoon for my annual checkup"
        
        # Extract parameters
        filled_params, missing_params = await parameter_extractor.extract_parameters(
            message=message,
            intent_definition=sample_intent_definition,
            existing_params={}
        )

        # Assert extracted parameters
        assert filled_params["doctor_id"] == "dr_smith_123"
        assert filled_params["appointment_date"] == "2024-12-25"
        assert filled_params["appointment_time"] == "14:30"
        assert filled_params["reason"] == "annual checkup"
        assert len(missing_params) == 0

    @pytest.mark.asyncio
    async def test_ordinal_reference_handling(
        self, parameter_extractor, mock_llm_provider, sample_intent_definition
    ):
        """Test handling ordinal references like 'the first one'."""
        # Mock LLM response for ordinal reference
        mock_llm_response = json.dumps({
            "extracted_parameters": {
                "doctor_id": "dr_jones_456"
            },
            "missing_parameters": ["appointment_date", "appointment_time"],
            "ordinal_resolution": {
                "reference": "second one",
                "resolved_to": "Dr. Jones from the previous list"
            }
        })
        
        mock_llm_provider.async_generate.return_value = mock_llm_response

        # Test with conversation history containing options
        conversation_history = [
            {
                "role": "assistant",
                "content": "I found these doctors:\n1. Dr. Smith\n2. Dr. Jones\n3. Dr. Brown"
            },
            {
                "role": "user",
                "content": "I'll take the second one"
            }
        ]
        
        # Extract parameters with context
        filled_params, missing_params = await parameter_extractor._extract_with_llm_conversational(
            message="I'll take the second one",
            params=sample_intent_definition.parameters,
            intent_description=sample_intent_definition.description,
            conversation_history=conversation_history,
            existing_params={}
        )

        # Assert resolved ordinal reference
        assert filled_params["doctor_id"] == "dr_jones_456"
        assert "appointment_date" in missing_params
        assert "appointment_time" in missing_params

    @pytest.mark.asyncio
    async def test_relative_date_parsing(
        self, parameter_extractor, mock_llm_provider, sample_intent_definition
    ):
        """Test parsing relative dates like 'tomorrow' or 'next Monday'."""
        # Mock LLM response with relative date parsing
        mock_llm_response = json.dumps({
            "extracted_parameters": {
                "appointment_date": "2024-12-26",
                "appointment_time": "09:00"
            },
            "missing_parameters": ["doctor_id"],
            "date_parsing": {
                "original": "tomorrow morning",
                "parsed_date": "2024-12-26",
                "parsed_time": "09:00",
                "reference_date": "2024-12-25"
            }
        })
        
        mock_llm_provider.async_generate.return_value = mock_llm_response

        # Test with relative date
        message = "I'd like an appointment tomorrow morning"
        
        # Extract parameters
        filled_params, missing_params = await parameter_extractor.extract_parameters(
            message=message,
            intent_definition=sample_intent_definition,
            existing_params={}
        )

        # Assert parsed dates
        assert filled_params["appointment_date"] == "2024-12-26"
        assert filled_params["appointment_time"] == "09:00"
        assert "doctor_id" in missing_params

    @pytest.mark.asyncio
    async def test_multi_round_parameter_collection(
        self, parameter_extractor, mock_llm_provider, sample_intent_definition
    ):
        """Test collecting parameters over multiple conversation rounds."""
        # First round - partial extraction
        mock_llm_response_1 = json.dumps({
            "extracted_parameters": {
                "doctor_id": "dr_smith_123"
            },
            "missing_parameters": ["appointment_date", "appointment_time"]
        })
        
        # Second round - more parameters
        mock_llm_response_2 = json.dumps({
            "extracted_parameters": {
                "appointment_date": "2024-12-30"
            },
            "missing_parameters": ["appointment_time"]
        })
        
        # Third round - final parameter
        mock_llm_response_3 = json.dumps({
            "extracted_parameters": {
                "appointment_time": "15:00"
            },
            "missing_parameters": []
        })
        
        # Set up sequential LLM responses
        mock_llm_provider.async_generate.side_effect = [
            mock_llm_response_1,
            mock_llm_response_2,
            mock_llm_response_3
        ]

        # Round 1
        filled_1, missing_1 = await parameter_extractor.extract_parameters(
            message="I'd like to see Dr. Smith",
            intent_definition=sample_intent_definition,
            existing_params={}
        )
        
        assert filled_1["doctor_id"] == "dr_smith_123"
        assert "appointment_date" in missing_1

        # Round 2
        filled_2, missing_2 = await parameter_extractor.extract_parameters(
            message="How about December 30th?",
            intent_definition=sample_intent_definition,
            existing_params=filled_1
        )
        
        assert filled_2["appointment_date"] == "2024-12-30"
        assert filled_2["doctor_id"] == "dr_smith_123"  # Preserved from round 1
        assert "appointment_time" in missing_2

        # Round 3
        filled_3, missing_3 = await parameter_extractor.extract_parameters(
            message="3pm would work",
            intent_definition=sample_intent_definition,
            existing_params=filled_2
        )
        
        assert filled_3["appointment_time"] == "15:00"
        assert filled_3["doctor_id"] == "dr_smith_123"  # Preserved
        assert filled_3["appointment_date"] == "2024-12-30"  # Preserved
        assert len(missing_3) == 0

    @pytest.mark.asyncio
    async def test_parameter_correction(
        self, parameter_extractor, mock_llm_provider, sample_intent_definition
    ):
        """Test correcting previously extracted parameters."""
        # Mock LLM response with parameter correction
        mock_llm_response = json.dumps({
            "extracted_parameters": {
                "appointment_date": "2024-12-31"  # Corrected date
            },
            "missing_parameters": [],
            "corrections": {
                "appointment_date": {
                    "old_value": "2024-12-30",
                    "new_value": "2024-12-31",
                    "reason": "User corrected the date"
                }
            }
        })
        
        mock_llm_provider.async_generate.return_value = mock_llm_response

        # Test with correction
        existing_params = {
            "doctor_id": "dr_smith_123",
            "appointment_date": "2024-12-30",
            "appointment_time": "15:00"
        }
        
        filled_params, missing_params = await parameter_extractor.extract_parameters(
            message="Actually, make that December 31st instead",
            intent_definition=sample_intent_definition,
            existing_params=existing_params
        )

        # Assert corrected parameter
        assert filled_params["appointment_date"] == "2024-12-31"
        assert filled_params["doctor_id"] == "dr_smith_123"  # Unchanged
        assert filled_params["appointment_time"] == "15:00"  # Unchanged
        assert len(missing_params) == 0

    @pytest.mark.asyncio
    async def test_context_aware_prompt_generation(
        self, parameter_extractor, mock_llm_provider
    ):
        """Test generating context-aware prompts for missing parameters."""
        # Mock LLM response for prompt generation
        mock_llm_response = json.dumps({
            "prompt": "I see you want to book with Dr. Smith. What date would work best for your appointment?",
            "context_used": {
                "doctor_name": "Dr. Smith",
                "previous_attempts": 0,
                "conversation_length": 2
            }
        })
        
        mock_llm_provider.async_generate.return_value = mock_llm_response

        # Generate prompt with context
        param_def = ParameterDefinition(
            name="appointment_date",
            type=ParameterType.DATE,
            required=True,
            description="Date for the appointment",
            prompt="What date would you like?",
        )
        
        conversation_history = [
            {"role": "user", "content": "I want to see Dr. Smith"},
            {"role": "assistant", "content": "Sure, I can help you book with Dr. Smith"}
        ]
        
        prompt = await parameter_extractor.generate_parameter_prompt(
            param_name="appointment_date",
            param_def=param_def,
            intent_description="Book a doctor appointment",
            conversation_history=conversation_history
        )

        # Assert context-aware prompt
        assert "Dr. Smith" in prompt
        assert "date" in prompt.lower()

    @pytest.mark.asyncio
    async def test_ambiguous_input_handling(
        self, parameter_extractor, mock_llm_provider, sample_intent_definition
    ):
        """Test handling ambiguous user input."""
        # Mock LLM response with ambiguity detection
        mock_llm_response = json.dumps({
            "extracted_parameters": {},
            "missing_parameters": ["doctor_id", "appointment_date", "appointment_time"],
            "ambiguities": {
                "time_reference": {
                    "input": "later",
                    "possible_interpretations": ["later today", "later this week", "unspecified future"],
                    "clarification_needed": True
                }
            },
            "clarification_prompt": "When you say 'later', do you mean later today or another day?"
        })
        
        mock_llm_provider.async_generate.return_value = mock_llm_response

        # Test with ambiguous input
        filled_params, missing_params = await parameter_extractor.extract_parameters(
            message="I need to see a doctor later",
            intent_definition=sample_intent_definition,
            existing_params={}
        )

        # Assert no parameters extracted due to ambiguity
        assert len(filled_params) == 0
        assert len(missing_params) == 3

    @pytest.mark.asyncio
    async def test_fallback_to_pattern_matching(
        self, parameter_extractor, mock_llm_provider, sample_intent_definition
    ):
        """Test fallback to pattern matching when LLM fails."""
        # Mock LLM failure
        mock_llm_provider.async_generate.side_effect = Exception("LLM service unavailable")

        # Test with clear pattern-matchable input
        message = "Book appointment with dr_smith_123 on 2024-12-25 at 14:30"
        
        # Extract parameters (should fallback to pattern matching)
        filled_params, missing_params = await parameter_extractor.extract_parameters(
            message=message,
            intent_definition=sample_intent_definition,
            existing_params={}
        )

        # Assert pattern matching worked
        assert filled_params["doctor_id"] == "dr_smith_123"
        assert filled_params["appointment_date"] == "2024-12-25"
        assert filled_params["appointment_time"] == "14:30"

    @pytest.mark.asyncio
    async def test_conversation_memory(
        self, parameter_extractor, mock_llm_provider, sample_intent_definition
    ):
        """Test that extractor remembers context from conversation history."""
        # Mock LLM response using conversation memory
        mock_llm_response = json.dumps({
            "extracted_parameters": {
                "doctor_id": "dr_jones_456",
                "reason": "follow-up for back pain"
            },
            "missing_parameters": ["appointment_date", "appointment_time"],
            "context_from_history": {
                "previous_doctor": "Dr. Jones",
                "previous_condition": "back pain",
                "last_appointment": "2024-11-15"
            }
        })
        
        mock_llm_provider.async_generate.return_value = mock_llm_response

        # Test with conversation history
        conversation_history = [
            {"role": "user", "content": "I saw Dr. Jones last month for back pain"},
            {"role": "assistant", "content": "I see you previously saw Dr. Jones"},
            {"role": "user", "content": "Yes, I need a follow-up appointment"}
        ]
        
        filled_params, missing_params = await parameter_extractor._extract_with_llm_conversational(
            message="Yes, I need a follow-up appointment",
            params=sample_intent_definition.parameters,
            intent_description=sample_intent_definition.description,
            conversation_history=conversation_history,
            existing_params={}
        )

        # Assert context was used
        assert filled_params["doctor_id"] == "dr_jones_456"
        assert "back pain" in filled_params["reason"]