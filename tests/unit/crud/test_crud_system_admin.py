"""
Unit tests for system admin CRUD operations.
"""
from datetime import datetime, timedelta

import pytest
from sqlalchemy import select

from src.coherence.crud.crud_system_admin import (
    create_system_admin,
    create_system_admin_api_key,
    get_multi_system_admin_api_key_for_admin,
    get_system_admin,
    get_system_admin_api_key,
    get_system_admin_by_clerk_id,
    remove_system_admin,
    remove_system_admin_api_key,
    update_system_admin_api_key,
)
from src.coherence.models.system_admin import SystemAdmin
from src.coherence.schemas.system_admin import (
    SystemAdminAPIKeyCreate,
    SystemAdminAPIKeyUpdate,
    SystemAdminCreate,
)


@pytest.fixture
async def test_system_admin(db_session):
    """Create a test system admin for testing."""
    obj_in = SystemAdminCreate(
        clerk_user_id="test_clerk_user_123",
        created_by="test_creator_user"
    )
    db_obj = await create_system_admin(db=db_session, obj_in=obj_in)
    yield db_obj
    # Clean up
    await db_session.execute(select(SystemAdmin).where(SystemAdmin.id == db_obj.id))
    await db_session.commit()


@pytest.fixture
async def test_api_key(db_session, test_system_admin):
    """Create a test API key for testing."""
    expires_at = datetime.now() + timedelta(days=30)
    obj_in = SystemAdminAPIKeyCreate(
        name="Test API Key",
        created_by="test_creator_user",
        expires_at=expires_at,
        permissions={"read": True, "write": False}
    )
    db_obj, _ = await create_system_admin_api_key(
        db=db_session,
        obj_in=obj_in,
        system_admin_id=test_system_admin.id
    )
    yield db_obj


class TestSystemAdminCRUD:
    
    async def test_create_system_admin(self, db_session):
        """Test creating a system admin."""
        obj_in = SystemAdminCreate(
            clerk_user_id="test_clerk_user_456",
            created_by="test_creator_user"
        )
        db_obj = await create_system_admin(db=db_session, obj_in=obj_in)
        
        assert db_obj.id is not None
        assert db_obj.clerk_user_id == "test_clerk_user_456"
        assert db_obj.created_by == "test_creator_user"
        assert db_obj.created_at is not None
    
    async def test_get_system_admin(self, db_session, test_system_admin):
        """Test getting a system admin by ID."""
        db_obj = await get_system_admin(db=db_session, id=test_system_admin.id)
        
        assert db_obj is not None
        assert db_obj.id == test_system_admin.id
        assert db_obj.clerk_user_id == test_system_admin.clerk_user_id
    
    async def test_get_system_admin_by_clerk_id(self, db_session, test_system_admin):
        """Test getting a system admin by clerk ID."""
        db_obj = await get_system_admin_by_clerk_id(
            db=db_session,
            clerk_user_id=test_system_admin.clerk_user_id
        )
        
        assert db_obj is not None
        assert db_obj.id == test_system_admin.id
        assert db_obj.clerk_user_id == test_system_admin.clerk_user_id
    
    async def test_remove_system_admin(self, db_session):
        """Test removing a system admin."""
        # Create a system admin to remove
        obj_in = SystemAdminCreate(
            clerk_user_id="test_clerk_user_to_remove",
            created_by="test_creator_user"
        )
        created_admin = await create_system_admin(db=db_session, obj_in=obj_in)
        
        # Remove the admin
        removed_admin = await remove_system_admin(db=db_session, id=created_admin.id)
        
        assert removed_admin is not None
        assert removed_admin.id == created_admin.id
        
        # Verify it's gone
        db_obj = await get_system_admin(db=db_session, id=created_admin.id)
        assert db_obj is None


class TestSystemAdminAPIKeyCRUD:
    
    async def test_create_system_admin_api_key(self, db_session, test_system_admin):
        """Test creating an API key."""
        obj_in = SystemAdminAPIKeyCreate(
            name="New Test API Key",
            created_by="test_creator_user",
            permissions={"admin": True}
        )
        db_obj, raw_key = await create_system_admin_api_key(
            db=db_session,
            obj_in=obj_in,
            system_admin_id=test_system_admin.id
        )
        
        assert db_obj.id is not None
        assert db_obj.name == "New Test API Key"
        assert db_obj.system_admin_id == test_system_admin.id
        assert raw_key is not None and len(raw_key) > 0
        assert db_obj.revoked is False
    
    async def test_get_system_admin_api_key(self, db_session, test_api_key):
        """Test getting an API key by ID."""
        db_obj = await get_system_admin_api_key(db=db_session, id=test_api_key.id)
        
        assert db_obj is not None
        assert db_obj.id == test_api_key.id
        assert db_obj.name == test_api_key.name
        assert db_obj.system_admin_id == test_api_key.system_admin_id
    
    async def test_get_api_keys_for_admin(self, db_session, test_system_admin, test_api_key):
        """Test getting all API keys for a system admin."""
        # Create a second API key
        obj_in = SystemAdminAPIKeyCreate(
            name="Second Test API Key",
            created_by="test_creator_user"
        )
        await create_system_admin_api_key(
            db=db_session,
            obj_in=obj_in,
            system_admin_id=test_system_admin.id
        )
        
        # Get all keys for the admin
        keys = await get_multi_system_admin_api_key_for_admin(
            db=db_session,
            system_admin_id=test_system_admin.id
        )
        
        assert keys is not None
        assert len(keys) == 2
        
        # Verify the system_admin_id is correct on all keys
        for key in keys:
            assert key.system_admin_id == test_system_admin.id
    
    async def test_update_system_admin_api_key(self, db_session, test_api_key):
        """Test updating an API key."""
        # Update the key
        obj_in = SystemAdminAPIKeyUpdate(
            name="Updated API Key Name",
            revoked=True
        )
        updated_key = await update_system_admin_api_key(
            db=db_session,
            db_obj=test_api_key,
            obj_in=obj_in
        )
        
        assert updated_key.name == "Updated API Key Name"
        assert updated_key.revoked is True
        
        # Verify changes were saved to DB
        db_obj = await get_system_admin_api_key(db=db_session, id=test_api_key.id)
        assert db_obj.name == "Updated API Key Name"
        assert db_obj.revoked is True
    
    async def test_remove_system_admin_api_key(self, db_session, test_system_admin):
        """Test removing an API key."""
        # Create an API key to remove
        obj_in = SystemAdminAPIKeyCreate(
            name="API Key to Remove",
            created_by="test_creator_user"
        )
        key_to_remove, _ = await create_system_admin_api_key(
            db=db_session,
            obj_in=obj_in,
            system_admin_id=test_system_admin.id
        )
        
        # Remove the key
        removed_key = await remove_system_admin_api_key(db=db_session, id=key_to_remove.id)
        
        assert removed_key is not None
        assert removed_key.id == key_to_remove.id
        
        # Verify it's gone
        db_obj = await get_system_admin_api_key(db=db_session, id=key_to_remove.id)
        assert db_obj is None
    
    async def test_cascade_delete(self, db_session):
        """Test that deleting a system admin cascades to API keys."""
        # Create a new admin
        obj_in = SystemAdminCreate(
            clerk_user_id="cascade_test_user",
            created_by="test_creator_user"
        )
        admin = await create_system_admin(db=db_session, obj_in=obj_in)
        
        # Create API keys for this admin
        key_in = SystemAdminAPIKeyCreate(
            name="Cascade Test Key",
            created_by="test_creator_user"
        )
        key, _ = await create_system_admin_api_key(
            db=db_session,
            obj_in=key_in,
            system_admin_id=admin.id
        )
        
        # Verify the key exists
        assert await get_system_admin_api_key(db=db_session, id=key.id) is not None
        
        # Delete the admin
        await remove_system_admin(db=db_session, id=admin.id)
        
        # Verify the key was also deleted
        assert await get_system_admin_api_key(db=db_session, id=key.id) is None