"""
Unit tests for template enhancement scripts.

Tests the functionality of:
- enhance_unified_templates.py
- generate_template_test_data.py
- validate_template_structure.py
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime

from scripts.enhance_unified_templates import TemplateEnhancer
from scripts.generate_template_test_data import TestDataGenerator
from scripts.validate_template_structure import TemplateStructureValidator, ValidationResult
from src.coherence.models.template import Template, TemplateCategory


class TestTemplateEnhancer:
    """Tests for TemplateEnhancer class."""
    
    @pytest.fixture
    def enhancer(self):
        return TemplateEnhancer()
    
    @pytest.fixture
    def mock_template(self):
        """Create a mock unified template."""
        template = Mock(spec=Template)
        template.key = "test_template"
        template.category = TemplateCategory.UNIFIED
        template.version = 1
        template.endpoint = "GET_/api/test"
        template.integration = Mock(
            base_url="https://api.example.com",
            credential_name="example_api_key"
        )
        
        # Set up action config
        template.action_config = {
            "action": {
                "method": "GET",
                "path": "/api/test",
                "auth": {"type": "api_key", "header": "X-API-Key"}
            },
            "parameters": {
                "test_param": {
                    "type": "string",
                    "required": True,
                    "minLength": 1,
                    "maxLength": 100,
                    "pattern": "^[A-Za-z]+$"
                },
                "optional_param": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 100
                }
            }
        }
        
        # Set up response format
        template.response_format = {
            "response": {
                "crfs": {
                    "type": "structured",
                    "default_format": "structured"
                }
            }
        }
        
        template.test_data = None
        
        return template
    
    def test_is_already_enhanced_false(self, enhancer, mock_template):
        """Test detection of non-enhanced template."""
        assert not enhancer._is_already_enhanced(mock_template)
    
    def test_is_already_enhanced_true(self, enhancer, mock_template):
        """Test detection of already enhanced template."""
        # Add enhanced markers
        mock_template.action_config["action"]["integration"] = {
            "base_url": "https://api.example.com"
        }
        mock_template.action_config["action"]["validation_rules"] = {}
        mock_template.response_format["response"]["crfs"]["auto_select"] = True
        
        assert enhancer._is_already_enhanced(mock_template)
    
    def test_enhance_action_config(self, enhancer, mock_template):
        """Test action configuration enhancement."""
        action_config = mock_template.action_config.copy()
        enhancer._enhance_action_config(action_config, mock_template)
        
        # Check integration was added
        assert "integration" in action_config["action"]
        integration = action_config["action"]["integration"]
        assert integration["base_url"] == "https://api.example.com"
        assert integration["api_version"] == "v1"
        assert integration["credential_ref"] == "example_api_key"
        
        # Check validation rules were generated
        assert "validation_rules" in action_config["action"]
        rules = action_config["action"]["validation_rules"]
        assert "test_param" in rules
        assert rules["test_param"]["type"] == "string"
        assert rules["test_param"]["min_length"] == 1
        assert rules["test_param"]["max_length"] == 100
        assert rules["test_param"]["pattern"] == "^[A-Za-z]+$"
        assert rules["test_param"]["required"] == True
        
        # Check transformations were generated
        assert "transformations" in action_config["action"]
        transforms = action_config["action"]["transformations"]
        assert "test_param" in transforms
        assert "trim" in transforms["test_param"]
    
    def test_enhance_response_format(self, enhancer, mock_template):
        """Test response format enhancement."""
        response_format = mock_template.response_format.copy()
        enhancer._enhance_response_format(response_format)
        
        # Check auto_select was enabled
        assert response_format["response"]["crfs"]["auto_select"] == True
        
        # Check formats were added
        assert "formats" in response_format["response"]["crfs"]
        formats = response_format["response"]["crfs"]["formats"]
        assert "structured" in formats
        assert "text/plain" in formats
        assert "application/json" in formats
        
        # Check error mapping was added
        assert "error_mapping" in response_format["response"]
        error_mapping = response_format["response"]["error_mapping"]
        assert "400" in error_mapping
        assert "401" in error_mapping
        assert "404" in error_mapping
        assert "500" in error_mapping
    
    def test_extract_api_version(self, enhancer):
        """Test API version extraction."""
        assert enhancer._extract_api_version("/v1/users") == "v1"
        assert enhancer._extract_api_version("/v2/api/users") == "v2"
        assert enhancer._extract_api_version("/api/users") == "v1"  # Default
        assert enhancer._extract_api_version("/v10/test") == "v10"
    
    def test_generate_validation_rules(self, enhancer):
        """Test validation rule generation."""
        parameters = {
            "string_param": {
                "type": "string",
                "minLength": 3,
                "maxLength": 50,
                "pattern": "^[A-Z]+$",
                "required": True
            },
            "number_param": {
                "type": "number",
                "minimum": 0,
                "maximum": 100
            },
            "array_param": {
                "type": "array",
                "minItems": 1,
                "maxItems": 10
            }
        }
        
        rules = enhancer._generate_validation_rules(parameters)
        
        # Check string rules
        assert rules["string_param"]["type"] == "string"
        assert rules["string_param"]["min_length"] == 3
        assert rules["string_param"]["max_length"] == 50
        assert rules["string_param"]["pattern"] == "^[A-Z]+$"
        assert rules["string_param"]["required"] == True
        
        # Check number rules
        assert rules["number_param"]["type"] == "number"
        assert rules["number_param"]["minimum"] == 0
        assert rules["number_param"]["maximum"] == 100
        
        # Check array rules
        assert rules["array_param"]["type"] == "array"
        assert rules["array_param"]["min_items"] == 1
        assert rules["array_param"]["max_items"] == 10
    
    def test_generate_transformations(self, enhancer):
        """Test parameter transformation generation."""
        parameters = {
            "email": {"type": "string", "format": "email"},
            "url": {"type": "string", "format": "uri"},
            "uppercase_field": {"type": "string", "pattern": "[A-Z]+"},
            "regular_string": {"type": "string"},
            "number_field": {"type": "number"}
        }
        
        transforms = enhancer._generate_transformations(parameters)
        
        # Check email transformations
        assert "trim" in transforms["email"]
        assert "lowercase" in transforms["email"]
        
        # Check URL transformations
        assert "trim" in transforms["url"]
        assert "lowercase" in transforms["url"]
        
        # Check uppercase transformations
        assert "trim" in transforms["uppercase_field"]
        assert "uppercase" in transforms["uppercase_field"]
        
        # Check regular string transformations
        assert "trim" in transforms["regular_string"]
        
        # Check number has no transformations
        assert "number_field" not in transforms
    
    @pytest.mark.asyncio
    async def test_enhance_template(self, enhancer, mock_template):
        """Test full template enhancement."""
        result = await enhancer._enhance_template(mock_template)
        
        assert result is not None
        assert "action_config" in result
        assert "response_format" in result
        assert "test_data" in result
        
        # Check test data was generated
        test_data = result["test_data"]
        assert "mock_responses" in test_data
        assert "sample_parameters" in test_data
        assert "test_param" in test_data["sample_parameters"]


class TestTestDataGenerator:
    """Tests for TestDataGenerator class."""
    
    @pytest.fixture
    def generator(self):
        return TestDataGenerator()
    
    @pytest.fixture
    def mock_template(self):
        """Create a mock template for testing."""
        template = Mock(spec=Template)
        template.key = "test_api"
        template.category = TemplateCategory.UNIFIED
        template.action_config = {
            "action": {
                "method": "GET",
                "path": "/api/users/{id}"
            },
            "parameters": {
                "id": {
                    "type": "string",
                    "format": "uuid",
                    "required": True
                },
                "email": {
                    "type": "string",
                    "format": "email"
                },
                "age": {
                    "type": "integer",
                    "minimum": 18,
                    "maximum": 120
                }
            }
        }
        return template
    
    def test_generate_parameter_value_string(self, generator):
        """Test string parameter value generation."""
        # Test email format
        param_config = {"type": "string", "format": "email"}
        value = generator._generate_parameter_value(param_config, "standard")
        assert "@" in value
        
        # Test enum
        param_config = {"type": "string", "enum": ["active", "inactive"]}
        value = generator._generate_parameter_value(param_config, "standard")
        assert value in ["active", "inactive"]
        
        # Test length constraints
        param_config = {"type": "string", "minLength": 5, "maxLength": 10}
        value = generator._generate_parameter_value(param_config, "minimal")
        assert len(value) >= 5
        
        value = generator._generate_parameter_value(param_config, "maximal")
        assert len(value) <= 10
    
    def test_generate_parameter_value_number(self, generator):
        """Test number parameter value generation."""
        param_config = {"type": "integer", "minimum": 0, "maximum": 100}
        
        # Test minimal
        value = generator._generate_parameter_value(param_config, "minimal")
        assert value == 0
        
        # Test maximal
        value = generator._generate_parameter_value(param_config, "maximal")
        assert value == 100
        
        # Test standard
        value = generator._generate_parameter_value(param_config, "standard")
        assert 0 <= value <= 100
    
    def test_generate_parameter_value_array(self, generator):
        """Test array parameter value generation."""
        param_config = {
            "type": "array",
            "minItems": 1,
            "maxItems": 5,
            "items": {"type": "string"}
        }
        
        # Test minimal
        value = generator._generate_parameter_value(param_config, "minimal")
        assert isinstance(value, list)
        assert len(value) == 1
        
        # Test maximal
        value = generator._generate_parameter_value(param_config, "maximal")
        assert isinstance(value, list)
        assert len(value) == 5
    
    def test_generate_mock_responses(self, generator, mock_template):
        """Test mock response generation."""
        responses = generator._generate_mock_responses(
            mock_template, "GET", "/api/users/{id}"
        )
        
        assert "success" in responses
        assert "empty" in responses
        assert "partial" in responses
        
        # Check success response structure
        success = responses["success"]
        assert success["status"] == 200
        assert "data" in success
        assert isinstance(success["data"], dict)
    
    def test_generate_error_scenarios(self, generator):
        """Test error scenario generation."""
        parameters = {
            "required_field": {"type": "string", "required": True},
            "number_field": {"type": "number"}
        }
        
        scenarios = generator._generate_error_scenarios(parameters)
        
        # Should have scenarios for missing required, invalid type, auth error, not found
        assert len(scenarios) >= 4
        
        # Check missing required field scenario
        missing_scenario = next(
            s for s in scenarios if s["name"] == "missing_required_field"
        )
        assert "required_field" not in missing_scenario["parameters"]
        assert missing_scenario["expected_error"]["status"] == 400
        
        # Check invalid type scenario
        invalid_type_scenario = next(
            s for s in scenarios if s["name"] == "invalid_type_number_field"
        )
        assert invalid_type_scenario["parameters"]["number_field"] == "not_a_number"
    
    def test_generate_edge_cases(self, generator):
        """Test edge case generation."""
        parameters = {
            "text": {"type": "string", "maxLength": 100},
            "number": {"type": "integer", "minimum": -10}
        }
        
        edge_cases = generator._generate_edge_cases(parameters)
        
        # Check for expected edge cases
        case_names = [c["name"] for c in edge_cases]
        assert "empty_string_text" in case_names
        assert "max_length_text" in case_names
        assert "special_chars_text" in case_names
        assert "zero_number" in case_names
        assert "negative_number" in case_names


class TestTemplateStructureValidator:
    """Tests for TemplateStructureValidator class."""
    
    @pytest.fixture
    def validator(self):
        return TemplateStructureValidator()
    
    @pytest.fixture
    def valid_template(self):
        """Create a valid unified template."""
        template = Mock(spec=Template)
        template.key = "valid_template"
        template.category = TemplateCategory.UNIFIED
        template.scope = "tenant"
        
        template.intent_config = {
            "intent": {
                "patterns": ["get user by id", "fetch user {id}"],
                "confidence_threshold": 0.85
            }
        }
        
        template.action_config = {
            "action": {
                "method": "GET",
                "path": "/users/{id}",
                "integration": {
                    "base_url": "https://api.example.com",
                    "api_version": "v1",
                    "credential_ref": "api_key"
                },
                "auth": {
                    "type": "api_key",
                    "header": "X-API-Key",
                    "value": "{{credentials.api_key}}"
                }
            },
            "parameters": {
                "id": {"type": "string", "required": True}
            }
        }
        
        template.response_format = {
            "response": {
                "crfs": {
                    "type": "structured",
                    "auto_select": True,
                    "formats": {
                        "structured": {
                            "sections": [{"type": "text", "content": "User: {{name}}"}]
                        }
                    }
                },
                "error_mapping": {
                    "404": "User not found"
                }
            }
        }
        
        template.test_data = {
            "mock_responses": {
                "success": {"id": "123", "name": "Test User"}
            },
            "sample_parameters": {"id": "123"}
        }
        
        template.integration = Mock(base_url="https://api.example.com")
        
        return template
    
    @pytest.fixture
    def invalid_template(self):
        """Create an invalid template with issues."""
        template = Mock(spec=Template)
        template.key = ""  # Missing key
        template.category = TemplateCategory.UNIFIED
        template.scope = None
        
        template.intent_config = {}  # Missing intent section
        template.action_config = {
            "action": {
                "method": "INVALID",  # Invalid method
                "path": "users/{id}"  # Missing leading slash
                # Missing integration section
            },
            "parameters": {}  # Path param not defined
        }
        
        template.response_format = None  # Missing response format
        template.test_data = None
        template.integration = None
        
        return template
    
    def test_validate_valid_template(self, validator, valid_template):
        """Test validation of a valid template."""
        result = validator._validate_template(valid_template)
        
        assert result.is_valid
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_invalid_template(self, validator, invalid_template):
        """Test validation of an invalid template."""
        result = validator._validate_template(invalid_template)
        
        assert not result.is_valid
        assert len(result.errors) > 0
        
        # Check specific errors
        error_messages = " ".join(result.errors)
        assert "Missing template key" in error_messages
        assert "Missing 'intent' section" in error_messages
        assert "Invalid HTTP method: INVALID" in error_messages
        assert "Path parameter 'id' not defined" in error_messages
    
    def test_validate_intent_config(self, validator):
        """Test intent configuration validation."""
        template = Mock()
        result = ValidationResult()
        
        # Test missing intent section
        template.intent_config = {}
        validator._validate_intent_config(template, result)
        assert "Missing 'intent' section" in result.errors[0]
        
        # Test missing patterns
        result = ValidationResult()
        template.intent_config = {"intent": {}}
        validator._validate_intent_config(template, result)
        assert "Missing 'patterns'" in result.errors[0]
        
        # Test empty patterns
        result = ValidationResult()
        template.intent_config = {"intent": {"patterns": []}}
        validator._validate_intent_config(template, result)
        assert "At least one pattern is required" in result.errors[0]
        
        # Test invalid confidence threshold
        result = ValidationResult()
        template.intent_config = {
            "intent": {
                "patterns": ["test pattern"],
                "confidence_threshold": 1.5
            }
        }
        validator._validate_intent_config(template, result)
        assert "confidence_threshold' must be between 0 and 1" in result.errors[0]
    
    def test_validate_action_config(self, validator):
        """Test action configuration validation."""
        template = Mock()
        template.action_config = {
            "action": {
                "method": "POST",
                "path": "/users/{id}",
                "integration": {
                    "base_url": "not-a-url"  # Invalid URL
                }
            },
            "parameters": {}  # Missing path parameter
        }
        
        result = ValidationResult()
        validator._validate_action_config(template, result)
        
        # Check for warnings and errors
        assert "Path parameter 'id' not defined" in result.errors[0]
        assert "base_url should start with http" in " ".join(result.warnings)
    
    def test_validate_parameter_mappings(self, validator):
        """Test parameter mapping validation."""
        template = Mock()
        template.action_config = {
            "action": {
                "parameter_mapping": {
                    "user_id": "{{parameters.userId}}",  # References undefined param
                    "name": "{{parameters.name}}"
                }
            },
            "parameters": {
                "name": {"type": "string", "required": True}
            }
        }
        
        result = ValidationResult()
        validator._validate_parameter_mappings(template, result)
        
        assert "references undefined parameter: userId" in result.errors[0]
    
    def test_validate_test_data(self, validator):
        """Test test data validation."""
        template = Mock()
        template.action_config = {
            "parameters": {
                "required_param": {"type": "string", "required": True},
                "optional_param": {"type": "string"}
            }
        }
        template.test_data = {
            "mock_responses": {},  # Missing success response
            "sample_parameters": {
                "optional_param": "value"  # Missing required param
            }
        }
        
        result = ValidationResult()
        validator._validate_test_data(template, result)
        
        assert "missing 'success' mock response" in " ".join(result.warnings)
        assert "missing required parameter: required_param" in " ".join(result.warnings)
    
    @pytest.mark.asyncio
    async def test_fix_template_issues(self, validator, invalid_template):
        """Test automatic issue fixing."""
        # Set up a template with fixable issues
        template = Mock(spec=Template)
        template.action_config = {"action": {}}  # Missing integration
        template.response_format = {
            "response": {
                "crfs": {}  # Missing auto_select
            }
        }
        
        result = ValidationResult()
        result.add_error("Some error")
        
        db = Mock()
        fixed = await validator._fix_template_issues(template, result, db)
        
        assert fixed
        assert "integration" in template.action_config["action"]
        assert template.response_format["response"]["crfs"]["auto_select"] == True
        assert "error_mapping" in template.response_format["response"]