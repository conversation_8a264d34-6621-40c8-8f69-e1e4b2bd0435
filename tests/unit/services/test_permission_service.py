from typing import Set

import pytest

from src.coherence.services.permission_service import (
    CoherencePermission,
    PermissionService,
)


@pytest.fixture
def permission_service() -> PermissionService:
    return PermissionService()

def test_org_admin_permissions(permission_service: PermissionService):
    """Test permissions for a standard organization admin."""
    permissions = permission_service.get_coherence_permissions(
        clerk_org_role="admin", is_system_admin=False
    )
    expected = {
        CoherencePermission.WORKFLOW_CREATE.value,
        CoherencePermission.WORKFLOW_READ.value,
        CoherencePermission.WORKFLOW_UPDATE.value,
        CoherencePermission.WORKFLOW_DELETE.value,
        CoherencePermission.TEMPLATE_CREATE.value,
        CoherencePermission.TEMPLATE_READ.value,
        CoherencePermission.TEMPLATE_UPDATE.value,
        CoherencePermission.TEMPLATE_DELETE.value,
        CoherencePermission.TEMPLATE_READ_VERSIONS.value,
        CoherencePermission.INTEGRATION_CREATE.value,
        CoherencePermission.INTEGRATION_READ.value,
        CoherencePermission.INTEGRATION_UPDATE.value,
        CoherencePermission.INTEGRATION_DELETE.value,
        CoherencePermission.INTEGRATION_MANAGE_CREDENTIALS.value,
        CoherencePermission.APIKEY_CREATE.value,
        CoherencePermission.APIKEY_READ.value,
        CoherencePermission.APIKEY_DELETE.value,
        "organization:view_own_dashboard",
        CoherencePermission.AUDITLOG_VIEW.value,
    }
    assert permissions == expected

def test_org_member_permissions(permission_service: PermissionService):
    """Test permissions for a standard organization member."""
    permissions = permission_service.get_coherence_permissions(
        clerk_org_role="member", is_system_admin=False
    )
    expected = {
        CoherencePermission.WORKFLOW_READ.value,
        CoherencePermission.WORKFLOW_EXECUTE.value,
        CoherencePermission.TEMPLATE_READ.value,
        "organization:view_own_dashboard",
    }
    assert permissions == expected

def test_org_member_permissions_with_clerk_prefix(permission_service: PermissionService):
    """Test permissions for an organization member with 'org:' prefix."""
    permissions = permission_service.get_coherence_permissions(
        clerk_org_role="org:member", is_system_admin=False
    )
    expected = {
        CoherencePermission.WORKFLOW_READ.value,
        CoherencePermission.WORKFLOW_EXECUTE.value,
        CoherencePermission.TEMPLATE_READ.value,
        "organization:view_own_dashboard",
    }
    assert permissions == expected

def test_system_admin_permissions_no_org_role(permission_service: PermissionService):
    """Test permissions for a system admin without a specific organization role."""
    permissions = permission_service.get_coherence_permissions(
        clerk_org_role=None, is_system_admin=True
    )
    # System admin should have all defined permissions
    assert permissions == CoherencePermission.all_permissions()

def test_system_admin_permissions_with_org_admin_role(permission_service: PermissionService):
    """Test permissions for a system admin who is also an org admin."""
    permissions = permission_service.get_coherence_permissions(
        clerk_org_role="admin", is_system_admin=True
    )
    # System admin should have all defined permissions, regardless of org role
    assert permissions == CoherencePermission.all_permissions()

def test_system_admin_permissions_with_org_member_role(permission_service: PermissionService):
    """Test permissions for a system admin who is also an org member."""
    permissions = permission_service.get_coherence_permissions(
        clerk_org_role="org:member", is_system_admin=True
    )
    assert permissions == CoherencePermission.all_permissions()


def test_unknown_role_permissions(permission_service: PermissionService):
    """Test permissions for an unknown role, not system admin."""
    permissions = permission_service.get_coherence_permissions(
        clerk_org_role="viewer", is_system_admin=False
    )
    assert permissions == set()

def test_no_role_permissions(permission_service: PermissionService):
    """Test permissions for no role, not system admin."""
    permissions = permission_service.get_coherence_permissions(
        clerk_org_role=None, is_system_admin=False
    )
    assert permissions == set()

def test_all_permissions_method():
    """Test the all_permissions class method."""
    all_perms = CoherencePermission.all_permissions()
    assert isinstance(all_perms, Set)
    assert len(all_perms) > 0 # Should have some permissions defined
    # Check a few known permissions
    assert CoherencePermission.WORKFLOW_CREATE.value in all_perms
    assert CoherencePermission.TENANT_LIST_ALL.value in all_perms
    assert CoherencePermission.SYSTEM_HEALTH_VIEW.value in all_perms

    # Verify all enum members are included
    for perm_enum in CoherencePermission:
        assert perm_enum.value in all_perms
    
    # Verify count matches enum size
    assert len(all_perms) == len(list(CoherencePermission))