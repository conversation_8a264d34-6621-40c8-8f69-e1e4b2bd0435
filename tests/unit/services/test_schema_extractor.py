"""Tests for the OpenAPISchemaExtractor service."""

import pytest
from src.coherence.services.schema_extractor import OpenAPISchemaExtractor


class TestOpenAPISchemaExtractor:
    """Test the OpenAPISchemaExtractor service."""

    def test_extract_basic_parameters(self):
        """Test extracting basic parameters from OpenAPI spec."""
        api_spec = {
            "paths": {
                "/users/{id}": {
                    "get": {
                        "parameters": [
                            {
                                "name": "id",
                                "in": "path",
                                "required": True,
                                "schema": {"type": "integer"},
                                "description": "User ID"
                            },
                            {
                                "name": "include_details",
                                "in": "query",
                                "required": False,
                                "schema": {
                                    "type": "boolean",
                                    "default": False
                                },
                                "description": "Include detailed information"
                            }
                        ]
                    }
                }
            }
        }
        
        parameters = OpenAPISchemaExtractor.extract_enhanced_parameters(
            api_spec, "/users/{id}", "get"
        )
        
        assert len(parameters) == 2
        
        # Check path parameter
        path_param = next(p for p in parameters if p.name == "id")
        assert path_param.type == "integer"
        assert path_param.in_location == "path"
        assert path_param.required is True
        assert path_param.description == "User ID"
        
        # Check query parameter
        query_param = next(p for p in parameters if p.name == "include_details")
        assert query_param.type == "boolean"
        assert query_param.in_location == "query"
        assert query_param.required is False
        assert query_param.default is False
        assert query_param.description == "Include detailed information"

    def test_extract_parameters_with_constraints(self):
        """Test extracting parameters with validation constraints."""
        api_spec = {
            "paths": {
                "/search": {
                    "get": {
                        "parameters": [
                            {
                                "name": "query",
                                "in": "query",
                                "required": True,
                                "schema": {
                                    "type": "string",
                                    "minLength": 3,
                                    "maxLength": 100,
                                    "pattern": "^[a-zA-Z0-9\\s]+$"
                                }
                            },
                            {
                                "name": "limit",
                                "in": "query",
                                "required": False,
                                "schema": {
                                    "type": "integer",
                                    "minimum": 1,
                                    "maximum": 100,
                                    "default": 20
                                }
                            }
                        ]
                    }
                }
            }
        }
        
        parameters = OpenAPISchemaExtractor.extract_enhanced_parameters(
            api_spec, "/search", "get"
        )
        
        # Check string parameter with constraints
        query_param = next(p for p in parameters if p.name == "query")
        assert query_param.min_length == 3
        assert query_param.max_length == 100
        assert query_param.pattern == "^[a-zA-Z0-9\\s]+$"
        
        # Check integer parameter with constraints
        limit_param = next(p for p in parameters if p.name == "limit")
        assert limit_param.minimum == 1
        assert limit_param.maximum == 100
        assert limit_param.default == 20

    def test_extract_parameters_with_enum(self):
        """Test extracting parameters with enum values."""
        api_spec = {
            "paths": {
                "/items": {
                    "get": {
                        "parameters": [
                            {
                                "name": "status",
                                "in": "query",
                                "required": False,
                                "schema": {
                                    "type": "string",
                                    "enum": ["active", "inactive", "pending"],
                                    "default": "active"
                                }
                            }
                        ]
                    }
                }
            }
        }
        
        parameters = OpenAPISchemaExtractor.extract_enhanced_parameters(
            api_spec, "/items", "get"
        )
        
        status_param = parameters[0]
        assert status_param.name == "status"
        assert status_param.type == "enum"  # Should be detected as enum type
        assert status_param.enum == ["active", "inactive", "pending"]
        assert status_param.default == "active"

    def test_extract_request_body_parameters(self):
        """Test extracting parameters from request body."""
        api_spec = {
            "paths": {
                "/users": {
                    "post": {
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "required": ["name", "email"],
                                        "properties": {
                                            "name": {
                                                "type": "string",
                                                "minLength": 1,
                                                "maxLength": 50
                                            },
                                            "email": {
                                                "type": "string",
                                                "format": "email"
                                            },
                                            "age": {
                                                "type": "integer",
                                                "minimum": 0,
                                                "maximum": 150
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        parameters = OpenAPISchemaExtractor.extract_enhanced_parameters(
            api_spec, "/users", "post"
        )
        
        assert len(parameters) == 3
        
        # Check required string parameter
        name_param = next(p for p in parameters if p.name == "name")
        assert name_param.type == "string"
        assert name_param.in_location == "body"
        assert name_param.required is True
        assert name_param.min_length == 1
        assert name_param.max_length == 50
        
        # Check email parameter with format
        email_param = next(p for p in parameters if p.name == "email")
        assert email_param.type == "email"  # Should detect format
        assert email_param.format == "email"
        assert email_param.required is True
        
        # Check optional integer parameter
        age_param = next(p for p in parameters if p.name == "age")
        assert age_param.type == "integer"
        assert age_param.required is False
        assert age_param.minimum == 0
        assert age_param.maximum == 150

    def test_extract_parameters_with_examples(self):
        """Test extracting parameters with examples."""
        api_spec = {
            "paths": {
                "/products": {
                    "get": {
                        "parameters": [
                            {
                                "name": "category",
                                "in": "query",
                                "schema": {
                                    "type": "string",
                                    "example": "electronics"
                                }
                            }
                        ]
                    }
                }
            }
        }
        
        parameters = OpenAPISchemaExtractor.extract_enhanced_parameters(
            api_spec, "/products", "get"
        )
        
        category_param = parameters[0]
        assert category_param.example == "electronics"

    def test_extract_parameters_empty_operation(self):
        """Test extracting parameters from operation with no parameters."""
        api_spec = {
            "paths": {
                "/health": {
                    "get": {}
                }
            }
        }
        
        parameters = OpenAPISchemaExtractor.extract_enhanced_parameters(
            api_spec, "/health", "get"
        )
        
        assert len(parameters) == 0

    def test_extract_parameters_nonexistent_path(self):
        """Test extracting parameters from nonexistent path."""
        api_spec = {
            "paths": {
                "/users": {
                    "get": {}
                }
            }
        }
        
        parameters = OpenAPISchemaExtractor.extract_enhanced_parameters(
            api_spec, "/nonexistent", "get"
        )
        
        assert len(parameters) == 0

    def test_resolve_schema_reference(self):
        """Test resolving $ref references in schemas."""
        api_spec = {
            "components": {
                "schemas": {
                    "UserId": {
                        "type": "integer",
                        "minimum": 1
                    }
                }
            },
            "paths": {
                "/users/{id}": {
                    "get": {
                        "parameters": [
                            {
                                "name": "id",
                                "in": "path",
                                "schema": {
                                    "$ref": "#/components/schemas/UserId"
                                }
                            }
                        ]
                    }
                }
            }
        }
        
        parameters = OpenAPISchemaExtractor.extract_enhanced_parameters(
            api_spec, "/users/{id}", "get"
        )
        
        id_param = parameters[0]
        assert id_param.type == "integer"
        assert id_param.minimum == 1

    def test_validate_parameter_value(self):
        """Test parameter value validation."""
        from src.coherence.schemas.openapi_enhanced import EnhancedParameterSchema
        
        # Create a parameter schema for testing
        param = EnhancedParameterSchema(
            name="test_param",
            type="string",
            in_location="query",
            required=True,
            min_length=3,
            max_length=10,
            pattern="^[a-z]+$"
        )
        
        # Test valid value
        result = OpenAPISchemaExtractor.validate_parameter_value("hello", param)
        assert result.valid is True
        assert len(result.errors) == 0
        
        # Test invalid value (too short)
        result = OpenAPISchemaExtractor.validate_parameter_value("hi", param)
        assert result.valid is False
        assert any("minimum" in error.lower() for error in result.errors)
        
        # Test invalid value (pattern mismatch)
        result = OpenAPISchemaExtractor.validate_parameter_value("Hello123", param)
        assert result.valid is False
        assert any("pattern" in error.lower() for error in result.errors)
        
        # Test required parameter with empty value
        result = OpenAPISchemaExtractor.validate_parameter_value("", param)
        assert result.valid is False
        assert any("required" in error.lower() for error in result.errors)