"""
Unit tests for VectorIndexer service.
"""

import uuid
from unittest.mock import AsyncMock, MagicMock

import pytest

from src.coherence.core.errors.base import EmbeddingDimensionMismatchError
from src.coherence.services.vector_indexer import VectorIndexer


@pytest.fixture
def mock_qdrant_client():
    """Create a mock QdrantClient."""
    mock = MagicMock()
    mock.upsert_template_vectors = AsyncMock(return_value=True)
    mock.delete_template_vectors = AsyncMock(return_value=True)
    mock.search_templates = AsyncMock(return_value=[])
    mock._ensure_template_collection_exists = AsyncMock(return_value=True)
    mock._check_collection_exists = AsyncMock(return_value=True)
    return mock


@pytest.fixture
def mock_llm_factory():
    """Create a mock LLMFactory."""
    mock = MagicMock()
    mock_provider = MagicMock()
    mock_provider.generate_embedding = AsyncMock(return_value=[0.1] * 384)
    mock.get_provider = MagicMock(return_value=mock_provider)
    return mock


@pytest.fixture
def vector_indexer(mock_qdrant_client, mock_llm_factory):
    """Create a VectorIndexer with mocked dependencies."""
    return VectorIndexer(
        qdrant_client=mock_qdrant_client,
        llm_factory=mock_llm_factory
    )


@pytest.mark.asyncio
async def test_upsert_template(vector_indexer, mock_qdrant_client):
    """Test upserting a template."""
    # Create test data
    template_id = str(uuid.uuid4())
    template_key = "TEST_TEMPLATE"
    template_category = "intent_router"
    template_body = "This is a test template"
    template_description = "Test description"
    index_name = "template_idx_test"
    
    # Mock dependencies
    vector_indexer._ensure_template_collection_exists = AsyncMock(return_value=True)
    vector_indexer._generate_embedding = AsyncMock(return_value=[0.1] * 384)
    
    # Call the method
    result = await vector_indexer.upsert_template(
        db=AsyncMock(),
        template_id=template_id,
        template_key=template_key,
        template_category=template_category,
        template_body=template_body,
        template_description=template_description,
        index_name=index_name
    )
    
    # Assert result
    assert result is True
    
    # Verify method calls
    vector_indexer._ensure_template_collection_exists.assert_called_once_with(index_name)
    vector_indexer._generate_embedding.assert_called_once()
    mock_qdrant_client.upsert_template_vectors.assert_called_once()
    
    # Verify correct data was passed
    call_args = mock_qdrant_client.upsert_template_vectors.call_args[1]
    assert call_args["collection_name"] == index_name
    assert len(call_args["points"]) == 1
    assert call_args["points"][0]["id"] == template_id
    assert call_args["points"][0]["metadata"]["template_id"] == template_id
    assert call_args["points"][0]["metadata"]["template_key"] == template_key
    assert call_args["points"][0]["metadata"]["template_category"] == template_category


@pytest.mark.asyncio
async def test_delete_template(vector_indexer, mock_qdrant_client):
    """Test deleting a template."""
    # Create test data
    template_id = str(uuid.uuid4())
    index_name = "template_idx_test"
    
    # Call the method
    result = await vector_indexer.delete_template(
        template_id=template_id,
        index_name=index_name
    )
    
    # Assert result
    assert result is True
    
    # Verify method calls
    mock_qdrant_client.delete_template_vectors.assert_called_once_with(
        collection_name=index_name,
        points_selector={"ids": [template_id]}
    )


@pytest.mark.asyncio
async def test_search_templates(vector_indexer, mock_qdrant_client):
    """Test searching for templates."""
    # Create test data
    query = "test search query"
    tenant_id = str(uuid.uuid4())
    category = "intent_router"
    
    # Mock search results
    mock_results = [
        {"id": str(uuid.uuid4()), "score": 0.95, "template_key": "template1"},
        {"id": str(uuid.uuid4()), "score": 0.85, "template_key": "template2"},
    ]
    
    # Configure mocks
    vector_indexer._generate_embedding = AsyncMock(return_value=[0.1] * 384)
    vector_indexer._check_collection_exists = AsyncMock(return_value=True)
    mock_qdrant_client.search = AsyncMock(return_value=mock_results)
    
    # Call the method
    results = await vector_indexer.search_templates(
        query=query,
        tenant_id=tenant_id,
        category=category
    )
    
    # Assert result
    assert results == mock_results
    
    # Verify method calls
    vector_indexer._generate_embedding.assert_called_once_with(query)
    
    # Should search in tenant-specific and global collections
    assert mock_qdrant_client.search.call_count >= 2


@pytest.mark.asyncio
async def test_ensure_template_collection_exists(vector_indexer):
    """Test ensuring a template collection exists."""
    # Create test data
    index_name = "template_idx_test"
    
    # Configure mocks
    vector_indexer._check_collection_exists = AsyncMock(return_value=False)
    vector_indexer.qdrant_client.create_template_collection = AsyncMock(return_value=True)
    
    # Call the method
    result = await vector_indexer._ensure_template_collection_exists(index_name)
    
    # Assert result
    assert result is True
    
    # Verify method calls
    vector_indexer._check_collection_exists.assert_called_once_with(index_name)
    vector_indexer.qdrant_client.create_template_collection.assert_called_once_with(
        collection_name=index_name,
        vector_size=vector_indexer.embedding_dimensions
    )


@pytest.mark.asyncio
async def test_generate_embedding(vector_indexer, mock_llm_factory):
    """Test generating an embedding."""
    # Create test data
    text = "test text for embedding"
    
    # Call the method
    result = await vector_indexer._generate_embedding(text)
    
    # Assert result
    assert len(result) == 384
    
    # Verify method calls
    mock_provider = mock_llm_factory.get_provider.return_value
    mock_provider.generate_embedding.assert_called_once_with(
        text=text,
        dimensions=vector_indexer.embedding_dimensions
    )


@pytest.mark.asyncio
async def test_generate_embedding_dimension_mismatch(vector_indexer, mock_llm_factory):
    """Test that dimension mismatch raises an error."""
    # Create test data
    text = "test text for embedding"
    
    # Configure mock to return incorrect dimensions (384 + 100)
    mock_provider = mock_llm_factory.get_provider.return_value
    mock_provider.generate_embedding.return_value = [0.1] * (vector_indexer.embedding_dimensions + 100)
    
    # Call the method - should raise EmbeddingDimensionMismatchError
    with pytest.raises(EmbeddingDimensionMismatchError) as exc_info:
        await vector_indexer._generate_embedding(text)
    
    # Verify error contains expected information
    assert "dimension mismatch" in str(exc_info.value)
    assert exc_info.value.expected_dimension == vector_indexer.embedding_dimensions
    assert exc_info.value.actual_dimension == vector_indexer.embedding_dimensions + 100


@pytest.mark.asyncio
async def test_upsert_dimension_mismatch_raises(vector_indexer, mock_qdrant_client):
    """Test that upsert raises ValueError when _verify_dimensions reports a mismatch."""
    # Create test data
    template_id = str(uuid.uuid4())
    template_key = "TEST_TEMPLATE"
    template_category = "intent_router"
    template_body = "This is a test template"
    template_description = "Test description"
    index_name = "template_idx_test"
    
    # Mock dependencies
    vector_indexer._ensure_template_collection_exists = AsyncMock(side_effect=EmbeddingDimensionMismatchError(
        expected_dimension=384,
        actual_dimension=1536,
        model="text-embedding-3-small",
        message="Collection dimension mismatch: expected 384 but found 1536"
    ))
    vector_indexer._generate_embedding = AsyncMock(return_value=[0.1] * 384)
    
    # Call the method - should return False but not raise
    result = await vector_indexer.upsert_template(
        db=AsyncMock(),
        template_id=template_id,
        template_key=template_key,
        template_category=template_category,
        template_body=template_body,
        template_description=template_description,
        index_name=index_name
    )
    
    # Assert result is False
    assert result is False
    
    # Verify method calls
    vector_indexer._ensure_template_collection_exists.assert_called_once_with(index_name)
    vector_indexer._generate_embedding.assert_not_called()
    mock_qdrant_client.upsert_template_vectors.assert_not_called()