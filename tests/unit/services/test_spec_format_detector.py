"""
Tests for the API specification format detector service.
"""

import json

import pytest

from src.coherence.services.spec_format_detector import SpecFormat, SpecFormatDetector


class TestSpecFormatDetector:
    """Tests for the SpecFormatDetector service."""

    def test_detect_openapi_by_version(self):
        """Test detection of OpenAPI format by version key."""
        spec = {"openapi": "3.0.0", "info": {"title": "Test API"}}
        assert SpecFormatDetector.detect_format(spec) == SpecFormat.OPENAPI

        # Test with Swagger version
        spec = {"swagger": "2.0", "info": {"title": "Test API"}}
        assert SpecFormatDetector.detect_format(spec) == SpecFormat.OPENAPI

    def test_detect_fapi_by_version(self):
        """Test detection of FAPI format by version key."""
        spec = {"financial_api": "1.0", "info": {"title": "Financial API"}}
        assert SpecFormatDetector.detect_format(spec) == SpecFormat.FAPI

        spec = {"fapi_version": "2.0", "info": {"title": "Financial API"}}
        assert SpecFormatDetector.detect_format(spec) == SpecFormat.FAPI

    def test_detect_bapi_by_version(self):
        """Test detection of BAPI format by version key."""
        spec = {"business_api": "1.0", "info": {"title": "Business API"}}
        assert SpecFormatDetector.detect_format(spec) == SpecFormat.BAPI

        spec = {"bapi_version": "2.0", "info": {"title": "Business API"}}
        assert SpecFormatDetector.detect_format(spec) == SpecFormat.BAPI

    def test_detect_fapi_by_structure(self):
        """Test detection of FAPI format by structure indicators."""
        spec = {
            "info": {"title": "Financial API", "version": "1.0"},
            "security_profiles": {
                "oauth2_profile": {
                    "type": "oauth2",
                    "profile": "FAPI 1.0",
                    "requires_pkce": True
                }
            },
            "endpoints": [
                {
                    "path": "/accounts",
                    "method": "get",
                    "id": "get_accounts"
                }
            ]
        }
        assert SpecFormatDetector.detect_format(spec) == SpecFormat.FAPI

    def test_detect_bapi_by_structure(self):
        """Test detection of BAPI format by structure indicators."""
        spec = {
            "info": {"title": "Business API", "version": "1.0"},
            "services": [
                {
                    "name": "orders",
                    "operations": [
                        {
                            "name": "createOrder",
                            "method": "post",
                            "path": "/orders"
                        }
                    ]
                }
            ]
        }
        assert SpecFormatDetector.detect_format(spec) == SpecFormat.BAPI

    def test_detect_openapi_by_structure(self):
        """Test detection of OpenAPI format by structure indicators."""
        spec = {
            "info": {"title": "API", "version": "1.0"},
            "paths": {
                "/users": {
                    "get": {"description": "Get users"}
                }
            }
        }
        assert SpecFormatDetector.detect_format(spec) == SpecFormat.OPENAPI

    def test_parse_json_string(self):
        """Test parsing a JSON string specification."""
        spec_str = json.dumps({"openapi": "3.0.0", "info": {"title": "Test API"}})
        assert SpecFormatDetector.detect_format(spec_str) == SpecFormat.OPENAPI

    def test_invalid_json_string(self):
        """Test handling invalid JSON string."""
        invalid_json = "{invalid: json"
        with pytest.raises(ValueError, match="Invalid API specification JSON"):
            SpecFormatDetector.detect_format(invalid_json)

    def test_invalid_spec_type(self):
        """Test handling invalid specification type."""
        with pytest.raises(ValueError, match="API specification must be a dictionary"):
            SpecFormatDetector.detect_format([1, 2, 3])  # List instead of dict

    def test_unknown_format(self):
        """Test detection of unknown format."""
        spec = {"foo": "bar"}  # No recognizable format indicators
        assert SpecFormatDetector.detect_format(spec) == SpecFormat.UNKNOWN