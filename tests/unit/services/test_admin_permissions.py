from src.coherence.services.permission_service import PermissionService


def test_sys_admin_gets_wildcard():
    """Test that system admins receive the wildcard permission."""
    permission_service = PermissionService()
    
    # When a user is a system admin
    permissions = permission_service.get_coherence_permissions(
        clerk_org_role=None,  # No specific role needed for this test
        is_system_admin=True
    )
    
    # Then they should have the wildcard permission
    assert "system:*" in permissions, "System admin should have 'system:*' wildcard permission"

def test_normal_user_no_wildcard():
    """Test that regular users don't receive the wildcard permission."""
    permission_service = PermissionService()
    
    # When a user is not a system admin but has an org role
    permissions = permission_service.get_coherence_permissions(
        clerk_org_role="admin",  # Even org admins don't get wildcard
        is_system_admin=False
    )
    
    # Then they should not have the wildcard permission
    assert "system:*" not in permissions, "Regular users should not have 'system:*' wildcard permission"

def test_system_admin_with_org_role():
    """Test that system admins with org roles get both permission sets."""
    permission_service = PermissionService()
    
    # When a user is both system admin and has an org role
    permissions_sys_admin_and_org_admin = permission_service.get_coherence_permissions(
        clerk_org_role="admin",
        is_system_admin=True
    )
    
    # Then they should have the wildcard permission AND org admin permissions
    assert "system:*" in permissions_sys_admin_and_org_admin, "System admin should have 'system:*' wildcard permission"
    
    # And they should have organization-specific permissions 
    # Example: organization view dashboard permission should be present
    assert "organization:view_own_dashboard" in permissions_sys_admin_and_org_admin
    
    # And they should have integration permissions from the role
    assert "integration:read" in permissions_sys_admin_and_org_admin