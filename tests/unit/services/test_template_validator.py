"""
Unit tests for the UnifiedTemplateValidator.
"""

import pytest
from typing import Dict

from src.coherence.services.template_validator import (
    UnifiedTemplateValidator,
    ValidationResult
)


class TestUnifiedTemplateValidator:
    """Test cases for UnifiedTemplateValidator."""
    
    @pytest.fixture
    def validator(self):
        """Create a validator instance."""
        return UnifiedTemplateValidator()
    
    @pytest.fixture
    def valid_template(self) -> Dict:
        """Create a valid unified template for testing."""
        return {
            "meta": {
                "key": "weather_forecast",
                "endpoint_id": "GET_/v1/forecast",
                "version": 1,
                "tags": ["weather", "forecast"]
            },
            "intent": {
                "patterns": [
                    "weather forecast for {location}",
                    "what's the weather in {location}",
                    "forecast for {location}"
                ],
                "confidence_threshold": 0.85,
                "parameter_hints": {
                    "location": ["city", "place", "location"]
                }
            },
            "action": {
                "method": "GET",
                "path": "/v1/forecast",
                "integration": {
                    "base_url": "${WEATHER_API_URL:https://api.weather.com}",
                    "api_version": "v1",
                    "credential_ref": "weather_api_key"
                },
                "authentication": {
                    "type": "api_key",
                    "header": "X-API-Key",
                    "value": "{{credentials.weather_api_key}}"
                },
                "parameter_mapping": {
                    "q": "{{parameters.location}}",
                    "units": "{{parameters.units|default('metric')}}"
                },
                "validation_rules": {
                    "location": {
                        "type": "string",
                        "required": True,
                        "min_length": 1,
                        "max_length": 100,
                        "pattern": "^[A-Za-z\\s,]+$"
                    },
                    "units": {
                        "type": "string",
                        "enum": ["metric", "imperial"],
                        "default": "metric"
                    }
                },
                "transformations": {
                    "location": ["trim", "lowercase"]
                }
            },
            "response": {
                "crfs": {
                    "type": "structured",
                    "auto_select": True,
                    "default_format": "structured",
                    "formats": {
                        "structured": {
                            "sections": [
                                {
                                    "type": "text",
                                    "style": "heading",
                                    "content": "Weather Forecast for {{parameters.location}}"
                                },
                                {
                                    "type": "list",
                                    "items": "{{result.daily}}",
                                    "template": "{{item.date}}: {{item.temp}}°, {{item.conditions}}"
                                }
                            ]
                        },
                        "text/plain": {
                            "template": "Weather in {{parameters.location}}: {{result.summary}}"
                        }
                    }
                },
                "error_mapping": {
                    "404": "Location '{{parameters.location}}' not found",
                    "401": "Weather API authentication failed"
                }
            },
            "test_data": {
                "mock_responses": {
                    "success": {
                        "daily": [
                            {"date": "Today", "temp": 22, "conditions": "Sunny"},
                            {"date": "Tomorrow", "temp": 20, "conditions": "Partly cloudy"}
                        ],
                        "summary": "Sunny today, partly cloudy tomorrow"
                    }
                },
                "sample_parameters": {
                    "location": "London"
                }
            }
        }
    
    def test_validate_complete_valid_template(self, validator, valid_template):
        """Test validation of a complete valid template."""
        result = validator.validate_completeness(valid_template)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
        assert isinstance(result.warnings, list)
        assert result.metadata['has_test_data'] is True
        assert result.metadata['auth_type'] == 'api_key'
        assert result.metadata['parameter_count'] == 2
    
    def test_validate_missing_sections(self, validator):
        """Test validation with missing required sections."""
        template = {
            "meta": {"key": "test"},
            "intent": {"patterns": ["test pattern"]}
            # Missing action and response sections
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("Missing required sections" in error for error in result.errors)
        assert "action, response" in str(result.errors)
    
    def test_validate_invalid_meta_section(self, validator):
        """Test validation of invalid meta section."""
        template = {
            "meta": {
                "key": "Invalid-Key-Format",  # Should be snake_case
                "endpoint_id": "invalid_endpoint"  # Should be METHOD_/path
            },
            "intent": {"patterns": ["test"]},
            "action": {"method": "GET", "path": "/test"},
            "response": {"crfs": {"type": "structured"}}
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("Invalid key format" in error for error in result.errors)
        assert any("Invalid endpoint_id format" in error for error in result.errors)
    
    def test_validate_intent_without_patterns(self, validator):
        """Test validation of intent section without patterns."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {
                "confidence_threshold": 0.8
                # Missing patterns
            },
            "action": {"method": "GET", "path": "/test"},
            "response": {"crfs": {"type": "structured"}}
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("Intent section must have at least one pattern" in error for error in result.errors)
    
    def test_validate_invalid_http_method(self, validator):
        """Test validation with invalid HTTP method."""
        template = {
            "meta": {"key": "test", "endpoint_id": "INVALID_/test"},
            "intent": {"patterns": ["test"]},
            "action": {
                "method": "INVALID",  # Invalid HTTP method
                "path": "/test"
            },
            "response": {"crfs": {"type": "structured"}}
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("Invalid HTTP method" in error for error in result.errors)
    
    def test_validate_invalid_auth_type(self, validator):
        """Test validation with invalid authentication type."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test"]},
            "action": {
                "method": "GET",
                "path": "/test",
                "authentication": {
                    "type": "invalid_auth"  # Invalid auth type
                }
            },
            "response": {"crfs": {"type": "structured"}}
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("Invalid authentication type" in error for error in result.errors)
    
    def test_validate_api_key_auth_missing_location(self, validator):
        """Test validation of API key auth without header or query."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test"]},
            "action": {
                "method": "GET",
                "path": "/test",
                "authentication": {
                    "type": "api_key"
                    # Missing header or query
                }
            },
            "response": {"crfs": {"type": "structured"}}
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("API key authentication must specify" in error for error in result.errors)
    
    def test_validate_invalid_validation_rules(self, validator):
        """Test validation with invalid validation rules."""
        # Test invalid type
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test {param}"]},
            "action": {
                "method": "GET",
                "path": "/test",
                "validation_rules": {
                    "param": {
                        "type": "invalid_type"  # Invalid type
                    }
                }
            },
            "response": {"crfs": {"type": "structured"}}
        }
        
        result = validator.validate_completeness(template)
        assert result.is_valid is False
        assert any("Invalid parameter type" in error for error in result.errors)
        
        # Test invalid string format
        template['action']['validation_rules']['param'] = {
            "type": "string",
            "format": "invalid_format"  # Invalid format
        }
        
        result = validator.validate_completeness(template)
        assert result.is_valid is False
        assert any("Invalid string format" in error for error in result.errors)
        
        # Test invalid regex pattern
        template['action']['validation_rules']['param'] = {
            "type": "string",
            "pattern": "[invalid regex"  # Invalid regex
        }
        
        result = validator.validate_completeness(template)
        assert result.is_valid is False
        assert any("Invalid regex pattern" in error for error in result.errors)
    
    def test_validate_invalid_transformations(self, validator):
        """Test validation with invalid transformations."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test"]},
            "action": {
                "method": "GET",
                "path": "/test",
                "transformations": {
                    "param": ["invalid_transform"]  # Invalid transformation
                }
            },
            "response": {"crfs": {"type": "structured"}}
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("Invalid transformation" in error for error in result.errors)
    
    def test_validate_missing_crfs_config(self, validator):
        """Test validation with missing CRFS configuration."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test"]},
            "action": {"method": "GET", "path": "/test"},
            "response": {
                # Missing crfs
            }
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("missing CRFS configuration" in error for error in result.errors)
    
    def test_validate_invalid_crfs_type(self, validator):
        """Test validation with invalid CRFS type."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test"]},
            "action": {"method": "GET", "path": "/test"},
            "response": {
                "crfs": {
                    "type": "invalid_type"  # Invalid CRFS type
                }
            }
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("Invalid CRFS type" in error for error in result.errors)
    
    def test_validate_crfs_default_format_not_in_formats(self, validator):
        """Test validation when default format is not in formats."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test"]},
            "action": {"method": "GET", "path": "/test"},
            "response": {
                "crfs": {
                    "type": "structured",
                    "default_format": "missing_format",
                    "formats": {
                        "structured": {}
                    }
                }
            }
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("Default format" in error and "not found" in error for error in result.errors)
    
    def test_validate_invalid_error_mapping(self, validator):
        """Test validation with invalid error mapping."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test"]},
            "action": {"method": "GET", "path": "/test"},
            "response": {
                "crfs": {"type": "structured"},
                "error_mapping": {
                    "invalid": "Not a valid status code",  # Invalid status code
                    "999": "Status code out of range"  # Out of range
                }
            }
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("Invalid status code format" in error for error in result.errors)
        assert any("Invalid HTTP status code" in error for error in result.errors)
    
    def test_validate_parameter_mapping_mismatch(self, validator):
        """Test validation when parameters don't match between intent and action."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test {param1}"]},
            "action": {
                "method": "GET",
                "path": "/test",
                "parameter_mapping": {
                    "query": "{{parameters.param2}}"  # param2 not in intent
                }
            },
            "response": {"crfs": {"type": "structured"}}
        }
        
        result = validator.validate_completeness(template)
        
        assert result.is_valid is False
        assert any("Parameters in action not defined in intent" in error for error in result.errors)
    
    def test_validate_execution_readiness(self, validator, valid_template):
        """Test execution readiness check."""
        assert validator.validate_execution_readiness(valid_template) is True
        
        # Remove base URL
        del valid_template['action']['integration']
        assert validator.validate_execution_readiness(valid_template) is False
    
    def test_validate_test_mode_compatibility(self, validator, valid_template):
        """Test test mode compatibility validation."""
        result = validator.validate_test_mode_compatibility(valid_template)
        assert result.is_valid is True
        
        # Remove test data
        del valid_template['test_data']
        result = validator.validate_test_mode_compatibility(valid_template)
        assert result.is_valid is False
        assert any("no test_data section" in error for error in result.errors)
    
    def test_validate_test_data_missing_mock_responses(self, validator):
        """Test validation when test data is missing mock responses."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test"]},
            "action": {"method": "GET", "path": "/test"},
            "response": {"crfs": {"type": "structured"}},
            "test_data": {
                "sample_parameters": {"param": "value"}
                # Missing mock_responses
            }
        }
        
        result = validator.validate_test_mode_compatibility(template)
        assert result.is_valid is False
        assert any("missing mock_responses" in error for error in result.errors)
    
    def test_validate_no_test_data_warning(self, validator):
        """Test that missing test data generates a warning."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test"]},
            "action": {"method": "GET", "path": "/test"},
            "response": {"crfs": {"type": "structured"}}
            # No test_data
        }
        
        result = validator.validate_completeness(template)
        assert any("No test data provided" in warning for warning in result.warnings)
    
    def test_validate_environment_variable_syntax(self, validator):
        """Test validation of environment variable syntax in base URL."""
        # Valid syntax
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test"]},
            "action": {
                "method": "GET",
                "path": "/test",
                "integration": {
                    "base_url": "${API_URL:https://default.com}"
                }
            },
            "response": {"crfs": {"type": "structured"}}
        }
        
        result = validator.validate_completeness(template)
        assert not any("Invalid environment variable format" in error for error in result.errors)
        
        # Invalid syntax
        template['action']['integration']['base_url'] = "${invalid-var}"
        result = validator.validate_completeness(template)
        assert any("Invalid environment variable format" in error for error in result.errors)