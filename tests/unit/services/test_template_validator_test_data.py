"""
Unit tests for template validator test data validation.
"""

import pytest
from src.coherence.services.template_validator import (
    UnifiedTemplateValidator,
    ValidationResult,
)


class TestTemplateValidatorTestData:
    """Test template validator test data functionality."""

    @pytest.fixture
    def validator(self):
        """Create validator instance."""
        return UnifiedTemplateValidator()

    def test_validate_test_mode_with_complete_test_data(self, validator):
        """Test validation with complete test data."""
        template = {
            "test_data": {
                "mock_responses": {
                    "success": {"status": 200, "data": {"result": "Success"}},
                    "error": {"status": 400, "error": "Bad Request"},
                },
                "sample_parameters": {
                    "param1": "value1",
                    "param2": 123,
                },
            }
        }

        result = validator.validate_test_mode_compatibility(template)

        assert result.is_valid is True
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validate_test_mode_without_test_data(self, validator):
        """Test validation without test data section."""
        template = {}

        result = validator.validate_test_mode_compatibility(template)

        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "Template has no test_data section" in result.errors[0]

    def test_validate_test_mode_without_mock_responses(self, validator):
        """Test validation without mock responses."""
        template = {
            "test_data": {
                "sample_parameters": {
                    "param1": "value1",
                }
            }
        }

        result = validator.validate_test_mode_compatibility(template)

        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "test_data missing mock_responses" in result.errors[0]

    def test_validate_test_mode_without_success_response(self, validator):
        """Test validation without success mock response."""
        template = {
            "test_data": {
                "mock_responses": {
                    "error": {"status": 400, "error": "Bad Request"},
                },
                "sample_parameters": {
                    "param1": "value1",
                },
            }
        }

        result = validator.validate_test_mode_compatibility(template)

        assert result.is_valid is True
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "No success mock response defined" in result.warnings[0]

    def test_validate_test_mode_without_sample_parameters(self, validator):
        """Test validation without sample parameters."""
        template = {
            "test_data": {
                "mock_responses": {
                    "success": {"status": 200, "data": {"result": "Success"}},
                }
            }
        }

        result = validator.validate_test_mode_compatibility(template)

        assert result.is_valid is True
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "No sample parameters provided" in result.warnings[0]

    def test_validate_completeness_includes_test_data_warning(self, validator):
        """Test that completeness validation warns about missing test data."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test pattern"]},
            "action": {"method": "GET", "path": "/test"},
            "response": {"crfs": {"type": "structured"}},
            # No test_data
        }

        result = validator.validate_completeness(template)

        # Should be valid but with warning
        assert result.is_valid is True
        assert any("test data" in warning for warning in result.warnings)
        assert result.metadata["has_test_data"] is False

    def test_validate_completeness_with_test_data(self, validator):
        """Test that completeness validation recognizes test data."""
        template = {
            "meta": {"key": "test", "endpoint_id": "GET_/test"},
            "intent": {"patterns": ["test pattern"]},
            "action": {"method": "GET", "path": "/test"},
            "response": {"crfs": {"type": "structured"}},
            "test_data": {
                "mock_responses": {"success": {"data": "test"}},
                "sample_parameters": {"param": "value"},
            },
        }

        result = validator.validate_completeness(template)

        assert result.is_valid is True
        assert result.metadata["has_test_data"] is True

    def test_empty_mock_responses_dict(self, validator):
        """Test validation with empty mock_responses dict."""
        template = {
            "test_data": {
                "mock_responses": {},  # Empty dict
                "sample_parameters": {"param": "value"},
            }
        }

        result = validator.validate_test_mode_compatibility(template)

        # Should be valid but might warn about no success response
        assert result.is_valid is True
        assert len(result.warnings) == 1
        assert "No success mock response defined" in result.warnings[0]

    def test_complex_mock_response_structure(self, validator):
        """Test validation with complex mock response structure."""
        template = {
            "test_data": {
                "mock_responses": {
                    "success": {
                        "status": 200,
                        "headers": {"Content-Type": "application/json"},
                        "data": {
                            "items": [
                                {"id": 1, "name": "Item 1"},
                                {"id": 2, "name": "Item 2"},
                            ],
                            "pagination": {
                                "page": 1,
                                "total": 100,
                                "per_page": 10,
                            },
                        },
                    },
                    "not_found": {
                        "status": 404,
                        "error": {"code": "NOT_FOUND", "message": "Resource not found"},
                    },
                    "rate_limited": {
                        "status": 429,
                        "error": "Too many requests",
                        "retry_after": 60,
                    },
                },
                "sample_parameters": {
                    "page": 1,
                    "per_page": 10,
                    "sort": "created_at",
                    "order": "desc",
                },
            }
        }

        result = validator.validate_test_mode_compatibility(template)

        assert result.is_valid is True
        assert len(result.errors) == 0
        assert len(result.warnings) == 0