#!/usr/bin/env python3
"""
Interactive test script for unified template system.
Run this to test the unified template API endpoints.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from src.coherence.template_system.services.template_service import TemplateService
from src.coherence.template_system.unified_generator import UnifiedTemplateGenerator
from src.coherence.models.template import TemplateCategory, TemplateScope


async def test_unified_template_crud():
    """Test the unified template CRUD operations."""
    
    print("🧪 TESTING UNIFIED TEMPLATE CRUD OPERATIONS")
    print("=" * 60)
    
    # Database setup (using the same config as the app)
    DATABASE_URL = "postgresql+asyncpg://postgres:postgres@localhost:5433/coherence"
    engine = create_async_engine(DATABASE_URL)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as db:
        service = TemplateService()
        generator = UnifiedTemplateGenerator()
        
        # 1. Create a test unified template
        print("✅ 1. CREATING UNIFIED TEMPLATE")
        
        sample_template_data = {
            "meta": {
                "key": "test_weather_api",
                "endpoint_id": "GET_/weather/current",
                "version": 1
            },
            "intent": {
                "patterns": ["get weather", "current weather", "weather now"],
                "confidence_threshold": 0.85
            },
            "action": {
                "method": "GET",
                "path": "/weather/current",
                "auth": {"type": "api_key", "header": "X-API-Key"},
                "retries": {"max_attempts": 3, "backoff": "exponential"}
            },
            "parameters": {
                "schema": {
                    "location": {"type": "string", "required": True, "description": "City name"},
                    "units": {"type": "string", "enum": ["metric", "imperial"], "default": "metric"}
                },
                "ui_fields": {
                    "location": {"type": "text", "label": "Location", "required": True},
                    "units": {"type": "select", "label": "Units", "options": [
                        {"value": "metric", "label": "Metric"},
                        {"value": "imperial", "label": "Imperial"}
                    ]}
                }
            },
            "prompts": {
                "system": "You help users get weather information.",
                "extraction": {
                    "location": "What location would you like weather for?",
                    "units": "Would you like metric or imperial units?"
                },
                "completion": "Here's the current weather for {{location}}..."
            },
            "response": {
                "crfs": {
                    "type": "structured",
                    "sections": [
                        {"type": "text", "style": "heading", "content": "Weather for {{location}}"},
                        {"type": "text", "content": "Temperature: {{result.temperature}}°"},
                        {"type": "text", "content": "Conditions: {{result.conditions}}"}
                    ]
                },
                "error_mapping": {"404": "Location not found", "500": "Weather service unavailable"}
            },
            "docs_config": {
                "description": "Get current weather for any location",
                "examples": ["Get weather for New York", "Current weather in London"]
            }
        }
        
        # Validate the template
        errors = generator.validate_unified_template(sample_template_data)
        if errors:
            print(f"❌ Template validation failed: {errors}")
            return
        
        # Create the template
        template = await service.create_template(
            db=db,
            key=sample_template_data["meta"]["key"],
            category=TemplateCategory.UNIFIED,
            scope=TemplateScope.TENANT,
            tenant_id=None,  # Use None for global template
            description="Test unified weather template",
            endpoint_id=sample_template_data["meta"]["endpoint_id"],
            intent_config=sample_template_data["intent"],
            action_config=sample_template_data["action"],
            ui_fields=sample_template_data["parameters"]["ui_fields"],
            prompts=sample_template_data["prompts"],
            docs_config=sample_template_data["docs_config"],
            parameters=sample_template_data["parameters"]["schema"],
            response_format=sample_template_data["response"]["crfs"]
        )
        
        print(f"   📋 Created template: {template.key} (ID: {template.id})")
        print(f"   🎯 Intent patterns: {len(template.intent_config['patterns'])}")
        print(f"   📝 Parameters: {len(template.parameters)}")
        print(f"   🎨 UI fields: {len(template.ui_fields)}")
        print()
        
        # 2. Read the template back
        print("✅ 2. READING TEMPLATE")
        retrieved_template = await service.get_template_by_id(db, template.id)
        if retrieved_template:
            print(f"   📖 Retrieved: {retrieved_template.key}")
            print(f"   📅 Created: {retrieved_template.created_at}")
            print(f"   🔄 Version: {retrieved_template.version}")
        print()
        
        # 3. List templates
        print("✅ 3. LISTING TEMPLATES")
        templates = await service.list_templates(db, limit=10)
        print(f"   📚 Found {len(templates)} templates:")
        for tmpl in templates:
            print(f"      - {tmpl.key} ({tmpl.category}) [scope: {tmpl.scope}]")
        print()
        
        # 4. Test UI form generation
        print("✅ 4. TESTING UI FORM GENERATION")
        from src.coherence.template_system.ui_generator import UIFormGenerator
        
        unified_data = {
            "meta": {"key": template.key},
            "parameters": {
                "schema": template.parameters,
                "ui_fields": template.ui_fields
            },
            "prompts": template.prompts,
            "docs_config": template.docs_config
        }
        
        form_config = UIFormGenerator.generate_form_config(unified_data)
        print(f"   📱 Form title: {form_config['title']}")
        print(f"   🔧 Fields generated: {len(form_config['fields'])}")
        for field_name, field_config in form_config['fields'].items():
            print(f"      - {field_name}: {field_config['type']} ({'required' if field_config.get('required') else 'optional'})")
        print()
        
        # 5. Test CRFS formatting
        print("✅ 5. TESTING CRFS RESPONSE FORMATTING")
        from src.coherence.template_system.crfs_formatter import CRFSFormatter
        
        crfs_formatter = CRFSFormatter.from_unified_template(unified_data)
        sample_response = {
            "result": {
                "temperature": "22",
                "conditions": "Partly cloudy",
                "humidity": "65%"
            },
            "location": "New York"
        }
        
        formatted = crfs_formatter.format(sample_response)
        print("   📄 Formatted response:")
        print("   " + "\n   ".join(formatted.split('\n')))
        print()
        
        # 6. Clean up - delete the test template
        print("✅ 6. CLEANING UP")
        deleted = await service.delete_template(db, template.id)
        if deleted:
            print(f"   🗑️  Deleted test template: {template.key}")
        print()
        
        print("🎉 ALL UNIFIED TEMPLATE TESTS PASSED!")
        print("💡 The unified template system is working perfectly!")


if __name__ == "__main__":
    asyncio.run(test_unified_template_crud())