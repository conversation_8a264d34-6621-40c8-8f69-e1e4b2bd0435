"""
Unit tests for retry and backoff implementation.
"""

from unittest.mock import AsyncMock, call, patch

import pytest

from src.coherence.openapi_adapter.retry import (
    BackoffStrategy,
    RetryableError,
    RetryError,
    RetryHandler,
    with_retry,
)


class TestRetryHandler:
    """Tests for the RetryHandler class."""
    
    @pytest.mark.asyncio
    async def test_successful_execution(self):
        """Test successful execution doesn't retry."""
        mock_func = AsyncMock(return_value="success")
        retry_handler = RetryHandler(max_attempts=3)
        
        result = await retry_handler.execute(mock_func, "arg1", kwarg1="value")
        
        assert result == "success"
        mock_func.assert_called_once_with("arg1", kwarg1="value")
    
    @pytest.mark.asyncio
    async def test_retry_on_retryable_exception(self):
        """Test retries on RetryableError."""
        # Mock function that fails twice then succeeds
        mock_func = AsyncMock(side_effect=[
            RetryableError("retry me"),
            Retryable<PERSON>rror("retry me again"),
            "success"
        ])
        
        # Use constant backoff for predictable timing
        retry_handler = RetryHandler(
            max_attempts=3,
            backoff_strategy=BackoffStrategy.CONSTANT,
            base_delay=0.01,  # Very short delay for testing
            jitter=False,
        )
        
        # Mock sleep to avoid actual delays
        with patch("asyncio.sleep", AsyncMock()) as mock_sleep:
            result = await retry_handler.execute(mock_func)
        
        assert result == "success"
        assert mock_func.call_count == 3
        assert mock_sleep.call_count == 2  # Sleep called twice for retries
    
    @pytest.mark.asyncio
    async def test_max_attempts_exceeded(self):
        """Test RetryError raised when max attempts exceeded."""
        # Mock function that always fails
        mock_func = AsyncMock(side_effect=RetryableError("always fail"))
        
        retry_handler = RetryHandler(
            max_attempts=3,
            backoff_strategy=BackoffStrategy.CONSTANT,
            base_delay=0.01,
            jitter=False,
        )
        
        # Mock sleep to avoid actual delays
        with patch("asyncio.sleep", AsyncMock()):
            with pytest.raises(RetryError) as excinfo:
                await retry_handler.execute(mock_func)
        
        assert "All 3 retry attempts failed" in str(excinfo.value)
        assert mock_func.call_count == 3
    
    @pytest.mark.asyncio
    async def test_non_retryable_exception(self):
        """Test non-retryable exceptions are raised immediately."""
        # Mock function that raises a non-retryable exception
        mock_func = AsyncMock(side_effect=ValueError("not retryable"))
        
        retry_handler = RetryHandler(max_attempts=3)
        
        with pytest.raises(ValueError) as excinfo:
            await retry_handler.execute(mock_func)
        
        assert "not retryable" in str(excinfo.value)
        mock_func.assert_called_once()  # Only called once, no retries
    
    @pytest.mark.asyncio
    async def test_http_status_code_retries(self):
        """Test retries based on HTTP status codes with a simplified approach."""
        # Create counter to track calls
        counter = {"count": 0}
        
        # Define custom error class for testing
        class RetryableTestError(Exception):
            def __init__(self, status):
                self.status = status
                
        async def test_func():
            counter["count"] += 1
            if counter["count"] == 1:
                # First call: retryable error
                raise RetryableTestError(429)  # Too Many Requests
            elif counter["count"] == 2:
                # Second call: also retryable error
                raise RetryableTestError(503)  # Service Unavailable
            else:
                # Third call: success
                return "success"
                
        retry_handler = RetryHandler(
            max_attempts=3,
            backoff_strategy=BackoffStrategy.CONSTANT,
            base_delay=0.01,
            jitter=False,
        )
        
        # Mock sleep to avoid actual delays
        with patch("asyncio.sleep", AsyncMock()):
            result = await retry_handler.execute(test_func)
            
        # Verify the function was called exactly 3 times
        assert counter["count"] == 3
        # Verify the result
        assert result == "success"


class TestBackoffStrategies:
    """Tests for different backoff strategies."""
    
    def test_constant_backoff(self):
        """Test constant backoff strategy."""
        retry_handler = RetryHandler(
            backoff_strategy=BackoffStrategy.CONSTANT,
            base_delay=2.0,
            jitter=False,
        )
        
        # Should always return base_delay
        assert retry_handler._calculate_backoff(1) == 2.0
        assert retry_handler._calculate_backoff(2) == 2.0
        assert retry_handler._calculate_backoff(3) == 2.0
    
    def test_linear_backoff(self):
        """Test linear backoff strategy."""
        retry_handler = RetryHandler(
            backoff_strategy=BackoffStrategy.LINEAR,
            base_delay=1.0,
            jitter=False,
        )
        
        # Should increase linearly
        assert retry_handler._calculate_backoff(1) == 1.0
        assert retry_handler._calculate_backoff(2) == 2.0
        assert retry_handler._calculate_backoff(3) == 3.0
    
    def test_exponential_backoff(self):
        """Test exponential backoff strategy."""
        retry_handler = RetryHandler(
            backoff_strategy=BackoffStrategy.EXPONENTIAL,
            base_delay=1.0,
            jitter=False,
        )
        
        # Should increase exponentially (base_delay * 2^(attempt-1))
        assert retry_handler._calculate_backoff(1) == 1.0
        assert retry_handler._calculate_backoff(2) == 2.0
        assert retry_handler._calculate_backoff(3) == 4.0
        assert retry_handler._calculate_backoff(4) == 8.0
    
    def test_fibonacci_backoff(self):
        """Test Fibonacci backoff strategy."""
        retry_handler = RetryHandler(
            backoff_strategy=BackoffStrategy.FIBONACCI,
            base_delay=1.0,
            jitter=False,
        )
        
        # Should follow Fibonacci sequence
        assert retry_handler._calculate_backoff(1) == 1.0
        assert retry_handler._calculate_backoff(2) == 1.0
        assert retry_handler._calculate_backoff(3) == 2.0
        assert retry_handler._calculate_backoff(4) == 3.0
        assert retry_handler._calculate_backoff(5) == 5.0
    
    def test_max_delay(self):
        """Test maximum delay cap."""
        retry_handler = RetryHandler(
            backoff_strategy=BackoffStrategy.EXPONENTIAL,
            base_delay=1.0,
            max_delay=5.0,
            jitter=False,
        )
        
        # Should be capped at max_delay
        assert retry_handler._calculate_backoff(1) == 1.0
        assert retry_handler._calculate_backoff(3) == 4.0
        assert retry_handler._calculate_backoff(4) == 5.0  # Capped at max_delay
        assert retry_handler._calculate_backoff(5) == 5.0  # Capped at max_delay


class TestWithRetryDecorator:
    """Tests for the with_retry decorator."""
    
    @pytest.mark.asyncio
    async def test_decorator(self):
        """Test with_retry decorator."""
        # Create a mock function to decorate
        mock_func = AsyncMock(side_effect=[
            RetryableError("retry me"),
            "success"
        ])
        
        # Decorate the function
        decorated_func = with_retry(
            max_attempts=3,
            backoff_strategy="constant",
            base_delay=0.01,
            jitter=False,
        )(mock_func)
        
        # Mock sleep to avoid actual delays
        with patch("asyncio.sleep", AsyncMock()) as mock_sleep:
            result = await decorated_func("arg1", kwarg1="value")
        
        assert result == "success"
        assert mock_func.call_count == 2
        assert mock_sleep.call_count == 1
        
        # Verify all calls were made with the same arguments
        mock_func.assert_has_calls([
            call("arg1", kwarg1="value"),
            call("arg1", kwarg1="value"),
        ])