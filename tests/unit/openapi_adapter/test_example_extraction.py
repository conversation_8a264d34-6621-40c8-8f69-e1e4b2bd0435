"""
Tests for the OpenAPI example extraction functionality.

These tests verify that:
1. Examples are properly extracted from parameter descriptions
2. Examples are properly extracted from OpenAPI schemas
3. Parameter extraction includes example and format information
4. Template generation includes rich parameter information
"""

import uuid
from unittest.mock import AsyncMock, MagicMock

import pytest

from src.coherence.openapi_adapter.action_generator import ActionGenerator


class TestExampleExtraction:
    """Test suite for OpenAPI example extraction functionality."""

    def test_extract_example_from_description(self):
        """Test extracting examples from parameter descriptions."""
        # Create an ActionGenerator instance
        db_mock = AsyncMock()
        generator = ActionGenerator(db_mock)
        
        # Test various description patterns
        assert generator._extract_example_value_from_description("query", "Search query (example: covid)", "string") == "covid"
        assert generator._extract_example_value_from_description("max_results", "Maximum results to return, e.g. 50", "integer") == "50"
        assert generator._extract_example_value_from_description("include_images", "Include images in results, for example: true", "boolean") == "true"
        assert generator._extract_example_value_from_description("format", "Response format such as json", "string") == "json"
        
        # Test type-based examples
        assert generator._extract_example_value_from_description("user_id", "User identifier", "string") == "123e4567-e89b-12d3-a456-426614174000"
        assert generator._extract_example_value_from_description("email", "Email address", "string") == "<EMAIL>"
        assert generator._extract_example_value_from_description("api_key", "API key for authentication", "string") == "api_key_12345"
        assert generator._extract_example_value_from_description("count", "Count of items", "integer") == "42"
        assert generator._extract_example_value_from_description("price", "Product price", "number") == "42.5"

    def test_extract_examples_from_schema(self):
        """Test extracting examples from OpenAPI schema objects."""
        # Create an ActionGenerator instance
        db_mock = AsyncMock()
        generator = ActionGenerator(db_mock)
        
        # Test direct example in schema
        schema_with_example = {
            "type": "string",
            "description": "User status",
            "example": "active"
        }
        assert generator._extract_examples_from_schema(schema_with_example, "status", "string") == "active"
        
        # Test examples collection in schema
        schema_with_examples = {
            "type": "string",
            "description": "User role",
            "examples": {
                "admin": "admin",
                "user": "user"
            }
        }
        assert generator._extract_examples_from_schema(schema_with_examples, "role", "string") == "admin"
        
        # Test enum values in schema
        schema_with_enum = {
            "type": "string",
            "description": "Order status",
            "enum": ["pending", "processing", "completed", "cancelled"]
        }
        assert generator._extract_examples_from_schema(schema_with_enum, "order_status", "string") == "pending"
        
        # Test default value in schema
        schema_with_default = {
            "type": "integer",
            "description": "Page size",
            "default": 10
        }
        assert generator._extract_examples_from_schema(schema_with_default, "page_size", "integer") == "10"
        
        # Test fallback to description
        schema_with_desc_only = {
            "type": "string",
            "description": "Filter by status (e.g. active)"
        }
        assert generator._extract_examples_from_schema(schema_with_desc_only, "filter", "string") == "active"
        
        # Test fallback to parameter name
        schema_empty = {
            "type": "string"
        }
        assert generator._extract_examples_from_schema(schema_empty, "sort_by", "string") == "example_sort_by"

    def test_enhanced_parameter_extraction(self):
        """Test the enhanced parameter extraction with examples and format information."""
        # Create an ActionGenerator instance
        db_mock = AsyncMock()
        generator = ActionGenerator(db_mock)
        
        # Sample OpenAPI operation with various parameter types
        endpoint_spec = {
            "operationId": "searchItems",
            "summary": "Search for items",
            "description": "Search for items based on query parameters",
            "parameters": [
                {
                    "name": "q",
                    "in": "query",
                    "description": "Search query",
                    "required": True,
                    "schema": {
                        "type": "string",
                        "example": "laptop"
                    }
                },
                {
                    "name": "limit",
                    "in": "query",
                    "description": "Maximum number of results to return",
                    "schema": {
                        "type": "integer",
                        "minimum": 1,
                        "maximum": 100,
                        "default": 10
                    }
                },
                {
                    "name": "category",
                    "in": "query",
                    "description": "Filter by category",
                    "schema": {
                        "type": "string",
                        "enum": ["electronics", "books", "clothing"]
                    }
                },
                {
                    "name": "id",
                    "in": "path",
                    "required": True,
                    "description": "Item ID",
                    "schema": {
                        "type": "string",
                        "format": "uuid"
                    }
                }
            ],
            "requestBody": {
                "required": False,
                "content": {
                    "application/json": {
                        "schema": {
                            "type": "object",
                            "properties": {
                                "name": {
                                    "type": "string",
                                    "description": "Filter by name",
                                    "minLength": 3,
                                    "maxLength": 50
                                },
                                "price_range": {
                                    "type": "object",
                                    "properties": {
                                        "min": {
                                            "type": "number",
                                            "minimum": 0
                                        },
                                        "max": {
                                            "type": "number"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        # Extract parameters
        parameters = generator._extract_parameters(endpoint_spec)
        
        # Check that the parameters include examples and format information
        param_dict = {param["name"]: param for param in parameters}
        
        # Check query parameter 'q'
        assert "q" in param_dict
        assert param_dict["q"]["example"] == "laptop"
        assert param_dict["q"]["required"] is True
        
        # Check query parameter 'limit'
        assert "limit" in param_dict
        assert param_dict["limit"]["minimum"] == 1
        assert param_dict["limit"]["maximum"] == 100
        assert param_dict["limit"]["example"] == "10"  # From default value
        
        # Check query parameter 'category'
        assert "category" in param_dict
        assert param_dict["category"]["enum"] == ["electronics", "books", "clothing"]
        assert param_dict["category"]["example"] == "electronics"  # First enum value
        
        # Check path parameter 'id'
        assert "id" in param_dict
        assert param_dict["id"]["format"] == "uuid"
        assert param_dict["id"]["required"] is True
        
        # Check body parameter 'name'
        assert "name" in param_dict
        assert param_dict["name"]["minLength"] == 3
        assert param_dict["name"]["maxLength"] == 50
        assert param_dict["name"]["location"] == "body"
        
        # Verify all parameters from the endpoint spec are in our results
        for param in endpoint_spec.get("parameters", []):
            param_name = param.get("name")
            assert param_name in param_dict, f"Parameter {param_name} from endpoint_spec not found in extracted parameters"
            
        # Verify at least one body parameter from the request body properties
        request_body_props = endpoint_spec.get("requestBody", {}).get("content", {}).get("application/json", {}).get("schema", {}).get("properties", {})
        body_params_found = [prop for prop in request_body_props.keys() if prop in param_dict]
        
        # Check that we found at least one body parameter
        assert len(body_params_found) > 0, f"No body parameters were found. Expected at least one of: {list(request_body_props.keys())}"


@pytest.mark.asyncio
class TestTemplateGeneration:
    """Test suite for template generation with enhanced example information."""
    
    async def test_action_template_generation(self):
        """Test generating action templates with rich parameter information."""
        # Create a mock database session
        db_mock = AsyncMock()
        
        # Create mock endpoint and integration objects
        endpoint_id = uuid.uuid4()
        tenant_id = uuid.uuid4()
        integration_id = uuid.uuid4()
        
        # Create mock objects with required attributes and methods
        endpoint_mock = MagicMock()
        endpoint_mock.id = endpoint_id
        endpoint_mock.integration_id = integration_id
        endpoint_mock.path = "/products"
        endpoint_mock.method = "GET"
        endpoint_mock.operation_id = "getProducts"
        
        integration_mock = MagicMock()
        integration_mock.id = integration_id
        integration_mock.tenant_id = tenant_id
        integration_mock.name = "Product API"
        integration_mock.base_url = "https://api.example.com"
        integration_mock.openapi_spec = {
            "openapi": "3.0.0",
            "info": {
                "title": "Product API",
                "version": "1.0.0"
            },
            "paths": {
                "/products": {
                    "get": {
                        "operationId": "getProducts",
                        "summary": "Get products",
                        "description": "Get a list of products",
                        "parameters": [
                            {
                                "name": "category",
                                "in": "query",
                                "description": "Filter by category",
                                "schema": {
                                    "type": "string",
                                    "enum": ["electronics", "books", "clothing"]
                                }
                            },
                            {
                                "name": "limit",
                                "in": "query",
                                "description": "Maximum number of results to return",
                                "schema": {
                                    "type": "integer",
                                    "minimum": 1,
                                    "maximum": 100,
                                    "default": 10
                                }
                            }
                        ],
                        "responses": {
                            "200": {
                                "description": "Successful response",
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "id": {
                                                        "type": "string",
                                                        "format": "uuid"
                                                    },
                                                    "name": {
                                                        "type": "string"
                                                    },
                                                    "price": {
                                                        "type": "number"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        # Set up db.get to return our mock objects
        db_mock.get = AsyncMock()
        db_mock.get.side_effect = lambda model, id: endpoint_mock if id == endpoint_id else integration_mock
        
        # Create ActionGenerator instance
        generator = ActionGenerator(db_mock)
        
        # Generate action templates
        templates = await generator._generate_action_templates(endpoint_id, tenant_id)
        
        # Check that the templates were generated
        assert len(templates) == 3
        
        # Get the primary template, fallback template, and intent template
        primary_template = templates[0]
        templates[1]
        intent_template = templates[2]
        
        # Check primary template
        assert primary_template["category"] == "response_gen"
        assert "action_config" in primary_template["actions"] or "api_action" in primary_template["actions"]
        
        # Verify we have parameters in the generated template
        parameters = primary_template["parameters"]
        assert len(parameters) > 0, "No parameters found in generated template"
        
        # Get the expected parameters from our test mock data
        mock_parameters = integration_mock.openapi_spec["paths"]["/products"]["get"]["parameters"]
        parameter_names = [p.get("name") for p in mock_parameters]
        
        # Check that all expected parameters are included
        for param_name in parameter_names:
            assert param_name in parameters, f"Expected parameter {param_name} not found in template"
            
            # Check that each parameter has the enhanced information
            param = parameters[param_name]
            assert "example" in param, f"Parameter {param_name} is missing example value"
            
            # Check for additional schema info where applicable
            mock_param = next((p for p in mock_parameters if p.get("name") == param_name), None)
            if mock_param and "schema" in mock_param:
                schema = mock_param["schema"]
                
                # Check for enum values
                if "enum" in schema:
                    assert "enum" in param, f"Parameter {param_name} should have enum values"
                    assert param["enum"] == schema["enum"], f"Enum values don't match for {param_name}"
                    assert param["example"] == str(schema["enum"][0]), f"Example should be first enum value for {param_name}"
                
                # Check for min/max constraints
                if "minimum" in schema:
                    assert "minimum" in param, f"Parameter {param_name} should have minimum constraint"
                
                # Check for default values
                if "default" in schema:
                    assert param["example"] == str(schema["default"]), f"Example should be default value for {param_name}"
        
        # Check that the intent template was generated
        intent_body = intent_template["body"]
        assert len(intent_body) > 0, "Intent template body is empty"
        
        # Verify that intent body includes standard sections
        required_sections = ["block system_instruction", "block intent_patterns", "block parameters", "block examples"]
        for section in required_sections:
            assert section in intent_body, f"Missing required section {section} in intent template"
            
        # Check that parameter documentation is included
        for param_name in parameter_names:
            assert param_name in intent_body, f"Parameter {param_name} not found in intent template documentation"
            
        # Check that examples section uses parameter values
        examples_section = intent_body.split("{% block examples %}")[1].split("{% endblock %}")[0].strip()
        assert examples_section, "Examples section is empty"
        
        # Each example should include the operation ID
        assert "getProducts" in examples_section, "Operation ID not found in examples"
        
        # Verify examples are being used (without hardcoding exact values)
        # Each parameter should appear in the examples with a value
        for param_name in parameter_names:
            assert f"{param_name}=" in examples_section, f"Parameter {param_name} not found in examples"