"""
Test the structure of API action templates.

This module tests the structure and format of API action templates.
"""

import uuid
from unittest.mock import MagicMock

from jinja2 import Template as Jinja2Template

from src.coherence.models.template import Template, TemplateCategory


def test_template_structure():
    """Test that API action templates have the correct structure."""
    # Create a sample API action template
    template_id = uuid.uuid4()
    tenant_id = uuid.uuid4()
    
    template = Template(
        id=template_id,
        key="weather_api_getforecast",
        category=TemplateCategory.RESPONSE_GEN,
        body="The current temperature in {{ parameters.location.city }} is {{ results.weather_api.current_temp }}{{ results.weather_api.unit }}.",
        tenant_id=tenant_id,
        scope="tenant",
        actions=[{
            "api_key": "weather_api",
            "endpoint": "/v1/forecast",
            "method": "GET",
            "parameter_mapping": {
                "latitude": "{{ parameters.location.latitude }}",
                "longitude": "{{ parameters.location.longitude }}"
            },
            "response_mapping": {
                "current_temp": "{{ response.current.temperature_2m }}",
                "unit": "{{ response.current_units.temperature_2m }}"
            },
            "authentication": {
                "type": "api_key",
                "location": "header",
                "name": "X-API-Key",
                "value": "{{ credentials.weather_api }}"
            },
            "error_handling": {
                "timeout_ms": 3000,
                "retries": 2,
                "fallback_template": "weather_api_getforecast_fallback"
            }
        }]
    )
    
    # Assertions for the template structure
    assert template.key == "weather_api_getforecast"
    assert template.category == TemplateCategory.RESPONSE_GEN
    assert template.actions is not None
    assert len(template.actions) == 1
    
    # Assertions for the action structure
    action = template.actions[0]
    assert action["api_key"] == "weather_api"
    assert action["endpoint"] == "/v1/forecast"
    assert action["method"] == "GET"
    
    # Check parameter mapping
    assert "latitude" in action["parameter_mapping"]
    assert "longitude" in action["parameter_mapping"]
    assert "{{ parameters.location.latitude }}" in action["parameter_mapping"]["latitude"]
    
    # Check response mapping
    assert "current_temp" in action["response_mapping"]
    assert "unit" in action["response_mapping"]
    
    # Check authentication
    assert action["authentication"]["type"] == "api_key"
    assert action["authentication"]["location"] == "header"
    
    # Check error handling
    assert action["error_handling"]["timeout_ms"] == 3000
    assert action["error_handling"]["retries"] == 2
    assert action["error_handling"]["fallback_template"] == "weather_api_getforecast_fallback"


def test_fallback_template_structure():
    """Test that fallback templates have the correct structure."""
    # Create a sample fallback template
    template_id = uuid.uuid4()
    tenant_id = uuid.uuid4()
    
    fallback_template = Template(
        id=template_id,
        key="weather_api_getforecast_fallback",
        category=TemplateCategory.ERROR_HANDLER,
        body="I'm sorry, I couldn't get the weather information for {{ parameters.location.city }} due to a technical issue.",
        tenant_id=tenant_id,
        scope="tenant",
        actions=[]
    )
    
    # Assertions for the fallback template structure
    assert fallback_template.key == "weather_api_getforecast_fallback"
    assert fallback_template.category == TemplateCategory.ERROR_HANDLER
    assert len(fallback_template.actions) == 0
    assert "technical issue" in fallback_template.body
    

def test_template_rendering(monkeypatch):
    """Test that template rendering works correctly with API results."""
    # Mock template renderer
    mock_render = MagicMock(return_value="The current temperature in New York is 25.5°C.")
    monkeypatch.setattr("src.coherence.template_system.engine.renderer.TemplateRenderer.render", mock_render)
    
    # Create test data
    parameters = {
        "location": {
            "city": "New York",
            "latitude": 40.7128,
            "longitude": -74.0060
        }
    }
    
    api_result = {
        "current_temp": 25.5,
        "unit": "°C"
    }
    
    # Create context data for rendering
    context = {
        "parameters": parameters,
        "results": {
            "weather_api": api_result
        }
    }
    
    # Call the mocked renderer
    from src.coherence.template_system.engine.renderer import TemplateRenderer
    renderer = TemplateRenderer()
    result = renderer.render("test_template", context)
    
    # Assert the result
    assert result == "The current temperature in New York is 25.5°C."
    assert mock_render.called_once_with("test_template", context)


def test_jinja2_template_rendering():
    """Test direct Jinja2 template rendering for parameter and response mapping."""
    # Test parameter mapping
    param_template = "{{ parameters.location.latitude }}"
    param_data = {
        "parameters": {
            "location": {
                "latitude": 40.7128,
                "longitude": -74.0060
            }
        }
    }
    
    jinja_template = Jinja2Template(param_template)
    result = jinja_template.render(**param_data)
    assert result == "40.7128"
    
    # Test response mapping
    response_template = "{{ response.current.temperature_2m }}"
    response_data = {
        "response": {
            "current": {
                "temperature_2m": 25.5,
                "humidity": 45
            },
            "current_units": {
                "temperature_2m": "°C"
            }
        }
    }
    
    jinja_template = Jinja2Template(response_template)
    result = jinja_template.render(**response_data)
    assert result == "25.5"


def test_template_filter_support():
    """Test that templates support Jinja2 filters."""
    # Test uppercase filter
    template_str = "{{ value|upper }}"
    data = {"value": "hello"}
    
    jinja_template = Jinja2Template(template_str)
    result = jinja_template.render(**data)
    assert result == "HELLO"
    
    # Test join filter for arrays
    template_str = "{{ array|join(', ') }}"
    data = {"array": [1, 2, 3, 4, 5]}
    
    jinja_template = Jinja2Template(template_str)
    result = jinja_template.render(**data)
    assert result == "1, 2, 3, 4, 5"


def test_template_default_values():
    """Test that templates handle missing values with defaults."""
    # Template with default for missing value
    template_str = "{{ missing_value|default('N/A') }}"
    data = {}
    
    jinja_template = Jinja2Template(template_str)
    result = jinja_template.render(**data)
    assert result == "N/A"
    
    # Template with default for nested missing value
    template_str = "{{ response.data.value if response.data is defined else 'N/A' }}"
    data = {"response": {}}
    
    jinja_template = Jinja2Template(template_str)
    result = jinja_template.render(**data)
    assert result == "N/A"