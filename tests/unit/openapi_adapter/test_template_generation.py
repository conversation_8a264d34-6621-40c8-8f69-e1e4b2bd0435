"""
Tests for OpenAPI adapter template generation functionality.

This module tests the automatic generation of action templates 
from OpenAPI specifications.
"""

import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.coherence.models.template import TemplateCategory
from src.coherence.openapi_adapter.adapter import OpenAPIAdapter


class TestTemplateGeneration:
    @pytest.fixture
    def mock_db(self):
        db = AsyncMock()
        db.execute = AsyncMock()
        db.commit = AsyncMock()
        db.sync_execute = AsyncMock()
        return db
        
    @pytest.fixture
    def sample_openapi_spec(self):
        return {
            "openapi": "3.0.0",
            "info": {
                "title": "Weather API",
                "version": "1.0.0",
                "description": "API for accessing weather forecasts"
            },
            "servers": [
                {
                    "url": "https://api.open-meteo.com"
                }
            ],
            "paths": {
                "/v1/forecast": {
                    "get": {
                        "operationId": "getForecast",
                        "summary": "Get weather forecast",
                        "description": "Get weather forecast for a specific location",
                        "parameters": [
                            {
                                "name": "latitude",
                                "in": "query",
                                "required": True,
                                "schema": {"type": "number"},
                                "description": "Latitude coordinate"
                            },
                            {
                                "name": "longitude",
                                "in": "query",
                                "required": True,
                                "schema": {"type": "number"},
                                "description": "Longitude coordinate"
                            },
                            {
                                "name": "daily",
                                "in": "query",
                                "required": False,
                                "schema": {"type": "string"},
                                "description": "Daily parameters to include in output"
                            },
                            {
                                "name": "timezone",
                                "in": "query",
                                "required": False,
                                "schema": {"type": "string"},
                                "description": "Time zone for output"
                            }
                        ],
                        "responses": {
                            "200": {
                                "description": "Successful response",
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "object",
                                            "properties": {
                                                "current": {
                                                    "type": "object",
                                                    "properties": {
                                                        "temperature_2m": {"type": "number"},
                                                        "humidity": {"type": "number"}
                                                    }
                                                },
                                                "current_units": {
                                                    "type": "object",
                                                    "properties": {
                                                        "temperature_2m": {"type": "string"}
                                                    }
                                                },
                                                "daily": {
                                                    "type": "object",
                                                    "properties": {
                                                        "temperature_2m_max": {
                                                            "type": "array",
                                                            "items": {"type": "number"}
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            "400": {
                                "description": "Bad request",
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "object",
                                            "properties": {
                                                "error": {"type": "string"}
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                "/v1/air-quality": {
                    "get": {
                        "operationId": "getAirQuality",
                        "summary": "Get air quality data",
                        "parameters": [
                            {
                                "name": "latitude",
                                "in": "query",
                                "required": True,
                                "schema": {"type": "number"}
                            },
                            {
                                "name": "longitude",
                                "in": "query",
                                "required": True,
                                "schema": {"type": "number"}
                            }
                        ],
                        "responses": {
                            "200": {
                                "description": "Successful response",
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "object",
                                            "properties": {
                                                "aqi": {"type": "number"},
                                                "pm25": {"type": "number"}
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
    @pytest.mark.asyncio
    @patch('src.coherence.openapi_adapter.adapter.OpenAPIAdapter._generate_auth_config')
    async def test_generate_action_templates(self, mock_auth_config, mock_db, sample_openapi_spec):
        """Test generating action templates from an OpenAPI spec."""
        # Setup
        adapter = OpenAPIAdapter(mock_db)
        
        # Create a mock integration record
        integration_id = uuid.uuid4()
        tenant_id = uuid.uuid4()
        api_key = "weather_api"
        
        # Mock the integration retrieval
        mock_integration = MagicMock()
        mock_integration.id = integration_id
        mock_integration.openapi_spec = sample_openapi_spec
        mock_integration.name = "Weather API"
        mock_db.get.return_value = mock_integration
        
        # Mock the endpoint query results
        mock_forecast_endpoint = MagicMock()
        mock_forecast_endpoint.id = uuid.uuid4()
        mock_forecast_endpoint.path = "/v1/forecast"
        mock_forecast_endpoint.method = "GET"
        mock_forecast_endpoint.operation_id = "getForecast"
        
        mock_air_quality_endpoint = MagicMock()
        mock_air_quality_endpoint.id = uuid.uuid4()
        mock_air_quality_endpoint.path = "/v1/air-quality"
        mock_air_quality_endpoint.method = "GET"
        mock_air_quality_endpoint.operation_id = "getAirQuality"
        
        # Create a mock result object that is not a coroutine
        mock_result = MagicMock()
        mock_result.fetchall.return_value = [mock_forecast_endpoint, mock_air_quality_endpoint]
        mock_db.execute.return_value = mock_result
        
        # Make _generate_auth_config return a mock auth config dictionary directly
        mock_auth_config.return_value = {
            "type": "api_key",
            "location": "header",
            "name": "X-API-Key",
            "value": "{{ credentials.weather_api }}"
        }
        
        # Execute
        templates = await adapter.generate_action_templates(
            integration_id=integration_id,
            api_key=api_key,
            tenant_id=tenant_id
        )
        
        # Assert
        assert len(templates) == 4  # 2 main templates + 2 fallback templates
        
        # Group templates by key to check main and fallback pairs
        template_dict = {t["key"]: t for t in templates}
        
        # Verify forecast template
        forecast_template = template_dict.get("weather_api_getforecast")
        assert forecast_template is not None
        assert forecast_template["category"] == TemplateCategory.RESPONSE_GEN
        assert forecast_template["tenant_id"] == tenant_id
        assert len(forecast_template["actions"]) == 1
        
        # Verify forecast action config
        forecast_action = forecast_template["actions"][0]
        assert forecast_action["api_key"] == "weather_api"
        assert forecast_action["endpoint"] == "/v1/forecast"
        assert forecast_action["method"] == "GET"
        
        # Verify parameter mapping
        param_mapping = forecast_action["parameter_mapping"]
        assert "latitude" in param_mapping
        assert "longitude" in param_mapping
        assert "daily" in param_mapping
        assert "timezone" in param_mapping
        
        # Verify response mapping
        response_mapping = forecast_action["response_mapping"]
        # Check for common fields that the generator would create
        assert "current" in response_mapping
        
        # Verify air quality template exists
        air_quality_template = template_dict.get("weather_api_getairquality")
        assert air_quality_template is not None
        
        # Verify fallback templates
        forecast_fallback = template_dict.get("weather_api_getforecast_fallback")
        assert forecast_fallback is not None
        assert forecast_fallback["category"] == TemplateCategory.ERROR_HANDLER
        assert len(forecast_fallback["actions"]) == 0  # No actions in fallback
        
        air_quality_fallback = template_dict.get("weather_api_getairquality_fallback")
        assert air_quality_fallback is not None
        
    @pytest.mark.asyncio
    @patch('src.coherence.openapi_adapter.adapter.OpenAPIAdapter._generate_auth_config')
    async def test_parameter_schema_generation(self, mock_auth_config, mock_db, sample_openapi_spec):
        """Test that parameter schemas are correctly generated."""
        # Setup
        adapter = OpenAPIAdapter(mock_db)
        
        # Create a mock integration record
        integration_id = uuid.uuid4()
        tenant_id = uuid.uuid4()
        api_key = "weather_api"
        
        # Mock the integration retrieval
        mock_integration = MagicMock()
        mock_integration.id = integration_id
        mock_integration.openapi_spec = sample_openapi_spec
        mock_integration.name = "Weather API"
        mock_db.get.return_value = mock_integration
        
        # Mock the endpoint query result
        mock_endpoint = MagicMock()
        mock_endpoint.id = uuid.uuid4()
        mock_endpoint.path = "/v1/forecast"
        mock_endpoint.method = "GET"
        mock_endpoint.operation_id = "getForecast"
        
        # Create a mock result object that is not a coroutine
        mock_result1 = MagicMock()
        mock_result1.fetchall.return_value = [mock_endpoint]
        mock_db.execute.return_value = mock_result1
        
        # Make _generate_auth_config return a mock auth config dictionary directly
        mock_auth_config.return_value = {
            "type": "none",
            "value": None
        }
        
        # Execute
        templates = await adapter.generate_action_templates(
            integration_id=integration_id,
            api_key=api_key,
            tenant_id=tenant_id
        )
        
        # Find the main template
        main_template = next((t for t in templates if t["key"] == "weather_api_getforecast"), None)
        assert main_template is not None
        
        # Verify parameter schema
        parameters = main_template["parameters"]
        assert "latitude" in parameters
        assert parameters["latitude"]["type"] == "number" 
        assert parameters["latitude"]["required"] is True
        
        assert "longitude" in parameters
        assert parameters["longitude"]["required"] is True
        
        assert "daily" in parameters
        assert parameters["daily"]["required"] is False
        
    @pytest.mark.asyncio
    @patch('src.coherence.openapi_adapter.adapter.OpenAPIAdapter._generate_auth_config')
    async def test_response_mapping_generation(self, mock_auth_config, mock_db, sample_openapi_spec):
        """Test that response mappings are correctly generated."""
        # Setup
        adapter = OpenAPIAdapter(mock_db)
        
        # Create a mock integration record
        integration_id = uuid.uuid4()
        tenant_id = uuid.uuid4()
        api_key = "weather_api"
        
        # Mock the integration retrieval
        mock_integration = MagicMock()
        mock_integration.id = integration_id
        mock_integration.openapi_spec = sample_openapi_spec
        mock_integration.name = "Weather API"
        mock_db.get.return_value = mock_integration
        
        # Mock the endpoint query result
        mock_endpoint = MagicMock()
        mock_endpoint.id = uuid.uuid4()
        mock_endpoint.path = "/v1/forecast"
        mock_endpoint.method = "GET"
        mock_endpoint.operation_id = "getForecast"
        
        # Create a mock result object that is not a coroutine
        mock_result2 = MagicMock()
        mock_result2.fetchall.return_value = [mock_endpoint]
        mock_db.execute.return_value = mock_result2
        
        # Make _generate_auth_config return a mock auth config dictionary directly
        mock_auth_config.return_value = {
            "type": "none",
            "value": None
        }
        
        # Execute
        templates = await adapter.generate_action_templates(
            integration_id=integration_id,
            api_key=api_key,
            tenant_id=tenant_id
        )
        
        # Find the main template
        main_template = next((t for t in templates if t["key"] == "weather_api_getforecast"), None)
        assert main_template is not None
        
        # Verify response mapping in action config
        action = main_template["actions"][0]
        response_mapping = action["response_mapping"]
        
        # Check that we have mappings for the expected fields
        # The exact field names might vary based on implementation, 
        # but we should have mappings for the key fields in the schema
        assert any(key in ["current", "raw_response", "temperature", "temp"] for key in response_mapping.keys())
        
        # Check that the mappings use the correct Jinja2 syntax
        for mapping in response_mapping.values():
            if isinstance(mapping, str):
                assert "{{" in mapping and "}}" in mapping
                assert "response" in mapping  # Should reference the response object