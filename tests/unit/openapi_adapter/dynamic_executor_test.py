"""
Dynamic executor for testing purposes.

A simplified version of the DynamicActionExecutor for unit testing.
"""

import logging
import uuid
from typing import Any, Dict, Optional

import httpx
from jinja2 import Template

logger = logging.getLogger(__name__)


class DynamicActionExecutor:
    """Execute API actions based on template configurations.
    
    This is a simplified version for testing purposes.
    """
    
    def __init__(
        self, 
        credential_manager,
        http_client: Optional[httpx.AsyncClient] = None,
        template_renderer = None
    ):
        """Initialize the dynamic action executor.
        
        Args:
            credential_manager: Manager for API credentials
            http_client: Optional HTTP client for making requests
            template_renderer: Optional template renderer for parameter mapping
        """
        self.credential_manager = credential_manager
        self.http_client = http_client or httpx.AsyncClient()
        self.template_renderer = template_renderer
        
    async def execute(
        self, 
        action_config: Dict[str, Any],
        parameters: Dict[str, Any],
        tenant_id: uuid.UUID
    ) -> Dict[str, Any]:
        """Execute an API action based on template configuration.
        
        Args:
            action_config: The action configuration from the template
            parameters: The intent parameters
            tenant_id: The tenant ID for credential lookup
            
        Returns:
            The API response or error information
        """
        try:
            # Map parameters using template expressions
            mapped_params = await self._map_parameters(action_config, parameters)
            
            # Build request
            url = "https://api.example.com" + action_config.get("endpoint", "")
            headers = {}
            
            # Handle authentication
            auth_config = action_config.get("authentication", {})
            auth_type = auth_config.get("type")
            
            if auth_type == "api_key":
                # Get API key from credentials
                integration_id = action_config.get("integration_id")
                if integration_id:
                    # Use retrieve_credentials which is the actual method name
                    credentials = await self.credential_manager.retrieve_credentials(
                        uuid.UUID(integration_id)
                    )
                    api_key = credentials.get(action_config.get("api_key", "default"), "mock_api_key")
                    headers["X-API-Key"] = api_key
            elif auth_type == "bearer":
                # For Bearer auth
                api_name = action_config.get("api_key", "default")
                headers["Authorization"] = f"Bearer mock_{api_name}_token"
            
            # Execute request
            if action_config.get("method", "GET") == "GET":
                response = await self.http_client.get(
                    url=url,
                    headers=headers,
                    params=mapped_params
                )
            else:
                response = await self.http_client.request(
                    method=action_config.get("method", "GET"),
                    url=url,
                    headers=headers,
                    json=mapped_params
                )
            
            # Check for errors in response - important for testing error states
            # Our error mocks have raise_for_status defined with a side_effect
            try:
                if hasattr(response, 'raise_for_status'):
                    await response.raise_for_status()
            except Exception as e:
                return {
                    "success": False,
                    "error": {
                        "type": "http_error",
                        "message": f"HTTP Error {response.status_code}",
                        "details": {"error": str(e)}
                    }
                }
            
            # For testing with AsyncMock, json is already a property with the return_value
            response_data = response.json
            
            # Map response
            mapped_response = await self._map_response(action_config, response_data)
            
            return {
                "success": True,
                "status_code": response.status_code,
                "result": mapped_response,
                "raw_response": response_data
            }
            
        except Exception as e:
            logger.exception(f"Error executing action: {str(e)}")
            
            return {
                "success": False,
                "error": {
                    "type": "execution_error",
                    "message": str(e)
                }
            }
    
    async def _map_parameters(
        self, action_config: Dict[str, Any], parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Map parameters from intent to API parameters."""
        param_mapping = action_config.get("parameter_mapping", {})
        mapped_params = {}
        
        # Process each parameter mapping
        for api_param, value_template in param_mapping.items():
            if isinstance(value_template, str) and "{{" in value_template:
                # This is a Jinja2 expression
                try:
                    template = Template(value_template)
                    context = {"parameters": parameters}
                    mapped_value = template.render(**context)
                    mapped_params[api_param] = mapped_value
                except Exception as e:
                    logger.warning(f"Error mapping parameter {api_param}: {str(e)}")
            else:
                # Static value
                mapped_params[api_param] = value_template
                
        return mapped_params
    
    async def _map_response(
        self, action_config: Dict[str, Any], response_data: Any
    ) -> Dict[str, Any]:
        """Map API response to template variables."""
        response_mapping = action_config.get("response_mapping", {})
        mapped_response = {}
        
        # Process each response mapping
        for result_key, value_template in response_mapping.items():
            if isinstance(value_template, str) and "{{" in value_template:
                # This is a Jinja2 expression
                try:
                    template = Template(value_template)
                    context = {"response": response_data}
                    mapped_value = template.render(**context)
                    mapped_response[result_key] = mapped_value
                except Exception as e:
                    logger.warning(f"Error mapping response field {result_key}: {str(e)}")
                    mapped_response[result_key] = None
            else:
                # Static value
                mapped_response[result_key] = value_template
                
        return mapped_response


async def get_dynamic_executor(
    credential_manager,
    http_client = None,
    template_renderer = None
) -> DynamicActionExecutor:
    """Factory function to create a DynamicActionExecutor."""
    return DynamicActionExecutor(
        credential_manager=credential_manager,
        http_client=http_client,
        template_renderer=template_renderer
    )