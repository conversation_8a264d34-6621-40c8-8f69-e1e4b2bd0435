"""
Tests for the adapter pattern implementation for different API specification formats.
"""

import pytest

from src.coherence.openapi_adapter.spec_adapters import (
    BAPIAdapter,
    FAPIAdapter,
    OpenAPIAdapter,
    SpecAdapterFactory,
)
from src.coherence.services.spec_format_detector import SpecFormat, SpecFormatDetector


class TestSpecFormatDetection:
    """Tests for the SpecFormatDetector."""

    def test_detect_openapi_format(self):
        """Test detection of OpenAPI format."""
        # OpenAPI 3.0 spec
        openapi_spec = {
            "openapi": "3.0.0",
            "info": {"title": "Test API", "version": "1.0.0"},
            "paths": {}
        }
        assert SpecFormatDetector.detect_format(openapi_spec) == SpecFormat.OPENAPI

        # Swagger 2.0 spec
        swagger_spec = {
            "swagger": "2.0",
            "info": {"title": "Test API", "version": "1.0.0"},
            "paths": {}
        }
        assert SpecFormatDetector.detect_format(swagger_spec) == SpecFormat.OPENAPI

    def test_detect_fapi_format(self):
        """Test detection of FAPI format."""
        fapi_spec = {
            "financial_api": "1.0",
            "info": {"title": "FAPI Test", "version": "1.0.0"},
            "security_profiles": {
                "oauth2_profile": {
                    "type": "oauth2",
                    "profile": "FAPI 1.0",
                    "requires_pkce": True,
                    "flows": {
                        "authorization_code": {
                            "authorization_url": "https://auth.example.com/authorize",
                            "token_url": "https://auth.example.com/token",
                            "refresh_url": "https://auth.example.com/token",
                            "scopes": {
                                "accounts": "Access to account information",
                                "payments": "Make payments"
                            }
                        }
                    }
                }
            }
        }
        assert SpecFormatDetector.detect_format(fapi_spec) == SpecFormat.FAPI

    def test_detect_bapi_format(self):
        """Test detection of BAPI format."""
        bapi_spec = {
            "business_api": "1.0",
            "info": {"title": "BAPI Test", "version": "1.0.0"},
            "services": [
                {
                    "name": "orders",
                    "operations": [
                        {
                            "name": "createOrder",
                            "method": "post",
                            "path": "/orders",
                            "summary": "Create a new order"
                        }
                    ]
                }
            ]
        }
        assert SpecFormatDetector.detect_format(bapi_spec) == SpecFormat.BAPI

    def test_detect_unknown_format(self):
        """Test detection of unknown format."""
        unknown_spec = {
            "info": {"title": "Unknown API", "version": "1.0.0"},
            "some_key": "some_value"
        }
        assert SpecFormatDetector.detect_format(unknown_spec) == SpecFormat.UNKNOWN


class TestSpecAdapterFactory:
    """Tests for the SpecAdapterFactory."""

    def test_create_adapter_for_openapi(self):
        """Test creation of OpenAPIAdapter for OpenAPI format."""
        openapi_spec = {"openapi": "3.0.0"}
        adapter = SpecAdapterFactory.create_adapter(openapi_spec)
        assert isinstance(adapter, OpenAPIAdapter)

    def test_create_adapter_for_fapi(self):
        """Test creation of FAPIAdapter for FAPI format."""
        fapi_spec = {"financial_api": "1.0"}
        adapter = SpecAdapterFactory.create_adapter(fapi_spec)
        assert isinstance(adapter, FAPIAdapter)

    def test_create_adapter_for_bapi(self):
        """Test creation of BAPIAdapter for BAPI format."""
        bapi_spec = {"business_api": "1.0"}
        adapter = SpecAdapterFactory.create_adapter(bapi_spec)
        assert isinstance(adapter, BAPIAdapter)

    def test_create_adapter_for_unknown_format(self):
        """Test that factory raises ValueError for unknown format."""
        unknown_spec = {"info": {"title": "Unknown API"}}
        with pytest.raises(ValueError, match="Unsupported specification format"):
            SpecAdapterFactory.create_adapter(unknown_spec)

    def test_create_adapter_from_enum(self):
        """Test creation of adapters from enum values."""
        assert isinstance(SpecAdapterFactory.create_adapter_for_format(SpecFormat.OPENAPI), OpenAPIAdapter)
        assert isinstance(SpecAdapterFactory.create_adapter_for_format(SpecFormat.FAPI), FAPIAdapter)
        assert isinstance(SpecAdapterFactory.create_adapter_for_format(SpecFormat.BAPI), BAPIAdapter)
        
        with pytest.raises(ValueError, match="Unsupported specification format"):
            SpecAdapterFactory.create_adapter_for_format(SpecFormat.UNKNOWN)


class TestOpenAPIAdapter:
    """Tests for the OpenAPIAdapter."""

    def test_adapt_openapi_spec(self):
        """Test that OpenAPIAdapter passes through the spec without changes."""
        adapter = OpenAPIAdapter()
        openapi_spec = {
            "openapi": "3.0.0",
            "info": {"title": "Test API", "version": "1.0.0"},
            "paths": {"/test": {"get": {}}}
        }
        adapted_spec = adapter.adapt(openapi_spec)
        assert adapted_spec == openapi_spec
        
    def test_extract_auth_config(self):
        """Test extraction of auth config from OpenAPI spec."""
        adapter = OpenAPIAdapter()
        openapi_spec = {
            "openapi": "3.0.0",
            "components": {
                "securitySchemes": {
                    "api_key": {
                        "type": "apiKey",
                        "name": "X-API-Key",
                        "in": "header"
                    }
                }
            }
        }
        auth_config = adapter.extract_auth_config(openapi_spec)
        assert auth_config["auth_type"] == "apikey"
        assert auth_config["apikey_config"]["name"] == "X-API-Key"
        assert auth_config["apikey_config"]["in"] == "header"
        
    def test_extract_endpoints(self):
        """Test extraction of endpoints from OpenAPI spec."""
        adapter = OpenAPIAdapter()
        openapi_spec = {
            "openapi": "3.0.0",
            "paths": {
                "/users": {
                    "get": {
                        "operationId": "getUsers",
                        "summary": "Get all users",
                        "parameters": [
                            {
                                "name": "limit",
                                "in": "query",
                                "schema": {"type": "integer"}
                            }
                        ]
                    }
                }
            }
        }
        endpoints = adapter.extract_endpoints(openapi_spec)
        assert len(endpoints) == 1
        assert endpoints[0].path == "/users"
        assert endpoints[0].method == "GET"
        assert endpoints[0].operation_id == "getUsers"


class TestFAPIAdapter:
    """Tests for the FAPIAdapter."""

    def test_adapt_fapi_spec(self):
        """Test conversion of FAPI spec to OpenAPI format."""
        adapter = FAPIAdapter()
        fapi_spec = {
            "financial_api": "1.0",
            "info": {"title": "FAPI Test", "version": "1.0.0"},
            "security_profiles": {
                "oauth2_profile": {
                    "type": "oauth2",
                    "profile": "FAPI 1.0",
                    "requires_pkce": True,
                    "flows": {
                        "authorization_code": {
                            "authorization_url": "https://auth.example.com/authorize",
                            "token_url": "https://auth.example.com/token",
                            "scopes": {
                                "accounts": "Access to account information"
                            }
                        }
                    }
                }
            },
            "endpoints": [
                {
                    "path": "/accounts",
                    "method": "get",
                    "id": "get_accounts",
                    "summary": "Get Accounts"
                }
            ]
        }
        adapted_spec = adapter.adapt(fapi_spec)
        
        # Check basic structure
        assert "openapi" in adapted_spec
        assert adapted_spec["openapi"] == "3.0.0"
        assert adapted_spec["info"]["title"] == "FAPI Test"
        
        # Check security schemes
        assert "components" in adapted_spec
        assert "securitySchemes" in adapted_spec["components"]
        oauth_scheme = adapted_spec["components"]["securitySchemes"]["oauth2_profile"]
        assert oauth_scheme["type"] == "oauth2"
        assert "x-fapi-profile" in oauth_scheme
        assert oauth_scheme["x-fapi-profile"] == "FAPI 1.0"
        assert oauth_scheme["x-fapi-requires-pkce"] is True
        
        # Check paths
        assert "paths" in adapted_spec
        assert "/accounts" in adapted_spec["paths"]
        get_accounts = adapted_spec["paths"]["/accounts"]["get"]
        assert get_accounts["operationId"] == "get_accounts"
        assert get_accounts["summary"] == "Get Accounts"
        
    def test_extract_auth_config(self):
        """Test extraction of auth config from FAPI spec."""
        adapter = FAPIAdapter()
        fapi_spec = {
            "financial_api": "1.0",
            "security_profiles": {
                "oauth2_profile": {
                    "type": "oauth2",
                    "profile": "FAPI 1.0",
                    "requires_pkce": True,
                    "flows": {
                        "authorization_code": {
                            "scopes": {
                                "accounts": "Access to account information"
                            }
                        }
                    }
                }
            }
        }
        auth_config = adapter.extract_auth_config(fapi_spec)
        assert auth_config["auth_type"] == "oauth2"
        assert "oauth2_config" in auth_config
        assert auth_config["oauth2_config"]["fapi_profile"] == "FAPI 1.0"
        assert auth_config["oauth2_config"]["requires_pkce"] is True
        
    def test_extract_endpoints(self):
        """Test extraction of endpoints from FAPI spec."""
        adapter = FAPIAdapter()
        fapi_spec = {
            "financial_api": "1.0",
            "endpoints": [
                {
                    "path": "/accounts",
                    "method": "get",
                    "id": "get_accounts",
                    "summary": "Get Accounts",
                    "parameters": [
                        {
                            "name": "limit",
                            "in": "query",
                            "type": "integer"
                        }
                    ]
                }
            ]
        }
        endpoints = adapter.extract_endpoints(fapi_spec)
        assert len(endpoints) == 1
        assert endpoints[0].path == "/accounts"
        assert endpoints[0].method == "GET"
        assert endpoints[0].operation_id == "get_accounts"
        assert len(endpoints[0].parameters) == 1


class TestBAPIAdapter:
    """Tests for the BAPIAdapter."""

    def test_adapt_bapi_spec(self):
        """Test conversion of BAPI spec to OpenAPI format."""
        adapter = BAPIAdapter()
        bapi_spec = {
            "business_api": "1.0",
            "info": {"title": "BAPI Test", "version": "1.0.0"},
            "authentication": {
                "api_key_auth": {
                    "type": "apikey",
                    "name": "X-API-Key",
                    "in": "header"
                }
            },
            "services": [
                {
                    "name": "orders",
                    "operations": [
                        {
                            "name": "createOrder",
                            "method": "post",
                            "path": "/orders",
                            "summary": "Create a new order",
                            "input": {
                                "type": "object",
                                "properties": {
                                    "customer_id": {"type": "string"}
                                }
                            }
                        }
                    ]
                }
            ]
        }
        adapted_spec = adapter.adapt(bapi_spec)
        
        # Check basic structure
        assert "openapi" in adapted_spec
        assert adapted_spec["openapi"] == "3.0.0"
        assert adapted_spec["info"]["title"] == "BAPI Test"
        
        # Check security schemes
        assert "components" in adapted_spec
        assert "securitySchemes" in adapted_spec["components"]
        api_key_scheme = adapted_spec["components"]["securitySchemes"]["api_key_auth"]
        assert api_key_scheme["type"] == "apiKey"
        assert api_key_scheme["name"] == "X-API-Key"
        assert api_key_scheme["in"] == "header"
        
        # Check paths
        assert "paths" in adapted_spec
        assert "/orders" in adapted_spec["paths"]
        create_order = adapted_spec["paths"]["/orders"]["post"]
        assert create_order["operationId"] == "createOrder"
        assert create_order["summary"] == "Create a new order"
        assert "requestBody" in create_order
        assert "content" in create_order["requestBody"]
        assert "application/json" in create_order["requestBody"]["content"]
        schema = create_order["requestBody"]["content"]["application/json"]["schema"]
        assert schema["type"] == "object"
        assert "customer_id" in schema["properties"]
        
    def test_extract_auth_config(self):
        """Test extraction of auth config from BAPI spec."""
        adapter = BAPIAdapter()
        bapi_spec = {
            "business_api": "1.0",
            "authentication": {
                "api_key_auth": {
                    "type": "apikey",
                    "name": "X-API-Key",
                    "in": "header"
                }
            }
        }
        auth_config = adapter.extract_auth_config(bapi_spec)
        assert auth_config["auth_type"] == "apikey"
        assert "apikey_config" in auth_config
        assert auth_config["apikey_config"]["name"] == "X-API-Key"
        assert auth_config["apikey_config"]["in"] == "header"
        
    def test_extract_endpoints(self):
        """Test extraction of endpoints from BAPI spec."""
        adapter = BAPIAdapter()
        bapi_spec = {
            "business_api": "1.0",
            "services": [
                {
                    "name": "orders",
                    "operations": [
                        {
                            "name": "createOrder",
                            "method": "post",
                            "path": "/orders",
                            "summary": "Create a new order"
                        }
                    ]
                }
            ]
        }
        endpoints = adapter.extract_endpoints(bapi_spec)
        assert len(endpoints) == 1
        assert endpoints[0].path == "/orders"
        assert endpoints[0].method == "POST"
        assert endpoints[0].operation_id == "createOrder"
        assert endpoints[0].tags == ["orders"]  # Service name is added as a tag