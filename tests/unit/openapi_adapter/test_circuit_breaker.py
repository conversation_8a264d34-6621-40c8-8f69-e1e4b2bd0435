"""
Unit tests for circuit breaker implementation.
"""

import time
from unittest.mock import AsyncMock

import pytest

from src.coherence.openapi_adapter.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerError,
    CircuitState,
    get_circuit_breaker,
    register_circuit_breaker,
    with_circuit_breaker,
)


@pytest.fixture
def circuit_breaker():
    """Create a circuit breaker instance for testing."""
    return CircuitBreaker(
        name="test_breaker",
        failure_threshold=3,
        recovery_timeout=1.0,  # Short timeout for testing
        half_open_max_calls=2,
    )


class TestCircuitBreaker:
    """Tests for the CircuitBreaker class."""

    @pytest.mark.asyncio
    async def test_initial_state(self, circuit_breaker):
        """Test initial state of circuit breaker."""
        assert circuit_breaker.state == CircuitState.CLOSED
        assert circuit_breaker.failure_count == 0

    @pytest.mark.asyncio
    async def test_successful_execution(self, circuit_breaker):
        """Test successful execution doesn't change state."""
        mock_func = AsyncMock(return_value="success")
        
        result = await circuit_breaker.execute(mock_func, "arg1", kwarg1="value")
        
        assert result == "success"
        assert circuit_breaker.state == CircuitState.CLOSED
        assert circuit_breaker.failure_count == 0
        mock_func.assert_called_once_with("arg1", kwarg1="value")

    @pytest.mark.asyncio
    async def test_failure_threshold(self, circuit_breaker):
        """Test circuit opens after reaching failure threshold."""
        mock_func = AsyncMock(side_effect=RuntimeError("test error"))
        
        # Fail 3 times to reach threshold
        for _ in range(3):
            with pytest.raises(RuntimeError):
                await circuit_breaker.execute(mock_func)
        
        assert circuit_breaker.state == CircuitState.OPEN
        assert circuit_breaker.failure_count == 3

    @pytest.mark.asyncio
    async def test_open_circuit_fails_fast(self, circuit_breaker):
        """Test open circuit fails fast without calling function."""
        # Set circuit to open state
        circuit_breaker.state = CircuitState.OPEN
        circuit_breaker.last_failure_time = time.time()
        
        mock_func = AsyncMock()
        
        with pytest.raises(CircuitBreakerError):
            await circuit_breaker.execute(mock_func)
        
        # Function should not be called
        mock_func.assert_not_called()

    @pytest.mark.asyncio
    async def test_half_open_state_transition(self, circuit_breaker):
        """Test circuit transitions to half-open after timeout."""
        # Set circuit to open state but with expired timeout
        circuit_breaker.state = CircuitState.OPEN
        circuit_breaker.last_failure_time = time.time() - 2.0  # Timeout is 1.0
        
        mock_func = AsyncMock(return_value="success")
        
        result = await circuit_breaker.execute(mock_func)
        
        assert result == "success"
        assert circuit_breaker.state == CircuitState.CLOSED  # Success should close circuit

    @pytest.mark.asyncio
    async def test_half_open_state_limits_calls(self, circuit_breaker):
        """Test half-open state limits concurrent calls."""
        # Set circuit to half-open state
        circuit_breaker.state = CircuitState.HALF_OPEN
        circuit_breaker.half_open_calls = 2  # Max calls is 2
        
        mock_func = AsyncMock()
        
        with pytest.raises(CircuitBreakerError):
            await circuit_breaker.execute(mock_func)
        
        # Function should not be called
        mock_func.assert_not_called()

    @pytest.mark.asyncio
    async def test_half_open_success_closes_circuit(self, circuit_breaker):
        """Test successful call in half-open state closes the circuit."""
        # Set circuit to half-open state
        circuit_breaker.state = CircuitState.HALF_OPEN
        
        mock_func = AsyncMock(return_value="success")
        
        result = await circuit_breaker.execute(mock_func)
        
        assert result == "success"
        assert circuit_breaker.state == CircuitState.CLOSED

    @pytest.mark.asyncio
    async def test_half_open_failure_opens_circuit(self, circuit_breaker):
        """Test failed call in half-open state reopens the circuit."""
        # Set circuit to half-open state
        circuit_breaker.state = CircuitState.HALF_OPEN
        
        mock_func = AsyncMock(side_effect=RuntimeError("test error"))
        
        with pytest.raises(RuntimeError):
            await circuit_breaker.execute(mock_func)
        
        assert circuit_breaker.state == CircuitState.OPEN

    @pytest.mark.asyncio
    async def test_excluded_exceptions(self):
        """Test excluded exceptions don't count towards failure threshold."""
        # Create circuit breaker with excluded exceptions
        circuit_breaker = CircuitBreaker(
            name="test_excluded",
            failure_threshold=2,
            excluded_exceptions=[ValueError],
        )
        
        # This exception should be excluded
        value_error_func = AsyncMock(side_effect=ValueError("excluded"))
        
        # This exception should count towards threshold
        key_error_func = AsyncMock(side_effect=KeyError("counts"))
        
        # ValueError should not count towards threshold
        with pytest.raises(ValueError):
            await circuit_breaker.execute(value_error_func)
        
        assert circuit_breaker.state == CircuitState.CLOSED
        assert circuit_breaker.failure_count == 0
        
        # KeyError should count towards threshold
        with pytest.raises(KeyError):
            await circuit_breaker.execute(key_error_func)
        
        with pytest.raises(KeyError):
            await circuit_breaker.execute(key_error_func)
        
        assert circuit_breaker.state == CircuitState.OPEN


class TestCircuitBreakerRegistry:
    """Tests for the circuit breaker registry."""
    
    def test_register_and_get_circuit_breaker(self):
        """Test registering and retrieving circuit breakers."""
        circuit_breaker = CircuitBreaker(name="registry_test")
        
        register_circuit_breaker(circuit_breaker)
        retrieved = get_circuit_breaker("registry_test")
        
        assert retrieved is circuit_breaker
        assert retrieved.name == "registry_test"


class TestWithCircuitBreaker:
    """Tests for the circuit breaker decorator."""
    
    @pytest.mark.asyncio
    async def test_decorator(self):
        """Test with_circuit_breaker decorator."""
        # Define a function with the decorator
        @with_circuit_breaker(name="decorator_test", failure_threshold=2)
        async def test_func(value):
            return value * 2
        
        # Test successful execution
        result = await test_func(5)
        assert result == 10
        
        # Get the circuit breaker from registry
        circuit_breaker = get_circuit_breaker("decorator_test")
        assert circuit_breaker is not None
        assert circuit_breaker.state == CircuitState.CLOSED