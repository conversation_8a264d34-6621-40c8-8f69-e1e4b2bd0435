"""
Tests for the improved OpenAPI parameter extraction functionality.

These tests verify the enhancements made to parameter extraction including:
1. Proper schema reference resolution ($ref)
2. Improved type extraction from complex schemas  
3. Flattening of request body object properties
4. Enhanced enum, format, and example handling
"""

import pytest
from unittest.mock import AsyncMock

from src.coherence.openapi_adapter.adapter import OpenAPIAdapter


class TestImprovedParameterExtraction:
    """Test suite for enhanced OpenAPI parameter extraction."""

    def test_schema_reference_resolution(self):
        """Test resolving $ref references in schemas."""
        adapter = OpenAPIAdapter(AsyncMock())
        
        # Test spec with schema definitions
        api_spec = {
            "openapi": "3.0.0",
            "components": {
                "schemas": {
                    "User": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "string", "format": "uuid"},
                            "name": {"type": "string"},
                            "email": {"type": "string", "format": "email"}
                        },
                        "required": ["id", "name"]
                    },
                    "Status": {
                        "type": "string",
                        "enum": ["active", "inactive", "pending"]
                    }
                }
            }
        }
        
        # Test resolving reference to User schema
        user_ref = {"$ref": "#/components/schemas/User"}
        resolved = adapter._resolve_schema_reference(api_spec, user_ref)
        
        assert resolved["type"] == "object"
        assert "properties" in resolved
        assert "id" in resolved["properties"]
        assert resolved["properties"]["id"]["format"] == "uuid"
        assert resolved["required"] == ["id", "name"]
        
        # Test resolving reference to Status enum
        status_ref = {"$ref": "#/components/schemas/Status"}
        resolved_status = adapter._resolve_schema_reference(api_spec, status_ref)
        
        assert resolved_status["type"] == "string"
        assert resolved_status["enum"] == ["active", "inactive", "pending"]
        
        # Test non-reference schema (should return as-is)
        direct_schema = {"type": "string", "maxLength": 50}
        resolved_direct = adapter._resolve_schema_reference(api_spec, direct_schema)
        assert resolved_direct == direct_schema

    def test_type_extraction_from_schema(self):
        """Test extracting simple types from complex schemas."""
        adapter = OpenAPIAdapter(AsyncMock())
        
        # Test basic types
        assert adapter._extract_type_from_schema({"type": "string"}) == "string"
        assert adapter._extract_type_from_schema({"type": "integer"}) == "integer"
        assert adapter._extract_type_from_schema({"type": "number"}) == "number"
        assert adapter._extract_type_from_schema({"type": "boolean"}) == "boolean"
        assert adapter._extract_type_from_schema({"type": "array"}) == "array"
        assert adapter._extract_type_from_schema({"type": "object"}) == "object"
        
        # Test formatted types
        assert adapter._extract_type_from_schema({"type": "string", "format": "email"}) == "email"
        assert adapter._extract_type_from_schema({"type": "string", "format": "date"}) == "date"
        assert adapter._extract_type_from_schema({"type": "string", "format": "uuid"}) == "uuid"
        assert adapter._extract_type_from_schema({"type": "number", "format": "float"}) == "float"
        assert adapter._extract_type_from_schema({"type": "integer", "format": "int64"}) == "int64"
        
        # Test enum types
        assert adapter._extract_type_from_schema({"type": "string", "enum": ["a", "b"]}) == "enum"
        
        # Test fallback for unknown/missing type
        assert adapter._extract_type_from_schema({}) == "string"
        assert adapter._extract_type_from_schema(None) == "string"

    def test_request_body_flattening(self):
        """Test flattening of request body object properties into individual parameters."""
        adapter = OpenAPIAdapter(AsyncMock())
        
        # Test spec with object request body
        api_spec = {
            "openapi": "3.0.0",
            "paths": {
                "/users": {
                    "post": {
                        "operationId": "createUser",
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "name": {
                                                "type": "string",
                                                "description": "User's full name",
                                                "minLength": 2,
                                                "maxLength": 50
                                            },
                                            "email": {
                                                "type": "string",
                                                "format": "email",
                                                "description": "User's email address"
                                            },
                                            "age": {
                                                "type": "integer",
                                                "minimum": 18,
                                                "maximum": 120,
                                                "description": "User's age"
                                            },
                                            "status": {
                                                "type": "string",
                                                "enum": ["active", "inactive"],
                                                "default": "active",
                                                "description": "User's status"
                                            },
                                            "preferences": {
                                                "type": "object",
                                                "description": "User preferences as nested object"
                                            }
                                        },
                                        "required": ["name", "email"]
                                    }
                                }
                            }
                        },
                        "responses": {
                            "201": {"description": "User created"}
                        }
                    }
                }
            }
        }
        
        # Extract parameters
        parameters = adapter._extract_parameters(api_spec, "/users", "post")
        
        # Convert to dict for easier testing
        param_dict = {param["name"]: param for param in parameters}
        
        # Check that object properties were flattened into individual parameters
        assert "name" in param_dict
        assert "email" in param_dict
        assert "age" in param_dict
        assert "status" in param_dict
        assert "preferences" in param_dict
        
        # Check name parameter
        name_param = param_dict["name"]
        assert name_param["type"] == "string"
        assert name_param["required"] is True
        assert name_param["in"] == "body"
        assert name_param["description"] == "User's full name"
        
        # Check email parameter
        email_param = param_dict["email"]
        assert email_param["type"] == "email"  # Should extract format
        assert email_param["required"] is True
        assert email_param["format"] == "email"
        
        # Check age parameter
        age_param = param_dict["age"]
        assert age_param["type"] == "integer"
        assert age_param["required"] is False
        
        # Check status parameter (enum)
        status_param = param_dict["status"]
        assert status_param["type"] == "enum"
        assert status_param["enum"] == ["active", "inactive"]
        assert status_param["default"] == "active"
        
        # Check preferences parameter (nested object)
        pref_param = param_dict["preferences"]
        assert pref_param["type"] == "object"
        assert pref_param["required"] is False

    def test_complex_parameter_extraction(self):
        """Test parameter extraction with mix of path, query, and body parameters."""
        adapter = OpenAPIAdapter(AsyncMock())
        
        # Complex API spec with multiple parameter types
        api_spec = {
            "openapi": "3.0.0",
            "components": {
                "schemas": {
                    "FilterCriteria": {
                        "type": "object",
                        "properties": {
                            "category": {"type": "string"},
                            "min_price": {"type": "number", "minimum": 0}
                        }
                    }
                }
            },
            "paths": {
                "/stores/{storeId}/products": {
                    "post": {
                        "operationId": "searchProducts",
                        "parameters": [
                            {
                                "name": "storeId",
                                "in": "path",
                                "required": True,
                                "schema": {"type": "string", "format": "uuid"}
                            },
                            {
                                "name": "limit",
                                "in": "query",
                                "schema": {
                                    "type": "integer",
                                    "minimum": 1,
                                    "maximum": 100,
                                    "default": 20
                                }
                            },
                            {
                                "name": "sort",
                                "in": "query",
                                "schema": {
                                    "type": "string",
                                    "enum": ["name", "price", "rating"],
                                    "default": "name"
                                }
                            }
                        ],
                        "requestBody": {
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/FilterCriteria"
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "Products found"}
                        }
                    }
                }
            }
        }
        
        # Extract parameters
        parameters = adapter._extract_parameters(api_spec, "/stores/{storeId}/products", "post")
        
        # Convert to dict for easier testing
        param_dict = {param["name"]: param for param in parameters}
        
        # Check path parameter
        assert "storeId" in param_dict
        store_param = param_dict["storeId"]
        assert store_param["type"] == "uuid"
        assert store_param["required"] is True
        assert store_param["in"] == "path"
        
        # Check query parameters
        assert "limit" in param_dict
        limit_param = param_dict["limit"]
        assert limit_param["type"] == "integer"
        assert limit_param["required"] is False
        assert limit_param["in"] == "query"
        assert limit_param["default"] == 20
        
        assert "sort" in param_dict
        sort_param = param_dict["sort"]
        assert sort_param["type"] == "enum"
        assert sort_param["enum"] == ["name", "price", "rating"]
        assert sort_param["default"] == "name"
        
        # Check body parameters (from resolved schema reference)
        assert "category" in param_dict
        cat_param = param_dict["category"]
        assert cat_param["type"] == "string"
        assert cat_param["in"] == "body"
        
        assert "min_price" in param_dict
        price_param = param_dict["min_price"]
        assert price_param["type"] == "number"
        assert price_param["in"] == "body"

    def test_allof_anyof_oneof_handling(self):
        """Test handling of allOf, anyOf, oneOf schema compositions."""
        adapter = OpenAPIAdapter(AsyncMock())
        
        # Test allOf schema merging
        all_of_schema = {
            "allOf": [
                {"type": "object", "properties": {"name": {"type": "string"}}},
                {"type": "object", "properties": {"age": {"type": "integer"}}}
            ]
        }
        resolved = adapter._resolve_schema_reference({}, all_of_schema)
        assert "properties" in resolved
        assert "name" in resolved["properties"]
        assert "age" in resolved["properties"]
        
        # Test anyOf schema (takes first)
        any_of_schema = {
            "anyOf": [
                {"type": "string", "enum": ["a", "b"]},
                {"type": "integer"}
            ]
        }
        resolved = adapter._resolve_schema_reference({}, any_of_schema)
        assert resolved["type"] == "string"
        assert resolved["enum"] == ["a", "b"]
        
        # Test oneOf schema (takes first)
        one_of_schema = {
            "oneOf": [
                {"type": "number", "format": "float"},
                {"type": "integer"}
            ]
        }
        resolved = adapter._resolve_schema_reference({}, one_of_schema)
        assert resolved["type"] == "number"
        assert resolved["format"] == "float"

    def test_edge_cases(self):
        """Test edge cases and error handling."""
        adapter = OpenAPIAdapter(AsyncMock())
        
        # Test with missing operation
        parameters = adapter._extract_parameters({}, "/missing", "get")
        assert parameters == []
        
        # Test with malformed parameters
        api_spec = {
            "paths": {
                "/test": {
                    "get": {
                        "parameters": [
                            {"name": "valid", "in": "query", "schema": {"type": "string"}},
                            {"missing_name": True},  # Invalid parameter
                            "not_a_dict",  # Invalid parameter
                            {"name": "missing_in", "schema": {"type": "string"}},  # Missing 'in'
                        ]
                    }
                }
            }
        }
        
        parameters = adapter._extract_parameters(api_spec, "/test", "get")
        
        # Should only extract the valid parameter
        assert len(parameters) == 1
        assert parameters[0]["name"] == "valid"
        
        # Test with missing schema
        api_spec_no_schema = {
            "paths": {
                "/test": {
                    "get": {
                        "parameters": [
                            {"name": "no_schema", "in": "query"}
                        ]
                    }
                }
            }
        }
        
        parameters = adapter._extract_parameters(api_spec_no_schema, "/test", "get")
        assert len(parameters) == 1
        assert parameters[0]["type"] == "string"  # Should default to string


@pytest.mark.asyncio
class TestOpenAPIAdapterIntegration:
    """Integration tests for the OpenAPI adapter with improved parameter extraction."""

    async def test_extract_endpoints_with_enhanced_parameters(self):
        """Test that endpoint extraction includes enhanced parameter information."""
        db_mock = AsyncMock()
        adapter = OpenAPIAdapter(db_mock)
        
        # Mock database operations
        db_mock.add = AsyncMock()
        db_mock.flush = AsyncMock()
        
        integration_id = "test-integration-id"
        
        # Test spec with various parameter types
        api_spec = {
            "openapi": "3.0.0",
            "info": {"title": "Test API", "version": "1.0"},
            "paths": {
                "/products/{id}": {
                    "put": {
                        "operationId": "updateProduct",
                        "summary": "Update a product",
                        "parameters": [
                            {
                                "name": "id",
                                "in": "path",
                                "required": True,
                                "schema": {"type": "string", "format": "uuid"}
                            },
                            {
                                "name": "notify",
                                "in": "query",
                                "schema": {
                                    "type": "boolean",
                                    "default": False,
                                    "description": "Send notification"
                                }
                            }
                        ],
                        "requestBody": {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "name": {
                                                "type": "string",
                                                "minLength": 1,
                                                "maxLength": 100
                                            },
                                            "category": {
                                                "type": "string",
                                                "enum": ["electronics", "books", "clothing"]
                                            },
                                            "price": {
                                                "type": "number",
                                                "minimum": 0,
                                                "example": 29.99
                                            }
                                        },
                                        "required": ["name", "price"]
                                    }
                                }
                            }
                        },
                        "responses": {
                            "200": {"description": "Product updated"}
                        }
                    }
                }
            }
        }
        
        # Extract endpoints
        endpoints = await adapter._extract_endpoints(integration_id, api_spec)
        
        # Should have created one endpoint
        assert len(endpoints) == 1
        endpoint = endpoints[0]
        
        # Verify endpoint properties
        assert endpoint.path == "/products/{id}"
        assert endpoint.method == "PUT"
        assert endpoint.operation_id == "updateProduct"
        assert endpoint.summary == "Update a product"
        
        # Verify openapi_snippet contains the operation details
        snippet = endpoint.openapi_snippet
        assert snippet is not None
        assert snippet["summary"] == "Update a product"
        assert "parameters" in snippet
        assert "requestBody" in snippet
        
        # Verify parameters are preserved in snippet
        assert len(snippet["parameters"]) == 2
        param_names = [p["name"] for p in snippet["parameters"]]
        assert "id" in param_names
        assert "notify" in param_names
        
        # Verify request body is preserved
        assert snippet["requestBody"]["required"] is True
        body_schema = snippet["requestBody"]["content"]["application/json"]["schema"]
        assert body_schema["type"] == "object"
        assert "name" in body_schema["properties"]
        assert "category" in body_schema["properties"]
        assert "price" in body_schema["properties"]