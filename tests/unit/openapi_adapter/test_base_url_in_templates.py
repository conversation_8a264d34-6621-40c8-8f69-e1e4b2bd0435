"""
Tests to verify that the base_url is properly included in generated templates.
"""

import uuid
from unittest.mock import AsyncMock, MagicMock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.models.integration import APIEndpoint, APIIntegration
from src.coherence.openapi_adapter.action_generator import ActionGenerator


@pytest.mark.asyncio
async def test_base_url_in_templates():
    """Test that base_url is correctly included in generated templates."""
    # Create mock db session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Define test data
    endpoint_id = uuid.uuid4()
    tenant_id = uuid.uuid4()
    integration_id = uuid.uuid4()
    test_base_url = "https://api.example.com"
    
    # Create mock endpoint and integration
    mock_endpoint = MagicMock(spec=APIEndpoint)
    mock_endpoint.id = endpoint_id
    mock_endpoint.integration_id = integration_id
    mock_endpoint.path = "/test/path"
    mock_endpoint.method = "GET"
    mock_endpoint.operation_id = "getTestPath"
    mock_endpoint.action_class_name = None
    
    mock_integration = MagicMock(spec=APIIntegration)
    mock_integration.id = integration_id
    mock_integration.tenant_id = tenant_id
    mock_integration.name = "Test API"
    mock_integration.base_url = test_base_url
    mock_integration.openapi_spec = {
        "paths": {
            "/test/path": {
                "get": {
                    "summary": "Test endpoint",
                    "parameters": []
                }
            }
        }
    }

    # Set up the mock db to return our objects
    mock_db.get = AsyncMock()
    mock_db.get.side_effect = lambda model, id: mock_endpoint if model == APIEndpoint else mock_integration
    
    # Create the action generator
    action_generator = ActionGenerator(mock_db)
    
    # Generate templates
    templates = await action_generator._generate_action_templates(
        endpoint_id=endpoint_id,
        tenant_id=tenant_id,
        api_key="test_api"
    )
    
    # Verify base_url is in the action_config
    assert len(templates) >= 1, "At least one template should be generated"
    
    # Check primary template (response_gen)
    primary_template = next((t for t in templates if t["category"] == "response_gen"), None)
    assert primary_template is not None, "Primary template should be generated"
    assert "actions" in primary_template, "Template should have actions"
    assert "api_action" in primary_template["actions"], "Template should have api_action"
    assert "base_url" in primary_template["actions"]["api_action"], "action_config should include base_url"
    assert primary_template["actions"]["api_action"]["base_url"] == test_base_url, "base_url should match integration's base_url"

    # Check intent template
    intent_template = next((t for t in templates if t["category"] == "intent_router"), None)
    assert intent_template is not None, "Intent template should be generated"
    assert "actions" in intent_template, "Template should have actions"
    assert "api_action" in intent_template["actions"], "Template should have api_action"
    assert "base_url" in intent_template["actions"]["api_action"], "action_config should include base_url"
    assert intent_template["actions"]["api_action"]["base_url"] == test_base_url, "base_url should match integration's base_url"


@pytest.mark.asyncio
async def test_base_url_parameter_override():
    """Test that passed base_url parameter overrides integration's base_url."""
    # Create mock db session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Define test data
    endpoint_id = uuid.uuid4()
    tenant_id = uuid.uuid4()
    integration_id = uuid.uuid4()
    integration_base_url = "https://api.example.com"
    override_base_url = "https://custom-api.example.org"
    
    # Create mock endpoint and integration
    mock_endpoint = MagicMock(spec=APIEndpoint)
    mock_endpoint.id = endpoint_id
    mock_endpoint.integration_id = integration_id
    mock_endpoint.path = "/test/path"
    mock_endpoint.method = "GET"
    mock_endpoint.operation_id = "getTestPath"
    mock_endpoint.action_class_name = None
    
    mock_integration = MagicMock(spec=APIIntegration)
    mock_integration.id = integration_id
    mock_integration.tenant_id = tenant_id
    mock_integration.name = "Test API"
    mock_integration.base_url = integration_base_url
    mock_integration.openapi_spec = {
        "paths": {
            "/test/path": {
                "get": {
                    "summary": "Test endpoint",
                    "parameters": []
                }
            }
        }
    }

    # Set up the mock db to return our objects
    mock_db.get = AsyncMock()
    mock_db.get.side_effect = lambda model, id: mock_endpoint if model == APIEndpoint else mock_integration
    
    # Create the action generator
    action_generator = ActionGenerator(mock_db)
    
    # Generate templates with base_url override
    templates = await action_generator._generate_action_templates(
        endpoint_id=endpoint_id,
        tenant_id=tenant_id,
        api_key="test_api",
        base_url=override_base_url
    )
    
    # Check primary template (response_gen)
    primary_template = next((t for t in templates if t["category"] == "response_gen"), None)
    assert primary_template is not None, "Primary template should be generated"
    assert "base_url" in primary_template["actions"]["api_action"], "action_config should include base_url"
    assert primary_template["actions"]["api_action"]["base_url"] == override_base_url, "base_url should match the override value"

    # Check intent template
    intent_template = next((t for t in templates if t["category"] == "intent_router"), None)
    assert intent_template is not None, "Intent template should be generated"
    assert "base_url" in intent_template["actions"]["api_action"], "action_config should include base_url"
    assert intent_template["actions"]["api_action"]["base_url"] == override_base_url, "base_url should match the override value"