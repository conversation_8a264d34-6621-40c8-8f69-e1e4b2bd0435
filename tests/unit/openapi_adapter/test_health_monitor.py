"""
Tests for the API Health Monitor.

This module tests the functionality of the ApiHealthMonitor class,
including health checks, status tracking, and alerting.
"""

import uuid
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock

import pytest

from src.coherence.openapi_adapter.health_monitor import (
    ApiHealthMonitor,
    ApiStatus,
    HealthCheckConfig,
    HealthCheckResult,
)


class TestApiHealthMonitor:
    @pytest.fixture
    def mock_redis_client(self):
        """Create a mock Redis client for testing."""
        redis = AsyncMock()
        
        # Set up in-memory storage for testing
        self.mock_data = {}
        self.mock_sorted_sets = {}
        self.mock_hashes = {}
        
        # Mock the get method
        async def mock_get(key):
            return self.mock_data.get(key)
            
        # Mock the set method
        async def mock_set(key, value, expire=None):
            self.mock_data[key] = value
            return True
            
        # Mock the hset method
        async def mock_hset(key, mapping=None, **kwargs):
            if key not in self.mock_hashes:
                self.mock_hashes[key] = {}
                
            if mapping:
                self.mock_hashes[key].update(mapping)
            else:
                self.mock_hashes[key].update(kwargs)
                
            return len(mapping) if mapping else len(kwargs)
            
        # Mock the hgetall method
        async def mock_hgetall(key):
            hash_data = self.mock_hashes.get(key, {})
            # Convert keys to bytes to match Redis behavior
            return {k.encode() if isinstance(k, str) else k: v for k, v in hash_data.items()}
            
        # Mock the zadd method
        async def mock_zadd(key, mapping):
            if key not in self.mock_sorted_sets:
                self.mock_sorted_sets[key] = {}
                
            self.mock_sorted_sets[key].update(mapping)
            return len(mapping)
            
        # Mock the zrevrangebyscore method
        async def mock_zrevrangebyscore(key, min, max, start=0, num=None, withscores=False):
            if key not in self.mock_sorted_sets:
                return []
                
            # Filter by score
            filtered = {
                k: v for k, v in self.mock_sorted_sets[key].items() 
                if min <= v <= max
            }
            
            # Sort by score (descending)
            sorted_items = sorted(filtered.items(), key=lambda x: x[1], reverse=True)
            
            # Apply limit
            if num is not None:
                sorted_items = sorted_items[start:start + num]
            else:
                sorted_items = sorted_items[start:]
                
            # Format results
            if withscores:
                return sorted_items
            else:
                return [k for k, _ in sorted_items]
                
        # Mock the expire method
        async def mock_expire(key, seconds):
            return 1 if key in self.mock_data or key in self.mock_sorted_sets or key in self.mock_hashes else 0
            
        # Configure the mock
        redis.get = mock_get
        redis.set = mock_set
        redis.hset = mock_hset
        redis.hgetall = mock_hgetall
        redis.zadd = mock_zadd
        redis.zrevrangebyscore = mock_zrevrangebyscore
        redis.expire = mock_expire
        
        return redis
        
    @pytest.fixture
    def mock_metrics_collector(self):
        """Create a mock metrics collector for testing."""
        collector = MagicMock()
        collector.record_api_request = AsyncMock()
        collector.record_error = AsyncMock()
        collector.record_circuit_breaker_state = AsyncMock()
        return collector
        
    @pytest.fixture
    def mock_http_client(self):
        """Create a mock HTTP client for testing."""
        client = AsyncMock()
        
        # Create a mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = '{"status": "ok"}'
        
        # Configure client methods
        client.get = AsyncMock(return_value=mock_response)
        client.post = AsyncMock(return_value=mock_response)
        client.request = AsyncMock(return_value=mock_response)
        
        return client
        
    @pytest.fixture
    def health_monitor(self, mock_redis_client, mock_metrics_collector, mock_http_client):
        """Create an ApiHealthMonitor instance for testing."""
        return ApiHealthMonitor(
            redis_client=mock_redis_client,
            metrics_collector=mock_metrics_collector,
            http_client=mock_http_client,
            namespace="test_health"
        )
        
    @pytest.fixture
    def sample_health_check_config(self):
        """Create a sample health check configuration for testing."""
        return HealthCheckConfig(
            api_key="test_api",
            endpoint="https://api.example.com/health",
            method="GET",
            headers={"X-API-Key": "test_key"},
            expected_status=200,
            expected_response_pattern='"status": "ok"',
            check_interval_seconds=60,
            timeout_ms=1000,
            consecutive_failures_threshold=3,
            consecutive_successes_for_recovery=2
        )
        
    @pytest.mark.asyncio
    async def test_register_health_check(self, health_monitor, sample_health_check_config):
        """Test registering a health check."""
        # Register a health check
        await health_monitor.register_health_check(sample_health_check_config)
        
        # Verify it was registered
        assert "test_api" in health_monitor.health_checks
        assert health_monitor.health_checks["test_api"] == sample_health_check_config
        assert health_monitor.last_status["test_api"] == ApiStatus.UNKNOWN
        
    @pytest.mark.asyncio
    async def test_deregister_health_check(self, health_monitor, sample_health_check_config):
        """Test deregistering a health check."""
        # Register and then deregister a health check
        await health_monitor.register_health_check(sample_health_check_config)
        result = await health_monitor.deregister_health_check("test_api")
        
        # Verify it was deregistered
        assert result is True
        assert "test_api" not in health_monitor.health_checks
        assert "test_api" not in health_monitor.last_status
        
        # Try to deregister a non-existent health check
        result = await health_monitor.deregister_health_check("nonexistent_api")
        assert result is False
        
    @pytest.mark.asyncio
    async def test_check_health_success(self, health_monitor, sample_health_check_config, mock_http_client):
        """Test checking the health of an API with a successful response."""
        # Register a health check
        await health_monitor.register_health_check(sample_health_check_config)
        
        # Set up the mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = '{"status": "ok"}'
        mock_http_client.get.return_value = mock_response
        
        # Check health
        result = await health_monitor.check_health("test_api")
        
        # Verify the result
        assert result.api_key == "test_api"
        assert result.status == ApiStatus.HEALTHY
        assert result.status_code == 200
        assert result.consecutive_successes == 1
        assert result.consecutive_failures == 0
        assert result.error_message is None
        
        # Verify the HTTP request was made
        mock_http_client.get.assert_called_once_with(
            url="https://api.example.com/health",
            headers={"X-API-Key": "test_key"},
            timeout=1.0
        )
        
    @pytest.mark.asyncio
    async def test_check_health_failure(self, health_monitor, sample_health_check_config, mock_http_client):
        """Test checking the health of an API with a failed response."""
        # Register a health check
        await health_monitor.register_health_check(sample_health_check_config)
        
        # Set up the mock response
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = '{"status": "error"}'
        mock_http_client.get.return_value = mock_response
        
        # Check health
        result = await health_monitor.check_health("test_api")
        
        # Verify the result
        assert result.api_key == "test_api"
        assert result.status == ApiStatus.DEGRADED  # First failure, not enough for DOWN
        assert result.status_code == 500
        assert result.consecutive_successes == 0
        assert result.consecutive_failures == 1
        assert "Unexpected status code: 500" in result.error_message
        
    @pytest.mark.asyncio
    async def test_consecutive_failures_threshold(self, health_monitor, sample_health_check_config, mock_http_client):
        """Test that status changes to DOWN after consecutive failures."""
        # Register a health check
        await health_monitor.register_health_check(sample_health_check_config)
        
        # Set up the mock response for failure
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = '{"status": "error"}'
        mock_http_client.get.return_value = mock_response
        
        # Configure mock Redis to return failure counter
        self.mock_hashes["test_health:counters:test_api"] = {
            "failures": 2,  # Already had 2 failures
            "successes": 0
        }
        
        # Check health (should be the 3rd failure)
        result = await health_monitor.check_health("test_api")
        
        # Verify the result shows DOWN status after reaching threshold
        assert result.status == ApiStatus.DOWN
        assert result.consecutive_failures == 3
        
    @pytest.mark.asyncio
    async def test_consecutive_successes_recovery(self, health_monitor, sample_health_check_config, mock_http_client):
        """Test that status changes to HEALTHY after consecutive successes."""
        # Register a health check
        await health_monitor.register_health_check(sample_health_check_config)
        
        # Set initial status to DOWN
        health_monitor.last_status["test_api"] = ApiStatus.DOWN
        
        # Set up the mock response for success
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = '{"status": "ok"}'
        mock_http_client.get.return_value = mock_response
        
        # Configure mock Redis to return success counter
        self.mock_hashes["test_health:counters:test_api"] = {
            "failures": 0,
            "successes": 1  # Already had 1 success
        }
        
        # Check health (should be the 2nd success)
        result = await health_monitor.check_health("test_api")
        
        # Verify the result shows HEALTHY status after reaching threshold
        assert result.status == ApiStatus.HEALTHY
        assert result.consecutive_successes == 2
        
    @pytest.mark.asyncio
    async def test_status_change_callback(self, health_monitor, sample_health_check_config, mock_http_client):
        """Test that callbacks are called when status changes."""
        # Create a callback function
        callback_mock = AsyncMock()
        await health_monitor.register_status_change_callback(callback_mock)
        
        # Register a health check
        await health_monitor.register_health_check(sample_health_check_config)
        
        # Set up the mock response for failure
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = '{"status": "error"}'
        mock_http_client.get.return_value = mock_response
        
        # Configure mock Redis for enough failures to trigger DOWN status
        self.mock_hashes["test_health:counters:test_api"] = {
            "failures": 2,  # Already had 2 failures
            "successes": 0
        }
        
        # Check health (should go from UNKNOWN to DOWN)
        await health_monitor.check_health("test_api")
        
        # Verify the callback was called with the status change
        callback_mock.assert_called_once_with("test_api", ApiStatus.UNKNOWN, ApiStatus.DOWN)
        
    @pytest.mark.asyncio
    async def test_get_status(self, health_monitor, sample_health_check_config):
        """Test getting the current status of an API."""
        # Register a health check
        await health_monitor.register_health_check(sample_health_check_config)
        
        # Set status in memory
        health_monitor.last_status["test_api"] = ApiStatus.HEALTHY
        
        # Get status
        status = await health_monitor.get_status("test_api")
        
        # Verify the result
        assert status == ApiStatus.HEALTHY
        
        # Test getting status from Redis when not in memory
        del health_monitor.last_status["test_api"]
        self.mock_data["test_health:status:test_api"] = ApiStatus.DEGRADED.value
        
        # Get status
        status = await health_monitor.get_status("test_api")
        
        # Verify the result
        assert status == ApiStatus.DEGRADED
        
    @pytest.mark.asyncio
    async def test_get_status_history(self, health_monitor, sample_health_check_config):
        """Test getting the status history of an API."""
        # Register a health check
        await health_monitor.register_health_check(sample_health_check_config)
        
        # Create some history entries
        history_key = "test_health:history:test_api"
        now = datetime.now()
        
        result1 = HealthCheckResult(
            api_key="test_api",
            timestamp=now - timedelta(hours=1),
            status=ApiStatus.HEALTHY,
            response_time_ms=100.0,
            status_code=200
        )
        
        result2 = HealthCheckResult(
            api_key="test_api",
            timestamp=now,
            status=ApiStatus.DEGRADED,
            response_time_ms=500.0,
            status_code=500,
            error_message="Server error"
        )
        
        # Add to mock sorted set
        self.mock_sorted_sets[history_key] = {
            result1.model_dump_json(): result1.timestamp.timestamp(),
            result2.model_dump_json(): result2.timestamp.timestamp()
        }
        
        # Get history
        history = await health_monitor.get_status_history("test_api")
        
        # Verify the results
        assert len(history) == 2
        assert history[0].status == ApiStatus.DEGRADED  # Most recent first
        assert history[1].status == ApiStatus.HEALTHY
        
    @pytest.mark.asyncio
    async def test_tenant_isolation(self, health_monitor, sample_health_check_config):
        """Test multi-tenant isolation of health checks."""
        # Create tenant IDs
        tenant1 = uuid.uuid4()
        tenant2 = uuid.uuid4()
        
        # Create a copy of the config for tenant2
        tenant2_config = sample_health_check_config.model_copy()
        
        # Register health checks for different tenants
        await health_monitor.register_health_check(sample_health_check_config, tenant_id=tenant1)
        await health_monitor.register_health_check(tenant2_config, tenant_id=tenant2)
        
        # Verify they have separate check IDs
        check_id1 = health_monitor._get_check_id("test_api", tenant1)
        check_id2 = health_monitor._get_check_id("test_api", tenant2)
        
        assert check_id1 != check_id2
        assert check_id1 in health_monitor.health_checks
        assert check_id2 in health_monitor.health_checks
        
        # Verify deregistering one doesn't affect the other
        await health_monitor.deregister_health_check("test_api", tenant_id=tenant1)
        assert check_id1 not in health_monitor.health_checks
        assert check_id2 in health_monitor.health_checks