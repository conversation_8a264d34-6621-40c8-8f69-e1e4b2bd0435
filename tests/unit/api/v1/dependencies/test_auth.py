import hashlib
import uuid
from datetime import datetime, timedelta, timezone
from unittest.mock import Async<PERSON>ock, MagicMock, call, patch

import pytest
from fastapi import HTT<PERSON><PERSON>xception, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

# Functions to test
from src.coherence.api.v1.dependencies.auth import (
    authenticate_system_admin_api_key,
    get_org_api_key_principal,
    get_tenant_from_api_key,  # Test deprecated behavior
)

# Models needed for fixtures and type hints
from src.coherence.models.system_admin import SystemAdmin, SystemAdminAPIKey
from src.coherence.models.tenant import APIKey, OrganizationAPIKey, Tenant

# --- Fixtures ---

@pytest.fixture
def mock_db_session():
    """Provides a mock AsyncSession."""
    session = AsyncMock(spec=AsyncSession)
    session.execute = AsyncMock() # Mock execute method
    session.commit = AsyncMock() # Mock commit method
    session.refresh = AsyncMock() # Mock refresh method
    return session

@pytest.fixture
def mock_request():
    """Provides a mock FastAPI Request object with a mock state."""
    request = MagicMock(spec=Request)
    request.state = MagicMock()
    # Initialize state attributes to avoid AttributeError during tests
    request.state.is_system_admin = None
    request.state.system_admin_id = None
    request.state.api_key_id = None
    request.state.api_key_permissions = None
    request.state.tenant = None
    request.state.tenant_id = None
    request.state.rls_tenant_id = None
    request.state.clerk_org_id = None
    request.state.rls_clerk_org_id = None
    request.state.tenant_is_admin = None # For deprecated checks
    request.state.rls_is_admin = None # For deprecated checks
    return request

@pytest.fixture
def test_system_admin_user():
    """Provides a sample SystemAdmin object."""
    return SystemAdmin(
        id=uuid.uuid4(),
        email="<EMAIL>",
        clerk_user_id="clerk_sys_admin_123",
        is_superuser=True,
        name="Test System Admin"
    )

@pytest.fixture
def test_tenant():
    """Provides a sample Tenant object."""
    tenant_id = uuid.uuid4()
    return Tenant(
        id=tenant_id,
        name="Test Tenant",
        clerk_org_id="org_test_tenant_123",
        settings={"is_admin": False} # Example setting for deprecated check
    )

@pytest.fixture
def test_tenant_admin():
    """Provides a sample Tenant object marked as admin (old style)."""
    tenant_id = uuid.uuid4()
    return Tenant(
        id=tenant_id,
        name="Test Admin Tenant",
        clerk_org_id="org_test_admin_tenant_456",
        settings={"is_admin": True} # Example setting for deprecated check
    )

@pytest.fixture
def valid_system_admin_api_key_model(test_system_admin_user):
    """Provides a valid, non-expired, non-revoked SystemAdminAPIKey model."""
    key_id = uuid.uuid4()
    return SystemAdminAPIKey(
        id=key_id,
        system_admin_id=test_system_admin_user.id,
        key_hash=hashlib.sha256("valid_sys_key".encode()).hexdigest(),
        label="Valid Sys Key",
        prefix="vsk_",
        created_at=datetime.now(timezone.utc),
        expires_at=datetime.now(timezone.utc) + timedelta(days=30),
        last_used_at=None,
        revoked=False
    )

@pytest.fixture
def revoked_system_admin_api_key_model(test_system_admin_user):
    """Provides a revoked SystemAdminAPIKey model."""
    return SystemAdminAPIKey(
        id=uuid.uuid4(),
        system_admin_id=test_system_admin_user.id,
        key_hash=hashlib.sha256("revoked_sys_key".encode()).hexdigest(),
        label="Revoked Sys Key",
        prefix="rsk_",
        revoked=True,
        expires_at=datetime.now(timezone.utc) + timedelta(days=30)
    )

@pytest.fixture
def expired_system_admin_api_key_model(test_system_admin_user):
    """Provides an expired SystemAdminAPIKey model."""
    return SystemAdminAPIKey(
        id=uuid.uuid4(),
        system_admin_id=test_system_admin_user.id,
        key_hash=hashlib.sha256("expired_sys_key".encode()).hexdigest(),
        label="Expired Sys Key",
        prefix="esk_",
        revoked=False,
        expires_at=datetime.now(timezone.utc) - timedelta(days=1)
    )

@pytest.fixture
def valid_org_api_key_model(test_tenant):
    """Provides a valid OrganizationAPIKey model."""
    return OrganizationAPIKey(
        id=uuid.uuid4(),
        clerk_org_id=test_tenant.clerk_org_id,
        key_hash=hashlib.sha256("valid_org_key".encode()).hexdigest(),
        label="Valid Org Key",
        prefix="vok_",
        revoked=False,
        expires_at=datetime.now(timezone.utc) + timedelta(days=30),
        permissions=["workflow:read", "workflow:execute"]
    )

@pytest.fixture
def valid_old_api_key_model(test_tenant):
    """Provides a valid (deprecated) APIKey model."""
    return APIKey(
        id=uuid.uuid4(),
        tenant_id=test_tenant.id,
        key_hash=hashlib.sha256("valid_old_key".encode()).hexdigest(),
        label="Valid Old Key",
        prefix="old_",
        revoked=False,
    )

# --- Tests for authenticate_system_admin_api_key ---

@pytest.mark.asyncio
@patch('src.coherence.api.v1.dependencies.auth.get_system_admin_api_key_by_hash')
@patch('src.coherence.api.v1.dependencies.auth.get_system_admin_by_id')
@patch('src.coherence.api.v1.dependencies.auth.update_system_admin_api_key')
@patch('src.coherence.api.v1.dependencies.auth.datetime') # To mock datetime.now
async def test_authenticate_system_admin_api_key_valid(
    mock_datetime,
    mock_update_key,
    mock_get_admin_by_id,
    mock_get_key_by_hash,
    mock_request,
    mock_db_session,
    test_system_admin_user,
    valid_system_admin_api_key_model
):
    """Tests successful authentication with a valid system admin key."""
    fixed_now = datetime.now(timezone.utc)
    mock_datetime.now.return_value = fixed_now
    # Allow constructing timezone.utc without mocking issues
    mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw) if args else fixed_now

    mock_get_key_by_hash.return_value = valid_system_admin_api_key_model
    mock_get_admin_by_id.return_value = test_system_admin_user

    api_key_value = "valid_sys_key"
    authenticated_admin = await authenticate_system_admin_api_key(
        request=mock_request, x_api_key=api_key_value, db=mock_db_session
    )

    assert authenticated_admin == test_system_admin_user
    mock_get_key_by_hash.assert_called_once_with(mock_db_session, key_hash=hashlib.sha256(api_key_value.encode()).hexdigest())
    mock_get_admin_by_id.assert_called_once_with(mock_db_session, id=valid_system_admin_api_key_model.system_admin_id)
    mock_update_key.assert_called_once()
    # Check arguments passed to the mocked update function
    call_args = mock_update_key.call_args
    assert call_args == call(db=mock_db_session, db_obj=valid_system_admin_api_key_model, obj_in={'last_used_at': fixed_now})

    # Check request state
    assert mock_request.state.is_system_admin is True
    assert mock_request.state.system_admin_id == test_system_admin_user.id
    assert mock_request.state.api_key_id == valid_system_admin_api_key_model.id
    assert mock_request.state.api_key_permissions == ["system:*"]
    assert mock_request.state.tenant is None
    assert mock_request.state.tenant_id is None
    assert mock_request.state.clerk_org_id is None

@pytest.mark.asyncio
@patch('src.coherence.api.v1.dependencies.auth.get_system_admin_api_key_by_hash')
async def test_authenticate_system_admin_api_key_not_found(
    mock_get_key_by_hash, mock_request, mock_db_session
):
    """Tests authentication failure when the key hash is not found."""
    mock_get_key_by_hash.return_value = None
    api_key_value = "invalid_key"

    with pytest.raises(HTTPException) as exc_info:
        await authenticate_system_admin_api_key(
            request=mock_request, x_api_key=api_key_value, db=mock_db_session
        )
    assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
    assert exc_info.value.detail == "Invalid System Administrator API Key"

@pytest.mark.asyncio
@patch('src.coherence.api.v1.dependencies.auth.get_system_admin_api_key_by_hash')
async def test_authenticate_system_admin_api_key_revoked(
    mock_get_key_by_hash, mock_request, mock_db_session, revoked_system_admin_api_key_model
):
    """Tests authentication failure when the key is revoked."""
    mock_get_key_by_hash.return_value = revoked_system_admin_api_key_model
    api_key_value = "revoked_sys_key"

    with pytest.raises(HTTPException) as exc_info:
        await authenticate_system_admin_api_key(
            request=mock_request, x_api_key=api_key_value, db=mock_db_session
        )
    assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
    assert exc_info.value.detail == "System Administrator API Key has been revoked"

@pytest.mark.asyncio
@patch('src.coherence.api.v1.dependencies.auth.get_system_admin_api_key_by_hash')
@patch('src.coherence.api.v1.dependencies.auth.datetime')
async def test_authenticate_system_admin_api_key_expired(
    mock_datetime, mock_get_key_by_hash, mock_request, mock_db_session, expired_system_admin_api_key_model
):
    """Tests authentication failure when the key is expired."""
    # Ensure the mocked 'now' is after the key's expiry
    mock_datetime.now.return_value = datetime.now(timezone.utc) + timedelta(hours=1)
    mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw) if args else mock_datetime.now.return_value

    mock_get_key_by_hash.return_value = expired_system_admin_api_key_model
    api_key_value = "expired_sys_key"

    with pytest.raises(HTTPException) as exc_info:
        await authenticate_system_admin_api_key(
            request=mock_request, x_api_key=api_key_value, db=mock_db_session
        )
    assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
    assert exc_info.value.detail == "System Administrator API Key has expired"

@pytest.mark.asyncio
@patch('src.coherence.api.v1.dependencies.auth.get_system_admin_api_key_by_hash')
@patch('src.coherence.api.v1.dependencies.auth.get_system_admin_by_id')
async def test_authenticate_system_admin_api_key_admin_not_found(
    mock_get_admin_by_id,
    mock_get_key_by_hash,
    mock_request,
    mock_db_session,
    valid_system_admin_api_key_model # Use a valid key model
):
    """Tests failure when the API key is valid but the associated admin record is missing."""
    mock_get_key_by_hash.return_value = valid_system_admin_api_key_model
    mock_get_admin_by_id.return_value = None # Simulate admin not found

    api_key_value = "valid_sys_key"
    with pytest.raises(HTTPException) as exc_info:
        await authenticate_system_admin_api_key(
            request=mock_request, x_api_key=api_key_value, db=mock_db_session
        )
    assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert exc_info.value.detail == "Associated System Administrator account not found."
    mock_get_admin_by_id.assert_called_once_with(mock_db_session, id=valid_system_admin_api_key_model.system_admin_id)


# --- Tests for get_org_api_key_principal ---

@pytest.mark.asyncio
@patch('src.coherence.api.v1.dependencies.auth.get_system_admin_api_key_by_hash')
@patch('src.coherence.api.v1.dependencies.auth.select') # Mock select for OrganizationAPIKey query
@patch('src.coherence.api.v1.dependencies.auth.datetime') # Mock datetime for expiry check
async def test_get_org_api_key_principal_valid_org_key(
    mock_datetime,
    mock_select,
    mock_sys_key_check,
    mock_request,
    mock_db_session,
    test_tenant,
    valid_org_api_key_model
):
    """Tests successful authentication with a valid OrganizationAPIKey."""
    fixed_now = datetime.now(timezone.utc)
    mock_datetime.now.return_value = fixed_now
    mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw) if args else fixed_now

    mock_sys_key_check.return_value = None # Ensure it's not mistaken for a sys admin key

    # Mock the database result for OrganizationAPIKey lookup
    mock_org_key_result = AsyncMock()
    mock_org_key_result.scalar_one_or_none.return_value = valid_org_api_key_model
    # Mock the database result for Tenant lookup
    mock_tenant_result = AsyncMock()
    mock_tenant_result.scalar_one_or_none.return_value = test_tenant

    mock_db_session.execute.side_effect = [mock_org_key_result, mock_tenant_result]

    api_key_value = "valid_org_key"
    authenticated_tenant = await get_org_api_key_principal(
        request=mock_request, x_api_key=api_key_value, db=mock_db_session
    )

    assert authenticated_tenant == test_tenant
    mock_sys_key_check.assert_called_once_with(mock_db_session, key_hash=hashlib.sha256(api_key_value.encode()).hexdigest())
    assert mock_db_session.execute.call_count == 2
    mock_db_session.commit.assert_called_once() # Called to update last_used_at

    # Check request state
    assert mock_request.state.is_system_admin is False
    assert mock_request.state.tenant == test_tenant
    assert mock_request.state.tenant_id == test_tenant.id
    assert mock_request.state.clerk_org_id == test_tenant.clerk_org_id
    assert mock_request.state.api_key_permissions == valid_org_api_key_model.permissions

@pytest.mark.asyncio
@patch('src.coherence.api.v1.dependencies.auth.get_system_admin_api_key_by_hash')
async def test_get_org_api_key_principal_with_system_admin_key(
    mock_sys_key_check,
    mock_request,
    mock_db_session,
    valid_system_admin_api_key_model # Use a sys admin key model
):
    """Tests that using a SystemAdminAPIKey with get_org_api_key_principal raises an error."""
    mock_sys_key_check.return_value = valid_system_admin_api_key_model # Simulate finding a sys admin key

    api_key_value = "valid_sys_key" # The key value corresponding to the fixture hash
    with pytest.raises(HTTPException) as exc_info:
        await get_org_api_key_principal(
            request=mock_request, x_api_key=api_key_value, db=mock_db_session
        )

    assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
    assert "System Administrator API Key cannot be used" in exc_info.value.detail
    mock_sys_key_check.assert_called_once_with(mock_db_session, key_hash=hashlib.sha256(api_key_value.encode()).hexdigest())
    mock_db_session.execute.assert_not_called() # Should fail before querying OrganizationAPIKey


# --- Tests for get_tenant_from_api_key (Deprecated) ---

@pytest.mark.asyncio
@patch('src.coherence.api.v1.dependencies.auth.select') # Mock select for APIKey query
async def test_get_tenant_from_api_key_valid_old_key(
    mock_select,
    mock_request,
    mock_db_session,
    test_tenant,
    valid_old_api_key_model
):
    """Tests successful authentication with a valid key from the old APIKey table."""
    # Mock the database result for APIKey lookup
    mock_old_key_result = AsyncMock()
    mock_old_key_result.scalar_one_or_none.return_value = valid_old_api_key_model
    # Mock the database result for Tenant lookup
    mock_tenant_result = AsyncMock()
    mock_tenant_result.scalar_one_or_none.return_value = test_tenant

    mock_db_session.execute.side_effect = [mock_old_key_result, mock_tenant_result]

    api_key_value = "valid_old_key"
    authenticated_tenant = await get_tenant_from_api_key(
        request=mock_request, x_api_key=api_key_value, db=mock_db_session
    )

    assert authenticated_tenant == test_tenant
    assert mock_db_session.execute.call_count == 2
    mock_db_session.commit.assert_called_once() # Called to update last_used_at

    # Check request state (important for deprecated function compatibility)
    assert mock_request.state.is_system_admin is False
    assert mock_request.state.tenant == test_tenant
    assert mock_request.state.tenant_id == test_tenant.id
    assert mock_request.state.tenant_is_admin is False # Based on test_tenant fixture
    assert mock_request.state.rls_is_admin is False # Based on test_tenant fixture

@pytest.mark.asyncio
@patch('src.coherence.api.v1.dependencies.auth.select') # Mock select for APIKey query
async def test_get_tenant_from_api_key_with_system_admin_key(
    mock_select,
    mock_request,
    mock_db_session,
    valid_system_admin_api_key_model # Use a sys admin key model
):
    """
    Tests that using a SystemAdminAPIKey with the deprecated get_tenant_from_api_key
    now fails correctly (as it only checks the old APIKey table).
    """
    # Mock the database result for APIKey lookup - should return None because the key isn't there
    mock_old_key_result = AsyncMock()
    mock_old_key_result.scalar_one_or_none.return_value = None
    mock_db_session.execute.return_value = mock_old_key_result

    api_key_value = "valid_sys_key" # The key value corresponding to the sys admin fixture hash
    with pytest.raises(HTTPException) as exc_info:
        await get_tenant_from_api_key(
            request=mock_request, x_api_key=api_key_value, db=mock_db_session
        )

    assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
    assert exc_info.value.detail == "Invalid API key (old system)"
    # Ensure it only tried to query the old APIKey table once
    assert mock_db_session.execute.call_count == 1