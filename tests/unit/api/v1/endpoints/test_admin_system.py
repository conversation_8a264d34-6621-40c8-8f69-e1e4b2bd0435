"""
Tests for the admin_system endpoints.
"""
import uuid
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

import pytest
from fastapi import status
from httpx import AsyncClient


@pytest.fixture
def mock_auth():
    """Mock authentication to simulate system admin access."""
    with patch("src.coherence.api.v1.endpoints.admin_system.check_is_system_admin") as mock:
        # Return a mock system admin
        mock_admin = AsyncMock()
        mock_admin.id = uuid.uuid4()
        mock_admin.clerk_user_id = "test_clerk_user_123"
        mock.return_value = mock_admin
        yield mock


class TestSystemAdminEndpoints:
    
    async def test_create_system_admin(self, client: AsyncClient, mock_auth, mocker):
        """Test creating a new system admin."""
        # Mock the create_system_admin function
        mock_create = mocker.patch("src.coherence.api.v1.endpoints.admin_system.create_system_admin")
        mock_create.return_value = AsyncMock(
            id=uuid.uuid4(),
            clerk_user_id="new_clerk_user_123",
            created_at=datetime.now(),
            created_by="test_creator"
        )
        
        # Mock audit log to avoid DB calls
        mocker.patch("src.coherence.api.v1.endpoints.admin_system.log_audit_event")
        
        # Make the request
        data = {
            "clerk_user_id": "new_clerk_user_123",
            "created_by": "test_creator"
        }
        response = await client.post("/v1/admin/system/system-admins", json=data)
        
        # Check response
        assert response.status_code == status.HTTP_201_CREATED
        assert "id" in response.json()
        assert response.json()["clerk_user_id"] == "new_clerk_user_123"
        
        # Verify create_system_admin was called
        mock_create.assert_called_once()
    
    async def test_list_system_admins(self, client: AsyncClient, mock_auth, mocker):
        """Test listing system admins."""
        # Mock the get_multi_system_admin function
        mock_get_multi = mocker.patch("src.coherence.api.v1.endpoints.admin_system.get_multi_system_admin")
        mock_get_multi.return_value = [
            AsyncMock(
                id=uuid.uuid4(),
                clerk_user_id="admin1",
                created_at=datetime.now(),
                created_by="test_creator"
            ),
            AsyncMock(
                id=uuid.uuid4(),
                clerk_user_id="admin2",
                created_at=datetime.now(),
                created_by="test_creator"
            )
        ]
        
        # Make the request
        response = await client.get("/v1/admin/system/system-admins")
        
        # Check response
        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) == 2
        assert response.json()[0]["clerk_user_id"] == "admin1"
        assert response.json()[1]["clerk_user_id"] == "admin2"
        
        # Verify get_multi_system_admin was called
        mock_get_multi.assert_called_once()


class TestSystemAdminAPIKeyEndpoints:
    
    async def test_create_api_key(self, client: AsyncClient, mock_auth, mocker):
        """Test creating a new API key."""
        # Mock the create_system_admin_api_key function
        api_key_id = uuid.uuid4()
        admin_id = mock_auth.return_value.id
        
        mock_create = mocker.patch("src.coherence.api.v1.endpoints.admin_system.create_system_admin_api_key")
        mock_create.return_value = (
            AsyncMock(
                id=api_key_id,
                name="Test API Key",
                key_hash="hashed_key",
                system_admin_id=admin_id,
                created_at=datetime.now(),
                created_by="test_creator",
                revoked=False,
                last_used_at=None,
                expires_at=datetime.now() + timedelta(days=30),
                permissions={"admin": True}
            ),
            "raw_api_key_123"
        )
        
        # Mock audit log to avoid DB calls
        mocker.patch("src.coherence.api.v1.endpoints.admin_system.log_audit_event")
        
        # Make the request
        data = {
            "name": "Test API Key",
            "created_by": "test_creator",
            "permissions": {"admin": True},
            "expires_at": (datetime.now() + timedelta(days=30)).isoformat()
        }
        response = await client.post("/v1/admin/system/api-keys", json=data)
        
        # Check response
        assert response.status_code == status.HTTP_201_CREATED
        assert "id" in response.json()
        assert response.json()["name"] == "Test API Key"
        assert response.json()["api_key"] == "raw_api_key_123"
        
        # Verify create_system_admin_api_key was called with correct admin ID
        mock_create.assert_called_once()
        _, kwargs = mock_create.call_args
        assert kwargs["system_admin_id"] == admin_id
    
    async def test_list_api_keys(self, client: AsyncClient, mock_auth, mocker):
        """Test listing API keys."""
        # Mock the get_multi_system_admin_api_key_for_admin function
        admin_id = mock_auth.return_value.id
        
        mock_get_multi = mocker.patch("src.coherence.api.v1.endpoints.admin_system.get_multi_system_admin_api_key_for_admin")
        mock_get_multi.return_value = [
            AsyncMock(
                id=uuid.uuid4(),
                name="API Key 1",
                key_hash="hashed_key_1",
                system_admin_id=admin_id,
                created_at=datetime.now(),
                created_by="test_creator",
                revoked=False,
                permissions={"admin": True}
            ),
            AsyncMock(
                id=uuid.uuid4(),
                name="API Key 2",
                key_hash="hashed_key_2",
                system_admin_id=admin_id,
                created_at=datetime.now(),
                created_by="test_creator",
                revoked=True,
                permissions={"read": True}
            )
        ]
        
        # Make the request
        response = await client.get("/v1/admin/system/api-keys")
        
        # Check response
        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) == 2
        assert response.json()[0]["name"] == "API Key 1"
        assert response.json()[1]["name"] == "API Key 2"
        
        # Verify get_multi_system_admin_api_key_for_admin was called with correct admin ID
        mock_get_multi.assert_called_once()
        _, kwargs = mock_get_multi.call_args
        assert kwargs["system_admin_id"] == admin_id