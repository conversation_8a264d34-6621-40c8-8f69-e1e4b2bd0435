"""
Tests for the resolve.py endpoints
"""

from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ock, patch

import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.endpoints.resolve import get_tenant_with_admin_bypass
from src.coherence.models.tenant import Tenant


@pytest.mark.asyncio
async def test_get_tenant_with_admin_bypass_with_api_key():
    """Test that the function returns the correct tenant when API key is provided"""
    # Create mock request with API key
    mock_request = MagicMock(spec=Request)
    mock_request.headers = {"X-API-Key": "test-api-key"}
    
    # Mock db session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Mock API key lookup
    from src.coherence.models.tenant import OrganizationAPIKey
    
    # Create mock API key
    mock_api_key = MagicMock(spec=OrganizationAPIKey)
    mock_api_key.id = "api-key-id"
    mock_api_key.clerk_org_id = "clerk-org-id"
    mock_api_key.permissions = ["test:permission"]
    mock_api_key.revoked = False
    mock_api_key.expires_at = None
    
    # Create mock tenant
    mock_tenant = MagicMock(spec=Tenant)
    mock_tenant.id = "test-tenant-id"
    mock_tenant.clerk_org_id = "clerk-org-id"
    
    # Mock query execution
    api_key_result = MagicMock()
    api_key_result.scalar_one_or_none.return_value = mock_api_key
    
    tenant_result = MagicMock()
    tenant_result.scalar_one_or_none.return_value = mock_tenant
    
    # Configure the mock db to return our mocked results
    mock_db.execute.side_effect = [api_key_result, tenant_result]
    
    # Patch config and is_system_admin
    with patch("src.coherence.api.v1.endpoints.resolve.is_system_admin", return_value=False), \
         patch("src.coherence.core.config.settings.SYSTEM_ADMIN_API_KEY", "different-key"), \
         patch("hashlib.sha256") as mock_hash:
        
        # Configure the mock hash
        mock_hash_instance = MagicMock()
        mock_hash_instance.hexdigest.return_value = "test-hash"
        mock_hash.return_value = mock_hash_instance
        
        # Call the function
        result = await get_tenant_with_admin_bypass(
            request=mock_request,
            clerk_auth=MagicMock(),
            db=mock_db,
        )
        
        # Assert that the function returned the correct tenant
        assert result == mock_tenant
        
        # Verify that the tenant was set in request state
        assert mock_request.state.tenant == mock_tenant
        assert mock_request.state.tenant_id == mock_tenant.id
        assert mock_request.state.rls_tenant_id == str(mock_tenant.id)


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.resolve.is_system_admin")
async def test_get_tenant_with_admin_bypass_with_system_admin_org(mock_is_system_admin):
    """Test that the function finds the appropriate tenant for system admin with org_id"""
    # Mock is_system_admin to return True
    mock_is_system_admin.return_value = True
    
    # Create mock request
    mock_request = MagicMock(spec=Request)
    mock_request.state = MagicMock()
    
    # Create mock clerk auth with org_id
    mock_clerk_auth = MagicMock()
    mock_clerk_auth.org_id = "test-org-id"
    mock_clerk_auth.user_id = "test-user-id"
    
    # Create mock db session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Create mock tenant that will be returned from the query
    mock_tenant = MagicMock(spec=Tenant)
    mock_tenant.id = "test-tenant-id"
    mock_tenant.clerk_org_id = "test-org-id"
    
    # Mock the query execution
    mock_result = MagicMock()
    mock_result.scalar_one_or_none.return_value = mock_tenant
    mock_db.execute.return_value = mock_result
    
    # Call the function
    result = await get_tenant_with_admin_bypass(
        request=mock_request,
        clerk_auth=mock_clerk_auth,
        db=mock_db,
    )
    
    # Assert that the function returned the tenant for the org_id
    assert result == mock_tenant
    
    # Verify that the tenant context was set in the request state
    assert mock_request.state.tenant == mock_tenant
    assert mock_request.state.tenant_id == mock_tenant.id
    assert mock_request.state.rls_tenant_id == str(mock_tenant.id)
    
    # Verify that the correct query was executed
    mock_db.execute.assert_called_once()
    assert "clerk_org_id" in str(mock_db.execute.call_args[0][0])


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.resolve.is_system_admin")
async def test_get_tenant_with_admin_bypass_with_system_admin_no_org(mock_is_system_admin):
    """Test that the function finds a default tenant for system admin without org_id"""
    # Mock is_system_admin to return True
    mock_is_system_admin.return_value = True
    
    # Create mock request
    mock_request = MagicMock(spec=Request)
    mock_request.state = MagicMock()
    
    # Create mock clerk auth without org_id
    mock_clerk_auth = MagicMock()
    mock_clerk_auth.org_id = None
    mock_clerk_auth.user_id = "test-user-id"
    
    # Create mock db session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Create mock tenant that will be returned as default
    mock_tenant = MagicMock(spec=Tenant)
    mock_tenant.id = "default-tenant-id"
    
    # Mock the query execution - first query returns None, second returns the default tenant
    mock_result_default = MagicMock()
    mock_result_default.scalar_one_or_none.return_value = mock_tenant
    mock_db.execute.return_value = mock_result_default
    
    # Call the function
    result = await get_tenant_with_admin_bypass(
        request=mock_request,
        clerk_auth=mock_clerk_auth,
        db=mock_db,
    )
    
    # Assert that the function returned the default tenant
    assert result == mock_tenant
    
    # Verify that the tenant context was set in the request state
    assert mock_request.state.tenant == mock_tenant
    assert mock_request.state.tenant_id == mock_tenant.id
    assert mock_request.state.rls_tenant_id == str(mock_tenant.id)


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.resolve.is_system_admin")
async def test_get_tenant_with_admin_bypass_with_system_admin_no_tenants(mock_is_system_admin):
    """Test that the function raises an error when no tenants are found for system admin"""
    # Mock is_system_admin to return True
    mock_is_system_admin.return_value = True
    
    # Create mock clerk auth
    mock_clerk_auth = MagicMock()
    
    # Create mock db session that returns no tenants
    mock_db = AsyncMock(spec=AsyncSession)
    mock_result = MagicMock()
    mock_result.scalar_one_or_none.return_value = None
    mock_db.execute.return_value = mock_result
    
    # Call the function and expect exception
    with pytest.raises(HTTPException) as excinfo:
        await get_tenant_with_admin_bypass(
            request=MagicMock(),
            clerk_auth=mock_clerk_auth,
            db=mock_db,
        )
    
    # Verify the exception details
    assert excinfo.value.status_code == 500
    assert "No tenants available" in excinfo.value.detail


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.resolve.is_system_admin")
async def test_get_tenant_with_admin_bypass_not_system_admin_no_api_key(mock_is_system_admin):
    """Test that the function raises an error for non-admins with no API key"""
    # Mock is_system_admin to return False
    mock_is_system_admin.return_value = False
    
    # Create mock request with no API key
    mock_request = MagicMock(spec=Request)
    mock_request.headers = {}  # No X-API-Key header
    
    # Call the function and expect exception
    with pytest.raises(HTTPException) as excinfo:
        await get_tenant_with_admin_bypass(
            request=mock_request,
            clerk_auth=MagicMock(),
            db=MagicMock(),
        )
    
    # Verify the exception details
    assert excinfo.value.status_code == 401
    assert "API key required" in excinfo.value.detail