"""
Tests for the admin_templates.py vectorization hooks
"""

import uuid
from unittest.mock import Async<PERSON>ock, MagicMock, patch

import pytest
from fastapi import BackgroundTasks, Request
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.dependencies.auth import ClerkAuthDetails
from src.coherence.api.v1.endpoints.admin_templates import (
    create_template,
    delete_template,
    update_template,
)
from src.coherence.models.template import Template as TemplateModel
from src.coherence.schemas.admin_template import (
    AdminTemplate,
    AdminTemplateCategory,
    AdminTemplateCreate,
    AdminTemplateUpdate,
)


# Helper function to create a proper AsyncMock for CRUD methods
def async_return(result):
    """Create an AsyncMock that returns the given value when awaited."""
    mock = AsyncMock()
    mock.return_value = result
    return mock


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.admin_templates.VectorIndexer")
@patch("src.coherence.api.v1.endpoints.admin_templates.crud_admin_template")
@patch("src.coherence.api.v1.endpoints.admin_templates._construct_admin_template_response")
async def test_create_template_with_vectorization(
    mock_construct_response, mock_crud, mock_vector_indexer
):
    """Test that creating an intent router template triggers vectorization"""
    # Setup test data
    tenant_id = str(uuid.uuid4())
    template_id = uuid.uuid4()
    
    # Mock request with tenant_id
    mock_request = MagicMock(spec=Request)
    mock_request.state.tenant_id = tenant_id
    
    # Mock ClerkAuthDetails
    mock_auth = MagicMock(spec=ClerkAuthDetails)
    mock_auth.user_id = str(uuid.uuid4())
    
    # Mock DB session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Create template request body
    template_in = AdminTemplateCreate(
        key="test-intent-template",
        description="Test intent template",
        category=AdminTemplateCategory.INTENT_ROUTER,
        language="en",
        body="Test template body",
    )
    
    # Mock the created template from DB
    mock_db_template = MagicMock(spec=TemplateModel)
    mock_db_template.id = template_id
    mock_db_template.key = template_in.key
    mock_db_template.category = template_in.category
    mock_db_template.body = template_in.body
    mock_db_template.description = template_in.description
    
    # Setup mock return values as async functions
    mock_crud.create_with_tenant = async_return(mock_db_template)
    
    # Create mock AdminTemplate for return value
    mock_admin_template = MagicMock(spec=AdminTemplate)
    mock_construct_response.return_value = mock_admin_template
    
    # Setup background tasks mock
    background_tasks = MagicMock(spec=BackgroundTasks)
    
    # Setup async mock return for upsert_template
    mock_vector_indexer_instance = mock_vector_indexer.return_value
    mock_vector_indexer_instance.upsert_template = async_return(True)
    
    # Call the function
    await create_template(
        request=mock_request,
        template_in=template_in,
        background_tasks=background_tasks,
        db=mock_db,
        current_user=mock_auth,
    )
    
    # Verify the response is constructed correctly
    assert mock_construct_response.called
    assert mock_construct_response.call_args[0][0] == mock_db_template
    
    # Verify vectorization was added as a background task for intent router templates
    background_tasks.add_task.assert_called_once()
    
    # Check that the VectorIndexer was initialized
    mock_vector_indexer.assert_called_once()


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.admin_templates.VectorIndexer")
@patch("src.coherence.api.v1.endpoints.admin_templates.crud_admin_template")
@patch("src.coherence.api.v1.endpoints.admin_templates._construct_admin_template_response")
async def test_update_template_with_vectorization(
    mock_construct_response, mock_crud, mock_vector_indexer
):
    """Test that updating an intent router template triggers vectorization"""
    # Setup test data
    tenant_id = str(uuid.uuid4())
    template_id = uuid.uuid4()
    
    # Mock request with tenant_id
    mock_request = MagicMock(spec=Request)
    mock_request.state.tenant_id = tenant_id
    mock_request.state.is_system_admin = False
    
    # Mock ClerkAuthDetails
    mock_auth = MagicMock(spec=ClerkAuthDetails)
    mock_auth.user_id = str(uuid.uuid4())
    
    # Mock DB session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Create template update data
    template_in = AdminTemplateUpdate(
        key="test-intent-template",
        description="Updated test intent template",
        body="Updated template body",
    )
    
    # Mock the updated template from DB
    mock_db_template = MagicMock(spec=TemplateModel)
    mock_db_template.id = template_id
    mock_db_template.key = template_in.key
    mock_db_template.category = AdminTemplateCategory.INTENT_ROUTER.value
    mock_db_template.body = template_in.body
    mock_db_template.description = template_in.description
    
    # Setup mock return values as async functions
    mock_crud.update_template = async_return(mock_db_template)
    
    # Create mock AdminTemplate for return value
    mock_admin_template = MagicMock(spec=AdminTemplate)
    mock_construct_response.return_value = mock_admin_template
    
    # Setup background tasks mock
    background_tasks = MagicMock(spec=BackgroundTasks)
    
    # Setup async mock return for upsert_template
    mock_vector_indexer_instance = mock_vector_indexer.return_value
    mock_vector_indexer_instance.upsert_template = async_return(True)
    
    # Call the function
    await update_template(
        request=mock_request,
        template_id=template_id,
        template_in=template_in,
        background_tasks=background_tasks,
        db=mock_db,
        current_user=mock_auth,
    )
    
    # Verify the response is constructed correctly
    assert mock_construct_response.called
    assert mock_construct_response.call_args[0][0] == mock_db_template
    
    # Verify vectorization was added as a background task for intent router templates
    background_tasks.add_task.assert_called_once()
    
    # Check that the VectorIndexer was initialized
    mock_vector_indexer.assert_called_once()


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.admin_templates.VectorIndexer")
@patch("src.coherence.api.v1.endpoints.admin_templates.crud_admin_template")
@patch("src.coherence.api.v1.endpoints.admin_templates._construct_admin_template_response")
async def test_delete_template_with_vector_cleanup(
    mock_construct_response, mock_crud, mock_vector_indexer
):
    """Test that deleting an intent router template triggers vector cleanup"""
    # Setup test data
    tenant_id = str(uuid.uuid4())
    template_id = uuid.uuid4()
    
    # Mock request with tenant_id
    mock_request = MagicMock(spec=Request)
    mock_request.state.tenant_id = tenant_id
    mock_request.state.is_system_admin = False
    
    # Mock ClerkAuthDetails
    mock_auth = MagicMock(spec=ClerkAuthDetails)
    mock_auth.user_id = str(uuid.uuid4())
    
    # Mock DB session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Mock the template to be deleted
    mock_template = MagicMock(spec=TemplateModel)
    mock_template.id = template_id
    mock_template.key = "test-intent-template"
    mock_template.category = AdminTemplateCategory.INTENT_ROUTER.value
    mock_template.body = "Template body"
    mock_template.description = "Template description"
    mock_template.protected = False
    
    # Mock the deleted template result
    mock_deleted_template = MagicMock(spec=TemplateModel)
    mock_deleted_template.id = template_id
    
    # Setup mock return values as async functions
    mock_crud.get_by_id_and_tenant = async_return(mock_template)
    mock_crud.remove_by_id_and_tenant = async_return(mock_deleted_template)
    
    # Create mock AdminTemplate for return value
    mock_admin_template = MagicMock(spec=AdminTemplate)
    mock_construct_response.return_value = mock_admin_template
    
    # Setup background tasks mock
    background_tasks = MagicMock(spec=BackgroundTasks)
    
    # Setup async mock return for delete_template
    mock_vector_indexer_instance = mock_vector_indexer.return_value
    mock_vector_indexer_instance.delete_template = async_return(True)
    
    # Call the function
    await delete_template(
        request=mock_request,
        template_id=template_id,
        background_tasks=background_tasks,
        db=mock_db,
        current_user=mock_auth,
    )
    
    # Verify the response is constructed correctly
    assert mock_construct_response.called
    assert mock_construct_response.call_args[0][0] == mock_deleted_template
    
    # Verify vector deletion was added as a background task for intent router templates
    background_tasks.add_task.assert_called_once()
    
    # Check that the VectorIndexer was initialized
    mock_vector_indexer.assert_called_once()


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.admin_templates.VectorIndexer")
@patch("src.coherence.api.v1.endpoints.admin_templates.crud_admin_template")
@patch("src.coherence.api.v1.endpoints.admin_templates._construct_admin_template_response")
async def test_create_template_no_vectorization_for_non_intent_templates(
    mock_construct_response, mock_crud, mock_vector_indexer
):
    """Test that creating a non-intent router template doesn't trigger vectorization"""
    # Setup test data
    tenant_id = str(uuid.uuid4())
    template_id = uuid.uuid4()
    
    # Mock request with tenant_id
    mock_request = MagicMock(spec=Request)
    mock_request.state.tenant_id = tenant_id
    
    # Mock ClerkAuthDetails
    mock_auth = MagicMock(spec=ClerkAuthDetails)
    mock_auth.user_id = str(uuid.uuid4())
    
    # Mock DB session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Create template request body (non-INTENT_ROUTER category)
    template_in = AdminTemplateCreate(
        key="test-response-template",
        description="Test response template",
        category=AdminTemplateCategory.RESPONSE_GEN,  # Not an intent router
        language="en",
        body="Test template body",
    )
    
    # Mock the created template from DB
    mock_db_template = MagicMock(spec=TemplateModel)
    mock_db_template.id = template_id
    mock_db_template.key = template_in.key
    mock_db_template.category = template_in.category
    mock_db_template.body = template_in.body
    mock_db_template.description = template_in.description
    
    # Setup mock return values as async functions
    mock_crud.create_with_tenant = async_return(mock_db_template)
    
    # Create mock AdminTemplate for return value
    mock_admin_template = MagicMock(spec=AdminTemplate)
    mock_construct_response.return_value = mock_admin_template
    
    # Setup background tasks mock
    background_tasks = MagicMock(spec=BackgroundTasks)
    
    # Call the function
    await create_template(
        request=mock_request,
        template_in=template_in,
        background_tasks=background_tasks,
        db=mock_db,
        current_user=mock_auth,
    )
    
    # Verify the response is constructed correctly
    assert mock_construct_response.called
    assert mock_construct_response.call_args[0][0] == mock_db_template
    
    # Verify vectorization was NOT added as a background task for non-intent templates
    background_tasks.add_task.assert_not_called()
    
    # Check that the VectorIndexer was NOT initialized
    mock_vector_indexer.assert_not_called()