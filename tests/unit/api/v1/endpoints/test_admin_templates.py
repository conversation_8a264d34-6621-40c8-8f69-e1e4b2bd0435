"""
Tests for the admin_templates.py endpoints
"""

import uuid
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
from fastapi import BackgroundTasks, Request
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.dependencies.auth import ClerkAuthDetails
from src.coherence.api.v1.endpoints.admin_templates import (
    create_template,
    delete_template,
    update_template,
)
from src.coherence.models.template import Template as TemplateModel
from src.coherence.schemas.admin_template import (
    AdminTemplate,
    AdminTemplateCategory,
    AdminTemplateCreate,
    AdminTemplateScope,
    AdminTemplateUpdate,
)


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.admin_templates.VectorIndexer")
@patch("src.coherence.api.v1.endpoints.admin_templates.crud_admin_template")
@patch("src.coherence.api.v1.endpoints.admin_templates._construct_admin_template_response")
async def test_create_template_with_vectorization(
    mock_construct_response, mock_crud, mock_vector_indexer
):
    """Test that creating an intent router template triggers vectorization"""
    # Setup test data
    tenant_id = str(uuid.uuid4())
    template_id = uuid.uuid4()
    
    # Mock request with tenant_id
    mock_request = MagicMock(spec=Request)
    mock_request.state.tenant_id = tenant_id
    
    # Mock ClerkAuthDetails
    mock_auth = MagicMock(spec=ClerkAuthDetails)
    mock_auth.user_id = str(uuid.uuid4())
    
    # Mock DB session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Create template request body
    template_in = AdminTemplateCreate(
        key="test-intent-template",
        description="Test intent template",
        category=AdminTemplateCategory.INTENT_ROUTER,
        language="en",
        body="Test template body",
        scope=AdminTemplateScope.TENANT,
    )
    
    # Mock the created template from DB
    mock_db_template = MagicMock(spec=TemplateModel)
    mock_db_template.id = template_id
    mock_db_template.key = template_in.key
    mock_db_template.category = template_in.category
    mock_db_template.body = template_in.body
    mock_db_template.description = template_in.description
    
    # Setup mock return values
    mock_crud.create_with_tenant.return_value = mock_db_template
    mock_construct_response.return_value = AdminTemplate(
        id=template_id,
        tenant_id=uuid.UUID(tenant_id),
        key=template_in.key,
        description=template_in.description,
        category=template_in.category,
        language=template_in.language,
        body=template_in.body,
        scope=template_in.scope,
        version=1,
        created_at=None,
        updated_at=None,
        created_by=None,
        protected=False,
        actions={},
        parameters={},
        latest_version_details=None,
    )
    
    # Setup background tasks mock
    background_tasks = MagicMock(spec=BackgroundTasks)
    
    # Call the function
    await create_template(
        request=mock_request,
        template_in=template_in,
        background_tasks=background_tasks,
        db=mock_db,
        current_user=mock_auth,
    )
    
    # Verify the response is constructed correctly
    assert mock_construct_response.called
    assert mock_construct_response.call_args[0][0] == mock_db_template
    
    # Verify mock_crud.create_with_tenant was called with the correct arguments
    mock_crud.create_with_tenant.assert_called_once()
    
    # Verify vectorization was added as a background task for intent router templates
    background_tasks.add_task.assert_called_once()
    
    # Check that the VectorIndexer was initialized
    mock_vector_indexer.assert_called_once()
    
    # Check the background task arguments
    task_args = background_tasks.add_task.call_args[0]
    assert task_args[0] == mock_vector_indexer.return_value.upsert_template
    
    # Verify that the correct index name was used (based on tenant ID)
    index_name_arg_position = 7  # Position of index_name in kwargs
    assert f"intent_idx_{tenant_id}_user" in str(task_args[index_name_arg_position])


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.admin_templates.VectorIndexer")
@patch("src.coherence.api.v1.endpoints.admin_templates.crud_admin_template")
@patch("src.coherence.api.v1.endpoints.admin_templates._construct_admin_template_response")
async def test_update_template_with_vectorization(
    mock_construct_response, mock_crud, mock_vector_indexer
):
    """Test that updating an intent router template triggers vectorization"""
    # Setup test data
    tenant_id = str(uuid.uuid4())
    template_id = uuid.uuid4()
    
    # Mock request with tenant_id
    mock_request = MagicMock(spec=Request)
    mock_request.state.tenant_id = tenant_id
    mock_request.state.is_system_admin = False
    
    # Mock ClerkAuthDetails
    mock_auth = MagicMock(spec=ClerkAuthDetails)
    mock_auth.user_id = str(uuid.uuid4())
    
    # Mock DB session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Create template update data
    template_in = AdminTemplateUpdate(
        key="test-intent-template",
        description="Updated test intent template",
        body="Updated template body",
    )
    
    # Mock the updated template from DB
    mock_db_template = MagicMock(spec=TemplateModel)
    mock_db_template.id = template_id
    mock_db_template.key = template_in.key
    mock_db_template.category = AdminTemplateCategory.INTENT_ROUTER.value
    mock_db_template.body = template_in.body
    mock_db_template.description = template_in.description
    
    # Setup mock return values
    mock_crud.update_template.return_value = mock_db_template
    mock_construct_response.return_value = AdminTemplate(
        id=template_id,
        tenant_id=uuid.UUID(tenant_id),
        key=template_in.key,
        description=template_in.description,
        category=AdminTemplateCategory.INTENT_ROUTER,
        language="en",
        body=template_in.body,
        scope=AdminTemplateScope.TENANT,
        version=1,
        created_at=None,
        updated_at=None,
        created_by=None,
        protected=False,
        actions={},
        parameters={},
        latest_version_details=None,
    )
    
    # Setup background tasks mock
    background_tasks = MagicMock(spec=BackgroundTasks)
    
    # Call the function
    await update_template(
        request=mock_request,
        template_id=template_id,
        template_in=template_in,
        background_tasks=background_tasks,
        db=mock_db,
        current_user=mock_auth,
    )
    
    # Verify the response is constructed correctly
    assert mock_construct_response.called
    assert mock_construct_response.call_args[0][0] == mock_db_template
    
    # Verify mock_crud.update_template was called with the correct arguments
    mock_crud.update_template.assert_called_once()
    
    # Verify vectorization was added as a background task for intent router templates
    background_tasks.add_task.assert_called_once()
    
    # Check that the VectorIndexer was initialized
    mock_vector_indexer.assert_called_once()
    
    # Check the background task arguments
    task_args = background_tasks.add_task.call_args[0]
    assert task_args[0] == mock_vector_indexer.return_value.upsert_template
    
    # Verify that the correct index name was used (based on tenant ID)
    index_name_arg_position = 7  # Position of index_name in kwargs
    assert f"intent_idx_{tenant_id}_user" in str(task_args[index_name_arg_position])


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.admin_templates.VectorIndexer")
@patch("src.coherence.api.v1.endpoints.admin_templates.crud_admin_template")
@patch("src.coherence.api.v1.endpoints.admin_templates._construct_admin_template_response")
async def test_delete_template_with_vector_cleanup(
    mock_construct_response, mock_crud, mock_vector_indexer
):
    """Test that deleting an intent router template triggers vector cleanup"""
    # Setup test data
    tenant_id = str(uuid.uuid4())
    template_id = uuid.uuid4()
    
    # Mock request with tenant_id
    mock_request = MagicMock(spec=Request)
    mock_request.state.tenant_id = tenant_id
    mock_request.state.is_system_admin = False
    
    # Mock ClerkAuthDetails
    mock_auth = MagicMock(spec=ClerkAuthDetails)
    mock_auth.user_id = str(uuid.uuid4())
    
    # Mock DB session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Mock the template to be deleted
    mock_template = MagicMock(spec=TemplateModel)
    mock_template.id = template_id
    mock_template.key = "test-intent-template"
    mock_template.category = AdminTemplateCategory.INTENT_ROUTER.value
    mock_template.body = "Template body"
    mock_template.description = "Template description"
    mock_template.protected = False
    
    # Mock the deleted template result
    mock_deleted_template = MagicMock(spec=TemplateModel)
    mock_deleted_template.id = template_id
    
    # Setup mock return values
    mock_crud.get_by_id_and_tenant.return_value = mock_template
    mock_crud.remove_by_id_and_tenant.return_value = mock_deleted_template
    mock_construct_response.return_value = AdminTemplate(
        id=template_id,
        tenant_id=uuid.UUID(tenant_id),
        key="test-intent-template",
        description="Template description",
        category=AdminTemplateCategory.INTENT_ROUTER,
        language="en",
        body="Template body",
        scope=AdminTemplateScope.TENANT,
        version=1,
        created_at=None,
        updated_at=None,
        created_by=None,
        protected=False,
        actions={},
        parameters={},
        latest_version_details=None,
    )
    
    # Setup background tasks mock
    background_tasks = MagicMock(spec=BackgroundTasks)
    
    # Call the function
    await delete_template(
        request=mock_request,
        template_id=template_id,
        background_tasks=background_tasks,
        db=mock_db,
        current_user=mock_auth,
    )
    
    # Verify the response is constructed correctly
    assert mock_construct_response.called
    assert mock_construct_response.call_args[0][0] == mock_deleted_template
    
    # Verify mock_crud.get_by_id_and_tenant was called to check if template exists
    mock_crud.get_by_id_and_tenant.assert_called_once()
    
    # Verify mock_crud.remove_by_id_and_tenant was called to delete the template
    mock_crud.remove_by_id_and_tenant.assert_called_once()
    
    # Verify vector deletion was added as a background task for intent router templates
    background_tasks.add_task.assert_called_once()
    
    # Check that the VectorIndexer was initialized
    mock_vector_indexer.assert_called_once()
    
    # Check the background task arguments
    task_args = background_tasks.add_task.call_args[0]
    assert task_args[0] == mock_vector_indexer.return_value.delete_template
    
    # Verify that the correct template ID was passed
    assert str(template_id) in str(task_args[1:])
    
    # Verify that the correct index name was used (based on tenant ID)
    assert f"intent_idx_{tenant_id}_user" in str(task_args[1:])


@pytest.mark.asyncio
@patch("src.coherence.api.v1.endpoints.admin_templates.VectorIndexer")
@patch("src.coherence.api.v1.endpoints.admin_templates.crud_admin_template")
@patch("src.coherence.api.v1.endpoints.admin_templates._construct_admin_template_response")
async def test_create_template_no_vectorization_for_non_intent_templates(
    mock_construct_response, mock_crud, mock_vector_indexer
):
    """Test that creating a non-intent router template doesn't trigger vectorization"""
    # Setup test data
    tenant_id = str(uuid.uuid4())
    template_id = uuid.uuid4()
    
    # Mock request with tenant_id
    mock_request = MagicMock(spec=Request)
    mock_request.state.tenant_id = tenant_id
    
    # Mock ClerkAuthDetails
    mock_auth = MagicMock(spec=ClerkAuthDetails)
    mock_auth.user_id = str(uuid.uuid4())
    
    # Mock DB session
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Create template request body (non-INTENT_ROUTER category)
    template_in = AdminTemplateCreate(
        key="test-response-template",
        description="Test response template",
        category=AdminTemplateCategory.RESPONSE_GEN,  # Not an intent router
        language="en",
        body="Test template body",
        scope=AdminTemplateScope.TENANT,
    )
    
    # Mock the created template from DB
    mock_db_template = MagicMock(spec=TemplateModel)
    mock_db_template.id = template_id
    mock_db_template.key = template_in.key
    mock_db_template.category = template_in.category
    mock_db_template.body = template_in.body
    mock_db_template.description = template_in.description
    
    # Setup mock return values
    mock_crud.create_with_tenant.return_value = mock_db_template
    mock_construct_response.return_value = AdminTemplate(
        id=template_id,
        tenant_id=uuid.UUID(tenant_id),
        key=template_in.key,
        description=template_in.description,
        category=template_in.category,
        language=template_in.language,
        body=template_in.body,
        scope=template_in.scope,
        version=1,
        created_at=None,
        updated_at=None,
        created_by=None,
        protected=False,
        actions={},
        parameters={},
        latest_version_details=None,
    )
    
    # Setup background tasks mock
    background_tasks = MagicMock(spec=BackgroundTasks)
    
    # Call the function
    await create_template(
        request=mock_request,
        template_in=template_in,
        background_tasks=background_tasks,
        db=mock_db,
        current_user=mock_auth,
    )
    
    # Verify the response is constructed correctly
    assert mock_construct_response.called
    assert mock_construct_response.call_args[0][0] == mock_db_template
    
    # Verify mock_crud.create_with_tenant was called
    mock_crud.create_with_tenant.assert_called_once()
    
    # Verify vectorization was NOT added as a background task for non-intent templates
    background_tasks.add_task.assert_not_called()
    
    # Check that the VectorIndexer was NOT initialized
    mock_vector_indexer.assert_not_called()