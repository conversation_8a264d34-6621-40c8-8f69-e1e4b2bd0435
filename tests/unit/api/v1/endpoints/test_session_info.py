from unittest.mock import Magic<PERSON>ock

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
from uuid_extensions import uuid7  # For generating UUIDs if needed for tenant IDs

from src.coherence.api.v1.dependencies.auth import (
    ClerkAuthDetails,
    get_clerk_auth_details,
)
from src.coherence.api.v1.endpoints.session_info import router as session_info_router
from src.coherence.db.session import get_db
from src.coherence.models.tenant import Tenant
from src.coherence.services.permission_service import (
    CoherencePermission,
    PermissionService,
)

# Create a FastAPI app instance for testing
app = FastAPI()
app.include_router(session_info_router)

@pytest.fixture
def client() -> TestClient:
    return TestClient(app)

@pytest.fixture
def mock_db_session():
    db = MagicMock()
    db.query = MagicMock()
    return db

@pytest.fixture
def mock_permission_service():
    service = MagicMock(spec=PermissionService)
    service.get_coherence_permissions = MagicMock()
    return service

# Override dependencies for testing
def override_get_db():
    # This will be replaced by the mock_db_session fixture in tests
    # Needs to be a generator or async generator if the original is
    # For now, assume it's a simple function for this mock structure
    pass

def override_get_clerk_auth_details():
    # This will be replaced by a function returning specific ClerkAuthDetails in tests
    pass

def override_permission_service():
    # This will be replaced by the mock_permission_service fixture in tests
    pass

app.dependency_overrides[get_db] = override_get_db
app.dependency_overrides[get_clerk_auth_details] = override_get_clerk_auth_details
app.dependency_overrides[PermissionService] = override_permission_service


def test_get_session_info_org_admin(client: TestClient, mock_db_session, mock_permission_service):
    tenant_id = uuid7()
    mock_tenant = Tenant(id=tenant_id, name="Test Tenant Inc.", clerk_organization_id="org_clerk_123")

    def mock_get_clerk_details():
        return ClerkAuthDetails(
            user_id="user_clerk_admin",
            org_id="org_clerk_123",
            org_role="admin",
            org_slug="test-tenant-inc",
            is_system_admin=False
        )
    app.dependency_overrides[get_clerk_auth_details] = mock_get_clerk_details
    app.dependency_overrides[get_db] = lambda: mock_db_session
    app.dependency_overrides[PermissionService] = lambda: mock_permission_service

    mock_db_session.query(Tenant).filter().first.return_value = mock_tenant
    expected_permissions = {CoherencePermission.WORKFLOW_CREATE.value, CoherencePermission.TENANT_VIEW_OWN_DASHBOARD.value}
    mock_permission_service.get_coherence_permissions.return_value = expected_permissions

    response = client.get("/auth/session-info", headers={"Authorization": "Bearer valid_token"})

    assert response.status_code == 200
    data = response.json()
    assert data["clerk_user_id"] == "user_clerk_admin"
    assert data["tenant_id"] == str(tenant_id)
    assert data["tenant_name"] == "Test Tenant Inc."
    assert data["is_system_admin"] is False
    assert set(data["permissions"]) == expected_permissions
    mock_permission_service.get_coherence_permissions.assert_called_once_with(
        clerk_org_role="admin", is_system_admin=False
    )

def test_get_session_info_system_admin_no_org(client: TestClient, mock_db_session, mock_permission_service):
    def mock_get_clerk_details_sys_admin():
        return ClerkAuthDetails(
            user_id="user_clerk_sysadmin",
            org_id=None, # System admin might not be in an org context
            org_role=None,
            org_slug=None,
            is_system_admin=True
        )
    app.dependency_overrides[get_clerk_auth_details] = mock_get_clerk_details_sys_admin
    app.dependency_overrides[get_db] = lambda: mock_db_session
    app.dependency_overrides[PermissionService] = lambda: mock_permission_service

    # System admin, so no specific tenant lookup based on org_id
    mock_db_session.query(Tenant).filter().first.return_value = None
    
    all_permissions = CoherencePermission.all_permissions() # System admin gets all
    mock_permission_service.get_coherence_permissions.return_value = all_permissions

    response = client.get("/auth/session-info", headers={"Authorization": "Bearer valid_sysadmin_token"})

    assert response.status_code == 200
    data = response.json()
    assert data["clerk_user_id"] == "user_clerk_sysadmin"
    assert data["tenant_id"] is None
    assert data["tenant_name"] is None
    assert data["is_system_admin"] is True
    assert set(data["permissions"]) == all_permissions
    mock_permission_service.get_coherence_permissions.assert_called_once_with(
        clerk_org_role=None, is_system_admin=True
    )

def test_get_session_info_org_member_tenant_not_found(client: TestClient, mock_db_session, mock_permission_service):
    """ Test case where clerk_org_id is present, but no matching Coherence tenant exists. """
    def mock_get_clerk_details_member_no_tenant():
        return ClerkAuthDetails(
            user_id="user_clerk_member_orphan_org",
            org_id="org_clerk_orphan",
            org_role="member",
            org_slug="orphan-org",
            is_system_admin=False
        )
    app.dependency_overrides[get_clerk_auth_details] = mock_get_clerk_details_member_no_tenant
    app.dependency_overrides[get_db] = lambda: mock_db_session
    app.dependency_overrides[PermissionService] = lambda: mock_permission_service

    mock_db_session.query(Tenant).filter().first.return_value = None # Tenant not found
    member_permissions = {CoherencePermission.TENANT_VIEW_OWN_DASHBOARD.value}
    mock_permission_service.get_coherence_permissions.return_value = member_permissions

    response = client.get("/auth/session-info", headers={"Authorization": "Bearer valid_token_orphan"})

    assert response.status_code == 200 # Endpoint should still succeed, but tenant info will be None
    data = response.json()
    assert data["clerk_user_id"] == "user_clerk_member_orphan_org"
    assert data["tenant_id"] is None
    assert data["tenant_name"] is None
    assert data["is_system_admin"] is False
    assert set(data["permissions"]) == member_permissions
    mock_permission_service.get_coherence_permissions.assert_called_once_with(
        clerk_org_role="member", is_system_admin=False
    )

def test_get_session_info_unauthenticated(client: TestClient):
    # Simulate get_clerk_auth_details raising an HTTPException for unauthenticated user
    def mock_get_clerk_details_unauth():
        # In a real scenario, get_clerk_auth_details would raise this itself
        # based on missing/invalid header. Here we simulate its effect.
        from fastapi import HTTPException
        raise HTTPException(status_code=401, detail="Not authenticated")

    app.dependency_overrides[get_clerk_auth_details] = mock_get_clerk_details_unauth
    
    response = client.get("/auth/session-info") # No Auth header
    
    assert response.status_code == 401
    assert response.json()["detail"] == "Not authenticated"

    # Reset dependency for other tests
    app.dependency_overrides[get_clerk_auth_details] = override_get_clerk_auth_details


# Clean up overrides after tests if necessary, though for this structure it might not be
# if each test explicitly sets its overrides.
@pytest.fixture(autouse=True)
def cleanup_overrides():
    yield
    app.dependency_overrides = {} # Clear all overrides
    # Restore original dependencies if they were stored, or re-apply general mocks
    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_clerk_auth_details] = override_get_clerk_auth_details
    app.dependency_overrides[PermissionService] = override_permission_service