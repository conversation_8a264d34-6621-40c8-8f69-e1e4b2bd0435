import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import HTT<PERSON>Ex<PERSON>, status

from src.coherence.api.v1.dependencies.auth import ClerkAuthDetails
from src.coherence.api.v1.endpoints.tenants import get_tenant_dashboard_by_id
from src.coherence.models.tenant import Tenant
from src.coherence.schemas.tenant import TenantRead


@pytest.fixture
def mock_db_session():
    return AsyncMock()

@pytest.fixture
def mock_auth_details_valid_org_admin():
    return ClerkAuthDetails(
        user_id="user_test_123",
        org_id="org_clerk_12345",
        org_role="admin",
        is_system_admin=False
    )

@pytest.fixture
def mock_auth_details_valid_org_member():
    return ClerkAuthDetails(
        user_id="user_test_member_456",
        org_id="org_clerk_12345", # Same org
        org_role="member",
        is_system_admin=False
    )

@pytest.fixture
def mock_auth_details_different_org():
    return ClerkAuthDetails(
        user_id="user_test_789",
        org_id="org_clerk_DIFFERENT",
        org_role="admin",
        is_system_admin=False
    )

@pytest.fixture
def mock_auth_details_system_admin():
    return ClerkAuthDetails(
        user_id="user_sys_admin_000",
        org_id=None, # System admin might not be in an org context
        org_role="system_admin", # Or some other special role
        is_system_admin=True
    )


@pytest.fixture
def sample_tenant_model():
    return Tenant(
        id=uuid.uuid4(),
        name="Test Tenant",
        clerk_org_id="org_clerk_12345",
        industry_pack="SaaS",
        compliance_tier="SOC2",
        created_at="2023-01-01T00:00:00Z",
        updated_at="2023-01-01T00:00:00Z",
    )

@pytest.fixture
def sample_tenant_read_schema(sample_tenant_model: Tenant):
    return TenantRead.model_validate(sample_tenant_model) # Use model_validate for Pydantic v2

# Patch the RequirePermission dependency for all tests in this module
@pytest.fixture(autouse=True)
def mock_require_permission():
    with patch("src.coherence.api.v1.dependencies.auth.RequirePermission.__call__", return_value=None) as mock:
        # This mock will allow the permission check to pass by default.
        # Tests that need to check permission failure will need to adjust this mock's behavior.
        yield mock


@pytest.mark.asyncio
async def test_get_tenant_dashboard_by_id_success(
    mock_db_session: AsyncMock,
    mock_auth_details_valid_org_admin: ClerkAuthDetails,
    sample_tenant_model: Tenant,
    sample_tenant_read_schema: TenantRead,
    mock_require_permission: MagicMock # Ensure it's passed to control its behavior if needed
):
    target_clerk_org_id = "org_clerk_12345"
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = sample_tenant_model

    # Ensure the permission check passes for this test
    mock_require_permission.return_value = None 

    result = await get_tenant_dashboard_by_id(
        tenant_id=target_clerk_org_id,
        db=mock_db_session,
        auth_details=mock_auth_details_valid_org_admin,
        _perm_check=None # Dependency is mocked by autouse fixture
    )

    mock_db_session.execute.assert_called_once()
    # Basic check on the query structure (can be more specific if needed)
    # query_arg = mock_db_session.execute.call_args[0][0]
    # assert str(query_arg).startswith("SELECT tenants.id")
    # assert f"tenants.clerk_org_id = '{target_clerk_org_id}'" in str(query_arg)
    
    assert result == sample_tenant_read_schema
    assert result.clerk_org_id == target_clerk_org_id
    assert result.name == sample_tenant_model.name


@pytest.mark.asyncio
async def test_get_tenant_dashboard_by_id_not_found(
    mock_db_session: AsyncMock,
    mock_auth_details_valid_org_admin: ClerkAuthDetails,
    mock_require_permission: MagicMock
):
    target_clerk_org_id = "org_clerk_NONEXISTENT"
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = None
    mock_require_permission.return_value = None

    with pytest.raises(HTTPException) as exc_info:
        await get_tenant_dashboard_by_id(
            tenant_id=target_clerk_org_id,
            db=mock_db_session,
            auth_details=mock_auth_details_valid_org_admin,
            _perm_check=None
        )
    assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_get_tenant_dashboard_by_id_permission_denied(
    mock_db_session: AsyncMock,
    mock_auth_details_valid_org_member: ClerkAuthDetails, # A member who might not have the perm
    sample_tenant_model: Tenant,
    mock_require_permission: MagicMock
):
    target_clerk_org_id = "org_clerk_12345"
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = sample_tenant_model

    # Simulate permission check failure
    mock_require_permission.side_effect = HTTPException(
        status_code=status.HTTP_403_FORBIDDEN, detail="Missing required permission"
    )

    with pytest.raises(HTTPException) as exc_info:
        await get_tenant_dashboard_by_id(
            tenant_id=target_clerk_org_id,
            db=mock_db_session,
            auth_details=mock_auth_details_valid_org_member,
            _perm_check=None # Dependency will raise due to side_effect
        )
    assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
    assert "Missing required permission" in exc_info.value.detail

@pytest.mark.asyncio
async def test_get_tenant_dashboard_by_id_system_admin_can_access(
    mock_db_session: AsyncMock,
    mock_auth_details_system_admin: ClerkAuthDetails,
    sample_tenant_model: Tenant, # System admin accessing a specific tenant
    sample_tenant_read_schema: TenantRead,
    mock_require_permission: MagicMock
):
    target_clerk_org_id = "org_clerk_12345" # The tenant being accessed
    sample_tenant_model.clerk_org_id = target_clerk_org_id # Ensure fixture matches
    
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = sample_tenant_model
    mock_require_permission.return_value = None # System admin should have the permission

    result = await get_tenant_dashboard_by_id(
        tenant_id=target_clerk_org_id,
        db=mock_db_session,
        auth_details=mock_auth_details_system_admin,
        _perm_check=None
    )
    assert result == sample_tenant_read_schema
    assert result.clerk_org_id == target_clerk_org_id

# Note on RLS:
# Testing RLS itself is complex in unit tests as it's a database-level feature.
# The Python code relies on RLS to enforce that a non-system admin can only see their own org's data
# if the `tenant_id` in the path matches their `auth_details.org_id`.
# The `RequirePermission("organization:view_own_dashboard")` check ensures they have the *general*
# permission to view *a* dashboard. The RLS (and potentially logic within the endpoint if it compared
# `tenant_id` path param with `auth_details.org_id` for non-sysadmins) would handle the "own" part.
# The current endpoint implementation primarily relies on RLS for the "own" aspect after the permission check.
# If the `tenant_id` in the path does not match `auth_details.org_id` for a non-system admin,
# the `select(Tenant).where(Tenant.clerk_org_id == tenant_id)` query, under RLS, should return None
# (or raise an error depending on RLS policy), leading to a 404, effectively blocking access.

@pytest.mark.asyncio
async def test_get_tenant_dashboard_by_id_rls_effect_non_matching_org_leads_to_404(
    mock_db_session: AsyncMock,
    mock_auth_details_different_org: ClerkAuthDetails, # User from a different org
    sample_tenant_model: Tenant, # Data for org_clerk_12345
    mock_require_permission: MagicMock
):
    # User from 'org_clerk_DIFFERENT' tries to access 'org_clerk_12345'
    target_clerk_org_id_of_resource = "org_clerk_12345"
    
    # Simulate RLS: if the DB query for target_clerk_org_id_of_resource returns None
    # because the current user (mock_auth_details_different_org) doesn't have access.
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = None
    mock_require_permission.return_value = None # Assume they have the general permission

    with pytest.raises(HTTPException) as exc_info:
        await get_tenant_dashboard_by_id(
            tenant_id=target_clerk_org_id_of_resource, # Path param is the resource they want
            db=mock_db_session,
            auth_details=mock_auth_details_different_org, # JWT org is different
            _perm_check=None
        )
    # This will result in a 404 because the query (scoped by RLS) found nothing.
    assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
    assert f"Tenant with ID '{target_clerk_org_id_of_resource}' not found or access denied." in exc_info.value.detail