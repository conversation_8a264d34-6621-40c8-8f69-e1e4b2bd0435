"""
Unit tests for template testing endpoint.
"""

import pytest
from unittest.mock import MagicMock, AsyncMock
from uuid import UUI<PERSON>, uuid4
from fastapi import HTT<PERSON>Ex<PERSON>, status

from src.coherence.api.v1.endpoints.template_test import (
    test_template,
    get_test_scenarios,
)
from src.coherence.models.template import Template, TemplateCategory
from src.coherence.schemas.admin_template import (
    TemplateTestRequest,
    TemplateTestResponse,
)


class TestTemplateTestEndpoint:
    """Test template testing endpoint functionality."""

    @pytest.fixture
    def mock_db(self):
        """Create mock database session."""
        db = AsyncMock()
        return db

    @pytest.fixture
    def mock_request(self):
        """Create mock request object."""
        request = MagicMock()
        request.state.tenant_id = str(uuid4())
        request.state.clerk_org_id = "org_123"
        return request

    @pytest.fixture
    def mock_user(self):
        """Create mock user."""
        user = MagicMock()
        user.user_id = str(uuid4())
        user.email = "<EMAIL>"
        return user

    @pytest.fixture
    def sample_template(self):
        """Create sample unified template with test data."""
        template = Template(
            id=uuid4(),
            key="test_template",
            category=TemplateCategory.UNIFIED,
            body="Test template body",
            test_data={
                "mock_responses": {
                    "success": {"status": 200, "data": {"result": "Success"}},
                    "error": {"status": 400, "error": "Bad Request"},
                },
                "sample_parameters": {
                    "param1": "value1",
                    "param2": 123,
                },
            },
            action_config={
                "validation_rules": {
                    "param1": {"required": True, "type": "string"},
                    "param2": {"required": False, "type": "integer"},
                },
                "transformations": {
                    "param1": ["trim", "lowercase"],
                },
            },
            response_format={
                "crfs": {
                    "type": "structured",
                    "auto_select": True,
                    "default_format": "structured",
                },
            },
        )
        return template

    @pytest.mark.asyncio
    async def test_test_template_success(
        self, mock_db, mock_request, mock_user, sample_template
    ):
        """Test successful template test execution."""
        template_id = sample_template.id
        test_request = TemplateTestRequest(scenario="success")

        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_template
        mock_db.execute.return_value = mock_result

        # Execute test
        response = await test_template(
            template_id=template_id,
            test_request=test_request,
            request=mock_request,
            db=mock_db,
            current_user=mock_user,
        )

        # Verify response
        assert isinstance(response, TemplateTestResponse)
        assert response.template_id == template_id
        assert response.template_key == "test_template"
        assert response.scenario == "success"
        assert response.mock_response == {"status": 200, "data": {"result": "Success"}}
        assert response.test_data_available is True
        assert "success" in response.scenarios_available
        assert "error" in response.scenarios_available

    @pytest.mark.asyncio
    async def test_test_template_with_validation_errors(
        self, mock_db, mock_request, mock_user, sample_template
    ):
        """Test template test with validation errors."""
        template_id = sample_template.id
        test_request = TemplateTestRequest(
            parameters={"param2": 123},  # Missing required param1
            scenario="success",
        )

        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_template
        mock_db.execute.return_value = mock_result

        # Execute test
        response = await test_template(
            template_id=template_id,
            test_request=test_request,
            request=mock_request,
            db=mock_db,
            current_user=mock_user,
        )

        # Verify validation errors
        assert len(response.validation_errors) > 0
        assert "Missing required parameter: param1" in response.validation_errors

    @pytest.mark.asyncio
    async def test_test_template_with_transformations(
        self, mock_db, mock_request, mock_user, sample_template
    ):
        """Test template test with parameter transformations."""
        template_id = sample_template.id
        test_request = TemplateTestRequest(
            parameters={"param1": "  VALUE1  ", "param2": 123},
            scenario="success",
        )

        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_template
        mock_db.execute.return_value = mock_result

        # Execute test
        response = await test_template(
            template_id=template_id,
            test_request=test_request,
            request=mock_request,
            db=mock_db,
            current_user=mock_user,
        )

        # Verify transformations were applied
        assert response.parameters_transformed["param1"] == "value1"
        assert response.parameters_transformed["param2"] == 123

    @pytest.mark.asyncio
    async def test_test_template_not_found(
        self, mock_db, mock_request, mock_user
    ):
        """Test template test when template not found."""
        template_id = uuid4()
        test_request = TemplateTestRequest()

        # Mock database query returning None
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result

        # Execute test and expect exception
        with pytest.raises(HTTPException) as exc_info:
            await test_template(
                template_id=template_id,
                test_request=test_request,
                request=mock_request,
                db=mock_db,
                current_user=mock_user,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert f"Template {template_id} not found" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_test_template_non_unified(
        self, mock_db, mock_request, mock_user
    ):
        """Test template test with non-unified template."""
        template = Template(
            id=uuid4(),
            key="action_template",
            category=TemplateCategory.ACTION,  # Not unified
            body="Action template",
        )
        test_request = TemplateTestRequest()

        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = template
        mock_db.execute.return_value = mock_result

        # Execute test and expect exception
        with pytest.raises(HTTPException) as exc_info:
            await test_template(
                template_id=template.id,
                test_request=test_request,
                request=mock_request,
                db=mock_db,
                current_user=mock_user,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Only unified templates can be tested" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_get_test_scenarios_success(
        self, mock_db, mock_request, mock_user, sample_template
    ):
        """Test getting test scenarios for a template."""
        template_id = sample_template.id

        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_template
        mock_db.execute.return_value = mock_result

        # Get scenarios
        response = await get_test_scenarios(
            template_id=template_id,
            request=mock_request,
            db=mock_db,
            current_user=mock_user,
        )

        # Verify response
        assert response["template_id"] == str(template_id)
        assert response["template_key"] == "test_template"
        assert response["test_data_available"] is True
        assert "success" in response["scenarios"]
        assert "error" in response["scenarios"]
        assert response["sample_parameters"]["param1"] == "value1"
        assert response["sample_parameters"]["param2"] == 123

    @pytest.mark.asyncio
    async def test_test_template_no_test_data(
        self, mock_db, mock_request, mock_user
    ):
        """Test template test when template has no test data."""
        template = Template(
            id=uuid4(),
            key="template_no_test",
            category=TemplateCategory.UNIFIED,
            body="Template without test data",
            test_data=None,  # No test data
        )
        test_request = TemplateTestRequest(
            parameters={"param1": "value1"}
        )

        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = template
        mock_db.execute.return_value = mock_result

        # Execute test
        response = await test_template(
            template_id=template.id,
            test_request=test_request,
            request=mock_request,
            db=mock_db,
            current_user=mock_user,
        )

        # Verify default mock response is created
        assert response.mock_response["status"] == "success"
        assert response.mock_response["data"]["param1"] == "value1"
        assert response.test_data_available is False
        assert len(response.scenarios_available) == 0