"""
Tests for auth_utils.py
"""

import unittest
from unittest.mock import MagicMock

from fastapi import Request

from src.coherence.api.v1.utils.auth_utils import is_system_admin


class TestAuthUtils(unittest.TestCase):
    def test_is_system_admin_with_flag(self):
        """Test is_system_admin returns True when is_system_admin flag is set"""
        # Create a mock request with is_system_admin = True
        mock_request = MagicMock(spec=Request)
        mock_request.state.is_system_admin = True
        
        # Test the function
        result = is_system_admin(mock_request)
        
        # Assert result is True
        self.assertTrue(result)
        
    def test_is_system_admin_with_wildcard_permission(self):
        """Test is_system_admin returns True when system:* permission is present"""
        # Create a mock request with system:* in permissions
        mock_request = MagicMock(spec=Request)
        mock_request.state.is_system_admin = False
        mock_request.state.permissions = {"system:*", "other:permission"}
        
        # Test the function
        result = is_system_admin(mock_request)
        
        # Assert result is True
        self.assertTrue(result)
        
    def test_is_system_admin_returns_false_for_regular_user(self):
        """Test is_system_admin returns False for regular users"""
        # Create a mock request for a regular user
        mock_request = MagicMock(spec=Request)
        mock_request.state.is_system_admin = False
        mock_request.state.permissions = {"regular:permission"}
        
        # Test the function
        result = is_system_admin(mock_request)
        
        # Assert result is False
        self.assertFalse(result)
        
    def test_is_system_admin_handles_missing_attributes(self):
        """Test is_system_admin handles missing attributes gracefully"""
        # Create a mock request with no relevant attributes
        mock_request = MagicMock(spec=Request)
        mock_request.state = MagicMock()
        delattr(mock_request.state, "is_system_admin")
        delattr(mock_request.state, "permissions")
        
        # Test the function
        result = is_system_admin(mock_request)
        
        # Assert result is False
        self.assertFalse(result)