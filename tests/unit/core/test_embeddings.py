"""
Tests for embedding dimension standardization.

This module contains tests to verify that the embedding dimensions
are correctly enforced when generating embeddings.
"""

from unittest.mock import AsyncMock, MagicMock

import pytest

from src.coherence.core.config import settings
from src.coherence.core.errors.base import EmbeddingDimensionMismatchError
from src.coherence.core.llm.providers.openai_provider import OpenAIProvider
from src.coherence.services.vector_indexer import VectorIndexer


@pytest.fixture
def mock_openai_response():
    """Create a mock OpenAI API response for embeddings."""
    # Create a response object with the structure expected from OpenAI
    mock_response = MagicMock()
    mock_response.data = [MagicMock()]
    mock_response.usage = MagicMock()
    mock_response.usage.total_tokens = 10
    return mock_response


@pytest.mark.asyncio
async def test_embedding_dimensions_match():
    """Test that the embedding dimensions match the expected value."""
    # Arrange
    expected_dimensions = settings.EMBEDDING_DIMENSION
    openai_provider = OpenAIProvider(api_key="fake_key")
    
    # Mock the OpenAI client
    mock_response = MagicMock()
    mock_response.data = [MagicMock()]
    mock_response.data[0].embedding = [0.0] * expected_dimensions  # Create vector with correct dimensions
    mock_response.usage = MagicMock()
    mock_response.usage.total_tokens = 10
    
    openai_provider.client = MagicMock()
    openai_provider.client.embeddings = MagicMock()
    openai_provider.client.embeddings.create = AsyncMock(return_value=mock_response)
    
    # Act
    result = await openai_provider.generate_embedding("test text")
    
    # Assert
    assert len(result) == expected_dimensions
    openai_provider.client.embeddings.create.assert_called_once()
    # Verify dimensions parameter was passed to the API call
    assert openai_provider.client.embeddings.create.call_args[1]["dimensions"] == expected_dimensions


@pytest.mark.asyncio
async def test_embedding_dimensions_mismatch():
    """Test that an error is raised when the embedding dimensions don't match."""
    # Arrange
    expected_dimensions = settings.EMBEDDING_DIMENSION
    wrong_dimensions = 1024  # Different from the expected dimensions
    openai_provider = OpenAIProvider(api_key="fake_key")
    
    # Mock the OpenAI client
    mock_response = MagicMock()
    mock_response.data = [MagicMock()]
    mock_response.data[0].embedding = [0.0] * wrong_dimensions  # Create vector with wrong dimensions
    mock_response.usage = MagicMock()
    mock_response.usage.total_tokens = 10
    
    openai_provider.client = MagicMock()
    openai_provider.client.embeddings = MagicMock()
    openai_provider.client.embeddings.create = AsyncMock(return_value=mock_response)
    
    # Act & Assert
    with pytest.raises(EmbeddingDimensionMismatchError) as exc_info:
        await openai_provider.generate_embedding("test text")
    
    # Verify the error contains expected details
    assert exc_info.value.details["expected_dimension"] == expected_dimensions
    assert exc_info.value.details["actual_dimension"] == wrong_dimensions
    assert settings.EMBEDDING_MODEL in str(exc_info.value)


@pytest.mark.asyncio
async def test_vector_indexer_generate_embedding():
    """Test that the VectorIndexer correctly passes dimensions to the provider."""
    # Arrange
    expected_dimensions = settings.EMBEDDING_DIMENSION
    
    # Create a mock LLM provider
    mock_provider = MagicMock()
    mock_provider.generate_embedding = AsyncMock(return_value=[0.0] * expected_dimensions)
    
    # Create a mock LLM factory that returns our mock provider
    mock_factory = MagicMock()
    mock_factory.get_default_provider = MagicMock(return_value=mock_provider)
    
    # Create a VectorIndexer with our mock factory
    vector_indexer = VectorIndexer(llm_factory=mock_factory)
    
    # Act
    result = await vector_indexer._generate_embedding("test text")
    
    # Assert
    assert len(result) == expected_dimensions
    # Verify dimensions parameter was passed to the provider
    mock_provider.generate_embedding.assert_called_once()
    assert mock_provider.generate_embedding.call_args[1]["dimensions"] == expected_dimensions