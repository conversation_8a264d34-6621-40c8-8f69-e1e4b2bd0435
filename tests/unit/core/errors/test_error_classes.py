"""
Tests for the Coherence error classes.

This module contains unit tests for the error classes defined in
src.coherence.core.errors.base.
"""


from src.coherence.core.errors.base import (
    AuthenticationError,
    AuthorizationError,
    CoherenceError,
    ConfigurationError,
    DatabaseError,
    ExternalServiceError,
    InternalError,
    LLMServiceError,
    RateLimitError,
    ResourceConflictError,
    ResourceNotFoundError,
    ValidationError,
)


class TestCoherenceError:
    """Tests for the base CoherenceError class."""

    def test_init_with_defaults(self):
        """Test initialization with default values."""
        error = CoherenceError()
        assert error.message == "An unexpected error occurred"
        assert error.error_code == "coherence.error"
        assert error.status_code == 500
        assert error.details == {}
        assert error.original_exception is None

    def test_init_with_custom_values(self):
        """Test initialization with custom values."""
        message = "Test error message"
        details = {"test": "value"}
        status_code = 400
        error_code = "test.error"
        original_exception = ValueError("Original error")

        error = CoherenceError(
            message=message,
            details=details,
            status_code=status_code,
            error_code=error_code,
            original_exception=original_exception,
        )

        assert error.message == message
        assert error.details == details
        assert error.status_code == status_code
        assert error.error_code == error_code
        assert error.original_exception == original_exception

    def test_to_dict(self):
        """Test the to_dict method."""
        error = CoherenceError(
            message="Test error message",
            details={"test": "value"},
            original_exception=ValueError("Original error"),
        )

        error_dict = error.to_dict()
        assert error_dict["error_code"] == "coherence.error"
        assert error_dict["message"] == "Test error message"
        assert error_dict["details"] == {"test": "value"}
        assert error_dict["type"] == "ValueError"


class TestValidationError:
    """Tests for the ValidationError class."""

    def test_init_with_defaults(self):
        """Test initialization with default values."""
        error = ValidationError()
        assert error.message == "Invalid input data"
        assert error.error_code == "coherence.validation_error"
        assert error.status_code == 400
        assert error.details == {}
        assert error.original_exception is None

    def test_init_with_field_errors(self):
        """Test initialization with field errors."""
        field_errors = {"name": "Field cannot be empty"}
        error = ValidationError(field_errors=field_errors)
        assert error.message == "Invalid input data"
        assert error.error_code == "coherence.validation_error"
        assert error.status_code == 400
        assert error.details == {"field_errors": field_errors}
        assert error.original_exception is None

    def test_init_with_details_and_field_errors(self):
        """Test initialization with both details and field errors."""
        field_errors = {"name": "Field cannot be empty"}
        details = {"other": "value"}
        error = ValidationError(field_errors=field_errors, details=details)
        assert error.details == {"field_errors": field_errors, "other": "value"}


class TestResourceNotFoundError:
    """Tests for the ResourceNotFoundError class."""

    def test_init_with_defaults(self):
        """Test initialization with default values."""
        error = ResourceNotFoundError()
        assert error.message == "Resource not found"
        assert error.error_code == "coherence.resource_not_found"
        assert error.status_code == 404
        assert error.details == {}
        assert error.original_exception is None

    def test_init_with_resource_type_and_id(self):
        """Test initialization with resource type and ID."""
        error = ResourceNotFoundError(resource_type="User", resource_id="123")
        assert error.message == "User with ID '123' not found"
        assert error.error_code == "coherence.resource_not_found"
        assert error.status_code == 404
        assert error.details == {"resource_type": "User", "resource_id": "123"}
        assert error.original_exception is None

    def test_init_with_resource_type_only(self):
        """Test initialization with resource type only."""
        error = ResourceNotFoundError(resource_type="User")
        assert error.message == "User not found"
        assert error.details == {"resource_type": "User"}


class TestExternalServiceError:
    """Tests for the ExternalServiceError class."""

    def test_init_with_defaults(self):
        """Test initialization with default values."""
        error = ExternalServiceError()
        assert error.message == "External service error"
        assert error.error_code == "coherence.external_service_error"
        assert error.status_code == 502
        assert error.details == {}
        assert error.original_exception is None

    def test_init_with_service_name(self):
        """Test initialization with service name."""
        error = ExternalServiceError(service_name="OpenAI")
        assert error.message == "Error calling external service: OpenAI"
        assert error.error_code == "coherence.external_service_error"
        assert error.status_code == 502
        assert error.details == {"service": "OpenAI"}
        assert error.original_exception is None

    def test_init_with_message_override(self):
        """Test initialization with message override."""
        error = ExternalServiceError(
            service_name="OpenAI",
            message="Custom error message",
        )
        assert error.message == "Custom error message"
        assert error.details == {"service": "OpenAI"}


class TestLLMServiceError:
    """Tests for the LLMServiceError class."""

    def test_init_with_defaults(self):
        """Test initialization with default values."""
        error = LLMServiceError()
        assert error.message == "LLM service error"
        assert error.error_code == "coherence.llm_service_error"
        assert error.status_code == 502
        assert error.details == {"service": "LLM Service"}
        assert error.original_exception is None

    def test_init_with_model(self):
        """Test initialization with model."""
        error = LLMServiceError(model="gpt-4o")
        assert error.message == "LLM service error"
        assert error.error_code == "coherence.llm_service_error"
        assert error.status_code == 502
        assert error.details == {"service": "LLM Service", "model": "gpt-4o"}
        assert error.original_exception is None

    def test_init_with_service_name_override(self):
        """Test initialization with service name override."""
        error = LLMServiceError(
            model="gpt-4o",
            service_name="OpenAI",
        )
        assert error.message == "LLM service error"
        assert error.details == {"service": "OpenAI", "model": "gpt-4o"}


class TestRateLimitError:
    """Tests for the RateLimitError class."""

    def test_init_with_defaults(self):
        """Test initialization with default values."""
        error = RateLimitError()
        assert error.message == "Rate limit exceeded"
        assert error.error_code == "coherence.rate_limit_error"
        assert error.status_code == 429
        assert error.details == {}
        assert error.original_exception is None

    def test_init_with_limit_and_reset(self):
        """Test initialization with limit and reset_after."""
        error = RateLimitError(limit=100, reset_after=60)
        assert error.message == "Rate limit exceeded"
        assert error.error_code == "coherence.rate_limit_error"
        assert error.status_code == 429
        assert error.details == {"limit": 100, "reset_after": 60}
        assert error.original_exception is None


class TestOtherErrorClasses:
    """Tests for other error classes."""

    def test_authentication_error(self):
        """Test AuthenticationError."""
        error = AuthenticationError(message="Invalid credentials")
        assert error.message == "Invalid credentials"
        assert error.error_code == "coherence.authentication_error"
        assert error.status_code == 401

    def test_authorization_error(self):
        """Test AuthorizationError."""
        error = AuthorizationError(message="Insufficient permissions")
        assert error.message == "Insufficient permissions"
        assert error.error_code == "coherence.authorization_error"
        assert error.status_code == 403

    def test_resource_conflict_error(self):
        """Test ResourceConflictError."""
        error = ResourceConflictError(message="Resource already exists")
        assert error.message == "Resource already exists"
        assert error.error_code == "coherence.resource_conflict"
        assert error.status_code == 409

    def test_database_error(self):
        """Test DatabaseError."""
        error = DatabaseError(message="Database connection failed")
        assert error.message == "Database connection failed"
        assert error.error_code == "coherence.database_error"
        assert error.status_code == 500

    def test_configuration_error(self):
        """Test ConfigurationError."""
        error = ConfigurationError(message="Missing configuration")
        assert error.message == "Missing configuration"
        assert error.error_code == "coherence.configuration_error"
        assert error.status_code == 500

    def test_internal_error(self):
        """Test InternalError."""
        error = InternalError(message="Unexpected error")
        assert error.message == "Unexpected error"
        assert error.error_code == "coherence.internal_error"
        assert error.status_code == 500