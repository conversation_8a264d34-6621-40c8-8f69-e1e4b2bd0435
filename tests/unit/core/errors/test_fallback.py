"""
Tests for the fallback strategies.

This module contains unit tests for the fallback strategies defined in
src.coherence.core.errors.fallback.
"""

import pytest

from src.coherence.core.errors.base import ExternalServiceError, LLMServiceError
from src.coherence.core.errors.fallback import (
    CachedResultFallback,
    CallbackFallback,
    DefaultValueFallback,
    Fallback<PERSON>hain,
    fallback_for_external_service,
    fallback_for_llm,
    with_fallback,
)


class TestDefaultValueFallback:
    """Tests for the DefaultValueFallback class."""

    def test_fallback_function(self):
        """Test that fallback function returns the default value."""
        fallback = DefaultValueFallback(default_value=42)
        result = fallback.fallback_function()
        assert result == 42


class TestCachedResultFallback:
    """Tests for the CachedResultFallback class."""

    def test_cache_key_generation_default(self):
        """Test default cache key generation."""
        fallback = CachedResultFallback()
        key1 = fallback._get_cache_key("arg1", "arg2", kwarg1="value1")
        key2 = fallback._get_cache_key("arg1", "arg2", kwarg1="value1")
        key3 = fallback._get_cache_key("arg1", "arg3", kwarg1="value1")

        assert key1 == key2
        assert key1 != key3

    def test_cache_key_generation_custom(self):
        """Test custom cache key generation."""
        def key_func(*args, **kwargs):
            return f"{args[0]}:{kwargs.get('kwarg1', '')}"

        fallback = CachedResultFallback(cache_key=key_func)
        key1 = fallback._get_cache_key("arg1", "arg2", kwarg1="value1")
        key2 = fallback._get_cache_key("arg1", "different", kwarg1="value1")
        key3 = fallback._get_cache_key("arg1", "arg2", kwarg1="different")

        assert key1 == key2
        assert key1 != key3
        assert key1 == "arg1:value1"

    def test_update_and_get_cache(self):
        """Test updating and retrieving from cache."""
        fallback = CachedResultFallback()
        
        # Update cache
        fallback.update_cache(42, "arg1", kwarg1="value1")
        
        # Retrieve from cache
        result = fallback.fallback_function("arg1", kwarg1="value1")
        assert result == 42
        
        # Try with different arguments
        with pytest.raises(KeyError):
            fallback.fallback_function("arg2", kwarg1="value1")


class TestFallbackChain:
    """Tests for the FallbackChain class."""

    def test_empty_chain(self):
        """Test that an empty chain raises an error."""
        chain = FallbackChain(strategies=[])
        with pytest.raises(RuntimeError):
            chain.fallback_function()

    def test_chain_first_success(self):
        """Test that the chain returns the result from the first successful strategy."""
        fallback1 = DefaultValueFallback(default_value=42)
        fallback2 = DefaultValueFallback(default_value=84)
        
        chain = FallbackChain(strategies=[fallback1, fallback2])
        result = chain.fallback_function()
        assert result == 42

    def test_chain_second_success(self):
        """Test that the chain tries the second strategy if the first fails."""
        class FailingFallback:
            def __init__(self, name="failing"):
                self.name = name
            
            def fallback_function(self, *args, **kwargs):
                raise ValueError("Simulated failure")
        
        fallback1 = FailingFallback()
        fallback2 = DefaultValueFallback(default_value=84)
        
        chain = FallbackChain(strategies=[fallback1, fallback2])
        result = chain.fallback_function()
        assert result == 84

    def test_chain_all_fail(self):
        """Test that the chain raises the last error if all strategies fail."""
        class FailingFallback:
            def __init__(self, name="failing", error=ValueError):
                self.name = name
                self.error = error
            
            def fallback_function(self, *args, **kwargs):
                raise self.error("Simulated failure")
        
        fallback1 = FailingFallback(name="first", error=ValueError)
        fallback2 = FailingFallback(name="second", error=KeyError)
        
        chain = FallbackChain(strategies=[fallback1, fallback2])
        with pytest.raises(RuntimeError):
            chain.fallback_function()


class TestCallbackFallback:
    """Tests for the CallbackFallback class."""

    def test_callback_function(self):
        """Test that callback function is called with the same arguments."""
        def callback(*args, **kwargs):
            return f"{args[0]}:{kwargs.get('kwarg1', '')}"
        
        fallback = CallbackFallback(callback=callback)
        result = fallback.fallback_function("arg1", kwarg1="value1")
        assert result == "arg1:value1"


class TestWithFallback:
    """Tests for the with_fallback decorator."""

    def test_function_success(self):
        """Test that the function is called normally when it succeeds."""
        fallback = DefaultValueFallback(default_value=42)
        
        @with_fallback(strategy=fallback)
        def test_func():
            return 100
        
        result = test_func()
        assert result == 100

    def test_function_failure_default_exceptions(self):
        """Test that the fallback is used when the function raises any exception."""
        fallback = DefaultValueFallback(default_value=42)
        
        @with_fallback(strategy=fallback)
        def test_func():
            raise ValueError("Simulated failure")
        
        result = test_func()
        assert result == 42

    def test_function_failure_specific_exceptions(self):
        """Test that the fallback is used only for specified exceptions."""
        fallback = DefaultValueFallback(default_value=42)
        
        @with_fallback(strategy=fallback, exceptions=[ValueError])
        def test_func(error_type=ValueError):
            raise error_type("Simulated failure")
        
        # Should use fallback for ValueError
        result = test_func(error_type=ValueError)
        assert result == 42
        
        # Should not use fallback for KeyError
        with pytest.raises(KeyError):
            test_func(error_type=KeyError)

    def test_cached_result_update(self):
        """Test that successful results update the cache for CachedResultFallback."""
        fallback = CachedResultFallback()
        
        @with_fallback(strategy=fallback)
        def test_func(succeed=True):
            if not succeed:
                raise ValueError("Simulated failure")
            return 100
        
        # First call succeeds and updates cache
        result1 = test_func(succeed=True)
        assert result1 == 100
        
        # Second call fails but uses cached result
        result2 = test_func(succeed=False)
        assert result2 == 100

    def test_fallback_failure(self):
        """Test that original exception is re-raised if fallback also fails."""
        class FailingFallback:
            def __init__(self, name="failing"):
                self.name = name
            
            def fallback_function(self, *args, **kwargs):
                raise ValueError("Fallback failure")
        
        fallback = FailingFallback()
        
        @with_fallback(strategy=fallback)
        def test_func():
            raise KeyError("Original failure")
        
        with pytest.raises(KeyError, match="Original failure"):
            test_func()


class TestSpecializedFallbacks:
    """Tests for specialized fallback decorators."""

    def test_fallback_for_external_service(self):
        """Test the fallback_for_external_service decorator."""
        fallback = DefaultValueFallback(default_value=42)
        
        @fallback_for_external_service(service_name="TestAPI", strategy=fallback)
        def test_func(error_type=None):
            if error_type:
                raise error_type("Simulated failure")
            return 100
        
        # Should use fallback for ExternalServiceError
        result1 = test_func(error_type=ExternalServiceError)
        assert result1 == 42
        
        # Should not use fallback for other errors
        with pytest.raises(ValueError):
            test_func(error_type=ValueError)
        
        # Should return normal result without error
        result3 = test_func()
        assert result3 == 100

    def test_fallback_for_llm(self):
        """Test the fallback_for_llm decorator."""
        fallback = DefaultValueFallback(default_value="Fallback response")
        
        @fallback_for_llm(strategy=fallback)
        def test_func(error_type=None):
            if error_type:
                raise error_type("Simulated failure")
            return "Normal response"
        
        # Should use fallback for LLMServiceError
        result1 = test_func(error_type=LLMServiceError)
        assert result1 == "Fallback response"
        
        # Should not use fallback for other errors
        with pytest.raises(ValueError):
            test_func(error_type=ValueError)
        
        # Should not use fallback for parent class ExternalServiceError
        with pytest.raises(ExternalServiceError):
            test_func(error_type=ExternalServiceError)
        
        # Should return normal result without error
        result4 = test_func()
        assert result4 == "Normal response"