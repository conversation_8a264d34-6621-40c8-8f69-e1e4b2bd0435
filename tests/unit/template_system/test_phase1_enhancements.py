"""
Test Phase 1 enhancements to the unified template system.
"""

import pytest
from unittest.mock import Mock, patch

from src.coherence.template_system.unified_generator import UnifiedTemplateGenerator


class TestPhase1Enhancements:
    """Test the Phase 1 enhancements to unified templates."""
    
    @pytest.fixture
    def generator(self):
        """Create a UnifiedTemplateGenerator instance for testing."""
        return UnifiedTemplateGenerator()
        
    def test_generate_integration_section(self, generator):
        """Test that integration section is properly generated."""
        operation = {
            "operationId": "getWeather",
            "summary": "Get current weather",
            "security": [{"apiKey": []}],
            "responses": {
                "200": {
                    "description": "Success",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "temperature": {"type": "number"},
                                    "conditions": {"type": "string"}
                                }
                            }
                        }
                    }
                }
            }
        }
        
        template = generator.generate_from_openapi_endpoint(
            operation=operation,
            path="/v1/weather",
            method="GET",
            base_url="https://api.weather.com"
        )
        
        # Check integration section exists
        assert "integration" in template["action"], "Missing integration section"
        integration = template["action"]["integration"]
        
        # Verify integration fields
        assert integration["base_url"] == "https://api.weather.com"
        assert integration["api_version"] == "v1"
        assert "credential_ref" in integration
        assert integration["credential_ref"] == "api_weather_com_api_key"
        
    def test_generate_validation_rules(self, generator):
        """Test that validation rules are generated from schema."""
        operation = {
            "operationId": "createUser",
            "parameters": [
                {
                    "name": "email",
                    "in": "query",
                    "required": True,
                    "schema": {
                        "type": "string",
                        "format": "email",
                        "minLength": 5,
                        "maxLength": 100
                    }
                },
                {
                    "name": "age",
                    "in": "query",
                    "schema": {
                        "type": "integer",
                        "minimum": 18,
                        "maximum": 120
                    }
                }
            ],
            "responses": {"200": {"description": "Success"}}
        }
        
        template = generator.generate_from_openapi_endpoint(
            operation=operation,
            path="/users",
            method="POST",
            base_url="https://api.example.com"
        )
        
        # Check validation rules exist
        assert "validation_rules" in template["parameters"]
        rules = template["parameters"]["validation_rules"]
        
        # Check email validation
        assert "email" in rules
        email_rules = rules["email"]
        assert email_rules["type"] == "string"
        assert email_rules["format"] == "email"
        assert email_rules["min_length"] == 5
        assert email_rules["max_length"] == 100
        assert email_rules["required"] is True
        
        # Check age validation
        assert "age" in rules
        age_rules = rules["age"]
        assert age_rules["type"] == "integer"
        assert age_rules["min"] == 18
        assert age_rules["max"] == 120
        
    def test_generate_transformation_rules(self, generator):
        """Test that transformation rules are generated."""
        operation = {
            "operationId": "updateProfile",
            "parameters": [
                {
                    "name": "email",
                    "in": "query",
                    "schema": {
                        "type": "string",
                        "format": "email"
                    }
                },
                {
                    "name": "username",
                    "in": "query",
                    "schema": {
                        "type": "string"
                    }
                },
                {
                    "name": "tags",
                    "in": "query",
                    "schema": {
                        "type": "array",
                        "uniqueItems": True
                    }
                }
            ],
            "responses": {"200": {"description": "Success"}}
        }
        
        template = generator.generate_from_openapi_endpoint(
            operation=operation,
            path="/profile",
            method="PUT",
            base_url="https://api.example.com"
        )
        
        # Check transformations exist
        assert "transformations" in template["parameters"]
        transforms = template["parameters"]["transformations"]
        
        # Check email transformations
        assert "email" in transforms
        assert "trim" in transforms["email"]
        assert "lowercase" in transforms["email"]
        
        # Check username transformations  
        assert "username" in transforms
        assert "trim" in transforms["username"]
        assert "lowercase" in transforms["username"]
        
        # Check array transformations
        assert "tags" in transforms
        assert "deduplicate" in transforms["tags"]
        
    def test_generate_test_data(self, generator):
        """Test that test data is generated."""
        operation = {
            "operationId": "getUser",
            "parameters": [
                {
                    "name": "user_id",
                    "in": "path",
                    "required": True,
                    "schema": {"type": "string"}
                }
            ],
            "responses": {
                "200": {
                    "description": "Success",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "id": {"type": "string"},
                                    "name": {"type": "string"},
                                    "email": {"type": "string", "format": "email"},
                                    "active": {"type": "boolean"}
                                }
                            }
                        }
                    }
                }
            }
        }
        
        template = generator.generate_from_openapi_endpoint(
            operation=operation,
            path="/users/{user_id}",
            method="GET",
            base_url="https://api.example.com"
        )
        
        # Check test data exists
        assert "test_data" in template
        test_data = template["test_data"]
        
        # Check mock responses
        assert "mock_responses" in test_data
        assert "success" in test_data["mock_responses"]
        mock_response = test_data["mock_responses"]["success"]
        assert "id" in mock_response
        assert "name" in mock_response
        assert "email" in mock_response
        assert "active" in mock_response
        
        # Check sample parameters
        assert "sample_parameters" in test_data
        assert "user_id" in test_data["sample_parameters"]
        
    def test_crfs_auto_selection(self, generator):
        """Test that CRFS auto-selection is enabled."""
        operation = {
            "operationId": "listItems",
            "responses": {
                "200": {
                    "description": "Success",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "id": {"type": "integer"},
                                        "name": {"type": "string"}
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        template = generator.generate_from_openapi_endpoint(
            operation=operation,
            path="/items",
            method="GET",
            base_url="https://api.example.com"
        )
        
        # Check CRFS configuration
        assert "crfs" in template["response"]
        crfs_config = template["response"]["crfs"]
        
        # Verify auto-selection is enabled
        assert crfs_config["auto_select"] is True
        assert crfs_config["default_format"] == "structured"
        
    def test_api_version_extraction(self, generator):
        """Test API version extraction from path."""
        operation = {
            "operationId": "getStatus",
            "responses": {"200": {"description": "Success"}}
        }
        
        # Test with version in path
        template = generator.generate_from_openapi_endpoint(
            operation=operation,
            path="/v2/status",
            method="GET",
            base_url="https://api.example.com"
        )
        
        assert template["action"]["integration"]["api_version"] == "v2"
        
        # Test without version in path (should default to v1)
        template = generator.generate_from_openapi_endpoint(
            operation=operation,
            path="/status",
            method="GET",
            base_url="https://api.example.com"
        )
        
        assert template["action"]["integration"]["api_version"] == "v1"