"""
Tests for documentation template functionality.

Tests the creation, indexing, retrieval, and rendering of documentation templates
for API endpoints.
"""

import uuid
import json
from unittest.mock import AsyncMock, Mock, patch
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.models.template import Template, TemplateCategory
from src.coherence.models.integration import APIIntegration, APIEndpoint, IntegrationStatus
from src.coherence.schemas.admin_template import (
    DocumentationTemplate,
    DocumentationContent,
    ParameterDocumentation,
    ResponseDocumentation,
    ExampleDocumentation,
)
from src.coherence.openapi_adapter.action_generator import ActionGenerator
from src.coherence.services.vector_indexer import VectorIndexer
from src.coherence.template_system.services.template_service import TemplateService


class TestDocumentationTemplates:
    """Test documentation template functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def mock_llm_provider(self):
        """Mock LLM provider."""
        provider = AsyncMock()
        provider.provider_name = "mock"
        provider.provider_model = "mock-model"
        return provider

    @pytest.fixture
    def mock_qdrant_client(self):
        """Mock Qdrant client."""
        client = AsyncMock()
        return client

    @pytest.fixture
    def vector_indexer(self, mock_qdrant_client):
        """Create vector indexer with mocked client."""
        return VectorIndexer(qdrant_client=mock_qdrant_client)

    @pytest.fixture
    def action_generator(self, mock_llm_provider):
        """Create action generator with mocked LLM."""
        return ActionGenerator(llm_provider=mock_llm_provider)

    @pytest.fixture
    def template_service(self):
        """Create template service."""
        return TemplateService()

    @pytest.fixture
    def sample_api_endpoint(self):
        """Create sample API endpoint."""
        return APIEndpoint(
            id=uuid.uuid4(),
            integration_id=uuid.uuid4(),
            path="/api/v1/users/{id}",
            method="GET",
            summary="Get user by ID",
            description="Retrieve user information by their unique identifier",
            parameters=[
                {
                    "name": "id",
                    "in": "path",
                    "required": True,
                    "description": "User ID",
                    "schema": {"type": "string"}
                }
            ],
            responses={
                "200": {
                    "description": "User found",
                    "schema": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "string"},
                            "name": {"type": "string"},
                            "email": {"type": "string"}
                        }
                    }
                },
                "404": {
                    "description": "User not found",
                    "schema": {
                        "type": "object", 
                        "properties": {
                            "error": {"type": "string"}
                        }
                    }
                }
            }
        )

    @pytest.fixture
    def sample_integration(self):
        """Create sample API integration."""
        return APIIntegration(
            id=uuid.uuid4(),
            tenant_id=uuid.uuid4(),
            name="User API",
            description="API for user management",
            status=IntegrationStatus.ACTIVE,
            spec={"openapi": "3.0.0", "info": {"title": "User API", "version": "1.0"}}
        )

    @pytest.mark.asyncio
    async def test_generate_documentation_template(
        self, action_generator, mock_llm_provider, sample_api_endpoint, sample_integration
    ):
        """Test generating documentation template with LLM."""
        # Mock LLM response
        mock_llm_response = json.dumps({
            "documentation": {
                "endpoint": "/api/v1/users/{id}",
                "method": "GET",
                "summary": "Get user by ID",
                "description": "Retrieve detailed user information by their unique identifier. This endpoint returns user profile data including name, email, and other attributes.",
                "parameters": [
                    {
                        "name": "id",
                        "type": "string",
                        "required": True,
                        "description": "The unique identifier of the user to retrieve",
                        "example": "user_123abc"
                    }
                ],
                "responses": [
                    {
                        "status_code": "200",
                        "description": "Successfully retrieved user",
                        "schema": {
                            "id": "string",
                            "name": "string",
                            "email": "string"
                        }
                    },
                    {
                        "status_code": "404",
                        "description": "User not found",
                        "schema": {
                            "error": "string"
                        }
                    }
                ],
                "examples": [
                    {
                        "name": "Retrieve user John Doe",
                        "request": {
                            "method": "GET",
                            "url": "/api/v1/users/user_123abc",
                            "headers": {
                                "Authorization": "Bearer <token>"
                            }
                        },
                        "response": {
                            "status": 200,
                            "body": {
                                "id": "user_123abc",
                                "name": "John Doe",
                                "email": "<EMAIL>"
                            }
                        }
                    }
                ]
            },
            "template": {
                "key": "doc_api_v1_users_{id}_get",
                "name": "Get User Documentation",
                "description": "Documentation for retrieving user information",
                "category": "documentation",
                "content": "# Get User by ID\n\n{{ documentation | render_documentation }}"
            }
        })
        
        mock_llm_provider.async_generate.return_value = mock_llm_response

        # Call the method
        result = await action_generator._generate_documentation_template_with_llm(
            endpoint_spec=sample_api_endpoint.dict(),
            endpoint=sample_api_endpoint,
            integration=sample_integration,
            template_key="doc_api_v1_users_{id}_get",
            intent_template_key="get_user_intent",
            parameters=[{"name": "id", "type": "string", "required": True}],
            tenant_id=uuid.uuid4(),
            human_readable_name="Get user documentation"
        )

        # Assert result structure
        assert result["key"] == "doc_api_v1_users_{id}_get"
        assert result["category"] == "documentation"
        assert "documentation" in result
        assert result["documentation"]["endpoint"] == "/api/v1/users/{id}"
        assert result["documentation"]["method"] == "GET"
        assert len(result["documentation"]["parameters"]) == 1
        assert len(result["documentation"]["responses"]) == 2
        assert len(result["documentation"]["examples"]) == 1

    @pytest.mark.asyncio
    async def test_index_documentation_template(
        self, vector_indexer, mock_qdrant_client, mock_db_session
    ):
        """Test indexing documentation template for vector search."""
        # Mock Qdrant operations
        mock_qdrant_client.upsert.return_value = None
        
        # Sample documentation content
        documentation_content = {
            "endpoint": "/api/v1/users/{id}",
            "method": "GET",
            "summary": "Get user by ID",
            "description": "Retrieve user information",
            "parameters": [
                {
                    "name": "id",
                    "type": "string",
                    "required": True,
                    "description": "User ID"
                }
            ]
        }

        # Call the method
        result = await vector_indexer.upsert_documentation_template(
            db=mock_db_session,
            template_id="template_123",
            template_key="doc_api_v1_users_{id}_get",
            documentation_content=documentation_content,
            index_name="template_idx_global",
            tenant_id="tenant_123",
            metadata={"category": "documentation"}
        )

        # Assert upsert was called with correct parameters
        mock_qdrant_client.upsert.assert_called_once()
        call_args = mock_qdrant_client.upsert.call_args
        
        assert call_args[1]["collection_name"] == "template_idx_global"
        assert len(call_args[1]["points"]) == 1
        
        point = call_args[1]["points"][0]
        assert point.id == "template_123"
        assert point.payload["template_key"] == "doc_api_v1_users_{id}_get"
        assert point.payload["category"] == "documentation"
        assert point.payload["is_documentation"] is True
        assert "documentation_questions" in point.payload

    @pytest.mark.asyncio
    async def test_retrieve_documentation_template(
        self, template_service, mock_db_session
    ):
        """Test retrieving documentation template by key."""
        # Mock database query
        mock_template = Template(
            id=uuid.uuid4(),
            key="doc_api_v1_users_{id}_get",
            name="Get User Documentation",
            category=TemplateCategory.DOCUMENTATION,
            content={
                "endpoint": "/api/v1/users/{id}",
                "method": "GET",
                "summary": "Get user by ID",
                "description": "Retrieve user information"
            },
            tenant_id=uuid.uuid4()
        )
        
        mock_db_session.execute = AsyncMock()
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_template
        mock_db_session.execute.return_value = mock_result

        # Call the method
        result = await template_service.get_template(
            db=mock_db_session,
            key="doc_api_v1_users_{id}_get",
            category="documentation",
            tenant_id=uuid.uuid4()
        )

        # Assert result
        assert result is not None
        assert result.key == "doc_api_v1_users_{id}_get"
        assert result.category == TemplateCategory.DOCUMENTATION
        assert result.content["endpoint"] == "/api/v1/users/{id}"

    @pytest.mark.asyncio
    async def test_render_documentation_template(
        self, template_service, mock_db_session
    ):
        """Test rendering documentation template to human-readable format."""
        # Mock database query
        mock_template = Template(
            id=uuid.uuid4(),
            key="doc_api_v1_users_{id}_get",
            name="Get User Documentation",
            category=TemplateCategory.DOCUMENTATION,
            content="# {{ documentation.endpoint }}\n\n**Method**: {{ documentation.method }}\n\n{{ documentation.description }}",
            tenant_id=uuid.uuid4()
        )
        
        mock_db_session.execute = AsyncMock()
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_template
        mock_db_session.execute.return_value = mock_result

        # Context for rendering
        context = {
            "documentation": {
                "endpoint": "/api/v1/users/{id}",
                "method": "GET",
                "description": "Retrieve user information by their unique identifier"
            }
        }

        # Call the method
        result = await template_service.render_template(
            db=mock_db_session,
            key="doc_api_v1_users_{id}_get",
            category="documentation",
            context=context,
            tenant_id=uuid.uuid4()
        )

        # Assert rendered output
        assert "# /api/v1/users/{id}" in result
        assert "**Method**: GET" in result
        assert "Retrieve user information by their unique identifier" in result

    @pytest.mark.asyncio
    async def test_documentation_template_schema_validation(self):
        """Test DocumentationTemplate schema validation."""
        # Valid documentation template
        valid_data = {
            "key": "doc_api_v1_users_{id}_get",
            "name": "Get User Documentation",
            "description": "Documentation for user retrieval",
            "category": "documentation",
            "documentation_content": {
                "endpoint": "/api/v1/users/{id}",
                "method": "GET",
                "summary": "Get user by ID",
                "description": "Retrieve user information",
                "parameters": [
                    {
                        "name": "id",
                        "type": "string",
                        "required": True,
                        "description": "User ID",
                        "example": "user_123"
                    }
                ],
                "responses": [
                    {
                        "status_code": "200",
                        "description": "Success",
                        "schema": {"type": "object"}
                    }
                ],
                "examples": [
                    {
                        "name": "Example 1",
                        "request": {
                            "method": "GET",
                            "url": "/api/v1/users/user_123"
                        },
                        "response": {
                            "status": 200,
                            "body": {"id": "user_123"}
                        }
                    }
                ]
            }
        }

        # Create schema instance
        template = DocumentationTemplate(**valid_data)
        
        # Assert structure
        assert template.key == "doc_api_v1_users_{id}_get"
        assert template.category == "documentation"
        assert template.documentation_content.endpoint == "/api/v1/users/{id}"
        assert len(template.documentation_content.parameters) == 1
        assert len(template.documentation_content.responses) == 1
        assert len(template.documentation_content.examples) == 1

    @pytest.mark.asyncio
    async def test_documentation_search_integration(
        self, vector_indexer, mock_qdrant_client
    ):
        """Test searching for documentation templates."""
        # Mock Qdrant search results
        mock_search_results = [
            Mock(
                score=0.95,
                payload={
                    "template_key": "doc_api_v1_users_{id}_get",
                    "category": "documentation",
                    "documentation_questions": [
                        "How do I get user information?",
                        "What is the user retrieval endpoint?"
                    ]
                }
            )
        ]
        
        mock_qdrant_client.search.return_value = mock_search_results

        # Perform search
        query = "How to get user details?"
        results = await mock_qdrant_client.search(
            collection_name="template_idx_global",
            query_vector=[0.1] * 1536,  # Mock embedding
            limit=5,
            query_filter={
                "must": [
                    {"key": "category", "match": {"value": "documentation"}}
                ]
            }
        )

        # Assert results
        assert len(results) == 1
        assert results[0].payload["template_key"] == "doc_api_v1_users_{id}_get"
        assert results[0].payload["category"] == "documentation"
        assert results[0].score > 0.9

    @pytest.mark.asyncio
    async def test_documentation_error_handling(
        self, action_generator, mock_llm_provider
    ):
        """Test error handling in documentation template generation."""
        # Mock LLM error
        mock_llm_provider.async_generate.side_effect = Exception("LLM service unavailable")

        # Try to generate documentation
        with pytest.raises(Exception) as exc:
            await action_generator._generate_documentation_template_with_llm(
                endpoint_spec={},
                endpoint=Mock(),
                integration=Mock(),
                template_key="test_doc",
                intent_template_key="test_intent",
                parameters=[],
                tenant_id=uuid.uuid4(),
                human_readable_name="Test documentation"
            )

        assert "LLM service unavailable" in str(exc.value)