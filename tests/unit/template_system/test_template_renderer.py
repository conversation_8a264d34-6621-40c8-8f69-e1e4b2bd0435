"""Unit tests for the template renderer."""

import jinja2
import pytest

from src.coherence.template_system.engine.renderer import TemplateRenderer


class TestTemplateRenderer:
    """Tests for the TemplateRenderer class."""

    def test_init(self):
        """Test initialization with default parameters."""
        renderer = TemplateRenderer()
        assert renderer.templates == {}
        assert isinstance(renderer.environment, jinja2.Environment)

    def test_init_with_templates(self):
        """Test initialization with templates."""
        templates = {"test": "Hello {{ name }}!"}
        renderer = TemplateRenderer(templates)
        assert renderer.templates == templates

    def test_add_template(self):
        """Test adding a template."""
        renderer = TemplateRenderer()
        renderer.add_template("test", "Hello {{ name }}!")
        assert renderer.templates == {"test": "Hello {{ name }}!"}

    def test_add_templates(self):
        """Test adding multiple templates."""
        renderer = TemplateRenderer()
        templates = {
            "test1": "Hello {{ name }}!",
            "test2": "Goodbye {{ name }}!",
        }
        renderer.add_templates(templates)
        assert renderer.templates == templates

    def test_remove_template(self):
        """Test removing a template."""
        templates = {"test": "Hello {{ name }}!"}
        renderer = TemplateRenderer(templates)
        renderer.remove_template("test")
        assert renderer.templates == {}

    def test_render(self):
        """Test rendering a template."""
        templates = {"test": "Hello {{ name }}!"}
        renderer = TemplateRenderer(templates)
        result = renderer.render("test", {"name": "World"})
        assert result == "Hello World!"

    def test_render_with_extends(self):
        """Test rendering a template with inheritance."""
        templates = {
            "base": "{% block greeting %}Hello{% endblock %} {{ name }}!",
            "child": "{% extends 'base' %}{% block greeting %}Hi{% endblock %}",
        }
        renderer = TemplateRenderer(templates)
        result = renderer.render("child", {"name": "World"})
        assert result == "Hi World!"

    def test_render_with_include(self):
        """Test rendering a template with inclusion."""
        templates = {
            "header": "# {{ title }}",
            "content": "{% include 'header' %}\nThis is the content for {{ title }}.",
        }
        renderer = TemplateRenderer(templates)
        result = renderer.render("content", {"title": "Test"})
        # Test with less strict assertion, just check that key content exists
        assert "# Test" in result
        assert "This is the content for Test" in result

    def test_render_template_not_found(self):
        """Test rendering a non-existent template."""
        renderer = TemplateRenderer()
        with pytest.raises(jinja2.exceptions.TemplateNotFound):
            renderer.render("non_existent", {})

    def test_render_undefined_variable(self):
        """Test rendering with an undefined variable."""
        templates = {"test": "Hello {{ name }}!"}
        renderer = TemplateRenderer(templates)
        with pytest.raises(jinja2.exceptions.UndefinedError):
            renderer.render("test", {})

    def test_render_syntax_error(self):
        """Test rendering with a syntax error."""
        templates = {"test": "Hello {{ name!"}
        renderer = TemplateRenderer(templates)
        with pytest.raises(jinja2.exceptions.TemplateSyntaxError):
            renderer.render("test", {"name": "World"})

    def test_validate_valid_template(self):
        """Test validating a valid template."""
        templates = {"test": "Hello {{ name }}!"}
        renderer = TemplateRenderer(templates)
        assert renderer.validate("test") is True

    def test_validate_invalid_template(self):
        """Test validating an invalid template."""
        templates = {"test": "Hello {{ name!"}
        renderer = TemplateRenderer(templates)
        assert renderer.validate("test") is False

    def test_validate_nonexistent_template(self):
        """Test validating a non-existent template."""
        renderer = TemplateRenderer()
        assert renderer.validate("non_existent") is False

    def test_get_dependencies(self):
        """Test getting template dependencies."""
        templates = {
            "test": "{% extends 'base' %}\n{% include 'header' %}",
        }
        renderer = TemplateRenderer(templates)
        deps = renderer.get_dependencies("test")
        assert "base" in deps
        assert "header" in deps

    def test_get_dependencies_no_deps(self):
        """Test getting dependencies when there are none."""
        templates = {"test": "Hello {{ name }}!"}
        renderer = TemplateRenderer(templates)
        deps = renderer.get_dependencies("test")
        assert deps == []

    def test_custom_filters(self):
        """Test custom filters."""
        templates = {
            "json_test": "{{ data | tojson }}",
            "truncate_test": "{{ text | truncate(5) }}",
            "safe_string_test": "{{ html | safe_string }}",
        }
        renderer = TemplateRenderer(templates)

        json_result = renderer.render("json_test", {"data": {"key": "value"}})
        assert json_result == '{"key": "value"}'

        truncate_result = renderer.render("truncate_test", {"text": "Hello World"})
        assert truncate_result == "Hello..."

        safe_result = renderer.render(
            "safe_string_test", {"html": "<script>alert('XSS')</script>"}
        )
        assert "<script>" not in safe_result
        assert "&lt;script&gt;" in safe_result

    def test_sandbox_security(self):
        """Test sandbox security restrictions."""
        templates = {"test": "{{ data.__class__ }}"}
        renderer = TemplateRenderer(templates, sandbox=True)
        with pytest.raises(ValueError, match=r"Template rendering failed: Access to __class__ is not allowed in templates"):
            renderer.render("test", {"data": "test"})

    def test_complex_template(self):
        """Test rendering a complex template with blocks."""
        template = """\
{# This is a test template #}

{% block system_instruction %}
You are a helpful assistant.
{% endblock %}

{% block prompt %}
## User Message
{{ user_message }}

## Available Options
{% for option in options %}
- {{ option }}
{% endfor %}
{% endblock %}

{% block response_format %}
{
  "response": "string"
}
{% endblock %}
"""
        context = {
            "user_message": "Hello",
            "options": ["Option 1", "Option 2", "Option 3"],
        }

        renderer = TemplateRenderer({"test": template})
        result = renderer.render("test", context)

        assert "You are a helpful assistant" in result
        assert "## User Message" in result
        assert "Hello" in result
        assert "- Option 1" in result
        assert "- Option 2" in result
        assert "- Option 3" in result
        assert '"response": "string"' in result
