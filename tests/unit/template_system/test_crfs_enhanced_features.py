"""
Test suite for CRFS enhanced features (Phase 4).

This module tests the new CRFS v2.0+ enhancements including:
- Auto-format selection
- Multi-format support
- Mixed format responses
- Streaming capabilities
- Error mapping enhancements
"""

import pytest
from unittest.mock import Mock, patch
from src.coherence.template_system.crfs_formatter import CRFSFormatter


class TestCRFSAutoFormatSelection:
    """Test CRFS auto-format selection functionality."""
    
    def test_auto_select_json_format(self):
        """Test auto-selection of JSON format based on Accept header."""
        config = {
            "kind": "coherence-response",
            "crfs_version": "2.0",
            "auto_select": True,
            "default_format": "structured",
            "formats": {
                "application/json": {
                    "type": "raw"
                },
                "structured": {
                    "type": "structured",
                    "format": {
                        "structure": {
                            "sections": []
                        }
                    }
                }
            }
        }
        
        formatter = CRFSFormatter(config)
        
        # Test JSON format selection
        selected = formatter._auto_select_format("application/json, text/html;q=0.9")
        assert selected == "application/json"
    
    def test_auto_select_text_format(self):
        """Test auto-selection of text format."""
        config = {
            "kind": "coherence-response",
            "crfs_version": "2.0",
            "auto_select": True,
            "formats": {
                "text/plain": {"type": "template"},
                "application/json": {"type": "raw"}
            }
        }
        
        formatter = CRFSFormatter(config)
        selected = formatter._auto_select_format("text/plain")
        assert selected == "text/plain"
    
    def test_auto_select_wildcard_fallback(self):
        """Test fallback to default format with wildcard Accept header."""
        config = {
            "kind": "coherence-response",
            "crfs_version": "2.0",
            "auto_select": True,
            "default_format": "structured",
            "formats": {
                "structured": {"type": "structured"}
            }
        }
        
        formatter = CRFSFormatter(config)
        selected = formatter._auto_select_format("*/*")
        assert selected == "structured"
    
    def test_auto_select_no_match(self):
        """Test behavior when no format matches Accept header."""
        config = {
            "kind": "coherence-response",
            "crfs_version": "2.0",
            "auto_select": True,
            "formats": {
                "application/json": {"type": "raw"}
            }
        }
        
        formatter = CRFSFormatter(config)
        selected = formatter._auto_select_format("application/xml")
        assert selected is None


class TestCRFSMultiFormatSupport:
    """Test CRFS multi-format support."""
    
    def test_format_with_auto_selection_enabled(self):
        """Test formatting with auto-selection enabled."""
        config = {
            "kind": "coherence-response",
            "crfs_version": "2.0",
            "auto_select": True,
            "formats": {
                "application/json": {
                    "type": "raw"
                },
                "structured": {
                    "type": "template",
                    "template": "Result: {{ result }}"
                }
            }
        }
        
        formatter = CRFSFormatter(config)
        context = {"result": "test data"}
        
        # Should use auto-selection
        result = formatter.format_with_auto_selection(context, "application/json")
        assert '"test data"' in result  # Raw JSON format
    
    def test_mixed_response_formatting(self):
        """Test formatting mixed response with different section formats."""
        base_config = {
            "kind": "coherence-response",
            "crfs_version": "2.0"
        }
        
        sections = [
            {
                "title": "Summary",
                "format": {
                    "type": "template",
                    "template": "Summary: {{ result.summary }}"
                },
                "data_path": "result"
            },
            {
                "title": "Raw Data",
                "format": {
                    "type": "raw"
                },
                "data_path": "result.data"
            }
        ]
        
        formatter = CRFSFormatter(base_config)
        context = {
            "result": {
                "summary": "Test summary",
                "data": {"key": "value"}
            }
        }
        
        result = formatter.format_mixed_response(context, sections)
        
        assert "## Summary" in result
        assert "Summary: Test summary" in result
        assert "## Raw Data" in result
        assert '"key": "value"' in result


class TestCRFSStreamingSupport:
    """Test CRFS streaming capabilities."""
    
    def test_streaming_response_formatting(self):
        """Test formatting of streaming response chunks."""
        config = {
            "kind": "coherence-response",
            "crfs_version": "2.0",
            "format": {
                "type": "template",
                "template": "Chunk: {{ result }}"
            }
        }
        
        formatter = CRFSFormatter(config)
        
        # Mock streaming data
        def mock_stream():
            yield {"result": "chunk1"}
            yield {"result": "chunk2"}
            yield {"result": "chunk3"}
        
        result = formatter.format_streaming_response(mock_stream())
        
        assert "Chunk: chunk1" in result
        assert "Chunk: chunk2" in result
        assert "Chunk: chunk3" in result


class TestCRFSMetadata:
    """Test CRFS metadata functionality."""
    
    def test_response_metadata_generation(self):
        """Test generation of response metadata."""
        config = {
            "kind": "coherence-response",
            "crfs_version": "2.0",
            "auto_select": True,
            "default_format": "structured",
            "formats": {
                "application/json": {"type": "raw"},
                "text/plain": {"type": "template"},
                "structured": {"type": "structured"}
            }
        }
        
        formatter = CRFSFormatter(config)
        metadata = formatter.get_response_metadata()
        
        assert metadata["crfs_version"] == "2.0"
        assert metadata["kind"] == "coherence-response"
        assert metadata["auto_select_enabled"] is True
        assert metadata["default_format"] == "structured"
        assert metadata["supports_streaming"] is True
        assert metadata["supports_mixed_formats"] is True
        assert set(metadata["available_formats"]) == {
            "application/json", "text/plain", "structured"
        }


class TestCRFSErrorHandling:
    """Test enhanced CRFS error handling."""
    
    def test_format_with_error_mapping(self):
        """Test formatting with error mapping support."""
        config = {
            "kind": "coherence-response",
            "crfs_version": "2.0",
            "error_mapping": {
                "404": "Resource '{{ parameters.resource_id }}' not found",
                "401": "Authentication failed"
            },
            "format": {
                "type": "template",
                "template": "Success: {{ result }}"
            }
        }
        
        formatter = CRFSFormatter(config)
        
        # Test error mapping
        context = {"parameters": {"resource_id": "user123"}}
        result = formatter.format_with_error_mapping(context, error_code="404")
        assert "Resource 'user123' not found" in result
        
        # Test static error mapping
        result = formatter.format_with_error_mapping(context, error_code="401")
        assert "Authentication failed" in result
        
        # Test normal formatting without error
        context = {"result": "test data"}
        result = formatter.format_with_error_mapping(context)
        assert "Success: test data" in result
    
    def test_streaming_error_handling(self):
        """Test error handling in streaming responses."""
        config = {
            "kind": "coherence-response",
            "crfs_version": "2.0",
            "format": {
                "type": "template",
                "template": "Chunk: {{ result }}"
            }
        }
        
        formatter = CRFSFormatter(config)
        
        def problematic_stream():
            yield {"result": "good_chunk"}
            yield {"bad": "chunk"}  # This will cause a template error
            yield {"result": "another_good_chunk"}
        
        with patch('coherence.template_system.crfs_formatter.logger') as mock_logger:
            result = formatter.format_streaming_response(problematic_stream())
            
            # Should contain good chunks and skip bad ones
            assert "Chunk: good_chunk" in result
            assert "Chunk: another_good_chunk" in result
            
            # Should log errors for bad chunks
            mock_logger.error.assert_called()


class TestCRFSIntegration:
    """Test CRFS integration with unified templates."""
    
    def test_from_unified_template_with_enhanced_features(self):
        """Test creating CRFS formatter from unified template with enhanced features."""
        template_data = {
            "meta": {
                "key": "test_template",
                "endpoint_id": "test_endpoint"
            },
            "response": {
                "crfs": {
                    "kind": "coherence-response",
                    "crfs_version": "2.0",
                    "auto_select": True,
                    "default_format": "structured",
                    "formats": {
                        "application/json": {"type": "raw"},
                        "structured": {
                            "type": "structured",
                            "format": {
                                "structure": {"sections": []}
                            }
                        }
                    }
                },
                "error_mapping": {
                    "500": "Internal server error occurred"
                }
            }
        }
        
        formatter = CRFSFormatter.from_unified_template(template_data)
        
        assert formatter.config["auto_select"] is True
        assert formatter.config["error_mapping"]["500"] == "Internal server error occurred"
        assert "formats" in formatter.config
        assert formatter.template_context["template_key"] == "test_template"
    
    def test_parameter_help_generation(self):
        """Test parameter help generation using CRFS formatting."""
        config = {
            "kind": "coherence-response",
            "crfs_version": "2.0"
        }
        
        formatter = CRFSFormatter(config)
        
        parameters_schema = {
            "user_id": {
                "type": "string",
                "required": True,
                "description": "Unique identifier for the user",
                "minLength": 1,
                "maxLength": 50
            },
            "age": {
                "type": "integer",
                "required": False,
                "description": "User's age",
                "minimum": 0,
                "maximum": 150
            },
            "status": {
                "type": "string",
                "enum": ["active", "inactive", "pending"]
            }
        }
        
        help_text = formatter.generate_parameter_help(parameters_schema)
        
        assert "**user_id** (string) *required*" in help_text
        assert "Unique identifier for the user" in help_text
        assert "min length: 1, max length: 50" in help_text
        assert "**age** (integer)" in help_text
        assert "min: 0, max: 150" in help_text
        assert "options: active, inactive, pending" in help_text


# Integration test fixtures
@pytest.fixture
def sample_crfs_config():
    """Provide a sample CRFS configuration for testing."""
    return {
        "kind": "coherence-response",
        "crfs_version": "2.0",
        "auto_select": True,
        "default_format": "structured",
        "formats": {
            "application/json": {
                "type": "raw"
            },
            "text/plain": {
                "type": "template",
                "template": "{{ result }}"
            },
            "structured": {
                "type": "structured",
                "format": {
                    "structure": {
                        "sections": [
                            {
                                "id": "summary",
                                "type": "text",
                                "title": "Summary",
                                "content": "{{ result.summary }}"
                            }
                        ]
                    }
                }
            }
        },
        "error_mapping": {
            "404": "Resource not found",
            "500": "Internal server error"
        }
    }


@pytest.fixture
def sample_context():
    """Provide sample context data for testing."""
    return {
        "result": {
            "summary": "Test operation completed successfully",
            "data": [
                {"id": 1, "name": "Item 1"},
                {"id": 2, "name": "Item 2"}
            ],
            "metadata": {
                "total": 2,
                "processed_at": "2024-01-01T12:00:00Z"
            }
        },
        "parameters": {
            "user_id": "user123",
            "filter": "active"
        }
    }


class TestCRFSEndToEnd:
    """End-to-end tests for CRFS enhanced features."""
    
    def test_complete_crfs_workflow(self, sample_crfs_config, sample_context):
        """Test complete CRFS workflow with all enhanced features."""
        formatter = CRFSFormatter(sample_crfs_config)
        
        # Test metadata generation
        metadata = formatter.get_response_metadata()
        assert metadata["crfs_version"] == "2.0"
        assert metadata["auto_select_enabled"] is True
        
        # Test auto-format selection
        json_result = formatter.format_with_auto_selection(
            sample_context, 
            accept_header="application/json"
        )
        assert '"summary": "Test operation completed successfully"' in json_result
        
        # Test structured format
        structured_result = formatter.format_with_auto_selection(
            sample_context,
            accept_header="text/html"  # Should fall back to structured
        )
        assert "# Summary" in structured_result
        assert "Test operation completed successfully" in structured_result
        
        # Test error mapping
        error_result = formatter.format_with_error_mapping(
            sample_context,
            error_code="404"
        )
        assert "Resource not found" in error_result