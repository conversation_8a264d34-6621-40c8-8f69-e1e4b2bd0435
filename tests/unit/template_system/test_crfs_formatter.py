import json

from src.coherence.template_system.crfs_formatter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_unwrap_nested_result():
    config = {"kind": "coherence-response", "format": {"type": "raw"}}
    formatter = CRFSFormatter(config)
    context = {
        "result": {
            "success": True,
            "status_code": 200,
            "result": {
                "success": True,
                "status_code": 200,
                "result": {"value": 42},
            },
        }
    }
    output = formatter.format(context)
    result = json.loads(output)
    # Should have unwrapped the nested result and kept the final unwrapped data
    assert "result" in result
    assert result["result"]["value"] == 42
    # Should also include response metadata from unwrapping
    assert "response_metadata" in result


def test_condition_get_not_split():
    config = {"kind": "coherence-response", "format": {"type": "raw"}}
    formatter = CRFSFormatter(config)
    context = {"result": {"@graph": [1]}}
    condition = 'len(result.get("@graph")) > 0'
    assert formatter._evaluate_condition(condition, context)
