# Coherence Tests

This directory contains tests for the Coherence middleware. The tests are organized into three categories:

- **Unit Tests**: Tests for individual components in isolation (with dependencies mocked)
- **Integration Tests**: Tests that verify components work together correctly
- **Functional Tests**: End-to-end tests that verify system behavior from the API endpoints

## Running Tests

### With Docker (Recommended)

The easiest way to run tests is inside the Docker environment which has all dependencies configured:

```bash
# Start all Docker services
docker-compose up -d

# Run all tests
docker-compose exec coherence-api pytest

# Run specific test categories
docker-compose exec coherence-api pytest tests/unit/
docker-compose exec coherence-api pytest tests/integration/
docker-compose exec coherence-api pytest tests/functional/

# Run a specific test file
docker-compose exec coherence-api pytest tests/unit/test_intent_pipeline.py -v

# Run a specific test
docker-compose exec coherence-api pytest tests/unit/test_intent_pipeline.py::TestIntentResolver::test_resolve_tier1_success -v
```

### Without Docker

To run tests without Docker, ensure you have all dependencies installed and configured:

```bash
# Install dependencies
poetry install

# Run tests
poetry run pytest
```

## Setting Up Test Environment

For integration tests and some unit tests, you'll need to set environment variables:

```bash
# Set OpenAI API key for LLM-dependent tests
export OPENAI_API_KEY=your_key_here

# Run the tests
pytest tests/integration/
```

## Interactive Testing

For interactive testing of the intent pipeline, use the provided script:

```bash
# In Docker
docker-compose exec coherence-api python -m scripts.run_intent_test

# Without Docker
python -m scripts.run_intent_test
```

## Test Scripts

In addition to the formal tests, there are several test scripts in the `scripts/` directory:

- `test_intent_resolution.py`: Quick test of intent resolution pipeline
- `run_intent_test.py`: Interactive CLI for testing intent resolution
- `create_tenant.py`: Create a new tenant for testing

## Test Data

Test fixtures and mock data are defined in:

- `tests/conftest.py`: Shared fixtures for all tests
- Individual test files: Test-specific fixtures

## Mocking

Most external dependencies are mocked in unit tests to ensure isolation and predictability:

- LLM providers (OpenAI, etc.)
- Vector database (Qdrant)
- Redis cache
- Database sessions

Integration tests use real services from the Docker environment but may mock external APIs.