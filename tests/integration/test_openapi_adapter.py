"""
Integration tests for OpenAPI adapter functionality.
"""


import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.models.integration import APIEndpoint, APIIntegration
from src.coherence.models.tenant import Tenant
from src.coherence.openapi_adapter.adapter import OpenAPIAdapter


@pytest.fixture
def petstore_openapi_spec():
    """Sample OpenAPI specification for testing."""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Swagger Petstore",
            "description": "A sample API that uses a petstore as an example to demonstrate features in the OpenAPI 3.0 specification",
            "version": "1.0.0"
        },
        "servers": [
            {
                "url": "https://petstore.swagger.io/v1"
            }
        ],
        "paths": {
            "/pets": {
                "get": {
                    "summary": "List all pets",
                    "operationId": "listPets",
                    "parameters": [
                        {
                            "name": "limit",
                            "in": "query",
                            "description": "How many items to return at one time (max 100)",
                            "required": False,
                            "schema": {
                                "type": "integer",
                                "format": "int32"
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "A paged array of pets",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "id": {
                                                    "type": "integer"
                                                },
                                                "name": {
                                                    "type": "string"
                                                },
                                                "tag": {
                                                    "type": "string"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                "post": {
                    "summary": "Create a pet",
                    "operationId": "createPets",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "required": ["name"],
                                    "properties": {
                                        "name": {
                                            "type": "string"
                                        },
                                        "tag": {
                                            "type": "string"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "201": {
                            "description": "Null response"
                        }
                    }
                }
            },
            "/pets/{petId}": {
                "get": {
                    "summary": "Info for a specific pet",
                    "operationId": "showPetById",
                    "parameters": [
                        {
                            "name": "petId",
                            "in": "path",
                            "required": True,
                            "description": "The id of the pet to retrieve",
                            "schema": {
                                "type": "string"
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Expected response to a valid request",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "id": {
                                                "type": "integer"
                                            },
                                            "name": {
                                                "type": "string"
                                            },
                                            "tag": {
                                                "type": "string"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "components": {
            "securitySchemes": {
                "apiKey": {
                    "type": "apiKey",
                    "name": "api_key",
                    "in": "header"
                }
            }
        }
    }


@pytest.fixture
async def test_integration(async_db_session: AsyncSession, test_tenant: Tenant):
    """Create a test integration for OpenAPI adapter tests."""
    integration = APIIntegration(
        tenant_id=test_tenant.id,
        name="Test Petstore API",
        version="1.0.0",
        openapi_spec={},  # Will be populated in tests
        base_url="https://petstore.swagger.io/v1",
        status="draft",
    )
    async_db_session.add(integration)
    await async_db_session.flush()
    
    return integration


@pytest.mark.asyncio
async def test_import_openapi_spec(
    client: AsyncClient,
    test_tenant: Tenant,
    petstore_openapi_spec: dict,
    api_key_header: dict,
):
    """Test importing an OpenAPI specification."""
    # Make request to import spec
    response = await client.post(
        "/v1/admin/integrations/import",
        json={
            "name": "Petstore API",
            "spec": petstore_openapi_spec,
        },
        headers=api_key_header,
    )
    
    # Validate response
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "Petstore API"
    assert data["version"] == "1.0.0"
    assert data["base_url"] == "https://petstore.swagger.io/v1"
    assert len(data["endpoints"]) == 3  # Three operations in the spec
    assert len(data["auth_methods"]) == 1  # API key auth in the spec
    
    # Verify operations are extracted correctly
    operations = [e["path"] + " " + e["method"] for e in data["endpoints"]]
    assert "/pets GET" in operations
    assert "/pets POST" in operations
    assert "/pets/{petId} GET" in operations
    
    
@pytest.mark.asyncio
async def test_list_integrations(
    client: AsyncClient,
    test_tenant: Tenant,
    test_integration: APIIntegration,
    async_db_session: AsyncSession,
    api_key_header: dict,
):
    """Test listing API integrations."""
    # Insert a second integration
    integration2 = APIIntegration(
        tenant_id=test_tenant.id,
        name="Second API",
        version="2.0.0",
        openapi_spec={},
        base_url="https://example.com/api",
        status="active",
    )
    async_db_session.add(integration2)
    await async_db_session.commit()
    
    # Make request to list integrations
    response = await client.get(
        "/v1/admin/integrations",
        headers=api_key_header,
    )
    
    # Validate response
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    
    # Verify all integrations are returned
    integration_names = [i["name"] for i in data]
    assert "Test Petstore API" in integration_names
    assert "Second API" in integration_names
    
    
@pytest.mark.asyncio
async def test_get_integration_details(
    client: AsyncClient,
    test_tenant: Tenant,
    test_integration: APIIntegration,
    async_db_session: AsyncSession,
    api_key_header: dict,
):
    """Test getting details of a specific integration."""
    # Add an endpoint to the integration
    endpoint = APIEndpoint(
        integration_id=test_integration.id,
        path="/pets",
        method="GET",
        operation_id="listPets",
        enabled=True,
    )
    async_db_session.add(endpoint)
    await async_db_session.commit()
    
    # Make request to get integration details
    response = await client.get(
        f"/v1/admin/integrations/{test_integration.id}",
        headers=api_key_header,
    )
    
    # Validate response
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == str(test_integration.id)
    assert data["name"] == "Test Petstore API"
    assert len(data["endpoints"]) == 1
    assert data["endpoints"][0]["path"] == "/pets"
    assert data["endpoints"][0]["method"] == "GET"
    assert data["endpoints"][0]["operation_id"] == "listPets"
    
    
@pytest.mark.asyncio
async def test_generate_actions(
    client: AsyncClient,
    test_tenant: Tenant,
    test_integration: APIIntegration,
    async_db_session: AsyncSession,
    petstore_openapi_spec: dict,
    api_key_header: dict,
):
    """Test generating actions for an API integration."""
    # Update integration with OpenAPI spec
    test_integration.openapi_spec = petstore_openapi_spec
    await async_db_session.commit()
    
    # First import the endpoints
    adapter = OpenAPIAdapter(async_db_session)
    await adapter._extract_endpoints(test_integration.id, petstore_openapi_spec)
    await async_db_session.commit()
    
    # Make request to generate actions
    response = await client.post(
        "/v1/admin/integrations/actions/generate",
        json={"integration_id": str(test_integration.id)},
        headers=api_key_header,
    )
    
    # Validate response
    assert response.status_code == 200
    data = response.json()
    assert "actions" in data
    assert len(data["actions"]) == 3  # Three operations in the spec
    
    # Verify action classes are generated with correct names
    class_names = [action["class_name"] for action in data["actions"]]
    assert "ListPetsAction" in class_names
    assert "CreatePetsAction" in class_names
    assert "ShowPetByIdAction" in class_names
    
    # Verify parameters are extracted correctly
    for action in data["actions"]:
        if action["class_name"] == "ListPetsAction":
            assert len(action["parameters"]) == 1
            assert action["parameters"][0]["name"] == "limit"
            assert action["parameters"][0]["in"] == "query"
            assert action["parameters"][0]["required"] is False
        elif action["class_name"] == "CreatePetsAction":
            body_params = [p for p in action["parameters"] if p["in"] == "body"]
            assert len(body_params) == 1
        elif action["class_name"] == "ShowPetByIdAction":
            assert len(action["parameters"]) == 1
            assert action["parameters"][0]["name"] == "petId"
            assert action["parameters"][0]["in"] == "path"
            assert action["parameters"][0]["required"] is True
    
    
@pytest.mark.asyncio
async def test_generate_intents(
    client: AsyncClient,
    test_tenant: Tenant,
    test_integration: APIIntegration,
    async_db_session: AsyncSession,
    petstore_openapi_spec: dict,
    api_key_header: dict,
):
    """Test generating intents for an API integration."""
    # Update integration with OpenAPI spec
    test_integration.openapi_spec = petstore_openapi_spec
    await async_db_session.commit()
    
    # First import the endpoints
    adapter = OpenAPIAdapter(async_db_session)
    await adapter._extract_endpoints(test_integration.id, petstore_openapi_spec)
    
    # Add action class names to endpoints
    result = await async_db_session.execute(
        "SELECT id, operation_id, path, method FROM api_endpoints WHERE integration_id = :integration_id",
        {"integration_id": test_integration.id},
    )
    endpoints = result.fetchall()
    
    for endpoint in endpoints:
        class_name = ""
        if endpoint.operation_id == "listPets":
            class_name = "ListPetsAction"
        elif endpoint.operation_id == "createPets":
            class_name = "CreatePetsAction"
        elif endpoint.operation_id == "showPetById":
            class_name = "ShowPetByIdAction"
            
        await async_db_session.execute(
            "UPDATE api_endpoints SET action_class_name = :class_name WHERE id = :id",
            {"class_name": class_name, "id": endpoint.id},
        )
    
    await async_db_session.commit()
    
    # Make request to generate intents
    response = await client.post(
        "/v1/admin/integrations/intents/generate",
        json={"integration_id": str(test_integration.id)},
        headers=api_key_header,
    )
    
    # Validate response
    assert response.status_code == 200
    data = response.json()
    assert "intents" in data
    assert data["intent_count"] == 3  # Three operations in the spec
    
    # Verify intents are generated correctly
    intent_names = [intent["name"] for intent in data["intents"]]
    assert any("list_pets" in name for name in intent_names)
    assert any("create_pets" in name for name in intent_names)
    assert any("show_pet_by_id" in name for name in intent_names)
    
    # Verify parameters are extracted correctly
    for intent in data["intents"]:
        if "list_pets" in intent["name"]:
            assert "limit" in intent["parameters"]
            assert intent["parameters"]["limit"]["type"] == "integer"
            assert intent["parameters"]["limit"]["required"] is False
        elif "create_pets" in intent["name"]:
            assert "name" in intent["parameters"]
            assert intent["parameters"]["name"]["required"] is True
        elif "show_pet_by_id" in intent["name"]:
            assert "petId" in intent["parameters"]
            assert intent["parameters"]["petId"]["type"] == "string"
            assert intent["parameters"]["petId"]["required"] is True
    
    # Verify examples are generated
    for intent in data["intents"]:
        assert "examples" in intent
        assert len(intent["examples"]) > 0
    
    
@pytest.mark.asyncio
async def test_delete_integration(
    client: AsyncClient,
    test_tenant: Tenant,
    test_integration: APIIntegration,
    async_db_session: AsyncSession,
    api_key_header: dict,
):
    """Test deleting an API integration."""
    # Make request to delete integration
    response = await client.delete(
        f"/v1/admin/integrations/{test_integration.id}",
        headers=api_key_header,
    )
    
    # Validate response
    assert response.status_code == 204
    
    # Verify integration is deleted
    result = await async_db_session.execute(
        "SELECT COUNT(*) FROM api_integrations WHERE id = :id",
        {"id": test_integration.id},
    )
    count = result.scalar()
    assert count == 0