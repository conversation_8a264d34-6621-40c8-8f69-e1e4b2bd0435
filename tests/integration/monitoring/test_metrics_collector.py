"""
Integration tests for the metrics collector.

This module contains integration tests for the TenantMetricsCollector
to ensure that metrics are properly recorded and can be collected.
"""

from unittest.mock import MagicMock, patch

import pytest
from starlette.requests import Request

from src.coherence.monitoring.metrics_collector import (
    DEFAULT_TENANT_ID,
    TenantMetricsCollector,
)


@pytest.fixture
def mock_request():
    """Create a mock FastAPI request with tenant_id in state."""
    request = MagicMock(spec=Request)
    request.state.tenant_id = "test-tenant-123"
    return request


@pytest.fixture
def metrics_collector():
    """Create a TenantMetricsCollector instance."""
    return TenantMetricsCollector(tenant_id="test-tenant-123")


class TestTenantMetricsCollector:
    """Tests for the TenantMetricsCollector class."""

    def test_init_with_tenant_id(self):
        """Test initialization with a tenant ID."""
        collector = TenantMetricsCollector(tenant_id="test-tenant-123")
        assert collector.tenant_id == "test-tenant-123"

    def test_init_with_default_tenant_id(self):
        """Test initialization without a tenant ID uses the default."""
        collector = TenantMetricsCollector()
        assert collector.tenant_id == DEFAULT_TENANT_ID

    def test_from_request(self, mock_request):
        """Test creation from a request with tenant ID in state."""
        collector = TenantMetricsCollector.from_request(mock_request)
        assert collector.tenant_id == "test-tenant-123"

    def test_from_request_without_tenant_id(self):
        """Test creation from a request without tenant ID in state."""
        request = MagicMock(spec=Request)
        request.state = object()  # Ensure state doesn't have tenant_id
        collector = TenantMetricsCollector.from_request(request)
        assert collector.tenant_id == DEFAULT_TENANT_ID

    @patch("src.coherence.monitoring.metrics_collector.INTENT_RESOLUTION_COUNT")
    @patch("src.coherence.monitoring.metrics_collector.INTENT_RESOLUTION_LATENCY")
    @patch("src.coherence.monitoring.metrics_collector.INTENT_CONFIDENCE")
    def test_record_intent_resolution(
        self, mock_confidence, mock_latency, mock_count, metrics_collector
    ):
        """Test recording intent resolution metrics."""
        # Mock the Counter and Histogram objects
        mock_counter = MagicMock()
        mock_histogram = MagicMock()
        mock_count.labels.return_value = mock_counter
        mock_latency.labels.return_value = mock_histogram
        mock_confidence.labels.return_value = mock_histogram

        # Record intent resolution
        metrics_collector.record_intent_resolution(
            latency=0.15,
            result="success",
            tier="vector",
            confidence=0.92,
        )

        # Verify that the metrics were recorded
        mock_count.labels.assert_called_once_with(
            tenant_id="test-tenant-123", result="success"
        )
        mock_counter.inc.assert_called_once()

        mock_latency.labels.assert_called_once_with(tier="vector", result="success")
        mock_histogram.observe.assert_called()

        mock_confidence.labels.assert_called_once_with(tier="vector")
        mock_histogram.observe.assert_called()

    @patch("src.coherence.monitoring.metrics_collector.PARAMETER_EXTRACTION_LATENCY")
    @patch("src.coherence.monitoring.metrics_collector.PARAMETER_EXTRACTION_SUCCESS")
    def test_record_parameter_extraction_success(
        self, mock_success, mock_latency, metrics_collector
    ):
        """Test recording successful parameter extraction metrics."""
        # Mock the Counter and Histogram objects
        mock_counter = MagicMock()
        mock_histogram = MagicMock()
        mock_success.labels.return_value = mock_counter
        mock_latency.labels.return_value = mock_histogram

        # Record parameter extraction
        metrics_collector.record_parameter_extraction(
            latency=0.05,
            method="pattern",
            success=True,
            param_type="date",
        )

        # Verify that the metrics were recorded
        mock_latency.labels.assert_called_once_with(method="pattern")
        mock_histogram.observe.assert_called_once_with(0.05)

        mock_success.labels.assert_called_once_with(
            param_type="date", method="pattern"
        )
        mock_counter.inc.assert_called_once()

    @patch("src.coherence.monitoring.metrics_collector.PARAMETER_EXTRACTION_LATENCY")
    @patch("src.coherence.monitoring.metrics_collector.PARAMETER_EXTRACTION_FAILURE")
    def test_record_parameter_extraction_failure(
        self, mock_failure, mock_latency, metrics_collector
    ):
        """Test recording failed parameter extraction metrics."""
        # Mock the Counter and Histogram objects
        mock_counter = MagicMock()
        mock_histogram = MagicMock()
        mock_failure.labels.return_value = mock_counter
        mock_latency.labels.return_value = mock_histogram

        # Record parameter extraction
        metrics_collector.record_parameter_extraction(
            latency=0.05,
            method="pattern",
            success=False,
            param_type="date",
            failure_reason="invalid_format",
        )

        # Verify that the metrics were recorded
        mock_latency.labels.assert_called_once_with(method="pattern")
        mock_histogram.observe.assert_called_once_with(0.05)

        mock_failure.labels.assert_called_once_with(
            param_type="date", method="pattern", reason="invalid_format"
        )
        mock_counter.inc.assert_called_once()

    @patch("src.coherence.monitoring.metrics_collector.ACTION_EXECUTION_COUNT")
    @patch("src.coherence.monitoring.metrics_collector.ACTION_EXECUTION_LATENCY")
    def test_record_action_execution(
        self, mock_latency, mock_count, metrics_collector
    ):
        """Test recording action execution metrics."""
        # Mock the Counter and Histogram objects
        mock_counter = MagicMock()
        mock_histogram = MagicMock()
        mock_count.labels.return_value = mock_counter
        mock_latency.labels.return_value = mock_histogram

        # Record action execution
        metrics_collector.record_action_execution(
            latency=0.2,
            intent="get_weather",
            result="success",
        )

        # Verify that the metrics were recorded
        mock_count.labels.assert_called_once_with(
            intent="get_weather", result="success"
        )
        mock_counter.inc.assert_called_once()

        mock_latency.labels.assert_called_once_with(
            intent="get_weather", result="success"
        )
        mock_histogram.observe.assert_called_once_with(0.2)

    @patch("src.coherence.monitoring.metrics_collector.CONVERSATION_TURNS")
    @patch("src.coherence.monitoring.metrics_collector.CONVERSATION_DURATION")
    def test_record_conversation_metrics(
        self, mock_duration, mock_turns, metrics_collector
    ):
        """Test recording conversation metrics."""
        # Mock the Histogram objects
        mock_turns_histogram = MagicMock()
        mock_duration_histogram = MagicMock()
        mock_turns.labels.return_value = mock_turns_histogram
        mock_duration.labels.return_value = mock_duration_histogram

        # Record conversation metrics
        metrics_collector.record_conversation_metrics(turns=5, duration=120)

        # Verify that the metrics were recorded
        mock_turns.labels.assert_called_once_with(tenant_id="test-tenant-123")
        mock_turns_histogram.observe.assert_called_once_with(5)

        mock_duration.labels.assert_called_once_with(tenant_id="test-tenant-123")
        mock_duration_histogram.observe.assert_called_once_with(120)

    @patch("src.coherence.monitoring.metrics_collector.ACTIVE_CONVERSATIONS")
    def test_update_active_conversations(self, mock_gauge, metrics_collector):
        """Test updating active conversations count."""
        # Mock the Gauge object
        mock_gauge_instance = MagicMock()
        mock_gauge.labels.return_value = mock_gauge_instance

        # Update active conversations
        metrics_collector.update_active_conversations(count=10)

        # Verify that the metric was updated
        mock_gauge.labels.assert_called_once_with(tenant_id="test-tenant-123")
        mock_gauge_instance.set.assert_called_once_with(10)

    @patch("src.coherence.monitoring.metrics_collector.LLM_CALL_LATENCY")
    @patch("src.coherence.monitoring.metrics_collector.LLM_TOKEN_USAGE")
    def test_record_llm_usage(
        self, mock_token_usage, mock_latency, metrics_collector
    ):
        """Test recording LLM usage metrics."""
        # Mock the Counter and Histogram objects
        mock_counter = MagicMock()
        mock_histogram = MagicMock()
        mock_token_usage.labels.return_value = mock_counter
        mock_latency.labels.return_value = mock_histogram

        # Record LLM usage
        metrics_collector.record_llm_usage(
            latency=0.5,
            provider="openai",
            model="gpt-4o",
            operation="completion",
            token_count=150,
        )

        # Verify that the metrics were recorded
        mock_latency.labels.assert_called_once_with(
            provider="openai", model="gpt-4o", operation="completion"
        )
        mock_histogram.observe.assert_called_once_with(0.5)

        mock_token_usage.labels.assert_called_once_with(
            tenant_id="test-tenant-123",
            provider="openai",
            model="gpt-4o",
            operation="completion",
        )
        mock_counter.inc.assert_called_once_with(150)

    @patch("src.coherence.monitoring.metrics_collector.ERROR_BY_TYPE")
    @patch("src.coherence.monitoring.metrics_collector.ERROR_BY_ENDPOINT")
    @patch("src.coherence.monitoring.metrics_collector.ERROR_RATE")
    @patch("src.coherence.monitoring.metrics_collector.ERROR_COUNT")
    def test_record_error(
        self, mock_count, mock_rate, mock_by_endpoint, mock_by_type, metrics_collector
    ):
        """Test recording error metrics."""
        # Mock the Counter and Summary objects
        mock_error_count_counter = MagicMock()
        mock_error_rate_summary = MagicMock()
        mock_by_endpoint_counter = MagicMock()
        mock_by_type_counter = MagicMock()

        mock_count.labels.return_value = mock_error_count_counter
        mock_rate.labels.return_value = mock_error_rate_summary
        mock_by_endpoint.labels.return_value = mock_by_endpoint_counter
        mock_by_type.labels.return_value = mock_by_type_counter

        # Record error
        metrics_collector.record_error(
            error_type="validation_error",
            component="parameter_extraction",
            endpoint="/v1/resolve",
            status_code=400,
            error_code="coherence.validation_error",
        )

        # Verify that the metrics were recorded
        mock_count.labels.assert_called_once_with(
            component="parameter_extraction", error_type="validation_error", tenant_id="test-tenant-123"
        )
        mock_error_count_counter.inc.assert_called_once()

        mock_rate.labels.assert_called_once_with(component="parameter_extraction", tenant_id="test-tenant-123")
        mock_error_rate_summary.observe.assert_called_once_with(1.0)

        mock_by_endpoint.labels.assert_called_once_with(
            endpoint="/v1/resolve", method="resolve", status_code=400, tenant_id="test-tenant-123"
        )
        mock_by_endpoint_counter.inc.assert_called_once()

        mock_by_type.labels.assert_called_once_with(
            error_code="coherence.validation_error", tenant_id="test-tenant-123"
        )
        mock_by_type_counter.inc.assert_called_once()