"""
Integration tests for system admin API key bypass functionality.
"""

from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient

from src.coherence.main import app


@pytest.fixture
def test_client():
    return TestClient(app)


@pytest.mark.asyncio
@patch("src.coherence.api.v1.dependencies.auth.get_clerk_auth_details")
async def test_system_admin_bypass_for_api_key(mock_get_clerk_auth, test_client):
    """
    Test that system admins can access the /resolve endpoint without an API key.
    """
    # Mock a system admin user
    mock_auth = MagicMock()
    mock_auth.user_id = "user_123"
    mock_auth.org_id = "org_123"
    mock_auth.is_system_admin = True
    mock_get_clerk_auth.return_value = mock_auth
    
    # Create test request data
    test_data = {
        "message": "Hello, world!",
        "user_id": "user_123",  # String instead of UUID
        "role": "user",
        "context": {"message_history": []}
    }
    
    # Set headers with only auth token (no API key)
    headers = {
        "Authorization": "Bearer mock_token",
        "Content-Type": "application/json",
        "X-Tenant-ID": "org_123"  # Add tenant ID header
    }
    
    # Mock the is_system_admin function to return True and mock DB dependencies
    with patch("src.coherence.api.v1.endpoints.resolve.is_system_admin", return_value=True), \
         patch("src.coherence.api.v1.endpoints.resolve.select") as mock_select, \
         patch("src.coherence.intent_pipeline.orchestrator.ChatOrchestrator.handle_message") as mock_handle:
        
        # Configure mock_select to handle the Tenant lookup
        mock_tenant = MagicMock()
        mock_tenant.id = "tenant_123"
        mock_tenant.clerk_org_id = "org_123"
        
        # Configure select results for tenant lookup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_tenant
        mock_execute = MagicMock()
        mock_execute.return_value = mock_result
        mock_select.return_value = MagicMock()
        
        # Mock orchestrator response
        mock_handle.return_value = {
            "type": "action",
            "outcome": "Success"
        }
        
        response = test_client.post("/v1/resolve", json=test_data, headers=headers)
    
    # Should return either 200 or 500 (if orchestrator fails), but not 401
    assert response.status_code != 401, "System admin got 401 Unauthorized but should bypass API key requirement"
    
    # Check the error message doesn't mention API key if there's an error
    if response.status_code != 200:
        error_message = response.json().get("detail", "")
        assert "API key required" not in error_message, "Error message should not mention API key for system admin"


@pytest.mark.asyncio
@patch("src.coherence.api.v1.dependencies.auth.get_clerk_auth_details")
async def test_non_admin_requires_api_key(mock_get_clerk_auth, test_client):
    """
    Test that non-admin users still require an API key to access the /resolve endpoint.
    """
    # Mock a regular user
    mock_auth = MagicMock()
    mock_auth.user_id = "user_456"
    mock_auth.org_id = "org_456"
    mock_auth.is_system_admin = False
    mock_get_clerk_auth.return_value = mock_auth
    
    # Create test request data
    test_data = {
        "message": "Hello, world!",
        "user_id": "user_456",
        "role": "user",
        "context": {"message_history": []}
    }
    
    # Set headers with only auth token (no API key)
    headers = {
        "Authorization": "Bearer mock_token",
        "Content-Type": "application/json",
        "X-Tenant-ID": "org_456"  # Add tenant ID header
    }
    
    # Make request without API key
    with patch("src.coherence.api.v1.endpoints.resolve.is_system_admin", return_value=False):
        response = test_client.post("/v1/resolve", json=test_data, headers=headers)
    
    # Should return 401 Unauthorized
    assert response.status_code == 401, "Non-admin user should get 401 without API key"
    assert response.json().get("detail") == "API key required", "Error should mention API key requirement"