"""
Integration tests for tenant header and context processing.
"""
import uuid

import pytest
from fastapi import Depends, HTTPException, Request, status
from fastapi.responses import JSONResponse
from httpx import AsyncClient

from src.coherence.main import app as main_app  # Import the main app
from src.coherence.middleware.tenant_context import (
    current_tenant_id_var,
)


# A simple dependency that checks for tenant_id in request.state
async def require_tenant_id(request: Request):
    if not getattr(request.state, "tenant_id", None):
        # This error would be raised if TenantContextMiddleware ran
        # but didn't find a tenant_id, and the endpoint requires one.
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-Tenant-ID header is missing or invalid, and no tenant context found.",
        )
    return request.state.tenant_id

# A protected endpoint for testing
@main_app.get("/test-protected-tenant", tags=["Test"])
async def get_protected_tenant_resource(
    request: Request, tenant_id: uuid.UUID = Depends(require_tenant_id)
):
    # Also check the context var for good measure, though require_tenant_id checks request.state
    context_var_tenant_id = current_tenant_id_var.get()
    return JSONResponse(
        content={
            "message": "Accessed protected resource",
            "tenant_id_from_dependency": str(tenant_id),
            "tenant_id_from_contextvar": context_var_tenant_id,
            "request_state_tenant_id": getattr(request.state, "tenant_id", None),
            "request_state_rls_tenant_id": getattr(request.state, "rls_tenant_id", None),
        }
    )

@pytest.mark.asyncio
async def test_missing_tenant_id_header(client: AsyncClient):
    """
    Test that a 400 is returned when X-Tenant-ID header is missing
    for an endpoint that requires tenant context.
    """
    response = await client.get("/test-protected-tenant")
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert "X-Tenant-ID header is missing" in response.json()["detail"]

@pytest.mark.asyncio
async def test_invalid_tenant_id_header_format(client: AsyncClient):
    """
    Test that a 400 is returned by the middleware when X-Tenant-ID header has an invalid format.
    """
    response = await client.get(
        "/test-protected-tenant", headers={"X-Tenant-ID": "not-a-uuid"}
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    # This message comes directly from TenantContextMiddleware
    assert response.json()["detail"] == "Invalid X-Tenant-ID header format."

@pytest.mark.asyncio
async def test_valid_tenant_id_header(client: AsyncClient):
    """
    Test that a 200 is returned when a valid X-Tenant-ID header is provided.
    """
    valid_tenant_id = str(uuid.uuid4())
    response = await client.get(
        "/test-protected-tenant", headers={"X-Tenant-ID": valid_tenant_id}
    )
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["message"] == "Accessed protected resource"
    assert data["tenant_id_from_dependency"] == valid_tenant_id
    assert data["tenant_id_from_contextvar"] == valid_tenant_id
    assert data["request_state_tenant_id"] == valid_tenant_id
    assert data["request_state_rls_tenant_id"] == valid_tenant_id

# Note: The client fixture from tests/conftest.py uses the main_app,
# so the new route /test-protected-tenant will be available.
# The TenantContextMiddleware is already part of the main_app.