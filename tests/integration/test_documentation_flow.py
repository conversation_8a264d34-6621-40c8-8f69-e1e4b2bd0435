"""
Integration tests for the complete documentation flow.

Tests the end-to-end flow from user request for documentation
to the final rendered response.
"""

import uuid
import json
from unittest.mock import AsyncMock, Mock, patch
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.intent_pipeline.orchestrator import ChatOrchestrator
from src.coherence.intent_pipeline.resolver import IntentResolver, TemplateClassification
from src.coherence.intent_pipeline.parameter_extraction import ParameterExtractor
from src.coherence.intent_pipeline.schemas.intent import (
    IntentDefinition,
    IntentResolution,
    ConversationContext,
    ParameterDefinition,
    ParameterType,
)
from src.coherence.models.template import Template, TemplateCategory
from src.coherence.models.executed_action import ExecutedAction, ActionType, ParameterCollectionState
from src.coherence.template_system.services.template_service import TemplateService
from src.coherence.core.redis_client import RedisClient


class TestDocumentationFlow:
    """Test the complete documentation flow integration."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.add = Mock()
        session.commit = AsyncMock()
        return session

    @pytest.fixture
    def mock_redis_client(self):
        """Mock Redis client."""
        client = AsyncMock(spec=RedisClient)
        client.get = AsyncMock(return_value=None)
        client.set = AsyncMock()
        return client

    @pytest.fixture
    def mock_intent_resolver(self):
        """Mock intent resolver."""
        resolver = AsyncMock(spec=IntentResolver)
        return resolver

    @pytest.fixture
    def mock_parameter_extractor(self):
        """Mock parameter extractor."""
        extractor = AsyncMock(spec=ParameterExtractor)
        return extractor

    @pytest.fixture
    def mock_template_service(self):
        """Mock template service."""
        service = AsyncMock(spec=TemplateService)
        return service

    @pytest.fixture
    def chat_orchestrator(
        self,
        mock_db_session,
        mock_redis_client,
        mock_intent_resolver,
        mock_parameter_extractor,
        mock_template_service,
    ):
        """Create chat orchestrator with mocked dependencies."""
        orchestrator = ChatOrchestrator(
            db=mock_db_session,
            redis_client=mock_redis_client,
            intent_resolver=mock_intent_resolver,
            parameter_extractor=mock_parameter_extractor,
            llm_provider=AsyncMock(),
            template_service=mock_template_service,
            action_executor=None,
        )
        return orchestrator

    @pytest.fixture
    def documentation_intent(self):
        """Create documentation intent definition."""
        return IntentDefinition(
            name="show_documentation",
            description="Show API documentation for an endpoint",
            parameters={
                "endpoint": ParameterDefinition(
                    name="endpoint",
                    type=ParameterType.STRING,
                    required=True,
                    description="API endpoint path",
                    prompt="Which API endpoint documentation would you like to see?",
                ),
                "method": ParameterDefinition(
                    name="method",
                    type=ParameterType.STRING,
                    required=False,
                    description="HTTP method",
                    prompt="Which HTTP method (GET, POST, etc.)?",
                    options=["GET", "POST", "PUT", "DELETE", "PATCH"],
                ),
            },
            required_fields=["endpoint"],
        )

    @pytest.fixture
    def documentation_template(self):
        """Create documentation template."""
        return Template(
            id=uuid.uuid4(),
            key="doc_api_v1_users_{id}_get",
            name="User API Documentation",
            category=TemplateCategory.DOCUMENTATION,
            content="""# API Documentation: {{ documentation.endpoint }}

**Method**: {{ documentation.method }}

## Description
{{ documentation.description }}

## Parameters
{% for param in documentation.parameters %}
- **{{ param.name }}** ({{ param.type }}): {{ param.description }}{% if param.required %} *Required*{% endif %}
{% endfor %}

## Responses
{% for response in documentation.responses %}
### {{ response.status_code }}
{{ response.description }}
{% endfor %}

## Example
```json
{{ documentation.example | tojson(indent=2) }}
```""",
            tenant_id=uuid.uuid4(),
        )

    @pytest.mark.asyncio
    async def test_documentation_request_flow(
        self,
        chat_orchestrator,
        mock_intent_resolver,
        mock_parameter_extractor,
        mock_template_service,
        documentation_intent,
        documentation_template,
    ):
        """Test complete flow for documentation request."""
        # Setup test data
        tenant_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())
        
        # Mock intent resolution for documentation
        mock_intent_resolver.resolve_with_classification.return_value = (
            IntentResolution(
                intent="show_documentation",
                confidence=0.95,
                filled={},
                missing=["endpoint"],
                tier=1,
            ),
            TemplateClassification.DOCUMENTATION,
        )

        # Mock parameter extraction
        mock_parameter_extractor.extract_parameters.return_value = (
            {"endpoint": "/api/v1/users/{id}", "method": "GET"},
            [],  # No missing parameters
        )

        # Mock template retrieval
        mock_template_service.get_template.return_value = documentation_template
        
        # Mock template rendering
        mock_template_service.render_template.return_value = """# API Documentation: /api/v1/users/{id}

**Method**: GET

## Description
Retrieve user information by ID

## Parameters
- **id** (string): User ID *Required*

## Responses
### 200
Success

## Example
```json
{
  "id": "user_123",
  "name": "John Doe"
}
```"""

        # Execute the flow
        result = await chat_orchestrator.handle_message(
            tenant_id=tenant_id,
            user_id=user_id,
            user_role="user",
            message="Show me the documentation for the user endpoint",
            message_history=[],
            context={"conversation_id": conversation_id},
        )

        # Assert the result
        assert result["type"] == "reply"
        assert "API Documentation" in result["outcome"]
        assert "/api/v1/users/{id}" in result["outcome"]
        assert "GET" in result["outcome"]

        # Verify ExecutedAction was created
        mock_db_session.add.assert_called()
        executed_action = mock_db_session.add.call_args[0][0]
        assert isinstance(executed_action, ExecutedAction)
        assert executed_action.action_type == ActionType.DOCUMENTATION
        assert executed_action.is_documentation_request is True
        assert executed_action.documentation_endpoint == "/api/v1/users/{id}"
        assert executed_action.documentation_method == "GET"

    @pytest.mark.asyncio
    async def test_documentation_with_parameter_collection(
        self,
        chat_orchestrator,
        mock_intent_resolver,
        mock_parameter_extractor,
        mock_redis_client,
        documentation_intent,
    ):
        """Test documentation flow with parameter collection."""
        # Setup
        tenant_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        # Mock intent resolution
        mock_intent_resolver.resolve_with_classification.return_value = (
            IntentResolution(
                intent="show_documentation",
                confidence=0.92,
                filled={},
                missing=["endpoint"],
                tier=1,
            ),
            TemplateClassification.DOCUMENTATION,
        )

        # Mock parameter extraction - missing endpoint
        mock_parameter_extractor.extract_parameters.return_value = (
            {},  # No parameters extracted
            ["endpoint"],  # Missing endpoint
        )

        # Mock parameter prompt generation
        mock_parameter_extractor.generate_parameter_prompt.return_value = (
            "Which API endpoint documentation would you like to see?"
        )

        # Mock conversation context storage
        mock_redis_client.get.return_value = None  # No existing context

        # Execute first message
        result = await chat_orchestrator.handle_message(
            tenant_id=tenant_id,
            user_id=user_id,
            user_role="user",
            message="I need API documentation",
            message_history=[],
            context={"conversation_id": conversation_id},
        )

        # Assert parameter collection response
        assert result["type"] == "ask"
        assert result["missing_field"] == "endpoint"
        assert "endpoint" in result["question"].lower()

        # Verify conversation context was saved
        mock_redis_client.set.assert_called()
        saved_context = json.loads(mock_redis_client.set.call_args[0][1])
        assert saved_context["current_intent"] == "show_documentation"
        assert saved_context["missing_parameters"] == ["endpoint"]

    @pytest.mark.asyncio
    async def test_documentation_error_handling(
        self,
        chat_orchestrator,
        mock_intent_resolver,
        mock_parameter_extractor,
        mock_template_service,
        documentation_intent,
    ):
        """Test error handling in documentation flow."""
        # Setup
        tenant_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        # Mock successful intent resolution
        mock_intent_resolver.resolve_with_classification.return_value = (
            IntentResolution(
                intent="show_documentation",
                confidence=0.95,
                filled={"endpoint": "/api/v1/unknown"},
                missing=[],
                tier=1,
            ),
            TemplateClassification.DOCUMENTATION,
        )

        # Mock successful parameter extraction
        mock_parameter_extractor.extract_parameters.return_value = (
            {"endpoint": "/api/v1/unknown"},
            [],
        )

        # Mock template not found
        mock_template_service.get_template.return_value = None

        # Execute the flow
        result = await chat_orchestrator.handle_message(
            tenant_id=tenant_id,
            user_id=user_id,
            user_role="user",
            message="Show documentation for /api/v1/unknown",
            message_history=[],
            context={"conversation_id": conversation_id},
        )

        # Assert error response
        assert result["type"] == "reply"
        assert "couldn't find documentation" in result["outcome"].lower()

        # Verify ExecutedAction was created with error
        mock_db_session.add.assert_called()
        executed_action = mock_db_session.add.call_args[0][0]
        assert executed_action.action_type == ActionType.DOCUMENTATION
        assert executed_action.parameter_state == ParameterCollectionState.FAILED

    @pytest.mark.asyncio
    async def test_documentation_template_rendering_error(
        self,
        chat_orchestrator,
        mock_intent_resolver,
        mock_parameter_extractor,
        mock_template_service,
        documentation_intent,
        documentation_template,
    ):
        """Test handling of template rendering errors."""
        # Setup
        tenant_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        # Mock successful intent resolution
        mock_intent_resolver.resolve_with_classification.return_value = (
            IntentResolution(
                intent="show_documentation",
                confidence=0.95,
                filled={"endpoint": "/api/v1/users/{id}"},
                missing=[],
                tier=1,
            ),
            TemplateClassification.DOCUMENTATION,
        )

        # Mock successful parameter extraction
        mock_parameter_extractor.extract_parameters.return_value = (
            {"endpoint": "/api/v1/users/{id}", "method": "GET"},
            [],
        )

        # Mock template retrieval
        mock_template_service.get_template.return_value = documentation_template

        # Mock template rendering error
        mock_template_service.render_template.side_effect = Exception(
            "Template rendering error"
        )

        # Execute the flow
        result = await chat_orchestrator.handle_message(
            tenant_id=tenant_id,
            user_id=user_id,
            user_role="user",
            message="Show documentation for user endpoint",
            message_history=[],
            context={"conversation_id": conversation_id},
        )

        # Assert error response
        assert result["type"] == "error"
        assert "error" in result["outcome"].lower()

    @pytest.mark.asyncio
    async def test_mixed_intent_classification(
        self,
        chat_orchestrator,
        mock_intent_resolver,
        mock_parameter_extractor,
        documentation_intent,
    ):
        """Test handling of ambiguous intent classification."""
        # Setup
        tenant_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        # Mock unclear classification
        mock_intent_resolver.resolve_with_classification.return_value = (
            IntentResolution(
                intent="show_documentation",
                confidence=0.65,  # Low confidence
                filled={},
                missing=["endpoint"],
                tier=2,
                alternatives=[
                    {"intent": "create_user", "confidence": 0.60},
                    {"intent": "list_users", "confidence": 0.55},
                ],
            ),
            TemplateClassification.UNCLEAR,
        )

        # Execute the flow
        result = await chat_orchestrator.handle_message(
            tenant_id=tenant_id,
            user_id=user_id,
            user_role="user",
            message="user documentation",  # Ambiguous message
            message_history=[],
            context={"conversation_id": conversation_id},
        )

        # The orchestrator should still handle it as documentation
        # due to the intent name, even with UNCLEAR classification
        assert result is not None