# Standard library imports
import hashlib
import secrets

# Third-party imports
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

# Local application imports
from src.coherence.models.tenant import APIKey, Tenant


@pytest.fixture(scope="function")
async def test_tenant(async_db_session: AsyncSession):
    """Creates a Tenant instance and an associated API key for integration tests."""
    # Create a tenant instance
    tenant = Tenant(
        name="Test Tenant",
        # Add other necessary fields if the model requires them,
        # e.g., slug="test-tenant" if it has a unique constraint
    )
    async_db_session.add(tenant)
    await async_db_session.commit()
    await async_db_session.refresh(tenant)

    # Generate API key value and hash
    api_key_value = f"test_coh_{secrets.token_urlsafe(32)}" # Use a test prefix
    api_key_hash = hashlib.sha256(api_key_value.encode()).hexdigest()

    # Create an API key object for the database
    db_api_key = APIKey(
        tenant_id=tenant.id, 
        label="Test Key", # Use label instead of name
        key_hash=api_key_hash # Provide the hash
    ) 
    async_db_session.add(db_api_key)
    await async_db_session.commit()
    await async_db_session.refresh(db_api_key)

    # Yield tenant and the RAW api key value for use in headers
    yield tenant, api_key_value 

    # Clean up the tenant and api_key after the test
    # Explicitly delete the db_api_key object
    await async_db_session.delete(db_api_key) 
    await async_db_session.delete(tenant)
    await async_db_session.commit()


@pytest.fixture(scope="function")
async def api_key_header(test_tenant): # Depend on test_tenant
    """Provides the API key header for the test tenant."""
    _tenant, api_key_value = test_tenant # Unpack tenant and the raw key value
    return {"X-API-Key": api_key_value} # Use the raw key value
