"""
Integration tests for the intent resolution pipeline.

This test verifies that the intent pipeline works correctly with
actual service dependencies (minus external APIs).

To run this test:
    1. Start the Docker services: docker-compose up -d
    2. Run: docker-compose exec coherence-api pytest tests/integration/test_intent_resolution_integration.py -v
"""

# Standard library imports
import json
import os
import uuid
from unittest.mock import AsyncMock

# Third-party imports
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

# Application-specific imports
from src.coherence.core.config import settings
from src.coherence.core.llm.base import LL<PERSON>rovider, LLMResponse
from src.coherence.db.session import async_session
from src.coherence.intent_pipeline.orchestrator import get_chat_orchestrator
from src.coherence.intent_pipeline.schemas.intent import IntentDefinition
from src.coherence.intent_pipeline.template_integration import (
    TemplateBasedIntentResolver,
)
from src.coherence.template_system.services.template_service import TemplateService

# Skip tests if environment variables are not set
pytestmark = pytest.mark.skipif(
    "OPENAI_API_KEY" not in os.environ,
    reason="OPENAI_API_KEY environment variable not set",
)


@pytest.fixture
async def orchestrator():
    """Create a real chat orchestrator with actual dependencies."""
    # Ensure settings reflect environment variables for tests requiring real API calls
    if "OPENAI_API_KEY" in os.environ and settings.OPENAI_API_KEY is None:
        settings.OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")

    # Create a database session
    db = async_session()

    # Get chat orchestrator
    orchestrator_instance = await get_chat_orchestrator(
        db=db,
    )

    try:
        yield orchestrator_instance
    finally:
        # Clean up resources
        if (
            hasattr(orchestrator_instance, 'intent_resolver')
            and orchestrator_instance.intent_resolver is not None
            and hasattr(orchestrator_instance.intent_resolver, 'llm_provider')
            and orchestrator_instance.intent_resolver.llm_provider is not None
        ):
            await orchestrator_instance.intent_resolver.llm_provider.close()

        if (
            hasattr(orchestrator_instance, 'parameter_extractor')
            and orchestrator_instance.parameter_extractor is not None
            and hasattr(orchestrator_instance.parameter_extractor, 'llm_provider')
            and orchestrator_instance.parameter_extractor.llm_provider is not None
        ):
            await orchestrator_instance.parameter_extractor.llm_provider.close()

        if (
            hasattr(orchestrator_instance, 'intent_resolver')
            and orchestrator_instance.intent_resolver is not None
            and hasattr(orchestrator_instance.intent_resolver, 'qdrant_client')
            and orchestrator_instance.intent_resolver.qdrant_client is not None
            and hasattr(orchestrator_instance.intent_resolver.qdrant_client, 'client')
        ):
            qdrant_lib_client = orchestrator_instance.intent_resolver.qdrant_client.client
            if hasattr(qdrant_lib_client, 'aclose'):
                await qdrant_lib_client.aclose()
            elif hasattr(qdrant_lib_client, 'close'): # Fallback, though aclose is preferred for async
                qdrant_lib_client.close()

        if hasattr(orchestrator_instance, 'redis_client') and orchestrator_instance.redis_client is not None and hasattr(orchestrator_instance.redis_client, 'redis'):
            if hasattr(orchestrator_instance.redis_client.redis, 'aclose'): # For redis-py >= 4.2.0rc1
                await orchestrator_instance.redis_client.redis.aclose()
            elif hasattr(orchestrator_instance.redis_client.redis, 'close'): # For older versions or different close patterns
                orchestrator_instance.redis_client.redis.close()
                if hasattr(orchestrator_instance.redis_client.redis, 'wait_closed'): # Common pattern for older async close
                    await orchestrator_instance.redis_client.redis.wait_closed()

        if hasattr(orchestrator_instance, 'llm_provider') and orchestrator_instance.llm_provider is not None:
            await orchestrator_instance.llm_provider.close()
        await db.close()


@pytest.mark.asyncio
async def test_handle_message_weather_intent(orchestrator):
    """Test handling a weather-related message."""
    # Create test tenant and user IDs
    tenant_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())
    user_role = "user"

    # Process a message
    result = await orchestrator.handle_message(
        tenant_id=tenant_id,
        user_id=user_id,
        user_role=user_role,
        message="What's the weather like in San Francisco?",
    )

    # Verify we got some kind of response
    assert result is not None
    assert isinstance(result, dict)
    assert "type" in result

    # Log the result for debugging
    print(f"Result: {result}")

    # Types can vary based on mock configuration, but we should have either:
    # - 'action' with an outcome if the intent was fully resolved
    # - 'ask' with a question if parameters are missing
    # - 'reply' with fallback content if no intent matched
    assert result["type"] in ["action", "ask", "reply"]


@pytest.mark.asyncio
async def test_handle_message_health_metric(orchestrator):
    """Test handling a health metric message."""
    # Create test tenant and user IDs
    tenant_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())
    user_role = "user"

    # Process a message
    result = await orchestrator.handle_message(
        tenant_id=tenant_id,
        user_id=user_id,
        user_role=user_role,
        message="Log my weight as 165 pounds",
    )

    # Verify we got some kind of response
    assert result is not None
    assert isinstance(result, dict)
    assert "type" in result

    # Log the result for debugging
    print(f"Result: {result}")

    # Should be an action or ask for more information
    assert result["type"] in ["action", "ask", "reply"]


@pytest.mark.asyncio
async def test_conversation_continuity(orchestrator):
    """Test multi-turn conversation with parameter collection."""
    # Create test tenant and user IDs
    tenant_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())
    user_role = "user"
    conversation_id = str(uuid.uuid4())

    # First message with incomplete parameters
    first_result = await orchestrator.handle_message(
        tenant_id=tenant_id,
        user_id=user_id,
        user_role=user_role,
        message="I want to check the weather",
        context={"conversation_id": conversation_id},
    )

    # Log the result for debugging
    print(f"First result: {first_result}")

    # If we got an ask response, try to continue the conversation
    if first_result["type"] == "ask":
        # Provide the missing parameter
        second_result = await orchestrator.continue_conversation(
            tenant_id=tenant_id,
            conversation_id=conversation_id,
            user_id=user_id,
            message="San Francisco",
        )

        # Log the result for debugging
        print(f"Second result: {second_result}")

        # Verify we got a response
        assert second_result is not None
        assert isinstance(second_result, dict)
        assert "type" in second_result


@pytest.mark.asyncio
async def test_template_based_resolver_success_scenario(mocker):
    """Test TemplateBasedIntentResolver successfully resolves an intent using mocks."""
    # 1. Mock dependencies
    mock_db_session = AsyncMock(spec=AsyncSession)
    mock_template_service = AsyncMock(spec=TemplateService)
    mock_llm_provider = AsyncMock(spec=LLMProvider)

    # 2. Define test data
    user_id = "test_user_123"
    user_role = "user"
    user_message = "What's the weather like in London today?"
    intents_definitions = [
        IntentDefinition(name="get_weather", description="Provides current weather information", examples=["weather in city"], parameters={}),
        IntentDefinition(name="book_flight", description="Books a flight ticket", examples=["book flight to city"], parameters={}),
    ]

    # 2a. Mocked output from TemplateService.render_template
    # This structure is what _parse_template in TemplateBasedIntentResolver expects
    rendered_template_str = """
{% block system_instruction %}
System instructions: You are an intent classification AI.
Respond in JSON with 'intent', 'confidence', and 'parameters'.
{% endblock %}
{% block prompt %}
User prompt: Classify the following message.
User message: "What's the weather like in London today?"
Available intents: get_weather, book_flight
"""
    mock_template_service.render_template.return_value = rendered_template_str

    # 2b. Mocked output from LLMProvider.generate (content of LLMResponse)
    llm_response_json_str = json.dumps({
        "intent": "get_weather",
        "confidence": 0.92,
        "parameters": {"location": "London", "day": "today"}
    })
    mock_llm_provider.generate.return_value = LLMResponse(content=llm_response_json_str)

    # 3. Instantiate the resolver with mocks
    resolver = TemplateBasedIntentResolver(
        template_service=mock_template_service,
        llm_provider=mock_llm_provider
    )

    # 4. Call the method under test
    resolution, confidence = await resolver.resolve_intent_with_template(
        db=mock_db_session,
        user_id=user_id,
        user_role=user_role,
        message=user_message,
        intents=intents_definitions,
        # tenant_id and template_key use defaults
    )

    # 5. Assertions
    assert confidence == 0.92
    assert resolution is not None
    assert resolution.intent == "get_weather"
    assert resolution.confidence == 0.92
    assert resolution.filled == {"location": "London", "day": "today"}
    assert resolution.missing == [] # Parameter completion service would fill this

    # 5a. Assert mock calls
    mock_template_service.render_template.assert_awaited_once()
    args, kwargs = mock_template_service.render_template.call_args
    assert kwargs.get("db") == mock_db_session
    assert kwargs.get("key") == "INTENT_ROUTER_V1" # Default key
    assert kwargs.get("category") == "intent_router" # Default category
    assert kwargs.get("context")["user_message"] == user_message
    # Check if intent data in context matches (simplified check for brevity)
    assert len(kwargs.get("context")["available_intents"]) == len(intents_definitions)

    mock_llm_provider.generate.assert_awaited_once()
    args_llm, kwargs_llm = mock_llm_provider.generate.call_args
    # Check messages passed to LLM
    expected_system_instruction = "System instructions: You are an intent classification AI.\nRespond in JSON with 'intent', 'confidence', and 'parameters'."
    expected_user_prompt = "User prompt: Classify the following message.\nUser message: \"What's the weather like in London today?\"\nAvailable intents: get_weather, book_flight"
    
    assert len(kwargs_llm.get("messages")) == 2
    assert kwargs_llm.get("messages")[0]["role"] == "system"
    assert kwargs_llm.get("messages")[0]["content"] == expected_system_instruction
    assert kwargs_llm.get("messages")[1]["role"] == "user"
    assert kwargs_llm.get("messages")[1]["content"] == expected_user_prompt
    assert kwargs_llm.get("user") == user_id
