"""
Integration tests for OAuth flow and token management.
"""

import json
import uuid
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.models.integration import APIAuthConfig, APIIntegration, AuthType
from src.coherence.models.tenant import Tenant


@pytest.fixture
def oauth_mock_responses():
    """Mock responses for OAuth API calls."""
    return {
        "token_response": {
            "access_token": "mock_access_token",
            "token_type": "Bearer",
            "expires_in": 3600,
            "refresh_token": "mock_refresh_token",
            "scope": "read write",
        },
        "error_response": {
            "error": "invalid_grant",
            "error_description": "Invalid authorization code",
        },
    }


@pytest.fixture
async def test_integration(async_db_session: AsyncSession, test_tenant: Tenant):
    """Create a test integration for OAuth tests."""
    # Create test integration
    integration = APIIntegration(
        tenant_id=test_tenant.id,
        name="Test OAuth Integration",
        version="1.0",
        openapi_spec={"openapi": "3.0.0"},
        base_url="https://api.example.com",
        status="active",
    )
    async_db_session.add(integration)
    await async_db_session.flush()
    
    # Create auth config
    auth_config = APIAuthConfig(
        integration_id=integration.id,
        auth_type=AuthType.OAUTH2,
        credentials={
            "client_id": "test_client_id",
            "client_secret": "test_client_secret",
            "authorization_url": "https://auth.example.com/authorize",
            "token_url": "https://auth.example.com/token",
            "redirect_uri": "https://coherence.example.com/oauth/callback",
        },
        scopes=["read", "write"],
    )
    async_db_session.add(auth_config)
    
    await async_db_session.commit()
    return integration


@pytest.mark.asyncio
async def test_initialize_oauth_flow(
    client: AsyncClient,
    test_tenant: Tenant,
    test_integration: APIIntegration,
    api_key_header: dict,
):
    """Test initializing an OAuth authorization code flow."""
    # Make request to initialize flow
    response = await client.post(
        f"/v1/admin/oauth/initialize?integration_id={test_integration.id}&flow_type=authorization_code",
        headers=api_key_header,
    )
    
    # Validate response
    assert response.status_code == 200
    data = response.json()
    assert data["flow_type"] == "authorization_code"
    assert "authorization_url" in data
    assert "state" in data
    assert "client_id=test_client_id" in data["authorization_url"]
    
    
@pytest.mark.asyncio
async def test_initialize_client_credentials_flow(
    client: AsyncClient,
    test_tenant: Tenant,
    test_integration: APIIntegration,
    api_key_header: dict,
    oauth_mock_responses: dict,
):
    """Test initializing an OAuth client credentials flow."""
    # Mock token request
    with patch("aiohttp.ClientSession.post") as mock_post:
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(
            return_value=oauth_mock_responses["token_response"]
        )
        mock_post.return_value.__aenter__.return_value = mock_response
        
        # Make request to initialize flow
        response = await client.post(
            f"/v1/admin/oauth/initialize?integration_id={test_integration.id}&flow_type=client_credentials",
            headers=api_key_header,
        )
        
        # Validate response
        assert response.status_code == 200
        data = response.json()
        assert data["flow_type"] == "client_credentials"
        assert data["token_type"] == "Bearer"
        assert data["expires_in"] == 3600
        assert data["scope"] == "read write"
        
        # Verify the token request was made
        mock_post.assert_called_once()
        
        
@pytest.mark.asyncio
async def test_oauth_callback_success(
    client: AsyncClient,
    test_tenant: Tenant,
    test_integration: APIIntegration,
    db: AsyncSession,
    oauth_mock_responses: dict,
):
    """Test successful OAuth callback processing."""
    # Create a state record
    state = str(uuid.uuid4())
    await db.execute(
        """
        INSERT INTO oauth_states (state, data, expires_at)
        VALUES (:state, :data, :expires_at)
        """,
        {
            "state": state,
            "data": json.dumps({
                "integration_id": str(test_integration.id),
                "flow_type": "authorization_code",
                "redirect_uri": "https://coherence.example.com/oauth/callback",
                "scopes": ["read", "write"],
            }),
            "expires_at": datetime.now() + timedelta(minutes=30),
        },
    )
    await db.commit()
    
    # Mock token request
    with patch("aiohttp.ClientSession.post") as mock_post:
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(
            return_value=oauth_mock_responses["token_response"]
        )
        mock_post.return_value.__aenter__.return_value = mock_response
        
        # Make callback request
        response = await client.get(
            f"/v1/admin/oauth/callback?state={state}&code=test_auth_code",
        )
        
        # Validate response
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "Authorization completed successfully" in data["message"]
        
        # Verify the token request was made
        mock_post.assert_called_once()
        
        
@pytest.mark.asyncio
async def test_oauth_token_refresh(
    client: AsyncClient,
    test_tenant: Tenant,
    test_integration: APIIntegration,
    db: AsyncSession,
    api_key_header: dict,
    oauth_mock_responses: dict,
):
    """Test refreshing an OAuth token."""
    # Update auth config with token data
    await db.execute(
        """
        UPDATE api_auth_configs
        SET credentials = jsonb_set(credentials, '{token}', :token_data)
        WHERE integration_id = :integration_id
        """,
        {
            "integration_id": test_integration.id,
            "token_data": json.dumps({
                "access_token": "old_access_token",
                "token_type": "Bearer",
                "expires_in": 3600,
                "refresh_token": "old_refresh_token",
                "scope": "read write",
                "expires_at": datetime.now().timestamp() - 100,  # Expired
            }),
        },
    )
    await db.commit()
    
    # Mock token request
    with patch("aiohttp.ClientSession.post") as mock_post:
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(
            return_value=oauth_mock_responses["token_response"]
        )
        mock_post.return_value.__aenter__.return_value = mock_response
        
        # Make refresh request
        response = await client.post(
            f"/v1/admin/oauth/token/refresh?integration_id={test_integration.id}",
            headers=api_key_header,
        )
        
        # Validate response
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["token_type"] == "Bearer"
        assert data["expires_in"] == 3600
        
        # Verify the token request was made
        mock_post.assert_called_once()
        
        
@pytest.mark.asyncio
async def test_get_oauth_config(
    client: AsyncClient,
    test_tenant: Tenant,
    test_integration: APIIntegration,
    db: AsyncSession,
    api_key_header: dict,
):
    """Test getting OAuth configuration for an integration."""
    # Set token expiration
    expires_at = datetime.now() + timedelta(hours=1)
    await db.execute(
        """
        UPDATE api_auth_configs
        SET expires_at = :expires_at
        WHERE integration_id = :integration_id
        """,
        {
            "integration_id": test_integration.id,
            "expires_at": expires_at,
        },
    )
    await db.commit()
    
    # Make request
    response = await client.get(
        f"/v1/admin/oauth/config/{test_integration.id}",
        headers=api_key_header,
    )
    
    # Validate response
    assert response.status_code == 200
    data = response.json()
    assert data["integration_id"] == str(test_integration.id)
    assert data["auth_type"] == "oauth2"
    assert data["scopes"] == ["read", "write"]
    assert data["token_valid"] is True
    assert data["expires_at"] == expires_at.isoformat()