"""
Integration test for the template-driven API action system.

This module tests the end-to-end flow of template-driven API actions
from template generation to API execution and response rendering.
"""

import uuid
from unittest.mock import AsyncMock, patch

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.db.session import get_db
from src.coherence.intent_pipeline.schemas.intent import (
    IntentDefinition,
    IntentMatch,
    ParameterDefinition,
    ParameterType,
)
from src.coherence.main import app
from src.coherence.models.template import Template, TemplateCategory


@pytest.mark.asyncio
async def test_template_driven_weather_api():
    """
    Test the complete flow of the template-driven API action system:
    1. Generate templates from an OpenAPI spec
    2. Execute a weather query based on the template
    3. Verify the response transformation
    """
    # Mock database session
    db = AsyncMock(spec=AsyncSession)
    
    # Override the get_db dependency
    app.dependency_overrides[get_db] = lambda: db
    
    # Create a test client
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as client:
        # Setup test data - Create a weather API template
        template_id = uuid.uuid4()
        tenant_id = uuid.uuid4()
        
        template = Template(
            id=template_id,
            key="weather_forecast",
            category=TemplateCategory.RESPONSE_GEN,
            body="The current temperature in {{ parameters.location.city }} is {{ results.weather_api.current_temp }}{{ results.weather_api.unit }}. The forecast for the next few days is {{ results.weather_api.forecast }}.",
            tenant_id=tenant_id,
            scope="tenant",
            actions=[{
                "api_key": "weather_api",
                "endpoint": "/v1/forecast",
                "method": "GET",
                "parameter_mapping": {
                    "latitude": "{{ parameters.location.latitude }}",
                    "longitude": "{{ parameters.location.longitude }}"
                },
                "response_mapping": {
                    "current_temp": "{{ response.current.temperature_2m }}",
                    "forecast": "{{ response.daily.temperature_2m_max | join(', ') }}",
                    "unit": "{{ response.current_units.temperature_2m }}"
                }
            }]
        )
        
        # Mock the intent resolver to return a predefined intent
        async def mock_resolve(*args, **kwargs):
            return IntentMatch(
                intent="weather_forecast",
                confidence=0.95,
                template_key="weather_forecast",
                filled={
                    "location": {
                        "city": "New York",
                        "latitude": 40.7128,
                        "longitude": -74.0060
                    }
                },
                alternatives=[]
            )
        
        # Mock getting available intents
        async def mock_get_available_intents(*args, **kwargs):
            return [
                IntentDefinition(
                    name="weather_forecast",
                    description="Get the weather forecast for a location",
                    examples=[
                        "What's the weather like in New York?",
                        "Show me the forecast for San Francisco"
                    ],
                    parameters={
                        "location": ParameterDefinition(
                            name="location",
                            type=ParameterType.STRING,
                            required=True,
                            description="The location to get weather for",
                            prompt="What location would you like the weather for?"
                        )
                    },
                    required_fields={"location"},
                    template_key="weather_forecast"
                )
            ]
        
        # Mock the database to return our template
        async def mock_get_template(*args, **kwargs):
            return template
        
        # Set up the mocks
        with patch(
            "src.coherence.intent_pipeline.orchestrator.ChatOrchestrator._get_available_intents",
            side_effect=mock_get_available_intents
        ), patch(
            "src.coherence.intent_pipeline.resolver.IntentResolver.resolve",
            side_effect=mock_resolve
        ), patch(
            "src.coherence.intent_pipeline.orchestrator.ChatOrchestrator._get_intent_template",
            side_effect=mock_get_template
        ), patch(
            "src.coherence.openapi_adapter.dynamic_executor.DynamicActionExecutor.execute",
            side_effect=lambda *args, **kwargs: {
                "success": True,
                "status_code": 200,
                "result": {
                    "current_temp": 72.5,
                    "forecast": "75, 78, 73, 71, 70",
                    "unit": "°F"
                }
            }
        ):
            # Send a test request
            response = await client.post(
                "/v1/resolve",
                json={
                    "message": "What's the weather like in New York?",
                    "user_id": "test-user",
                    "tenant_id": str(tenant_id)
                }
            )
            
            # Verify response
            assert response.status_code == 200
            data = response.json()
            assert data["type"] == "action" or "reply" in data["type"]
            
            # Check that the output contains the expected weather information
            # The exact format depends on your implementation
            assert "72.5°F" in data["outcome"] or "temperature" in data["outcome"]
            assert "75, 78, 73, 71, 70" in data["outcome"] or "forecast" in data["outcome"]
    
    # Clean up the dependency override
    app.dependency_overrides.clear()


@pytest.mark.asyncio
async def test_fallback_handling():
    """
    Test that fallback templates are used when API calls fail.
    """
    # Mock database session
    db = AsyncMock(spec=AsyncSession)
    
    # Override the get_db dependency
    app.dependency_overrides[get_db] = lambda: db
    
    # Create a test client
    async with AsyncClient(app=app, base_url="http://test") as client:
        # Setup test data - Create a weather API template and fallback
        template_id = uuid.uuid4()
        fallback_id = uuid.uuid4()
        tenant_id = uuid.uuid4()
        
        template = Template(
            id=template_id,
            key="weather_forecast",
            category=TemplateCategory.RESPONSE_GEN,
            body="The current temperature in {{ parameters.location.city }} is {{ results.weather_api.current_temp }}{{ results.weather_api.unit }}.",
            tenant_id=tenant_id,
            scope="tenant",
            actions=[{
                "api_key": "weather_api",
                "endpoint": "/v1/forecast",
                "method": "GET",
                "parameter_mapping": {
                    "latitude": "{{ parameters.location.latitude }}",
                    "longitude": "{{ parameters.location.longitude }}"
                },
                "response_mapping": {
                    "current_temp": "{{ response.current.temperature_2m }}",
                    "unit": "{{ response.current_units.temperature_2m }}"
                },
                "error_handling": {
                    "fallback_template": "weather_forecast_fallback"
                }
            }]
        )
        
        fallback_template = Template(
            id=fallback_id,
            key="weather_forecast_fallback",
            category=TemplateCategory.ERROR_HANDLER,
            body="I'm sorry, I couldn't retrieve the weather information for {{ parameters.location.city }} due to a technical issue.",
            tenant_id=tenant_id,
            scope="tenant",
            actions=[]
        )
        
        # Mock the intent resolver and other dependencies similar to the first test
        # but this time make the API call fail
        
        # Mock the intent resolver to return a predefined intent
        async def mock_resolve(*args, **kwargs):
            return IntentMatch(
                intent="weather_forecast",
                confidence=0.95,
                template_key="weather_forecast",
                filled={
                    "location": {
                        "city": "New York",
                        "latitude": 40.7128,
                        "longitude": -74.0060
                    }
                },
                alternatives=[]
            )
        
        # Mock getting available intents
        async def mock_get_available_intents(*args, **kwargs):
            return [
                IntentDefinition(
                    name="weather_forecast",
                    description="Get the weather forecast for a location",
                    examples=[
                        "What's the weather like in New York?"
                    ],
                    parameters={
                        "location": ParameterDefinition(
                            name="location",
                            type=ParameterType.STRING,
                            required=True,
                            description="The location to get weather for"
                        )
                    },
                    required_fields={"location"},
                    template_key="weather_forecast"
                )
            ]
        
        # Mock the database to return our templates
        template_dict = {
            "weather_forecast": template,
            "weather_forecast_fallback": fallback_template
        }
        
        async def mock_get_template(*args, **kwargs):
            key = args[0] if args else kwargs.get("key", "weather_forecast")
            return template_dict.get(key)
        
        # Set up the mocks
        with patch(
            "src.coherence.intent_pipeline.orchestrator.ChatOrchestrator._get_available_intents",
            side_effect=mock_get_available_intents
        ), patch(
            "src.coherence.intent_pipeline.resolver.IntentResolver.resolve",
            side_effect=mock_resolve
        ), patch(
            "src.coherence.intent_pipeline.orchestrator.ChatOrchestrator._get_intent_template",
            side_effect=mock_get_template
        ), patch(
            "src.coherence.openapi_adapter.dynamic_executor.DynamicActionExecutor.execute",
            side_effect=lambda *args, **kwargs: {
                "success": False,
                "error": {
                    "type": "api_error",
                    "message": "API endpoint unreachable",
                    "fallback_used": True
                },
                "fallback": {
                    "template": "weather_forecast_fallback"
                }
            }
        ):
            # Send a test request
            response = await client.post(
                "/v1/resolve",
                json={
                    "message": "What's the weather like in New York?",
                    "user_id": "test-user",
                    "tenant_id": str(tenant_id)
                }
            )
            
            # Verify response
            assert response.status_code == 200
            data = response.json()
            
            # Check that the fallback message is returned
            assert "sorry" in data["outcome"].lower() or "couldn't retrieve" in data["outcome"].lower()
            assert "New York" in data["outcome"]
    
    # Clean up the dependency override
    app.dependency_overrides.clear()