"""
Integration tests for API specification format support.

These tests verify the end-to-end process of detecting, converting, and 
storing different API specification formats.
"""

import uuid
from typing import Any, Dict

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.main import app
from src.coherence.models.integration import APIIntegration, SpecFormat


@pytest.fixture
def openapi_spec() -> Dict[str, Any]:
    """Return a sample OpenAPI spec for testing."""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Test OpenAPI",
            "version": "1.0.0"
        },
        "paths": {
            "/test": {
                "get": {
                    "operationId": "testOperation",
                    "responses": {
                        "200": {
                            "description": "OK"
                        }
                    }
                }
            }
        }
    }


@pytest.fixture
def fapi_spec() -> Dict[str, Any]:
    """Return a sample FAPI spec for testing."""
    return {
        "financial_api": "1.0",
        "info": {
            "title": "Test FAPI",
            "version": "1.0.0"
        },
        "security_profiles": {
            "oauth2_profile": {
                "type": "oauth2",
                "profile": "FAPI 1.0",
                "requires_pkce": True,
                "flows": {
                    "authorization_code": {
                        "authorization_url": "https://auth.example.com/authorize",
                        "token_url": "https://auth.example.com/token",
                        "scopes": {
                            "accounts": "Access to account information"
                        }
                    }
                }
            }
        },
        "endpoints": [
            {
                "path": "/accounts",
                "method": "get",
                "id": "get_accounts",
                "summary": "Get Accounts"
            }
        ]
    }


@pytest.fixture
def bapi_spec() -> Dict[str, Any]:
    """Return a sample BAPI spec for testing."""
    return {
        "business_api": "1.0",
        "info": {
            "title": "Test BAPI",
            "version": "1.0.0"
        },
        "authentication": {
            "api_key_auth": {
                "type": "apikey",
                "name": "X-API-Key",
                "in": "header"
            }
        },
        "services": [
            {
                "name": "orders",
                "operations": [
                    {
                        "name": "createOrder",
                        "method": "post",
                        "path": "/orders",
                        "summary": "Create a new order"
                    }
                ]
            }
        ]
    }


@pytest.mark.asyncio
async def test_openapi_import(async_db_session: AsyncSession, monkeypatch, openapi_spec: Dict[str, Any]):
    """Test importing an OpenAPI spec and verifying it's stored correctly."""
    # Use a test tenant ID
    tenant_id = uuid.uuid4()
    
    # Create a test client
    client = TestClient(app)
    
    # Make the import request
    response = client.post(
        "/v1/openapi/import",
        json={
            "name": "Test OpenAPI Integration",
            "spec": openapi_spec
        },
        headers={
            "X-API-Key": "test_key",
            "X-Tenant-ID": str(tenant_id)
        }
    )
    
    # Check the response
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "Test OpenAPI Integration"
    assert data["spec_format"] == "openapi"
    
    # Check the database
    integration_id = uuid.UUID(data["integration_id"])
    db_integration = await async_db_session.get(APIIntegration, integration_id)
    assert db_integration is not None
    assert db_integration.spec_format == SpecFormat.OPENAPI
    
    # Verify no original spec is stored
    original_specs = await async_db_session.execute(
        "SELECT * FROM api_original_specs WHERE integration_id = :integration_id",
        {"integration_id": integration_id}
    )
    assert original_specs.fetchone() is None


@pytest.mark.asyncio
async def test_fapi_import(async_db_session: AsyncSession, monkeypatch, fapi_spec: Dict[str, Any]):
    """Test importing a FAPI spec and verifying it's converted and stored correctly."""
    # Use a test tenant ID
    tenant_id = uuid.uuid4()
    
    # Create a test client
    client = TestClient(app)
    
    # Make the import request
    response = client.post(
        "/v1/openapi/import",
        json={
            "name": "Test FAPI Integration",
            "spec": fapi_spec
        },
        headers={
            "X-API-Key": "test_key",
            "X-Tenant-ID": str(tenant_id)
        }
    )
    
    # Check the response
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "Test FAPI Integration"
    assert data["spec_format"] == "fapi"
    
    # Check the database
    integration_id = uuid.UUID(data["integration_id"])
    db_integration = await async_db_session.get(APIIntegration, integration_id)
    assert db_integration is not None
    assert db_integration.spec_format == SpecFormat.FAPI
    
    # Verify original spec is stored
    original_specs = await async_db_session.execute(
        "SELECT * FROM api_original_specs WHERE integration_id = :integration_id",
        {"integration_id": integration_id}
    )
    original_spec = original_specs.fetchone()
    assert original_spec is not None
    assert original_spec.format == "fapi"
    
    # Verify the integration still uses converted OpenAPI format internally
    assert "openapi" in db_integration.openapi_spec
    assert db_integration.openapi_spec["openapi"] == "3.0.0"


@pytest.mark.asyncio
async def test_bapi_import(async_db_session: AsyncSession, monkeypatch, bapi_spec: Dict[str, Any]):
    """Test importing a BAPI spec and verifying it's converted and stored correctly."""
    # Use a test tenant ID
    tenant_id = uuid.uuid4()
    
    # Create a test client
    client = TestClient(app)
    
    # Make the import request
    response = client.post(
        "/v1/openapi/import",
        json={
            "name": "Test BAPI Integration",
            "spec": bapi_spec
        },
        headers={
            "X-API-Key": "test_key",
            "X-Tenant-ID": str(tenant_id)
        }
    )
    
    # Check the response
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "Test BAPI Integration"
    assert data["spec_format"] == "bapi"
    
    # Check the database
    integration_id = uuid.UUID(data["integration_id"])
    db_integration = await async_db_session.get(APIIntegration, integration_id)
    assert db_integration is not None
    assert db_integration.spec_format == SpecFormat.BAPI
    
    # Verify original spec is stored
    original_specs = await async_db_session.execute(
        "SELECT * FROM api_original_specs WHERE integration_id = :integration_id",
        {"integration_id": integration_id}
    )
    original_spec = original_specs.fetchone()
    assert original_spec is not None
    assert original_spec.format == "bapi"
    
    # Verify the integration still uses converted OpenAPI format internally
    assert "openapi" in db_integration.openapi_spec
    assert db_integration.openapi_spec["openapi"] == "3.0.0"
    # Verify the paths were correctly created
    assert "/orders" in db_integration.openapi_spec["paths"]