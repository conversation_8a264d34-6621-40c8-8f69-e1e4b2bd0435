"""
Integration tests for the system admin bypass functionality.

These tests verify that system administrators can use the chat API
without providing an API key, while regular users still need one.
"""

import uuid

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.api.v1.api import api_router
from src.coherence.db.deps import get_db
from src.coherence.models.tenant import Tenant


@pytest.fixture
def test_app():
    """Create a test FastAPI app with the API router"""
    app = FastAPI()
    app.include_router(api_router)
    return app


@pytest.fixture
def mock_db_dependency(mocker):
    """Mock the database dependency"""
    async def override_get_db():
        yield mocker.AsyncMock(spec=AsyncSession)
    return override_get_db


@pytest.fixture
def mock_tenant():
    """Create a mock tenant"""
    tenant = Tenant()
    tenant.id = uuid.uuid4()
    tenant.name = "Test Tenant"
    tenant.clerk_org_id = "org_123456"
    return tenant


@pytest.fixture
def mock_db_with_tenant(mocker, mock_tenant):
    """Create a mock DB session that returns our test tenant"""
    async def _mock_db():
        # Create mock AsyncSession
        mock_db = mocker.AsyncMock(spec=AsyncSession)
        
        # Configure mock execute to return the tenant
        mock_result = mocker.MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_tenant
        mock_db.execute.return_value = mock_result
        
        yield mock_db
    
    return _mock_db


@pytest.fixture
def system_admin_headers():
    """Headers for a system admin request"""
    return {
        "Authorization": "Bearer system_admin_jwt_token",
        "X-Tenant-ID": "org_123456",
    }


@pytest.fixture
def regular_user_headers():
    """Headers for a regular user request"""
    return {
        "Authorization": "Bearer regular_user_jwt_token",
        "X-Tenant-ID": "org_123456",
        "X-API-Key": "valid_api_key",
    }


@pytest.fixture
def resolve_request_body():
    """Sample resolve request body"""
    return {
        "user_id": str(uuid.uuid4()),
        "role": "user",
        "message": "What's the weather like today?",
        "context": {}
    }


@pytest.mark.asyncio
async def test_system_admin_bypass_works(
    test_app, 
    mock_db_with_tenant, 
    mocker, 
    system_admin_headers,
    resolve_request_body
):
    """Test that system admins can use the chat API without an API key"""
    # Mock dependencies
    test_app.dependency_overrides[get_db] = mock_db_with_tenant
    
    # Mock the clerk auth dependency
    mock_clerk_auth = mocker.MagicMock()
    mock_clerk_auth.user_id = "system_admin_user_id"
    mock_clerk_auth.org_id = "org_123456"
    
    async def mock_get_clerk_auth_details():
        return mock_clerk_auth
    
    # Override the auth dependency
    from src.coherence.api.v1.dependencies.auth import get_clerk_auth_details
    test_app.dependency_overrides[get_clerk_auth_details] = mock_get_clerk_auth_details
    
    # Mock is_system_admin to return True for our test
    mocker.patch("src.coherence.api.v1.utils.auth_utils.is_system_admin", return_value=True)
    
    # Mock the chat orchestrator to return a simple response
    mock_orchestrator = mocker.MagicMock()
    mock_orchestrator.handle_message.return_value = {
        "type": "action",
        "outcome": "This is a test response from the mocked orchestrator.",
    }
    
    from src.coherence.intent_pipeline.orchestrator import get_chat_orchestrator
    test_app.dependency_overrides[get_chat_orchestrator] = lambda: mock_orchestrator
    
    # Create test client
    client = TestClient(test_app)
    
    # Make request without API key (system admin should be able to bypass)
    response = client.post(
        "/v1/resolve",
        headers=system_admin_headers,  # No API key in these headers
        json=resolve_request_body
    )
    
    # Verify the response
    assert response.status_code == 200, f"Expected 200 OK, got {response.status_code}: {response.text}"
    response_data = response.json()
    assert response_data["kind"] == "reply"
    assert "This is a test response" in response_data["text"]
    
    # Verify that the orchestrator was called with the correct parameters
    mock_orchestrator.handle_message.assert_called_once()
    call_args = mock_orchestrator.handle_message.call_args[1]
    assert "tenant_id" in call_args
    assert "user_id" in call_args
    assert "message" in call_args


@pytest.mark.asyncio
async def test_regular_user_requires_api_key(
    test_app, 
    mock_db_with_tenant, 
    mocker, 
    regular_user_headers,
    resolve_request_body
):
    """Test that regular users still need an API key"""
    # Mock dependencies
    test_app.dependency_overrides[get_db] = mock_db_with_tenant
    
    # Mock the clerk auth dependency
    mock_clerk_auth = mocker.MagicMock()
    mock_clerk_auth.user_id = "regular_user_id"
    mock_clerk_auth.org_id = "org_123456"
    
    async def mock_get_clerk_auth_details():
        return mock_clerk_auth
    
    # Override the auth dependency
    from src.coherence.api.v1.dependencies.auth import get_clerk_auth_details
    test_app.dependency_overrides[get_clerk_auth_details] = mock_get_clerk_auth_details
    
    # Mock is_system_admin to return False for regular users
    mocker.patch("src.coherence.api.v1.utils.auth_utils.is_system_admin", return_value=False)
    
    # Mock API key validation to succeed for our test key
    mocker.patch("hashlib.sha256")
    
    from src.coherence.models.tenant import OrganizationAPIKey
    mock_api_key = mocker.MagicMock(spec=OrganizationAPIKey)
    mock_api_key.id = "api-key-id"
    mock_api_key.clerk_org_id = "org_123456"
    mock_api_key.revoked = False
    mock_api_key.expires_at = None
    
    # Configure db.execute to return our mock API key
    async def override_mock_db():
        mock_db = mocker.AsyncMock(spec=AsyncSession)
        
        # First call - API key result
        api_key_result = mocker.MagicMock()
        api_key_result.scalar_one_or_none.return_value = mock_api_key
        
        # Second call - tenant result
        tenant_result = mocker.MagicMock()
        tenant_result.scalar_one_or_none.return_value = mocker.MagicMock(spec=Tenant, id=uuid.uuid4(), clerk_org_id="org_123456")
        
        # Configure the mock to return different results for consecutive calls
        mock_db.execute.side_effect = [api_key_result, tenant_result]
        
        yield mock_db
    
    test_app.dependency_overrides[get_db] = override_mock_db
    
    # Mock the chat orchestrator
    mock_orchestrator = mocker.MagicMock()
    mock_orchestrator.handle_message.return_value = {
        "type": "action",
        "outcome": "Regular user response.",
    }
    
    from src.coherence.intent_pipeline.orchestrator import get_chat_orchestrator
    test_app.dependency_overrides[get_chat_orchestrator] = lambda: mock_orchestrator
    
    # Create test client
    client = TestClient(test_app)
    
    # 1. Test with API key - should succeed
    response_with_key = client.post(
        "/v1/resolve",
        headers=regular_user_headers,  # Includes API key
        json=resolve_request_body
    )
    
    assert response_with_key.status_code == 200
    assert response_with_key.json()["kind"] == "reply"
    
    # 2. Test without API key - should fail
    headers_without_key = regular_user_headers.copy()
    del headers_without_key["X-API-Key"]
    
    response_without_key = client.post(
        "/v1/resolve",
        headers=headers_without_key,
        json=resolve_request_body
    )
    
    assert response_without_key.status_code == 401
    assert "API key required" in response_without_key.text


@pytest.mark.asyncio
async def test_continue_endpoint_with_system_admin(
    test_app, 
    mock_db_with_tenant, 
    mocker, 
    system_admin_headers
):
    """Test that system admins can use the continue endpoint without an API key"""
    # Mock dependencies
    test_app.dependency_overrides[get_db] = mock_db_with_tenant
    
    # Mock auth
    mock_clerk_auth = mocker.MagicMock()
    mock_clerk_auth.user_id = "system_admin_user_id"
    mock_clerk_auth.org_id = "org_123456"
    
    async def mock_get_clerk_auth_details():
        return mock_clerk_auth
    
    from src.coherence.api.v1.dependencies.auth import get_clerk_auth_details
    test_app.dependency_overrides[get_clerk_auth_details] = mock_get_clerk_auth_details
    
    # Mock is_system_admin to return True
    mocker.patch("src.coherence.api.v1.utils.auth_utils.is_system_admin", return_value=True)
    
    # Mock the chat orchestrator
    mock_orchestrator = mocker.MagicMock()
    mock_orchestrator.continue_conversation.return_value = {
        "type": "action",
        "outcome": "Continuing the conversation as a system admin.",
    }
    
    from src.coherence.intent_pipeline.orchestrator import get_chat_orchestrator
    test_app.dependency_overrides[get_chat_orchestrator] = lambda: mock_orchestrator
    
    # Create test client
    client = TestClient(test_app)
    
    # Make request without API key to the continue endpoint
    continue_request = {
        "conversation_id": str(uuid.uuid4()),
        "user_id": str(uuid.uuid4()),
        "message": "Yes, that's what I want.",
    }
    
    response = client.post(
        "/v1/continue",
        headers=system_admin_headers,  # No API key
        json=continue_request
    )
    
    # Verify the response
    assert response.status_code == 200
    response_data = response.json()
    assert response_data["kind"] == "reply"
    assert "Continuing the conversation" in response_data["text"]


@pytest.mark.asyncio
async def test_status_endpoint_with_system_admin(
    test_app, 
    mock_db_with_tenant, 
    mocker, 
    system_admin_headers
):
    """Test that system admins can check workflow status without an API key"""
    # Mock dependencies
    test_app.dependency_overrides[get_db] = mock_db_with_tenant
    
    # Mock auth
    mock_clerk_auth = mocker.MagicMock()
    mock_clerk_auth.user_id = "system_admin_user_id"
    mock_clerk_auth.org_id = "org_123456"
    
    async def mock_get_clerk_auth_details():
        return mock_clerk_auth
    
    from src.coherence.api.v1.dependencies.auth import get_clerk_auth_details
    test_app.dependency_overrides[get_clerk_auth_details] = mock_get_clerk_auth_details
    
    # Mock is_system_admin to return True
    mocker.patch("src.coherence.api.v1.utils.auth_utils.is_system_admin", return_value=True)
    
    # Mock the workflow status
    from src.coherence.models.workflow_status import WorkflowStatus
    mock_workflow = mocker.MagicMock(spec=WorkflowStatus)
    mock_workflow.id = uuid.uuid4()
    mock_workflow.tenant_id = mocker.MagicMock()
    mock_workflow.status = "completed"
    mock_workflow.progress = 1.0
    mock_workflow.current_step = "Final step"
    mock_workflow.result = {"output": "Task completed successfully"}
    
    # Mock DB to return the workflow
    async def override_mock_db_workflow():
        mock_db = mocker.AsyncMock(spec=AsyncSession)
        
        # Mock query result
        result = mocker.MagicMock()
        result.scalars().first.return_value = mock_workflow
        mock_db.execute.return_value = result
        
        yield mock_db
    
    test_app.dependency_overrides[get_db] = override_mock_db_workflow
    
    # Create test client
    client = TestClient(test_app)
    
    # Make request without API key to check workflow status
    workflow_id = str(uuid.uuid4())
    response = client.get(
        f"/v1/status/{workflow_id}",
        headers=system_admin_headers  # No API key
    )
    
    # Verify the response
    assert response.status_code == 200
    response_data = response.json()
    assert response_data["status"] == "completed"
    assert response_data["progress"] == 1.0
    assert response_data["current_step"] == "Final step"
    assert response_data["result"] == {"output": "Task completed successfully"}