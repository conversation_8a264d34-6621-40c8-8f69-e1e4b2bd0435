#!/usr/bin/env python3
"""Check that templates are properly deleted from both PostgreSQL and vector stores."""

import asyncio
import logging
from pathlib import Path
import sys
from typing import Optional
import uuid

# Add src to sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.coherence.db.session import async_session
from src.coherence.models.template import Template
from src.coherence.core.qdrant_client import get_qdrant_client, QdrantClient
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def check_postgres_templates(db: AsyncSession, tenant_id: Optional[str] = None):
    """Check PostgreSQL for templates."""
    print("\n=== Checking PostgreSQL Database ===")
    
    # Count all templates
    total_count = await db.scalar(select(func.count(Template.id)))
    print(f"Total templates in database: {total_count}")
    
    # Count templates by scope
    scope_counts = await db.execute(
        select(Template.scope, func.count(Template.id))
        .group_by(Template.scope)
    )
    for scope, count in scope_counts:
        print(f"  - {scope} templates: {count}")
    
    # If tenant_id provided, check tenant-specific templates
    if tenant_id:
        tenant_uuid = uuid.UUID(tenant_id)
        tenant_count = await db.scalar(
            select(func.count(Template.id))
            .where(Template.tenant_id == tenant_uuid)
        )
        print(f"\nTemplates for tenant {tenant_id}: {tenant_count}")
        
        # List any found templates
        if tenant_count > 0:
            templates = await db.execute(
                select(Template.key, Template.category, Template.scope)
                .where(Template.tenant_id == tenant_uuid)
            )
            print("Found templates:")
            for key, category, scope in templates:
                print(f"  - {key} ({category}, {scope})")
    
    # Check for any protected templates
    protected_count = await db.scalar(
        select(func.count(Template.id))
        .where(Template.protected == True)
    )
    print(f"\nProtected templates: {protected_count}")
    
    # List categories with counts
    category_counts = await db.execute(
        select(Template.category, func.count(Template.id))
        .group_by(Template.category)
    )
    print("\nTemplates by category:")
    for category, count in category_counts:
        print(f"  - {category}: {count}")


async def check_vector_stores(tenant_id: Optional[str] = None):
    """Check vector stores for templates."""
    print("\n=== Checking Vector Stores (Qdrant) ===")
    
    qdrant_client = await get_qdrant_client()
    
    collections_to_check = []
    
    # Add tenant-specific collection if tenant_id provided
    if tenant_id:
        collections_to_check.append(f"intent_idx_{tenant_id}_user")
    
    # Always check global collection
    collections_to_check.append("template_idx_global")
    
    # Try to list all collections
    try:
        # Direct access to the client instance (not async)
        collections_response = qdrant_client.client.get_collections()
        all_collections = [col.name for col in collections_response.collections]
        print(f"Total collections in Qdrant: {len(all_collections)}")
        
        # Filter for template/intent collections
        template_collections = [col for col in all_collections if "template" in col or "intent" in col]
        print(f"Template/Intent collections: {len(template_collections)}")
        for col in template_collections:
            print(f"  - {col}")
    except Exception as e:
        print(f"Error listing collections: {e}")
        all_collections = []
    
    # Check specific collections
    for collection_name in collections_to_check:
        print(f"\nChecking collection: {collection_name}")
        try:
            # Check if collection exists
            if collection_name not in all_collections:
                print(f"  Collection does not exist")
                continue
            
            # Get collection info
            collection_info = qdrant_client.client.get_collection(collection_name)
            point_count = collection_info.points_count
            vector_count = collection_info.vectors_count
            
            print(f"  Points count: {point_count}")
            print(f"  Vectors count: {vector_count}")
            
            # If there are points, try to get some samples
            if point_count > 0:
                # Scroll through some points to see what's there
                points_response = qdrant_client.client.scroll(
                    collection_name=collection_name,
                    limit=5,
                    with_payload=True,
                    with_vectors=False
                )
                
                print(f"  Sample points (showing up to 5):")
                for point in points_response[0]:
                    payload = point.payload
                    print(f"    - ID: {point.id}")
                    if payload:
                        print(f"      Template Key: {payload.get('template_key', 'N/A')}")
                        print(f"      Category: {payload.get('template_category', 'N/A')}")
                        print(f"      Scope: {payload.get('scope', 'N/A')}")
                        print(f"      Tenant ID: {payload.get('tenant_id', 'N/A')}")
                
        except Exception as e:
            print(f"  Error checking collection: {e}")


async def main():
    """Main function to check both databases."""
    # Get tenant ID from environment or command line
    tenant_id = None
    if len(sys.argv) > 1:
        tenant_id = sys.argv[1]
    else:
        # Try to get from clerk organization ID in environment
        clerk_org_id = os.getenv("CLERK_ORG_ID")
        if clerk_org_id:
            print(f"Using Clerk org ID: {clerk_org_id}")
            # You might need to look up the tenant ID from clerk org ID
            # For now, we'll just use it directly
            tenant_id = clerk_org_id
    
    print(f"Checking for tenant: {tenant_id or 'ALL'}")
    
    # Check PostgreSQL
    async with async_session() as db:
        await check_postgres_templates(db, tenant_id)
    
    # Check Vector stores
    await check_vector_stores(tenant_id)
    
    print("\n=== Check Complete ===")


if __name__ == "__main__":
    if len(sys.argv) > 2 or (len(sys.argv) == 2 and sys.argv[1] == "--help"):
        print("Usage: python check_templates_deleted.py [tenant_id]")
        print("If no tenant_id is provided, checks all templates")
        sys.exit(0)
    
    asyncio.run(main())