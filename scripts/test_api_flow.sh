#!/bin/bash
# Coherence API Flow Test Script
# This script tests a complete flow of API operations to validate
# that the API is functioning correctly. 

# Exit on any error
set -e

# Colors for readable output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check for jq
if ! command -v jq &> /dev/null; then
    echo -e "${RED}Error: jq is required but not installed. Please install jq to continue.${NC}"
    echo "On macOS: brew install jq"
    echo "On Ubuntu/Debian: sudo apt-get install jq"
    exit 1
fi

# Set variables
API_URL=${API_URL:-"http://localhost:8001/v1"}
ADMIN_URL="${API_URL}/admin"

# Check for system admin key
if [ -z "$SYSTEM_ADMIN_KEY" ]; then
    echo -e "${RED}Error: SYSTEM_ADMIN_KEY environment variable must be set.${NC}"
    echo "Example: export SYSTEM_ADMIN_KEY=your_system_admin_key"
    exit 1
fi

echo -e "${BLUE}Starting Coherence API test flow...${NC}"
echo -e "${BLUE}API URL: ${API_URL}${NC}"

# Create a unique tenant name for testing
TENANT_NAME="Test Tenant $(date +%s)"
echo -e "${YELLOW}Step 1: Creating tenant '${TENANT_NAME}'${NC}"

TENANT_RESPONSE=$(curl -s -X POST "${ADMIN_URL}/tenants" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${SYSTEM_ADMIN_KEY}" \
  -d "{
    \"name\": \"${TENANT_NAME}\",
    \"industry_pack\": \"General\",
    \"admin_email\": \"<EMAIL>\"
  }")

# Check for errors
if echo "$TENANT_RESPONSE" | grep -q "error"; then
    echo -e "${RED}Error creating tenant:${NC}"
    echo "$TENANT_RESPONSE" | jq .
    exit 1
fi

# Extract tenant ID and API key
TENANT_ID=$(echo $TENANT_RESPONSE | jq -r '.id')
TENANT_API_KEY=$(echo $TENANT_RESPONSE | jq -r '.api_keys[0].key')

echo -e "${GREEN}Created tenant with ID: ${TENANT_ID}${NC}"
echo -e "${GREEN}API Key: ${TENANT_API_KEY}${NC}"

# Create additional API key
echo -e "${YELLOW}Step 2: Creating additional API key for tenant${NC}"
API_KEY_RESPONSE=$(curl -s -X POST "${ADMIN_URL}/tenants/${TENANT_ID}/api-keys" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d "{
    \"tenant_id\": \"${TENANT_ID}\",
    \"label\": \"Test API Key\"
  }")

# Extract the new API key
NEW_API_KEY=$(echo $API_KEY_RESPONSE | jq -r '.key')
echo -e "${GREEN}Created additional API key: ${NEW_API_KEY}${NC}"

# Update tenant settings
echo -e "${YELLOW}Step 3: Updating tenant settings${NC}"
SETTINGS_RESPONSE=$(curl -s -X PUT "${ADMIN_URL}/tenants/${TENANT_ID}/settings" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d '{
    "tier1_threshold": "0.9",
    "tier2_threshold": "0.75",
    "llm_model": "gpt-4",
    "embedding_model": "text-embedding-ada-002",
    "settings": {
      "debug_mode": "true",
      "log_level": "debug"
    }
  }')

echo -e "${GREEN}Updated tenant settings:${NC}"
echo "$SETTINGS_RESPONSE" | jq .

# Generate a UUID for user ID
USER_ID=$(uuidgen | tr '[:upper:]' '[:lower:]')
echo -e "${YELLOW}Step 4: Testing intent resolution with user ID: ${USER_ID}${NC}"

# Try to resolve an intent
RESOLVE_RESPONSE=$(curl -s -X POST "${API_URL}/resolve" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d "{
    \"user_id\": \"${USER_ID}\",
    \"role\": \"user\",
    \"message\": \"What's the weather like in Chicago?\",
    \"context\": {
      \"location\": \"US\"
    }
  }")

echo -e "${GREEN}Resolve response:${NC}"
echo "$RESOLVE_RESPONSE" | jq .

# Extract important information for continuation
RESPONSE_KIND=$(echo $RESOLVE_RESPONSE | jq -r '.kind')
CONVERSATION_ID=$(echo $RESOLVE_RESPONSE | jq -r '.conversation_id // ""')

# If we don't have a conversation ID, try to extract it from other means
if [ -z "$CONVERSATION_ID" ]; then
    # Try to find it in the response somewhere else (structure might vary)
    echo -e "${YELLOW}No conversation_id found in direct response. Looking deeper...${NC}"
    # This is a simplified approach - adapt as needed for your API structure
    CONVERSATION_ID=$(echo $RESOLVE_RESPONSE | grep -o '"conversation_id":"[^"]*"' | head -1 | cut -d'"' -f4)
    
    # If still not found, we'll generate one for testing
    if [ -z "$CONVERSATION_ID" ]; then
        CONVERSATION_ID=$(uuidgen | tr '[:upper:]' '[:lower:]')
        echo -e "${YELLOW}Generated a new conversation ID for testing: ${CONVERSATION_ID}${NC}"
    fi
fi

# Continue the conversation if needed
if [ "$RESPONSE_KIND" = "ask" ]; then
    echo -e "${YELLOW}Step 5: Continuing conversation to provide more information${NC}"
    CONTINUE_RESPONSE=$(curl -s -X POST "${API_URL}/continue" \
      -H "Content-Type: application/json" \
      -H "X-API-Key: ${TENANT_API_KEY}" \
      -d "{
        \"conversation_id\": \"${CONVERSATION_ID}\",
        \"user_id\": \"${USER_ID}\",
        \"message\": \"Tomorrow afternoon\"
      }")
    
    echo -e "${GREEN}Continue response:${NC}"
    echo "$CONTINUE_RESPONSE" | jq .
    
    # Update response kind for potential async check
    RESPONSE_KIND=$(echo $CONTINUE_RESPONSE | jq -r '.kind')
    
    # Extract workflow ID if it's an async response
    if [ "$RESPONSE_KIND" = "async" ]; then
        WORKFLOW_ID=$(echo $CONTINUE_RESPONSE | jq -r '.workflow_id')
    fi
fi

# Check workflow status if async
if [ "$RESPONSE_KIND" = "async" ] && [ ! -z "$WORKFLOW_ID" ]; then
    echo -e "${YELLOW}Step 6: Checking async workflow status${NC}"
    
    # Wait a moment for processing
    echo "Waiting 2 seconds for workflow processing..."
    sleep 2
    
    STATUS_RESPONSE=$(curl -s -X GET "${API_URL}/status/${WORKFLOW_ID}" \
      -H "X-API-Key: ${TENANT_API_KEY}")
    
    echo -e "${GREEN}Workflow status:${NC}"
    echo "$STATUS_RESPONSE" | jq .
fi

# Create a template
echo -e "${YELLOW}Step 7: Creating a test template${NC}"
TEMPLATE_RESPONSE=$(curl -s -X POST "${ADMIN_URL}/templates" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d "{
    \"key\": \"weather_test_template\",
    \"category\": \"intent_router\",
    \"body\": \"How is the weather in {location} on {date}?\",
    \"language\": \"en\"
  }")

echo -e "${GREEN}Template creation response:${NC}"
echo "$TEMPLATE_RESPONSE" | jq .

# Optional: Try to import an OpenAPI spec
echo -e "${YELLOW}Step 8: Testing OpenAPI import (mock example)${NC}"
echo -e "${BLUE}Note: This is an example only and might fail if the endpoint doesn't accept a URL${NC}"

OPENAPI_RESPONSE=$(curl -s -X POST "${ADMIN_URL}/integrations" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d "{
    \"name\": \"Weather API Test\",
    \"spec_url\": \"https://api.example.com/openapi.json\"
  }" || echo "{\"error\": \"OpenAPI import might not be implemented as expected\"}")

echo -e "${GREEN}OpenAPI import response (might be an error if endpoint differs):${NC}"
echo "$OPENAPI_RESPONSE" | jq .

# Test a follow-up question to check context preservation
echo -e "${YELLOW}Step 9: Testing follow-up question with context preservation${NC}"
FOLLOWUP_RESPONSE=$(curl -s -X POST "${API_URL}/resolve" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${TENANT_API_KEY}" \
  -d "{
    \"conversation_id\": \"${CONVERSATION_ID}\",
    \"user_id\": \"${USER_ID}\",
    \"role\": \"user\",
    \"message\": \"How about tomorrow?\",
    \"context\": {
      \"previous_location\": \"Chicago\",
      \"timezone\": \"America/Chicago\"
    }
  }")

echo -e "${GREEN}Follow-up question response:${NC}"
echo "$FOLLOWUP_RESPONSE" | jq .

# Cleanup - optional: Delete the test tenant at the end
# Uncomment this section if you want to delete the tenant after testing
: '
echo -e "${YELLOW}Final Step: Cleaning up - Deleting test tenant${NC}"
DELETE_RESPONSE=$(curl -s -X DELETE "${ADMIN_URL}/tenants/${TENANT_ID}" \
  -H "X-API-Key: ${SYSTEM_ADMIN_KEY}")

if [ -z "$DELETE_RESPONSE" ]; then
    echo -e "${GREEN}Successfully deleted test tenant${NC}"
else
    echo -e "${RED}Error deleting tenant:${NC}"
    echo "$DELETE_RESPONSE"
fi
'

echo -e "${BLUE}Test flow completed!${NC}"
echo -e "${BLUE}Tenant ID: ${TENANT_ID}${NC}"
echo -e "${BLUE}Primary API Key: ${TENANT_API_KEY}${NC}"
echo -e "${BLUE}Secondary API Key: ${NEW_API_KEY}${NC}"
echo -e "${BLUE}============================================================================${NC}"
echo -e "${BLUE}You can now use these credentials for further manual testing.${NC}"
echo -e "${BLUE}To delete this tenant when done, run:${NC}"
echo -e "${YELLOW}curl -X DELETE \"${ADMIN_URL}/tenants/${TENANT_ID}\" -H \"X-API-Key: ${SYSTEM_ADMIN_KEY}\"${NC}"