#!/usr/bin/env python3

import json
import sys
from pathlib import Path

import requests
from requests.exceptions import RequestException


def import_openapi_spec(spec_path: str) -> None:
    """Import an OpenAPI spec into Coherence."""
    try:
        # Read the OpenAPI spec
        with open(spec_path, 'r') as f:
            spec = json.load(f)

        # Prepare the request
        url = 'http://localhost:8001/v1/admin/integrations/import'
        headers = {
            'Content-Type': 'application/json',
            'X-API-Key': '8e-ASxyyl6A2BmR0M_R5krA4J8lIfthagfQA10PM_YQ'
        }
        data = {
            'name': 'Weather API',
            'spec': spec
        }

        # Make the request
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        response.raise_for_status()
        print(f'Status: {response.status_code}')
        print('Import successful!')
        print('Response:', json.dumps(response.json(), indent=2))
        
    except FileNotFoundError:
        print(f'Error: Could not find spec file at {spec_path}')
        sys.exit(1)
    except json.JSONDecodeError:
        print(f'Error: Invalid JSON in spec file {spec_path}')
        sys.exit(1)
    except RequestException as e:
        print(f'Error making request: {str(e)}')
        sys.exit(1)


if __name__ == '__main__':
    spec_path = Path(__file__).parent.parent / 'docs' / 'weather.json'
    import_openapi_spec(str(spec_path))
