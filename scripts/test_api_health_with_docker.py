#!/usr/bin/env python3
"""
Test script for the API Health Monitor with Docker environment.

This script tests the API Health Monitor by:
1. Connecting to the Redis instance in the Docker environment
2. Registering health checks for test APIs
3. Monitoring API health status
4. Testing the circuit breaker integration
"""

import asyncio
import logging
import os

import httpx

from src.coherence.core.config import settings
from src.coherence.core.redis_client import get_redis_client
from src.coherence.monitoring.metrics_collector import TenantMetricsCollector
from src.coherence.openapi_adapter.health_monitor import (
    ApiHealthMonitor,
    ApiStatus,
    HealthCheckConfig,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Test API configurations - using real endpoints that succeed, fail, or timeout
TEST_APIS = [
    {
        "api_key": "httpbin_success",
        "endpoint": "https://httpbin.org/status/200",
        "expected_status": 200,
        "description": "Always succeeds with 200"
    },
    {
        "api_key": "httpbin_fail",
        "endpoint": "https://httpbin.org/status/500",
        "expected_status": 200,  # This will fail because endpoint returns 500
        "description": "Always fails with 500"
    },
    {
        "api_key": "httpbin_delay",
        "endpoint": "https://httpbin.org/delay/3",
        "timeout_ms": 2000,  # 2 second timeout for a 3 second response
        "expected_status": 200,
        "description": "Will timeout (3s delay with 2s timeout)"
    },
]


class DockerHealthMonitorTester:
    """Test harness for the API Health Monitor with Docker environment."""

    def __init__(self):
        """Initialize the tester."""
        self.health_monitor = None
        self.redis_client = None
        self.metrics_collector = None
        self.status_changes = []

    async def setup(self):
        """Set up the test environment using Docker container Redis."""
        # Override Redis settings to use Docker environment
        settings.REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")
        settings.REDIS_PORT = int(os.environ.get("REDIS_PORT", "6380"))  # Docker Redis port
        
        logger.info(f"Connecting to Redis at {settings.REDIS_HOST}:{settings.REDIS_PORT}")
        
        # Get Redis client using settings
        self.redis_client = await get_redis_client()
        
        # Try a simple Redis operation to verify connection
        try:
            await self.redis_client.set("health_monitor_test", "connected")
            test_result = await self.redis_client.get("health_monitor_test")
            logger.info(f"Redis connection test: {test_result}")
        except Exception as e:
            logger.error(f"Error connecting to Redis: {e}")
            raise
        
        # Create HTTP client with reasonable timeouts
        http_client = httpx.AsyncClient(timeout=10.0)
        
        # Create metrics collector
        self.metrics_collector = TenantMetricsCollector()
        
        # Create health monitor with Docker Redis
        self.health_monitor = ApiHealthMonitor(
            redis_client=self.redis_client,
            metrics_collector=self.metrics_collector,
            http_client=http_client,
            namespace="docker_health_monitor_test",
        )
        
        # Register status change callback
        await self.health_monitor.register_status_change_callback(self.status_change_callback)
        
        logger.info("Test environment set up with Docker Redis")

    def status_change_callback(self, api_key: str, old_status: ApiStatus, new_status: ApiStatus):
        """Callback for API status changes."""
        logger.info(f"API status change: {api_key} - {old_status} -> {new_status}")
        self.status_changes.append({
            "api_key": api_key,
            "old_status": old_status,
            "new_status": new_status,
            "timestamp": asyncio.get_event_loop().time()
        })

    async def register_test_apis(self):
        """Register health checks for test APIs."""
        for api_config in TEST_APIS:
            config = HealthCheckConfig(
                api_key=api_config["api_key"],
                endpoint=api_config["endpoint"],
                method="GET",
                expected_status=api_config.get("expected_status", 200),
                timeout_ms=api_config.get("timeout_ms", 5000),
                check_interval_seconds=10,  # Check every 10 seconds
                consecutive_failures_threshold=2,  # Mark as DOWN after 2 failures
                consecutive_successes_for_recovery=2,  # Mark as HEALTHY after 2 successes
            )
            
            await self.health_monitor.register_health_check(config)
            logger.info(f"Registered health check for {api_config['api_key']} - {api_config.get('description', '')}")

    async def run_health_checks_once(self):
        """Run health checks once for all registered APIs."""
        results = {}
        
        # Check each API individually
        for api_config in TEST_APIS:
            api_key = api_config["api_key"]
            try:
                logger.info(f"Checking health for {api_key}...")
                result = await self.health_monitor.check_health(api_key)
                results[api_key] = result
                logger.info(
                    f"Health check for {api_key}: {result.status} "
                    f"(status_code={result.status_code}, response_time={result.response_time_ms:.2f}ms)"
                )
            except Exception as e:
                logger.error(f"Error checking health for {api_key}: {e}")
                results[api_key] = None
        
        return results

    async def monitor_for_duration(self, duration_seconds=60):
        """Start monitoring and observe for a duration."""
        # Start monitoring
        await self.health_monitor.start_monitoring()
        logger.info(f"Started monitoring for {duration_seconds} seconds...")
        
        # Print status every 10 seconds
        start_time = asyncio.get_event_loop().time()
        while asyncio.get_event_loop().time() - start_time < duration_seconds:
            # Get current status
            statuses = {}
            for api_config in TEST_APIS:
                api_key = api_config["api_key"]
                status = await self.health_monitor.get_status(api_key)
                statuses[api_key] = status
            
            logger.info(f"Current API statuses: {statuses}")
            logger.info(f"Status changes so far: {len(self.status_changes)}")
            
            # Wait 10 seconds
            await asyncio.sleep(10)
            
        # Stop monitoring
        await self.health_monitor.stop_monitoring()
        logger.info("Stopped monitoring")
        
        # Final status check
        final_statuses = {}
        for api_config in TEST_APIS:
            api_key = api_config["api_key"]
            status = await self.health_monitor.get_status(api_key)
            final_statuses[api_key] = status
            
            # Get history
            history = await self.health_monitor.get_status_history(api_key, limit=5)
            logger.info(f"Status history for {api_key} (last 5 entries):")
            for entry in history:
                logger.info(
                    f"  {entry.timestamp.isoformat()}: {entry.status} "
                    f"(status_code={entry.status_code}, response_time={entry.response_time_ms:.2f}ms)"
                )
            
        return final_statuses

    async def run_tests(self):
        """Run all tests."""
        try:
            logger.info("Setting up test environment...")
            await self.setup()
            
            logger.info("\n=== Test 1: Register Test APIs ===")
            await self.register_test_apis()
            
            logger.info("\n=== Test 2: Initial Health Checks ===")
            await self.run_health_checks_once()
            
            logger.info("\n=== Test 3: Monitor for 60 seconds ===")
            final_statuses = await self.monitor_for_duration(60)
            
            logger.info("\n=== Test Results ===")
            logger.info(f"Final API statuses: {final_statuses}")
            logger.info(f"Status changes detected: {len(self.status_changes)}")
            for change in self.status_changes:
                logger.info(f"  {change['api_key']}: {change['old_status']} -> {change['new_status']}")
            
            logger.info("\nTests completed!")
            
        except Exception as e:
            logger.exception(f"Error during tests: {e}")
        finally:
            # Clean up
            if self.health_monitor:
                await self.health_monitor.stop_monitoring()
            if self.redis_client:
                await self.redis_client.close()


async def main():
    """Main entry point."""
    tester = DockerHealthMonitorTester()
    await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())