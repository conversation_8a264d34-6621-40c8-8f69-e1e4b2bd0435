#!/usr/bin/env python3
"""
<PERSON>ript to fix template actions format from list to dictionary.

This script fixes templates that have 'actions' stored as lists instead of dictionaries,
which causes validation errors in the admin dashboard.
"""

import argparse
import asyncio
import json
import logging
from uuid import UUID

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import models only after setting up logging
from src.coherence.db.session import async_db_url
from src.coherence.models.template import Template


async def fix_template_actions(template_id: UUID = None, dry_run: bool = False):
    """
    Fix templates with actions stored as lists instead of dictionaries.
    
    Args:
        template_id: Optional template ID to fix a specific template. If None, fixes all templates.
        dry_run: If True, only log what would be changed without actually making changes.
    """
    # Use the URL from the session module
    engine = create_async_engine(async_db_url)
    async_session = sessionmaker(engine, expire_on_commit=False, class_=AsyncSession)
    
    logger.info("Starting template actions fix script")
    
    async with async_session() as session:
        # Query to find templates with actions as lists
        if template_id:
            logger.info(f"Looking for template with ID: {template_id}")
            stmt = select(Template).where(Template.id == template_id)
        else:
            logger.info("Looking for all templates with list-type actions")
            stmt = select(Template)
        
        result = await session.execute(stmt)
        templates = result.scalars().all()
        
        templates_to_fix = []
        for template in templates:
            if not template.actions:
                continue
                
            if isinstance(template.actions, list):
                templates_to_fix.append(template)
                logger.info(f"Found template with list actions: ID={template.id}, key={template.key}")
            
        logger.info(f"Found {len(templates_to_fix)} templates to fix")
        
        if not templates_to_fix:
            logger.info("No templates need to be fixed")
            return
            
        # Fix templates
        for template in templates_to_fix:
            logger.info(f"Processing template: {template.id} ({template.key})")
            
            # Different handling based on non-empty or empty lists
            if template.actions and len(template.actions) > 0:
                # Extract the first action from the list
                first_action = template.actions[0]
                # Create a dictionary with the first action under a key
                new_actions = {"api_action": first_action}
                logger.info(f"Converting list with {len(template.actions)} items to dictionary with key 'api_action'")
            else:
                # Empty list becomes empty dict
                new_actions = {}
                logger.info("Converting empty list to empty dictionary")
            
            # Show the before/after
            logger.info(f"BEFORE: {json.dumps(template.actions)}")
            logger.info(f"AFTER: {json.dumps(new_actions)}")
            
            if not dry_run:
                # Update the template in the database
                update_stmt = (
                    update(Template)
                    .where(Template.id == template.id)
                    .values(actions=new_actions)
                )
                await session.execute(update_stmt)
                
            logger.info(f"{'Would update' if dry_run else 'Updated'} template: {template.id}")
            
        if not dry_run:
            await session.commit()
            logger.info("Successfully committed changes to database")
        else:
            logger.info("Dry run completed - no changes made to database")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Fix template actions format from list to dictionary")
    parser.add_argument("--template-id", type=UUID, help="Specific template ID to fix")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be changed without making changes")
    
    args = parser.parse_args()
    
    asyncio.run(fix_template_actions(template_id=args.template_id, dry_run=args.dry_run))
    logger.info("Script completed successfully")