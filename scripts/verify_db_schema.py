#!/usr/bin/env python3
"""
Verify database schema matches SQLAlchemy models.
"""

import sys
from sqlalchemy import inspect, create_engine
from sqlalchemy.sql import text
from src.coherence.db.session import engine
from src.coherence.db.base import Base
from src.coherence.models import *


def verify_schema():
    """Verify database schema matches models."""
    inspector = inspect(engine)
    
    # Get all table names from the database
    db_tables = set(inspector.get_table_names())
    
    # Get all table names from SQLAlchemy models
    model_tables = set(Base.metadata.tables.keys())
    
    print("=== Database Schema Verification ===\n")
    
    # Check for tables in database but not in models
    extra_tables = db_tables - model_tables
    if extra_tables:
        print("⚠️  Tables in database but not in models:")
        for table in sorted(extra_tables):
            print(f"   - {table}")
        print()
    
    # Check for tables in models but not in database
    missing_tables = model_tables - db_tables
    if missing_tables:
        print("❌ Tables in models but not in database:")
        for table in sorted(missing_tables):
            print(f"   - {table}")
        print()
    
    # Check each table's columns
    print("📊 Table Column Verification:")
    for table_name in sorted(model_tables & db_tables):
        model_table = Base.metadata.tables[table_name]
        db_columns = {col['name'] for col in inspector.get_columns(table_name)}
        model_columns = {col.name for col in model_table.columns}
        
        extra_cols = db_columns - model_columns
        missing_cols = model_columns - db_columns
        
        if extra_cols or missing_cols:
            print(f"\n   Table: {table_name}")
            if extra_cols:
                print(f"     ⚠️  Extra columns in DB: {', '.join(sorted(extra_cols))}")
            if missing_cols:
                print(f"     ❌ Missing columns in DB: {', '.join(sorted(missing_cols))}")
        else:
            print(f"   ✅ {table_name} - columns match")
    
    # Summary
    print("\n=== Summary ===")
    if not extra_tables and not missing_tables:
        print("✅ All tables match between database and models")
    else:
        print("⚠️  Schema differences detected")
        
    # Check for any pending migrations
    with engine.connect() as conn:
        result = conn.execute(text("SELECT version_num FROM alembic_version"))
        current_rev = result.scalar()
        print(f"\n📌 Current migration: {current_rev}")
    
    return len(extra_tables) == 0 and len(missing_tables) == 0


if __name__ == "__main__":
    success = verify_schema()
    sys.exit(0 if success else 1)