#!/usr/bin/env python
"""
Test script to verify template deduplication logic.
"""

import asyncio
import logging
import uuid

from sqlalchemy import func, select

from scripts.add_weather_intent import add_weather_intent_template
from src.coherence.db.session import async_session
from src.coherence.models.template import Template

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_template_deduplication():
    """Test that the template deduplication works correctly."""
    logger.info("Starting template deduplication test...")
    
    db = async_session()
    try:
        # First count how many WEATHER_QUERY templates exist
        query = select(func.count()).select_from(Template).where(
            Template.key == "WEATHER_QUERY",
            Template.category == "intent_router"
        )
        result = await db.execute(query)
        initial_count = result.scalar()
        
        logger.info(f"Initial WEATHER_QUERY template count: {initial_count}")
        
        # Try to add a global template
        global_template = await add_weather_intent_template(db, None)
        
        # Get an existing tenant ID
        tenant_query = select(Template.tenant_id).where(
            Template.tenant_id.is_not(None)
        ).limit(1)
        tenant_result = await db.execute(tenant_query)
        tenant_id = tenant_result.scalar_one_or_none()
        
        # Fall back to a random UUID if no tenant found
        if not tenant_id:
            logger.warning("No existing tenant found, using random UUID (test will fail)")
            tenant_id = uuid.uuid4()
        tenant_template = await add_weather_intent_template(db, tenant_id)
        
        # Try adding the same templates again
        global_template_again = await add_weather_intent_template(db, None)
        tenant_template_again = await add_weather_intent_template(db, tenant_id)
        
        # Verify the same templates were returned
        assert global_template.id == global_template_again.id, "Global template IDs should match"
        assert tenant_template.id == tenant_template_again.id, "Tenant template IDs should match"
        
        # Count templates again
        query = select(func.count()).select_from(Template).where(
            Template.key == "WEATHER_QUERY",
            Template.category == "intent_router"
        )
        result = await db.execute(query)
        final_count = result.scalar()
        
        # If templates already existed, count will remain the same
        # If they didn't exist, count will increase by the number of templates added
        expected_min = initial_count  # No new templates added (all found)
        expected_max = initial_count + 2  # Both templates added (none found)
        assert expected_min <= final_count <= expected_max, f"Expected between {expected_min} and {expected_max} templates, found {final_count}"
        
        logger.info(f"Final WEATHER_QUERY template count: {final_count}")
        logger.info("Template deduplication test passed!")
        
        # Clean up test tenant template (but leave global one)
        if tenant_template:
            await db.delete(tenant_template)
            await db.commit()
            logger.info(f"Cleaned up test tenant template: {tenant_template.id}")
    finally:
        await db.close()

async def main():
    """Main entry point."""
    try:
        await test_template_deduplication()
        logger.info("Template deduplication test completed successfully!")
    except AssertionError as e:
        logger.error(f"Test failed: {e}")
    except Exception as e:
        logger.error(f"Error during testing: {e}")

if __name__ == "__main__":
    asyncio.run(main())