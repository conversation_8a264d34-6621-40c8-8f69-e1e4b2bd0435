#!/bin/bash

# <PERSON>ript to apply all fixes for the template system and audit logging
set -e  # Exit on error

echo "Applying fixes for the Coherence system..."

# 1. Fix the template trigger in the database
echo "Fixing template trigger in database..."
psql -U postgres -d coherence << 'EOF'
-- Fix the template trigger function
DROP TRIGGER IF EXISTS version_template ON templates;
DROP FUNCTION IF EXISTS save_template_version CASCADE;

-- Create a new function that correctly maps the fields
CREATE OR REPLACE FUNCTION save_template_version()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO template_versions (
        id, 
        template_id, 
        tenant_id, 
        version, 
        content,
        body,
        actions,
        parameters
    )
    VALUES (
        gen_random_uuid(),
        OLD.id,
        OLD.tenant_id,
        (SELECT COALESCE(MAX(version), 0) + 1 FROM template_versions WHERE template_id = OLD.id),
        to_jsonb(OLD),
        OLD.body,
        OLD.actions,
        OLD.parameters
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Re-create the trigger
CREATE TRIGGER version_template
BEFORE UPDATE ON templates
FOR EACH ROW EXECUTE FUNCTION save_template_version();
EOF

echo "Template trigger fixed successfully!"

# 2. Restart the API server to apply code changes
echo "Restarting API server..."
docker-compose restart coherence-api

echo "All fixes applied successfully!"