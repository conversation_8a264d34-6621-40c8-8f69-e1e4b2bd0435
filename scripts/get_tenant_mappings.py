#!/usr/bin/env python3
"""Get tenant mappings to find the right tenant ID."""

import asyncio
from pathlib import Path
import sys
from uuid import UUID

# Add src to sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.coherence.db.session import async_session
from src.coherence.models.tenant import Tenant
from src.coherence.models.template import Template
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession


async def get_tenant_mappings(db: AsyncSession):
    """Get all tenant mappings."""
    print("=== All Tenant Mappings ===\n")
    
    # Get all tenants
    tenants_result = await db.execute(select(Tenant))
    tenants = tenants_result.scalars().all()
    
    print(f"Total tenants: {len(tenants)}")
    
    for tenant in tenants:
        print(f"\nTenant ID: {tenant.id}")
        print(f"  Name: {tenant.name}")
        print(f"  Clerk Org ID: {tenant.clerk_org_id}")
        print(f"  Industry Pack: {tenant.industry_pack}")
        print(f"  Compliance Tier: {tenant.compliance_tier}")
        print(f"  Created: {tenant.created_at}")
        
        # Show template count for this tenant
        template_count = await db.scalar(
            select(func.count()).select_from(Template)
            .where(Template.tenant_id == tenant.id)
        )
        print(f"  Templates: {template_count}")


async def main():
    """Main function."""
    async with async_session() as db:
        await get_tenant_mappings(db)


if __name__ == "__main__":
    asyncio.run(main())