#!/usr/bin/env python3
"""
Enhance Unified Templates <PERSON>ript

This script enhances existing unified templates with:
- Integration configuration with base URLs
- Validation rules from parameter schemas
- CRFS auto-select configuration
- Test data for development
- Parameter transformations

Phase 7 Implementation - Migration and Enhancement Tools
"""

import asyncio
import json
import logging
import re
from typing import Any, Dict, List, Optional, Set
from urllib.parse import urlparse

from sqlalchemy import select
from sqlalchemy.orm import selectinload

from src.coherence.db.deps import get_admin_db_session
from src.coherence.models.template import Template, TemplateCategory
from src.coherence.models.integration import APIIntegration

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TemplateEnhancer:
    """Enhances unified templates with additional configuration and metadata."""
    
    def __init__(self):
        self.enhanced_count = 0
        self.error_count = 0
        self.skipped_count = 0
    
    async def enhance_all_templates(self):
        """Enhance all unified templates in the database."""
        async with get_admin_db_session() as db:
            # Query all unified templates
            result = await db.execute(
                select(Template)
                .where(Template.category == TemplateCategory.UNIFIED)
                .options(selectinload(Template.integration))
            )
            templates = result.scalars().all()
            
            logger.info(f"Found {len(templates)} unified templates to enhance")
            
            for template in templates:
                try:
                    enhanced = await self._enhance_template(template)
                    if enhanced:
                        # Update template fields
                        if hasattr(template, 'action_config') and template.action_config:
                            template.action_config = enhanced.get('action_config', template.action_config)
                        
                        if hasattr(template, 'response_format') and template.response_format:
                            template.response_format = enhanced.get('response_format', template.response_format)
                        
                        if hasattr(template, 'test_data'):
                            template.test_data = enhanced.get('test_data')
                        
                        template.version = (template.version or 1) + 1
                        self.enhanced_count += 1
                        logger.info(f"Enhanced template: {template.key}")
                    else:
                        self.skipped_count += 1
                        logger.info(f"Skipped template (already enhanced): {template.key}")
                        
                except Exception as e:
                    self.error_count += 1
                    logger.error(f"Error enhancing template {template.key}: {str(e)}")
            
            # Commit all changes
            await db.commit()
            
        # Print summary
        logger.info(f"\nEnhancement Summary:")
        logger.info(f"  Enhanced: {self.enhanced_count}")
        logger.info(f"  Skipped: {self.skipped_count}")
        logger.info(f"  Errors: {self.error_count}")
        logger.info(f"  Total: {len(templates)}")
    
    async def _enhance_template(self, template: Template) -> Optional[Dict[str, Any]]:
        """Enhance a single template with additional configuration."""
        # Check if already enhanced
        if self._is_already_enhanced(template):
            return None
        
        enhanced = {
            'action_config': template.action_config or {},
            'response_format': template.response_format or {},
            'test_data': {}
        }
        
        # Enhance action configuration
        self._enhance_action_config(enhanced['action_config'], template)
        
        # Enhance response format with CRFS
        self._enhance_response_format(enhanced['response_format'])
        
        # Generate test data
        enhanced['test_data'] = self._generate_test_data(template)
        
        return enhanced
    
    def _is_already_enhanced(self, template: Template) -> bool:
        """Check if template has already been enhanced."""
        # Check for enhanced markers
        action_config = template.action_config or {}
        if 'action' in action_config:
            action = action_config['action']
            if 'integration' in action and 'validation_rules' in action:
                return True
        
        response_format = template.response_format or {}
        if 'response' in response_format:
            response = response_format['response']
            if 'crfs' in response and response['crfs'].get('auto_select'):
                return True
        
        return False
    
    def _enhance_action_config(self, action_config: Dict[str, Any], template: Template):
        """Enhance action configuration with integration details and validation rules."""
        if 'action' not in action_config:
            action_config['action'] = {}
        
        action = action_config['action']
        
        # Add integration configuration
        if template.integration:
            integration = template.integration
            action['integration'] = {
                'base_url': self._extract_base_url(template, integration),
                'api_version': self._extract_api_version(action.get('path', '')),
                'credential_ref': integration.credential_name if integration.credential_name else None,
                'health_check': self._generate_health_check(integration)
            }
        
        # Add validation rules
        if 'parameters' in action_config:
            action['validation_rules'] = self._generate_validation_rules(
                action_config['parameters']
            )
        
        # Add parameter transformations
        action['transformations'] = self._generate_transformations(
            action_config.get('parameters', {})
        )
    
    def _enhance_response_format(self, response_format: Dict[str, Any]):
        """Enhance response format with CRFS auto-selection."""
        if 'response' not in response_format:
            response_format['response'] = {}
        
        response = response_format['response']
        
        if 'crfs' not in response:
            response['crfs'] = {}
        
        crfs = response['crfs']
        
        # Enable auto-selection
        crfs['auto_select'] = True
        
        # Ensure default format is set
        if 'default_format' not in crfs:
            crfs['default_format'] = 'structured'
        
        # Enhance formats if present
        if 'formats' not in crfs:
            crfs['formats'] = {
                'structured': {
                    'sections': [
                        {
                            'type': 'text',
                            'content': 'API response received successfully'
                        }
                    ]
                },
                'text/plain': {
                    'template': 'Response: {{result}}'
                },
                'application/json': {
                    'raw': True
                }
            }
        
        # Add error mapping
        if 'error_mapping' not in response:
            response['error_mapping'] = {
                '400': 'Invalid request parameters',
                '401': 'Authentication failed',
                '403': 'Access forbidden',
                '404': 'Resource not found',
                '500': 'Internal server error'
            }
    
    def _extract_base_url(self, template: Template, integration: Optional[APIIntegration]) -> str:
        """Extract base URL from template or integration."""
        # Check action config first
        action_config = template.action_config or {}
        if 'action' in action_config and 'base_url' in action_config['action']:
            return action_config['action']['base_url']
        
        # Check integration
        if integration and integration.base_url:
            return integration.base_url
        
        # Generate from endpoint if possible
        if template.endpoint:
            # Parse endpoint for potential base URL pattern
            return "${API_BASE_URL}"
        
        return "${API_BASE_URL}"
    
    def _extract_api_version(self, path: str) -> str:
        """Extract API version from path."""
        # Look for version patterns like /v1/, /v2/, etc.
        version_match = re.search(r'/v(\d+)(?:/|$)', path)
        if version_match:
            return f"v{version_match.group(1)}"
        
        return "v1"  # Default
    
    def _generate_health_check(self, integration: Optional[APIIntegration]) -> Dict[str, Any]:
        """Generate health check configuration."""
        # Basic health check config
        return {
            'endpoint': '/health',
            'interval': 300,
            'timeout': 10
        }
    
    def _generate_validation_rules(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate validation rules from parameter schemas."""
        rules = {}
        
        for param_name, param_config in parameters.items():
            if isinstance(param_config, dict):
                rule = {}
                
                # Extract type
                if 'type' in param_config:
                    rule['type'] = param_config['type']
                
                # String validations
                if param_config.get('type') == 'string':
                    if 'minLength' in param_config:
                        rule['min_length'] = param_config['minLength']
                    if 'maxLength' in param_config:
                        rule['max_length'] = param_config['maxLength']
                    if 'pattern' in param_config:
                        rule['pattern'] = param_config['pattern']
                    if 'enum' in param_config:
                        rule['enum'] = param_config['enum']
                
                # Number validations
                elif param_config.get('type') in ['number', 'integer']:
                    if 'minimum' in param_config:
                        rule['minimum'] = param_config['minimum']
                    if 'maximum' in param_config:
                        rule['maximum'] = param_config['maximum']
                
                # Array validations
                elif param_config.get('type') == 'array':
                    if 'minItems' in param_config:
                        rule['min_items'] = param_config['minItems']
                    if 'maxItems' in param_config:
                        rule['max_items'] = param_config['maxItems']
                
                # Required field
                if param_config.get('required'):
                    rule['required'] = True
                
                if rule:
                    rules[param_name] = rule
        
        return rules
    
    def _generate_transformations(self, parameters: Dict[str, Any]) -> Dict[str, List[str]]:
        """Generate parameter transformations based on type and format."""
        transformations = {}
        
        for param_name, param_config in parameters.items():
            if isinstance(param_config, dict):
                transforms = []
                
                # String transformations
                if param_config.get('type') == 'string':
                    # Always trim strings
                    transforms.append('trim')
                    
                    # Format-based transformations
                    format_type = param_config.get('format', '')
                    if format_type == 'email':
                        transforms.append('lowercase')
                    elif format_type == 'uri' or format_type == 'url':
                        transforms.append('lowercase')
                    
                    # Pattern-based transformations
                    pattern = param_config.get('pattern', '')
                    if 'uppercase' in pattern or '[A-Z]' in pattern:
                        transforms.append('uppercase')
                    elif 'lowercase' in pattern or '[a-z]' in pattern:
                        transforms.append('lowercase')
                
                if transforms:
                    transformations[param_name] = transforms
        
        return transformations
    
    def _generate_test_data(self, template: Template) -> Dict[str, Any]:
        """Generate test data for the template."""
        test_data = {
            'mock_responses': {},
            'sample_parameters': {}
        }
        
        # Generate sample parameters
        parameters = template.action_config.get('parameters', {}) if template.action_config else {}
        for param_name, param_config in parameters.items():
            if isinstance(param_config, dict):
                test_data['sample_parameters'][param_name] = self._generate_sample_value(
                    param_config
                )
        
        # Generate mock responses
        test_data['mock_responses']['success'] = {
            'status': 'success',
            'data': {
                'id': '12345',
                'message': 'Operation completed successfully',
                'timestamp': '2025-01-04T20:00:00Z'
            }
        }
        
        test_data['mock_responses']['error'] = {
            'status': 'error',
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': 'Invalid parameters provided'
            }
        }
        
        return test_data
    
    def _generate_sample_value(self, param_config: Dict[str, Any]) -> Any:
        """Generate a sample value based on parameter configuration."""
        param_type = param_config.get('type', 'string')
        
        if param_type == 'string':
            if 'enum' in param_config:
                return param_config['enum'][0] if param_config['enum'] else 'example'
            elif 'format' in param_config:
                format_type = param_config['format']
                if format_type == 'email':
                    return '<EMAIL>'
                elif format_type == 'date':
                    return '2025-01-04'
                elif format_type == 'date-time':
                    return '2025-01-04T20:00:00Z'
                elif format_type == 'uri' or format_type == 'url':
                    return 'https://example.com'
            elif 'pattern' in param_config:
                # Simple pattern-based generation
                pattern = param_config['pattern']
                if 'phone' in pattern.lower():
                    return '******-123-4567'
                elif 'zip' in pattern.lower() or 'postal' in pattern.lower():
                    return '12345'
            
            # Default string
            return param_config.get('example', 'example_value')
        
        elif param_type == 'number' or param_type == 'integer':
            minimum = param_config.get('minimum', 0)
            maximum = param_config.get('maximum', 100)
            return param_config.get('example', (minimum + maximum) // 2)
        
        elif param_type == 'boolean':
            return param_config.get('example', True)
        
        elif param_type == 'array':
            item_config = param_config.get('items', {})
            sample_item = self._generate_sample_value(item_config)
            return [sample_item]
        
        elif param_type == 'object':
            # Generate object with properties
            properties = param_config.get('properties', {})
            sample_obj = {}
            for prop_name, prop_config in properties.items():
                if isinstance(prop_config, dict):
                    sample_obj[prop_name] = self._generate_sample_value(prop_config)
            return sample_obj
        
        return None


async def main():
    """Main entry point for the enhancement script."""
    logger.info("Starting Unified Template Enhancement...")
    
    enhancer = TemplateEnhancer()
    await enhancer.enhance_all_templates()
    
    logger.info("Enhancement complete!")


if __name__ == "__main__":
    asyncio.run(main())