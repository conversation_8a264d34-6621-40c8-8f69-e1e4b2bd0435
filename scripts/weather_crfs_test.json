{"kind": "coherence-response", "crfs_version": "2.0", "metadata": {"title": "Weather Alerts Summary", "description": "Current active weather alerts from the National Weather Service", "source": "Weather API", "operation_id": "get_alerts_active", "method": "GET", "path": "/alerts/active"}, "summary": {"fields": [{"key": "total", "label": "Total Active Alerts", "format": "number", "value": "{{ result.total }}"}, {"key": "land", "label": "Land-based Alerts", "format": "number", "value": "{{ result.land }}"}, {"key": "marine", "label": "Marine Alerts", "format": "number", "value": "{{ result.marine }}"}]}, "format": {"type": "structured", "structure": {"header": "Current Weather Alerts", "sections": [{"id": "status", "type": "conditional", "conditions": {"has_alerts": {"check": "result.total > 0", "content": "⚠️ There are {{ result.total }} active weather alerts", "style": "warning"}, "no_alerts": {"check": "result.total == 0", "content": "✓ No active weather alerts", "style": "success"}}}, {"id": "regions", "type": "list", "title": "Alerts by Region", "data_path": "result.regions", "item_format": {"template": "{{ key }}: {{ value }} alerts"}, "conditionals": {"show_if": "result.regions and len(result.regions) > 0"}}, {"id": "areas", "type": "list", "title": "Alerts by State/Area", "data_path": "result.areas", "item_format": {"template": "{{ key }}: {{ value }} alerts"}, "conditionals": {"show_if": "result.areas and len(result.areas) > 0"}}]}}, "actions": [{"id": "refresh", "label": "<PERSON><PERSON><PERSON>", "mode": "api_call", "endpoint": "/v1/resolve"}], "error_template": "⚠️ Unable to fetch weather alerts: {{ error.message }}"}