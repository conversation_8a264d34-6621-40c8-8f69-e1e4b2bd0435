#!/usr/bin/env python
"""
Simple script to seed global templates directly.
"""

import asyncio
import logging
import uuid
from datetime import datetime

from sqlalchemy import insert
from src.coherence.db.session import async_session
from src.coherence.models.template import Template

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

DEFAULT_TEMPLATES = [
    {
        "key": "DEFAULT_INTENT_ROUTER",
        "category": "intent_router",
        "description": "Default intent router for system",
        "body": """You are an intent recognition system. Analyze the user's message and determine the appropriate action.""",
        "scope": "global",
    },
    {
        "key": "DEFAULT_PARAM_COMPLETE",
        "category": "param_complete",
        "description": "Default parameter completion template",
        "body": """Complete the missing parameters: {{parameters}}""",
        "scope": "global",
    },
    {
        "key": "DEFAULT_RESPONSE_GEN",
        "category": "response_gen",
        "description": "Default response generation template",
        "body": """Generate a helpful response based on: {{context}}""",
        "scope": "global",
    },
    {
        "key": "DEFAULT_ERROR_HANDLER",
        "category": "error_handler",
        "description": "Default error handler template",
        "body": """An error occurred: {{error_message}}. Please try again.""",
        "scope": "global",
    },
    {
        "key": "DEFAULT_RETRIEVAL",
        "category": "retrieval",
        "description": "Default retrieval template for RAG",
        "body": """Retrieve relevant information for: {{query}}""",
        "scope": "global",
    },
]

async def seed_global_templates():
    """Seed global templates directly into the database."""
    async with async_session() as db:
        try:
            for template_data in DEFAULT_TEMPLATES:
                # Check if template already exists
                from sqlalchemy.future import select
                existing = await db.execute(
                    select(Template).where(
                        Template.key == template_data["key"],
                        Template.category == template_data["category"],
                        Template.scope == "global"
                    )
                )
                if existing.scalar():
                    logger.info(f"Template {template_data['key']} already exists, skipping")
                    continue
                
                # Create new template
                template = Template(
                    id=uuid.uuid4(),
                    key=template_data["key"],
                    category=template_data["category"],
                    description=template_data["description"],
                    body=template_data["body"],
                    scope="global",
                    language="en",
                    version=1,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    tenant_id=None,  # Global templates have no tenant
                    protected=True,  # Mark as protected
                )
                db.add(template)
                logger.info(f"Created global template: {template_data['key']}")
            
            await db.commit()
            logger.info("Successfully seeded all global templates")
            
        except Exception as e:
            logger.error(f"Error seeding templates: {str(e)}")
            await db.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(seed_global_templates())