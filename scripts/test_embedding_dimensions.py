#!/usr/bin/env python
"""
Test embedding dimensions at each stage of the pipeline.

This script is a diagnostic tool that checks embedding dimensions at each stage:
1. Direct from OpenAI provider
2. Through the LLM factory
3. Through the VectorIndexer
4. Through the IntentResolver

It helps identify where dimension mismatches occur.

Usage:
python -m scripts.test_embedding_dimensions
"""

import asyncio
import logging
import sys
import time

import structlog

from src.coherence.core.config import settings
from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.llm.providers.openai_provider import OpenAIProvider
from src.coherence.core.qdrant_client import get_qdrant_client
from src.coherence.intent_pipeline.resolver import IntentResolver
from src.coherence.services.vector_indexer import VectorIndexer

# Configure structlog for better formatting
structlog.configure(
    processors=[
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.JSONRenderer(indent=2),
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
)

# Set up standard logging as well
logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = structlog.get_logger(__name__)

# Define test parameters
TEST_TEXT = "This is a test to verify embedding dimensions throughout the pipeline."
EXPECTED_DIMENSION = settings.EMBEDDING_DIMENSION
MODEL = settings.EMBEDDING_MODEL

async def test_openai_provider_direct():
    """Test embedding dimensions directly using OpenAIProvider."""
    start_time = time.time()
    logger.info(
        "Testing OpenAIProvider directly",
        model=MODEL,
        expected_dimension=EXPECTED_DIMENSION
    )
    
    try:
        provider = OpenAIProvider(
            api_key=settings.OPENAI_API_KEY,
            embedding_model=MODEL
        )
        
        logger.info("Generating embedding without dimensions parameter")
        # Try without explicit dimensions parameter first
        try:
            embedding_no_dim = await provider.generate_embedding(
                text=TEST_TEXT
            )
            logger.info(
                "Embedding generated without dimensions param",
                actual_dimension=len(embedding_no_dim),
                expected_dimension=EXPECTED_DIMENSION,
                dimensions_match=(len(embedding_no_dim) == EXPECTED_DIMENSION)
            )
            
            # Validate the dimensions match as expected
            if len(embedding_no_dim) != EXPECTED_DIMENSION:
                logger.error(
                    "DIMENSION MISMATCH: OpenAI provider without explicit dimensions",
                    actual_dimension=len(embedding_no_dim),
                    expected_dimension=EXPECTED_DIMENSION
                )
        except Exception as e:
            logger.error(
                "Error generating embedding without dimensions param",
                error=str(e),
                error_type=type(e).__name__
            )
        
        logger.info("Generating embedding with explicit dimensions parameter")
        # Now with explicit dimensions parameter
        embedding = await provider.generate_embedding(
            text=TEST_TEXT,
            dimensions=EXPECTED_DIMENSION
        )
        
        actual_dimension = len(embedding)
        logger.info(
            "Embedding test results (direct provider)",
            actual_dimension=actual_dimension,
            expected_dimension=EXPECTED_DIMENSION,
            dimensions_match=(actual_dimension == EXPECTED_DIMENSION),
            elapsed_ms=int((time.time() - start_time) * 1000)
        )
        
        return {
            "stage": "openai_provider_direct",
            "success": actual_dimension == EXPECTED_DIMENSION,
            "actual_dimension": actual_dimension,
            "expected_dimension": EXPECTED_DIMENSION
        }
    except Exception as e:
        logger.error(
            "Error testing OpenAIProvider directly",
            error=str(e),
            error_type=type(e).__name__,
            elapsed_ms=int((time.time() - start_time) * 1000)
        )
        return {
            "stage": "openai_provider_direct",
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__
        }

async def test_llm_factory():
    """Test embedding dimensions using LLMFactory."""
    start_time = time.time()
    logger.info(
        "Testing LLMFactory",
        model=MODEL,
        expected_dimension=EXPECTED_DIMENSION
    )
    
    try:
        factory = LLMFactory()
        provider = factory.get_default_provider(fail_fast=True)
        
        logger.info("Generating embedding with LLMFactory provider")
        embedding = await provider.generate_embedding(
            text=TEST_TEXT,
            dimensions=EXPECTED_DIMENSION
        )
        
        actual_dimension = len(embedding)
        logger.info(
            "Embedding test results (LLMFactory)",
            actual_dimension=actual_dimension,
            expected_dimension=EXPECTED_DIMENSION,
            dimensions_match=(actual_dimension == EXPECTED_DIMENSION),
            provider_type=type(provider).__name__,
            elapsed_ms=int((time.time() - start_time) * 1000)
        )
        
        return {
            "stage": "llm_factory",
            "success": actual_dimension == EXPECTED_DIMENSION,
            "actual_dimension": actual_dimension,
            "expected_dimension": EXPECTED_DIMENSION,
            "provider_type": type(provider).__name__
        }
    except Exception as e:
        logger.error(
            "Error testing LLMFactory",
            error=str(e),
            error_type=type(e).__name__,
            elapsed_ms=int((time.time() - start_time) * 1000)
        )
        return {
            "stage": "llm_factory",
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__
        }

async def test_vector_indexer():
    """Test embedding dimensions using VectorIndexer."""
    start_time = time.time()
    logger.info(
        "Testing VectorIndexer",
        model=MODEL,
        expected_dimension=EXPECTED_DIMENSION
    )
    
    try:
        # Create VectorIndexer
        vector_indexer = VectorIndexer()
        
        logger.info("Generating embedding with VectorIndexer")
        embedding = await vector_indexer._generate_embedding(TEST_TEXT)
        
        actual_dimension = len(embedding)
        logger.info(
            "Embedding test results (VectorIndexer)",
            actual_dimension=actual_dimension,
            expected_dimension=EXPECTED_DIMENSION,
            dimensions_match=(actual_dimension == EXPECTED_DIMENSION),
            elapsed_ms=int((time.time() - start_time) * 1000)
        )
        
        return {
            "stage": "vector_indexer",
            "success": actual_dimension == EXPECTED_DIMENSION,
            "actual_dimension": actual_dimension,
            "expected_dimension": EXPECTED_DIMENSION
        }
    except Exception as e:
        logger.error(
            "Error testing VectorIndexer",
            error=str(e),
            error_type=type(e).__name__,
            elapsed_ms=int((time.time() - start_time) * 1000)
        )
        return {
            "stage": "vector_indexer",
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__
        }

async def test_intent_resolver():
    """Test embedding dimensions using IntentResolver."""
    start_time = time.time()
    logger.info(
        "Testing IntentResolver",
        model=MODEL,
        expected_dimension=EXPECTED_DIMENSION
    )
    
    try:
        # Get dependencies
        qdrant_client = await get_qdrant_client()
        factory = LLMFactory()
        provider = factory.get_default_provider(fail_fast=True)
        
        # Create IntentResolver
        resolver = IntentResolver(
            qdrant_client=qdrant_client,
            llm_provider=provider
        )
        
        logger.info("Generating embedding with IntentResolver")
        embedding = await resolver._generate_embedding(TEST_TEXT)
        
        actual_dimension = len(embedding)
        logger.info(
            "Embedding test results (IntentResolver)",
            actual_dimension=actual_dimension,
            expected_dimension=EXPECTED_DIMENSION,
            dimensions_match=(actual_dimension == EXPECTED_DIMENSION),
            elapsed_ms=int((time.time() - start_time) * 1000)
        )
        
        return {
            "stage": "intent_resolver",
            "success": actual_dimension == EXPECTED_DIMENSION,
            "actual_dimension": actual_dimension,
            "expected_dimension": EXPECTED_DIMENSION
        }
    except Exception as e:
        logger.error(
            "Error testing IntentResolver",
            error=str(e),
            error_type=type(e).__name__,
            elapsed_ms=int((time.time() - start_time) * 1000)
        )
        return {
            "stage": "intent_resolver",
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__
        }

async def run_tests():
    """Run all embedding dimension tests."""
    logger.info(
        "Starting embedding dimension test suite",
        expected_dimension=EXPECTED_DIMENSION,
        embedding_model=MODEL
    )
    
    results = []
    
    # Test OpenAIProvider directly
    results.append(await test_openai_provider_direct())
    
    # Test LLMFactory
    results.append(await test_llm_factory())
    
    # Test VectorIndexer
    results.append(await test_vector_indexer())
    
    # Test IntentResolver
    results.append(await test_intent_resolver())
    
    # Summarize results
    success_count = sum(1 for r in results if r.get("success", False))
    logger.info(
        "Test suite completed",
        total_tests=len(results),
        successful_tests=success_count,
        failed_tests=len(results) - success_count
    )
    
    # Print results table
    print("\n======================================================")
    print("EMBEDDING DIMENSION TEST RESULTS")
    print("======================================================")
    print(f"Expected dimension: {EXPECTED_DIMENSION}")
    print(f"Embedding model: {MODEL}")
    print("------------------------------------------------------")
    
    for result in results:
        stage = result.get("stage", "unknown")
        if result.get("success", False):
            status = "✅ PASS"
            details = f"Dim: {result.get('actual_dimension')}"
        else:
            status = "❌ FAIL"
            details = result.get("error", "Unknown error")
        
        print(f"{stage:25} | {status:8} | {details}")
    
    print("======================================================\n")
    
    # Exit with status code
    if success_count < len(results):
        sys.exit(1)
    
    return results

if __name__ == "__main__":
    asyncio.run(run_tests())