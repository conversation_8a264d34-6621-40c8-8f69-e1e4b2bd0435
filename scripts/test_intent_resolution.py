#!/usr/bin/env python
"""
Test script for the Coherence intent resolution pipeline.

This script tests the end-to-end flow of resolving a natural language message
to an intent, extracting parameters, and executing an action.

Usage:
    python -m scripts.test_intent_resolution

Environment variables:
    OPENAI_API_KEY: OpenAI API key for LLM calls
"""

import asyncio
import json
import os
import uuid

# Now import project modules
from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.qdrant_client import QdrantClient
from src.coherence.core.redis_client import RedisClient
from src.coherence.db.session import SessionLocal
from src.coherence.intent_pipeline.orchestrator import ChatOrchestrator
from src.coherence.intent_pipeline.parameter_extraction import ParameterExtractor
from src.coherence.intent_pipeline.resolver import IntentResolver

# Sample test messages
TEST_MESSAGES = [
    "What's the weather like in New York?",
    "Log my weight as 150 pounds",
    "Show me the forecast for San Francisco tomorrow",
    "Record my blood pressure as 120/80",
    "I'm not feeling well, what should I do?",  # Fallback test
]


async def setup_test_environment():
    """Set up the test environment with dependencies."""
    # Create a database session
    db = SessionLocal()

    # Create LLM factory and provider
    llm_factory = LLMFactory()
    openai_api_key = os.environ.get("OPENAI_API_KEY")
    if not openai_api_key:
        print("WARNING: OPENAI_API_KEY not set. Using mock provider instead.")

    # Create the intent resolver
    llm_provider = llm_factory.create_provider(
        name="openai" if openai_api_key else "mock",
        model="gpt-4o",
        api_key=openai_api_key,
    )

    # Create Qdrant client (mock for testing)
    class MockQdrantClient(QdrantClient):
        async def search(self, *args, **kwargs):
            # Mock empty results for testing
            return []

    qdrant_client = MockQdrantClient("localhost", 6333)

    # Create Redis client (mock for testing)
    class MockRedisClient(RedisClient):
        async def get(self, key):
            return None

        async def set(self, key, value, expire=None):
            pass

    redis_client = MockRedisClient("redis://localhost")

    # Create the parameter extractor
    parameter_extractor = ParameterExtractor(
        llm_provider=llm_provider,
        max_completion_rounds=3,
    )

    # Create the intent resolver
    intent_resolver = IntentResolver(
        qdrant_client=qdrant_client,
        llm_provider=llm_provider,
    )

    # Create the chat orchestrator
    orchestrator = ChatOrchestrator(
        db=db,
        redis_client=redis_client,
        intent_resolver=intent_resolver,
        parameter_extractor=parameter_extractor,
        llm_provider=llm_provider,
    )

    return db, orchestrator


async def test_intent_resolution(orchestrator: ChatOrchestrator):
    """Test the intent resolution pipeline with sample messages."""
    tenant_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())
    user_role = "user"

    print("\n===== TESTING INTENT RESOLUTION =====\n")

    for i, message in enumerate(TEST_MESSAGES):
        print(f"\n----- Test {i+1}: '{message}' -----")

        # Process the message
        result = await orchestrator.handle_message(
            tenant_id=tenant_id,
            user_id=user_id,
            user_role=user_role,
            message=message,
        )

        # Print the result
        print(f"Result type: {result.get('type')}")

        if result.get("type") == "ask":
            print(f"Missing field: {result.get('missing_field')}")
            print(f"Question: {result.get('question')}")

            # Simulate user response
            if result.get("missing_field") == "location":
                response = "New York"
            elif result.get("missing_field") == "metric_type":
                response = "weight"
            elif result.get("missing_field") == "value":
                response = "150 pounds"
            else:
                response = "I don't know"

            print(f"User response: {response}")

            # Continue the conversation
            continue_result = await orchestrator.continue_conversation(
                tenant_id=tenant_id,
                conversation_id=str(uuid.uuid4()),  # This would normally be stored
                user_id=user_id,
                message=response,
            )

            print(f"Continuation result type: {continue_result.get('type')}")
            if continue_result.get("type") == "action":
                print(f"Outcome: {continue_result.get('outcome')}")

        elif result.get("type") == "action":
            print(f"Outcome: {result.get('outcome')}")

        elif result.get("type") == "reply":
            print(f"Response: {result.get('outcome')}")

        elif result.get("type") == "fallback":
            print(f"Fallback response: {result.get('outcome')}")

        else:
            print(f"Unexpected result type: {json.dumps(result, indent=2)}")


async def main():
    """Run the test script."""
    # Set up the test environment
    db, orchestrator = await setup_test_environment()

    try:
        # Test intent resolution
        await test_intent_resolution(orchestrator)
    finally:
        # Clean up resources
        db.close()


if __name__ == "__main__":
    asyncio.run(main())
