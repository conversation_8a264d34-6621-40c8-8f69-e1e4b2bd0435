"""
<PERSON><PERSON><PERSON> to apply fixes for the coherence system.

This script applies all necessary fixes to the coherence system, including:
1. Database trigger fix for template versions
2. Database migration for the Alembic chain
3. UUID validation fix for audit logging and tenant context
"""

import asyncio
import logging
import os
import time
import uuid

from sqlalchemy import text

from src.coherence.db.session import async_session

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def fix_template_trigger():
    """Fix the template trigger in the database."""
    logger.info("Fixing template trigger in database...")

    try:
        async with async_session() as session:
            # Drop the existing trigger
            await session.execute(
                text("DROP TRIGGER IF EXISTS version_template ON templates;")
            )

            # Drop the existing function
            await session.execute(
                text("DROP FUNCTION IF EXISTS save_template_version CASCADE;")
            )

            # Create a new function that correctly maps the fields
            trigger_function = """
            CREATE OR REPLACE FUNCTION save_template_version()
            RETURNS TRIGGER AS $$
            BEGIN
                INSERT INTO template_versions (
                    id, 
                    template_id, 
                    tenant_id, 
                    version, 
                    content,
                    body,
                    actions,
                    parameters
                )
                VALUES (
                    gen_random_uuid(),
                    OLD.id,
                    OLD.tenant_id,
                    (SELECT COALESCE(MAX(version), 0) + 1 FROM template_versions WHERE template_id = OLD.id),
                    to_jsonb(OLD),
                    OLD.body,
                    OLD.actions,
                    OLD.parameters
                );
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """
            await session.execute(text(trigger_function))

            # Re-create the trigger
            await session.execute(
                text(
                    """
            CREATE TRIGGER version_template
            BEFORE UPDATE ON templates
            FOR EACH ROW EXECUTE FUNCTION save_template_version();
            """
                )
            )

            # Commit the changes
            await session.commit()

            logger.info("Template trigger fixed successfully!")
            return True
    except Exception as e:
        logger.error(f"Error fixing template trigger: {str(e)}")
        return False


async def create_audit_log_uuid_check():
    """Create a trigger to validate UUID formats for audit logs."""
    logger.info("Creating UUID validation trigger for audit logs...")

    try:
        async with async_session() as session:
            # Drop the function if it exists
            await session.execute(
                text("DROP FUNCTION IF EXISTS validate_uuid_format CASCADE;")
            )

            # Create a function to validate UUID format
            validation_function = """
            CREATE OR REPLACE FUNCTION validate_uuid_format()
            RETURNS TRIGGER AS $$
            BEGIN
                -- Validate tenant_id
                IF NEW.tenant_id IS NOT NULL THEN
                    -- This will raise an error if the UUID is invalid
                    PERFORM NEW.tenant_id::uuid;
                END IF;
                
                -- Validate user_id
                IF NEW.user_id IS NOT NULL THEN
                    -- This will raise an error if the UUID is invalid
                    PERFORM NEW.user_id::uuid;
                END IF;
                
                RETURN NEW;
            EXCEPTION WHEN OTHERS THEN
                RAISE WARNING 'Invalid UUID format in audit_logs: %', SQLERRM;
                IF NEW.tenant_id IS NOT NULL AND NEW.tenant_id::text !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
                    NEW.tenant_id = NULL;
                END IF;
                IF NEW.user_id IS NOT NULL AND NEW.user_id::text !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
                    NEW.user_id = NULL;
                END IF;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """
            await session.execute(text(validation_function))

            # Drop the trigger if it exists (as a separate statement)
            await session.execute(
                text(
                    "DROP TRIGGER IF EXISTS validate_uuid_format_trigger ON audit_logs;"
                )
            )

            # Create the trigger (as a separate statement)
            await session.execute(
                text(
                    """
            CREATE TRIGGER validate_uuid_format_trigger
            BEFORE INSERT ON audit_log
            FOR EACH ROW EXECUTE FUNCTION validate_uuid_format();
            """
                )
            )

            # Commit the changes
            await session.commit()

            logger.info("Audit log UUID validation trigger created successfully!")
            return True
    except Exception as e:
        logger.error(f"Error creating audit log UUID validation trigger: {str(e)}")
        return False


async def apply_alembic_migration():
    """Apply Alembic migrations with proper error handling and recovery."""
    logger.info("Starting Alembic migration process...")

    try:
        # First, ensure critical enum types exist (for backwards compatibility)
        async with async_session() as session:
            logger.info("Ensuring critical enum types exist...")
            
            # Create validation_status enum if it doesn't exist
            await session.execute(text("""
                DO $$ BEGIN
                    CREATE TYPE validation_status AS ENUM ('pending', 'valid', 'invalid', 'outdated');
                EXCEPTION 
                    WHEN duplicate_object THEN 
                        RAISE NOTICE 'validation_status type already exists';
                END $$;
            """))
            
            # Create spec_format enum if it doesn't exist  
            await session.execute(text("""
                DO $$ BEGIN
                    CREATE TYPE spec_format AS ENUM ('openapi', 'fapi', 'bapi');
                EXCEPTION 
                    WHEN duplicate_object THEN 
                        RAISE NOTICE 'spec_format type already exists';
                END $$;
            """))
            
            # Create auth_type enum if it doesn't exist
            await session.execute(text("""
                DO $$ BEGIN
                    CREATE TYPE auth_type AS ENUM ('api_key', 'oauth2', 'basic', 'bearer', 'custom', 'none');
                EXCEPTION 
                    WHEN duplicate_object THEN 
                        RAISE NOTICE 'auth_type type already exists';
                END $$;
            """))
            
            # Create integration_status enum if it doesn't exist
            await session.execute(text("""
                DO $$ BEGIN
                    CREATE TYPE integration_status AS ENUM ('active', 'inactive', 'error');
                EXCEPTION 
                    WHEN duplicate_object THEN 
                        RAISE NOTICE 'integration_status type already exists';
                END $$;
            """))
            
            # Create template_category enum if it doesn't exist
            await session.execute(text("""
                DO $$ BEGIN
                    CREATE TYPE template_category AS ENUM ('scheduling', 'communication', 'clinical', 'administrative', 'billing', 'reporting', 'documentation', 'action', 'unified');
                EXCEPTION 
                    WHEN duplicate_object THEN 
                        RAISE NOTICE 'template_category type already exists';
                END $$;
            """))
            
            await session.commit()
            logger.info("Enum types ensured successfully!")

        # Now run Alembic migrations
        logger.info("Running Alembic migrations...")
        
        # Check current migration state
        current_result = os.popen("alembic current 2>&1").read()
        logger.info(f"Current migration state: {current_result}")
        
        # Check if we need to stamp the database first (for fresh installs)
        if "FAILED" in current_result or "Context impl PostgresqlImpl" not in current_result:
            logger.warning("Database appears uninitialized, stamping with base migration")
            stamp_result = os.popen("alembic stamp base 2>&1").read()
            logger.info(f"Stamp result: {stamp_result}")
        
        # Run upgrade to head
        logger.info("Upgrading database to latest migration...")
        upgrade_result = os.popen("alembic upgrade head 2>&1").read()
        
        # Check if upgrade was successful
        if "ERROR" in upgrade_result or "error" in upgrade_result.lower():
            logger.error(f"Migration error detected: {upgrade_result}")
            
            # Try to handle specific common errors
            if "relation" in upgrade_result and "already exists" in upgrade_result:
                logger.warning("Tables already exist, attempting to reconcile...")
                # Mark current state as head if tables exist
                stamp_head_result = os.popen("alembic stamp head 2>&1").read()
                logger.info(f"Stamped as head: {stamp_head_result}")
                return True
            elif "multiple heads" in upgrade_result.lower():
                logger.error("Multiple migration heads detected! Manual intervention required.")
                return False
            else:
                # Log the error but don't fail startup
                logger.error("Migration failed but allowing startup to continue")
                return True
        else:
            logger.info("Alembic migrations applied successfully!")
            
            # Verify final state
            final_result = os.popen("alembic current 2>&1").read()
            logger.info(f"Final migration state: {final_result}")
            
            return True
            
    except Exception as e:
        logger.error(f"Error during migration process: {str(e)}")
        logger.info("Migration error is non-fatal, continuing with startup...")
        return True


async def test_uuid_validation():
    """Test the UUID validation fixes."""
    logger.info("Testing UUID validation...")

    try:
        # Test validation with valid UUID
        valid_uuid = "123e4567-e89b-12d3-a456-************"
        uuid_obj = uuid.UUID(valid_uuid)
        logger.info(f"Valid UUID parsed successfully: {uuid_obj}")

        # Test validation with invalid UUID (should raise ValueError)
        invalid_uuid = "not-a-uuid"
        try:
            uuid.UUID(invalid_uuid)
            logger.error(
                "UUID validation test failed: Invalid UUID was parsed without error"
            )
            return False
        except ValueError:
            logger.info("UUID validation correctly caught invalid UUID format")

        logger.info("UUID validation test passed successfully!")
        return True
    except Exception as e:
        logger.error(f"Error testing UUID validation: {str(e)}")
        return False


async def main():
    """Apply all fixes."""
    logger.info("Starting to apply fixes...")

    # Test UUID validation first (doesn't require database)
    uuid_validation_result = await test_uuid_validation()

    # Wait for database to be ready
    retries = 5
    while retries > 0:
        try:
            async with async_session() as session:
                await session.execute(text("SELECT 1"))
                logger.info("Database connection successful!")
                break
        except Exception as e:
            logger.warning(f"Database not ready yet: {str(e)}")
            retries -= 1
            if retries == 0:
                logger.error("Failed to connect to database after multiple attempts")
                return
            time.sleep(2)

    # Apply fixes
    trigger_fix_result = await fix_template_trigger()

    # Create audit log UUID validation trigger
    audit_uuid_check_result = await create_audit_log_uuid_check()

    # Try to apply Alembic migration if available
    try:
        migration_result = await apply_alembic_migration()
    except Exception:
        logger.info("Skipping Alembic migration (not required)")
        migration_result = True

    if (
        trigger_fix_result
        and migration_result
        and uuid_validation_result
        and audit_uuid_check_result
    ):
        logger.info("All fixes applied successfully!")
    else:
        logger.warning("Some fixes may not have been applied successfully.")


if __name__ == "__main__":
    asyncio.run(main())
