"""Test regenerating Weather API templates with proper CRFS format."""

import asyncio
import json
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import select
from src.coherence.db.session import async_session
from src.coherence.models.integration import APIIntegration, APIEndpoint
from src.coherence.openapi_adapter.action_generator import ActionGenerator
from src.coherence.models.template import Template

async def test_regenerate_weather_templates():
    """Test regenerating Weather templates and check CRFS format."""
    async with async_session() as db:
        # Find Weather integration
        query = select(APIIntegration).where(APIIntegration.name == "Weather")
        result = await db.execute(query)
        integration = result.scalar_one_or_none()
        
        if not integration:
            print("Weather integration not found")
            return
            
        print(f"Found integration: {integration.name}")
        
        # Find alerts/active endpoint
        query = select(APIEndpoint).where(
            APIEndpoint.integration_id == integration.id,
            APIEndpoint.path == "/alerts/active"
        )
        result = await db.execute(query)
        endpoint = result.scalar_one_or_none()
        
        if not endpoint:
            print("Endpoint /alerts/active not found")
            return
            
        print(f"Found endpoint: {endpoint.method} {endpoint.path}")
        
        # Create action generator
        generator = ActionGenerator(db)
        
        # Generate templates (but don't save them yet)
        templates = await generator._generate_action_templates(
            endpoint_id=endpoint.id,
            tenant_id=integration.tenant_id,
            api_key="weather",
            base_url=None
        )
        
        print(f"\nGenerated {len(templates)} templates")
        
        # Check the intent template
        intent_template = None
        for template in templates:
            if template.get("category") == "intent_router":
                intent_template = template
                break
                
        if intent_template:
            print(f"\nIntent template key: {intent_template['key']}")
            response_format = intent_template.get("response_format", {})
            print(f"Response format:")
            print(json.dumps(response_format, indent=2)[:500] + "...")
            
            format_type = response_format.get("format", {}).get("type")
            print(f"\nFormat type: {format_type}")
            
            if format_type == "structured":
                structure = response_format.get("format", {}).get("structure", {})
                sections = structure.get("sections", [])
                print(f"Number of sections: {len(sections)}")
                for i, section in enumerate(sections[:3]):
                    print(f"Section {i}: type={section.get('type')}, title={section.get('title', 'N/A')}")
                    
                print("\n✅ SUCCESS: Structured CRFS format generated!")
            else:
                print("\n❌ FAILED: Still using template format")
        else:
            print("\nNo intent template found")
            
        # Check existing template in database
        query = select(Template).where(
            Template.key == "action_intent_weather_get_alerts_active",
            Template.is_active == True
        )
        result = await db.execute(query)
        existing = result.scalar_one_or_none()
        
        if existing:
            print(f"\n\nExisting template in DB:")
            print(f"Key: {existing.key}")
            existing_format = existing.response_format or {}
            format_type = existing_format.get("format", {}).get("type", "N/A")
            print(f"Current format type: {format_type}")
            
            if format_type != "structured":
                print("\n⚠️  The existing template needs to be regenerated to use structured format")

if __name__ == "__main__":
    asyncio.run(test_regenerate_weather_templates())