#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to create an admin API key for a tenant.

This script creates an admin API key for a specified tenant,
which can be used to access the admin endpoints.
"""

import asyncio
import hashlib
import secrets
import sys
from uuid import UUID

from sqlalchemy.future import select

from src.coherence.db.session import async_session
from src.coherence.models.tenant import API<PERSON><PERSON>, Tenant

# Add /app to sys.path to allow for src imports when running script directly
# This is done after all imports to satisfy some linters, but before script logic.
# Use insert(0, ...) to give this path higher precedence.
if "/app" not in sys.path:
    sys.path.insert(0, "/app")

async def create_admin_key(tenant_id_str: str, label: str = "Admin API Key") -> str:
    """Create an admin API key for the specified tenant."""
    try:
        tenant_id = UUID(tenant_id_str)
    except ValueError:
        print(f"Invalid tenant ID: {tenant_id_str}")
        return ""

    async with async_session() as db:
        # Verify tenant exists
        result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
        tenant = result.scalar_one_or_none()
        
        if not tenant:
            print(f"Tenant not found with ID: {tenant_id_str}")
            return ""
        
        # Generate new API key
        api_key_value = f"coh_{secrets.token_urlsafe(32)}"
        api_key_hash = hashlib.sha256(api_key_value.encode()).hexdigest()
        
        # Create API key
        db_api_key = APIKey(
            tenant_id=tenant_id,
            label=label,
            key_hash=api_key_hash,
            revoked=False,
        )
        db.add(db_api_key)
        
        # Update tenant settings to make it an admin
        if not tenant.settings:
            tenant.settings = {}
        tenant.settings["is_admin"] = True
        
        await db.commit()
        print(f"Created admin API key for tenant {tenant.name} (ID: {tenant_id})")
        print(f"API Key: {api_key_value}")
        print("IMPORTANT: Save this key! It will not be shown again.")
        return api_key_value


async def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        print("Usage: python create_admin_key.py <tenant_id> [label]")
        return
    
    tenant_id = sys.argv[1]
    label = sys.argv[2] if len(sys.argv) > 2 else "Admin API Key"
    
    await create_admin_key(tenant_id, label)


if __name__ == "__main__":
    asyncio.run(main())
