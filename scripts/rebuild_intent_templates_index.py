#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to rebuild the intent templates index in Qdrant.

This script extracts intent templates from the database and rebuilds the vector index
for faster intent matching (Tier 1) based on template examples.
"""

import asyncio
import logging
import uuid
from typing import Any, List

from sqlalchemy import select

from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.qdrant_client import QdrantClient
from src.coherence.db.session import async_session
from src.coherence.models.template import Template
from src.coherence.models.tenant import Tenant

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_examples_from_template(body: str) -> List[str]:
    """Extract example phrases from an intent template body.
    
    Args:
        body: The template body text
        
    Returns:
        List of extracted example phrases
    """
    examples = []
    lines = body.split("\n")
    in_examples_section = False
    in_intent_patterns = False
    in_block_examples = False
    
    for line in lines:
        line = line.strip()
        
        # Look for traditional examples section
        if "## LLM-Accessible Examples" in line:
            in_examples_section = True
            continue
            
        # Look for Jinja2 template style intent_patterns block
        if "{% block intent_patterns %}" in line:
            in_intent_patterns = True
            continue
            
        if in_intent_patterns and "{% endblock %}" in line:
            in_intent_patterns = False
            continue
            
        # Look for examples block in Jinja2 templates
        if "{% block examples %}" in line:
            in_block_examples = True
            continue
            
        if in_block_examples and "{% endblock %}" in line:
            in_block_examples = False
            continue
        
        # Process traditional examples
        if in_examples_section:
            if line.startswith("##"):
                # End of examples section
                in_examples_section = False
                continue
                
            if line.startswith("- "):
                # Extract the example text
                example = line[2:].strip()
                if example:
                    examples.append(example)
                    
        # Process intent patterns (OpenAPI style templates)
        elif in_intent_patterns:
            if line.startswith("- "):
                # Extract the pattern
                pattern = line[2:].strip()
                if pattern:
                    # Remove any placeholder variables like {param}
                    import re
                    pattern = re.sub(r'\{[^}]*\}', 'example', pattern)
                    examples.append(pattern)
                    
        # Process example blocks (usually has format "example" -> function())
        elif in_block_examples:
            if "->" in line:
                # Extract just the example part (before the ->)
                example_part = line.split("->")[0].strip().strip('"\'')
                if example_part:
                    examples.append(example_part)
    
    return examples

async def index_intent_template(
    tenant_id: str,
    template: Template,
    llm_provider: Any,
    qdrant_client: QdrantClient,
    collection_name: str
) -> bool:
    """Index a single intent template in Qdrant.
    
    Args:
        tenant_id: Tenant ID
        template: The template to index
        llm_provider: LLM provider for embeddings
        qdrant_client: Qdrant client for vector operations
        collection_name: Name of the collection to index into
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Skip non-intent templates
        if template.category != "intent_router":
            return False
            
        # For templates with metadata stored as SQLAlchemy MetaData objects
        # We need to access it as a JSON/dict properly
        if hasattr(template, 'metadata') and template.metadata is not None:
            if isinstance(template.metadata, dict):
                metadata = template.metadata
            else:
                # Convert SQLAlchemy MetaData to dict if needed
                metadata = template.metadata.to_dict() if hasattr(template.metadata, 'to_dict') else {}
                # If to_dict doesn't exist, try attribute access and build dict manually
                if not metadata and hasattr(template.metadata, 'vector_indexable'):
                    metadata = {
                        'vector_indexable': getattr(template.metadata, 'vector_indexable', False),
                        'response_template_key': getattr(template.metadata, 'response_template_key', None),
                    }
        else:
            metadata = {}
            
        # Skip templates without vector_indexable flag
        if not metadata.get("vector_indexable", False):
            logger.warning(f"Template {template.key} is not vector indexable, skipping.")
            return False
            
        # Get the response template key
        response_template_key = metadata.get("response_template_key")
        if not response_template_key:
            logger.warning(f"No response template key in metadata for template {template.key}")
            return False
            
        # Extract examples from template body
        examples = extract_examples_from_template(template.body)
        if not examples:
            logger.warning(f"No examples found in template {template.key}")
            return False
            
        # Index each example
        successful_examples = 0
        for example in examples:
            try:
                # Generate embedding for the example
                embedding = await llm_provider.generate_embedding(example)
                
                # Add to Qdrant
                await qdrant_client.upsert_intent_vectors(
                    tenant_id=tenant_id,
                    role="user",
                    vectors=[{
                        "id": str(uuid.uuid4()),
                        "vector": embedding,
                        "payload": {
                            "intent": response_template_key,
                            "text": example,
                            "parameters": {},
                            "template_id": str(template.id),
                            "template_key": template.key
                        }
                    }]
                )
                successful_examples += 1
            except Exception as ex:
                logger.error(f"Error indexing example '{example}' from template {template.key}: {ex}")
        
        logger.info(f"Indexed {successful_examples}/{len(examples)} examples from template {template.key}")
        return successful_examples > 0
    except Exception as e:
        logger.error(f"Error indexing template {template.key}: {e}")
        return False

async def rebuild_index_for_tenant(
    tenant_id: uuid.UUID,
    qdrant_client: QdrantClient,
    llm_factory: LLMFactory,
):
    """Rebuild the intent templates index for a specific tenant."""
    # Create collection for this tenant
    collection_name = f"intent_idx_{tenant_id}_user"
    
    # Check if collection exists and recreate it
    try:
        if await qdrant_client.collection_exists(collection_name):
            # Delete and recreate
            await qdrant_client.delete_intent_vectors(
                tenant_id=str(tenant_id),
                role="user"
            )
            logger.info(f"Deleted existing collection: {collection_name}")
    except Exception as e:
        logger.error(f"Error checking/deleting collection: {e}")
    
    # Create the collection
    try:
        await qdrant_client.create_intent_collection(
            tenant_id=str(tenant_id),
            role="user",
            vector_size=384  # Standardized dimension
        )
        logger.info(f"Created collection: {collection_name}")
    except Exception as e:
        logger.error(f"Error creating collection: {e}")
        return
    
    # Get LLM provider for embeddings
    try:
        # Use the default provider with fail_fast=False to fallback to mock provider if needed
        llm_provider = llm_factory.get_default_provider(fail_fast=False)
        logger.info(f"Using provider: {llm_provider.__class__.__name__}")
    except Exception as e:
        logger.error(f"Error getting LLM provider: {e}")
        return
    
    # Get intent templates for this tenant
    async with async_session() as db:
        # Find all intent templates for this tenant
        query = select(Template).where(
            Template.tenant_id == tenant_id,
            Template.category == "intent_router",
        )
        result = await db.execute(query)
        templates = result.scalars().all()
        
        if not templates:
            logger.warning(f"No intent templates found for tenant: {tenant_id}")
            return
        
        logger.info(f"Found {len(templates)} intent templates for tenant: {tenant_id}")
        
        # Index each template
        successful_templates = 0
        for template in templates:
            try:
                success = await index_intent_template(
                    tenant_id=str(tenant_id),
                    template=template,
                    llm_provider=llm_provider,
                    qdrant_client=qdrant_client,
                    collection_name=collection_name
                )
                if success:
                    successful_templates += 1
            except Exception as e:
                logger.error(f"Error indexing template {template.key}: {e}")
        
        logger.info(f"Successfully indexed {successful_templates}/{len(templates)} templates for tenant {tenant_id}")

async def main():
    """Main entry point."""
    logger.info("Starting intent templates index rebuild...")
    
    # Initialize Qdrant client
    qdrant_client = QdrantClient()
    
    # Initialize LLM factory
    llm_factory = LLMFactory()
    
    # Get all tenant IDs
    async with async_session() as db:
        query = select(Tenant.id)
        result = await db.execute(query)
        tenant_ids = [row[0] for row in result.all()]
    
    # Rebuild index for each tenant
    for tenant_id in tenant_ids:
        logger.info(f"Processing tenant: {tenant_id}")
        await rebuild_index_for_tenant(tenant_id, qdrant_client, llm_factory)
    
    logger.info("Intent templates index rebuild completed!")

if __name__ == "__main__":
    asyncio.run(main())