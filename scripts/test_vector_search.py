#!/usr/bin/env python
"""
Simple test script to verify that vector search works with standardized dimensions.
"""

import asyncio
import logging

from src.coherence.core.config import settings
from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.qdrant_client import QdrantClient
from src.coherence.db.session import async_session
from src.coherence.services.vector_indexer import VectorIndexer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test query
TEST_QUERY = "What's the weather like in Seattle today?"

async def test_search():
    """Test vector search with standardized dimensions."""
    # Initialize components
    qdrant_client = QdrantClient()
    llm_factory = LLMFactory()
    vector_indexer = VectorIndexer(qdrant_client=qdrant_client, llm_factory=llm_factory)
    
    async with async_session():
        # List all collections
        collections = await qdrant_client.list_collections()
        logger.info(f"Available collections: {collections}")
        
        # Test intent search
        logger.info(f"Testing intent search with query: '{TEST_QUERY}'")
        
        # Find all tenant collections with intent data
        intent_collections = [c for c in collections if c.startswith("intent_idx_") and c.endswith("_user")]
        
        for collection in intent_collections:
            # Extract tenant_id from collection name
            tenant_id = collection.replace("intent_idx_", "").replace("_user", "")
            
            # Generate embedding for query
            embedding = await vector_indexer._generate_embedding(TEST_QUERY)
            
            # Search for intents
            results = await qdrant_client.search_intents(
                tenant_id=tenant_id,
                role="user",
                query_vector=embedding,
                limit=3,
                score_threshold=0.6
            )
            
            logger.info(f"Results for collection {collection}:")
            for idx, result in enumerate(results):
                logger.info(f"  {idx+1}. Score: {result.get('score', 0):.4f}, Intent: {result.get('intent', 'unknown')}")
                logger.info(f"     Example: {result.get('text', '')}")
                if "parameters" in result:
                    logger.info(f"     Parameters: {result.get('parameters', {})}")
            
            logger.info("-" * 40)
        
        # Test template search
        logger.info(f"Testing template search with query: '{TEST_QUERY}'")
        
        template_collections = [c for c in collections if c.startswith("template_idx_")]
        
        for collection in template_collections:
            # Generate embedding for query
            embedding = await vector_indexer._generate_embedding(TEST_QUERY)
            
            # Search for templates
            results = await vector_indexer.search_templates(
                query=TEST_QUERY,
                limit=3,
                score_threshold=0.6
            )
            
            logger.info("Results for template search:")
            for idx, result in enumerate(results):
                logger.info(f"  {idx+1}. Score: {result.get('score', 0):.4f}, Template: {result.get('template_key', 'unknown')}")
                logger.info(f"     Category: {result.get('template_category', '')}")
            
            logger.info("-" * 40)

async def main():
    """Main function."""
    logger.info(f"Starting vector search test with dimension: {settings.EMBEDDING_DIMENSION}")
    logger.info(f"Using embedding model: {settings.EMBEDDING_MODEL}")
    
    await test_search()
    
    logger.info("Vector search test completed!")

if __name__ == "__main__":
    asyncio.run(main())