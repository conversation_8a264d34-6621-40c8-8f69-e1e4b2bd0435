#!/usr/bin/env python
"""
Template seeding script for Coherence.

This script seeds the database with default templates for each category.
"""

import asyncio
import logging
import uuid

import click
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.db.session import async_session
from src.coherence.models.tenant import Tenant
from src.coherence.template_system.defaults.templates import (
    DEFAULT_ERROR_HANDLER_TEMPLATE,
    DEFAULT_INTENT_ROUTER_TEMPLATE,
    DEFAULT_PARAM_COMPLETE_TEMPLATE,
    DEFAULT_RESPONSE_GEN_TEMPLATE,
    DEFAULT_RETRIEVAL_TEMPLATE,
)
from src.coherence.template_system.services.template_service import TemplateService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


DEFAULT_TEMPLATES = [
    {
        "key": "INTENT_ROUTER_V1",
        "category": "intent_router",
        "body": DEFAULT_INTENT_ROUTER_TEMPLATE,
        "description": "Default intent router template for tier 2 LLM-based routing",
    },
    {
        "key": "PARAM_COMPLETE_V1",
        "category": "param_complete",
        "body": DEFAULT_PARAM_COMPLETE_TEMPLATE,
        "description": "Default parameter completion template for conversational interfaces",
    },
    {
        "key": "RETRIEVAL_V1",
        "category": "retrieval",
        "body": DEFAULT_RETRIEVAL_TEMPLATE,
        "description": "Default retrieval template for RAG-based intent resolution (tier 3)",
    },
    {
        "key": "RESPONSE_GEN_V1",
        "category": "response_gen",
        "body": DEFAULT_RESPONSE_GEN_TEMPLATE,
        "description": "Default response generation template",
    },
    {
        "key": "ERROR_HANDLER_V1",
        "category": "error_handler",
        "body": DEFAULT_ERROR_HANDLER_TEMPLATE,
        "description": "Default error handler template",
    },
]


async def seed_global_templates(db: AsyncSession) -> None:
    """Seed global templates that are available to all tenants."""
    logger.info("Seeding global templates...")

    template_service = TemplateService()

    for template_def in DEFAULT_TEMPLATES:
        try:
            await template_service.create_template(
                db=db,
                key=template_def["key"],
                category=template_def["category"],
                body=template_def["body"],
                scope="global",
                scope_id=None,
                tenant_id=None,
                description=template_def["description"],
            )
            logger.info(f"Created global template: {template_def['key']}")
        except Exception as e:
            logger.error(
                f"Error creating global template {template_def['key']}: {str(e)}"
            )


async def seed_tenant_templates(db: AsyncSession, tenant_id: uuid.UUID) -> None:
    """Seed tenant-specific template overrides."""
    logger.info(f"Seeding tenant templates for tenant {tenant_id}...")

    template_service = TemplateService()

    # For demonstration, we'll create a tenant-specific override for the intent router
    intent_router_override = DEFAULT_INTENT_ROUTER_TEMPLATE.replace(
        "You are a specialized AI assistant",
        f"You are a specialized AI assistant for tenant {tenant_id}",
    )

    try:
        await template_service.create_template(
            db=db,
            key="INTENT_ROUTER_V1",
            category="intent_router",
            body=intent_router_override,
            scope="tenant",
            scope_id=tenant_id,
            tenant_id=tenant_id,
            description="Tenant-specific intent router template",
        )
        logger.info(f"Created tenant-specific template for tenant {tenant_id}")
    except Exception as e:
        logger.error(f"Error creating tenant template: {str(e)}")


async def get_default_tenant(db: AsyncSession) -> Tenant:
    """Get the default tenant."""
    from sqlalchemy import select

    result = await db.execute(select(Tenant))
    tenant = result.scalars().first()

    if not tenant:
        raise ValueError("No tenants found. Please create a tenant first.")

    return tenant


@click.command()
@click.option("--tenant-id", help="Tenant ID to seed templates for (optional)")
def seed_templates(tenant_id: str = None):
    """Seed default templates for Coherence."""

    async def _run():
        async with async_session() as db:
            # Seed global templates
            await seed_global_templates(db)

            # Seed tenant-specific templates if specified
            if tenant_id:
                await seed_tenant_templates(db, uuid.UUID(tenant_id))
            else:
                # Seed for default tenant
                try:
                    default_tenant = await get_default_tenant(db)
                    await seed_tenant_templates(db, default_tenant.id)
                except ValueError as e:
                    logger.warning(str(e))

    asyncio.run(_run())
    logger.info("Template seeding complete!")


if __name__ == "__main__":
    seed_templates()
