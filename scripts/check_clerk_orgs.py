# This is a simple script to output details of the Clerk environment values
# to help debug Clerk organization issues

import json
import os


def check_clerk_environment():
    """Check and display Clerk environment variables."""
    print("=== Clerk Environment Variables ===")
    
    # Look for all environment variables related to Clerk
    clerk_vars = {}
    for key, value in os.environ.items():
        if "CLERK" in key.upper():
            # Mask sensitive values like keys
            if "KEY" in key.upper() or "SECRET" in key.upper():
                masked_value = value[:5] + "..." + value[-5:] if len(value) > 10 else "***"
                clerk_vars[key] = masked_value
            else:
                clerk_vars[key] = value
    
    # Pretty print the variables
    print(json.dumps(clerk_vars, indent=2))
    
    print("\n=== Clerk Configuration ===")
    # Print other relevant Clerk configuration (URLs, etc.)
    print(f"Sign-in URL: {os.environ.get('NEXT_PUBLIC_CLERK_SIGN_IN_URL', '/sign-in')}")
    print(f"Sign-up URL: {os.environ.get('NEXT_PUBLIC_CLERK_SIGN_UP_URL', '/sign-up')}")
    print(f"After Sign-in URL: {os.environ.get('NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL', '/dashboard')}")

if __name__ == "__main__":
    check_clerk_environment()