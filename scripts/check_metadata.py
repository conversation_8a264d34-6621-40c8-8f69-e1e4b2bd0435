import os
import sys

# Mimic path setup from alembic/env.py
# Assuming this script is in /app/scripts/ and env.py is in /app/alembic/
# Adjust if actual paths differ within the container.
# The goal is to allow `from src.coherence...` imports.
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, PROJECT_ROOT)

print(f"Python sys.path: {sys.path}")
print(f"Current working directory: {os.getcwd()}")


try:
    print("Attempting to import Base...")
    from src.coherence.db.base import Base  # This should be the one used by env.py
    print(f"Successfully imported Base: {Base}")
    print(f"Base.metadata: {Base.metadata}")
except ImportError as e:
    print(f"Error importing Base: {e}")
    Base = None
    exit(1)

if Base:
    try:
        print("Attempting to import models...")
        # Import order can sometimes matter if there are inter-model dependencies at definition time,
        # though SQLAlchemy usually handles this with string-based FKs.
        # We mimic the import style from env.py as much as possible.
        # General import like in env.py, though after specific ones here
        from src.coherence import models as coherence_models_module

        # Our problematic/new models
        from src.coherence.models.admin_workflow import AdminWorkflow
        from src.coherence.models.audit_log import AuditLog
        from src.coherence.models.generated_action import GeneratedAction
        from src.coherence.models.integration import (
            APIAuthConfig,
            APIEndpoint,
            APIIntegration,
        )
        from src.coherence.models.organization import Organization
        from src.coherence.models.template import (
            Template,
            TemplateDependency,
            TemplateTest,
            TemplateVersion,
        )
        from src.coherence.models.tenant import Tenant  # Existing Tenant model
        from src.coherence.models.user import User
        from src.coherence.models.workflow_status import WorkflowStatus
        print("Successfully imported all specified models.")

        print("\nTables registered in Base.metadata:")
        if Base.metadata.tables:
            for table_name in sorted(Base.metadata.tables.keys()):
                print(f"- {table_name}")
        else:
            print("No tables found in Base.metadata.")

    except ImportError as e:
        print(f"Error importing models: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
else:
    print("Base was not imported, cannot inspect metadata.") 