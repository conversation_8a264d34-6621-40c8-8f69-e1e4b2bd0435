#!/usr/bin/env python3
"""
Test script to verify Phase 1 implementation of uni-template enhancements.
"""

import json
from src.coherence.template_system.unified_generator import UnifiedTemplateGenerator
from src.coherence.db.deps import get_db
from src.coherence.models.template import Template
from sqlalchemy import select


def test_enhanced_template_generation():
    """Test that enhanced templates are generated correctly."""
    print("Testing enhanced template generation...")
    
    generator = UnifiedTemplateGenerator()
    
    # Sample OpenAPI operation
    operation = {
        "operationId": "getWeatherForecast",
        "summary": "Get weather forecast for a location",
        "description": "Returns the weather forecast for the specified location",
        "security": [{"apiKey": []}],
        "parameters": [
            {
                "name": "location",
                "in": "query",
                "required": True,
                "description": "City name or location",
                "schema": {
                    "type": "string",
                    "minLength": 2,
                    "maxLength": 100,
                    "pattern": "^[A-Za-z\\s,]+$"
                }
            },
            {
                "name": "units",
                "in": "query",
                "description": "Temperature units",
                "schema": {
                    "type": "string",
                    "enum": ["metric", "imperial"],
                    "default": "metric"
                }
            }
        ],
        "responses": {
            "200": {
                "description": "Success",
                "content": {
                    "application/json": {
                        "schema": {
                            "type": "object",
                            "properties": {
                                "location": {"type": "string"},
                                "temperature": {"type": "number"},
                                "conditions": {"type": "string"},
                                "humidity": {"type": "integer", "minimum": 0, "maximum": 100}
                            }
                        }
                    }
                }
            },
            "404": {
                "description": "Location not found"
            }
        }
    }
    
    # Generate template
    template = generator.generate_from_openapi_endpoint(
        operation=operation,
        path="/v2/weather/forecast",
        method="GET",
        base_url="https://api.weather.com"
    )
    
    print("\n=== Generated Template ===")
    print(json.dumps(template, indent=2))
    
    # Verify enhancements
    print("\n=== Verification ===")
    
    # 1. Check integration section
    assert "integration" in template["action"], "Missing integration section"
    integration = template["action"]["integration"]
    assert integration["base_url"] == "https://api.weather.com", "Incorrect base URL"
    assert integration["api_version"] == "v2", "Incorrect API version"
    assert integration["credential_ref"] == "api_weather_com_api_key", "Incorrect credential ref"
    print("✓ Integration section properly generated")
    
    # 2. Check validation rules
    assert "validation_rules" in template["parameters"], "Missing validation rules"
    rules = template["parameters"]["validation_rules"]
    assert "location" in rules, "Missing location validation"
    assert rules["location"]["min_length"] == 2, "Incorrect min length"
    assert rules["location"]["max_length"] == 100, "Incorrect max length"
    assert rules["location"]["pattern"] == "^[A-Za-z\\s,]+$", "Incorrect pattern"
    print("✓ Validation rules properly generated")
    
    # 3. Check transformations
    assert "transformations" in template["parameters"], "Missing transformations"
    transforms = template["parameters"]["transformations"]
    assert "location" in transforms, "Missing location transformations"
    assert "trim" in transforms["location"], "Missing trim transformation"
    print("✓ Transformation rules properly generated")
    
    # 4. Check test data
    assert "test_data" in template, "Missing test data"
    test_data = template["test_data"]
    assert "mock_responses" in test_data, "Missing mock responses"
    assert "sample_parameters" in test_data, "Missing sample parameters"
    assert test_data["sample_parameters"]["location"] == "sample_location", "Incorrect sample location"
    print("✓ Test data properly generated")
    
    # 5. Check CRFS auto-selection
    assert template["response"]["crfs"]["auto_select"] is True, "CRFS auto-select not enabled"
    assert template["response"]["crfs"]["default_format"] == "structured", "Incorrect default format"
    print("✓ CRFS auto-selection enabled")
    
    print("\n✅ All Phase 1 enhancements verified successfully!")
    

def test_database_migration():
    """Test that the database migration for test_data field worked."""
    print("\n\nTesting database migration...")
    
    from src.coherence.db.session import SessionLocal
    
    # Get a database session
    db = SessionLocal()
    
    try:
        # Try to query templates table with test_data field
        stmt = select(Template).limit(1)
        result = db.execute(stmt)
        template = result.scalar()
        
        # Check if test_data attribute exists
        if template:
            _ = template.test_data  # This will raise AttributeError if field doesn't exist
            print("✓ test_data field exists on Template model")
        else:
            # Create a test template to verify field
            test_template = Template(
                key="test_phase1",
                category="unified",
                scope="global",
                body="test template",
                test_data={"mock_responses": {"success": {"result": "ok"}}}
            )
            db.add(test_template)
            db.commit()
            
            # Verify it was saved
            saved = db.query(Template).filter_by(key="test_phase1").first()
            assert saved.test_data is not None, "test_data not saved"
            print("✓ test_data field successfully added and working")
            
            # Clean up
            db.delete(saved)
            db.commit()
            
    except Exception as e:
        print(f"❌ Error testing database: {e}")
        raise
    finally:
        db.close()
    
    print("\n✅ Database migration verified successfully!")


if __name__ == "__main__":
    print("=== Phase 1 Implementation Test ===\n")
    test_enhanced_template_generation()
    test_database_migration()
    print("\n=== All tests passed! Phase 1 implementation is complete. ===")