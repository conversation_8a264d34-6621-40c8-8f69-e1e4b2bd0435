#!/usr/bin/env python3
"""
Validate Template Structure Script

This script validates the structure and completeness of unified templates:
- Checks for required sections and fields
- Validates parameter mappings
- Validates CRFS configuration
- Checks credential references
- Validates integration configuration

Phase 7 Implementation - Template Structure Validator
"""

import asyncio
import json
import logging
import re
from typing import Any, Dict, List, Optional, Set, Tuple
from urllib.parse import urlparse

from sqlalchemy import select
from sqlalchemy.orm import selectinload

from src.coherence.db.deps import get_admin_db_session
from src.coherence.models.template import Template, TemplateCategory
from src.coherence.models.integration import APIIntegration

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ValidationResult:
    """Container for validation results."""
    
    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
        self.is_valid: bool = True
    
    def add_error(self, message: str):
        self.errors.append(message)
        self.is_valid = False
    
    def add_warning(self, message: str):
        self.warnings.append(message)
    
    def add_info(self, message: str):
        self.info.append(message)
    
    def __str__(self) -> str:
        result = []
        if self.errors:
            result.append(f"Errors ({len(self.errors)}):")
            for error in self.errors:
                result.append(f"  ❌ {error}")
        
        if self.warnings:
            result.append(f"\nWarnings ({len(self.warnings)}):")
            for warning in self.warnings:
                result.append(f"  ⚠️  {warning}")
        
        if self.info:
            result.append(f"\nInfo ({len(self.info)}):")
            for info in self.info:
                result.append(f"  ℹ️  {info}")
        
        return "\n".join(result)


class TemplateStructureValidator:
    """Validates the structure and completeness of unified templates."""
    
    def __init__(self):
        self.total_templates = 0
        self.valid_templates = 0
        self.invalid_templates = 0
        self.templates_with_warnings = 0
    
    async def validate_all_templates(self, fix_issues: bool = False):
        """Validate all unified templates in the database."""
        async with get_admin_db_session() as db:
            # Query all unified templates
            result = await db.execute(
                select(Template)
                .where(Template.category == TemplateCategory.UNIFIED)
                .options(selectinload(Template.integration))
            )
            templates = result.scalars().all()
            
            self.total_templates = len(templates)
            logger.info(f"Found {self.total_templates} unified templates to validate")
            
            validation_results = []
            
            for template in templates:
                result = self._validate_template(template)
                
                if result.is_valid:
                    self.valid_templates += 1
                    if result.warnings:
                        self.templates_with_warnings += 1
                else:
                    self.invalid_templates += 1
                
                validation_results.append((template, result))
                
                # Log results
                logger.info(f"\nTemplate: {template.key}")
                if result.is_valid:
                    logger.info("  ✅ Valid")
                else:
                    logger.error("  ❌ Invalid")
                
                if result.errors or result.warnings:
                    logger.info(str(result))
                
                # Fix issues if requested
                if fix_issues and not result.is_valid:
                    fixed = await self._fix_template_issues(template, result, db)
                    if fixed:
                        logger.info("  🔧 Fixed issues")
            
            if fix_issues:
                await db.commit()
        
        # Print summary
        self._print_summary()
        
        return validation_results
    
    def _validate_template(self, template: Template) -> ValidationResult:
        """Validate a single template."""
        result = ValidationResult()
        
        # Validate basic structure
        self._validate_basic_structure(template, result)
        
        # Validate intent configuration
        self._validate_intent_config(template, result)
        
        # Validate action configuration
        self._validate_action_config(template, result)
        
        # Validate response format
        self._validate_response_format(template, result)
        
        # Validate parameter mappings
        self._validate_parameter_mappings(template, result)
        
        # Validate CRFS configuration
        self._validate_crfs_config(template, result)
        
        # Validate credential references
        self._validate_credential_references(template, result)
        
        # Validate test data if present
        self._validate_test_data(template, result)
        
        return result
    
    def _validate_basic_structure(self, template: Template, result: ValidationResult):
        """Validate basic template structure."""
        # Check required fields
        if not template.key:
            result.add_error("Missing template key")
        
        if not template.category:
            result.add_error("Missing template category")
        elif template.category != TemplateCategory.UNIFIED:
            result.add_error(f"Invalid category for unified template: {template.category}")
        
        if not template.scope:
            result.add_warning("Missing template scope")
        
        # Check JSONB fields
        if not template.intent_config:
            result.add_error("Missing intent_config")
        
        if not template.action_config:
            result.add_error("Missing action_config")
        
        if not template.response_format:
            result.add_warning("Missing response_format")
    
    def _validate_intent_config(self, template: Template, result: ValidationResult):
        """Validate intent configuration."""
        if not template.intent_config:
            return
        
        intent_config = template.intent_config
        
        # Check for intent patterns
        if 'intent' not in intent_config:
            result.add_error("Missing 'intent' section in intent_config")
            return
        
        intent = intent_config['intent']
        
        # Validate patterns
        if 'patterns' not in intent:
            result.add_error("Missing 'patterns' in intent configuration")
        elif not isinstance(intent['patterns'], list):
            result.add_error("'patterns' must be a list")
        elif len(intent['patterns']) == 0:
            result.add_error("At least one pattern is required")
        else:
            # Validate each pattern
            for i, pattern in enumerate(intent['patterns']):
                if not isinstance(pattern, str):
                    result.add_error(f"Pattern {i} is not a string")
                elif len(pattern.strip()) == 0:
                    result.add_error(f"Pattern {i} is empty")
                elif len(pattern) > 500:
                    result.add_warning(f"Pattern {i} is very long ({len(pattern)} chars)")
        
        # Check confidence threshold
        if 'confidence_threshold' in intent:
            threshold = intent['confidence_threshold']
            if not isinstance(threshold, (int, float)):
                result.add_error("'confidence_threshold' must be a number")
            elif threshold < 0 or threshold > 1:
                result.add_error("'confidence_threshold' must be between 0 and 1")
        else:
            result.add_info("No confidence_threshold specified, will use default")
    
    def _validate_action_config(self, template: Template, result: ValidationResult):
        """Validate action configuration."""
        if not template.action_config:
            return
        
        action_config = template.action_config
        
        # Check for action section
        if 'action' not in action_config:
            result.add_error("Missing 'action' section in action_config")
            return
        
        action = action_config['action']
        
        # Validate HTTP method
        if 'method' not in action:
            result.add_error("Missing 'method' in action configuration")
        elif action['method'] not in ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS']:
            result.add_error(f"Invalid HTTP method: {action['method']}")
        
        # Validate path
        if 'path' not in action:
            result.add_error("Missing 'path' in action configuration")
        else:
            path = action['path']
            if not path.startswith('/'):
                result.add_warning(f"Path should start with '/': {path}")
            
            # Check for path parameters
            path_params = re.findall(r'\{(\w+)\}', path)
            if path_params:
                # Verify path parameters are defined
                parameters = action_config.get('parameters', {})
                for param in path_params:
                    if param not in parameters:
                        result.add_error(f"Path parameter '{param}' not defined in parameters")
        
        # Validate integration section
        if 'integration' in action:
            self._validate_integration_config(action['integration'], result)
        else:
            result.add_warning("Missing 'integration' section in action")
        
        # Validate authentication
        if 'auth' in action:
            auth = action['auth']
            if 'type' not in auth:
                result.add_error("Missing 'type' in auth configuration")
            elif auth['type'] not in ['api_key', 'bearer', 'basic', 'oauth2', 'none']:
                result.add_warning(f"Unknown auth type: {auth['type']}")
    
    def _validate_integration_config(self, integration: Dict[str, Any], result: ValidationResult):
        """Validate integration configuration."""
        # Check base URL
        if 'base_url' not in integration:
            result.add_error("Missing 'base_url' in integration configuration")
        else:
            base_url = integration['base_url']
            if not base_url:
                result.add_error("Empty base_url")
            elif not base_url.startswith('http') and not base_url.startswith('$'):
                result.add_warning(f"base_url should start with http(s):// or be a variable: {base_url}")
        
        # Check API version
        if 'api_version' not in integration:
            result.add_info("No api_version specified")
        
        # Check credential reference
        if 'credential_ref' in integration and integration['credential_ref']:
            # Basic validation of credential reference format
            cred_ref = integration['credential_ref']
            if not re.match(r'^[\w_]+$', cred_ref):
                result.add_warning(f"Credential reference contains invalid characters: {cred_ref}")
    
    def _validate_response_format(self, template: Template, result: ValidationResult):
        """Validate response format configuration."""
        if not template.response_format:
            return
        
        response_format = template.response_format
        
        # Check for response section
        if 'response' not in response_format:
            result.add_warning("Missing 'response' section in response_format")
            return
        
        response = response_format['response']
        
        # Check for CRFS configuration
        if 'crfs' not in response:
            result.add_warning("Missing 'crfs' configuration in response")
        else:
            crfs = response['crfs']
            
            # Validate CRFS type
            if 'type' in crfs and crfs['type'] not in ['structured', 'raw', 'template']:
                result.add_warning(f"Unknown CRFS type: {crfs['type']}")
            
            # Check auto_select
            if 'auto_select' not in crfs:
                result.add_info("CRFS auto_select not enabled")
            
            # Validate formats
            if 'formats' in crfs:
                self._validate_crfs_formats(crfs['formats'], result)
        
        # Check error mapping
        if 'error_mapping' not in response:
            result.add_info("No error_mapping defined")
    
    def _validate_crfs_formats(self, formats: Dict[str, Any], result: ValidationResult):
        """Validate CRFS format configurations."""
        if not isinstance(formats, dict):
            result.add_error("CRFS formats must be a dictionary")
            return
        
        if not formats:
            result.add_warning("No CRFS formats defined")
            return
        
        for format_name, format_config in formats.items():
            if not isinstance(format_config, dict):
                result.add_error(f"Format '{format_name}' configuration must be a dictionary")
                continue
            
            # Validate structured format
            if format_name == 'structured' or format_config.get('type') == 'structured':
                if 'sections' not in format_config:
                    result.add_warning(f"Structured format '{format_name}' missing 'sections'")
                elif not isinstance(format_config['sections'], list):
                    result.add_error(f"Sections in format '{format_name}' must be a list")
            
            # Validate template format
            elif 'template' in format_config:
                template_str = format_config['template']
                if not isinstance(template_str, str):
                    result.add_error(f"Template in format '{format_name}' must be a string")
                else:
                    # Check for Jinja2 variables
                    variables = re.findall(r'\{\{(\w+)\}\}', template_str)
                    if not variables:
                        result.add_info(f"Template in format '{format_name}' has no variables")
    
    def _validate_parameter_mappings(self, template: Template, result: ValidationResult):
        """Validate parameter mappings between intent and action."""
        if not template.action_config:
            return
        
        action_config = template.action_config
        action = action_config.get('action', {})
        parameters = action_config.get('parameters', {})
        
        # Check parameter_mapping if present
        if 'parameter_mapping' in action:
            mapping = action['parameter_mapping']
            
            for target, source in mapping.items():
                # Extract parameter names from Jinja2 templates
                param_refs = re.findall(r'parameters\.(\w+)', source)
                for param_ref in param_refs:
                    if param_ref not in parameters:
                        result.add_error(
                            f"Parameter mapping references undefined parameter: {param_ref}"
                        )
        
        # Check for required parameters
        required_params = [
            name for name, config in parameters.items()
            if isinstance(config, dict) and config.get('required')
        ]
        
        if required_params:
            result.add_info(f"Required parameters: {', '.join(required_params)}")
    
    def _validate_credential_references(self, template: Template, result: ValidationResult):
        """Validate credential references in the template."""
        if not template.action_config:
            return
        
        action = template.action_config.get('action', {})
        
        # Check auth configuration
        auth = action.get('auth', {})
        if auth and 'value' in auth:
            value = auth['value']
            # Check for credential references
            cred_refs = re.findall(r'\{\{credentials\.(\w+)\}\}', value)
            
            for cred_ref in cred_refs:
                # Check if credential reference matches integration
                integration = action.get('integration', {})
                if 'credential_ref' in integration:
                    if integration['credential_ref'] != cred_ref:
                        result.add_warning(
                            f"Credential reference mismatch: {cred_ref} != {integration['credential_ref']}"
                        )
                else:
                    result.add_info(f"Credential reference '{cred_ref}' not declared in integration")
    
    def _validate_test_data(self, template: Template, result: ValidationResult):
        """Validate test data if present."""
        if not hasattr(template, 'test_data') or not template.test_data:
            result.add_info("No test data present")
            return
        
        test_data = template.test_data
        
        # Check for required sections
        if 'mock_responses' not in test_data:
            result.add_warning("Test data missing 'mock_responses'")
        else:
            mock_responses = test_data['mock_responses']
            if 'success' not in mock_responses:
                result.add_warning("Test data missing 'success' mock response")
        
        if 'sample_parameters' not in test_data:
            result.add_warning("Test data missing 'sample_parameters'")
        else:
            # Validate sample parameters match schema
            sample_params = test_data['sample_parameters']
            parameters = template.action_config.get('parameters', {})
            
            # Check for missing required parameters
            for param_name, param_config in parameters.items():
                if isinstance(param_config, dict) and param_config.get('required'):
                    if param_name not in sample_params:
                        result.add_warning(
                            f"Test data missing required parameter: {param_name}"
                        )
    
    async def _fix_template_issues(
        self, 
        template: Template, 
        result: ValidationResult, 
        db
    ) -> bool:
        """Attempt to fix common template issues."""
        fixed_any = False
        
        # Fix missing integration section
        if template.action_config and 'action' in template.action_config:
            action = template.action_config['action']
            
            if 'integration' not in action:
                # Add basic integration section
                action['integration'] = {
                    'base_url': '${API_BASE_URL}',
                    'api_version': 'v1'
                }
                fixed_any = True
                logger.info("    Added missing integration section")
        
        # Fix missing CRFS auto_select
        if template.response_format and 'response' in template.response_format:
            response = template.response_format['response']
            
            if 'crfs' in response and 'auto_select' not in response['crfs']:
                response['crfs']['auto_select'] = True
                fixed_any = True
                logger.info("    Enabled CRFS auto_select")
        
        # Fix missing error mapping
        if template.response_format and 'response' in template.response_format:
            response = template.response_format['response']
            
            if 'error_mapping' not in response:
                response['error_mapping'] = {
                    '400': 'Invalid request parameters',
                    '401': 'Authentication failed',
                    '404': 'Resource not found',
                    '500': 'Internal server error'
                }
                fixed_any = True
                logger.info("    Added default error mapping")
        
        return fixed_any
    
    def _print_summary(self):
        """Print validation summary."""
        logger.info("\n" + "=" * 60)
        logger.info("VALIDATION SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total Templates:          {self.total_templates}")
        logger.info(f"Valid Templates:          {self.valid_templates} ({self.valid_templates/self.total_templates*100:.1f}%)")
        logger.info(f"Invalid Templates:        {self.invalid_templates} ({self.invalid_templates/self.total_templates*100:.1f}%)")
        logger.info(f"Templates with Warnings:  {self.templates_with_warnings} ({self.templates_with_warnings/self.total_templates*100:.1f}%)")
        logger.info("=" * 60)


async def main():
    """Main entry point for the validation script."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Validate unified template structure')
    parser.add_argument(
        '--fix', 
        action='store_true', 
        help='Attempt to fix common issues'
    )
    parser.add_argument(
        '--template-key',
        help='Validate specific template by key'
    )
    
    args = parser.parse_args()
    
    logger.info("Starting Template Structure Validation...")
    
    validator = TemplateStructureValidator()
    
    if args.template_key:
        # Validate specific template
        async with get_admin_db_session() as db:
            result = await db.execute(
                select(Template)
                .where(Template.key == args.template_key)
                .where(Template.category == TemplateCategory.UNIFIED)
            )
            template = result.scalar_one_or_none()
            
            if not template:
                logger.error(f"Template not found: {args.template_key}")
                return
            
            validation_result = validator._validate_template(template)
            
            logger.info(f"\nValidation result for {template.key}:")
            logger.info(str(validation_result))
            
            if args.fix and not validation_result.is_valid:
                fixed = await validator._fix_template_issues(template, validation_result, db)
                if fixed:
                    await db.commit()
                    logger.info("Issues fixed!")
    else:
        # Validate all templates
        await validator.validate_all_templates(fix_issues=args.fix)
    
    logger.info("\nValidation complete!")


if __name__ == "__main__":
    asyncio.run(main())