#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the ICD-10-CM template integration with intent vectors.

This script:
1. Imports the ICD-10-CM OpenAPI spec
2. Generates templates for all endpoints
3. Verifies that intent templates are created 
4. Checks that intent templates are indexed in Qdrant
5. Performs a test search to verify vector matching
"""

import asyncio
import json
import logging
import uuid
from typing import List

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.qdrant_client import QdrantClient
from src.coherence.db.session import async_session
from src.coherence.models.integration import APIEndpoint
from src.coherence.models.template import Template
from src.coherence.models.tenant import Tenant
from src.coherence.openapi_adapter.adapter import get_openapi_adapter
from src.coherence.template_system.services.template_service import TemplateService

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def get_first_tenant() -> uuid.UUID:
    """Get the first tenant from the database."""
    async with async_session() as db:
        result = await db.execute(select(Tenant).limit(1))
        tenant = result.scalar_one_or_none()
        if not tenant:
            raise ValueError("No tenants found in the database")
        return tenant.id

async def import_icd10cm_spec(tenant_id: uuid.UUID, db: AsyncSession) -> uuid.UUID:
    """Import the ICD-10-CM OpenAPI spec."""
    logger.info(f"Checking for existing ICD-10-CM integration for tenant {tenant_id}")
    
    # Check if the integration already exists
    from sqlalchemy import and_, select

    from src.coherence.models.integration import APIIntegration
    
    integration_name = "ICD-10-CM Clinical Tables API"
    result = await db.execute(
        select(APIIntegration).where(
            and_(
                APIIntegration.tenant_id == tenant_id,
                APIIntegration.name == integration_name
            )
        )
    )
    existing_integration = result.scalar_one_or_none()
    
    if existing_integration:
        logger.info(f"Found existing ICD-10-CM integration with ID: {existing_integration.id}")
        return existing_integration.id
    
    logger.info(f"Importing new ICD-10-CM spec for tenant {tenant_id}")
    
    # Load the spec from file
    spec_path = "/app/icd10cm_openapi.json"
    try:
        with open(spec_path, "r") as f:
            spec_data = json.load(f)
    except FileNotFoundError:
        logger.error(f"ICD10CM OpenAPI spec not found at {spec_path}")
        # Try alternative paths
        alternative_paths = [
            "/Users/<USER>/Documents/projects/coherence/icd10cm_openapi.json",
            "icd10cm_openapi.json",
            "../icd10cm_openapi.json"
        ]
        for alt_path in alternative_paths:
            try:
                logger.info(f"Trying alternative path: {alt_path}")
                with open(alt_path, "r") as f:
                    spec_data = json.load(f)
                    logger.info(f"Successfully loaded spec from {alt_path}")
                    break
            except FileNotFoundError:
                continue
        else:
            raise FileNotFoundError("Could not find ICD10CM OpenAPI spec in any of the tried locations")
    
    # Get OpenAPI adapter
    adapter = await get_openapi_adapter(db)
    
    # Import the spec
    result = await adapter.import_spec(
        tenant_id=tenant_id,
        name=integration_name,
        spec_data=spec_data
    )
    
    logger.info(f"Successfully imported ICD-10-CM spec: {result}")
    return uuid.UUID(result["integration_id"])

async def generate_templates(integration_id: uuid.UUID, tenant_id: uuid.UUID, db: AsyncSession) -> List[Template]:
    """Generate templates for all endpoints in the integration."""
    logger.info(f"Generating templates for integration {integration_id}")
    
    # Get OpenAPI adapter
    await get_openapi_adapter(db)
    
    # Get all endpoints for this integration
    result = await db.execute(
        select(APIEndpoint).where(
            APIEndpoint.integration_id == integration_id
        )
    )
    endpoints = result.scalars().all()
    
    # Get action generator
    from src.coherence.openapi_adapter.action_generator import get_action_generator
    action_generator = await get_action_generator(db)
    
    # Generate template definitions for each endpoint
    template_defs = []
    for endpoint in endpoints:
        endpoint_templates = await action_generator._generate_action_templates(
            endpoint_id=endpoint.id,
            tenant_id=tenant_id,
            api_key="icd10cm"
        )
        template_defs.extend(endpoint_templates)
    
    # Create templates
    templates = []
    template_service = TemplateService()
    for template_def in template_defs:
            template = await template_service.create_template(
                db=db,
                key=template_def["key"],
                category=template_def["category"],
                body=template_def["body"],
                scope=template_def["scope"],
                scope_id=template_def["scope_id"],
                tenant_id=template_def["tenant_id"],
                description=template_def.get("description"),
                actions=template_def.get("actions"),
                parameters=template_def.get("parameters"),
            )
            templates.append(template)
    
    await db.commit()
    logger.info(f"Generated {len(templates)} templates")
    return templates

async def check_intent_templates(tenant_id: uuid.UUID, db: AsyncSession) -> List[Template]:
    """Check if intent templates were created."""
    logger.info(f"Checking intent templates for tenant {tenant_id}")
    
    # Query for intent templates
    result = await db.execute(
        select(Template).where(
            Template.tenant_id == tenant_id,
            Template.category == "intent_router"
        )
    )
    templates = result.scalars().all()
    
    logger.info(f"Found {len(templates)} intent templates")
    for template in templates:
        logger.info(f"Template {template.key}: {template.description}")
    
    return templates

async def check_vector_index(tenant_id: uuid.UUID) -> bool:
    """Check if intent templates are indexed in Qdrant."""
    logger.info(f"Checking vector index for tenant {tenant_id}")
    
    # Initialize Qdrant client
    qdrant_client = QdrantClient()
    
    # Check if collection exists
    collection_name = f"intent_idx_{tenant_id}_user"
    exists = await qdrant_client.collection_exists(collection_name)
    
    if not exists:
        logger.warning(f"Collection {collection_name} does not exist")
        return False
    
    # Run a test search to verify indexing
    llm_factory = LLMFactory()
    llm_provider = llm_factory.get_default_provider(fail_fast=False)
    
    # Generate embedding for a test query
    test_query = "search for disease codes"
    embedding = await llm_provider.generate_embedding(test_query)
    
    # Search the vector index
    results = await qdrant_client.search(
        collection_name=collection_name,
        query_vector=embedding,
        limit=5
    )
    
    logger.info(f"Search results for '{test_query}':")
    for result in results:
        logger.info(f"Score: {result['score']}, Intent: {result.get('intent')}, Text: {result.get('text')}")
    
    return len(results) > 0

async def run_intent_test(tenant_id: uuid.UUID) -> None:
    """Run an end-to-end test of the intent resolution pipeline."""
    logger.info(f"Running intent resolution test for tenant {tenant_id}")
    
    # Initialize Qdrant client and LLM provider
    qdrant_client = QdrantClient()
    llm_factory = LLMFactory()
    llm_provider = llm_factory.get_default_provider(fail_fast=False)
    
    # Generate embedding for a test query
    test_query = "find ICD-10 codes for diabetes"
    embedding = await llm_provider.generate_embedding(test_query)
    
    # Search the vector index using Tier 1
    collection_name = f"intent_idx_{tenant_id}_user"
    results = await qdrant_client.search(
        collection_name=collection_name,
        query_vector=embedding,
        limit=5
    )
    
    if not results:
        logger.warning("No results found in vector search")
        return
    
    # Get the top result
    top_result = results[0]
    
    logger.info(f"Top result for '{test_query}':")
    logger.info(f"Score: {top_result['score']}")
    logger.info(f"Intent: {top_result.get('intent')}")
    logger.info(f"Text: {top_result.get('text')}")
    logger.info(f"Parameters: {top_result.get('parameters')}")
    
    # TODO: If we had the full intent pipeline, we would call resolve() and check the result

async def main():
    """Main entry point."""
    logger.info("Starting ICD-10-CM template integration test")
    
    async with async_session() as db:
        # Get the first tenant
        tenant_id = await get_first_tenant()
        logger.info(f"Using tenant ID: {tenant_id}")
        
        # Step 1: Import the ICD-10-CM spec
        integration_id = await import_icd10cm_spec(tenant_id, db)
        
        # Step 2: Generate templates
        await generate_templates(integration_id, tenant_id, db)
        
        # Step 3: Check for intent templates
        await check_intent_templates(tenant_id, db)
        
        # Step 4: Check vector index
        indexed = await check_vector_index(tenant_id)
        if not indexed:
            logger.warning("Vector index not found or empty. Rebuilding...")
            from scripts.rebuild_intent_templates_index import rebuild_index_for_tenant
            
            qdrant_client = QdrantClient()
            llm_factory = LLMFactory()
            await rebuild_index_for_tenant(tenant_id, qdrant_client, llm_factory)
        
        # Step 5: Run intent test
        await run_intent_test(tenant_id)
    
    logger.info("ICD-10-CM template integration test completed!")

if __name__ == "__main__":
    asyncio.run(main())