#!/usr/bin/env python
"""
Migration script to convert existing SYSTEM_ADMIN_CLERK_USER_ID to SystemAdmin record.

This script checks for the SYSTEM_ADMIN_CLERK_USER_ID environment variable and creates
a corresponding SystemAdmin record if it exists and no SystemAdmin records exist yet.

Usage:
    python -m scripts.migrate_system_admin
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta

from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.crud.crud_system_admin import (
    create_system_admin,
    create_system_admin_api_key,
    get_multi_system_admin,
    get_system_admin_by_clerk_id,
)
from src.coherence.db.session import get_async_session
from src.coherence.schemas.system_admin import (
    SystemAdminAPIKeyCreate,
    SystemAdminCreate,
)


async def migrate_system_admin() -> None:
    """
    Migrate SYSTEM_ADMIN_CLERK_USER_ID to a SystemAdmin record.
    """
    # Check if the environment variable exists
    env_admin_id = os.environ.get("SYSTEM_ADMIN_CLERK_USER_ID")
    if not env_admin_id:
        print("No SYSTEM_ADMIN_CLERK_USER_ID environment variable found. Nothing to migrate.")
        return
    
    # Check if the old API key exists
    env_api_key = os.environ.get("SYSTEM_ADMIN_API_KEY")
    
    # Get a database session
    async for session in get_async_session():
        db: AsyncSession = session
        
        try:
            # Check if any system admins already exist
            existing_admins = await get_multi_system_admin(db=db, limit=1)
            if existing_admins:
                # Check if the system admin with this clerk ID already exists
                existing_admin = await get_system_admin_by_clerk_id(db=db, clerk_user_id=env_admin_id)
                if existing_admin:
                    print(f"System admin with clerk ID {env_admin_id} already exists. No migration needed.")
                else:
                    print("System admins exist, but none with this clerk ID. Creating new record.")
                    admin_create = SystemAdminCreate(
                        clerk_user_id=env_admin_id,
                        created_by="migration_script"
                    )
                    system_admin = await create_system_admin(db=db, obj_in=admin_create)
                    print(f"Created system admin with ID: {system_admin.id}")
            else:
                # No system admins exist yet, create one
                admin_create = SystemAdminCreate(
                    clerk_user_id=env_admin_id,
                    created_by="migration_script"
                )
                system_admin = await create_system_admin(db=db, obj_in=admin_create)
                print(f"Created system admin with ID: {system_admin.id}")
                
                # Create an API key if the old one existed
                if env_api_key:
                    # Create API key - expires in 90 days
                    expires_at = datetime.now() + timedelta(days=90)
                    api_key_create = SystemAdminAPIKeyCreate(
                        name="Migrated Admin API Key",
                        created_by="migration_script",
                        expires_at=expires_at,
                        permissions={"admin": True}  # Full admin permissions
                    )
                    _, raw_api_key = await create_system_admin_api_key(
                        db=db,
                        obj_in=api_key_create,
                        system_admin_id=system_admin.id
                    )
                    
                    print("Created new API key to replace existing SYSTEM_ADMIN_API_KEY:")
                    print(f"API Key: {raw_api_key}")
                    print("IMPORTANT: Update your environment variables with this new key")
                    print("           and remove the old SYSTEM_ADMIN_API_KEY and SYSTEM_ADMIN_CLERK_USER_ID variables.")
                    print(f"Expires: {expires_at.isoformat()}")
            
            print("Migration completed.")
            
        except Exception as e:
            await db.rollback()
            print(f"Error migrating system admin: {str(e)}")
            sys.exit(1)


def main():
    asyncio.run(migrate_system_admin())


if __name__ == "__main__":
    main()