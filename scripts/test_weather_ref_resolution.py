"""Test Weather API template generation with $ref resolution."""

import asyncio
import json
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select
from src.coherence.db.session import async_engine, async_session
from src.coherence.models.integration import APIIntegration, APIEndpoint
from src.coherence.openapi_adapter.action_generator import ActionGenerator

async def test_weather_ref_resolution():
    """Test that Weather API templates generate with structured CRFS format."""
    async with async_session() as db:
        # Find Weather integration
        query = select(APIIntegration).where(APIIntegration.name == "Weather")
        result = await db.execute(query)
        integration = result.scalar_one_or_none()
        
        if not integration:
            print("Weather integration not found")
            return
            
        print(f"Found integration: {integration.name}")
        
        # Find alerts/active endpoint
        query = select(APIEndpoint).where(
            APIEndpoint.integration_id == integration.id,
            APIEndpoint.path == "/alerts/active"
        )
        result = await db.execute(query)
        endpoint = result.scalar_one_or_none()
        
        if not endpoint:
            print("Endpoint /alerts/active not found")
            return
            
        print(f"Found endpoint: {endpoint.method} {endpoint.path}")
        
        # Create action generator
        generator = ActionGenerator(db)
        
        # Get endpoint spec
        endpoint_spec = generator._get_endpoint_spec(
            integration.openapi_spec, 
            endpoint.path, 
            endpoint.method.lower()
        )
        
        print("\nEndpoint spec responses:")
        print(json.dumps(endpoint_spec.get("responses", {}), indent=2))
        
        # Test reference resolution
        if "200" in endpoint_spec.get("responses", {}):
            response_200 = endpoint_spec["responses"]["200"]
            print(f"\n200 response: {response_200}")
            
            if "$ref" in response_200:
                print(f"Resolving reference: {response_200['$ref']}")
                resolved = generator.resolve_reference(response_200["$ref"], integration.openapi_spec)
                print(f"Resolved to: {json.dumps(resolved, indent=2)}")
        
        # Generate response format
        response_format = generator._generate_response_format(
            endpoint_spec, 
            integration.name,
            integration.openapi_spec
        )
        
        print("\nGenerated response format:")
        print(json.dumps(response_format, indent=2))
        
        # Check if it's structured format
        format_type = response_format.get("format", {}).get("type")
        print(f"\nFormat type: {format_type}")
        
        if format_type == "structured":
            print("✅ SUCCESS: Structured format generated!")
        else:
            print("❌ FAILED: Still using template format")

if __name__ == "__main__":
    asyncio.run(test_weather_ref_resolution())