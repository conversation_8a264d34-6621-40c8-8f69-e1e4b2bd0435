#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to generate a TypeScript file with permissions from CoherencePermission enum.
This ensures the frontend has the exact same permission strings as the backend.
"""

import json
import os
import sys
from pathlib import Path
from typing import List, Set

# Add the project root to the Python path
project_root = str(Path(__file__).parent.parent)
sys.path.insert(0, project_root)

# Import CoherencePermission from its actual location
from src.coherence.services.permission_service import CoherencePermission


def generate_permissions_ts(output_path: str, permissions: List[str]) -> None:
    """Generate the TypeScript file with permissions array."""
    ts_content = f"""/* ⚠️ AUTO-GENERATED – DO NOT EDIT */
export const PERMISSIONS = {json.dumps(permissions, indent=2)} as const;

export type Permission = typeof PERMISSIONS[number];
"""
    
    # Ensure the output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Write the TypeScript file
    with open(output_path, 'w') as f:
        f.write(ts_content)
    
    print(f"Generated {output_path} with {len(permissions)} permissions")

def generate_permissions_json(output_path: str, permissions: List[str]) -> None:
    """Generate a JSON file with permissions for testing purposes."""
    with open(output_path, 'w') as f:
        json.dump(permissions, f, indent=2)
    
    print(f"Generated {output_path} for tests")

def main():
    # Get the project root directory
    repo_root = Path(__file__).parent.parent
    
    # Extract all permission values from the enum
    permissions: Set[str] = {p.value for p in CoherencePermission}
    permissions_list = sorted(list(permissions))
    
    # Output paths
    ts_output_path = repo_root / "apps" / "admin" / "lib" / "generated" / "permissions.ts"
    json_output_path = repo_root / "apps" / "admin" / "lib" / "generated" / "permissions.json"
    
    # Generate the files
    generate_permissions_ts(str(ts_output_path), permissions_list)
    generate_permissions_json(str(json_output_path), permissions_list)

if __name__ == "__main__":
    main()