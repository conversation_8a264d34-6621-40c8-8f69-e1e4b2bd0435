#!/usr/bin/env python3
"""Test response format generation when creating templates from API specs."""

import asyncio
import uuid
from pathlib import Path
import sys
import json

# Add src to sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.coherence.openapi_adapter.action_generator import ActionGenerator
from src.coherence.models.integration import APIEndpoint, APIIntegration
from src.coherence.db.session import AsyncSessionLocal


async def test_response_format_generation():
    """Test that response formats are generated correctly."""
    
    # Create mock OpenAPI spec with various response types
    openapi_spec = {
        "openapi": "3.0.0",
        "info": {"title": "Test API", "version": "1.0.0"},
        "paths": {
            "/items": {
                "get": {
                    "summary": "Get list of items",
                    "operationId": "getItems",
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "total": {"type": "integer"},
                                            "items": {
                                                "type": "array",
                                                "items": {
                                                    "type": "object",
                                                    "properties": {
                                                        "id": {"type": "string"},
                                                        "name": {"type": "string"},
                                                        "description": {"type": "string"},
                                                        "created_at": {"type": "string"},
                                                        "status": {"type": "string"}
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/items/{id}": {
                "get": {
                    "summary": "Get single item",
                    "operationId": "getItem",
                    "parameters": [
                        {
                            "name": "id",
                            "in": "path",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "id": {"type": "string"},
                                            "name": {"type": "string"},
                                            "description": {"type": "string"},
                                            "created_at": {"type": "string"},
                                            "updated_at": {"type": "string"},
                                            "status": {"type": "string"},
                                            "metadata": {"type": "object"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/alerts": {
                "get": {
                    "summary": "Get weather alerts",
                    "operationId": "getAlerts",
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "id": {"type": "string"},
                                                "event": {"type": "string"},
                                                "headline": {"type": "string"},
                                                "description": {"type": "string"},
                                                "severity": {"type": "string"},
                                                "urgency": {"type": "string"},
                                                "area": {"type": "string"}
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    async with AsyncSessionLocal() as db:
        # Create mock integration and endpoints
        integration_id = uuid.uuid4()
        tenant_id = uuid.uuid4()
        
        # Mock integration
        integration = APIIntegration(
            id=integration_id,
            tenant_id=tenant_id,
            name="Test API",
            base_url="https://api.example.com",
            openapi_spec=openapi_spec
        )
        
        # Mock endpoints
        endpoints = [
            APIEndpoint(
                id=uuid.uuid4(),
                integration_id=integration_id,
                path="/items",
                method="GET",
                operation_id="getItems"
            ),
            APIEndpoint(
                id=uuid.uuid4(),
                integration_id=integration_id,
                path="/items/{id}",
                method="GET",
                operation_id="getItem"
            ),
            APIEndpoint(
                id=uuid.uuid4(),
                integration_id=integration_id,
                path="/alerts",
                method="GET",
                operation_id="getAlerts"
            )
        ]
        
        # Create action generator
        generator = ActionGenerator(db)
        
        # Mock database fetching
        async def mock_get(model_cls, id_val):
            if model_cls == APIIntegration and id_val == integration_id:
                return integration
            for endpoint in endpoints:
                if model_cls == APIEndpoint and id_val == endpoint.id:
                    return endpoint
            return None
        
        generator.db.get = mock_get
        
        # Test each endpoint
        for endpoint in endpoints:
            print(f"\n=== Testing {endpoint.method} {endpoint.path} ===")
            
            # Generate templates
            templates = await generator._generate_action_templates(
                endpoint_id=endpoint.id,
                tenant_id=tenant_id,
                api_key="test_api"
            )
            
            # Check that response format is generated
            for template in templates:
                if template.get("response_format"):
                    print(f"\nTemplate: {template['key']}")
                    print(f"Response format type: {template['response_format']['type']}")
                    
                    if template['response_format']['type'] == 'structured':
                        print("\nGenerated template:")
                        print("---")
                        print(template['response_format']['template'])
                        print("---")
                    
                    # Verify the format is appropriate for the response type
                    if endpoint.path == "/items":
                        # Should have list formatting
                        assert "Total items:" in template['response_format']['template']
                        assert "{% for item in" in template['response_format']['template']
                    elif endpoint.path == "/items/{id}":
                        # Should have object formatting
                        assert "## Details" in template['response_format']['template']
                    elif endpoint.path == "/alerts":
                        # Should have direct array formatting
                        assert "Total items:" in template['response_format']['template']
                        assert "{% for item in response[:10] %}" in template['response_format']['template']
            
            print("\n✅ Response format generated successfully")


if __name__ == "__main__":
    asyncio.run(test_response_format_generation())