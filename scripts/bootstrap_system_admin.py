#!/usr/bin/env python
"""
Bootstrap script to create the first system admin.

This script should only run when no system admins exist in the system.
It creates the first system admin based on a Clerk user ID and optionally
creates an initial API key for that admin.

Usage:
    python -m scripts.bootstrap_system_admin --clerk-user-id <CLERK_USER_ID> [--api-key-name <NAME>]

Environment variables:
    BOOTSTRAP_SECRET: Optional secret key to restrict who can run this script
    INITIAL_SYSTEM_ADMIN_CLERK_USER_ID: Optional Clerk user ID (alternative to command-line arg)
"""

import argparse
import asyncio
import os
import sys
from datetime import datetime, timedelta

from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.crud.crud_system_admin import (
    create_system_admin,
    create_system_admin_api_key,
    get_multi_system_admin,
)
from src.coherence.db.session import async_session
from src.coherence.schemas.system_admin import (
    <PERSON>Ad<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    SystemAdmin<PERSON>reate,
)


async def bootstrap_system_admin(
    clerk_user_id: str,
    api_key_name: str = None,
    bootstrap_secret: str = None,
) -> None:
    """
    Create the first system admin if none exists.
    
    Args:
        clerk_user_id: Clerk user ID for the system admin
        api_key_name: Optional name for an initial API key
        bootstrap_secret: Optional secret to validate
    """
    # Validate bootstrap secret if configured
    env_secret = os.environ.get("BOOTSTRAP_SECRET")
    if env_secret and bootstrap_secret != env_secret:
        print("Error: Invalid bootstrap secret")
        sys.exit(1)
    
    # Get a database session
    async with async_session() as session:
        db: AsyncSession = session
        
        # Check if any system admins already exist
        existing_admins = await get_multi_system_admin(db=db, limit=1)
        if existing_admins:
            print("Error: System admins already exist. Bootstrap is only for initial setup.")
            sys.exit(1)
        
        try:
            # Create the system admin
            admin_create = SystemAdminCreate(
                clerk_user_id=clerk_user_id,
                created_by="bootstrap_script"
            )
            system_admin = await create_system_admin(db=db, obj_in=admin_create)
            print(f"Created system admin with ID: {system_admin.id}")
            
            # Create an initial API key if requested
            if api_key_name:
                # Create API key - expires in 90 days
                expires_at = datetime.now() + timedelta(days=90)
                api_key_create = SystemAdminAPIKeyCreate(
                    name=api_key_name,
                    created_by="bootstrap_script",
                    expires_at=expires_at,
                    permissions={"admin": True}  # Full admin permissions
                )
                _, raw_api_key = await create_system_admin_api_key(
                    db=db,
                    obj_in=api_key_create,
                    system_admin_id=system_admin.id
                )
                
                print("Created initial API key:")
                print(f"API Key: {raw_api_key}")
                print("IMPORTANT: Store this key securely; it will not be shown again.")
                print(f"Expires: {expires_at.isoformat()}")
            
            print("Bootstrap completed successfully.")
            
        except Exception as e:
            await db.rollback()
            print(f"Error bootstrapping system admin: {str(e)}")
            sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description="Bootstrap the first system admin")
    parser.add_argument(
        "--clerk-user-id",
        required=False,
        help="Clerk user ID for the system admin",
    )
    parser.add_argument(
        "--api-key-name",
        required=False,
        default="Initial Admin API Key",
        help="Name for the initial API key",
    )
    parser.add_argument(
        "--bootstrap-secret",
        required=False,
        help="Secret to authenticate bootstrap operation",
    )
    
    args = parser.parse_args()
    
    # Check for clerk user ID in args or env
    clerk_user_id = args.clerk_user_id or os.environ.get("INITIAL_SYSTEM_ADMIN_CLERK_USER_ID")
    if not clerk_user_id:
        print("Error: Clerk user ID is required. Provide via --clerk-user-id or INITIAL_SYSTEM_ADMIN_CLERK_USER_ID env var.")
        sys.exit(1)
    
    asyncio.run(bootstrap_system_admin(
        clerk_user_id=clerk_user_id,
        api_key_name=args.api_key_name,
        bootstrap_secret=args.bootstrap_secret,
    ))


if __name__ == "__main__":
    main()