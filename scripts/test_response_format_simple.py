#!/usr/bin/env python3
"""Simple test of response format feature in templates."""

import asyncio
import logging
import sys
import uuid
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import select
from src.coherence.db.session import async_session
from src.coherence.models.template import Template

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


async def test_response_format():
    """Test the response format feature."""
    
    # Use the imported async_session
    async with async_session() as db:
        try:
            # Query the template directly
            result = await db.execute(
                select(Template).where(
                    Template.key == "weather_alerts_formatted",
                    Template.category == "response_gen",
                    Template.tenant_id == uuid.UUID("ce1e63e1-a9ec-4865-bb14-3fb8383ba0e8")
                ).order_by(Template.version.desc())
            )
            template = result.scalars().first()
            
            if template:
                logger.info(f"Found template: {template.key}")
                logger.info(f"Response format: {template.response_format}")
                
                # Test response formatter directly
                from src.coherence.template_system.response_formatter import ResponseFormatter
                formatter = ResponseFormatter(template.response_format)
                
                # Test context
                test_context = {
                    "result": {
                        "success": True,
                        "total": 260,
                        "land": 161,
                        "marine": 99,
                        "regions": {
                            "AL": 48,
                            "AT": 7,
                            "GL": 21,
                            "PA": 22,
                            "PI": 1
                        }
                    },
                    "results": {
                        "default": {
                            "success": True,
                            "status_code": 200,
                            "data": {
                                "total": 260,
                                "land": 161,
                                "marine": 99
                            }
                        }
                    }
                }
                
                # Format the response
                formatted = formatter.format_response(test_context)
                logger.info("Formatted response:")
                print("\n" + formatted + "\n")
            else:
                logger.error("Template not found")
                
        except Exception as e:
            logger.error(f"Error testing response format: {str(e)}", exc_info=True)
            raise


if __name__ == "__main__":
    asyncio.run(test_response_format())