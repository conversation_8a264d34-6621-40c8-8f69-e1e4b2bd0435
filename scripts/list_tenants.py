import os

import psycopg2

# Get database connection parameters from environment variables or use defaults
db_host = os.environ.get("COHERENCE_POSTGRES_SERVER", "localhost")
db_port = os.environ.get("COHERENCE_POSTGRES_PORT", "5432")
db_name = os.environ.get("COHERENCE_POSTGRES_DB", "coherence")
db_user = os.environ.get("COHERENCE_POSTGRES_USER", "postgres")
db_password = os.environ.get("COHERENCE_POSTGRES_PASSWORD", "postgres")

def list_tenants():
    """List all tenants with their clerk_org_id for debugging."""
    print("=== Listing all tenants ===")
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    try:
        with conn.cursor() as cursor:
            # Query all tenants with their clerk_org_id
            cursor.execute("""
                SELECT id, name, clerk_org_id, industry_pack 
                FROM tenants
                ORDER BY name
            """)
            
            rows = cursor.fetchall()
            print(f"Found {len(rows)} tenants in the database")
            
            for row in rows:
                tenant_id, name, clerk_org_id, industry_pack = row
                print(f"Tenant ID: {tenant_id}")
                print(f"Tenant Name: {name}")
                print(f"Clerk Org ID: {clerk_org_id}")
                print(f"Industry Pack: {industry_pack}")
                print("=" * 50)
                
    finally:
        conn.close()

if __name__ == "__main__":
    list_tenants()