#!/usr/bin/env python
"""
Script to create a new tenant with an API key.

Usage:
    python -m scripts.create_tenant --name "Tenant Name" [--industry "Industry"] [--tier "Tier"]

This script will create a new tenant in the database and generate an API key.
"""

import argparse
import hashlib
import secrets
import sys
import uuid
from datetime import datetime, timezone

import sqlalchemy
from sqlalchemy.orm import Session

from src.coherence.db.session import SessionLocal
from src.coherence.models.tenant import APIKey, Tenant

# Add the current directory to the path
sys.path.append(".")


def generate_api_key() -> str:
    """Generate a random API key."""
    return secrets.token_urlsafe(32)


def create_tenant(
    session: Session,
    name: str,
    industry_pack: str = None,
    compliance_tier: str = None,
) -> tuple[Tenant, str]:
    """
    Create a new tenant with a generated API key.

    Args:
        session: Database session
        name: Name of the tenant
        industry_pack: Optional industry pack for the tenant
        compliance_tier: Optional compliance tier

    Returns:
        Tuple of (tenant, api_key)
    """
    # Create the tenant
    tenant = Tenant(
        id=uuid.uuid4(),
        name=name,
        industry_pack=industry_pack,
        compliance_tier=compliance_tier,
    )
    session.add(tenant)

    # Generate API key
    api_key = generate_api_key()
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()

    # Create API key record
    api_key_obj = APIKey(
        id=uuid.uuid4(),
        tenant_id=tenant.id,
        label="Default API Key",
        key_hash=key_hash,
        created_at=datetime.now(timezone.utc),
    )
    session.add(api_key_obj)

    # Commit the changes
    session.commit()

    return tenant, api_key


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Create a new tenant with an API key")
    parser.add_argument("--name", required=True, help="Name of the tenant")
    parser.add_argument("--industry", help="Industry pack for the tenant")
    parser.add_argument("--tier", help="Compliance tier")

    args = parser.parse_args()

    try:
        # Create DB session
        session = SessionLocal()

        # Create tenant
        tenant, api_key = create_tenant(
            session=session,
            name=args.name,
            industry_pack=args.industry,
            compliance_tier=args.tier,
        )

        # Print the result
        print("Tenant created successfully:")
        print(f"  ID:        {tenant.id}")
        print(f"  Name:      {tenant.name}")
        print(f"  Industry:  {tenant.industry_pack or 'None'}")
        print(f"  Tier:      {tenant.compliance_tier or 'None'}")
        print(f"  API Key:   {api_key}")
        print("\nIMPORTANT: Store this API key securely. It will not be shown again.")

    except sqlalchemy.exc.IntegrityError:
        print(f"Error: A tenant with the name '{args.name}' already exists.")
        return 1
    except Exception as e:
        print(f"Error creating tenant: {e}")
        return 1
    finally:
        session.close()

    return 0


if __name__ == "__main__":
    sys.exit(main())
