#!/usr/bin/env python
"""
Script to debug vector search for embeddings.
"""

import asyncio
import logging

from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.qdrant_client import get_qdrant_client

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test tenant ID
TEST_TENANT_ID = "6e8fad41-8656-425d-b96c-732e8e50c0f5"

async def test_vector_search():
    """Test vector search with real embeddings."""
    logger.info("Testing vector search...")
    
    # Get Qdrant client
    qdrant_client = await get_qdrant_client()
    
    # Get embedding provider
    llm_factory = LLMFactory()
    provider = llm_factory.create_provider(
        name="openai",
        model="text-embedding-3-small"
    )
    
    # Test queries
    test_queries = [
        "What is the weather like in New York today?",
        "How's the weather in San Francisco?",
        "Tell me the weather forecast for Seattle",
        "What's the temperature in Boston?"
    ]
    
    for query in test_queries:
        logger.info(f"Testing query: '{query}'")
        
        # Generate embedding
        embedding = await provider.generate_embedding(query)
        logger.info(f"Generated embedding with {len(embedding)} dimensions")
        
        # Search for matches
        collection_name = f"intent_idx_{TEST_TENANT_ID}_user"
        results = await qdrant_client.search(
            collection_name=collection_name,
            query_vector=embedding,
            limit=5
        )
        
        # Display results
        logger.info(f"Query '{query}' found {len(results)} matches:")
        for i, result in enumerate(results):
            logger.info(f"  Match {i+1}: score={result.score}, intent={result.payload.get('intent')}")
            logger.info(f"  Match text: {result.payload.get('text')}")
            logger.info(f"  Match params: {result.payload.get('parameters')}")

async def main():
    """Main entry point."""
    try:
        await test_vector_search()
    except Exception as e:
        logger.error(f"Error during testing: {e}")

if __name__ == "__main__":
    asyncio.run(main())