#!/usr/bin/env python
"""
Script to add a weather intent to the system.

This creates a simple weather intent that can be used to test the intent resolution system.
"""

import asyncio
import logging
import uuid
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.db.session import SessionLocal
from src.coherence.intent_pipeline.schemas.intent import (
    IntentDefinition,
    ParameterDefinition,
    ParameterType,
)
from src.coherence.models.template import Template

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define a simple weather intent for testing
WEATHER_INTENT = IntentDefinition(
    name="weather_query",
    description="Get weather information for a location",
    examples=[
        "What's the weather like in Chicago?",
        "Tell me the weather forecast for New York",
        "How's the weather in San Francisco?",
        "Is it raining in Seattle?",
        "Weather for Miami",
        "What's the temperature in Boston?",
        "Will it rain tomorrow in Denver?",
    ],
    parameters={
        "location": ParameterDefinition(
            name="location",
            type=ParameterType.STRING,
            required=True,
            description="Location to get weather for",
            prompt="What location would you like the weather for?",
        ),
        "date": ParameterDefinition(
            name="date",
            type=ParameterType.DATE,
            required=False,
            description="Date for the weather forecast",
            prompt="What date would you like the weather for?",
            default_value="today",
        ),
    },
    required_fields={"location"},
    action_class="WeatherAction",
)

# Add a vector-based intent template to match this intent
WEATHER_TEMPLATE = """
{# Weather intent template for vector matching #}
{% block system_instruction %}
This template helps recognize weather-related queries in user messages.
{% endblock %}

{% block intent_patterns %}
- What is the weather like in {location}?
- How's the weather in {location}?
- Weather forecast for {location}
- Tell me the weather in {location}
- Weather for {location} {date}
- Will it rain in {location}?
- Is it sunny in {location}?
- Temperature in {location}
{% endblock %}

{% block parameters %}
- location: The city, state, or country to get weather for
- date: The date for the forecast (optional, defaults to today)
{% endblock %}

{% block examples %}
"What's the weather like in Chicago?" -> weather_query(location="Chicago")
"How's the weather in San Francisco today?" -> weather_query(location="San Francisco", date="today")
"Tell me the weather forecast for New York tomorrow" -> weather_query(location="New York", date="tomorrow")
"Weather for Miami next week" -> weather_query(location="Miami", date="next week")
{% endblock %}
"""

# Vector embeddings for weather examples to simulate Tier 1 matching
WEATHER_EMBEDDINGS = [
    "What's the weather like in {location}?",
    "How's the weather in {location}?",
    "Tell me the weather forecast for {location}",
    "Weather in {location}",
    "Is it going to rain in {location}?",
    "What's the temperature in {location}?",
    "Will it be sunny in {location} tomorrow?",
]


async def add_weather_intent_template(db: AsyncSession, tenant_id: Optional[uuid.UUID] = None):
    """Add a weather intent template to the system."""
    # Check if template already exists
    from sqlalchemy import select
    
    scope = "global" if tenant_id is None else "tenant"
    query = select(Template).where(
        Template.key == "WEATHER_QUERY",
        Template.category == "intent_router",
        Template.scope == scope,
        Template.scope_id == tenant_id,
        Template.tenant_id == tenant_id
    )
    
    result = await db.execute(query)
    existing_template = result.scalars().first()
    
    if existing_template:
        logger.info(f"Weather intent template already exists: {existing_template.id}")
        return existing_template
    
    # Create the template in the database if it doesn't exist
    weather_template = Template(
        id=uuid.uuid4(),
        key="WEATHER_QUERY",
        category="intent_router",
        body=WEATHER_TEMPLATE,
        scope=scope,
        scope_id=tenant_id,
        tenant_id=tenant_id,
        language="en",
        version=1,
        description="Template for matching weather queries",
        actions={
            "intent": "weather_query",
            "parameters": {
                "location": {"type": "string", "required": True},
                "date": {"type": "date", "required": False, "default": "today"},
            },
        },
        parameters={
            "location": {"type": "string", "required": True},
            "date": {"type": "date", "required": False, "default": "today"},
        },
    )

    db.add(weather_template)
    await db.commit()
    logger.info(f"Created weather intent template: {weather_template.id}")
    return weather_template


async def add_weather_intent(tenant_id: Optional[uuid.UUID] = None):
    """Add a weather intent to the system for the given tenant (or global if None)."""
    async with SessionLocal() as db:
        # Add the template
        template = await add_weather_intent_template(db, tenant_id)
        logger.info(f"Added weather intent template with ID: {template.id}")

        # TODO: Add vector embeddings to Qdrant (not implemented here)
        # This would normally involve:
        # 1. Generating embeddings for the example phrases
        # 2. Storing them in Qdrant with the intent ID
        # 3. Training the vector matching model

        # Print success message
        if tenant_id:
            logger.info(f"Successfully added weather intent for tenant: {tenant_id}")
        else:
            logger.info("Successfully added global weather intent")


async def main():
    """Main entry point."""
    logger.info("Adding weather intent...")
    
    # Get all tenant IDs
    async with SessionLocal() as db:
        from sqlalchemy import select

        from src.coherence.models.tenant import Tenant
        
        query = select(Tenant.id)
        result = await db.execute(query)
        tenant_ids = [row[0] for row in result.all()]
    
    # Add global weather intent
    await add_weather_intent()
    
    # Add tenant-specific weather intents
    for tenant_id in tenant_ids:
        await add_weather_intent(tenant_id)
    
    logger.info("Weather intent added successfully!")


if __name__ == "__main__":
    asyncio.run(main())