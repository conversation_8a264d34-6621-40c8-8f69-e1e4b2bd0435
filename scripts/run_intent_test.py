#!/usr/bin/env python
"""
Interactive test script for the Coherence intent resolution pipeline.

This script provides a simple CLI to interact with the intent pipeline,
allowing you to test intent resolution, parameter extraction, and
multi-turn conversations.

Usage:
    python -m scripts.run_intent_test

Environment variables:
    OPENAI_API_KEY: OpenAI API key for LLM calls
"""

import asyncio
import json
import os
import uuid
from typing import Optional

# Now import project modules
from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.qdrant_client import get_qdrant_client
from src.coherence.core.redis_client import get_redis_client
from src.coherence.db.session import async_session


class IntentTester:
    """Interactive CLI for testing the intent pipeline."""

    def __init__(self):
        """Initialize the tester."""
        self.tenant_id = str(uuid.uuid4())
        self.user_id = str(uuid.uuid4())
        self.user_role = "user"
        self.conversation_id = str(uuid.uuid4())
        self.orchestrator = None
        self.db = None
        self.redis_client = None
        self.qdrant_client = None

    async def setup(self):
        """Set up the test environment."""
        print("Setting up the intent pipeline...")

        # Check for API key
        if "OPENAI_API_KEY" not in os.environ:
            print("WARNING: OPENAI_API_KEY not set. Using mock provider instead.")

        # Create dependencies
        self.db = async_session()
        self.redis_client = await get_redis_client()
        self.qdrant_client = await get_qdrant_client()
        llm_factory = LLMFactory()

        # Create orchestrator
        # Create the orchestrator manually since get_chat_orchestrator is a dependency injection function
        from src.coherence.intent_pipeline.orchestrator import ChatOrchestrator
        from src.coherence.intent_pipeline.resolver import get_intent_resolver
        from src.coherence.intent_pipeline.parameter_extraction import get_parameter_extractor
        from src.coherence.template_system.services.template_service import TemplateService
        from src.coherence.openapi_adapter.credential_manager import get_credential_manager
        from src.coherence.openapi_adapter.dynamic_executor import get_dynamic_executor
        from src.coherence.core.config import settings
        
        # Create the components
        intent_resolver = await get_intent_resolver(
            db=self.db,
            qdrant_client=self.qdrant_client,
            llm_factory=llm_factory,
        )
        
        parameter_extractor = await get_parameter_extractor(
            llm_provider=llm_factory.create_provider(
                name=settings.LLM_PROVIDER,
                model=settings.PARAM_COMPLETION_MODEL,
            ),
            max_completion_rounds=settings.MAX_COMPLETION_ROUNDS,
        )
        
        llm_provider = llm_factory.create_provider(
            name=settings.LLM_PROVIDER,
            model=settings.LLM_MODEL,
        )
        
        template_service = TemplateService()
        credential_manager = await get_credential_manager(self.db)
        action_executor = await get_dynamic_executor(credential_manager)
        
        # Create the orchestrator
        self.orchestrator = ChatOrchestrator(
            db=self.db,
            redis_client=self.redis_client,
            intent_resolver=intent_resolver,
            parameter_extractor=parameter_extractor,
            llm_provider=llm_provider,
            template_service=template_service,
            action_executor=action_executor,
        )

        print("Pipeline set up successfully!")
        print(f"Tenant ID: {self.tenant_id}")
        print(f"User ID: {self.user_id}")
        print(f"Conversation ID: {self.conversation_id}")
        print("\nReady to process messages.")

    async def cleanup(self):
        """Clean up resources."""
        if self.redis_client:
            await self.redis_client.close()

        if self.qdrant_client:
            await self.qdrant_client.close()
            
        if self.db:
            await self.db.close()

    async def process_message(
        self, message: str, conversation_id: Optional[str] = None
    ):
        """Process a user message."""
        print(f"\nProcessing: '{message}'")

        # Use the specified conversation ID or the default one
        current_conversation_id = conversation_id or self.conversation_id

        # Process the message
        result = await self.orchestrator.handle_message(
            tenant_id=self.tenant_id,
            user_id=self.user_id,
            user_role=self.user_role,
            message=message,
            context={"conversation_id": current_conversation_id},
        )

        # Display the result
        print("\nResult:")
        print(f"Type: {result.get('type')}")

        if result.get("type") == "ask":
            print(f"Missing field: {result.get('missing_field')}")
            print(f"Question: {result.get('question')}")

            # Get user input for the missing parameter
            user_input = input("\nYour response: ")

            # Continue the conversation
            continue_result = await self.orchestrator.continue_conversation(
                tenant_id=self.tenant_id,
                conversation_id=current_conversation_id,
                user_id=self.user_id,
                message=user_input,
            )

            # Display the continuation result
            print("\nContinuation result:")
            print(f"Type: {continue_result.get('type')}")

            if continue_result.get("type") == "action":
                print(f"Outcome: {continue_result.get('outcome')}")
            elif continue_result.get("type") == "ask":
                print(f"Still need more info - {continue_result.get('question')}")
                print("(Use the same conversation ID to continue)")
            elif continue_result.get("type") == "reply":
                print(f"Response: {continue_result.get('outcome')}")

        elif result.get("type") == "action":
            print(f"Outcome: {result.get('outcome')}")

        elif result.get("type") == "reply":
            print(f"Response: {result.get('outcome')}")

        else:
            print(f"Full result: {json.dumps(result, indent=2)}")

        return result

    async def run_cli(self):
        """Run the CLI interface."""
        await self.setup()

        try:
            print("\n=== Coherence Intent Pipeline Tester ===")
            print("Enter messages to test intent resolution.")
            print("Type 'exit' or 'quit' to exit.")
            print("Type 'new' to start a new conversation.")
            print("Type 'help' for more commands.")

            while True:
                try:
                    user_input = input("\nEnter a message: ")

                    if user_input.lower() in ["exit", "quit"]:
                        break

                    elif user_input.lower() == "help":
                        print("\nCommands:")
                        print("  exit, quit - Exit the application")
                        print("  new - Start a new conversation")
                        print("  id - Show current conversation ID")
                        print("  use <id> - Use a specific conversation ID")
                        print("  help - Show this help message")

                    elif user_input.lower() == "new":
                        self.conversation_id = str(uuid.uuid4())
                        print(
                            f"Started new conversation with ID: {self.conversation_id}"
                        )

                    elif user_input.lower() == "id":
                        print(f"Current conversation ID: {self.conversation_id}")

                    elif user_input.lower().startswith("use "):
                        try:
                            new_id = user_input[4:].strip()
                            uuid.UUID(new_id)  # Validate UUID format
                            self.conversation_id = new_id
                            print(f"Now using conversation ID: {self.conversation_id}")
                        except ValueError:
                            print("Invalid UUID format. Please provide a valid UUID.")

                    elif user_input.strip():
                        await self.process_message(user_input)

                except KeyboardInterrupt:
                    print("\nExiting...")
                    break

                except Exception as e:
                    print(f"\nError: {str(e)}")

        finally:
            await self.cleanup()
            print("\nThank you for using the Coherence Intent Pipeline Tester!")


async def main():
    """Run the test CLI."""
    tester = IntentTester()
    await tester.run_cli()


if __name__ == "__main__":
    asyncio.run(main())
