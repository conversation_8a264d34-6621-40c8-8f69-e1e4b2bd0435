#!/usr/bin/env python
"""
Check Vector Dimensions Script

This script checks the dimensions of all vector collections in Qdrant and 
reports any collections with non-standard dimensions.

Usage:
    python -m scripts.check_vector_dimensions [--tenant TENANT_ID] [--fail-on-mismatch]

Options:
    --tenant TENANT_ID     Check only collections for the specified tenant
    --fail-on-mismatch     Exit with error code if any collection has non-standard dimensions
    --fix                  Print rebuild commands for collections with non-standard dimensions
"""

import argparse
import asyncio
import sys
from typing import List, Optional, Tuple

from src.coherence.core.config import settings
from src.coherence.core.qdrant_client import QdrantClient


async def check_collection_dimensions(tenant_id: Optional[str] = None, fail_on_mismatch: bool = False, fix: bool = False) -> None:
    """
    Check dimensions of all vector collections in Qdrant.
    
    Args:
        tenant_id: Optional tenant ID to check only collections for that tenant
        fail_on_mismatch: If True, exit with error code if any collection has non-standard dimensions
        fix: If True, print rebuild commands for collections with non-standard dimensions
    """
    qdrant_client = QdrantClient()
    collections = await qdrant_client.list_collections()
    
    # Filter collections to only include index collections
    index_collections = [c for c in collections if "_idx_" in c.name]
    
    # Further filter by tenant if specified
    if tenant_id:
        index_collections = [c for c in index_collections if tenant_id in c.name]
    
    print(f"Found {len(index_collections)} index collections{f' for tenant {tenant_id}' if tenant_id else ''}.")
    
    # Check dimensions of each collection
    mismatched_collections: List[Tuple[str, int]] = []
    for collection in index_collections:
        try:
            info = await qdrant_client.get_collection(collection.name)
            size = info.config.params.vectors.size
            expected_size = settings.EMBEDDING_DIMENSION
            
            if size != expected_size:
                mismatched_collections.append((collection.name, size))
                print(f"⚠️  Collection {collection.name} has dimension {size} (expected {expected_size})")
            else:
                print(f"✅ Collection {collection.name} has correct dimension {size}")
        except Exception as e:
            print(f"❌ Error checking collection {collection.name}: {e}")
    
    # Summary
    if mismatched_collections:
        print(f"\n🔴 Found {len(mismatched_collections)} collections with non-standard dimensions:")
        for name, size in mismatched_collections:
            print(f"  - {name}: {size}d (expected {settings.EMBEDDING_DIMENSION}d)")
        
        if fix:
            print("\n📋 Run the following commands to fix these collections:")
            for name, _ in mismatched_collections:
                # Determine if it's a template or intent collection
                if "template_idx" in name:
                    tenant_part = name.split("template_idx_")[-1] if "template_idx_" in name else ""
                    tenant_arg = f"--tenant {tenant_part}" if tenant_part and tenant_part != "global" else ""
                    print(f"python -m scripts.rebuild_template_index {tenant_arg}")
                elif "intent_idx" in name:
                    tenant_part = name.split("intent_idx_")[-1].split("_")[0] if "intent_idx_" in name else ""
                    tenant_arg = f"--tenant {tenant_part}" if tenant_part else ""
                    print(f"python -m scripts.rebuild_intent_index {tenant_arg}")
        
        if fail_on_mismatch:
            print(f"\n❌ Exiting with error due to {len(mismatched_collections)} collections with non-standard dimensions")
            sys.exit(1)
    else:
        print(f"\n🟢 All collections have the standard dimension ({settings.EMBEDDING_DIMENSION})")


async def main():
    parser = argparse.ArgumentParser(description="Check vector collection dimensions")
    parser.add_argument("--tenant", help="Check only collections for the specified tenant")
    parser.add_argument("--fail-on-mismatch", action="store_true", help="Exit with error code if any collection has non-standard dimensions")
    parser.add_argument("--fix", action="store_true", help="Print rebuild commands for collections with non-standard dimensions")
    
    args = parser.parse_args()
    
    await check_collection_dimensions(args.tenant, args.fail_on_mismatch, args.fix)


if __name__ == "__main__":
    asyncio.run(main())