#!/usr/bin/env python3
"""
Utility to decode and validate a Clerk JWT token.
This helps debug issues with claims extraction.
"""

import argparse
import base64
import json
import sys
from typing import Any, Dict, Optional


def parse_args():
    parser = argparse.ArgumentParser(description='Debug a Clerk JWT token')
    parser.add_argument('--token', type=str, help='The JWT token to debug')
    parser.add_argument('--token-file', type=str, help='File containing the JWT token')
    return parser.parse_args()

def decode_jwt(token: str) -> Dict[str, Any]:
    """
    Decode a JWT token without verification.
    """
    parts = token.split('.')
    if len(parts) != 3:
        raise ValueError(f"Invalid JWT format, expected 3 parts but got {len(parts)}")
    
    # Some base64 implementations might require padding
    payload_base64 = parts[1].replace('-', '+').replace('_', '/')
    padding = len(payload_base64) % 4
    if padding:
        payload_base64 += '=' * (4 - padding)
    
    try:
        payload_json = base64.b64decode(payload_base64)
        payload = json.loads(payload_json)
        return payload
    except Exception as e:
        print(f"Error decoding payload: {e}")
        raise

def extract_session_claims(payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Extract session claims from the payload.
    """
    return payload.get("__session", {})

def print_claims_analysis(claims: Dict[str, Any], session_claims: Dict[str, Any]):
    """
    Print detailed analysis of the JWT claims.
    """
    print("\n=== JWT Claims Analysis ===")
    print("\nTop-level claims:")
    for key, value in claims.items():
        if key != "__session":  # Skip session claims for now
            print(f"  {key}: {value}")
    
    print("\nSession claims:")
    if not session_claims:
        print("  No session claims found!")
    else:
        for key, value in session_claims.items():
            print(f"  {key}: {value} (type: {type(value).__name__})")
    
    # Check for organization-related claims
    print("\nOrganization-related claims:")
    org_id = session_claims.get("org_id")
    org_name = session_claims.get("org_name")
    org_role = session_claims.get("org_role")
    
    if not any([org_id, org_name, org_role]):
        print("  No organization-related claims found!")
    else:
        print(f"  org_id: {org_id} (type: {type(org_id).__name__})")
        print(f"  org_name: {org_name} (type: {type(org_name).__name__})")
        print(f"  org_role: {org_role} (type: {type(org_role).__name__})")
    
    # Check for template placeholders
    print("\nTemplate placeholder check:")
    for key, value in session_claims.items():
        if isinstance(value, str) and (value.startswith("{{") or "{{" in value):
            print(f"  WARNING: Detected possible unrendered template in {key}: {value}")

def main():
    args = parse_args()
    
    # Get token from args or file
    token = None
    if args.token:
        token = args.token
    elif args.token_file:
        try:
            with open(args.token_file, 'r') as f:
                token = f.read().strip()
        except Exception as e:
            print(f"Error reading token file: {e}")
            sys.exit(1)
    else:
        print("Please provide either --token or --token-file")
        sys.exit(1)
    
    try:
        # Decode token
        payload = decode_jwt(token)
        
        # Extract session claims
        session_claims = extract_session_claims(payload)
        
        # Print raw data
        print("\n=== Raw JWT Payload ===")
        print(json.dumps(payload, indent=2))
        
        print("\n=== Raw Session Claims ===")
        print(json.dumps(session_claims, indent=2))
        
        # Analyze
        print_claims_analysis(payload, session_claims)
        
    except Exception as e:
        print(f"Error processing token: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()