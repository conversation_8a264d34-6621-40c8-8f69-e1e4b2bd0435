#!/usr/bin/env python
"""
Test script for OpenAPI integration functionality.

This script:
1. Imports an OpenAPI specification
2. Generates actions for the endpoints
3. Generates intents for the actions
4. Simulates an intent resolution
"""

import argparse
import asyncio
import json
import logging
import uuid
from typing import Dict, List

import httpx
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.db.deps import get_db
from src.coherence.db.session import get_async_session
from src.coherence.openapi_adapter.action_generator import get_action_generator
from src.coherence.openapi_adapter.adapter import get_openapi_adapter
from src.coherence.openapi_adapter.intent_mapper import get_intent_mapper

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("openapi_integration_test")


async def import_openapi_spec(
    db: AsyncSession, tenant_id: uuid.UUID, spec_file: str, name: str
) -> uuid.UUID:
    """Import an OpenAPI specification.
    
    Args:
        db: Database session
        tenant_id: Tenant ID
        spec_file: Path to OpenAPI specification file
        name: Name for the integration
        
    Returns:
        ID of the created integration
    """
    logger.info(f"Importing OpenAPI spec from {spec_file}")
    
    # Load the OpenAPI spec
    with open(spec_file, "r") as f:
        spec_data = json.load(f)
    
    # Create the OpenAPI adapter
    adapter = await get_openapi_adapter(db)
    
    # Import the spec
    result = await adapter.import_spec(
        tenant_id=tenant_id,
        name=name,
        spec_data=spec_data,
    )
    
    integration_id = result["integration_id"]
    logger.info(f"Created integration {name} with ID {integration_id}")
    logger.info(f"Imported {len(result['endpoints'])} endpoints")
    
    return uuid.UUID(integration_id)


async def generate_actions(
    db: AsyncSession, integration_id: uuid.UUID
) -> List[Dict]:
    """Generate actions for an integration.
    
    Args:
        db: Database session
        integration_id: Integration ID
        
    Returns:
        List of generated actions
    """
    logger.info(f"Generating actions for integration {integration_id}")
    
    # Get the action generator
    action_generator = await get_action_generator(db)
    
    # Fetch all endpoints for the integration
    result = await db.execute(
        "SELECT id FROM api_endpoints WHERE integration_id = :integration_id AND enabled = true",
        {"integration_id": integration_id},
    )
    endpoints = result.fetchall()
    
    # Generate actions for each endpoint
    actions = []
    for endpoint in endpoints:
        try:
            # Generate the action
            action_result = await action_generator.generate_action_class(endpoint.id)
            actions.append(action_result)
            logger.info(f"Generated action {action_result['class_name']}")
        except Exception as e:
            logger.warning(f"Failed to generate action for endpoint {endpoint.id}: {str(e)}")
    
    logger.info(f"Generated {len(actions)} actions")
    return actions


async def generate_intents(
    db: AsyncSession, integration_id: uuid.UUID, tenant_id: uuid.UUID
) -> List[Dict]:
    """Generate intents for an integration.
    
    Args:
        db: Database session
        integration_id: Integration ID
        tenant_id: Tenant ID
        
    Returns:
        List of generated intents
    """
    logger.info(f"Generating intents for integration {integration_id}")
    
    # Get the intent mapper
    intent_mapper = await get_intent_mapper(db)
    
    # Generate intents for the integration
    intents = await intent_mapper.generate_intents_for_integration(
        integration_id=integration_id,
        tenant_id=tenant_id,
    )
    
    logger.info(f"Generated {len(intents)} intents")
    return [intent.model_dump() for intent in intents]


async def test_http_endpoints(api_key: str, base_url: str = "http://localhost:8000") -> None:
    """Test the HTTP API endpoints for OpenAPI integration.
    
    Args:
        api_key: API key for authentication
        base_url: Base URL for the API
    """
    logger.info(f"Testing HTTP endpoints against {base_url}")
    
    # Setup HTTP client
    headers = {"X-API-Key": api_key}
    async with httpx.AsyncClient(base_url=base_url, headers=headers) as client:
        # Test listing integrations
        logger.info("Testing GET /v1/admin/integrations/integrations")
        response = await client.get("/v1/admin/integrations/integrations")
        
        if response.status_code == 200:
            integrations = response.json()
            logger.info(f"Found {len(integrations)} integrations")
            
            # Test individual integration details if any exist
            if integrations:
                integration_id = integrations[0]["id"]
                logger.info(f"Testing GET /v1/admin/integrations/integrations/{integration_id}")
                response = await client.get(f"/v1/admin/integrations/integrations/{integration_id}")
                
                if response.status_code == 200:
                    integration_details = response.json()
                    logger.info(f"Integration {integration_details['name']} has {len(integration_details['endpoints'])} endpoints")
                else:
                    logger.error(f"Failed to get integration details: {response.status_code} - {response.text}")
        else:
            logger.error(f"Failed to list integrations: {response.status_code} - {response.text}")


async def main() -> None:
    """Main function for the test script."""
    parser = argparse.ArgumentParser(description="Test OpenAPI integration functionality")
    parser.add_argument(
        "--tenant-id", required=True, help="Tenant ID (UUID)"
    )
    parser.add_argument(
        "--spec-file", help="Path to OpenAPI specification file to import"
    )
    parser.add_argument(
        "--name", default="Test API", help="Name for the integration"
    )
    parser.add_argument(
        "--api-key", help="API key for testing HTTP endpoints"
    )
    parser.add_argument(
        "--base-url", default="http://localhost:8000", help="Base URL for the API"
    )
    
    args = parser.parse_args()
    tenant_id = uuid.UUID(args.tenant_id)
    
    # Get a database session
    async_session_maker = await get_async_session()
    async with async_session_maker() as session:
        db = await get_db(session)
        
        if args.spec_file:
            # Import the OpenAPI spec
            integration_id = await import_openapi_spec(
                db=db,
                tenant_id=tenant_id,
                spec_file=args.spec_file,
                name=args.name,
            )
            
            # Generate actions
            await generate_actions(
                db=db,
                integration_id=integration_id,
            )
            
            # Generate intents
            await generate_intents(
                db=db,
                integration_id=integration_id,
                tenant_id=tenant_id,
            )
            
            logger.info("OpenAPI integration test completed successfully")
            
        if args.api_key:
            # Test HTTP endpoints
            await test_http_endpoints(
                api_key=args.api_key,
                base_url=args.base_url,
            )


if __name__ == "__main__":
    asyncio.run(main())