#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to rebuild the template vector index.

This script extracts all templates from the database and indexes them
in the vector database for semantic search capabilities using the standardized
embedding model and dimension.
"""

import asyncio
import logging
import os
import sys
import time
from typing import Any, Dict, List, Optional

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.core.config import settings
from src.coherence.db.session import async_session
from src.coherence.models.template import Template
from src.coherence.services.vector_indexer import VectorIndexer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


async def get_all_templates(db: AsyncSession) -> List[Template]:
    """
    Get all templates from the database.

    Args:
        db: Database session

    Returns:
        List of all templates
    """
    query = select(Template)
    result = await db.execute(query)
    return list(result.scalars().all())


async def build_template_index(tenant_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Build the template vector index using the standardized embedding dimensions.

    Args:
        tenant_id: Optional tenant ID to filter templates by
        
    Returns:
        Report dictionary with indexing results
    """
    # Global flag to determine if embedding service is available
    embedding_available = True
    
    # Initialize report
    report = {
        "total_templates": 0,
        "successful": 0,
        "failed": 0,
        "collections": set(),
        "failures": []
    }
    
    start_time = time.time()
    
    async with async_session() as db:
        # Get templates from database
        templates = await get_all_templates(db)
        logger.info(f"Retrieved {len(templates)} templates from database")
        report["total_templates"] = len(templates)

        # Filter templates by tenant if specified
        if tenant_id:
            templates = [t for t in templates if str(t.tenant_id) == tenant_id]
            logger.info(f"Filtered to {len(templates)} templates for tenant {tenant_id}")
            report["total_templates"] = len(templates)

        # Group templates by index name
        index_templates: Dict[str, List[Template]] = {}
        for template in templates:
            # Determine index name based on scope
            if template.tenant_id:
                index_name = f"template_idx_{template.tenant_id}"
            elif hasattr(template, 'pack_id') and template.pack_id:
                index_name = f"template_idx_pack_{template.pack_id}"
            else:
                index_name = "template_idx_global"

            # Add template to appropriate index group
            if index_name not in index_templates:
                index_templates[index_name] = []
            index_templates[index_name].append(template)
            
            # Add collection to report
            report["collections"].add(index_name)

        # Create vector indexer
        vector_indexer = VectorIndexer()
        logger.info(f"Using embedding model {settings.EMBEDDING_MODEL} with dimension {settings.EMBEDDING_DIMENSION}")

        # Process each index
        for index_name, templates_group in index_templates.items():
            logger.info(f"Processing {len(templates_group)} templates for index {index_name}")

            # Index each template
            for template in templates_group:
                logger.info(f"Indexing template {template.key} ({template.id}) in {index_name}")
                try:
                    if not embedding_available:
                        logger.warning(f"Skipping template {template.id} due to embedding service unavailability")
                        report["failed"] += 1
                        report["failures"].append({
                            "template_id": str(template.id),
                            "key": template.key,
                            "reason": "embedding_unavailable"
                        })
                        continue
                        
                    # Create metadata
                    metadata = {}
                    if hasattr(template, 'version'):
                        metadata["version"] = template.version
                    if hasattr(template, 'pack_id') and template.pack_id:
                        metadata["pack_id"] = template.pack_id
                    if template.tenant_id:
                        metadata["tenant_id"] = str(template.tenant_id)
                    
                    success = await vector_indexer.upsert_template(
                        db=db,
                        template_id=str(template.id),
                        template_key=template.key,
                        template_category=template.category,
                        template_body=template.body,
                        template_description=template.description or "",
                        index_name=index_name,
                        metadata=metadata
                    )
                    
                    if success:
                        logger.info(f"Successfully indexed template {template.key}")
                        report["successful"] += 1
                    else:
                        logger.warning(f"Failed to index template {template.key}")
                        report["failed"] += 1
                        report["failures"].append({
                            "template_id": str(template.id),
                            "key": template.key,
                            "reason": "indexing_failed"
                        })
                except Exception as e:
                    if "OpenAI API" in str(e) and embedding_available:
                        embedding_available = False
                        logger.error(f"OpenAI API error: {str(e)}. Disabling embedding for remaining templates.")
                    
                    logger.error(f"Error indexing template {template.key}: {str(e)}")
                    report["failed"] += 1
                    report["failures"].append({
                        "template_id": str(template.id),
                        "key": template.key,
                        "reason": str(e)
                    })

        # Convert collections to list for better reporting
        report["collections"] = list(report["collections"])
        
        # Add timing information
        end_time = time.time()
        report["execution_time_seconds"] = end_time - start_time
        
        logger.info("Template indexing complete")
        return report


async def main() -> None:
    """Main entry point for the script."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Rebuild the template vector index"
    )
    parser.add_argument(
        "--tenant-id",
        help="Optional tenant ID to rebuild index for a specific tenant",
    )
    args = parser.parse_args()

    try:
        logger.info(f"Starting template index rebuild using {settings.EMBEDDING_MODEL} model with {settings.EMBEDDING_DIMENSION} dimensions")
        report = await build_template_index(tenant_id=args.tenant_id)
        
        # Output summary
        logger.info("\n=== Template Indexing Summary ===")
        logger.info(f"Total templates: {report['total_templates']}")
        logger.info(f"Successfully indexed: {report['successful']}")
        logger.info(f"Failed to index: {report['failed']}")
        logger.info(f"Collections updated: {', '.join(report['collections'])}")
        logger.info(f"Execution time: {report['execution_time_seconds']:.2f}s")
        
        if report["failures"]:
            logger.warning(f"\nFailed templates: {len(report['failures'])}")
            for failure in report["failures"][:5]:  # Show only first 5 failures
                logger.warning(f"  - Template {failure['key']} ({failure['template_id']}): {failure['reason']}")
            
            if len(report["failures"]) > 5:
                logger.warning(f"  ... and {len(report['failures']) - 5} more")
    except Exception as e:
        logger.error(f"Error building template index: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())