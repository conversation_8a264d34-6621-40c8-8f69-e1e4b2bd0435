import asyncio
import os
import sys

from sqlalchemy.future import select

# Add the project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.coherence.db.session import async_session
from src.coherence.models.tenant import Tenant


async def debug_clerk_tenant_mapping():
    """List all tenants with their clerk_org_id for debugging."""
    print("=== Checking Clerk Org ID to Tenant Mapping ===")
    
    async with async_session() as session:
        # Query all tenants
        result = await session.execute(select(Tenant))
        tenants = result.scalars().all()
        
        print(f"Found {len(tenants)} tenants in the database")
        
        for tenant in tenants:
            print(f"Tenant ID: {tenant.id}")
            print(f"Tenant Name: {tenant.name}")
            print(f"Clerk Org ID: {tenant.clerk_org_id}")
            print(f"Industry Pack: {tenant.industry_pack}")
            print("=" * 50)


if __name__ == "__main__":
    asyncio.run(debug_clerk_tenant_mapping())