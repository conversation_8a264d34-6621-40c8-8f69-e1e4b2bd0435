#!/usr/bin/env python3
"""Test response format feature in templates."""

import asyncio
import json
import logging
import sys
import uuid
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from src.coherence.db.base import Base
from src.coherence.db.session import async_session
from src.coherence.core.qdrant_client import get_qdrant_client
from src.coherence.template_system.services.template_service import TemplateService
from src.coherence.openapi_adapter.adapter import OpenAPIAdapter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Database URL from environment or default
DATABASE_URL = "postgresql+asyncpg://postgres:postgres@localhost:5433/coherence"


async def test_response_format():
    """Test the response format feature."""
    
    # Use the imported async_session
    async with async_session() as db:
        try:
            template_service = TemplateService()
            
            # Check if template already exists
            existing_templates = await template_service.list_templates(
                db=db,
                tenant_id=uuid.UUID("ce1e63e1-a9ec-4865-bb14-3fb8383ba0e8"),
                category="response_gen"
            )
            
            logger.info(f"Found {len(existing_templates)} templates")
            
            existing_template = None
            for t in existing_templates:
                if t.key == "weather_alerts_formatted":
                    existing_template = t
                    break
            
            if existing_template:
                logger.info(f"Found existing template: {existing_template.key}")
                template = existing_template
                # Delete duplicates if any
                for t in existing_templates:
                    if t.key == "weather_alerts_formatted" and t.id != existing_template.id:
                        # Delete template
                        await db.execute(
                            text("DELETE FROM templates WHERE id = :id"),
                            {"id": str(t.id)}
                        )
                await db.commit()
            else:
                # Create a test template with response format
                response_format = {
                    "type": "structured",
                    "sections": [
                        {
                            "name": "header",
                            "type": "text",
                            "content": "Weather Alerts Summary",
                            "style": "heading"
                        },
                        {
                            "name": "status",
                            "type": "conditional",
                            "conditions": {
                                "success": {
                                    "check": "result.success == true",
                                    "content": "Successfully retrieved {{ result.total }} weather alerts",
                                    "style": "success"
                                },
                                "error": {
                                    "check": "result.success == false",
                                    "content": "Failed to retrieve alerts: {{ result.error.message }}",
                                    "style": "error"
                                }
                            }
                        },
                        {
                            "name": "summary",
                            "type": "object",
                            "source": "result",
                            "show_keys": True,
                            "key_order": ["total", "land", "marine"],
                            "key_formatting": {
                                "total": "Total Alerts",
                                "land": "Land Alerts",
                                "marine": "Marine Alerts"
                            }
                        },
                        {
                            "name": "regions",
                            "type": "conditional",
                            "conditions": {
                                "has_regions": {
                                    "check": "result.regions",
                                    "content": "Regional breakdown available",
                                    "style": "info"
                                }
                            }
                        }
                    ],
                    "filters": ["json_pretty"],
                    "conditionals": {}
                }
                
                # Create the template
                template = await template_service.create_template(
                    db=db,
                    key="weather_alerts_formatted",
                    category="response_gen",
                    body="Default template body - will use response format instead",
                    tenant_id=uuid.UUID("ce1e63e1-a9ec-4865-bb14-3fb8383ba0e8"),  # Phaseloch tenant
                    actions={
                        "api_action": {
                            "method": "GET",
                            "endpoint": "/alerts/active"
                        }
                    },
                    parameters={
                        "type": "object",
                        "properties": {}
                    },
                    response_format=response_format
                )
                
                logger.info(f"Created template with response format: {template.key}")
                
                # Commit the transaction to ensure template is saved
                await db.commit()
            
            # Test rendering with sample context
            test_context = {
                "result": {
                    "success": True,
                    "total": 260,
                    "land": 161,
                    "marine": 99,
                    "regions": {
                        "AL": 48,
                        "AT": 7,
                        "GL": 21,
                        "PA": 22,
                        "PI": 1
                    }
                },
                "results": {
                    "default": {
                        "success": True,
                        "status_code": 200,
                        "data": {
                            "total": 260,
                            "land": 161,
                            "marine": 99
                        }
                    }
                }
            }
            
            # Render the template
            rendered = await template_service.render_template(
                db=db,
                key="weather_alerts_formatted",
                category="response_gen",
                context=test_context,
                tenant_id=uuid.UUID("ce1e63e1-a9ec-4865-bb14-3fb8383ba0e8")
            )
            
            logger.info("Rendered template with response format:")
            print("\n" + rendered + "\n")
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"Error testing response format: {str(e)}", exc_info=True)
            raise


if __name__ == "__main__":
    asyncio.run(test_response_format())