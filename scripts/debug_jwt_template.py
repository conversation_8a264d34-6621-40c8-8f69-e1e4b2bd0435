#!/usr/bin/env python
"""
Script to debug JWT token templates by connecting to the Clerk API
and testing JWT template generation.
"""

import argparse
import base64
import json
import os
import sys
import urllib.error
import urllib.request
from typing import Any, Dict, Optional


def decode_jwt(token: str) -> Dict[str, Any]:
    """
    Decode a JWT token without validation.
    Returns the payload part.
    """
    parts = token.split(".")
    if len(parts) != 3:
        raise ValueError(f"Invalid JWT format: {token[:10]}...")
    
    # Decode the payload (middle part)
    payload_b64 = parts[1]
    # Add padding if needed
    payload_b64 += "=" * ((4 - len(payload_b64) % 4) % 4)
    # Replace URL safe chars
    payload_b64 = payload_b64.replace("-", "+").replace("_", "/")
    
    try:
        payload_bytes = base64.b64decode(payload_b64)
        payload = json.loads(payload_bytes)
        return payload
    except Exception as e:
        raise ValueError(f"Failed to decode JWT payload: {e}")

def get_clerk_jwt(api_key: str, user_id: str, template_name: Optional[str] = None) -> str:
    """
    Get a JWT token for a user from the Clerk API.
    """
    url = f"https://api.clerk.com/v1/users/{user_id}/jwt"
    if template_name:
        url += f"?template={template_name}"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    req = urllib.request.Request(url, headers=headers)
    try:
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode())
            return result.get("jwt", "")
    except urllib.error.HTTPError as e:
        print(f"HTTP Error: {e.code} {e.reason}")
        print(e.read().decode())
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

def get_clerk_templates(api_key: str) -> Dict[str, Any]:
    """
    Get all JWT templates from the Clerk API.
    """
    url = "https://api.clerk.com/v1/jwt_templates"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    req = urllib.request.Request(url, headers=headers)
    try:
        with urllib.request.urlopen(req) as response:
            return json.loads(response.read().decode())
    except urllib.error.HTTPError as e:
        print(f"HTTP Error: {e.code} {e.reason}")
        print(e.read().decode())
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

def create_or_update_template(api_key: str, name: str, claims: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create or update a JWT template in Clerk.
    """
    # First check if the template exists
    templates = get_clerk_templates(api_key)
    template_id = None
    
    for template in templates.get("data", []):
        if template.get("name") == name:
            template_id = template.get("id")
            break
    
    # Prepare the request data
    data = {
        "name": name,
        "claims": json.dumps(claims)
    }
    
    if template_id:
        # Update existing template
        url = f"https://api.clerk.com/v1/jwt_templates/{template_id}"
        method = "PATCH"
    else:
        # Create new template
        url = "https://api.clerk.com/v1/jwt_templates"
        method = "POST"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    req = urllib.request.Request(
        url, 
        data=json.dumps(data).encode(),
        headers=headers,
        method=method
    )
    
    try:
        with urllib.request.urlopen(req) as response:
            return json.loads(response.read().decode())
    except urllib.error.HTTPError as e:
        print(f"HTTP Error: {e.code} {e.reason}")
        print(e.read().decode())
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="Debug Clerk JWT templates")
    parser.add_argument("--user-id", help="Clerk User ID")
    parser.add_argument("--template", help="JWT Template name (default: none)")
    parser.add_argument("--api-key", help="Clerk API Key (or use CLERK_API_KEY env var)")
    parser.add_argument("--create-template", action="store_true", help="Create/update the coherence_session template")
    parser.add_argument("--list-templates", action="store_true", help="List all JWT templates")
    
    args = parser.parse_args()
    
    # Get API key from args or environment
    api_key = args.api_key or os.environ.get("CLERK_API_KEY") or os.environ.get("CLERK_SECRET_KEY")
    if not api_key:
        print("Error: Clerk API key is required. Provide it with --api-key or set CLERK_API_KEY environment variable.")
        sys.exit(1)
    
    # List templates if requested
    if args.list_templates:
        templates = get_clerk_templates(api_key)
        print(json.dumps(templates, indent=2))
        return
    
    # Create/update template if requested
    if args.create_template:
        coherence_claims = {
            "user_id": "{{user.id}}",
            "is_system_admin": True,
            "org_id": "{{org.id}}",
            "org_name": "{{org.name}}",
            "org_role": "{{org.role}}",
            "org_slug": "{{org.slug}}",
            "__session": {
                "org_id": "{{org.id}}",
                "org_name": "{{org.name}}",
                "org_role": "{{org.role}}",
                "org_slug": "{{org.slug}}",
                "org_metadata": {}
            }
        }
        
        result = create_or_update_template(api_key, "coherence_session", coherence_claims)
        print("Template updated:")
        print(json.dumps(result, indent=2))
        return
    
    # Get user ID from args or environment
    user_id = args.user_id or os.environ.get("CLERK_USER_ID") or os.environ.get("SYSTEM_ADMIN_CLERK_USER_ID")
    if not user_id:
        print("Error: User ID is required for JWT generation. Provide it with --user-id or set CLERK_USER_ID environment variable.")
        sys.exit(1)
    
    # Get JWT
    jwt = get_clerk_jwt(api_key, user_id, args.template)
    
    # Decode and display JWT
    print("JWT Token:")
    print(jwt)
    print("\nDecoded Payload:")
    try:
        payload = decode_jwt(jwt)
        print(json.dumps(payload, indent=2))
    except Exception as e:
        print(f"Error decoding JWT: {e}")
    
if __name__ == "__main__":
    main()