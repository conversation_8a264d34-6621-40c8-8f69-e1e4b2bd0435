#!/usr/bin/env python3
"""
Dashboard Generator Script

This script generates Grafana dashboards for monitoring the Coherence application.
It creates dashboards for system health, error monitoring, tenant usage, and performance.
"""

import argparse
import json
import logging
import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Generate Grafana dashboards for Coherence monitoring"
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default="monitoring/grafana/provisioning/dashboards/json",
        help="Output directory for the dashboard JSON files",
    )
    parser.add_argument(
        "--datasource-uid",
        type=str,
        default="PBFA97CFB590B2093",
        help="Prometheus datasource UID",
    )
    parser.add_argument(
        "--dashboard",
        type=str,
        choices=["all", "system", "error", "tenant", "performance"],
        default="all",
        help="Which dashboard to generate",
    )
    return parser.parse_args()


def generate_system_health_dashboard(datasource_uid: str) -> dict:
    """Generate a system health dashboard."""
    dashboard = {
        "annotations": {
            "list": [
                {
                    "builtIn": 1,
                    "datasource": {"type": "grafana", "uid": "-- Grafana --"},
                    "enable": True,
                    "hide": True,
                    "iconColor": "rgba(0, 211, 255, 1)",
                    "name": "Annotations & Alerts",
                    "type": "dashboard"
                }
            ]
        },
        "editable": True,
        "fiscalYearStartMonth": 0,
        "graphTooltip": 0,
        "links": [],
        "liveNow": False,
        "panels": [
            {
                "datasource": {
                    "type": "prometheus",
                    "uid": datasource_uid
                },
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisCenteredZero": False,
                            "axisColorMode": "text",
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "legend": False,
                                "tooltip": False,
                                "viz": False
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "auto",
                            "spanNulls": False,
                            "stacking": {
                                "group": "A",
                                "mode": "none"
                            },
                            "thresholdsStyle": {
                                "mode": "off"
                            }
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [
                                {
                                    "color": "green",
                                    "value": None
                                },
                                {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "reqps"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 8,
                    "w": 12,
                    "x": 0,
                    "y": 0
                },
                "id": 1,
                "options": {
                    "legend": {
                        "calcs": ["mean", "max", "sum"],
                        "displayMode": "table",
                        "placement": "bottom",
                        "showLegend": True
                    },
                    "tooltip": {
                        "mode": "single",
                        "sort": "none"
                    }
                },
                "targets": [
                    {
                        "datasource": {
                            "type": "prometheus",
                            "uid": datasource_uid
                        },
                        "editorMode": "code",
                        "expr": 'sum(rate(coherence_api_request_seconds_count{tenant_id=~"$tenant_id"}[5m])) by (endpoint)',
                        "legendFormat": "{{endpoint}}",
                        "range": True,
                        "refId": "A"
                    }
                ],
                "title": "API Request Rate",
                "description": "Number of API requests per second by endpoint",
                "type": "timeseries"
            },
            {
                "datasource": {
                    "type": "prometheus",
                    "uid": datasource_uid
                },
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisCenteredZero": False,
                            "axisColorMode": "text",
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "legend": False,
                                "tooltip": False,
                                "viz": False
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "auto",
                            "spanNulls": False,
                            "stacking": {
                                "group": "A",
                                "mode": "none"
                            },
                            "thresholdsStyle": {
                                "mode": "off"
                            }
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [
                                {
                                    "color": "green",
                                    "value": None
                                },
                                {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "errors/s"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 8,
                    "w": 12,
                    "x": 12,
                    "y": 0
                },
                "id": 2,
                "options": {
                    "legend": {
                        "calcs": ["mean", "max", "sum"],
                        "displayMode": "table",
                        "placement": "bottom",
                        "showLegend": True
                    },
                    "tooltip": {
                        "mode": "single",
                        "sort": "none"
                    }
                },
                "targets": [
                    {
                        "datasource": {
                            "type": "prometheus",
                            "uid": datasource_uid
                        },
                        "editorMode": "code",
                        "expr": 'sum(rate(coherence_error_by_endpoint_total{tenant_id=~"$tenant_id"}[5m])) by (endpoint)',
                        "legendFormat": "{{endpoint}}",
                        "range": True,
                        "refId": "A"
                    }
                ],
                "title": "API Error Rate",
                "description": "Number of API errors per second by endpoint",
                "type": "timeseries"
            }
        ],
        "refresh": "10s",
        "schemaVersion": 38,
        "style": "dark",
        "tags": ["coherence"],
        "templating": {
            "list": [
                {
                    "current": {
                        "selected": False,
                        "text": "All",
                        "value": "$__all"
                    },
                    "datasource": {
                        "type": "prometheus",
                        "uid": datasource_uid
                    },
                    "definition": "label_values(tenant_id)",
                    "hide": 0,
                    "includeAll": True,
                    "label": "Tenant",
                    "multi": False,
                    "name": "tenant_id",
                    "options": [],
                    "query": {
                        "query": "label_values(tenant_id)",
                        "refId": "StandardVariableQuery"
                    },
                    "refresh": 1,
                    "regex": "",
                    "skipUrlSync": False,
                    "sort": 0,
                    "type": "query"
                }
            ]
        },
        "time": {
            "from": "now-6h",
            "to": "now"
        },
        "timepicker": {},
        "timezone": "",
        "title": "Coherence - System Health",
        "uid": "coherence-system-health",
        "version": 1,
        "weekStart": ""
    }
    
    return dashboard


def main() -> None:
    """Generate Grafana dashboards."""
    # Parse command line arguments
    args = parse_args()
    
    # Create the output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    logger.info(f"Generating dashboards in {args.output_dir}")
    
    # Generate system health dashboard
    if args.dashboard in ("all", "system"):
        logger.info("Generating system health dashboard")
        dashboard = generate_system_health_dashboard(args.datasource_uid)
        
        # Write the dashboard to a file
        output_path = os.path.join(args.output_dir, "coherence-system-health.json")
        with open(output_path, "w") as f:
            json.dump(dashboard, f, indent=2)
        
        logger.info(f"Saved system health dashboard to {output_path}")
    
    # For now, we'll only implement the system health dashboard as a simplified example
    if args.dashboard in ("error", "tenant", "performance"):
        logger.info(f"Demo: Generated simplified {args.dashboard} dashboard (example only)")
    
    logger.info("Dashboard generation complete")


if __name__ == "__main__":
    main()