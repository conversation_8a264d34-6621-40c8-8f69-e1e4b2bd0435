#!/usr/bin/env python3
"""Complete cleanup of templates from PostgreSQL and Qdrant for a single user/org platform."""

import asyncio
from pathlib import Path
import sys
from uuid import UUID

# Add src to sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.coherence.db.session import async_session
from src.coherence.models.template import Template
from src.coherence.core.config import settings
from qdrant_client import QdrantClient as QdrantClientBase
from sqlalchemy import select, delete, func
from sqlalchemy.ext.asyncio import AsyncSession


async def cleanup_postgres(db: AsyncSession, tenant_id: str):
    """Clean up PostgreSQL templates for the tenant."""
    print("=== Cleaning PostgreSQL Templates ===\n")
    
    tenant_uuid = UUID(tenant_id)
    
    # Find templates for this tenant
    templates = await db.execute(
        select(Template).where(Template.tenant_id == tenant_uuid)
    )
    templates_to_delete = templates.scalars().all()
    
    print(f"Found {len(templates_to_delete)} templates to delete:")
    for template in templates_to_delete:
        print(f"  - {template.key} ({template.category})")
    
    if templates_to_delete:
        # Delete them
        await db.execute(
            delete(Template).where(Template.tenant_id == tenant_uuid)
        )
        await db.commit()
        print(f"\n✓ Deleted {len(templates_to_delete)} templates from PostgreSQL")
    else:
        print("\n✓ No templates to delete from PostgreSQL")


def cleanup_qdrant(tenant_id: str, keep_org_id: str):
    """Clean up Qdrant collections."""
    print("\n=== Cleaning Qdrant Collections ===\n")
    
    # Create direct Qdrant client
    client = QdrantClientBase(
        host=settings.QDRANT_HOST,
        port=settings.QDRANT_PORT,
        api_key=settings.QDRANT_API_KEY if hasattr(settings, 'QDRANT_API_KEY') else None,
        check_compatibility=False  # Avoid version warning
    )
    
    try:
        # Get all collections
        collections_response = client.get_collections()
        collections = collections_response.collections
        
        print(f"Total collections: {len(collections)}")
        
        # Collections to keep (for your tenant)
        keep_collections = [
            f"intent_idx_{tenant_id}_user",
            f"template_idx_{tenant_id}",
            "template_idx_global"  # Keep global templates
        ]
        
        # Collections to delete
        collections_to_delete = []
        for col in collections:
            if col.name not in keep_collections:
                collections_to_delete.append(col.name)
        
        print(f"\nCollections to keep ({len(keep_collections)}):")
        for col in keep_collections:
            if col in [c.name for c in collections]:
                print(f"  ✓ {col}")
            else:
                print(f"  - {col} (doesn't exist)")
        
        print(f"\nCollections to delete ({len(collections_to_delete)}):")
        for col in collections_to_delete:
            print(f"  - {col}")
        
        # Delete unwanted collections
        for col_name in collections_to_delete:
            try:
                client.delete_collection(col_name)
                print(f"  ✓ Deleted {col_name}")
            except Exception as e:
                print(f"  ✗ Error deleting {col_name}: {e}")
        
        # Clean up points in kept collections
        print("\n=== Cleaning Points in Kept Collections ===")
        
        # Clean template_idx_global to only keep core templates
        if "template_idx_global" in [c.name for c in collections]:
            print("\nCleaning template_idx_global...")
            # These are the core templates we want to keep
            core_templates = [
                "PARAM_COMPLETE_V1",
                "WEATHER_QUERY", 
                "RETRIEVAL_V1",
                "ERROR_HANDLER_V1", 
                "RESPONSE_GEN_V1"
            ]
            
            # Get all points
            points = client.scroll(
                collection_name="template_idx_global",
                limit=1000,
                with_payload=True,
                with_vectors=False
            )
            
            points_to_delete = []
            for point in points[0]:
                if point.payload and point.payload.get('template_key') not in core_templates:
                    points_to_delete.append(point.id)
            
            if points_to_delete:
                client.delete(
                    collection_name="template_idx_global",
                    points_selector=points_to_delete
                )
                print(f"  ✓ Deleted {len(points_to_delete)} non-core points")
            else:
                print("  ✓ No non-core points to delete")
        
        # Clean tenant-specific collections
        tenant_collection = f"template_idx_{tenant_id}"
        if tenant_collection in [c.name for c in collections]:
            print(f"\nCleaning {tenant_collection}...")
            # Since we're deleting all templates from PostgreSQL, 
            # we should clear this collection too
            try:
                client.delete_collection(tenant_collection)
                print(f"  ✓ Deleted entire collection (will be recreated when needed)")
            except Exception as e:
                print(f"  ✗ Error: {e}")
        
        print("\n✓ Qdrant cleanup complete")
        
    except Exception as e:
        print(f"Error accessing Qdrant: {e}")


async def verify_cleanup(db: AsyncSession, tenant_id: str):
    """Verify the cleanup was successful."""
    print("\n=== Verifying Cleanup ===\n")
    
    tenant_uuid = UUID(tenant_id)
    
    # Check PostgreSQL
    template_count = await db.scalar(
        select(func.count(Template.id)).where(Template.tenant_id == tenant_uuid)
    )
    print(f"PostgreSQL templates for tenant: {template_count}")
    
    # Check Qdrant
    client = QdrantClientBase(
        host=settings.QDRANT_HOST,
        port=settings.QDRANT_PORT,
        check_compatibility=False
    )
    
    collections = client.get_collections().collections
    print(f"\nQdrant collections remaining: {len(collections)}")
    for col in collections:
        print(f"  - {col.name}")
    
    print("\n✓ Verification complete")


async def main():
    """Main cleanup function."""
    # Your tenant/org info
    tenant_id = "ce1e63e1-a9ec-4865-bb14-3fb8383ba0e8"  # Phaseloch
    org_id = "org_2wx2bWKn0Off8bfc2gCNvlMv8IW"
    
    print(f"Cleaning up for:")
    print(f"  Tenant: {tenant_id}")
    print(f"  Org: {org_id}")
    print("\n" + "="*50 + "\n")
    
    # Clean PostgreSQL
    async with async_session() as db:
        await cleanup_postgres(db, tenant_id)
    
    # Clean Qdrant
    cleanup_qdrant(tenant_id, org_id)
    
    # Verify
    async with async_session() as db:
        from sqlalchemy import func
        await verify_cleanup(db, tenant_id)
    
    print("\n🎉 Cleanup complete!")


if __name__ == "__main__":
    asyncio.run(main())