#!/usr/bin/env python
"""
Script to test template vectorization and search.

This script tests the template vectorization functionality by:
1. Creating a test template
2. Vectorizing it
3. Searching for it with various queries
4. Deleting it
"""

import asyncio
import logging
import os
import sys
import uuid

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from sqlalchemy.ext.asyncio import AsyncSession

from src.coherence.db.session import SessionLocal
from src.coherence.models.template import Template
from src.coherence.services.vector_indexer import VectorIndexer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


async def create_test_template(db: AsyncSession) -> Template:
    """
    Create a test template for vectorization testing.

    Args:
        db: Database session

    Returns:
        The created template
    """
    # Generate a unique ID for the test template
    test_id = str(uuid.uuid4())
    
    # Create a test template
    template = Template(
        id=uuid.uuid4(),
        key=f"test_template_{test_id}",
        category="test",
        body="""
        This is a test template for vectorization testing.
        It contains information about weather forecasting and API integration.
        The template will be used to test the search functionality.
        It should match queries about weather, forecasts, and predictions.
        Additionally, it has information about API usage and integration.
        """,
        description="A test template for weather forecast APIs",
        scope="global",
        language="en",
        version=1,
        created_at=None,  # Let the database set this
        updated_at=None,  # Let the database set this
    )
    
    # Add to database
    db.add(template)
    await db.commit()
    await db.refresh(template)
    
    logger.info(f"Created test template: {template.key} ({template.id})")
    return template


async def delete_test_template(db: AsyncSession, template_id: uuid.UUID) -> None:
    """
    Delete a test template.

    Args:
        db: Database session
        template_id: ID of the template to delete
    """
    template = await db.get(Template, template_id)
    if template:
        await db.delete(template)
        await db.commit()
        logger.info(f"Deleted test template: {template.key} ({template.id})")
    else:
        logger.warning(f"Test template {template_id} not found")


async def test_template_vectorization() -> None:
    """Test template vectorization and search."""
    
    # Create database session
    async with SessionLocal() as db:
        try:
            # Create a test template
            template = await create_test_template(db)
            
            # Create vector indexer
            vector_indexer = VectorIndexer()
            
            # Determine index name based on scope
            index_name = "template_idx_test"
            
            # Index the template
            logger.info(f"Indexing template {template.key} in {index_name}")
            success = await vector_indexer.upsert_template(
                db=db,
                template_id=template.id,
                template_key=template.key,
                template_category=template.category,
                template_body=template.body,
                template_description=template.description or "",
                index_name=index_name,
            )
            
            if not success:
                logger.error("Failed to index template")
                return
                
            logger.info("Template indexed successfully")
            
            # Define test queries
            test_queries = [
                "weather forecast",
                "API integration",
                "How to predict weather",
                "Using the weather forecast API",
                "template for API usage",
                "completely unrelated query about football",
            ]
            
            # Test search
            for query in test_queries:
                logger.info(f"Searching for: {query}")
                results = await vector_indexer.search_templates(
                    query=query,
                    limit=5,
                    score_threshold=0.7,
                )
                
                if results:
                    for result in results:
                        logger.info(
                            f"Match: {result.get('template_key')} "
                            f"(Score: {result.get('score', 0):.4f})"
                        )
                else:
                    logger.info("No matches found")
                    
                print()  # Add blank line between queries
                
            # Delete the template vector
            logger.info(f"Deleting template vector from {index_name}")
            success = await vector_indexer.delete_template(
                template_id=template.id,
                index_name=index_name,
            )
            
            if not success:
                logger.error("Failed to delete template vector")
                
            # Delete the test template from the database
            await delete_test_template(db, template.id)
            
            logger.info("Test completed successfully")
            
        except Exception as e:
            logger.error(f"Error during test: {str(e)}", exc_info=True)


async def main() -> None:
    """Main entry point for the script."""
    try:
        await test_template_vectorization()
    except Exception as e:
        logger.error(f"Test failed: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())