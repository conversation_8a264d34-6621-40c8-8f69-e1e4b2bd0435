#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to rebuild the intent index in Qdrant using synchronous operations.

This script extracts intents from the database and rebuilds the vector index
for faster intent matching (Tier 1).
"""

import logging

from src.coherence.db.session import SessionLocal
from src.coherence.models.tenant import Tenant

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Example weather query embeddings (for simulation)
EXAMPLE_EMBEDDINGS = {
    "What's the weather like in Chicago?": [0.1] * 384,
    "How's the weather in San Francisco?": [0.2] * 384,
    "Tell me the weather forecast for New York": [0.3] * 384,
    "Weather in Miami": [0.4] * 384,
    "Is it going to rain in Seattle?": [0.5] * 384,
    "What's the temperature in Boston?": [0.6] * 384,
    "Will it be sunny in Denver tomorrow?": [0.7] * 384,
}


def main():
    """Main entry point."""
    logger.info("Starting intent index rebuild...")
    
    # Create DB session
    db = SessionLocal()
    
    try:
        # Get all tenant IDs
        tenants = db.query(Tenant.id).all()
        tenant_ids = [row[0] for row in tenants]
        
        logger.info(f"Found {len(tenant_ids)} tenants")
        
        # Use Docker to interact with Qdrant directly
        for tenant_id in tenant_ids:
            collection_name = f"intent_idx_{tenant_id}_user"
            
            # Create collection
            cmd = f"""
            docker-compose exec coherence-api python -c "
from src.coherence.core.qdrant_client import QdrantClient
import asyncio

async def create_collection():
    client = QdrantClient()
    
    # Check if collection exists
    try:
        if await client.has_collection('{collection_name}'):
            await client.delete_collection('{collection_name}')
            print(f'Deleted existing collection: {collection_name}')
    except Exception as exc:
        print(f'Error checking collection: {{str(exc)}}')
    
    # Create collection
    try:
        await client.create_collection(
            collection_name='{collection_name}',
            vector_size=384
        )
        print(f'Created collection: {collection_name}')
        
        # Add example points
        examples = list(EXAMPLE_EMBEDDINGS.keys())
        for text in examples:
            # For testing, we'll use predefined embeddings
            embedding = [0.1] * 384
            # Add the point
            await client.add_points(
                collection_name='{collection_name}',
                points=[
                    {{
                        'id': str(__import__('uuid').uuid4()),
                        'vector': embedding,
                        'payload': {{
                            'intent': 'weather_query',
                            'text': text,
                            'parameters': {{
                                'location': text.split('in ')[1].rstrip('?') if 'in ' in text else ''
                            }}
                        }}
                    }}
                ]
            )
            print(f'Added example to index: {{text}}')
    except Exception as exc:
        print(f'Error creating collection or adding points: {{str(exc)}}')

asyncio.run(create_collection())
"
            """
            
            # Execute the command
            import subprocess
            subprocess.run(cmd, shell=True)
            
            logger.info(f"Processed tenant: {tenant_id}")
        
        logger.info("Intent index rebuild completed!")
    
    finally:
        db.close()


if __name__ == "__main__":
    main()