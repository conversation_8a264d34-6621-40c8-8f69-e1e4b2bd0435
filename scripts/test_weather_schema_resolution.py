"""Test Weather API schema resolution for better CRFS structure."""

import asyncio
import json
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import select
from src.coherence.db.session import async_session
from src.coherence.models.integration import APIIntegration, APIEndpoint
from src.coherence.openapi_adapter.action_generator import ActionGenerator

async def test_weather_schema_resolution():
    """Test schema resolution to check structure generation."""
    async with async_session() as db:
        # Find Weather integration
        query = select(APIIntegration).where(APIIntegration.name == "Weather")
        result = await db.execute(query)
        integration = result.scalar_one_or_none()
        
        if not integration:
            print("Weather integration not found")
            return
            
        # Create action generator
        generator = ActionGenerator(db)
        
        # Test resolving a schema reference
        schema_ref = "#/components/schemas/AlertCollectionJsonLd"
        try:
            resolved_schema = generator.resolve_reference(schema_ref, integration.openapi_spec)
            print(f"Resolved schema for {schema_ref}:")
            print(json.dumps(resolved_schema, indent=2)[:500] + "...")
            
            # Check type
            schema_type = resolved_schema.get("type")
            properties = resolved_schema.get("properties", {})
            print(f"\nSchema type: {schema_type}")
            print(f"Properties: {list(properties.keys())[:10]}")
            
        except Exception as e:
            print(f"Error resolving schema: {e}")
            
        # Test with alerts/active/count which has inline schema
        query = select(APIEndpoint).where(
            APIEndpoint.integration_id == integration.id,
            APIEndpoint.path == "/alerts/active/count"
        )
        result = await db.execute(query)
        count_endpoint = result.scalar_one_or_none()
        
        if count_endpoint:
            print("\n\nTesting /alerts/active/count endpoint:")
            endpoint_spec = generator._get_endpoint_spec(
                integration.openapi_spec, 
                count_endpoint.path, 
                count_endpoint.method.lower()
            )
            
            response_format = generator._generate_response_format(
                endpoint_spec, 
                integration.name,
                integration.openapi_spec
            )
            
            print("Generated response format:")
            print(json.dumps(response_format, indent=2)[:1000] + "...")
            
            format_type = response_format.get("format", {}).get("type")
            print(f"\nFormat type: {format_type}")
            
            if format_type == "structured":
                sections = response_format.get("format", {}).get("structure", {}).get("sections", [])
                print(f"Number of sections: {len(sections)}")
                for i, section in enumerate(sections[:3]):
                    print(f"Section {i}: {section.get('type')}")

if __name__ == "__main__":
    asyncio.run(test_weather_schema_resolution())