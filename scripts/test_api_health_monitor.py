#!/usr/bin/env python3
"""
Test script for the API Health Monitor.

This script tests the API Health Monitor by:
1. Registering health checks for test APIs
2. Simulating API status changes
3. Verifying the health monitor responds correctly to status changes
4. Testing integration with circuit breaker
"""

import asyncio
import logging
from typing import Dict, List

import httpx

from src.coherence.core.redis_client import get_redis_client
from src.coherence.monitoring.metrics_collector import TenantMetricsCollector
from src.coherence.openapi_adapter.health_monitor import (
    ApiHealthMonitor,
    ApiStatus,
    HealthCheckConfig,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Test API configurations
TEST_APIS = [
    {
        "api_key": "test_api_1",
        "endpoint": "https://httpbin.org/status/200",
        "expected_status": 200,
    },
    {
        "api_key": "test_api_2",
        "endpoint": "https://httpbin.org/status/500",
        "expected_status": 200,  # This will fail
    },
    {
        "api_key": "test_api_3",
        "endpoint": "https://httpbin.org/delay/5",
        "expected_status": 200,
        "timeout_ms": 2000,  # This will timeout
    },
]


class ApiHealthMonitorTester:
    """Test harness for the API Health Monitor."""

    def __init__(self):
        """Initialize the tester."""
        self.health_monitor = None
        self.redis_client = None
        self.status_changes: List[Dict] = []

    async def setup(self):
        """Set up the test environment."""
        # Get Redis client
        self.redis_client = await get_redis_client()

        # Create a mock HTTP client with controlled responses
        http_client = httpx.AsyncClient(timeout=10.0)

        # Create metrics collector
        metrics_collector = TenantMetricsCollector()

        # Create health monitor
        self.health_monitor = ApiHealthMonitor(
            redis_client=self.redis_client,
            metrics_collector=metrics_collector,
            http_client=http_client,
            namespace="test_health_monitor",
        )

        # Register status change callback
        await self.health_monitor.register_status_change_callback(self.status_change_callback)

        logger.info("Test environment set up")

    def status_change_callback(self, api_key: str, old_status: ApiStatus, new_status: ApiStatus):
        """Callback for API status changes."""
        logger.info(f"API status change: {api_key} - {old_status} -> {new_status}")
        self.status_changes.append({
            "api_key": api_key,
            "old_status": old_status,
            "new_status": new_status,
        })

    async def register_test_apis(self):
        """Register health checks for test APIs."""
        for api_config in TEST_APIS:
            config = HealthCheckConfig(
                api_key=api_config["api_key"],
                endpoint=api_config["endpoint"],
                method="GET",
                expected_status=api_config["expected_status"],
                timeout_ms=api_config.get("timeout_ms", 5000),
                check_interval_seconds=10,
                consecutive_failures_threshold=2,
                consecutive_successes_for_recovery=2,
            )

            await self.health_monitor.register_health_check(config)
            logger.info(f"Registered health check for {api_config['api_key']}")

    async def test_health_checks(self):
        """Test health checks for all registered APIs."""
        results = {}

        # Test each API individually
        for api_config in TEST_APIS:
            api_key = api_config["api_key"]
            try:
                result = await self.health_monitor.check_health(api_key)
                results[api_key] = result
                logger.info(
                    f"Health check for {api_key}: {result.status} "
                    f"(status_code={result.status_code}, response_time={result.response_time_ms:.2f}ms)"
                )
            except Exception as e:
                logger.error(f"Error checking health for {api_key}: {e}")
                results[api_key] = None

        return results

    async def test_status_retrieval(self):
        """Test retrieving status for all APIs."""
        statuses = {}

        # Get status for each API
        for api_config in TEST_APIS:
            api_key = api_config["api_key"]
            try:
                status = await self.health_monitor.get_status(api_key)
                statuses[api_key] = status
                logger.info(f"Status for {api_key}: {status}")
            except Exception as e:
                logger.error(f"Error getting status for {api_key}: {e}")
                statuses[api_key] = None

        return statuses

    async def test_status_history(self):
        """Test retrieving status history for an API."""
        # Pick the first API for history test
        api_key = TEST_APIS[0]["api_key"]

        try:
            # Get history
            history = await self.health_monitor.get_status_history(api_key, days=1, limit=10)
            logger.info(f"Status history for {api_key}: {len(history)} entries")

            # Print history entries
            for entry in history:
                logger.info(
                    f"  {entry.timestamp.isoformat()}: {entry.status} "
                    f"(status_code={entry.status_code}, response_time={entry.response_time_ms:.2f}ms)"
                )

            return history
        except Exception as e:
            logger.error(f"Error getting status history for {api_key}: {e}")
            return []

    async def test_deregistration(self):
        """Test deregistering a health check."""
        # Pick the first API for deregistration test
        api_key = TEST_APIS[0]["api_key"]

        try:
            # Deregister
            result = await self.health_monitor.deregister_health_check(api_key)
            logger.info(f"Deregistered health check for {api_key}: {result}")

            # Verify it's deregistered by trying to get status
            status = await self.health_monitor.get_status(api_key)
            logger.info(f"Status after deregistration for {api_key}: {status}")

            return result
        except Exception as e:
            logger.error(f"Error deregistering health check for {api_key}: {e}")
            return False

    async def test_monitoring_loop(self):
        """Test the background monitoring loop."""
        # Start monitoring
        await self.health_monitor.start_monitoring()
        logger.info("Started monitoring")

        # Wait for a while to allow monitoring to run
        logger.info("Waiting for monitoring to run (30 seconds)...")
        await asyncio.sleep(30)

        # Stop monitoring
        await self.health_monitor.stop_monitoring()
        logger.info("Stopped monitoring")

        # Get final statuses
        statuses = await self.test_status_retrieval()
        return statuses

    async def run_tests(self):
        """Run all tests."""
        logger.info("Setting up test environment...")
        await self.setup()

        logger.info("\n=== Test 1: Register Test APIs ===")
        await self.register_test_apis()

        logger.info("\n=== Test 2: Individual Health Checks ===")
        await self.test_health_checks()

        logger.info("\n=== Test 3: Status Retrieval ===")
        await self.test_status_retrieval()

        logger.info("\n=== Test 4: Status History ===")
        await self.test_status_history()

        logger.info("\n=== Test 5: Monitoring Loop ===")
        await self.test_monitoring_loop()

        logger.info("\n=== Test 6: Deregistration ===")
        await self.test_deregistration()

        logger.info("\n=== Test Results ===")
        logger.info(f"Status changes detected: {len(self.status_changes)}")
        for change in self.status_changes:
            logger.info(f"  {change['api_key']}: {change['old_status']} -> {change['new_status']}")

        logger.info("\nTests completed!")


async def main():
    """Main entry point."""
    tester = ApiHealthMonitorTester()
    await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())