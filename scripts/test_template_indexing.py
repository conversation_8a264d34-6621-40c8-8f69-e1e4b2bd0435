#!/usr/bin/env python
"""
Test script to verify template indexing in Qdrant.
"""

import asyncio
import logging
import os

from src.coherence.core.qdrant_client import get_qdrant_client
from src.coherence.db.session import async_session

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Force use the Docker container ports that are exposed
os.environ["COHERENCE_QDRANT_HOST"] = "localhost"
os.environ["COHERENCE_QDRANT_PORT"] = "6335"  # Mapped Docker port
os.environ["COHERENCE_POSTGRES_PORT"] = "5433"  # Mapped Docker port
os.environ["COHERENCE_REDIS_PORT"] = "6380"  # Mapped Docker port

async def test_collections_exist():
    """Test that Qdrant collections exist and contain points."""
    logger.info("Testing if Qdrant collections exist...")
    
    # Get Qdrant client
    qdrant_client = await get_qdrant_client()
    
    try:
        # List all collections
        logger.info(f"Connecting to Qdrant at {os.environ.get('COHERENCE_QDRANT_HOST')}:{os.environ.get('COHERENCE_QDRANT_PORT')}")
        collections = qdrant_client.client.get_collections()
        logger.info(f"Found collections: {collections}")
        
        if not collections.collections:
            logger.warning("No collections found in Qdrant!")
            logger.info("This is expected if you haven't created any collections yet.")
            return True
        
        # Check each collection for points
        for collection in collections.collections:
            collection_name = collection.name
            
            # Check if collection has points
            collection_info = qdrant_client.client.get_collection(collection_name)
            logger.info(f"Collection {collection_name} info: {collection_info}")
            
            # Get vector size from collection info
            vector_size = collection_info.config.params.vectors.size
            logger.info(f"Collection {collection_name} has vector size: {vector_size}")
            
            # Make a test search to verify it works
            # Create a test vector matching the collection's dimension
            test_vector = [0.1] * vector_size
            search_results = await qdrant_client.search(
                collection_name=collection_name,
                query_vector=test_vector,
                limit=5
            )
            
            if search_results:
                logger.info(f"Collection {collection_name} contains {len(search_results)} searchable points")
                for i, result in enumerate(search_results[:3]):  # Show first 3 results
                    logger.info(f"  Result {i+1}: score={result['score']}")
                    for key, value in result.items():
                        if key != 'score' and key != 'id':
                            logger.info(f"    {key}: {value}")
            else:
                logger.info(f"Collection {collection_name} exists but returned no search results")
        
        return True
    except Exception as e:
        logger.error(f"Error testing collections: {str(e)}")
        return False

async def test_weather_query():
    """Test a weather query to see if it gets matched correctly."""
    logger.info("Testing weather query matching...")
    
    # First, check if we have any tenants in the database

    from sqlalchemy import select

    from src.coherence.models.tenant import Tenant
    
    db = async_session()
    try:
        # Try to get a tenant ID from the database
        query = select(Tenant.id).limit(1)
        result = await db.execute(query)
        tenant_id_from_db = result.scalar_one_or_none()
        
        if tenant_id_from_db:
            tenant_id = str(tenant_id_from_db)
        else:
            logger.warning("No tenant found in database. Using existing collection from docker ps output.")
            # Use IDs we saw in the previous test
            tenant_id = "6e8fad41-8656-425d-b96c-732e8e50c0f5"
        
        logger.info(f"Using tenant ID: {tenant_id}")
        
        # Get Qdrant client 
        qdrant_client = await get_qdrant_client()
        
        # Create LLM provider for generating embeddings
        from src.coherence.core.llm.factory import LLMFactory
        llm_factory = LLMFactory()
        try:
            provider = llm_factory.create_provider(
                name="openai", 
                model="text-embedding-3-small"
            )
        except Exception as e:
            logger.warning(f"Unable to create OpenAI provider: {str(e)}. Using mock embedding provider.")
            from src.coherence.core.llm.providers.mock_provider import MockLLMProvider
            provider = MockLLMProvider()
        
        # Check available collections and find one for testing
        collections = await qdrant_client.list_collections()
        logger.info(f"Available collections: {collections}")
        
        # Find any intent collection to test with
        intent_collections = [c for c in collections if c.startswith("intent_idx_")]
        
        if not intent_collections:
            logger.warning("No intent collections found. Creating a test collection.")
            collection_name = f"intent_idx_{tenant_id}_user"
            await qdrant_client.create_intent_collection(str(tenant_id), "user")
            logger.info(f"Created collection {collection_name}")
            logger.warning("No data has been indexed. Run rebuild_intent_index.py first to index data.")
            return True
        else:
            # Use the first available intent collection
            collection_name = intent_collections[0]
            logger.info(f"Using existing collection: {collection_name}")
            
            # Get the vector size from the collection
            collection_info = qdrant_client.client.get_collection(collection_name)
            vector_size = collection_info.config.params.vectors.size
            logger.info(f"Collection {collection_name} has vector size: {vector_size}")
        
        # Test queries
        test_queries = [
            "What's the weather like in Chicago?",
            "How's the weather in New York?",
            "Tell me the weather in Seattle"
        ]
        
        for query in test_queries:
            # Generate embedding for the query with the correct dimensions
            try:
                embedding = await provider.generate_embedding(query)
                logger.info(f"Generated embedding for '{query}' with dimension {len(embedding)}")
                
                # Ensure the embedding has the right dimension for the collection
                if len(embedding) != vector_size:
                    logger.warning(f"Generated embedding dimension {len(embedding)} doesn't match collection dimension {vector_size}, resizing...")
                    import hashlib
                    hash_obj = hashlib.md5(query.encode())
                    hash_int = int(hash_obj.hexdigest(), 16)
                    embedding = [((hash_int + i) % 1000) / 1000.0 for i in range(vector_size)]
                    logger.info(f"Resized embedding to dimension {len(embedding)}")
                
            except Exception as e:
                logger.warning(f"Error generating embedding: {e}, using mock embedding")
                import hashlib
                hash_obj = hashlib.md5(query.encode())
                hash_int = int(hash_obj.hexdigest(), 16)
                embedding = [((hash_int + i) % 1000) / 1000.0 for i in range(vector_size)]
                logger.info(f"Generated mock embedding with dimension {len(embedding)}")
            
            # Search for intent
            search_results = await qdrant_client.search(
                collection_name=collection_name,
                query_vector=embedding,
                limit=3
            )
            
            if search_results:
                logger.info(f"Query '{query}' matched:")
                for i, result in enumerate(search_results):
                    intent = result.get('intent', 'N/A')
                    logger.info(f"  Match {i+1}: score={result['score']}, intent={intent}")
                    
                    # Extract other metadata
                    for key, value in result.items():
                        if key not in ['score', 'id', 'intent']:
                            logger.info(f"    {key}: {value}")
            else:
                logger.info(f"Query '{query}' found no matches. This is normal if you haven't indexed any data yet.")
        
        return True
    except Exception as e:
        import traceback
        error_text = traceback.format_exc()
        logger.error(f"Error testing weather query: {str(e)}\n{error_text}")
        return False
    finally:
        await db.close()

async def main():
    """Main entry point."""
    try:
        # Test collections
        collections_exist = await test_collections_exist()
        
        if collections_exist:
            # Test weather query
            await test_weather_query()
            
        logger.info("Template indexing tests completed!")
    except Exception as e:
        logger.error(f"Error during testing: {e}")

if __name__ == "__main__":
    asyncio.run(main())