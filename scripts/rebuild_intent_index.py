#!/usr/bin/env python
"""
<PERSON><PERSON>t to rebuild the intent index in Qdrant.

This script extracts intents from the database and rebuilds the vector index
for faster intent matching (Tier 1).
"""

import asyncio
import logging
import uuid

from sqlalchemy import select

from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.qdrant_client import QdrantClient
from src.coherence.db.session import async_session
from src.coherence.models.template import Template
from src.coherence.models.tenant import Tenant

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Example weather query embeddings (for simulation)
EXAMPLE_EMBEDDINGS = {
    "What's the weather like in Chicago?": [0.1] * 384,
    "How's the weather in San Francisco?": [0.2] * 384,
    "Tell me the weather forecast for New York": [0.3] * 384,
    "Weather in Miami": [0.4] * 384,
    "Is it going to rain in Seattle?": [0.5] * 384,
    "What's the temperature in Boston?": [0.6] * 384,
    "Will it be sunny in Denver tomorrow?": [0.7] * 384,
}


async def rebuild_index_for_tenant(
    tenant_id: uuid.UUID,
    qdrant_client: QdrantClient,
    llm_factory: LLMFactory,
):
    """Rebuild the intent index for a specific tenant."""
    # Create collection for this tenant
    collection_name = f"intent_idx_{tenant_id}_user"
    
    # Check if collection exists
    try:
        if await qdrant_client.collection_exists(collection_name):
            # Delete and recreate using delete_intent_vectors (which handles deleting the entire collection)
            await qdrant_client.delete_intent_vectors(
                tenant_id=str(tenant_id),
                role="user"
            )
            logger.info(f"Deleted existing collection: {collection_name}")
    except Exception as e:
        logger.error(f"Error checking/deleting collection: {e}")
    
    # Create the collection
    try:
        await qdrant_client.create_intent_collection(
            tenant_id=str(tenant_id),
            role="user",
            vector_size=384  # Use standardized dimension
        )
        logger.info(f"Created collection: {collection_name}")
    except Exception as e:
        logger.error(f"Error creating collection: {e}")
        return
    
    # Get LLM provider for embeddings
    try:
        # Use the default provider with fail_fast=False to fallback to mock provider if needed
        llm_provider = llm_factory.get_default_provider(fail_fast=False)
        logger.info(f"Using provider: {llm_provider.__class__.__name__}")
    except Exception as e:
        logger.error(f"Error getting LLM provider: {e}")
        return
    
    # Get intent templates for this tenant
    async with async_session() as db:
        # Find all weather intent templates for this tenant
        query = select(Template).where(
            Template.tenant_id == tenant_id,
            Template.key == "WEATHER_QUERY",
            Template.category == "intent_router",
        )
        result = await db.execute(query)
        templates = result.scalars().all()
        
        if not templates:
            # Try global templates
            query = select(Template).where(
                Template.scope == "global",
                Template.key == "WEATHER_QUERY",
                Template.category == "intent_router",
            )
            result = await db.execute(query)
            templates = result.scalars().all()
        
        if not templates:
            logger.warning(f"No weather intent templates found for tenant: {tenant_id}")
            return
        
        logger.info(f"Found {len(templates)} templates for tenant: {tenant_id}")
        
        # Extract examples from templates
        examples = []
        intent_id = "weather_query"
        
        # Add predefined examples
        examples = list(EXAMPLE_EMBEDDINGS.keys())
        
        # For each example, generate an embedding and add it to the index
        for example in examples:
            try:
                # In real system, we'd generate embedding here
                # For this test, we'll use the predefined ones
                embedding = EXAMPLE_EMBEDDINGS.get(example, [0.1] * 384)
                
                # Add the point to Qdrant
                await qdrant_client.upsert_intent_vectors(
                    tenant_id=str(tenant_id),
                    role="user",
                    vectors=[
                        {
                            "id": str(uuid.uuid4()),
                            "vector": embedding,
                            "metadata": {
                                "intent": intent_id,
                                "text": example,
                                "parameters": {
                                    "location": example.split("in ")[1].rstrip("?") if "in " in example else ""
                                }
                            }
                        }
                    ]
                )
                logger.info(f"Added example to index: {example}")
            except Exception as e:
                logger.error(f"Error adding example to index: {e}")
        
        logger.info(f"Finished rebuilding index for tenant: {tenant_id}")


async def main():
    """Main entry point."""
    logger.info("Starting intent index rebuild...")
    
    # Initialize Qdrant client
    qdrant_client = QdrantClient()
    
    # Initialize LLM factory
    llm_factory = LLMFactory()
    
    # Get all tenant IDs
    async with async_session() as db:
        query = select(Tenant.id)
        result = await db.execute(query)
        tenant_ids = [row[0] for row in result.all()]
    
    # Rebuild index for each tenant
    for tenant_id in tenant_ids:
        await rebuild_index_for_tenant(tenant_id, qdrant_client, llm_factory)
    
    logger.info("Intent index rebuild completed!")


if __name__ == "__main__":
    asyncio.run(main())