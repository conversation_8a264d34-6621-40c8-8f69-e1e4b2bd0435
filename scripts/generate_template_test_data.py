#!/usr/bin/env python3
"""
Generate Test Data for Unified Templates

This script generates comprehensive test data for unified templates including:
- Mock API responses for different scenarios
- Sample parameter values based on schemas
- Error response examples
- Edge case data

Phase 7 Implementation - Test Data Generation Tool
"""

import asyncio
import json
import logging
import random
import string
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
try:
    from faker import Faker
    fake = Faker()
except ImportError:
    # If Faker is not installed, use basic fallback
    class FallbackFaker:
        def email(self): return "<EMAIL>"
        def url(self): return "https://example.com"
        def uuid4(self): return "550e8400-e29b-41d4-a716-************"
        def domain_name(self): return "example.com"
        def ipv4(self): return "*********"
        def ipv6(self): return "2001:db8::1"
        def phone_number(self): return "******-123-4567"
        def postcode(self): return "12345"
        def ssn(self): return "***********"
        def word(self): return "example"
        def sentence(self, nb_words=5): return "This is an example sentence."
        def text(self, max_nb_chars=200): return "Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        def company(self): return "Example Corp"
    fake = FallbackFaker()

from sqlalchemy import select
from sqlalchemy.orm import selectinload

from src.coherence.db.deps import get_admin_db_session
from src.coherence.models.template import Template, TemplateCategory

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Faker initialized above with fallback


class TestDataGenerator:
    """Generates comprehensive test data for unified templates."""
    
    def __init__(self):
        self.generated_count = 0
        self.error_count = 0
        self.faker = fake
    
    async def generate_for_all_templates(self):
        """Generate test data for all unified templates."""
        async with get_admin_db_session() as db:
            # Query all unified templates
            result = await db.execute(
                select(Template)
                .where(Template.category == TemplateCategory.UNIFIED)
            )
            templates = result.scalars().all()
            
            logger.info(f"Found {len(templates)} unified templates for test data generation")
            
            for template in templates:
                try:
                    test_data = self._generate_test_data_for_template(template)
                    
                    # Update template with test data
                    if hasattr(template, 'test_data'):
                        template.test_data = test_data
                        self.generated_count += 1
                        logger.info(f"Generated test data for template: {template.key}")
                        
                except Exception as e:
                    self.error_count += 1
                    logger.error(f"Error generating test data for template {template.key}: {str(e)}")
            
            # Commit all changes
            await db.commit()
            
        # Print summary
        logger.info(f"\nTest Data Generation Summary:")
        logger.info(f"  Generated: {self.generated_count}")
        logger.info(f"  Errors: {self.error_count}")
        logger.info(f"  Total: {len(templates)}")
    
    def _generate_test_data_for_template(self, template: Template) -> Dict[str, Any]:
        """Generate comprehensive test data for a single template."""
        # Extract template configuration
        action_config = template.action_config or {}
        parameters = action_config.get('parameters', {})
        method = action_config.get('action', {}).get('method', 'GET')
        path = action_config.get('action', {}).get('path', '')
        
        test_data = {
            'sample_parameters': self._generate_sample_parameters(parameters),
            'mock_responses': self._generate_mock_responses(template, method, path),
            'error_scenarios': self._generate_error_scenarios(parameters),
            'edge_cases': self._generate_edge_cases(parameters),
            'performance_data': self._generate_performance_data()
        }
        
        return test_data
    
    def _generate_sample_parameters(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate multiple sets of sample parameters."""
        if not parameters:
            return []
        
        samples = []
        
        # Generate standard case
        standard_sample = {}
        for param_name, param_config in parameters.items():
            if isinstance(param_config, dict):
                standard_sample[param_name] = self._generate_parameter_value(
                    param_config, 'standard'
                )
        samples.append({'name': 'standard', 'values': standard_sample})
        
        # Generate minimal case (only required fields)
        minimal_sample = {}
        for param_name, param_config in parameters.items():
            if isinstance(param_config, dict) and param_config.get('required'):
                minimal_sample[param_name] = self._generate_parameter_value(
                    param_config, 'minimal'
                )
        if minimal_sample:
            samples.append({'name': 'minimal', 'values': minimal_sample})
        
        # Generate maximal case (all fields with max values)
        maximal_sample = {}
        for param_name, param_config in parameters.items():
            if isinstance(param_config, dict):
                maximal_sample[param_name] = self._generate_parameter_value(
                    param_config, 'maximal'
                )
        samples.append({'name': 'maximal', 'values': maximal_sample})
        
        return samples
    
    def _generate_parameter_value(self, param_config: Dict[str, Any], scenario: str) -> Any:
        """Generate a parameter value based on configuration and scenario."""
        param_type = param_config.get('type', 'string')
        
        if param_type == 'string':
            return self._generate_string_value(param_config, scenario)
        elif param_type in ['number', 'integer']:
            return self._generate_number_value(param_config, scenario, param_type == 'integer')
        elif param_type == 'boolean':
            return self._generate_boolean_value(scenario)
        elif param_type == 'array':
            return self._generate_array_value(param_config, scenario)
        elif param_type == 'object':
            return self._generate_object_value(param_config, scenario)
        
        return None
    
    def _generate_string_value(self, param_config: Dict[str, Any], scenario: str) -> str:
        """Generate string value based on format and constraints."""
        # Check for enum first
        if 'enum' in param_config:
            enum_values = param_config['enum']
            if scenario == 'standard':
                return enum_values[0] if enum_values else ''
            elif scenario == 'maximal':
                return enum_values[-1] if enum_values else ''
            else:
                return random.choice(enum_values) if enum_values else ''
        
        # Check format
        format_type = param_config.get('format', '')
        
        if format_type == 'email':
            return self.faker.email()
        elif format_type == 'date':
            return datetime.now().strftime('%Y-%m-%d')
        elif format_type == 'date-time':
            return datetime.now().isoformat() + 'Z'
        elif format_type in ['uri', 'url']:
            return self.faker.url()
        elif format_type == 'uuid':
            return str(self.faker.uuid4())
        elif format_type == 'hostname':
            return self.faker.domain_name()
        elif format_type == 'ipv4':
            return self.faker.ipv4()
        elif format_type == 'ipv6':
            return self.faker.ipv6()
        
        # Check pattern
        pattern = param_config.get('pattern', '')
        if pattern:
            if 'phone' in pattern.lower():
                return self.faker.phone_number()
            elif 'zip' in pattern.lower() or 'postal' in pattern.lower():
                return self.faker.postcode()
            elif 'ssn' in pattern.lower():
                return self.faker.ssn()
        
        # Generate based on length constraints
        min_length = param_config.get('minLength', 1)
        max_length = param_config.get('maxLength', 100)
        
        if scenario == 'minimal':
            length = min_length
        elif scenario == 'maximal':
            length = max_length
        else:
            length = (min_length + max_length) // 2
        
        # Generate string of appropriate length
        if length <= 10:
            return self.faker.word()[:length]
        elif length <= 50:
            return self.faker.sentence(nb_words=5)[:length]
        else:
            return self.faker.text(max_nb_chars=length)
    
    def _generate_number_value(self, param_config: Dict[str, Any], scenario: str, is_integer: bool) -> float:
        """Generate number value based on constraints."""
        minimum = param_config.get('minimum', 0)
        maximum = param_config.get('maximum', 1000000)
        
        if scenario == 'minimal':
            value = minimum
        elif scenario == 'maximal':
            value = maximum
        else:
            value = (minimum + maximum) / 2
        
        return int(value) if is_integer else value
    
    def _generate_boolean_value(self, scenario: str) -> bool:
        """Generate boolean value based on scenario."""
        if scenario == 'minimal':
            return False
        elif scenario == 'maximal':
            return True
        else:
            return random.choice([True, False])
    
    def _generate_array_value(self, param_config: Dict[str, Any], scenario: str) -> List[Any]:
        """Generate array value based on constraints."""
        min_items = param_config.get('minItems', 0)
        max_items = param_config.get('maxItems', 10)
        item_config = param_config.get('items', {})
        
        if scenario == 'minimal':
            count = min_items
        elif scenario == 'maximal':
            count = max_items
        else:
            count = (min_items + max_items) // 2
        
        # Generate items
        items = []
        for _ in range(count):
            item = self._generate_parameter_value(item_config, 'standard')
            items.append(item)
        
        return items
    
    def _generate_object_value(self, param_config: Dict[str, Any], scenario: str) -> Dict[str, Any]:
        """Generate object value based on properties."""
        properties = param_config.get('properties', {})
        required = param_config.get('required', [])
        
        obj = {}
        for prop_name, prop_config in properties.items():
            if scenario == 'minimal' and prop_name not in required:
                continue
            
            if isinstance(prop_config, dict):
                obj[prop_name] = self._generate_parameter_value(prop_config, scenario)
        
        return obj
    
    def _generate_mock_responses(self, template: Template, method: str, path: str) -> Dict[str, Any]:
        """Generate mock API responses for different scenarios."""
        responses = {}
        
        # Success response
        if method == 'GET':
            if 'list' in path or path.endswith('s'):
                # List response
                responses['success'] = {
                    'status': 200,
                    'data': {
                        'items': [
                            self._generate_resource_object() for _ in range(3)
                        ],
                        'total': 3,
                        'page': 1,
                        'per_page': 10
                    }
                }
            else:
                # Single resource response
                responses['success'] = {
                    'status': 200,
                    'data': self._generate_resource_object()
                }
        
        elif method == 'POST':
            responses['success'] = {
                'status': 201,
                'data': self._generate_resource_object(created=True)
            }
        
        elif method == 'PUT' or method == 'PATCH':
            responses['success'] = {
                'status': 200,
                'data': self._generate_resource_object(updated=True)
            }
        
        elif method == 'DELETE':
            responses['success'] = {
                'status': 204,
                'data': None
            }
        
        # Empty response
        responses['empty'] = {
            'status': 200,
            'data': [] if method == 'GET' and ('list' in path or path.endswith('s')) else {}
        }
        
        # Partial response (for testing missing fields)
        responses['partial'] = {
            'status': 200,
            'data': {
                'id': self.faker.uuid4(),
                'status': 'partial'
            }
        }
        
        return responses
    
    def _generate_resource_object(self, created: bool = False, updated: bool = False) -> Dict[str, Any]:
        """Generate a generic resource object."""
        obj = {
            'id': self.faker.uuid4(),
            'name': self.faker.company(),
            'description': self.faker.text(max_nb_chars=200),
            'status': random.choice(['active', 'inactive', 'pending']),
            'created_at': (datetime.now() - timedelta(days=30)).isoformat() + 'Z',
            'metadata': {
                'version': '1.0',
                'tags': [self.faker.word() for _ in range(3)]
            }
        }
        
        if created:
            obj['created_at'] = datetime.now().isoformat() + 'Z'
            obj['created_by'] = self.faker.email()
        
        if updated:
            obj['updated_at'] = datetime.now().isoformat() + 'Z'
            obj['updated_by'] = self.faker.email()
        
        return obj
    
    def _generate_error_scenarios(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate error scenario test cases."""
        scenarios = []
        
        # Validation errors
        for param_name, param_config in parameters.items():
            if isinstance(param_config, dict):
                # Missing required parameter
                if param_config.get('required'):
                    scenarios.append({
                        'name': f'missing_{param_name}',
                        'parameters': {k: v for k, v in parameters.items() if k != param_name},
                        'expected_error': {
                            'status': 400,
                            'code': 'VALIDATION_ERROR',
                            'message': f'Missing required parameter: {param_name}'
                        }
                    })
                
                # Invalid type
                if param_config.get('type') == 'number':
                    invalid_params = parameters.copy()
                    invalid_params[param_name] = 'not_a_number'
                    scenarios.append({
                        'name': f'invalid_type_{param_name}',
                        'parameters': invalid_params,
                        'expected_error': {
                            'status': 400,
                            'code': 'TYPE_ERROR',
                            'message': f'Invalid type for parameter: {param_name}'
                        }
                    })
        
        # Authentication error
        scenarios.append({
            'name': 'auth_failure',
            'parameters': parameters,
            'headers': {'Authorization': 'Bearer invalid_token'},
            'expected_error': {
                'status': 401,
                'code': 'AUTH_ERROR',
                'message': 'Invalid authentication credentials'
            }
        })
        
        # Not found error
        scenarios.append({
            'name': 'resource_not_found',
            'parameters': {'id': 'non_existent_id'},
            'expected_error': {
                'status': 404,
                'code': 'NOT_FOUND',
                'message': 'Resource not found'
            }
        })
        
        return scenarios
    
    def _generate_edge_cases(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate edge case test data."""
        edge_cases = []
        
        for param_name, param_config in parameters.items():
            if isinstance(param_config, dict):
                param_type = param_config.get('type', 'string')
                
                if param_type == 'string':
                    # Empty string
                    edge_case = parameters.copy()
                    edge_case[param_name] = ''
                    edge_cases.append({
                        'name': f'empty_string_{param_name}',
                        'parameters': edge_case
                    })
                    
                    # Very long string
                    max_length = param_config.get('maxLength', 1000)
                    edge_case = parameters.copy()
                    edge_case[param_name] = 'x' * max_length
                    edge_cases.append({
                        'name': f'max_length_{param_name}',
                        'parameters': edge_case
                    })
                    
                    # Special characters
                    edge_case = parameters.copy()
                    edge_case[param_name] = "Test!@#$%^&*()<>?{}[]|\\\"':;"
                    edge_cases.append({
                        'name': f'special_chars_{param_name}',
                        'parameters': edge_case
                    })
                
                elif param_type in ['number', 'integer']:
                    # Zero
                    edge_case = parameters.copy()
                    edge_case[param_name] = 0
                    edge_cases.append({
                        'name': f'zero_{param_name}',
                        'parameters': edge_case
                    })
                    
                    # Negative number
                    if param_config.get('minimum', -1) < 0:
                        edge_case = parameters.copy()
                        edge_case[param_name] = -1
                        edge_cases.append({
                            'name': f'negative_{param_name}',
                            'parameters': edge_case
                        })
                
                elif param_type == 'array':
                    # Empty array
                    edge_case = parameters.copy()
                    edge_case[param_name] = []
                    edge_cases.append({
                        'name': f'empty_array_{param_name}',
                        'parameters': edge_case
                    })
        
        return edge_cases
    
    def _generate_performance_data(self) -> Dict[str, Any]:
        """Generate performance test data."""
        return {
            'response_times': {
                'p50': random.randint(50, 100),
                'p90': random.randint(100, 200),
                'p95': random.randint(200, 300),
                'p99': random.randint(300, 500)
            },
            'throughput': {
                'requests_per_second': random.randint(100, 1000),
                'concurrent_users': random.randint(10, 100)
            },
            'rate_limits': {
                'requests_per_minute': 60,
                'requests_per_hour': 3600,
                'burst_limit': 100
            }
        }


async def main():
    """Main entry point for the test data generation script."""
    logger.info("Starting Test Data Generation for Unified Templates...")
    
    generator = TestDataGenerator()
    await generator.generate_for_all_templates()
    
    logger.info("Test data generation complete!")


if __name__ == "__main__":
    asyncio.run(main())