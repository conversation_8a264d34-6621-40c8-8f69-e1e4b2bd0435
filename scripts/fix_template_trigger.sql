-- Fix the template trigger function
DROP TRIGGER IF EXISTS version_template ON templates;
DROP FUNCTION IF EXISTS save_template_version CASCADE;

-- Create a new function that correctly maps the fields
CREATE OR REPLACE FUNCTION save_template_version()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO template_versions (
        id, 
        template_id, 
        tenant_id, 
        version, 
        content,
        body,
        actions,
        parameters
    )
    VALUES (
        gen_random_uuid(),
        OLD.id,
        OLD.tenant_id,
        (SELECT COALESCE(MAX(version), 0) + 1 FROM template_versions WHERE template_id = OLD.id),
        to_jsonb(OLD),
        OLD.body,
        OLD.actions,
        OLD.parameters
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Re-create the trigger
CREATE TRIGGER version_template
BEFORE UPDATE ON templates
FOR EACH ROW EXECUTE FUNCTION save_template_version();