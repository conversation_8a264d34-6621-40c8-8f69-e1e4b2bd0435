#!/usr/bin/env python3
"""Check what collections exist in Qdrant."""

import asyncio
from pathlib import Path
import sys

# Add src to sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.coherence.core.config import settings
from qdrant_client import QdrantClient as QdrantClientBase


def check_collections():
    """Check what collections exist in Qdrant."""
    print("=== Checking Qdrant Collections ===\n")
    
    # Create direct Qdrant client
    client = QdrantClientBase(
        host=settings.QDRANT_HOST,
        port=settings.QDRANT_PORT,
        api_key=settings.QDRANT_API_KEY if hasattr(settings, 'QDRANT_API_KEY') else None,
    )
    
    print(f"Connecting to Qdrant at {settings.QDRANT_HOST}:{settings.QDRANT_PORT}")
    
    try:
        # Get all collections
        collections_response = client.get_collections()
        collections = collections_response.collections
        
        print(f"\nTotal collections: {len(collections)}")
        print("\nAll collections:")
        for col in collections:
            print(f"  - {col.name}")
        
        # Filter for template/intent collections
        template_collections = [col for col in collections if "template" in col.name or "intent" in col.name]
        
        print(f"\nTemplate/Intent collections: {len(template_collections)}")
        for col in template_collections:
            print(f"  - {col.name}")
            
            # Get more info about each collection
            try:
                collection_info = client.get_collection(col.name)
                print(f"    Points: {collection_info.points_count}")
                print(f"    Vectors: {collection_info.vectors_count}")
                print(f"    Status: {collection_info.status}")
            except Exception as e:
                print(f"    Error getting info: {e}")
        
        # Specifically check for template_idx_global
        print("\n=== Checking template_idx_global ===")
        if "template_idx_global" in [col.name for col in collections]:
            print("✓ template_idx_global EXISTS")
            info = client.get_collection("template_idx_global")
            print(f"  Points: {info.points_count}")
            print(f"  Vectors: {info.vectors_count}")
            print(f"  Status: {info.status}")
            
            # Get a sample of points
            if info.points_count > 0:
                points = client.scroll(
                    collection_name="template_idx_global",
                    limit=5,
                    with_payload=True,
                    with_vectors=False
                )
                print("\n  Sample points:")
                for point in points[0]:
                    print(f"    ID: {point.id}")
                    if point.payload:
                        print(f"      Key: {point.payload.get('template_key', 'N/A')}")
                        print(f"      Category: {point.payload.get('template_category', 'N/A')}")
        else:
            print("✗ template_idx_global DOES NOT EXIST")
        
        # Check for other intent collections
        print("\n=== Intent Collections ===")
        intent_collections = [col for col in collections if col.name.startswith("intent_idx_")]
        print(f"Found {len(intent_collections)} intent collections")
        for col in intent_collections:
            print(f"  - {col.name}")
        
    except Exception as e:
        print(f"Error accessing Qdrant: {e}")
        print(f"Error type: {type(e)}")


if __name__ == "__main__":
    check_collections()