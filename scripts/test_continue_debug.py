#!/usr/bin/env python
"""Test script to debug conversation continuation issues."""

import asyncio
import json
import httpx
import uuid

async def test_conversation_flow():
    # Force use localhost since we're testing from within the Docker network
    base_url = "http://127.0.0.1:8001/v1"
    api_key = "coh_8r74navkqv25KX7WBKEnkm49utIWQkZeb_Rc_qvEIgI"
    
    headers = {
        "X-API-Key": api_key,
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient() as client:
        # Step 1: Initial message
        print("Step 1: Sending initial message...")
        conversation_id = str(uuid.uuid4())
        initial_request = {
            "conversation_id": conversation_id,
            "user_id": str(uuid.uuid4()),
            "message": "what are the icd10 codes for diabetes",
            "role": "user",
        }
        
        print(f"Request: {json.dumps(initial_request, indent=2)}")
        
        response = await client.post(
            f"{base_url}/resolve",
            headers=headers,
            json=initial_request
        )
        
        print(f"Response status: {response.status_code}")
        result = response.json()
        print(f"Response body: {json.dumps(result, indent=2)}")
        
        if result.get("kind") == "intent_clarification":
            print("\nStep 2: Got clarification request, sending response...")
            
            # Wait a moment to ensure Redis save is complete
            await asyncio.sleep(0.5)
            
            continue_request = {
                "conversation_id": conversation_id,
                "user_id": initial_request["user_id"],
                "message": "I need to retrieve the codes using the API",
                "role": "user",
            }
            
            print(f"Continue request: {json.dumps(continue_request, indent=2)}")
            
            continue_response = await client.post(
                f"{base_url}/continue",
                headers=headers,
                json=continue_request
            )
            
            print(f"Continue response status: {continue_response.status_code}")
            continue_result = continue_response.json()
            print(f"Continue response body: {json.dumps(continue_result, indent=2)}")
        else:
            print("No clarification needed or unexpected response type")

if __name__ == "__main__":
    asyncio.run(test_conversation_flow())