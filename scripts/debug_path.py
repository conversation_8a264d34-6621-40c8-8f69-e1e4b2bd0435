"""
Debug script to verify Python import paths.
"""

import os
import sys


def main():
    print("Python Path (sys.path):")
    for path in sys.path:
        print(f"  - {path}")

    print("\nCurrent Working Directory:")
    print(f"  {os.getcwd()}")

    print("\nTrying imports:")
    try:
        print("✅ 'import src.coherence' succeeded")
    except ImportError as e:
        print(f"❌ 'import src.coherence' failed: {e}")

    try:
        print("✅ 'import coherence' succeeded")
    except ImportError as e:
        print(f"❌ 'import coherence' failed: {e}")


if __name__ == "__main__":
    main()
