"""
<PERSON><PERSON><PERSON> to apply the audit logging fixes.

This script ensures the middleware changes are applied correctly.
"""

import asyncio
import logging

from sqlalchemy import text

from src.coherence.db.deps import get_admin_db_session

logger = logging.getLogger(__name__)


async def verify_uuid_handling():
    """
    Verify UUID handling by checking if the system can handle various UUID edge cases.
    """
    try:
        async with get_admin_db_session() as db:
            # Run a query to test UUID validation and handling
            test_query = text(
                """
            SELECT 
                uuid_generate_v4() as valid_uuid,
                'not-a-uuid' as invalid_uuid,
                app.current_tenant_id,
                app.is_system_admin
            """
            )

            result = await db.execute(test_query)
            row = result.fetchone()
            logger.info(f"UUID validation test - valid UUID: {row.valid_uuid}")
            logger.info(
                f"UUID validation test - current tenant ID: {row.current_tenant_id}"
            )
            logger.info(
                f"UUID validation test - system admin status: {row.is_system_admin}"
            )
            logger.info("UUID handling verification completed successfully")

            # Set a test value to verify tenant context middleware
            test_set_query = text("SET app.current_tenant_id = 'not-a-uuid';")
            await db.execute(test_set_query)
            logger.info("Successfully set invalid UUID test value")

            # Now check if we can get the value back (should be cleaned by middleware)
            verify_query = text(
                "SELECT current_setting('app.current_tenant_id') as tenant_id;"
            )
            result = await db.execute(verify_query)
            row = result.fetchone()
            logger.info(f"Retrieved tenant ID setting: {row.tenant_id}")

    except Exception as e:
        logger.error(f"Failed to verify UUID handling: {e}")
        raise


async def main():
    """
    Main function to apply audit logging fixes.
    """
    logging.basicConfig(level=logging.INFO)
    logger.info("Starting audit logging fixes application")

    # Test UUID handling to verify our fixes
    await verify_uuid_handling()

    logger.info("Audit logging fixes applied successfully")


if __name__ == "__main__":
    asyncio.run(main())
