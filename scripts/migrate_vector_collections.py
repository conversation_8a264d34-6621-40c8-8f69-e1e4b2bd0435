#!/usr/bin/env python
"""
Migrate vector collections to standardized dimensions.

This script:
1. Lists all collections with non-standard dimensions
2. For each collection:
   a. Creates a backup of collection metadata
   b. Deletes the existing collection
   c. Creates a new collection with standardized dimensions
   d. Sets up appropriate index points
3. Outputs a report of actions taken

Usage:
python -m scripts.migrate_vector_collections [--confirm]

Options:
--confirm    Required flag to actually perform the migration (dry run otherwise)
"""

import argparse
import asyncio
import json
import logging
import os
import time
from typing import Any, Dict, List

from src.coherence.core.config import settings
from src.coherence.core.qdrant_client import get_qdrant_client

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Standard dimension from config
STANDARD_DIMENSION = settings.EMBEDDING_DIMENSION

async def list_collections_needing_migration() -> List[Dict[str, Any]]:
    """
    List collections that need migration to the standard dimension.
    
    Returns:
        List of dictionaries with collection information
    """
    qdrant_client = await get_qdrant_client()
    
    collections_to_migrate = []
    collections = await qdrant_client.list_collections()
    
    for collection_name in collections:
        try:
            # Get collection details
            collection_info = qdrant_client.client.get_collection(collection_name)
            vector_size = collection_info.config.params.vectors.size
            
            if vector_size != STANDARD_DIMENSION:
                collections_to_migrate.append({
                    "name": collection_name,
                    "current_vector_size": vector_size,
                    "target_vector_size": STANDARD_DIMENSION,
                    "points_count": collection_info.vectors_count,
                    "payload_schema": collection_info.payload_schema
                })
                
                logger.info(f"Collection requiring migration: {collection_name}, " +
                          f"Current Size: {vector_size}, " +
                          f"Target Size: {STANDARD_DIMENSION}, " +
                          f"Points: {collection_info.vectors_count}")
        except Exception as e:
            logger.error(f"Error checking collection {collection_name}: {str(e)}")
    
    return collections_to_migrate

async def backup_collection_metadata(collection_name: str) -> Dict[str, Any]:
    """
    Backup collection metadata for future reference.
    
    Args:
        collection_name: Name of the collection
        
    Returns:
        Dictionary with collection metadata
    """
    qdrant_client = await get_qdrant_client()
    
    try:
        # Get collection details
        collection_info = qdrant_client.client.get_collection(collection_name)
        
        # Create metadata dict
        metadata = {
            "name": collection_name,
            "vector_size": collection_info.config.params.vectors.size,
            "distance": str(collection_info.config.params.vectors.distance),
            "points_count": collection_info.vectors_count,
            "payload_schema": {k: str(v.data_type) for k, v in collection_info.payload_schema.items()},
            "backup_date": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Write to backup file
        os.makedirs("backups", exist_ok=True)
        backup_path = f"backups/{collection_name}_{time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(backup_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Backed up collection metadata to {backup_path}")
        return metadata
    
    except Exception as e:
        logger.error(f"Error backing up metadata for {collection_name}: {str(e)}")
        return {"name": collection_name, "error": str(e)}

async def recreate_collection(collection_info: Dict[str, Any]) -> bool:
    """
    Delete and recreate a collection with the standard dimension.
    
    Args:
        collection_info: Dictionary with collection information
        
    Returns:
        True if successful, False otherwise
    """
    collection_name = collection_info["name"]
    qdrant_client = await get_qdrant_client()
    
    try:
        # Delete the collection
        logger.info(f"Deleting collection {collection_name}...")
        qdrant_client.client.delete_collection(collection_name)
        
        # Wait for deletion to complete
        await asyncio.sleep(1)
        
        # Create the new collection with standardized dimensions
        logger.info(f"Recreating collection {collection_name} with dimension {STANDARD_DIMENSION}...")
        
        # If it's a template collection, use the template creation method
        if collection_name.startswith("template_idx_"):
            result = await qdrant_client.create_template_collection(
                collection_name=collection_name,
                vector_size=STANDARD_DIMENSION
            )
        # If it's an intent collection, use the intent creation method
        elif collection_name.startswith("intent_idx_"):
            # Extract tenant_id and role from collection name
            parts = collection_name.split("_")
            if len(parts) >= 3:
                tenant_id = parts[2]
                role = parts[3] if len(parts) >= 4 else "user"
                result = await qdrant_client.create_intent_collection(
                    tenant_id=tenant_id,
                    role=role,
                    vector_size=STANDARD_DIMENSION
                )
            else:
                logger.error(f"Cannot parse tenant_id and role from collection name: {collection_name}")
                return False
        # For any other collection types, use a generic method
        else:
            logger.info(f"Creating generic collection for {collection_name}")
            qdrant_client.client.create_collection(
                collection_name=collection_name,
                vectors_config={
                    "size": STANDARD_DIMENSION,
                    "distance": "Cosine"
                }
            )
            result = True
            
            # Recreate payload schema indexes if they exist
            if collection_info.get("payload_schema"):
                for field_name, field_type in collection_info["payload_schema"].items():
                    try:
                        qdrant_client.client.create_payload_index(
                            collection_name=collection_name,
                            field_name=field_name,
                            field_schema=field_type
                        )
                    except Exception as e:
                        logger.warning(f"Could not recreate payload index for {field_name}: {str(e)}")
        
        if result:
            logger.info(f"Successfully recreated collection {collection_name} with dimension {STANDARD_DIMENSION}")
            return True
        else:
            logger.error(f"Failed to recreate collection {collection_name}")
            return False
    
    except Exception as e:
        logger.error(f"Error recreating collection {collection_name}: {str(e)}")
        return False

async def migrate_collections(dry_run: bool = True) -> Dict[str, Any]:
    """
    Migrate collections to the standard dimension.
    
    Args:
        dry_run: If True, only show what would be done without making changes
        
    Returns:
        Migration report as a dictionary
    """
    # Get collections needing migration
    collections_to_migrate = await list_collections_needing_migration()
    
    if not collections_to_migrate:
        logger.info("No collections need migration. All dimensions are already standardized.")
        return {
            "status": "success",
            "message": "No collections need migration",
            "collections_migrated": 0,
            "total_collections": 0
        }
    
    # Initialize report
    report = {
        "status": "dry_run" if dry_run else "success",
        "collections_to_migrate": len(collections_to_migrate),
        "collections": []
    }
    
    # Process each collection
    for collection_info in collections_to_migrate:
        collection_name = collection_info["name"]
        collection_report = {
            "name": collection_name,
            "current_dimension": collection_info["current_vector_size"],
            "target_dimension": STANDARD_DIMENSION,
            "points_count": collection_info["points_count"]
        }
        
        try:
            if dry_run:
                logger.info(f"[DRY RUN] Would migrate collection: {collection_name}")
                collection_report["status"] = "would_migrate"
                collection_report["needs_reindex"] = True
            else:
                # Backup the collection metadata
                backup = await backup_collection_metadata(collection_name)
                
                # Recreate the collection
                success = await recreate_collection(collection_info)
                
                collection_report["status"] = "migrated" if success else "failed"
                collection_report["needs_reindex"] = True
                collection_report["backup"] = backup.get("backup_path", "not_created")
        except Exception as e:
            logger.error(f"Error processing collection {collection_name}: {str(e)}")
            collection_report["status"] = "error"
            collection_report["error"] = str(e)
        
        report["collections"].append(collection_report)
    
    # Update report summary
    successful = len([c for c in report["collections"] if c.get("status") in ["migrated", "would_migrate"]])
    report["successful_migrations"] = successful
    report["failed_migrations"] = len(collections_to_migrate) - successful
    
    return report

async def main():
    """Main entry point for the script."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Migrate Qdrant collections to standardized dimensions")
    parser.add_argument("--confirm", action="store_true", help="Confirm migration (required for actual changes)")
    args = parser.parse_args()
    
    # Determine if this is a dry run
    dry_run = not args.confirm
    
    try:
        if dry_run:
            logger.info("THIS IS A DRY RUN. Add --confirm to actually perform the migration.")
        
        logger.info(f"Starting collection migration to standardize on {STANDARD_DIMENSION} dimensions...")
        
        # Run the migration
        report = await migrate_collections(dry_run=dry_run)
        
        # Output summary
        logger.info("\n=== Migration Summary ===")
        
        if report.get("status") == "dry_run":
            logger.info("DRY RUN COMPLETED - No actual changes were made")
        
        logger.info(f"Collections to migrate: {report.get('collections_to_migrate', 0)}")
        logger.info(f"Successfully migrated: {report.get('successful_migrations', 0)}")
        logger.info(f"Failed migrations: {report.get('failed_migrations', 0)}")
        
        if not dry_run and report.get("collections_to_migrate", 0) > 0:
            logger.info("\n=== IMPORTANT NEXT STEPS ===")
            logger.info(f"The collections have been recreated with the standard dimension ({STANDARD_DIMENSION}).")
            logger.info("You now need to reindex all content using:")
            logger.info("1. For intent vectors: python -m scripts.rebuild_intent_index")
            logger.info("2. For template vectors: Run the appropriate indexing script")
            
    except Exception as e:
        logger.error(f"Error in migration script: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())