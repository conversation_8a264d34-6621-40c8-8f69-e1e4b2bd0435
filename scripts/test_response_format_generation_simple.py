#!/usr/bin/env python3
"""Test response format generation when creating templates from API specs."""

import sys
from pathlib import Path

# Add src to sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.coherence.openapi_adapter.action_generator import ActionGenerator


def test_response_format_generation():
    """Test that response formats are generated correctly."""
    
    # Create mock OpenAPI spec with various response types
    spec_list_response = {
        "responses": {
            "200": {
                "description": "Success",
                "content": {
                    "application/json": {
                        "schema": {
                            "type": "object",
                            "properties": {
                                "total": {"type": "integer"},
                                "items": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "id": {"type": "string"},
                                            "name": {"type": "string"},
                                            "description": {"type": "string"},
                                            "created_at": {"type": "string"},
                                            "status": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    spec_object_response = {
        "responses": {
            "200": {
                "description": "Success",
                "content": {
                    "application/json": {
                        "schema": {
                            "type": "object",
                            "properties": {
                                "id": {"type": "string"},
                                "name": {"type": "string"},
                                "description": {"type": "string"},
                                "created_at": {"type": "string"},
                                "updated_at": {"type": "string"},
                                "status": {"type": "string"},
                                "metadata": {"type": "object"}
                            }
                        }
                    }
                }
            }
        }
    }
    
    spec_array_response = {
        "responses": {
            "200": {
                "description": "Success",
                "content": {
                    "application/json": {
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "id": {"type": "string"},
                                    "event": {"type": "string"},
                                    "headline": {"type": "string"},
                                    "description": {"type": "string"},
                                    "severity": {"type": "string"},
                                    "urgency": {"type": "string"},
                                    "area": {"type": "string"}
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    # Create action generator (without database)
    generator = ActionGenerator(None)
    
    # Test list response format
    print("=== Testing List Response Format ===")
    format_list = generator._generate_response_format(spec_list_response, "Test API")
    print(f"Type: {format_list['type']}")
    if format_list['type'] == 'structured':
        print("\nGenerated template:")
        print("---")
        print(format_list['template'])
        print("---")
        
        # Verify list format
        assert "Total items:" in format_list['template']
        assert "{% for item in" in format_list['template']
        print("✅ List format validated")
    
    # Test object response format
    print("\n=== Testing Object Response Format ===")
    format_object = generator._generate_response_format(spec_object_response, "Test API")
    print(f"Type: {format_object['type']}")
    if format_object['type'] == 'structured':
        print("\nGenerated template:")
        print("---")
        print(format_object['template'])
        print("---")
        
        # Verify object format
        assert "## Details" in format_object['template']
        assert "**Id**:" in format_object['template'] or "**Name**:" in format_object['template']
        print("✅ Object format validated")
    
    # Test direct array response format
    print("\n=== Testing Direct Array Response Format ===")
    format_array = generator._generate_response_format(spec_array_response, "Weather API")
    print(f"Type: {format_array['type']}")
    if format_array['type'] == 'structured':
        print("\nGenerated template:")
        print("---")
        print(format_array['template'])
        print("---")
        
        # Verify array format
        assert "Total items:" in format_array['template']
        assert "{% for item in response[:10] %}" in format_array['template']
        print("✅ Array format validated")
    
    # Test simple response format (no content)
    print("\n=== Testing Simple Response Format ===")
    spec_simple = {
        "responses": {
            "200": {
                "description": "Success"
            }
        }
    }
    format_simple = generator._generate_response_format(spec_simple, "Simple API")
    print(f"Type: {format_simple['type']}")
    print(f"Template: {format_simple.get('template', 'N/A')}")
    assert format_simple['type'] == 'structured'
    assert "successful" in format_simple['template']
    print("✅ Simple format validated")
    
    print("\n🎉 All response format tests passed!")


if __name__ == "__main__":
    test_response_format_generation()