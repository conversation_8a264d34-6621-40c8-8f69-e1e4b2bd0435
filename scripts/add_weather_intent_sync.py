#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to add a weather intent to the system using synchronous approach.

This creates a simple weather intent that can be used to test the intent resolution system.
"""

import logging
import uuid
from typing import Optional

from sqlalchemy.orm import Session

from src.coherence.db.session import SessionLocal
from src.coherence.models.template import Template
from src.coherence.models.tenant import Tenant

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add a vector-based intent template to match weather intent
WEATHER_TEMPLATE = """
{# Weather intent template for vector matching #}
{% block system_instruction %}
This template helps recognize weather-related queries in user messages.
{% endblock %}

{% block intent_patterns %}
- What is the weather like in {location}?
- How's the weather in {location}?
- Weather forecast for {location}
- Tell me the weather in {location}
- Weather for {location} {date}
- Will it rain in {location}?
- Is it sunny in {location}?
- Temperature in {location}
{% endblock %}

{% block parameters %}
- location: The city, state, or country to get weather for
- date: The date for the forecast (optional, defaults to today)
{% endblock %}

{% block examples %}
"What's the weather like in Chicago?" -> weather_query(location="Chicago")
"How's the weather in San Francisco today?" -> weather_query(location="San Francisco", date="today")
"Tell me the weather forecast for New York tomorrow" -> weather_query(location="New York", date="tomorrow")
"Weather for Miami next week" -> weather_query(location="Miami", date="next week")
{% endblock %}
"""

def add_weather_intent_template(db: Session, tenant_id: Optional[uuid.UUID] = None):
    """Add a weather intent template to the system."""
    # Create the template in the database
    weather_template = Template(
        id=uuid.uuid4(),
        key="WEATHER_QUERY",
        category="intent_router",
        body=WEATHER_TEMPLATE,
        scope="global" if tenant_id is None else "tenant",
        scope_id=tenant_id,
        tenant_id=tenant_id,
        language="en",
        version=1,
        description="Template for matching weather queries",
        actions={
            "intent": "weather_query",
            "parameters": {
                "location": {"type": "string", "required": True},
                "date": {"type": "date", "required": False, "default": "today"},
            },
        },
        parameters={
            "location": {"type": "string", "required": True},
            "date": {"type": "date", "required": False, "default": "today"},
        },
    )

    db.add(weather_template)
    db.commit()
    logger.info(f"Created weather intent template: {weather_template.id}")
    return weather_template


def add_weather_intent(db: Session, tenant_id: Optional[uuid.UUID] = None):
    """Add a weather intent to the system for the given tenant (or global if None)."""
    # Add the template
    template = add_weather_intent_template(db, tenant_id)
    logger.info(f"Added weather intent template with ID: {template.id}")

    # Print success message
    if tenant_id:
        logger.info(f"Successfully added weather intent for tenant: {tenant_id}")
    else:
        logger.info("Successfully added global weather intent")


def main():
    """Main entry point."""
    logger.info("Adding weather intent...")
    
    # Create a DB session
    db = SessionLocal()
    
    try:
        # Get all tenant IDs
        tenants = db.query(Tenant.id).all()
        tenant_ids = [row[0] for row in tenants]
        
        # Add global weather intent
        add_weather_intent(db, None)
        
        # Add tenant-specific weather intents
        for tenant_id in tenant_ids:
            add_weather_intent(db, tenant_id)
        
        logger.info("Weather intent added successfully!")
    finally:
        db.close()


if __name__ == "__main__":
    main()