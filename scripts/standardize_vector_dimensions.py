#!/usr/bin/env python
"""
Standardize vector dimensions across all collections in Qdrant.

This script:
1. Lists all collections in Qdrant
2. Checks each collection's vector dimension
3. Verifies dimensions returned by the embedding provider
4. For collections with non-standard dimensions:
   a. Creates a new collection with the standard dimension
   b. Re-creates the collection with the standard dimension
   c. Adds a note to reindex the content
5. Exits with non-zero status in CI mode if problems are found

Usage:
python -m scripts.standardize_vector_dimensions [--ci]
"""

import argparse
import asyncio
import logging
import sys
import time
from typing import Any, Dict, List, Optional, Tuple

from src.coherence.core.config import settings
from src.coherence.core.llm.factory import LLMFactory
from src.coherence.core.qdrant_client import get_qdrant_client

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Standard dimension from config
STANDARD_DIMENSION = settings.EMBEDDING_DIMENSION
EMBEDDING_MODEL = settings.EMBEDDING_MODEL

async def list_collections_with_dimensions() -> List[Dict[str, Any]]:
    """
    List all collections in Qdrant with their vector dimensions.
    
    Returns:
        List of dictionaries with collection information
    """
    logger.info("Listing collections and checking vector dimensions...")
    
    # Get Qdrant client
    qdrant_client = await get_qdrant_client()
    
    collection_info = []
    collections = await qdrant_client.list_collections()
    
    for collection_name in collections:
        try:
            # Get collection details
            collection_info_response = qdrant_client.client.get_collection(collection_name)
            vector_size = collection_info_response.config.params.vectors.size
            
            collection_info.append({
                "name": collection_name,
                "vector_size": vector_size,
                "points_count": collection_info_response.vectors_count,
                "needs_migration": vector_size != STANDARD_DIMENSION
            })
            
            logger.info(f"Collection: {collection_name}, Vector Size: {vector_size}, " +
                      f"Points: {collection_info_response.vectors_count}, " +
                      f"Needs Migration: {vector_size != STANDARD_DIMENSION}")
            
        except Exception as e:
            logger.error(f"Error getting info for collection {collection_name}: {str(e)}")
    
    return collection_info

async def check_embedding_provider_dimensions() -> Tuple[bool, Optional[int]]:
    """
    Check that the embedding provider is returning vectors with the expected dimensions.
    
    Returns:
        Tuple of (is_correct, actual_dimension)
    """
    logger.info(f"Verifying embedding provider dimensions ({EMBEDDING_MODEL})...")
    start_time = time.time()
    
    try:
        # Log test parameters
        logger.info(
            "Embedding test parameters",
            embedding_model=EMBEDDING_MODEL,
            expected_dimension=STANDARD_DIMENSION
        )
        
        # Create a provider to test
        logger.info("Creating LLM provider using factory")
        factory_start = time.time()
        llm_factory = LLMFactory()
        provider = llm_factory.get_default_provider(fail_fast=True)
        factory_time = time.time() - factory_start
        
        logger.info(
            "LLM provider created",
            provider_type=type(provider).__name__,
            factory_time_ms=int(factory_time * 1000)
        )
        
        # Generate a test embedding
        test_text = "This is a test to verify embedding dimensions from model " + EMBEDDING_MODEL
        
        logger.info(
            "Generating test embedding",
            test_text_length=len(test_text),
            requested_dimensions=STANDARD_DIMENSION
        )
        
        # Should explicitly use the configured dimension
        embedding_start = time.time()
        embedding = await provider.generate_embedding(
            text=test_text,
            dimensions=STANDARD_DIMENSION
        )
        embedding_time = time.time() - embedding_start
        
        # Analyze the result
        actual_dimension = len(embedding)
        is_correct = actual_dimension == STANDARD_DIMENSION
        
        if is_correct:
            logger.info(
                "✓ Embedding dimension test passed",
                model=EMBEDDING_MODEL,
                expected_dimension=STANDARD_DIMENSION,
                actual_dimension=actual_dimension,
                elapsed_time_ms=int(embedding_time * 1000)
            )
        else:
            logger.error(
                "✗ Embedding dimension test failed",
                model=EMBEDDING_MODEL,
                expected_dimension=STANDARD_DIMENSION,
                actual_dimension=actual_dimension,
                elapsed_time_ms=int(embedding_time * 1000)
            )
            
            # Print first few values of embedding for debugging
            logger.error(
                "Embedding vector sample (first 5 values)",
                vector_sample=embedding[:5] if embedding else None
            )
        
        total_time = time.time() - start_time
        logger.info(
            "Embedding provider verification completed",
            success=is_correct,
            total_time_ms=int(total_time * 1000)
        )
        
        return is_correct, actual_dimension
        
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(
            "Error checking embedding provider",
            error=str(e),
            error_type=type(e).__name__,
            total_time_ms=int(total_time * 1000),
            exc_info=True
        )
        return False, None

async def create_migration_plan(collections_info: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Create a migration plan for collections that need to be standardized.
    
    Args:
        collections_info: List of dictionaries with collection information
        
    Returns:
        Migration plan as a dictionary
    """
    collections_to_migrate = [c for c in collections_info if c["needs_migration"]]
    
    migration_plan = {
        "total_collections": len(collections_info),
        "collections_to_migrate": len(collections_to_migrate),
        "standard_dimension": STANDARD_DIMENSION,
        "collections": collections_to_migrate
    }
    
    return migration_plan

async def main(ci_mode: bool = False):
    """
    Main entry point for the script.
    
    Args:
        ci_mode: If True, exit with non-zero status if issues are found
    """
    try:
        logger.info(f"Starting collection dimension standardization to {STANDARD_DIMENSION}...")
        exit_code = 0
        
        # Check embedding provider dimensions
        provider_correct, actual_dimension = await check_embedding_provider_dimensions()
        if not provider_correct:
            logger.error(
                f"CRITICAL: Embedding provider is not returning vectors with the expected dimension.\n"
                f"Expected: {STANDARD_DIMENSION}, Got: {actual_dimension or 'error'}\n"
                f"Please check the OpenAIProvider.generate_embedding method to ensure it's using "
                f"the dimensions parameter correctly."
            )
            exit_code = 1
        
        # List collections with dimensions
        collections_info = await list_collections_with_dimensions()
        
        # Create migration plan
        migration_plan = await create_migration_plan(collections_info)
        
        # Output migration plan
        logger.info("Migration Plan Summary:")
        logger.info(f"Total Collections: {migration_plan['total_collections']}")
        logger.info(f"Collections to Migrate: {migration_plan['collections_to_migrate']}")
        logger.info(f"Standard Dimension: {migration_plan['standard_dimension']}")
        
        if migration_plan["collections_to_migrate"] > 0:
            logger.info("Collections requiring migration:")
            for collection in migration_plan["collections"]:
                logger.info(f"  - {collection['name']} (current: {collection['vector_size']} → target: {STANDARD_DIMENSION})")
            
            logger.info("\nMigration Instructions:")
            logger.info("1. Before migrating, make sure you have backups of your data")
            logger.info("2. For each collection that needs migration:")
            logger.info("   a. Delete the existing collection")
            logger.info("   b. Create a new collection with the standard dimension")
            logger.info("   c. Re-index all content using the standardized embedding model")
            logger.info("\nTo run the migration process automatically, use:")
            logger.info("python -m scripts.migrate_vector_collections --confirm")
            
            # Set exit code for CI mode
            if ci_mode:
                exit_code = 1
        else:
            logger.info("All collections already have the standard dimension. No migration needed!")
        
        # Exit with appropriate code in CI mode
        if ci_mode and exit_code != 0:
            logger.error("Exiting with error code 1 due to dimension mismatches (CI mode)")
            sys.exit(exit_code)
        
    except Exception as e:
        logger.error(f"Error in standardization script: {str(e)}")
        if ci_mode:
            sys.exit(1)
        raise

if __name__ == "__main__":
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Standardize vector dimensions across collections")
    parser.add_argument("--ci", action="store_true", help="Run in CI mode (exit with error if issues found)")
    args = parser.parse_args()
    
    asyncio.run(main(ci_mode=args.ci))