#!/usr/bin/env python3
"""Verify final state after cleanup."""

import asyncio
from pathlib import Path
import sys
from uuid import UUID

# Add src to sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.coherence.db.session import async_session
from src.coherence.models.template import Template
from src.coherence.core.config import settings
from qdrant_client import QdrantClient as QdrantClientBase
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession


async def verify_cleanup():
    """Verify the final state."""
    tenant_id = "ce1e63e1-a9ec-4865-bb14-3fb8383ba0e8"
    tenant_uuid = UUID(tenant_id)
    
    print("=== Final Verification ===\n")
    
    # Check PostgreSQL
    async with async_session() as db:
        # Total templates
        total_count = await db.scalar(select(func.count(Template.id)))
        print(f"Total templates in PostgreSQL: {total_count}")
        
        # Tenant templates
        tenant_count = await db.scalar(
            select(func.count(Template.id)).where(Template.tenant_id == tenant_uuid)
        )
        print(f"Templates for Phaseloch tenant: {tenant_count}")
        
        # List all remaining templates
        if total_count > 0:
            templates = await db.execute(
                select(Template.key, Template.category, Template.scope, Template.tenant_id)
            )
            print("\nRemaining templates:")
            for key, category, scope, tid in templates:
                tenant_name = "Phaseloch" if tid == tenant_uuid else "Other" if tid else "Global"
                print(f"  - {key} ({category}, {scope}, {tenant_name})")
    
    # Check Qdrant
    print("\n=== Qdrant Collections ===")
    client = QdrantClientBase(
        host=settings.QDRANT_HOST,
        port=settings.QDRANT_PORT,
        check_compatibility=False
    )
    
    collections = client.get_collections().collections
    print(f"Total collections: {len(collections)}")
    
    for col in collections:
        info = client.get_collection(col.name)
        print(f"\n{col.name}:")
        print(f"  Points: {info.points_count}")
        print(f"  Status: {info.status}")
        
        # Sample points for key collections
        if col.name == "template_idx_global" and info.points_count > 0:
            points = client.scroll(
                collection_name=col.name,
                limit=10,
                with_payload=True,
                with_vectors=False
            )
            print("  Templates:")
            for point in points[0]:
                if point.payload:
                    print(f"    - {point.payload.get('template_key', 'N/A')} ({point.payload.get('template_category', 'N/A')})")
    
    print("\n✓ Verification complete")


if __name__ == "__main__":
    asyncio.run(verify_cleanup())