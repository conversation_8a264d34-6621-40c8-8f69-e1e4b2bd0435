#!/usr/bin/env python
"""
Script to rebuild the weather intent vector index in Qdrant for our tenant.
"""

import asyncio
import logging
import uuid

from src.coherence.core.qdrant_client import get_qdrant_client
from src.coherence.db.session import async_session

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Weather query examples for vector embedding
WEATHER_QUERIES = [
    "What's the weather like in Chicago?",
    "How's the weather in San Francisco?",
    "Tell me the weather forecast for New York",
    "Weather in Miami",
    "Is it going to rain in Seattle?",
    "What's the temperature in Boston?",
    "Will it be sunny in Denver tomorrow?",
    "What's the weather forecast for Los Angeles?",
    "I want to know the weather in Houston",
    "Weather conditions in Atlanta",
    "Is it cold in Minneapolis?",
    "Weather report for Phoenix",
    "Temperature in Philadelphia",
    "Current weather in Dallas",
    "Will it snow in Detroit?"
]

async def rebuild_vector_index(tenant_id: uuid.UUID):
    """Rebuild the vector index for a specific tenant."""
    logger.info(f"Rebuilding vector index for tenant {tenant_id}...")
    
    # Get Qdrant client
    qdrant_client = await get_qdrant_client()
    
    # Create collection name
    collection_name = f"intent_idx_{tenant_id}_user"
    
    # Delete collection if exists
    try:
        # The collection_exists method might not be awaitable
        # Direct check with client.get_collections
        collections = qdrant_client.client.get_collections()
        if any(c.name == collection_name for c in collections.collections):
            # Direct delete without await
            qdrant_client.client.delete_collection(collection_name)
            logger.info(f"Deleted existing collection: {collection_name}")
    except Exception as e:
        logger.error(f"Error checking/deleting collection: {str(e)}")
        return
    
    # Create new collection
    try:
        # Use the direct client interface with proper parameters
        from qdrant_client.http import models as qdrant_models
        
        # Use the standard embedding dimension from settings
        from src.coherence.core.config import settings
        vector_size = settings.EMBEDDING_DIMENSION
        
        qdrant_client.client.create_collection(
            collection_name=collection_name,
            vectors_config=qdrant_models.VectorParams(
                size=vector_size,
                distance=qdrant_models.Distance.COSINE
            )
        )
        logger.info(f"Created collection: {collection_name} with vector size {vector_size}")
    except Exception as e:
        logger.error(f"Error creating collection: {str(e)}")
        return
    
    # Add example points
    points = []
    for i, query in enumerate(WEATHER_QUERIES):
        # Generate real embeddings or use consistent mock embeddings
        # that match what the resolver will use
        try:
            # Use the LLM provider to generate a real embedding
            from src.coherence.core.llm.factory import LLMFactory
            llm_factory = LLMFactory()
            provider = llm_factory.create_provider(
                name="openai", 
                model="text-embedding-3-small"
            )
            embedding = await provider.generate_embedding(query)
            logger.info(f"Generated real embedding for '{query}' with dimension {len(embedding)}")
        except Exception as e:
            logger.warning(f"Error generating real embedding: {e}, using mock embedding")
            # If embedding generation fails, use consistent mock embeddings
            # Important: Use a deterministic method that will yield the same vectors
            # for the same input text to ensure matches during query time
            import hashlib
            # Create a hash of the query text for stability
            hash_obj = hashlib.md5(query.encode())
            hash_int = int(hash_obj.hexdigest(), 16)
            # Create a deterministic embedding from the hash
            embedding = [((hash_int + i) % 1000) / 1000.0 for i in range(384)]
        
        # Extract location and date from query if present
        location = ""
        
        # Extract location
        if "in " in query:
            parts = query.split("in ")
            if len(parts) > 1:
                # Extract everything after "in " but before any date indicators
                location_part = parts[1].strip()
                
                # Look for date indicators
                date_indicators = ["today", "tomorrow", "next week"]
                for indicator in date_indicators:
                    if indicator in location_part:
                        # Found a date indicator
                        location = location_part.split(indicator)[0].strip().rstrip("?.,;! ")
                        break
                
                # If no date indicators found, clean up the location
                if not location:
                    location = location_part.rstrip("?.,;! ")
        
        # Create point
        point_id = str(uuid.uuid4())
        points.append({
            "id": point_id,
            "vector": embedding,
            "payload": {
                "intent": "weather_query",
                "text": query,
                "parameters": {
                    "location": location
                }
            }
        })
    
    # Add all points to the collection
    try:
        from qdrant_client.http import models as qdrant_models
        
        # Convert points to Qdrant format
        qdrant_points = []
        for point in points:
            qdrant_points.append(
                qdrant_models.PointStruct(
                    id=point["id"],
                    vector=point["vector"],
                    payload=point["payload"]
                )
            )
        
        # Add points in batches to avoid timeouts
        batch_size = 5
        for i in range(0, len(qdrant_points), batch_size):
            batch = qdrant_points[i:i+batch_size]
            qdrant_client.client.upsert(
                collection_name=collection_name,
                points=batch
            )
            logger.info(f"Added batch {i//batch_size + 1} with {len(batch)} points")
        
        logger.info(f"Added {len(points)} examples to vector index")
    except Exception as e:
        logger.error(f"Error adding points to collection: {str(e)}")

async def get_tenant_id(tenant_name: str) -> uuid.UUID:
    """Get the tenant ID for a specific tenant name."""
    async with async_session() as db:
        from sqlalchemy import select

        from src.coherence.models.tenant import Tenant
        
        # Get tenant with matching name
        query = select(Tenant.id, Tenant.name).where(Tenant.name == tenant_name)
        result = await db.execute(query)
        tenant = result.first()
        
        if not tenant:
            # Get the first tenant if no match
            query = select(Tenant.id, Tenant.name)
            result = await db.execute(query)
            tenant = result.first()
            
            if not tenant:
                raise ValueError("No tenants found in database")
        
        logger.info(f"Found tenant: {tenant.name} ({tenant.id})")
        return tenant.id

async def main():
    """Main entry point."""
    logger.info("Starting weather intent vector index rebuild...")
    
    # Use our new Test Tenant
    tenant_id = uuid.UUID("6e8fad41-8656-425d-b96c-732e8e50c0f5")
    
    # Rebuild the vector index
    await rebuild_vector_index(tenant_id)
    
    logger.info("Vector index rebuild completed!")

if __name__ == "__main__":
    asyncio.run(main())